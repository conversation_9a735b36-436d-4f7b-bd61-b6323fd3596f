export default [
  {
    path: 'target-population',
    component: () => import('@/pages/TargetPopulation/index.vue'),
    meta: {
      auth: ['user-operation', 'target-population'],
    },
  },
  {
    path: 'promotion-activity',
    component: () => import('@/pages/promotion-activity/index.vue'),
    meta: {
      auth: ['marketing-planning', 'dingTalk-try'],
    },
  },
  {
    path: 'promotion-activity/staff/:type',
    component: () => import('@/pages/promotion-activity/staff/index.vue'),
    meta: { isFullScreen: true, keepAlive: true, auth: ['marketing-planning', 'employee-spread-market-planning', 'dingTalk-try'] },
  },
  {
    path: 'promotion-activity/member/:type',
    component: () => import('@/pages/promotion-activity/staff/index.vue'),
    meta: { isFullScreen: true, auth: ['marketing-planning', 'member-operation'] },
  },
  {
    path: 'promotion-activity/sms/:type',
    component: () => import('@/pages/promotion-activity/sms/index.vue'),
    meta: { isFullScreen: true, auth: ['marketing-planning', 'sms-market-planning'] },
  },
  // 邮件推广任务详情
  {
    path: 'promotion-activity/mail-notice',
    component: () => import('@/pages/promotion-activity/mail-promotion-notice/index.vue'),
    meta: { isFullScreen: true, auth: [] },
  },
  {
    // 公众号首页
    path: 'wechat/home',
    component: () => import('@/pages/wechat/home.vue'),
    meta: {
      auth: ['wechat-operation'],
    },
  },
  // 新建模板消息群发
  {
    path: 'wechat/tplmsg',
    component: () => import('@/pages/wechat/tplmsg.vue'),
    meta: { isFullScreen: true, auth: ['wechat-operation'] },
  },
  // 新建高级群发
  {
    path: 'wechat/massmsg',
    component: () => import('@/pages/wechat/massmsg.vue'),
    meta: { isFullScreen: true, auth: ['wechat-operation'] },
  },
  // 新建渠道二维码
  {
    path: 'wechat/qr-create',
    component: () => import('@/pages/wechat/qr-create.vue'),
    meta: { isFullScreen: true, auth: ['wechat-operation'] },
  },
  {
    // 公众号营销
    path: 'wechat/:tabId/:appId',
    component: () => import('@/pages/wechat/index.vue'),
    meta: {
      auth: ['wechat-operation'],
    },
  },
  {
    path: 'trigger',
    component: () => import('@/pages/trigger/index.vue'),
    meta: {
    },
  },
  {
    path: 'trigger-list/:type/:id',
    component: () => import('@/pages/trigger/PrivateTrigger.vue'),
    meta: {
    },
  },
  {
    path: 'trigger/create',
    component: () => import('@/pages/trigger/create/index.vue'),
    meta: {
      isFullScreen: true,
    },
  },
  {
    path: 'trigger/edit',
    component: () => import('@/pages/trigger/create/index.vue'),
    meta: {
      isFullScreen: true,
    },
  },
  {
    path: 'marketing-process',
    component: () => import('@/pages/marketing-process/index.vue'),
    meta: {
      auth: ['user-operation', 'marketing-automation'],
    },
  },
  {
    path: 'marketing-process/create/:type',
    component: () => import('@/pages/marketing-process/create.vue'),
    meta: { isFullScreen: true, auth: ['user-operation'] },
  },
  {
    path: 'marketing-calendar',
    component: () => import('@/pages/marketing-calendar/index.vue'),
    meta: {
      auth: ['marketing-planning', 'user-operation', 'marketing-calendar'],
    },
  },
  {
    path: 'marketing-calendar/dashboard/:id',
    component: () => import('@/pages/marketing-calendar/dashboard.vue'),
    meta: {
      auth: ['marketing-planning', 'user-operation'],
    },
  },
  {
    path: 'marketing-calendar/setting',
    component: () => import('@/pages/marketing-calendar/setting.vue'),
    meta: {
      auth: ['marketing-planning', 'user-operation'],
    },
  },
  {
    path: 'site-list/:type',
    component: () => import('@/pages/site/index.vue'),
    meta: {
      auth: ['content-producer', 'dingTalk-try'],
    },
  },
  {
    path: 'site/design/:siteId/:templateSiteId?',
    component: () => import('@/pages/site/design.vue'),
    meta: { isFullScreen: true, auth: ['content-producer', 'dingTalk-try'] },
  },
  {
    path: 'site/create',
    component: () => import('@/pages/site/site-create/index.vue'),
    meta: { isFullScreen: true, auth: ['content-producer', 'dingTalk-try'] },
  },
]
