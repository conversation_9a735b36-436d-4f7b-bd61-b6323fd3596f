export default [
  {
    name: 'meeting-marketing-init',
    path: 'meeting-marketing/index',
    component: () => import('@/pages/meeting-marketing/meeting-init/index.vue'),
    meta: {
      auth: ['marketing-planning', 'user-operation', 'conference-planning'],
    },
  },
  {
    name: 'meeting-marketing-create',
    path: 'meeting-marketing/create',
    component: () => import('@/pages/meeting-marketing/MeetingCreate/index.vue'),
    meta: {
      isFullScreen: true,
      auth: ['marketing-planning', 'user-operation', 'conference-planning'],
    },
  },
  {
    name: 'meeting-signin-custom',
    path: 'signin-custom/:id',
    component: () => import('@/pages/meeting-marketing/MeetingSignin/signin-custom/index.vue'),
    meta: {
      isFullScreen: true,
    },
  },
  {
    name: 'meeting-infomation',
    path: 'meeting-marketing/:id',
    component: () => import('@/pages/meeting-marketing/meeting-infomation/index.vue'),
    meta: {
      auth: ['marketing-planning', 'user-operation', 'conference-planning'],
    },
    children: [
      {
        name: 'meeting-detail',
        path: 'meeting-detail',
        component: () => import('@/pages/meeting-marketing/MeetingDetail/index.vue'),
      },
      // {
      //   name: 'meeting-infomation-detail',
      //   path: 'meeting-detail',
      //   component: () => import('@/pages/meeting-marketing/meeting-detail/index'),
      // },
      {
        name: 'meeting-enroll-setting',
        path: 'enroll-setting',
        component: () => import('@/pages/meeting-marketing/enroll-setting/index.vue'),
      },
      {
        name: 'meeting-setting',
        path: 'meeting-setting',
        component: () => import('@/pages/meeting-marketing/MeetingSetting/index.vue'),
      },
      {
        name: 'meeting-spread-channel',
        path: 'spreadChannel',
        component: () => import('@/pages/meeting-marketing/spread-channel/index.vue'),
      },
      {
        name: 'meeting-invitation-content',
        path: 'invitationContent',
        component: () => import('@/pages/meeting-marketing/MeetingInvitationContent/index.vue'),
      },
      {
        name: 'meeting-poster',
        path: 'poster',
        component: () => import('@/pages/meeting-marketing/MeetingPoster/index.vue'),
      },
      {
        name: 'meeting-data',
        path: 'data',
        component: () => import('@/pages/meeting-marketing/MeetingData/index.vue'),
      },
      // {
      //   name: 'meeting-invitation-content',
      //   path: 'invitationContent/:pageType',
      //   component: () => import('@/pages/meeting-marketing/invitation-content/index'),
      // },
      {
        name: 'meeting-poster-content',
        path: 'invitationContent/:pageType',
        component: () => import('@/pages/meeting-marketing/invitation-content/index_qrcode.vue'),
      },
      {
        name: 'meeting-check-in',
        path: 'checkIn',
        component: () => import('@/pages/meeting-marketing/check-in/index.vue'),
      },
      {
        name: 'meeting-directional-invitation',
        path: 'directionalInvite',
        component: () => import('@/pages/meeting-marketing/directional-invitation/index.vue'),
      },
      {
        name: 'meeting-signin',
        path: 'signin',
        component: () => import('@/pages/meeting-marketing/MeetingSignin/index.vue'),
      },
      {
        name: 'meeting-invite',
        path: 'invite',
        component: () => import('@/pages/meeting-marketing/MeetingInvite/index.vue'),
      },
    ],
  },
]
