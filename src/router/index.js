import {
  baseHash,
  regRoutes,
  scrollBehavior,
} from './util.js'
import social from './social.js'
import member from './member.js'
import sms from './sms.js'
import activity from './activity.js'
import meeting from './meeting.js'
import website from './website.js'
import content from './content.js'
import live from './live.js'
import qywxmanage from './qywxmanage.js'
import mail from './mail.js'
import miniapp from './miniapp.js'
import material from './material.js'
import ad from './ad.js'
import partner from './partner.js'
import targetMarketing from './targetMarketing.js'
import demo from './demo.js'
import clue from './clue.js'
import whatsapp from './whatsapp.js'
import marketingCollaboration from './marketingCollaboration.js'
import cta from './cta.js'
import vibeMarketing from './vibeMarketing.js'

const home = [{
  name: 'home',
  path: baseHash,
  component: () => import('@/pages/index/index.vue'),
  // meta: { isFullScreen: true },
},
// 运营计划
{
  path: 'kanban',
  component: () => import('@/pages/kanban/index.vue'),
  meta: {
    auth: ['operation-planning'],
  },
},
{
  path: 'kanban/detail/:id',
  name: 'kanban-detail',
  component: () => import('@/pages/kanban/detail/index.vue'),
  meta: {
    auth: ['operation-planning'],
  },
},
{
  path: 'setting/setitems',
  name: 'setting-setitems',
  component: () => import('@/pages/setting/setitems/index.vue'),
  meta: {
    auth: ['super-administrator', 'dingTalk-try'],
    key: 'setting',
  },
  children: [
    {
      path: 'clue-set',
      name: 'setting-clueSet',
      component: () => import('@/pages/setting/setitems/clue-set/clue-set.vue'),
      meta: {
        auth: ['super-administrator', 'dingTalk-try'],
        key: 'setting',
      },
    },
    {
      path: 'clue-sync-log',
      name: 'setting-clueSyncLog',
      component: () => import('@/pages/setting/setitems/clue-set/clue-sync-log.vue'),
      meta: {
        auth: ['super-administrator', 'dingTalk-try'],
        key: 'setting',
      },
    },
  ],
},
{
  path: 'setting/pubplat-set',
  component: () => import('@/pages/setting/setitems/pubplat-set.vue'),
  meta: {
    auth: ['super-administrator'],
  },
},
{
  path: 'setting/pay-set',
  component: () => import('@/pages/setting/setitems/pay-set.vue'),
  meta: {
    auth: ['super-administrator'],
  },
},
{
  path: 'setting/mail-set',
  component: () => import('@/pages/setting/setitems/mail-set.vue'),
  meta: {
    auth: ['super-administrator'],
  },
},
{
  path: 'setting/ad-set',
  component: () => import('@/pages/setting/setitems/ad-set.vue'),
  meta: {
    auth: ['super-administrator'],
  },
},
{
  path: 'setting/meeting-set',
  component: () => import('@/pages/setting/setitems/meeting-set.vue'),
  meta: {
    auth: ['super-administrator'],
  },
},
{
  path: 'setting/marketing-ai',
  component: () => import('@/pages/setting/setitems/marketing-ai.vue'),
  meta: {
    auth: ['super-administrator'],
  },
},
{
  path: 'setting/marketing-sdr',
  component: () => import('@/pages/setting/setitems/marketing-sdr.vue'),
  meta: {
    auth: ['super-administrator'],
  },
},
{
  path: 'setting/qywx-set',
  component: () => import('@/pages/setting/setitems/qywx-set.vue'),
  meta: {
    auth: ['super-administrator'],
  },
},
{
  name: 'wechat-video-setting',
  path: 'wechat-video-setting',
  component: () => import('@/pages/setting/setitems/wechat-video-setting/index.vue'),
  meta: {
    auth: ['super-administrator'],
  },
},
{
  name: 'lead-workbench',
  path: 'lead-workbench',
  component: () => import('@/pages/lead-workbench/index.vue'),
  meta: {
    auth: ['marketing-planning', 'user-operation', 'marketing-user'],
  },
},
{
  name: 'enterprise-base',
  path: 'enterprise-base',
  component: () => import('@/pages/enterprise-base/index.vue'),
  meta: {
    auth: ['marketing-planning', 'user-operation', 'marketing-user'],
  },
},
{
  name: 'object-template',
  path: 'object-template',
  component: () => import('@/pages/object-template/index.vue'),
  meta: {
    auth: ['marketing-planning', 'user-operation'],
  },
},
// 营销用户菜单专属，为了区分权限角色
{
  name: 'marketing-user-object-template',
  path: 'marketing-user-object-template',
  component: () => import('@/pages/object-template/index.vue'),
  meta: {
    auth: ['marketing-planning', 'user-operation', 'marketing-user'],
  },
},
{
  name: 'user-picture',
  path: 'user-picture',
  component: () => import('@/pages/UserPicture/index.vue'),
  meta: {
    auth: ['marketing-planning', 'user-operation', 'tag-portrait'],
  },
},
{
  name: 'marketing-activity',
  path: 'marketing-activity',
  component: () => import('@/pages/marketing-activity/index.vue'),
  meta: {
    auth: ['marketing-planning'],
  },
},
{
  name: 'data-cockpit',
  path: 'data-cockpit/:id/:name',
  component: () => import('@/pages/data-cockpit/index.vue'),
  meta: {
    auth: ['data-analysis-administrator'],
  },
},
{
  name: 'sdr-kanban',
  path: 'sdr-kanban',
  component: () => import('@/pages/sdr-kanban/index.vue'),
  meta: {
    auth: ['marketing-sdr'],
  },
},
{
  path: 'setting/adviser',
  component: () => import('@/pages/setting/setitems/adviser-setting.vue'),
  meta: {
    auth: ['super-administrator'],
  },
},
{
  path: 'tags/index',
  component: () => import('@/pages/tags/index.vue'),
  meta: {
    auth: ['user-operation'],
  },
},
{
  path: 'tags-manage/index',
  component: () => import('@/pages/tags-manage/index.vue'),
  meta: {
    auth: ['user-operation'],
  },
},
{
  path: 'content-tags-manage/index',
  name: 'content-tags-manage',
  component: () => import('@/pages/content-tags-manage/index.vue'),
  meta: {
    auth: ['user-operation'],
  },
},
{
  path: 'dev',
  component: () => import('@/pages/dev-demo/index.vue'),
},
{
  path: 'dev-fullscreen',
  component: () => import('@/pages/dev-demo/fullscreen-index.vue'),
  meta: {
    isFullScreen: true,
  },
},
{
  path: 'forbidden',
  component: () => import('@/pages/forbidden/index.vue'),
},
{
  path: 'webhook',
  component: () => import('@/pages/Webhook/index.vue'),
  meta: {
    auth: ['user-operation', 'marketing-automation'],
  },
},
{
  path: 'webhook-calllog/:id/:title',
  component: () => import('@/pages/Webhook/CallLog/index.vue'),
  meta: {
    auth: ['user-operation'],
  },
},
]

const routes = [
  home,
  social,
  member,
  activity,
  sms,
  meeting,
  website,
  content,
  live,
  qywxmanage,
  mail,
  miniapp,
  material,
  ad,
  partner,
  targetMarketing,
  demo,
  clue,
  whatsapp,
  marketingCollaboration,
  cta,
  vibeMarketing,
]

export * from './util.js'

export default function (args = {}) {
  /**
   * 路由注册
   */
  const newRoutes = regRoutes(routes, args)

  return {
    routes: newRoutes,
    scrollBehavior,
  }
}
