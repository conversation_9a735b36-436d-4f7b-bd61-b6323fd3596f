// 引用次 mixin 需要对应组件有 currentTab, marketingEventId, marketingEventType 属性

import _ from 'lodash'
import { mapState, mapGetters, mapActions } from 'vuex'
import http from '@/services/http/index.js'
import CrmCustomComponent from '@/components/crm-custom-component/index.vue'

export default {
  data() {
    return {
      mainMenus: [],
      moreMenus: [],
      mainMenusData: [],
      moreMenusData: [],
    }
  },
  computed: {
    ...mapState('crmLayoutMenus', ['campaignTypes', 'appDropListItems']),
    ...mapGetters('crmLayoutMenus', ['crmBookmarks']),
    allMenusData() {
      return this.convertKeys(this.crmBookmarks.map(item => item[0]), this.appDropListItems, 'id') // 只取数组第一项
    },
    enableMenusMap() {
      return new Map(this.mainMenusData.concat(this.moreMenusData).map(item => [`custom-component-${item.id}`, item]))
    },
    closedMenusData: {
      get() {
        const _set = new Set(this.mainMenusData.map(item => item.id).concat(this.moreMenusData.map(item => item.id)))
        return this.allMenusData.filter(item => !_set.has(item.id))
      },
      set(val) {},
    },
    async formComponentData() {
      if (!this.marketingEventId || !this.isCustomComponents) return null
      if (this.objectDetailData && this.objectDetailData.component && this.objectDetailData.id === 'form_component') {
        const { data, describe, layout } = await http.queryMarketingEventObjDetail({
          objectDataId: this.marketingEventId,
          includeLayout: true,
          includeDescribe: true,
        })
        const _components = layout.components
        if (Array.isArray(_components)) {
          const _formComponentData = _components.find(item => item._id === 'form_component')
          _formComponentData.data = data
          _formComponentData.fields = describe.fields
          console.log('_formComponentData: ', _formComponentData)
          return _formComponentData
        }
      }
      return null
    },
    isCustomComponents() {
      console.log('this.currentTab: ', this.currentTab);
      return String(this.currentTab).startsWith('custom-component')
    },
    readyRenderComponent() {
      if (this.isCustomComponents) {
        return (
          !!this.marketingEventId
            && !!this.objectDetailData
            && (this.objectDetailData.id === 'form_component'
              ? !!this.objectDetailData.component.props && !!this.objectDetailData.component.props.fields
              : !!this.objectDetailData.id)
        )
      }
      return !!this.marketingEventId
    },
    objectDetailData: {
      get() {
        console.log('this.enableMenusMap: ', this.enableMenusMap);
        return this.isCustomComponents && this.enableMenusMap.size ? this.enableMenusMap.get(this.currentTab) : {}
      },
      set() {},
    },
    marketingEventObject() {
      return {
        marketingEventId: this.marketingEventId,
        marketingEventType: this.marketingEventType,
      }
    },
  },
  watch: {
    enableMenusMap(newVal) {
      if (newVal instanceof Map && newVal.size) {
        newVal.forEach((_, key) => {
          // 创建一个扩展的组件，传递 currentTab 属性
          const ExtendedComponent = Vue.extend({
            extends: CrmCustomComponent,
            props: {
              currentTab: {
                type: String,
                default: key
              }
            }
          })
          Vue.component(key, ExtendedComponent)
        })
      }
    },
    formComponentData(newVal) {
      newVal.then(res => {
        if (this.objectDetailData && this.objectDetailData.component && this.objectDetailData.id === 'form_component') {
          this.objectDetailData.component.props = res
        }
      })
    },
    mainMenus(newVal) {
      this.mainMenusData = this.convertKeys(newVal, this.appDropListItems, 'id')
    },
    moreMenus(newVal) {
      this.moreMenusData = this.convertKeys(newVal, this.appDropListItems, 'id')
    },
    appDropListItems(newVal) {
      this.mainMenusData = this.convertKeys(this.mainMenus, newVal, 'id')
      this.moreMenusData = this.convertKeys(this.moreMenus, newVal, 'id')
    },
    marketingEventType(newVal) {
      if (newVal) {
        this.getCustomTagByType(newVal)
      }
    },
    // marketingEventObject(newVal) {
    //   if (newVal.marketingEventId && newVal.marketingEventType) {
    //     this.getCrmWebDetail({ objectDataId: newVal.marketingEventId, MarketingEventObj: newVal.marketingEventType })
    //   }
    // },
  },
  methods: {
    ...mapActions('crmLayoutMenus', [
      'findCustomFieldDescribe',
      'queryDesignerLayout',
      // 'getCrmWebDetail',
      'getDropListItems',
    ]),
    getCustomTagByType(type) {
      http
        .getCustomTagByType({
          marketingEventType: type,
        })
        .then(({ errCode, data }) => {
          if (errCode === 0 && data) {
            this.mainMenus = data.customTagSetting.mainNavigationBar || []
            this.moreMenus = data.customTagSetting.secondaryNavigationBar || []
          }
        })
    },
    // 映射某个 key 的列表到一个 object 列表
    convertKeys(keys, componentsItems, key) {
      if (!Array.isArray(keys) || !Array.isArray(componentsItems)) return []
      const _objects = []
      const _map = new Map(componentsItems.map(item => [item[key], item]))
      keys.forEach(el => {
        if (el && _map.has(el)) {
          _objects.push(_.cloneDeep(_map.get(el)))
        }
      })
      return _objects
    },
  },
  created() {
    this.queryDesignerLayout()
    this.findCustomFieldDescribe()
    this.getDropListItems()
  },
  mounted() {
    if (this.marketingEventType) {
      this.getCustomTagByType(this.marketingEventType)
    }
  },
}