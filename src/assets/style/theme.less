@color-primary: #ffffff; // 全局主色
@color-link: var(--color-info06,#0c6cff); // 链接色
@color-success: #52c41a; // 成功色
@color-warning: #faad14; // 警告色
@color-error: #ff6363; // 错误色
@color-heading: #2a304d; // 标题色
@color-title: #181c25; // 主文本色
@color-subtitle: #91959e; // 次文本色
@color-actived: var(--color-primary06,#ff8000); // 失效色
@color-disabled: #c0c4cc; // 失效色
@font-size-base: 14px; // 主字号
@font-size-small: 12px; // 小字号
@button-bg-color: var(--color-primary06,#ff8000);  // 按钮颜色
@button-fg-color: #ffffff; // 按钮颜色
@border-radius-base: 2px; // 组件/浮层圆角
@border-color-base: #e9edf5; // 边框色
@box-shadow-base: 0px 0px 2px 0px rgba(0, 0, 0, 0.08); // 浮层阴影
@row-hover-color: #f5f7fa; // 行的hover背景颜色

.full-screen(@index: 1001, @bg-color: #fff) {
  padding-top: 0 !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: @index !important;
  background-color: @bg-color !important;
}

.ellipsis(@line) {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  /* autoprefixer: off */
  -webkit-box-orient: vertical;
  /* autoprefixer: on */
  -webkit-line-clamp: @line;
}

.righticon(@size:25px,@left:2px,@top:2px){
  position: relative;
  &::before {
    content: '\00a0';
    display: inline-block;
    border: 2px solid #fff;
    border-top-width: 0;
    border-right-width: 0;
    width: 9px;
    height: 5px;
    transform: rotate(-50deg);
    position: absolute;
    top: @top;
    left: @left;
    z-index: 3;
  }
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 0;
    border-top: @size solid var(--color-primary06);
    border-right: @size solid transparent;
  }
}