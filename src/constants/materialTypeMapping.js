/**
 * 物料类型映射常量
 * 用于在不同场景下统一物料类型的映射关系
 */

// 物料类型映射表
export const MATERIAL_TYPE_MAPPING = {
  6: 1,      // 文章类型 6 被映射为 1
  4: 4,      // 产品类型 4 保持为 4
  26: 10,    // 微页面类型 26 被映射为 10
  24: 24,    // 海报类型 24 保持为 24
  7: 2,      // 类型 7 被映射为 2
  16: 16,    // 表单类型 16 保持为 16
  9999: 9999, // 外部内容类型 9999 保持为 9999
}


/**
 * 格式化物料类型
 * @param {number} objectType - 原始物料类型
 * @param {number} defaultValue - 默认值，默认为 1
 * @returns {number} 映射后的物料类型
 */
export function formatMaterialType(objectType, defaultValue = 1) {
  return MATERIAL_TYPE_MAPPING[objectType] || defaultValue
}

/**
 * 获取forwardType
 * @param {number} materialType - 物料类型
 * @returns {number} forwardType
 */
export function getForwardType(materialType) {
  return FORWARD_TYPE_MAPPING[materialType] || 0
}

/**
 * 获取反向映射类型
 * @param {number} objectType - 原始类型
 * @returns {number} 映射后的类型
 */
export function getReverseType(objectType) {
  return REVERSE_TYPE_MAPPING[objectType] || objectType
}

/**
 * 物料类型说明
 */
export const MATERIAL_TYPE_DESCRIPTION = {
  1: '文章',
  2: '类型2',
  4: '产品',
  10: '微页面',
  16: '表单',
  24: '海报',
  9999: '外部内容'
}
