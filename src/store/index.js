import Vuex from 'vuex'
import Vue from 'vue'
import _ from 'lodash'
import http from '@/services/http/index.js'
import util from '@/services/util/index.js'

import Global from './modules/global/index.js'
import SocialDistribution from './modules/social-distribution/index.js'
import User from './modules/user/index.js'
import Member from './modules/member/index.js'
import EnterpriseInfoSetting from './modules/enterprise-info/index.js'
import PresetCard from './modules/preset-card/index.js'
import SmsMarketing from './modules/sms-marketing/index.js'
import TargetPopulation from './modules/target-population/index.js'
import PromotionActivity from './modules/promotion-activity/index.js'
import MarketingProcess from './modules/marketing-process/index.js'
import MarketingCalendar from './modules/marketing-calendar/index.js'
import MeetingMarketing from './modules/meeting-marketing/index.js'
import Form from './modules/form/index.js'
import MaterielCreateSuccess from './modules/materiel-create-success/index.js'
import QywxManage from './modules/qywx-manage/index.js'
import DingManage from './modules/ding-manage/index.js'
import MailMarketing from './modules/mail-marketing/index.js'
import TagsManage from './modules/tags-manage/index.js'
import Kanban from './modules/kanban/index.js'
import WebsiteAccess from './modules/website-access/index.js'
import MiniappInfo from './modules/miniapp-info/index.js'
import channel from './modules/channel/index.js'
import ActivityMarketing from './modules/activity-marketing/index.js'
import AdMarketing from './modules/ad-marketing/index.js'
import crmLayoutMenus from './modules/crm-layout-menus/index.js'
import MarketingEventSet from './modules/marketing-event-set/index.js'
import MarketingLiveDashboard from './modules/marketing-live/dashboard.js'
import CrmMenus from './modules/crm-menus/index.js'
import TargetMarketing from './modules/target-marketing/index.js'
import ContentTagsManage from './modules/content-tags-manage/index.js'
import WechatVideo from './modules/wechat-video/index.js'
import CardSetting from './modules/card-setting/index.js'
import Hexagon from '@/components/Hexagon/store/index.js'
import meetingFormSetting from './modules/meeting-marketing/formSetting'

Vue.use(Vuex)

const loginInfo = JSON.parse(util.ls('loginInfo'))
const entGroupData = util.lsJSON('entGroupData')
const store = new Vuex.Store({
  modules: {
    Global,
    SocialDistribution,
    User,
    Member,
    EnterpriseInfoSetting,
    PresetCard,
    SmsMarketing,
    TargetPopulation,
    PromotionActivity,
    MarketingProcess,
    Form,
    MarketingCalendar,
    MeetingMarketing,
    MaterielCreateSuccess,
    QywxManage,
    DingManage,
    MailMarketing,
    TagsManage,
    Kanban,
    WebsiteAccess,
    MiniappInfo,
    ActivityMarketing,
    channel,
    AdMarketing,
    crmLayoutMenus,
    MarketingEventSet,
    CrmMenus,
    MarketingLiveDashboard,
    TargetMarketing,
    ContentTagsManage,
    WechatVideo,
    CardSetting,
    Hexagon,
    meetingFormSetting
  },
  state: {
    type: 2,
    loginInfo: loginInfo || {},
    entGroupData: entGroupData || {},
    roleIds: [], // 当前用户权限
    config: {}, // 营销通配置信息
  },
  mutations: {
    setUserRole(state, roleIds) {
      state.roleIds = roleIds
    },
    setLoginInfo(state, payload) {
      state.loginInfo = _.extend({}, state.loginInfo, payload.loginInfo)
    },
    saveEntGroupList(state, payload) {
      state.entGroupData = payload
    },
    setConfig(state, config) {
      state.config = config
    },
    reset(state) {
      state.loginInfo = {}
      state.entGroupData = {}
    },
  },
  actions: {
    /**
     * 微信群列表
     * @param  {[type]} options.commit [description]
     * @param  {[type]} params         [description]
     * @return {[type]}                [description]
     */
    queryEnterpriseGroups({
      commit,
      state,
    }, params) {
      http.queryEnterpriseGroups(params).then(results => {
        if (results.errCode === 0) {
          const {
            data,
          } = results
          if (params.pageNum > 1) {
            data.result = state.entGroupData.result.concat(data.result)
          }
          util.lsJSON('entGroupData', data)
          commit('saveEntGroupList', data)
        }
      })
    },
  },
})
console.log(store)
export default store
