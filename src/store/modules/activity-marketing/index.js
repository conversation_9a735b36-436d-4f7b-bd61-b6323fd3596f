import http from "@/services/http";
import util from '@/services/util/index.js'
export default {
  namespaced: true,
  modules: {},
  state: {
    activityMarketingDetail: {},
    liveDetail: {},
    marketingEventDetail: {}
  },
  mutations: {
    getActivityMarketingDetail(state, payload) {
      state.activityMarketingDetail = payload.activityMarketingDetail;
    },
    getLiveDetail(state, payload) {
      state.liveDetail = payload.liveDetail
    },
    getMarketingEventDetailById(state,payload){
      const activityData = payload.data
      ? {
        ...payload.data,
        marketingEventTime: `${util.formatDateTime(
          payload.data.startTime,
          'YYYY-MM-DD hh:mm',
        ) || '--'} - ${util.formatDateTime(payload.data.endTime, 'YYYY-MM-DD hh:mm') ||
          '--'
        }`,
        marketingEventDetail: payload.data.description || '--',
        marketingEventCover: payload.data.cover || '--',
        marketingEventTitle: payload.data.name || '--',
        marketingEventAddress: payload.data.location || '--',
        isShowMap: payload.data.mapLocation && payload.data.mapLocation.lng
      }
      : null;
      state.marketingEventDetail = activityData;
    }
  },
  actions: {
    getMarketingEventsDetail({
      commit,
      dispatch,
    }, payload) {
      return new Promise((resolve, reject) => {
        let id = payload.id
        http.getMarketingEventsDetail({
          id: id
        }).then((res) => {
          if (res && res.errCode != 0) {
            return;
          }
          commit('getActivityMarketingDetail', {
            activityMarketingDetail: res.data
          });

          resolve(res.data);
        });
      });
    },
    queryLiveDetail({
      commit,
      dispatch,
    }, payload) {
      return new Promise((resolve, reject) => {
        http.queryLiveDetail({
          id: payload.id,
        }).then(res => {
          if (res && res.errCode !== 0) {
            return
          }
          commit('getLiveDetail', {
            liveDetail: res.data,
          })
          resolve(res.data)
        })
      })
    },
    getMarketingEventDetailById({
      commit,
      dispatch,
    }, payload) {
      return new Promise((resolve, reject) => {
        let id = payload.id
        // 占位符不请求
        if(!id || id === '!!marketingEventId!!') return
        http.getMarketingEventDetailById({
          marketingEventId: id
        }).then(async (res) => {
          if (res && !res.data && res.errCode != 0) {
            return;
          }
          if(res.data.associatedAccountId){
            const account = await dispatch('getAccountByMaterials', {
              id: res.data.associatedAccountId
            })
            res.data.channelsAvatar = account.channelsAvatar;
            res.data.channelsName = account.channelsName;
          }
          commit('getMarketingEventDetailById', {
            data: res.data
          });

          resolve(res.data);
        });
      });
    },
    getAccountByMaterials({
      commit,
      dispatch,
    }, payload) {
      return new Promise((resolve, reject) => {
        http.getAccountByMaterials(payload).then((res) => {
          resolve(res.data || {});
        });
      });
    },
  },
};
