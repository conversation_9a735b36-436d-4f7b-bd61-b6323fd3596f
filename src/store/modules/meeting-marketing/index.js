import http from '@/services/http/index';
import util from "@/services/util";
export default {
  state: {
    conferenceId: '',
    conferenceDetail: {},
    marketingEventDetail: {},
    conferenceStatisticData: {},
    channelList: [],
    invitationCommonSetting: [], // 邀约设置
    invitePoster: [], // 邀约海报
    currentMeetingDetailTab: 'overview',
  },
  mutations: {
    setState: Object.assign,
    getConferenceId(state, payload) {
      state.conferenceId = payload.id;
    },
    getConferenceDetail(state, payload) {
      state.conferenceDetail = payload.conferenceDetail;
    },
    setCurrentMeetingDetailTab(state, payload) {
      state.currentMeetingDetailTab = payload.currentMeetingDetailTab;
    },
  },
  actions: {
    queryConferenceDetail({
      commit,
      dispatch,
    }, payload) {
      return new Promise((resolve, reject) => {
        let id = payload.conferenceId
        http.queryConferenceDetail({
          id: id
        }).then(async(res) => {
          if (res && res.errCode != 0) {
            return;
          }
          let resData = res.data
          resData.startTimeCN = util.formatDateTime(
            resData.startTime,
            "YYYY-MM-DD hh:mm"
          )
          resData.endTimeCN = util.formatDateTime(
            resData.endTime,
            "YYYY-MM-DD hh:mm"
          )
          commit('getConferenceDetail', {
            conferenceDetail: resData
          });
          await dispatch('queryMarketingEventsDetailInMeeting', {marketingEventId: resData.marketingEventId})
          await dispatch('getConferenceStatisticData', {id: resData.id})
          resolve(res.data);
        });
      });
    },
    queryMarketingEventsDetailInMeeting({
      commit
    }, payload) {
      return new Promise((resolve, reject) => {
        let id = payload.marketingEventId
        http.queryMarketingEventsDetailInMeeting({
          id: id
        }).then((res) => {
          if (res && res.errCode != 0) {
            return;
          }
          let resData = res.data
          commit('setState', {
            marketingEventDetail: resData
          });
          resolve(res.data);
        });
      });
    },
    getConferenceStatisticData({
      commit
    }, payload) {
      return new Promise((resolve, reject) => {
        let id = payload.id
        http.getConferenceStatisticData({
          conferenceId: id,
        }).then((res) => {
          if (res && res.errCode != 0) {
            return;
          }
          let resData = res.data
          commit('setState', {
            conferenceStatisticData: resData
          });
          resolve(res.data);
        });
      });
    },
    queryChannelList({
      commit,
      dispatch,
    }, payload) {
      return new Promise((resolve, reject) => {
        http.queryChannelList().then((res) => {
          if (res && res.errCode != 0) {
            return;
          }
          commit('setState', {
            channelList: res.data,
          });
        });
      });
    },
    getInvitationCommonSetting({
      commit,
      dispatch,
    }, payload) {
      return new Promise((resolve, reject) => {
        let id = payload.id
        if(id)
        http.getInvitationCommonSetting({ conferenceId: id }).then((res) => {
          if (res && res.errCode != 0) {
            return;
          }
          commit('setState', {
            invitationCommonSetting: res.data,
          });
          resolve && resolve(res.data);
        });
      });
    },
    getInvitePoster({
      commit,
      dispatch,
    }, payload) {
      return new Promise((resolve, reject) => {
        let id = payload.id
        if(id)
        http.queryListByForwardTypeAndTargetId({
          pageNum: 1,
          pageSize: 10,
          time: null,
          title: '',
          targetId: id,
          forwardTypes: [1, 2, 3, 4, 5, 7, 8, 9, 10],  // 产品、文章、会议详情、会议邀请函、表单、会议表单、微页面 // 原先：[3, 4, 8],
          type: 2,
          needDetail: true,
        }).then((res) => {
          if (res && res.errCode != 0) {
            return;
          }
          let invitePoster = {};
          try {
            invitePoster = res.data.result[0] || {};
          } catch(e) {
          }
          commit('setState', {
            invitePoster,
          });
          resolve && resolve(res.data);
        });
      });
    },
    updateConferenceDetail({
      commit
    }, payload) {
      commit('getConferenceDetail', {
        conferenceDetail: payload.conferenceDetail
      });
    }
  },
};
