// 表单设置状态管理
const state = {
  // 需要保存的组件列表
  components: new Set(),
  // 已保存完成的组件列表
  savedComponents: new Set(),
  // 是否正在保存中
  isSaving: false,
  // 保存错误信息
  saveError: null
};

const mutations = {
  // 标记组件保存完成
  MARK_COMPONENT_SAVED(state, componentId) {
    state.savedComponents.add(componentId);
  },
  
  // 重置保存状态
  RESET_SAVE_STATE(state) {
    state.components.clear();
    state.savedComponents.clear();
    state.isSaving = false;
    state.saveError = null;
  },
  
  // 设置保存状态
  SET_SAVING(state, isSaving) {
    state.isSaving = isSaving;
  },
  
  // 设置保存错误
  SET_SAVE_ERROR(state, error) {
    state.saveError = error;
  }
};

const actions = {
  // 标记组件保存完成
  markComponentSaved({ commit, state, dispatch }, componentId) {
    commit('MARK_COMPONENT_SAVED', componentId);
    
    // 检查是否所有组件都已保存完成
    if (state.components.size === state.savedComponents.size) {
      dispatch('saveComplete');
    }
  },
  
  // 开始保存
  startSave({ commit, dispatch }) {
    commit('RESET_SAVE_STATE');
    commit('SET_SAVING', true);
  },
  
  // 保存完成
  saveComplete({ commit }) {
    commit('SET_SAVING', false);
  },
  
  // 保存失败
  saveError({ commit }, error) {
    commit('SET_SAVING', false);
    commit('SET_SAVE_ERROR', error);
  }
};

const getters = {
  // 是否所有组件都已保存
  areAllComponentsSaved: state => state.components.size === state.savedComponents.size,
  
  // 是否正在保存中
  isSaving: state => state.isSaving,
  
  // 获取保存错误
  saveError: state => state.saveError
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}; 