import http from "@/services/http";
export default {
  namespaced: true,
  modules: {
  },
  state: {
    activityTypeMapping: null,
    openMergePhone: false,
    marketingActivityAudit: false,
    marketingEventAudit: false,
    allActivityTypeMapping: null,
  },
  mutations: {
    setState: Object.assign
  },
  actions: {
    queryMarketingEventCommonSetting({
      commit,
    }) {
      http.getMarketingEventCommonSetting({ type: 0 }).then((res) => {
        if (res && res.errCode == 0) {
          commit("setState", {
            activityTypeMapping: res.data.activityTypeMapping || null,
            openMergePhone: res.data.openMergePhone || false,
            marketingActivityAudit: res.data.marketingActivityAudit || false,
            marketingEventAudit: res.data.marketingEventAudit || false
          });
        }
      });
    },
    queryAllMarketingEventCommonSetting({
      commit,
    }) {
      return new Promise((resolve) => {
        http.getMarketingEventCommonSetting().then((res) => {
          if (res && res.errCode == 0) {
            commit("setState", {
              allActivityTypeMapping: res.data.activityTypeMapping || null,
              openMergePhone: res.data.openMergePhone || false,
              marketingActivityAudit: res.data.marketingActivityAudit || false,
              marketingEventAudit: res.data.marketingEventAudit || false
            });
          }
          resolve();
        }).catch(() => {
          resolve();
        });
      });
    }
  },
  getters: {
    marketSetFilters(state) {
      if (state.marketingEventAudit) {
        return [
          {
            field_name: 'life_status',
            field_values: ['normal'],
            operator: 'EQ',
          }
        ]
      }
      return []
    }
  }
};
