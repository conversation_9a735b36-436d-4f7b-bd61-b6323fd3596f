export default {
  // 校验域名
  checkDomainConfig: {
    url: '/email/checkDomainConfig',
    default: {},
  },
  // 创建apiUser
  createApiUser: {
    url: '/email/createApiUser',
    default: {},
  },
  // 创建发信域名
  createEmailDomain: {
    url: '/email/createEmailDomain',
    default: {},
  },
  // 获取企业邮件设置
  getCompanyEmailBaseInfo: {
    url: '/email/getCompanyEmailBaseInfo',
    default: {},
  },
  // 获取域名详情
  getDomainDetail: {
    url: '/email/getDomainDetail',
    default: {},
  },
  // 查询账户信息
  queryMailAccountInfo: {
    url: '/email/queryAccountInfo',
    default: {},
  },
  // 更新域名
  updateDomainConfig: {
    url: '/email/updateDomainConfig',
    default: {},
  },
  // 增加发信人/收信人信息
  addSendReplyData: {
    url: '/email/addSendReplyData',
    default: {},
  },
  // 获取发信人或者接收人列表
  querySendReplyData: {
    url: '/email/querySendReplyData',
    default: {},
  },
  // 修改发信人/收信人信息
  updateSendReplyData: {
    url: '/email/updateSendReplyData',
    default: {},
  },
  // 删除邮件模板
  deleteMailTemplate: {
    url: '/email/deleteTemplate',
    default: {},
  },
  // 添加邮件模板
  addTemplate: {
    url: '/email/addTemplate',
    default: {},
  },
  // 查询邮件模板列表
  listPagerTemplate: {
    url: '/email/listPagerTemplate',
    default: {},
  },
  // 查询邮件模板详情
  queryTemplateDetail: {
    url: '/email/queryTemplateDetail',
    default: {},
  },
  // 获取邮件营销统计数据
  listMailMarketing: {
    url: '/email/listMailMarketing',
    default: {},
  },
  // 获取SOP邮件统计数据
  listSopMailNotice: {
    url: '/email/listSopMailNotice',
    default: {},
  },
  // 获取邮件点击详情
  queryClickLinkDetail: {
    url: '/email/queryClickLinkDetail',
    default: {},
  },
  // queryLeadTransforInfo
  queryLeadTransforInfo: {
    url: '/email/queryLeadTransforInfo',
    default: {},
  },
  // 邮件发送详情
  queryMailUserSendDetail: {
    url: '/email/queryMailUserSendDetail',
    default: {},
  },
  // 导出邮件发送情况
  exportMailUserSendDetail: {
    url: '/email/exportMailUserSendDetail',
    default: {},
  },
  // 修改邮件内容
  updateTemplateDetail: {
    url: '/email/updateTemplateDetail',
    default: {},
  },
  // 根据id获取任务详情
  getTaskDetailById: {
    url: '/email/getTaskDetailById',
    default: {},
  },
  // 根据营销活动id获取邮件详情
  getDetailByMarketingActivityId: {
    url: '/email/getDetailByMarketingActivityId',
    default: {},
  },
  // 创建邮件链接内容
  addMailLinkContent: {
    url: '/email/addMailLinkContent',
    default: {},
  },
  getEmailSendReplyDataById: {
    url: '/email/getSendReplyDataById',
    default: {},
  },
  // 通过类型拉取发送明细
  querySendDetailsByType: {
    url: '/email/querySendDetailsByType',
    default: {},
  },
  // 查询邮件任务过滤列表
  queryFilterAddress: {
    url: '/email/queryFilterAddress',
    default: {},
  },
  // 获取发送失败邮件列表
  querySendErrorMail: {
    url: '/email/querySendErrorMail',
    default: {},
  },
  // 删除发送失败邮件信息
  deleteSendErrorMail: {
    url: '/email/deleteSendErrorMail',
    default: {},
  },
  // 新建编辑闪闪邮件模板
  shanShanEditCreateEmail: {
    url: '/shanShanEdit/shanShanEditCreateEmail',
    default: {},
  },
  // 查询闪闪邮件列表
  queryShanShanEditEmailList: {
    url: '/shanShanEdit/queryShanShanEditEmailList',
    default: {},
  },
  // 查询闪闪邮件详情
  queryShanShanEditEmailDetail: {
    url: '/shanShanEdit/queryShanShanEditEmailDetail',
    default: {},
  },
  // 查询邮件列表
  listPagerMergeTemplate: {
    url: '/email/listPagerMergeTemplate',
    default: {},
  },
  // 删除闪闪模板
  deleteShanShanTemplate: {
    url: '/shanShanEdit/deleteShanShanTemplate',
    default: {},
  },
  // 获取闪闪临时code
  shanShanEditGetCode: {
    url: '/shanShanEdit/shanShanEditGetCode',
    default: {},
  },
  // 保存邮件素材
  saveEmailMaterial: {
    url: '/emailMaterial/save',
    default: {},
  },
  getEmailMaterial: {
    url: '/emailMaterial/get',
    default: {},
  },
  // 获取邮件推广任务详情
  getNoticeByUser: {
    url: '/notice/getNoticeByUser',
    default: {},
  },
}
