import {
  role,
  crmDashboard,
  responseI18nPreset,
} from '@/modules/i18n-preset.js'

export default {
  getServiceMpQrCode: {
    url: '/channelsetting/getServiceMpQrCode',
    default: {
      appId: '',
    },
  },
  getFsBind: {
    url: '/fsBind/listFsBind',
    default: {},
  },
  getMarketingNoticeSetting: {
    url: '/fsBind/getMarketingNoticeSetting',
    default: {},
  },
  updateMarketingNoticeSetting: {
    url: '/fsBind/updateMarketingNoticeSetting',
    default: {},
  },
  fakeInvite: {
    url: '/notice/sendfsUserIdNoticeInvite',
    default: {},
  },
  queryEnterpriseInfo: {
    url: '/setting/queryEnterpriseInfo',
    default: {},
  },
  setEnterpriseInfo: {
    url: '/setting/setEnterpriseInfo',
    default: {},
  },
  queryClueAuditStatus: {
    url: '/setting/queryClueAuditStatus',
    default: {},
  },
  setClueAuditStatus: {
    url: '/setting/setClueAuditStatus',
    default: {},
  },
  reSyncThirdDataToCrm: {
    url: '/clueManagement/reSyncThirdDataToCrm',
    default: {},
  },
  getAppId: {
    url: '/setting/getAppId',
    default: {},
  },
  getOfficialWebsiteInfo: {
    url: '/hexagon/getOfficialWebsiteInfo',
    prefix: '/FHH/EM8HQYWXMARKETING',
    default: {},
  },
  updateOfficialWebsiteInfo: {
    url: '/hexagon/updateOfficialWebsiteInfo',
    prefix: '/FHH/EM8HQYWXMARKETING',
    default: {},
  },
  getBoundMiniappInfo: {
    url: "/setting/getBoundMiniappInfoV2",
    default: {},
  },
  getWxAuthLink: {
    url: "/wxThirdAuth/getWxAuthLink",
    default: {}
  },
  unAuthWxApp: {
    url: "/setting/unAuthWxApp",
    default: {}
  },
  commitCodeAndSubmitAudit: {
    url: "/setting/commitCodeAndSubmitAudit",
    default: {}
  },
  releaseCode: {
    url: "/setting/releaseCode",
    default: {}
  },
  configMiniAppAutoUpgrade: {
    url: "/setting/configMiniAppAutoUpgrade",
    default: {}
  },
  getMiniAppAutoUpgradeStatus: {
    url: "/setting/getMiniAppAutoUpgradeStatus",
    default: {}
  },
  getMerchantId: {
    url: '/merchant/getMerchantId',
    // type: 'GET',
    default: {},
    warnings: '*',
  },
  mergeMerchantConfig: {
    url: '/merchant/mergeMerchantConfig',
    default: {},
  },
  queryListRole: {
    url: '/setting/listRole',
    // type: 'GET',
    default: {},
    onResponse: response => responseI18nPreset(response, data => data.map(item => ({
      ...item,
      ...(item.roleId && role[item.roleId]
        ? {
          roleName: role[item.roleId].name,
          roleDescription: role[item.roleId].desc,
        }
        : {}),
    }))),
  },
  queryListUserRoles: {
    url: '/setting/listUserRoles',
    // type: 'GET',
    default: {},
    onResponse: response => responseI18nPreset(response, data => data.map(item => ({
      ...item,
      ...(item.roleIds && item.roleIds.length
        ? {
          roleNames: item.roleIds.map(id => (role[id] ? role[id].name : null)),
        }
        : {}),
    }))),
  },
  addUserRoles: {
    url: '/setting/addUserRoles',
    default: {},
  },
  deleteUserRoleByUser: {
    url: '/setting/deleteUserRoleByUser',
    default: {},
  },
  editUserRoles: {
    url: '/setting/editUserRoles',
    default: {},
  },
  getWechatAccountChannelList: {
    url: '/setting/getWechatAccountChannelList',
    // type: 'GET',
    default: {},
  },
  setMiniappIntroductionSite: {
    url: '/setting/setMiniappIntroductionSite',
    default: {},
  },
  updateWechatAccountChannelList: {
    url: '/setting/updateWechatAccountChannelList',
    default: {},
  },
  shareContentCreateOrUpdate: {
    url: '/shareContent/createOrUpdate',
    default: {},
  },
  queryMiniAppNavbarList: {
    url: '/minAppNavbar/queryMiniAppNavbarList',
    default: {},
  },
  setMiniAppNavbarFunctionApiname: {
    url: "/minAppNavbar/setMiniAppNavbarFunctionApiname",
    default: {}
  },
  getMiniAppNavbarFunctionApiname: {
    url: "/minAppNavbar/getMiniAppNavbarFunctionApiname",
    default: {}
  },
  deleteMiniAppNavbar: {
    url: '/minAppNavbar/deleteMiniAppNavbar',
    default: {},
  },
  setMiniAppNavbar: {
    url: '/minAppNavbar/setMiniAppNavbar',
    default: {},
  },
  sortMiniAppNavbar: {
    url: '/minAppNavbar/sortMiniAppNavbar',
    default: {},
  },
  setButtonFontColor: {
    url: '/minAppNavbar/setButtonFontColor',
    default: {},
  },
  // 弹窗设置
  setHomePageBall: {
    url: '/minAppNavbar/setHomePageBall',
    default: {},
  },
  queryHomePageBall: {
    url: '/minAppNavbar/queryHomePageBall',
    default: {},
  },
  updateSafetyManagementSetting: {
    url: '/safetyManagement/updateSafetyManagementSetting',
    default: {},
  },
  getSafetyManagementSetting: {
    url: '/safetyManagement/getSafetyManagementSetting',
    default: {},
  },
  getClueManagementSetting: {
    url: '/clueManagement/getClueManagementSetting',
    default: {},
  },
  updateClueManagementSetting: {
    url: '/clueManagement/updateClueManagementSetting',
    default: {},
  },
  // 获取市场活动通用设置
  getMarketingEventCommonSetting: {
    url: '/marketingEventCommonSetting/getMarketingEventCommonSetting',
    default: {},
  },
  // 更新市场活动通用设置
  updateMarketingEventCommonSetting: {
    url: '/marketingEventCommonSetting/updateMarketingEventCommonSetting',
    default: {},
  },
  // 获取市场活动字段信息
  getMarketingEventTypeField: {
    url: '/marketingEventCommonSetting/getMarketingEventTypeField',
    default: {},
  },
  // 获取市场活动业务组件布局
  getMarketingEvenCustomTagSetting: {
    url: '/marketingEvenCustomTagSetting/getMarketingEvenCustomTagSetting',
    default: {},
  },
  // 新增市场活动业务组件布局
  insertCustomTag: {
    url: '/marketingEvenCustomTagSetting/insertCustomTag',
    default: {},
  },
  // 更新市场活动业务组件布局
  updateCustomTag: {
    url: '/marketingEvenCustomTagSetting/updateCustomTag',
    default: {},
  },
  // 删除市场活动业务组件布局
  deleteCustomTag: {
    url: '/marketingEvenCustomTagSetting/deleteCustomTag',
    default: {},
  },
  getSceneTriggerTemplates: {
    url: '/marketingEventCommonSetting/getSceneTriggerTemplates',
    default: {},
  },
  getSceneHexagonTemplates: {
    url: '/marketingEventCommonSetting/getSceneHexagonTemplates',
    default: {},
  },
  // 设置默认表单模板
  setHexagonDefaultTemplate: {
    url: '/marketingEventCommonSetting/setDefaultTemplate',
    default: {},
  },
  // 设置小程序授权页封面
  setAuthorizePageCover: {
    url: '/minAppAuthorize/setAuthorizePageCover',
    default: {},
  },
  // 查询小程序授权页封面
  queryAuthorizePageCover: {
    url: '/minAppAuthorize/queryAuthorizePageCover',
    default: {},
  },
  // 获取CRM数据驾驶舱可用列表
  getCrmDashboard: {
    url: '/crmDashboard/getCrmDashboard?',
    default: {},
    onResponse: response => responseI18nPreset(response, data => data.map(item => ({
      ...item,
      ...(item.dashboardName && crmDashboard[item.dashboardName]
        ? {
          dashboardName: crmDashboard[item.dashboardName],
        }
        : {}),
    }))),
  },
  getCrmAnalyse: {
    url: '/crmDashboard/getCrmAnalyse',
    default: {},
    onResponse: response => responseI18nPreset(response, data => data.map(item => ({
      ...item,
      ...(item.dashboardName && crmDashboard[item.dashboardName]
        ? {
          dashboardName: crmDashboard[item.dashboardName],
        }
        : {}),
    }))),
  },
  // 删除导航栏
  deleteMiniAppCardNavbar: {
    url: '/minAppCardNavbar/deleteMiniAppCardNavbar',
    default: {},
  },
  // 获取小程序名片设置项
  getMiniappCardSetting: {
    url: '/minAppCardNavbar/getMiniappCardSetting',
    default: {},
  },
  // 查询名片导航菜单列表
  queryMiniAppCardNavbar: {
    url: '/minAppCardNavbar/queryMiniAppCardNavbar',
    default: {},
  },
  // 对导航栏排序
  sortMiniAppCardNavbar: {
    url: '/minAppCardNavbar/sortMiniAppCardNavbar',
    default: {},
  },
  // 更新小程序设置
  updateCardSetting: {
    url: '/minAppCardNavbar/updateCardSetting',
    default: {},
  },
  // 更新名片导航栏设置
  updateMiniAppCardNavbar: {
    url: '/minAppCardNavbar/updateMiniAppCardNavbar',
    default: {},
  },
  // 名片模板列表
  getCardTemplateList: {
    url: '/cardTemplate/list',
    default: {},
  },
  // 创建名片模板
  createOrUpdateCardTemplate: {
    url: '/cardTemplate/createOrUpdate',
    default: {},
  },
  // 删除名片模板
  deleteCardTemplate: {
    url: '/cardTemplate/delete',
    default: {},
  },
  // 设置名片部门
  saveCardSuitable: {
    url: '/cardTemplate/saveSuitable',
    default: {},
  },
  // 获取小程序跳转地址
  queryMiniAppForwardUrl: {
    url: '/minAppAuthorize/queryMiniAppForwardUrl',
    default: {},
  },
  // 开启/关闭营销插件
  saveOrUpdateMarketingPlugin: {
    url: '/marketingPlugin/saveOrUpdateMarketingPlugin',
    default: {},
    warnings: {
      88070: '',
    },
  },
  // 获取推广渠道列表
  querySpreadChannelList: {
    url: '/spreadChannel/querySpreadChannelList',
  },
  // 保存推广渠道信息
  saveSpreadChannelInfo: {
    url: '/spreadChannel/saveSpreadChannelInfo',
  },
  // 获取推广渠道信息
  querySpreadChannelInfo: {
    url: '/spreadChannel/querySpreadChannelInfo',
  },
  // 删除推广渠道信息
  deleteSpreadChannelInfo: {
    url: '/spreadChannel/deleteSpreadChannelInfo',
  },
  // 保存或更新线索存入失败发送通知的配置信息
  addOrUpdateSaveClueFailNoticeConfig: {
    url: '/clue/addOrUpdateSaveClueFailNoticeConfig',
    default: {},
  },
  // 获取线索存入失败发送通知的配置信息
  getSaveClueFailNoticeConfig: {
    url: '/clue/getSaveClueFailNoticeConfig',
    default: {},
  },
  /**
   * 获取目标人群自定义对象字段映射
   */
  getMarketingUserGroupCustomizeObjectMapping: {
    url: '/setting/getMarketingUserGroupCustomizeObjectMapping',
    default: {},
  },
  /**
   * 设置目标人群自定义对象字段映射
   */
  setMarketingUserGroupCustomizeObjectMapping: {
    url: '/setting/setMarketingUserGroupCustomizeObjectMapping',
    default: {},
  },
  /**
   * 删除目标人群自定义对象字段映射
   */
  deleteMarketingUserGroupCustomizeObjectMapping: {
    url: '/setting/deleteMarketingUserGroupCustomizeObjectMapping',
    default: {},
  },
  /**
   * 获取企业自定义对象apiname
   */
  findDescribField: {
    url: '/crm/findDescribField',
    default: {},
  },

  //  获取线索默认创建人设置
  getClueDefaultCreatorSetting: {
    url: '/clueManagement/getClueDefaultCreatorSetting',
    default: {},
  },

  //  更新线索默认创建人设置
  updateClueDefaultCreatorSetting: {
    url: '/clueManagement/updateClueDefaultCreatorSetting',
    default: {},
  },
  // 获取企业钱包信息
  queryISVInfoListForWeb: {
    urlType: 'FHH',
    url: '/EM1APAY/PAYWallet/queryISVInfoListForWeb',
    default: {},
  },
  // 获取H5授权公众号
  getH5AccessPermissionsSeeting: {
    url: '/setting/getH5AccessPermissionsSeeting',
    default: {},
  },
  // 保存H5授权公众号
  saveH5AccessPermissionsSeeting: {
    url: '/setting/saveH5AccessPermissionsSeeting',
    default: {},
  },
  getAuthorizedUserInformation: {
    url: '/web/weChatServiceMarketing/getAuthorizedUserInformation',
    default: {},
  },
  getMiniappAccessPermissionsSetting: {
    url: '/setting/getMiniappAccessPermissionsSetting',
    default: {},
  },
  saveMiniappAccessPermissionsSetting: {
    url: '/setting/saveMiniappAccessPermissionsSetting',
    default: {},
  },
  activeOrCloseAuthorization: {
    url: '/setting/activeOrCloseAuthorization',
    default: {},
  },
  fakeContentLogCreate: {
    url: '/content-log/create',
    default: {},
  },
  queryMaterialShowList: {
    url: '/material/show/queryMaterialShowList',
    default: {},
  },
  saveOrUpdateMaterialShow: {
    url: '/material/show/saveOrUpdateMaterialShow',
    default: {},
  },
  getQywxSidebarMaterialSendSetting: {
    url: '/fsBind/getQywxSidebarMaterialSendSetting',
    default: {},
  },
  updateQywxSidebarMaterialSendSetting: {
    url: '/fsBind/updateQywxSidebarMaterialSendSetting',
    default: {},
  },
  // 获取企微朋友圈群发通讯录设置
  getQywxContactSetting: {
    url: '/fsBind/getQywxContactSetting',
    default: {},
  },
  // 更新企微朋友圈通讯录设置
  updateQywxContactSetting: {
    url: '/fsBind/updateQywxContactSetting',
    default: {},
  },
  marketingEventGetAnalysisSetting: {
    url: '/marketingEventCommonSetting/getAnalysisSetting',
    default: {},
  },
  marketingEventUpdateAnalysisSetting: {
    url: '/marketingEventCommonSetting/updateAnalysisSetting',
    default: {},
  },
  /**
   * 获取营销用户是否开启关联对象列表（data返回的是排除列表，默认都是关联的）
   */
  getMarketingUserExcludeApiname: {
    url: '/setting/getMarketingUserExcludeApiname',
    default: {},
  },
  /**
  * 设置营销用户是否关闭关联对象  status为1时说明是排除关联该对象
  */
  setMarketingUserExcludeApiname: {
    url: '/setting/setMarketingUserExcludeApiname',
    default: {},
  },
  queryMarketingUserSyncObjectTagsStatus: {
    url: '/setting/queryMarketingUserSyncObjectTagsStatus',
    default: {},
  },
  setMarketingUserSyncObjectTagsStatus: {
    url: '/setting/setMarketingUserSyncObjectTagsStatus',
    default: {},
  },
  checkLicense: {
    url: '/marketingPlugin/checkLicense',
    default: {},
  },
  queryEnterpriseChannelsAndRolesInfo: {
    url: '/setting/queryEnterpriseChannelsAndRolesInfo',
    default: {},
  },
  // 添加新的角色权限
  addUserAccessible: {
    url: '/setting/addUserAccessible',
    default: {},
  },
  // 角色权限编辑
  editUserAccessible: {
    url: '/setting/editUserAccessible',
    default: {},
  },
  // 获取当前员工权限
  queryCurrentUserAccessible: {
    url: "/setting/queryUserAccessible",
    default: {}
  },
  // 新分版插件
  queryNewVersionPlugin: {
    url: "/user/queryNewVersionPlugin",
    default: {}
  },
  // 查询新分版插件是否可以开启
  queryPluginEnableOpen: {
    url: "/user/queryPluginEnableOpen",
    default: {}
  },
  checkLicenseList: {
    url: "/marketingPlugin/checkLicenseList",
    default: {}
  },
  // 保存验证码设置
  saveSmsVerCodeSetting: {
    url: "/smsVerCodeSetting/save",
    default: {}
  },
  // 查询验证码设置
  querySmsVerCodeSetting: {
    url: "/smsVerCodeSetting/get",
    default: {}
  },
  // 插件更新设置
  marketingPluginUpdateSetting: {
    url: '/marketingPlugin/updateSetting',
    default: {},
  },
  // 插件获取设置
  marketingPluginGetSetting: {
    url: '/marketingPlugin/getSetting',
    default: {},
  },
  // 创建SDR规则
  sdrCreateOrUpdateSDRRule: {
    url: '/sdr/createOrUpdateSDRRule',
    default: {},
  },
  // 生成沟通话术案例
  sdrCreateConversationCase: {
    url: '/sdr/createConversationCase',
    default: {},
  },
  // 查询知识库列表
  queryKnowledgeBaseList: {
    url: '/FHH/EM1HESERVICE2/eservice/knowledgeConfig/queryKnowledgeBaseList',
    default: {},
  },
  // 查询企业云环境信息
  getEnvironment: {
    url: "/setting/getEnvironment",
    default: {}
  },
};
