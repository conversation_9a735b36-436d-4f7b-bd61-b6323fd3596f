export default {
  getWebsiteById: {
    // 根据id获取官网详情
    url: '/officialWebsite/getWebsiteById',
    default: {},
  },
  addWebsiteTrackData: {
    // 增加追踪页面
    url: '/officialWebsite/addWebsiteTrackData',
    default: {},
  },
  queryPageData: {
    // 获取官网分页数据
    url: '/officialWebsite/queryPageData',
    default: {},
  },
  queryWebsiteTrackData: {
    // 查询追踪页面列表
    url: '/officialWebsite/queryWebsiteTrackData',
    default: {},
  },
  queryWebsiteSetting: {
    // 配置获取接口
    url: '/officialWebsite/setting',
    default: {},
  },
  queryWebsiteFormData: {
    // 获取官网绑定表单
    url: '/officialWebsite/queryWebsiteFormData',
    default: {},
  },
  updateWebsiteTrackData: {
    // 修改追踪页面
    url: '/officialWebsite/updateWebsiteTrackData',
    default: {},
  },
  addWebsiteData: {
    // 添加官网数据
    url: '/officialWebsite/addWebsiteData',
    default: {},
  },
  updateWebsiteData: {
    // 添加官网数据
    url: '/officialWebsite/updateWebsiteData',
    default: {},
  },
  getWebsiteLeadChart: {
    // 添加官网数据
    url: '/officialWebsite/getWebsiteLeadChart',
    default: {},
  },
  queryEventAttributesDetail: {
    // 获取事件/属性详情列表
    url: '/officialWebsite/queryEventAttributesDetail',
    default: {},
  },
  getEventAttributesDetailById: {
    // 获取事件/属性详情
    url: '/officialWebsite/getEventAttributesDetailById',
    default: {},
  },
  addWebsiteEventAttributes: {
    // 新增官网事件/属性
    url: '/officialWebsite/addWebsiteEventAttributes',
    default: {},
  },
  updateWebsiteEventAttributes: {
    // 更新官网事件/属性
    url: '/officialWebsite/updateWebsiteEventAttributes',
    default: {},
  },
  queryUtmCampaigLeadCount: {
    // 推广计划列表
    url: '/officialWebsite/queryUtmCampaigLeadCount',
    default: {},
  },
  /**
   * 官网关联吸粉二维码绑定
   */
  bindQywxQrCodeWithWebsite: {
    url: '/officialWebsite/bindQywxQrCodeWithWebsite',
    default: {},
  },
  /**
   *官网关联吸粉二维码解绑
   */
  unbindQywxQrCodeWithWebsite: {
    url: '/officialWebsite/unbindQywxQrCodeWithWebsite',
    default: {},
  },
  /**
   * 查询官网关联的吸粉二维码
   */
  queryWebsiteBindFanQrCode: {
    url: '/officialWebsite/queryWebsiteBindFanQrCode',
    default: {},
  },
  /**
   * 微信公众号渠道二维码绑定官网
   */
  bindOfficialWebsite: {
    url: '/wxOfficialAccounts/bindOfficialWebsite',
    default: {},
  },
  /**
   * 公众号渠道二维码解绑官网
   */
  unbindOfficialWebsite: {
    url: '/wxOfficialAccounts/unbindOfficialWebsite',
    default: {},
  },
  /**
   * 查询绑定了官网的公众号渠道二维码
   */
  queryOfficialWebsiteWxQrCode: {
    url: '/wxOfficialAccounts/queryOfficialWebsiteWxQrCode',
    default: {},
  },
  /**
   * 获取某个推广渠道线索数
   */
  getChannelClueCount: {
    url: '/officialWebsite/getChannelClueCount',
    default: {},
  },
  /**
   * 导入列表
   */
  importWebsiteTrackData: {
    url: '/officialWebsite/importWebsiteTrackData',
    default: {},
  },
  /**
   * 导出列表
   */
  exportWebsiteTrackData: {
    url: '/officialWebsite/exportWebsiteTrackData',
    default: {},
  },
  /**
   * 导出客服线索
   */
  exportOnlineServiceData: {
    url: '/officialWebsite/exportCustomerServiceLeadsData',
    default: {},
  },
  /**
   * 获取在线客服灰度获取
  */
  queryEnterCustomerServiceEaList: {
    url: '/marketingPlugin/queryEnterCustomerServiceEaList',
    default: {},
  },
  /**
   * 获取站点页面
  */
  querySeoPage: {
    url: '/websiteSeo/querySeoPage',
    default: {},
    warnings: {
      920001: '',
    },
  },
  /**
   * 获取站点统计内容
  */
  getDomainContent: {
    url: '/websiteSeo/getDomainContent',
    default: {},
  },
  /**
   * 获取站点基本信息
  */
  queryWebsiteBasicInfo: {
    url: '/websiteSeo/queryWebsiteBasicInfo',
    default: {},
  },
  /**
   * 获取站点关键词排行
  */
  queryKeyWordRankArg: {
    url: '/websiteSeo/queryKeyWordRankArg',
    default: {},
  },
  /**
   * 站点分析
  */
  officialWebsiteAnalyze: {
    url: '/websiteSeo/officialWebsiteAnalyze',
    default: {},
  },
  /**
   * 更新TDK数据
  */
  updateTdkData: {
    url: '/websiteSeo/updateTdkData',
    default: {},
  },
  /**
   * 查询TDK数据
  */
  queryTdkData: {
    url: '/websiteSeo/queryTdkData',
    default: {},
    warnings: {
      920001: '',
    },
  },
  /**
   * 查询seo数据初始化状态
  */
  checkSeoInitResult: {
    url: '/websiteSeo/checkSeoInitResult',
    default: {},
  },
  /**
   * 查询seo ai 优化模版
  */
  getAiTemplate: {
    url: '/websiteSeo/getAiTemplate',
    default: {},
  },
  /**
   * 查询seo ai 优化模版
  */
  promptCompletions: {
    url: '/websiteSeo/promptCompletions',
    default: {},
  },
  /*
   * 官网数据简报
  */
  officialWebsiteDataBriefing: {
    url: '/officialWebsite/officialWebsiteDataBriefing',
    default: {},
  },
  /**
   * 官网线索趋势
  */
  officialLeadsTrend: {
    url: '/officialWebsite/leadsTrend',
    default: {},
  },
  /**
   * 官网流量趋势
  */
  officialTrafficTrend: {
    url: '/officialWebsite/trafficTrend',
    default: {},
  },
  /**
   * 官网流量统计
  */
  officialTrafficSourceStatistic: {
    url: '/officialWebsite/trafficSourceStatistic',
    default: {},
  },
  /**
   * 官网关联CTA
  */
  queryWebsiteCta: {
    url: '/officialWebsite/queryWebsiteCta',
    default: {},
  },
}
