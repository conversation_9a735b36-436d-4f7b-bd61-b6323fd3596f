export default {
  // 分页拉取所有内容营销的市场活动
  listContentMarketingEvent: {
    url: '/contentMarketingEvent/listContentMarketingEvent',
  },
  // 获取内容营销的市场活动详情
  getMarketingEventsDetail: {
    url: '/marketingEvent/getMarketingEventsDetail',
  },
  // 分页获取营销内容列表
  listMarketingContent: {
    url: '/contentMarketingEvent/listMarketingContent',
  },
  getQrCodeByMarketingEventIdAndObjectInfo: {
    url: '/contentMarketingEvent/getOrCreateQrCodeByMarketingEventIdAndObjectInfo',
  },
  // 获取内容营销下的全部推广雷达
  getAllRadarList: {
    url: '/enterpriseSpread/listSpreadRadarMarketingActivity',
  },
  // 删除关联
  contentMarketingDeleteRelation: {
    url: '/marketingEvent/deleteRelation',
  },
  // 获取海报
  getPosters: {
    url: '/qrPoster/queryListByForwardTypeAndTargetId',
  },
  // 推广雷达
  getRadarList: {
    url: '/contentMarketingEvent/listMarketingActivityInContentMarketingEvent',
  },
  // 获取物料的线索
  getCluesList: {
    url: '/customizeFormData/queryFormUserData',
  },
  // 导出线索
  exportClues: {
    url: '/marketing/customizeFormData/exportEnrollsData',
    noPrefix: true,
  },
  // 查询员工市场活动线索统计
  queryStaffMarketingClueStatistics: {
    url: '/marketingEventStatistics/queryStaffMarketingClueStatistics',
  },
  // 查询员工市场活动粉丝排行
  queryStaffAddWechatFansStatistics: {
    url: '/marketingEventStatistics/queryStaffAddWechatFansStatistics',
  },
  // 获取市场活动下推广内容数据
  queryMarketingEventContentStatistics: {
    url: '/marketingEventStatistics/queryMarketingEventContentStatistics',
  },
  // 获取市场活动下渠道统计数据
  queryMarketingEventChannelStatistics: {
    url: '/marketingEventStatistics/queryMarketingEventChannelStatistics',
    warnings: {
      930001: '',
    },
  },
  /**
   * 获取微页面的统计数据
   */
  getHexagonContent: {
    url: '/contentMarketingEvent/getHexagonContent',
  },
  listMarketingEventStatisticTop5: {
    url: '/marketingEventStatistics/listMarketingEventStatisticTop5',
  },
  // 获取市场活动下渠道统计数据(访问次数 访问用户数)
  queryConferenceChannelStatistic: {
    url: '/conference/queryConferenceChannelStatistic',
    warnings: {
      930001: '',
    },
  },
  
}
