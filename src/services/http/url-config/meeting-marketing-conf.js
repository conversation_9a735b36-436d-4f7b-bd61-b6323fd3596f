import cookie from '@/utils/cookie';

function qsToken(requestOpts) {
  requestOpts.url += '&_fs_token=' + (cookie.get('fs_token') || '');
}
export default {
  queryMeetingList: {
    url: '/conference/list',
    default: {},
  },
  queryConferenceDetail: {
    url: '/conference/queryConferenceDetail',
    default: {},
    warnings: {
      '80824': $t('marketing.services.wcczdsjqx_08e0c3')
    }
  },
  getConferenceId: {
    url: '/conference/getConferenceId',
    default: {},
  },
  updateConferenceDetail: {
    url: '/conference/updateConferenceDetail',
    default: {},
  },
  updateConferenceEnrollSetting: {
    url: '/conference/updateConferenceEnrollSetting',
    default: {},
  },
  queryConferenceParticipants: {
    url: '/conference/queryConferenceParticipants',
    default: {},
  },
  //删除邀约人
  deleteInvite: {
    url: '/conference/deleteInvite',
    default: {},
  },
  //更新邀约人
  updateInviteInfo: {
    url: '/conference/updateInviteInfo',
    default: {},
  },
  //更改邀约状态
  updateInviteStatus: {
    url: '/conference/updateInviteStatus',
    default: {},
  },
  //查询邀约人员列表
  queryInviteParticipants: {
    url: '/conference/queryInviteParticipants',
    default: {},
  },
  //查询邀约进展
  queryInviteCountInfo: {
    url: '/conference/queryInviteCountInfo',
    default: {},
  },
  //导出邀约人员列表
  exportInviteData: {
    url: '/conference/exportInviteData',
    default: {},
  },
  //发送邀约企信
  inviteParticipants: {
    url: '/conference/inviteParticipants',
    default: {},
  },
  //更改参会人员签到状态
  changeConferenceParticipantsSignStatus: {
    url: '/conference/changeConferenceParticipantsSignStatus',
    default: {},
  },
  //手动审核参会人员状态
  changeConferenceParticipantsReviewStatus: {
    url: '/conference/changeConferenceParticipantsReviewStatus',
    default: {},
  },
  //将参会人员存入销售线索
  saveConferenceParticipantsToCrm: {
    url: '/conference/saveConferenceParticipantsToCrm',
    default: {},
  },
  //导出参会人员列表
  exportConferenceParticipants: {
    url: '/conference/exportConferenceParticipants',
    default: {},
  },
  //删除参会人员
  deleteParticipants: {
    url: '/conference/deleteParticipants',
    default: {},
  },
  //获取会议统计数据
  getConferenceStatisticData: {
    url: '/conference/getConferenceStatisticData',
    default: {},
  },
  // //查询当前企业所有二维码海报
  // queryListByForwardTypeAndTargetId: {
  //   url: '/qrPoster/queryListByForwardTypeAndTargetId',
  //   default: {},
  // },
  //查询会议二维码
  queryConferenceQrCode: {
    url: '/conference/queryConferenceQrCode',
    default: {},
  },
  //查询会议表单二维码
  queryConferenceFormQrCode: {
    url: '/conference/queryConferenceFormQrCode',
    default: {},
  },
  //查询会议二维码
  resetConferenceQrCode: {
    url: '/conference/resetConferenceQrCode',
    default: {},
  },
  // //推广雷达
  // listSpreadRadarMarketingActivity: {
  //   url: '/enterpriseSpread/listSpreadRadarMarketingActivity',
  //   default: {},
  // },
  //添加会议
  addConference: {
    url: '/conference/addConference',
    default: {},
  },
  //获取会议签到二维码
  getSignInQrUrl: {
    url: '/conference/getSignInQrUrl',
    default: {},
  },
  //获取某个会议最新添加的标签列表
  getConferenceTag: {
    url: '/conference/getConferenceTag',
    default: {},
  },
  //获取会议模板
  getTemplateData: {
    url: '/conference/getTemplateData',
    default: {},
  },
  //查询会议提醒设置
  queryNotificationSettings: {
    url: '/conference/queryNotificationSettings',
    default: {},
  },
  //会议提醒设置
  notificationSettings: {
    url: '/conference/notificationSettings',
    default: {},
  },
  // 邀请函设置
  invitationSetting: {
    url: '/conference/invitationSetting',
    default: {},
  },
  //查询邀请函详情
  queryInvitationInfo: {
    url: '/conference/queryInvitationInfo',
    default: {},
  },
  //分页查询邀请函
  queryInvitationList: {
    url: '/conference/queryInvitationList',
    default: {},
  },
  //更新邀请函状态
  updateInvitationStatus: {
    url: '/conference/updateInvitationStatus',
    default: {}, // status 0-启用 1-停用 2-未发布 99-删除
  },
  //更新会议状态
  updateConferenceStatus: {
    url: '/conference/updateConferenceStatus',
    default: {},
  },
  //获取报名导入模板
  getEnrollImportTemplate: {
    url: '/marketing/conference/getEnrollImportTemplate',
    noPrefix: true,
    default: {},
  },
  //导入邀约人员
  importUserData: {
    url: '/conference/importUserData',
  },
  //导入签到数据
  importSignInData: {
    url: '/conference/importSignInData',
  },

  // 创建微信会议卡券
  createMeetingTicket: {
    url: '/wxTicket/createMeetingTicket',
    default: {},
  },
  // 查询微信会议卡券详情
  queryMeetingTicketDetail: {
    url: '/wxTicket/queryMeetingTicketDetail',
    default: {},
  },
  // 获取企业绑定的微信公众号信息列表
  getCompanyWxOfficialAccountsList: {
    url: '/wxOfficialAccounts/getCompanyWxOfficialAccountsList',
    default: {},
  },
  // 检查微信公众号卡券权限
  checkWxTicketStatus: {
    url: '/wxTicket/checkWxTicketStatus',
    default: {},
  },

  //  ** 5.6 **
  // 新建或编辑会议
  createOrUpdateConference: {
    url: '/conference/createOrUpdate',
    default: {},
  },
  // 获取活动成员对象字段
  getCampaignMembersObjField: {
    url: '/conference/getCampaignMembersObjField',
    default: {},
  },
  // 添加活动成员
  addCampaignMembersObj: {
    url: '/conference/addCampaignMembersObj',
    default: {},
  },
  // 发送邀约企信
  inviteParticipants: {
    url: '/conference/inviteParticipants',
    default: {},
  },
  //更改参会人员分组信息
  updateGroupInfo: {
    url: '/conference/updateGroupInfo',
    default: {},
  },
  //创建市场活动
  addMarketingEventObj: {
    url: '/FHH/EM1HNCRM/API/v1/object/MarketingEventObj/action/Add',
    default: {},
    onRequest: qsToken,
  },
  // 获取验票人员
  queryTicketManager: {
    url: '/conference/queryTicketManager',
    default: {},
  },
  // 更新验票人员
  addTicketManger: {
    url: '/conference/addTicketManger',
    default: {},
  },
  // 更新验票人员
  getAllEnrollDataByCampaign: {
    url: '/conference/getAllEnrollDataByCampaign',
    default: {},
  },
  // 查询会议下所有的分组
  queryConferenceUserGroup: {
    url: '/conference/queryConferenceUserGroup',
    default: {},
  },
  // 获取市场活动下公众号统计信息
  queryCampaignWxStatistics: {
    url: '/conference/queryCampaignWxStatistics',
    default: {},
  },
  /**
   * 获取市场活动下微信客户统计信息
   */
  queryCampaignExternalContactStatistics: {
    url: '/conference/queryCampaignExternalContactStatistics',
    default: {},
  },
  queryAllCampaignData: {
    url: '/conference/queryAllCampaignData',
    default: {},
  },
  // 更新签到设置
  updateSignInSetting: {
    url: '/conference/updateSignInSetting',
    default: {},
  },
  // 获取签到设置
  getSignInSetting: {
    url: '/conference/getSignInSetting',
    default: {},
  },
  // 获取邀约通用设置
  getInvitationCommonSetting: {
    url: '/conference/getInvitationCommonSetting',
    default: {},
  },
  // 更改/添加邀约通用设置
  upsertInvitationCommonSetting: {
    url: '/conference/upsertInvitationCommonSetting',
    default: {},
  },
  updateConferenceContent: {
    url: '/conference/updateConferenceContent',
    default: {},
  },
  // 获取签到设置配置信息
  getSignInSuccessSetting: {
    url: '/conference/getSignInSuccessSetting',
    default: {},
  },
  // 更新签到设置配置信息
  updateSignInSuccessSetting: {
    url: '/conference/updateSignInSuccessSetting',
    default: {},
  },
  // 获取活动成员自定义字段
  getObjectCustomFields: {
    type: 'GET',
    url: '/conference/getObjectCustomFields',
    default: {},
  },
  // 更新会议主页标签
  updateConferenceTag: {
    url: '/conference/updateConferenceTag',
    default: {},
  },
  // 更新市场活动相关信息
  updateConferenceObjectData: {
    url: '/conference/updateConferenceObjectData',
    default: {},
  },
};
