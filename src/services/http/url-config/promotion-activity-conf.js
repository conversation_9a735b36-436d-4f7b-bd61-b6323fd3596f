export default {
  // 获取营销活动列表
  queryListMarketingActivity: {
    url: '/marketingActivity/listMarketingActivity',
    default: {},
  },

  // 获取营销数据概览
  getPromotionStatistic: {
    url: '/statistic/getAllEmployeeSpreadStatisticData',
    default: {},
  },
  // 获取营销活动详情
  queryMarketingActivityDetail: {
    url: '/marketingActivity/getMarketingActivityDetail',
    default: {},
    warnings: {
      320001401: '',
      320001400: '',
    },
  },

  // 新建营销活动
  fakeAddMarketingActivity: {
    url: '/marketingActivity/addMarketingActivity',
    default: {},
  },
  // 修改营销活动
  fakeUpdateMarketingActivity: {
    url: '/marketingActivity/updateMarketingActivity',
    default: {},
  },
  // 修改营销活动基本信息
  updateMarketingActivityBaseInfo: {
    url: '/marketingActivity/updateBaseInfo',
    default: {},
  },
  // 修改营销活动详情
  fakeUpdateMarketingActivityDetail: {
    url: '/marketingActivity/updateMarketingActivityDetail',
    default: {},
  },
  fakeCancelMarketingActivityRevokeSend: {
    url: '/marketingActivity/revokeSend',
    default: {},
  },
  fakeCancelMarketingActivitySend: {
    url: '/marketingActivity/cancelSend',
    default: {},
  },
  // 重发推广消息
  fakeSpreadMarketingActivityToSpecialEmployeeAgain: {
    url:
      '/marketingActivity/spreadMarketingActivityToSpecialEmployeeAgain',
    default: {},
  },
  // 重发伙伴推广消息
  spreadPartnerActivityToSpecialEmployeeAgain: {
    url:
      '/marketingActivity/spreadPartnerActivityToSpecialEmployeeAgain',
    default: {},
  },
  // 获取微联服务号标签
  getTags: {
    url: '/weChatServiceMarketing/getTags',
    default: {},
  },
  // 获取推广草稿
  getActivityDraft: {
    url: '/marketingActivityExternalConfig/get',
    default: {},
  },
  // 获取推广草稿
  addActivityDraft: {
    url: '/marketingActivityExternalConfig/add',
    default: {},
  },
  // 更新推广草稿
  updateActivityDraft: {
    url: '/marketingActivityExternalConfig/update',
    default: {},
  },
  // 批量获取发送通知统计数据
  batchGetSendNotificationStatistic: {
    url: '/marketingActivity/batchGetSendNotificationStatistic',
    default: {},
  },
  // 计算粉丝数
  // 不再做预计算，直接发送微信推广信息
  // queryCanFansCount: {
  //   url: '/weChatServiceMarketing/queryCanFansCount',
  //   default: {},
  // },
  // 营销活动员工推广排行
  queryListMarketingActivityEmployeeRanking: {
    url: '/enterpriseSpread/listMarketingActivityEmployeeRanking',
    default: {},
  },
  listMarketingActivityPartnerRanking: {
    url: '/enterpriseSpread/listMarketingActivityPartnerRanking',
    default: {},
  },
  // 企业微信 - 群发消息 - 发送明细
  queryListQywxMarketingActivityEmployeeRanking: {
    url: '/enterpriseSpread/listQywxMarketingActivityEmployeeRanking',
    default: {},
  },
  // 企微消息群发发送详情列表-员工发送情况
  listEmployeeQywxGroupSendDetail: {
    url: '/enterpriseSpread/listEmployeeQywxGroupSendDetail',
    default: {},
  },
  exportMarketingActivityEmployeeRanking: {
    url: '/enterpriseSpread/exportMarketingActivityEmployeeRanking',
  },
  // 伙伴营销推广导出
  exportMarketingActivityPartnerRanking: {
    url: '/enterpriseSpread/exportMarketingActivityPartnerRanking',
  },
  // 获取市场活动详情数据
  marketingEventObjDetail: {
    urlType: 'FHH',
    url: '/EM1HNCRM/API/v1/object/MarketingEventObj/controller/Detail',
    default: { objectDataId: '', objectDescribeApiName: 'MarketingEventObj' },
  },
  // 导出营销活动员工排行详情
  exportQywxMarketingActivityEmployeeRanking: {
    url: '/enterpriseSpread/exportQywxMarketingActivityEmployeeRanking',
  },
  // 企微消息群发发送详情列表-导出员工发送情况
  exportEmployeeQywxGroupSendDetail: {
    url: '/enterpriseSpread/exportEmployeeQywxGroupSendDetail',
  },
  // 立即发送推广
  immediatelySend: {
    url: '/marketingActivity/immediatelySend',
  },
  // 获取主动推广宣传语
  getObjectSlogan: {
    url: '/objectSlogan/getObjectSlogan',
  },
  // 设置主动推广宣传语
  setObjectSlogan: {
    url: '/objectSlogan/setSlogan',
  },
  // 新建多种营销活动
  addEmployeeMarketingActivity: {
    url: '/marketingActivity/addEmployeeMarketingActivity',
    default: {},
  }
}
