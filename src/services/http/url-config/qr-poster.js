export default {
  // 创建二维码海报
  createQRPoster: {
    url: '/qrPoster/createQRPoster',
  },
  // 删除二维码海报
  deleteQRPoster: {
    url: '/qrPoster/deleteQRPoster',
  },
  // 查询当前企业所有二维码海报
  queryListByEa: {
    url: '/qrPoster/queryListByEa',
  },
  // 根据跳转类型及素材ID查询海报
  queryListByForwardTypeAndTargetId: {
    url: '/qrPoster/queryListByForwardTypeAndTargetId',
  },
  // 获取海报详情
  queryPosterDetail: {
    url: '/qrPoster/queryDetail',
  },
  // 获取创客贴初始化参数
  queryChuangKeTieJsSdkOption: {
    url: '/qrPoster/queryChuangKeTieJsSdkOption',
  },
  // 拉取创客贴返回的图片
  syncChuangKeTiePoster: {
    url: '/qrPoster/syncChuangKeTiePoster',
  },
  /**
   * 海报分组增加适用角色
   */
  addQRPosterGroupRole: {
    url: '/qrPoster/addQRPosterGroupRole',
  },
  /**
   * 取消置顶海报
   */
  cancelTopQRPoster: {
    url: '/qrPoster/cancelTopQRPoster',
  },
  /**
   * 批量删除海报
   */
  deleteQRPosterBatch: {
    url: '/qrPoster/deleteQRPosterBatch',
  },
  /**
   * 删除海报分组
   */
  deleteQRPosterGroup: {
    url: '/qrPoster/deleteQRPosterGroup',
  },
  /**
   * 创建&编辑海报分组
   */
  editQRPosterGroup: {
    url: '/qrPoster/editQRPosterGroup',
  },
  /**
   * 获取海报分组列表
   */
  listQRPosterGroup: {
    url: '/qrPoster/listQRPosterGroup',
  },
  /**
   * 海报设置分组
   */
  setQRPosterGroup: {
    url: '/qrPoster/setQRPosterGroup',
  },
  /**
   * 置顶海报
   */
  topQRPoster: {
    url: '/qrPoster/topQRPoster',
  },
  /**
   * 获取海报分组适用角色
   */
  getQRPosterGroupRole: {
    url: '/qrPoster/getGroupRole',
  },
  /**
   * 根据物料id获取内容海报
   */
  queryEvenQrPosterByTargetId: {
    url: '/qrPoster/queryEvenQrPosterByTargetId',
  },
  // 批量删除海报
  deleteQRPosterBatch: {
    url: '/qrPoster/deleteQRPosterBatch',
  },

}
