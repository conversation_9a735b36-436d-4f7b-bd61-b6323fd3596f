<template />

<script>
import FxiaokeStaffSelector from "../all-selector/fxiaoke-staff-selector";
import QywxStaffSelector from "../all-selector/qywx-staff-selector";
import DingStaffSelector from "../all-selector/ding-staff-selector";
import kisvData from "../kisv-data";

export default {
  // 兼容老参数
  props: ["spDialogVisible", "selectResult", "dialogTitle", "options", "lock",'source'],
  data() {
    return {
      selector: null,
      globalData: kisvData.datas
    };
  },
  computed: {
    addressBookType() {
      return this.$store.state.Global.addressBookType;
    },
    isQywxOpen() {
      return this.$store.state.Global.isQywxOpen
    },
    defaultSelectedItems() {
      // 回填已选项
      let { colleague = [], depart = [], role = [], usergroup = [] } =
        this.selectResult || {};
      colleague = colleague.map(item => item.id);
      depart = depart.map(item => item.id);
      role = role.map(item => item.id);
      usergroup = usergroup.map(item => item.id);
      return {
        member: colleague,
        group: depart,
        role,
        usergroup
      };
    }
  },
  watch: {
    spDialogVisible(val) {
      // 响应 vue 显示隐藏参数
      if (val) {
        this.selector = this.newBox();
      } else {
        this.selector && this.selector.destroy && this.selector.destroy();
        this.selector = null;
      }
    }
  },
  methods: {
    newBox() {
      /* 营销通10.2营销数据需求：之前做的多组织开关开启后强行切换通讯录为fs通讯录的逻辑去除
        通讯录最新逻辑：1、默认使用fs通讯录
                        2、开通了企业微信的企微营销下的菜单全都使用企微通讯录
                        3、全员营销的通讯录随之后台通讯录设置走（全员推广以及全员推广统计）企微、fs以及钉钉
      */
      /**
       * 如果lock有值，使用lock类型
       * lock: fs,qywx
       */
      console.log('newBox',this.lock,this.isQywxOpen, this.source, this.addressBookType);
      if (this.lock === 'qywx' && this.isQywxOpen) {
        return this.getSelector('qywx');
      } else if(this.source === 'marketingPromotion'){
        // 全员营销
        return this.getSelector(this.addressBookType);
      } else{
        return this.getSelector('fs');
      }
    },
    getSelector(type) {
      switch (type) {
        case "fs":
          return this.fxiaokeSelectorBox();
        case "qywx":
          return this.qywxSelectorBox();
        case "ding":
          return this.dingSelectorBox();
      }
    },
    fxiaokeSelectorBox() {
      console.log('?????????', this.options, this.dialogTitle, this.defaultSelectedItems)
      return new FxiaokeStaffSelector({
        ...this.options,
        title: this.dialogTitle,
        defaultSelectedItems: this.defaultSelectedItems,
        onSubmit: data => this.onSubmit(data),
        onCancel: () => {
          this.cancel();
        }
      });
    },
    qywxSelectorBox() {
      return new QywxStaffSelector(
        {
          ...this.options,
          title: this.dialogTitle,
          defaultSelectedItems: this.defaultSelectedItems,
          onSubmit: data => {
            const _data = {}
            _data.member = data.qywxMember
            _data.group = data.qywxDepartment
            this.onSubmit(_data)
          },
          onCancel: () => {
            this.cancel();
          }
        },
        this.$store
      );
    },
    dingSelectorBox() {
      const me = this;
      return new DingStaffSelector(
        {
          ...this.options,
          title: this.dialogTitle,
          defaultSelectedItems: this.defaultSelectedItems,
          onSubmit: data => this.onSubmit(data),
          onCancel: () => {
            this.cancel();
          }
        },
        this.$store
      );
    },
    onSubmit(data) {
      const colleague = !data.member
        ? []
        : data.member.map(item => ({ ...item }));
      const depart = !data.group ? [] : data.group.map(item => ({ ...item }));
      const role = !data.role ? [] : data.role.map(item => ({ ...item }));
      const usergroup = !data.usergroup
        ? []
        : data.usergroup.map(item => ({ ...item }));
      this.submit({
        ...this.selectResult,
        colleague,
        depart,
        role,
        usergroup
      });
    },
    cancel() {
      this.$emit("update:spDialogVisible", false);
    },
    submit(values) {
      this.$emit("update:selectResult", values);
      this.cancel();
    }
  }
};
</script>

<style lang="less"></style>
