<template>
  <div :class="'selectbar-input' + (isShowMore ? ' has-more' : '')">
    <ul class="person-btn-wrap" ref="selectWrap">
      <li :key="item.id" v-for="item in formatSelects">
        <div class="btn-item">
          <span class="icon-people" v-if="item.type == 'colleague'"></span>
          <span class="selected-name">{{ item.name }}</span>
          <span class="btn-remove" @click="_remove(item.type, item.id)"
            ><svg
              data-v-bb38b5bc=""
              version="1.1"
              role="presentation"
              width="9.600000000000001"
              height="12.8"
              viewBox="0 0 12 16"
              class="octicon"
              style="font-size: 0.8em;"
            >
              <path
                d="M7.48 8l3.75 3.75-1.48 1.48L6 9.48l-3.75 3.75-1.48-1.48L4.52 8 .77 4.25l1.48-1.48L6 6.52l3.75-3.75 1.48 1.48z"
              ></path></svg
          ></span>
        </div>
      </li>
      <li class="selectbar-select-btn" ref="selectBtn" @click="_handleSelect">
        +{{ options.title || $t('marketing.commons.qxz_708c9d') }}
      </li>
    </ul>

    <span class="search-clear">
      <svg
        class="clear-svg"
        version="1.1"
        role="presentation"
        width="10"
        height="13"
        viewBox="0 0 12 16"
      >
        <path
          d="M7.48 8l3.75 3.75-1.48 1.48L6 9.48l-3.75 3.75-1.48-1.48L4.52 8 .77 4.25l1.48-1.48L6 6.52l3.75-3.75 1.48 1.48z"
        ></path>
      </svg>
    </span>
    <div v-if="isShowMore" class="btn-more" @click="_handleSelect">···</div>
    <StaffSelectorDialog
      :dialogTitle="options.title"
      :options="options"
      :singleselect="options.single"
      :selectResult="selectResult"
      @update:selectResult="updateSelectResult"
      :spDialogVisible.sync="selectPersonDialogVisible"
    ></StaffSelectorDialog>
  </div>
</template>

<script>
import StaffSelectorDialog from "@/modules/staff-selector-dialog/index";
export default {
  components: {
    StaffSelectorDialog,
  },
  props: {
    options: {
      type: Object,
      default: () => ({}),
    },
    defaultSelectedItems: {
      type: Object,
      default: () => ({}),
    },
    lock: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      isShowMore: false,
      selectPersonDialogVisible: false,
      selectResult: {},
    };
  },
  
  watch: {
    defaultSelectedItems() {
      this.initSelectData()
    },
    // selectResult() {
    //   if (JSON.stringify(this.defaultSelectedItems) !== JSON.stringify(this.selectedResult)) {
    //     this.$emit('change', this.selectResult);
    //   }
    // },
  },
  computed: {
    formatSelects() {
      const selectResult = this.selectResult;
      const results = Object.keys(selectResult).reduce((a, b) => {
        let arr = [];
        if (selectResult[b] instanceof Array) {
          arr = selectResult[b].map(item => ({
            ...item,
            type: b
          }));
        }
        return [...a, ...arr];
      }, []);

      return results;
    }
  },
  methods: {
    _handleSelect() {
      // this.$emit("update:select");
      this.selectPersonDialogVisible = true;
    },
    updateSelectResult(selectResult) {
      this.selectResult = selectResult;
      this.$emit('change', this.selectResult);
    },
    _remove(type, id) {
      const selectResult = this.selectResult;
      selectResult[type] = selectResult[type].filter(item => item.id != id);
      this.$emit('change', this.selectResult);
      // this.$emit('update:selects', this.selectResult);
    },
    initSelectData(){
      console.log('initSelectData', this.defaultSelectedItems, this.selectedResult)
      if (JSON.stringify(this.defaultSelectedItems) !== JSON.stringify(this.selectedResult)) {
        this.selectResult = this.defaultSelectedItems;
      }
    }
  },
  updated() {
    if (this.$refs.selectBtn.offsetTop > 5) {
      this.isShowMore = true;
    } else {
      this.isShowMore = false;
    }
  },
  mounted() {
    if (this.$refs.selectBtn.offsetTop > 5) {
      this.isShowMore = true;
    } else {
      this.isShowMore = false;
    }
    this.initSelectData();
  }
};
</script>

<style lang="less">
.selectbar-input {
  position: relative;
  z-index: 9;
  background: #fff;
  border: 1px solid @border-color-base;
  border-radius: 4px;
  overflow: hidden;
  &.has-more {
    padding-right: 55px;
  }
  &.over-line {
    .btn-more {
      display: block;
    }
    &.focus-blur {
      .search-clear {
        display: none;
      }
    }
  }
  &.focus-blur {
    padding-right: 56px;
    height: 34px;
    overflow: hidden;
  }
  &.focus-in {
    max-height: 200px;
    .btn-more {
      display: none;
    }
  }
  .selectbar-select-btn {
    display: inline-block;
    line-height: 31px;
    font-size: 12px;
    color: var(--color-primary06, #407fff);
    background: #fff;
    border-radius: 3px;
    cursor: pointer;
    vertical-align: top;
    padding: 0 15px;
  }
  .person-btn-wrap {
    height: 30px;
    line-height: 28px;
    overflow: hidden;
    display: flex;
    align-items: center;
    li {
      display: inline-block;
      white-space: nowrap;
    }
  }
  .btn-item {
    line-height: 16px;
    margin: 2px;
    border-radius: 2px;
    padding-left: 3px;
    background-color: rgba(225, 233, 250, 0.92);
    cursor: pointer;
    color: #212b36;
    white-space: nowrap;
    > span {
      display: inline-block;
      vertical-align: middle;
    }
    .selected-name {
      margin: 0 1px;
      max-width: 100px;
      height: 26px;
      line-height: 26px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-size: 12px;
    }
    .btn-remove {
      height: 16px;
      width: 16px;
      color: #c3ced9;
      text-align: center;
      cursor: pointer;
      margin-top: 2px;
    }
  }
  .btn-more {
    position: absolute;
    top: 0;
    right: 0;
    width: 52px;
    height: 26px;
    margin: 2px 4px;
    text-align: center;
    line-height: 26px;
    background-color: #eff3fc;
    color: #181c25;
    font-weight: 700;
    cursor: pointer;
    border-radius: 2px;
  }
  .search-clear {
    display: none;
    position: absolute;
    right: 0;
    width: 16px;
    height: 16px;
    margin: 6px 2px;
    border-radius: 8px;
    background-color: #c3cbd9;
    color: #fff;
    line-height: 16px;
    text-align: center;
    cursor: pointer;
  }
  .clear-svg {
    position: absolute;
    fill: currentColor;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  .icon-people {
    width: 14px;
    height: 14px;
    display: inline-block;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAAAXNSR0IArs4c6QAAAy9JREFUSA3tVl9IFEEYn2/3zOyUlCzISqOHHgSJJOyliCT/hZIGWdRrUBQY510qibbYgR3qGhZJSAi9RAoZvvhSZg/2oBCWUW9RakEcodWRnXc7X9+urm67o7dBT9HCMDu/789v5pv5zS5j//oDbhdYF7qbxiNzCkM8ggA7GbJXDOB+e4uvGwDQbR5XhP7grRwWjQ4T2S57YgA2VJCXXVFdXa3ZbaKxJAIdWDTaIyLT/RBZ2fjklN8RswqQkLC+qWMPZS1aJd6AESGwlt1qS0ioAeRbA0TvyHBzY7Bjh8hmxxISUoAbHxaPe1ydh4TJPIgv7LO0j4npS0i5NGXHReOEhK0ttROUcEQUbGII0g3zPVGfkFDXGCSvO0vHf0aUjCYzvHu7NySyiTBZBNqx50+HZosLy3pjnG0ksWcQeSoweCkBqKke/4VAYJ8rDep5HRvd19cnj72evoqclZA1BRi+bb8WOGmdRFdXV3JNTU3UxBpab2fEfsw/MRIi+4xM6ukI+h6admvvKOnY5PR15NhEki4g/eWRxk40BtVtZpC/ST0/FV54V6eoBSYWm4+Wk+9evZFESgG0/vrm9hLTbu0dhHRznLE6EDH8jLJjOkZkrQx5N/lkaXEcCTSrFToOyKv03nzILmkIp82xtXcQEsFWq4P+biak/uiKDanc/LCqqimEOVZDt/lyVVZiXIoaGRxSlM50OiyPrMEMpYFPc6wYETf8hq8xEKxQ5I1JEY2XM1keMK10SsNe2TdKZJUm5qZ3SWikqmxTfBMki/dLiQdzc/tJpWjso52McOE30j0hYqmi9K4nHS2WVZIGxt58PEhZN9Eux6jNL7VludgnoY9dE1Jib4TPFqFeVoBIdqb8mGmLp5Nuo1rJA8f1JjM8tTgBEd0fEBrhnFftz80apcvgniF8REMuiPwmj/MhvdGVQ/uMSfQb4rKkAN/Ec9MVCcZ+yd60K5eVznzKmLOaL5U+LLI5Skrz6hc5Ghhi5vjkzIFQw7mvyPmap5MkNCjK47GD2VuSLk6HF2J0xsrIlm63Ixi/G8/or62QVvHdbqfxBwbSnbaW2gcC23/o71fgF9bhH4uzuShoAAAAAElFTkSuQmCC)
      50% no-repeat;
    background-size: 14px 14px;
  }
}
</style>
