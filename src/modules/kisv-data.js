/*
分版控制、灰度、权限等数据处理
*/

import http from '@/services/http/index.js'
import { setMenuById } from '@/modules/menu/menu.js'

const HOST = window.location.host
const onJudgeListeners = []

const orgDatas = {
  // 这是分版控制的原始值，按最一般化的情况赋值，即标准版
  judged: false,
  isKis: false,
  // isPrivateCloud: HOST.match(/sbt\.|hwcloud\.|crm\.sbtjt\.com/) !== null, //是否是华为云
  isPrivateCloud:
    HOST.match(
      /www\.fxiaoke\.com|www\.ceshi112\.com|wow\.ceshi112\.com|wow\.fxiaoke\.com|crm\.fxiaoke\.com|crm\.ceshi112\.com|localhost/,
    ) === null, // 非纷享域名的都是专属云
  isGrayPrivateCloud:
    HOST.match(
      /crm\.unicloudea\.com|www\.fxiaoke\.com|www\.ceshi112\.com|wow\.ceshi112\.com|wow\.fxiaoke\.com|crm\.fxiaoke\.com|crm\.ceshi112\.com|localhost/,
    ) === null, // 非纷享域名的都是专属云
  strategy: 'stan', // stan | pro |dingTalk
  vName: $t('marketing.commons.bzb_de28d2'),
  invalidType: 1, // 三类原因导致无法使用营销通：1 权限接口报错，2 无权限，3 有权限但版本已过期(或者未购买)，后两需要展示功能简介
  invalidMsgs: [
    '',
    $t('marketing.modules.wffwyxtqlx_7ab98b'),
    $t('marketing.modules.rxsyqlxyyg_fddd66'),
    $t('marketing.modules.zswfsyqlxg_16c3dd'),
    $t('marketing.modules.ddsybbwqx_3119fc'),
  ],
  uinfo: {
    // 用户信息
    ea: '',
    ei: NaN,
    roleIds: [], // 角色权限
    enterpriseName: '',
    isAppAdmin: false, // 系统管理员
    isOperator: false, // 分销管理员
    isKisVersion: false,
    name: '',
    version: 'marketing_strategy_stan_app', // 标准版
  },
  // 插件信息
  pluginInfo: {},
  lenLimits: {
    wechat: 9999, // 不限制绑定的公众号数量
  },
  status: {
    crmark: true, // 设置 - 基础设置 - “营销推广是否启用市场活动” false:显示为开关 true:隐藏开关
    distributionLeadJudge: false, // 设置 - 基础设置 - 分销线索审核 false:显示为开关 true:隐藏开关
  },
  menuIds: {
    'social-distribution-global-index': {
      // 限制 社会化分销菜单
      noChild: true,
      invalid: true,
    },
    'promotion-group': {
      // 标准版也不限制微信群管理菜单
      invalid: false,
    },
    member: {
      // 会员管理
      invalid: true,
    },
    'website-access-init': {
      // 官网接入
      invalid: false, // 放开版本限制
    },
  },
}
const datas = $.extend(true, {}, orgDatas)

function setVkis(uinfo) {
  // 设置 KIS 渠道标志位
  datas.isKis = true
  // 从feature-2.3.2开始，kis侧也会开通市场活动
  // datas.status.crmark = false; // 企业推广是否启用市场活动
}

function setVstan(uinfo) {
  // 设置 标准版 版标志位
  datas.strategy = 'stan'
  datas.vName = $t('marketing.commons.bzb_de28d2')
}
function setVDing() {
  datas.strategy = 'dingTalk'
  datas.vName = $t('marketing.modules.ddmfsyb_74d4bf')
}

function setVpro(uinfo) {
  // 设置 专业版 版标志位
  datas.strategy = 'pro'
  datas.vName = $t('marketing.commons.zyb_3f0864')
  const { lenLimits } = datas
  lenLimits.wechat = 99999 // 不限制绑定的公众号数量
  const { menuIds } = datas
  let vmenu = {}
  vmenu = menuIds['social-distribution-global-index']
  vmenu.noChild = false
  vmenu.invalid = false
  vmenu = menuIds['promotion-group']
  vmenu.invalid = false
  vmenu = menuIds.member
  vmenu.invalid = false
  vmenu = menuIds['website-access-init']
  vmenu.invalid = false
  if (uinfo.marketingFlowOpen) {
    setMenuById('webhook', {
      hide: false,
    })
  }
}

function setVersionData(uinfo) {
  uinfo = $.extend(true, {}, uinfo)
  if (uinfo.roleIds) {
    uinfo.roleIds.forEach(role => {
      const key = `is-${role}`.replace(/-(\w)/g, s1 => s1.charAt(1).toUpperCase())
      uinfo[key] = true
    })
  }

  $.extend(true, datas, orgDatas)
  $.extend(true, datas.uinfo, uinfo)
  uinfo = datas.uinfo
  datas.invalidType = 0
  // 无角色权限
  if (uinfo.roleIds.length < 1 && !uinfo.isOperator) {
    datas.invalidType = 2
  }
  // uinfo.isKisVersion = false
  // if (!uinfo.isAppAdmin && !uinfo.isOperator) {
  //   datas.invalidType = 2;
  // }
  if (uinfo.version && uinfo.version.indexOf('marketing_strategy_pro') !== -1) {
    // 专业版
    setVpro(uinfo)
  } else if (uinfo.version && uinfo.version.indexOf('marketing_strategy_strengthen_app') !== -1) {
    /**
     * 旗舰版
     *
     * 注：目前旗舰版与专业版功能相同，暂时使用专业版
     */
    setVpro(uinfo)
  } else if (
    uinfo.version
    && uinfo.version.indexOf('marketing_strategy_stan') !== -1
  ) {
    // 标准版
    setVstan(uinfo)
  } else if (uinfo.version && uinfo.version.indexOf('marketing_strategy_free_dingtalk_app') !== -1) {
    // 嵌入到钉钉客户管理里的版本
    // || getUrlParameter(window.location.search, "source") == "dingTalk"
    setVDing()
    if (!uinfo.isAppAdmin) {
      datas.invalidType = 4
    }
  } else if (!datas.invalidType) {
    // 认定为未购买或者已过期，不允许使用营销通功能
    datas.invalidType = 3
  }
  if (uinfo.isKisVersion === true) {
    // 是否 KIS 渠道
    setVkis(uinfo)
  }
  datas.judged = true // 用户信息、分版权限判定完成
  onJudgeListeners.forEach(func => func())
  onJudgeListeners.length = 0
}

if (window.location.hostname.indexOf('ceshi') > 0) {
  // TODO 用于测试环境
  window.yxt_kisv_reset = {
    pro: () => {
      datas.uinfo.version = 'marketing_strategy_pro_app'
      datas.uinfo.isKisVersion = false
      return setVersionData(datas.uinfo)
    },
    sta: () => {
      datas.uinfo.version = 'marketing_strategy_stan_app'
      datas.uinfo.isKisVersion = false
      return setVersionData(datas.uinfo)
    },
    kis: () => {
      datas.uinfo.isKisVersion = true
      return setVersionData(datas.uinfo)
    },
    nop: () => {
      datas.uinfo.isAppAdmin = false
      datas.uinfo.isOperator = false
      return setVersionData(datas.uinfo)
    },
  }
}

function errorLogGetEmployee(errMsg) {
  datas.invalidType = 1
  http.$.alertError(errMsg || datas.invalidMsgs[datas.invalidType])
  // 获取登录信息失败，发出错日志
  const errorData = {
    eventType: 'session',
    type: 'error',
    request: '/FHH/EM8HMARKETING/user/getCurrentEmployeeMsg',
  }
  try {
    FS.util.uploadLog('appMarketingError', errorData)
  } catch (e) { /* empty */ }
}

function getCurrentEmployee() {
  // setVersionData({ version: "marketing_strategy_stan_app", isAppAdmin: true, roleIds: ["super-administrator"] });
  // return;
  return http
    .getCurrentEmployee()
    .then(async res => {
      if (res && res.errCode === 0) {
        const resData = res.data || {}
        resData.realVersion = resData.version
        datas.pluginInfo = {
          ...datas.pluginInfo,
          fxEmailSpreadEnabled: !!resData.fxEmailSpreadEnabled,
        }
        // 钉钉试用版本，从钉钉客户管理-全员营销进去的，都只能看见钉钉免费试用版的信息@陈川，手动将版本号改成钉钉试用版，从那里进去的链接上会带有source=dingTalk 如果要修改链接@柯南颖
        if (resData.version === 'marketing_strategy_free_dingtalk_app' || window.location.href.indexOf('source=dingTalk') > -1) {
          // 处理菜单，手动添加dingTalk-try权限控制，但由于首页和运营计划是所有权限都可看，所以手动隐藏菜单，后续如果有所有权限都展示而这个版本不展示的也需要隐藏
          setMenuById('home', { hide: true })
          setMenuById('kanban', { hide: true })
          resData.roleIds = ['dingTalk-try']
          resData.version = 'marketing_strategy_free_dingtalk_app'
          // 将tab更改为全员营销
          setTimeout(() => {
            document.title = $t('marketing.commons.qyyx_4c4100')
          }, 3000)
        }
      } else {
        errorLogGetEmployee(res.errMsg) // 获取用户身份信息出错，认定为严重错误，不允许继续使用营销通任何功能
      }
      return res
    })
    .catch(e => {
      console.log('errorLogGetEmployee', e)
      errorLogGetEmployee()
    })
}

const pluginKeyMap = {
  64: 'memberMarketingPluginEnable',
  201: 'meetingMarketing',
  202: 'livingMarketing',
  203: 'adMarketing',
  204: 'websiteMarketing',
  205: 'miniappMarketing',
  206: 'employeeMarketing',
  207: 'smsMarketing',
  208: 'mailMarketing',
  209: 'qywxMarketing',
  210: 'weChatAccount',
  211: 'marketingPlan',
  // 查询是否能使用社会化分销和营销流程
  212: 'socialAndAutomationEnable',
  213: 'faceBookClueLink',
  214: 'xiaohongshuClue',
  215: 'douyingLifeClue',
  220: 'mailchimpPluginEnable',
}

function flatArrayPluginToObject(pluginList, pluginType = 'pluginType') {
  const pluginMap = {}
  const pluginStatusMap = {}
  pluginList.forEach(p => {
    pluginMap[p[pluginType]] = !!p.status
  })
  Object.keys(pluginMap).forEach(key => {
    pluginStatusMap[pluginKeyMap[key]] = !!pluginMap[key]
  })

  return pluginStatusMap
}

async function batchGetMarketingPluginStatus(pluginTypeList = []) {
  return new Promise(async resolve => {
    const res = await http.batchGetMarketingPluginStatus(pluginTypeList && pluginTypeList.length ? { pluginTypeList } : {})
    if (res && res.errCode === 0 && res.data) {
      const pluginList = res.data.dataList || []
      const pluginStatusMap = flatArrayPluginToObject(pluginList)
      datas.pluginInfo = {
        ...datas.pluginInfo,
        ...pluginStatusMap,
      }
      resolve(true)
    }
    resolve(false)
  })
}
// 营销通10.6需求 拆开插件状态到新接口
function queryNewVersionPlugin() {
  return new Promise(async resolve => {
    const res = await http.queryNewVersionPlugin()
    if (res && res.errCode === 0 && res.data) {
      const plugin = res.data.plugins || []
      const pluginStatusMap = flatArrayPluginToObject(plugin, 'type')
      datas.pluginInfo = {
        ...datas.pluginInfo,
        ...pluginStatusMap,
      }
      resolve(true)
    }
    resolve(false)
  })
}

// getCurrentEmployee() // 获取用户信息，越早越快越好，几乎所有权限、分版相关逻辑，全部依赖这份数据

async function getAllJudgePermissions() {
  const res = await Promise.all([getCurrentEmployee(), batchGetMarketingPluginStatus([64, 214, 215, 220]), queryNewVersionPlugin()]) || []
  if (res[0] && res[0].errCode === 0) {
    const resData = res[0].data || {}
    setVersionData(resData || {})
  }
}

getAllJudgePermissions()

function onJudge(func) {
  if (datas.judged) {
    setTimeout(func, 0)
  } else {
    onJudgeListeners.push(func)
  }
}

function getSets(id) {
  if (id && typeof id !== 'string') {
    id = id.id || id.name
  }
  return datas.menuIds[id] || {}
}

export default {
  datas,
  onJudge,
  getSets,
  getCurrentEmployee, // 对外暴露，用于用户开通完权限后回来
}
