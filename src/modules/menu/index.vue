<template>
  <div
    :class="['m-menu-wrapper','marketing-scrollbar-theme',!(userLanguage === 'zh-CN' || userLanguage === 'zh-TW') && 'm-en-menu-wrapper']"
    :style="{ overflow: isMouseOn && !fold ? 'auto' : 'hidden'}"
    @mouseout.capture="isMouseOn = false"
    @mouseover.capture="isMouseOn = true"
  >
    <!-- 头部 -->
    <div class="menu-title">
      <!-- <div v-if="!vDatas.isKis" class="menu-title-icon" @click="handleBackToHome"></div> -->
      <!-- kis版的折叠态菜单，没有返回按钮 -->
      <div
        v-if="!fold || vDatas.isKis"
        class="version-header__wrapper"
      >
        <!-- 钉钉版相关版本 -->
        <template v-if="versionInfo.platform == 'dingTalk'">
          <span>{{ $t('marketing.commons.fxyxt_93418a') }}</span>
          <fx-popover
            placement="bottom-start"
            width="190"
            trigger="hover"
          >
            <div class="version-desc">
              {{ versionInfo.desc }}
              <span
                class="link"
                @click="openddLink"
              >{{ $t('marketing.modules.ljgdyxtnl_24ee1b') }}</span>
            </div>
            <div
              v-if="versionFlagInfo"
              class="version-flag"
            >
              <img
                slot="reference"
                class="version-logo"
                :src="versionFlagInfo.icon"
                alt=""
              >
              <div>{{ versionFlagInfo.label }}</div>
            </div>
          </fx-popover>
        </template>
        <!-- 普通版相关版本 -->
        <template v-else>
          <span>{{ $t('marketing.commons.yxt_0f6c96') }}</span>
          <div
            v-if="versionFlagInfo"
            :style="versionFlagInfo.style"
            class="version-flag"
          >
            <img
              slot="reference"
              class="version-logo"
              :src="versionFlagInfo.icon"
              alt=""
            >
            {{ versionFlagInfo.label }}
          </div>
        </template>
      </div>
    </div>
    <!-- 父级路由 -->
    <div class="menu-wrapper">
      <template v-for="(menu, index) in authMenus">
        <div
          v-if="menu.separate"
          v-show="!menu.hide"
          :key="index"
          class="menu-separate-line"
        >
          {{ menu.name }}
        </div>
        <template v-else>
          <div
            v-if="isShow(menu)"
            :key="index"
            :class="['menu-item', menu.icon, isSelected(menu)]"
            @click="handleClickMenu(menu)"
          >
            <img
              v-if="menu.iconImg"
              :src="menu.iconImg"
              class="hexagon-icon"
              alt=""
            >
            <i
              v-else
              :class="{
                'hexagon-icon': /^hicon/.test(menu.iconfont),
                'iconfont': /^icon/.test(menu.iconfont),
                [menu.iconfont]: true,
              }"
              :style="{ color: menu.iconColor }"
            />
            <div
              v-if="!fold"
              class="menu-item-content"
              :style="{ width: (userLanguage === 'zh-CN' || userLanguage === 'zh-TW') ? '140px' : '190px' }"
            >
              <span>{{ menu.name }}</span>
              <span
                v-if="_isOwnValidChild(menu)"
                :class="['menu-arrow-icon', menu.unfold && 'unfold']"
              />
            </div>
          </div>

          <!-- 子级路由
          <transition v-if="!fold" :key="index" name="el-zoom-in-center">
            <div class="menu-item-wrapper" v-if="menu.unfold">
              <template v-for="sub in menu.children">
                <div
                  v-if="isShow(sub)"
                  :key="sub.id"
                  :class="['menu-item-sub', (sub === mDatas.selected || sub.id === mDatas.selected.id) && 'selected']"
                  @click="handleClickMenu(sub)"
                >
                  <span>{{ sub.name }}</span>
                </div>
              </template>
            </div>
          </transition> -->
          <!-- 子级路由 -->
          <div :key="'sub_' + index">
            <fx-collapse-transition v-if="!fold">
              <div v-show="menu.unfold">
                <template v-for="sub in menu.children">
                  <!-- 父菜单隐藏时子菜单不应该渲染 -->
                  <div
                    v-if="isShow(sub) && isShow(menu)"
                    :key="sub.id"
                    :class="['menu-item-sub', (sub === mDatas.selected || sub.id === mDatas.selected.id) && 'selected']"
                    @click="handleClickMenu(sub)"
                  >
                    <span>{{ sub.name }}</span>
                  </div>
                </template>
              </div>
            </fx-collapse-transition>
          </div>
        </template>
      </template>
    </div>

    <!-- 底部 -->
    <div
      v-if="!fold"
      :class="['menu-footer',!(userLanguage === 'zh-CN' || userLanguage === 'zh-TW') && 'en-menu-footer']"
      @mouseout="onMouseFoot"
      @mouseover="onMouseFoot"
    >
      <!-- 管理员才有设置，运营人员没有 -->
      <!-- <el-tooltip
        class="item"
        effect="dark"
        content="系统设置"
        placement="top-start"
      >
        <div
          class="footer-item menu-footer-setting"
          @click="handleGoToSetting"
          v-if="vDatas.uinfo.isAppAdmin"
        ></div>
      </el-tooltip>

      <div class="fi-split" v-if="vDatas.uinfo.isAppAdmin"></div> -->

      <fx-tooltip
        class="item"
        effect="dark"
        :content="$t('marketing.commons.bzwd_c39ced')"
        placement="top-start"
      >
        <div
          class="footer-item menu-footer-question"
          @click="handleGotoHelp"
        />
      </fx-tooltip>
      <div class="fi-split" />

      <fx-tooltip
        class="item"
        effect="dark"
        :content="$t('marketing.commons.sqdh_fe74be')"
        placement="top-start"
      >
        <div
          class="footer-item menu-footer-fold"
          :title="tiptxt"
          @click="handleFoldMenu"
        />
      </fx-tooltip>
    </div>
    <div
      v-if="fold"
      :class="['menu-footer','folded']"
      @mouseout="onMouseFoot"
      @mouseover="onMouseFoot"
    >
      <fx-tooltip
        class="item"
        effect="dark"
        :content="$t('marketing.commons.zkcd_513c32')"
        placement="top-start"
      >
        <div
          class="footer-item menu-footer-foldx"
          :title="tiptxt"
          @click="handleFoldMenu"
        />
      </fx-tooltip>
    </div>
  </div>
</template>

<script>
import _ from 'lodash'
import {
  menus, mDatas, highLight, toggle, getMenuById, addMenu,
} from './menu.js'
import kisvData from '@/modules/kisv-data.js'
import http from '@/services/http/index.js'

const versionFlag = {
  free: {
    label: $t('marketing.modules.mfb_0357cd'),
    style: {
      color: '#41457a',
      background: 'linear-gradient(90deg,#CDCDEE,#A39FE5)',
      filter: 'drop-shadow(1px 1px 0 #3D4378)',
    },
    icon: require('@/assets/images/version-icon-1.png'),
  },
  standard: {
    label: $t('marketing.commons.bzb_de28d2'),
    style: {
      color: '#404b6e',
      background: 'linear-gradient(90deg,#F9F9FA,#A1B0D7)',
      filter: 'drop-shadow(1px 1px 0 #343A69)',
    },
    icon: require('@/assets/images/version-icon-2.png'),
  },
  professional: {
    label: $t('marketing.commons.zyb_3f0864'),
    style: {
      color: '#582601',
      background: 'linear-gradient(90deg,#EACC9E,#C29254)',
      filter: 'drop-shadow(1px 1px 0 #662D00)',
    },
    icon: require('@/assets/images/version-icon-3.png'),
  },
  strengthen: {
    label: $t('marketing.modules.qjb_ce3d5f'),
    style: {
      color: '#a23807',
      background: 'linear-gradient(90deg,#FFCD68,#FFB648)',
      filter: 'drop-shadow(1px 1px 0 #AC5E12)',
    },
    icon: require('@/assets/images/version-icon-4.png'),
  },

}

export default {
  props: {
    fold: {
      type: Boolean,
      default: false, // 是否折叠态菜单
    },
    tiptxt: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      menus,
      mDatas,
      vDatas: kisvData.datas,
      isMouseOn: false,
      versionInfo: {
        platform: '', // normal | dingTalk
        type: '', // free | stan | pro | stren
        desc: '',
      },
      marketingUserObjectLists: [], // 自定义对象菜单
    }
  },
  computed: {
    roleIds() {
      return this.$store.state.roleIds
    },
    authMenus() {
      // 菜单权限控制逻辑
      const roleIds = this.roleIds || []
      // 超级管理员直接返回所有菜单权限
      if (roleIds.indexOf('super-administrator') !== -1) {
        return this.menus
      }
      // 检查权限， 父级菜单需检验路由是否拥有权限
      const checkRole = (menusList, menu, parent = false) => {
        // 不是分割线并且是显示状态菜单才进行权限校验
        if (!menu.separate && !menu.hide) {
          const { route = {} } = this.$router.resolve({
            name: menu.path || menu.id,
          })
          if (
            (route.meta.auth && route.meta.auth.some(role => roleIds.includes(role)))
            || (!parent && !route.meta.auth)
          ) {
            menusList.push(menu)
          }
        }
      }
      // 递归菜单做权限控制
      const each = data => data.reduce((menusList, menu, index) => {
        // 如果拥有子菜单先进行子菜单权限校验
        if (menu.children && menu.children.length) {
          const children = each(menu.children) || []
          if (children.length) {
            menusList.push({
              ...menu,
              children,
            })
          } else {
            checkRole(menusList, menu, true)
          }

          return menusList
        }
        // 不是分割线，并且菜单不是隐藏状态时进行菜单权限校验
        checkRole(menusList, menu)
        // 菜单分割线逻辑, 如果当前是分割线和上一个是分割线则移除上一个分割线
        if (menu.separate) {
          const prevItem = menusList[menusList.length - 1]
          if (prevItem && prevItem.separate) {
            menusList.splice(menusList.length - 1, 1)
          }
          menu.hide = false
          menusList.push(menu)
        }

        return menusList
      }, [])

      return each(this.menus)
    },
    versionFlagInfo() {
      return this.versionInfo && this.versionInfo.type ? versionFlag[this.versionInfo.type] : null
    },
    userLanguage(){
      return FS.userLanguage
    }
  },
  watch: {
    $route(to) {
      this.handleRouteChange(to)
    },
  },
  mounted() {
    console.log('this.vDatas--', this.vDatas)
    console.log('authMenus', this.authMenus)
    this.InitVersion()
    this.handleRouteChange(this.$route)
    this.getMarketingUserGroupCustomizeObjectMapping()
  },

  methods: {
    openddLink() {
      const uinfo = this.vDatas.uinfo || {}
      window.open(
        `https://h5.dingtalk.com/appcenter/index-pc.html?funnelsource=&corpId=${uinfo.dingCorpId}&ddtab=true&ddpin=true#/detail/DT_GOODS_881636355722025`,
      )
    },
    InitVersion() {
      const uinfo = this.vDatas.uinfo || {}
      const { realVersion } = uinfo
      const currentDate = new Date().getTime()
      const expireTime = Number(uinfo.expireTime) || currentDate
      const surplus = expireTime - currentDate
      const day = Math.ceil(surplus / 1000 / 60 / 60 / 24)
      console.log('uinfo--------', uinfo)
      /**
       * 钉钉版本
       */
      if (/marketing_strategy_stan_dingtalk.*_app/.test(realVersion)) {
        console.log('uinfo--------标准', uinfo)
        this.versionInfo = {
          platform: 'dingTalk',
          type: 'standard',
          desc: $t('marketing.commons.thgqzccjgs_ee1ddb', { data: { option0: day } }),
        }
      } else if (/marketing_strategy_pro_dingtalk.*_app/.test(realVersion)) {
        this.versionInfo = {
          platform: 'dingTalk',
          type: 'professional',
          desc: $t('marketing.commons.thgqzccjgs_ee1ddb', { data: { option0: day } }),
        }
      } else if (/marketing_strategy_free_dingtalk.*_app/.test(realVersion)) {
        this.versionInfo = {
          platform: 'dingTalk',
          type: 'free',
          desc: $t('marketing.modules.wsyqxxzzcc_4ba514'),
        }
      }

      /**
       * 正常版本
       */
      if (/marketing_strategy_pro_app/.test(realVersion)) {
        this.versionInfo = {
          platform: 'normal',
          type: 'professional',
        }
      } else if (/marketing_strategy_stan_app/.test(realVersion)) {
        this.versionInfo = {
          platform: 'normal',
          type: 'standard',
        }
      } else if (/marketing_strategy_strengthen_app/.test(realVersion)) {
        this.versionInfo = {
          platform: 'normal',
          type: 'strengthen',
        }
      }
    },

    onMouseFoot(evt) {
      // 折叠态的底部按钮区，不要触发临时展开态
      if (this.fold) {
        evt.stopPropagation()
      }
    },
    handleFoldMenu() {
      this.$emit('fold-menu')
    },
    handleBackToHome() {
      window.location.hash = '#app/manage/myapps'
    },
    handleGotoHelp() {
      window.open('https://help.fxiaoke.com/93d4')
    },
    handleGoToSetting() {
      highLight('')
      this.$router.push({ name: 'setting-setitems' })
    },

    handleClickMenu(menu) {
      const { name } = this.$route
      sessionStorage.setItem(`MarketingActiveGroupId_${name}`, '')
      const hasChild = this._isOwnValidChild(menu)
      if (!hasChild) {
        if (!menu.outlink) {
          highLight(menu)
          const vmenu = this.vDatas.menuIds[menu.id] || {}
          const route = vmenu.vroute
            || menu.vroute || {
            name: menu.path || menu.id,
            params: menu.params,
            query: menu.query,
          }

          const { resolved } = this.$router.resolve(route)
          const {
            meta: { target },
          } = resolved
          if (target === '_blank') {
            const { fullPath } = resolved
            const [base] = window.location.href.split('#')
            window.open(`${base}#${fullPath}`)
            return
          }

          this.$router.push(route)
        } else {
          window.open(menu.outlink)
        }
      } else {
        toggle(menu)
        // this.isMouseOn = !this.isMouseOn;
        setTimeout(() => this.$forceUpdate(), 10)
      }
    },
    handleRouteChange(route) {
      console.log('handleRouteChange: ', route)
      const menu = getMenuById(route.meta.high || route.name)
      if (menu) {
        highLight(menu)
      }
    },
    isShow(menu) {
      // 华为云隐藏会员管理
      if (menu.id === 'member' && kisvData.datas.isGrayPrivateCloud) {
        return false
      }
      return !menu.hide && !kisvData.getSets(menu).hide
    },
    isSelected(menu) {
      const selectedId = this.mDatas.selected.id
      if (menu === this.mDatas.selected || menu.id === selectedId) {
        return 'selected'
      }
      if (this.fold && menu.childDic[selectedId]) {
        // 折叠态菜单，子项被选中时，父菜单高亮
        return 'selected'
      }
      return ''
    },
    _isOwnValidChild(menu) {
      const vmenu = this.vDatas.menuIds[menu.id] || {}
      if (vmenu.noChild) {
        return false
      }
      return _.find(menu.children, sub => !sub.hide)
    },
    /**
     * 添加自定义对象菜单
     */
    getMarketingUserGroupCustomizeObjectMapping() {
      http.getMarketingUserGroupCustomizeObjectMapping().then(({ errCode, data }) => {
        if (errCode === 0) {
          this.marketingUserObjectLists = (data || []).map(o => ({
            id: o.objectApiName,
            name: o.objectName,
            path: 'object-template',
            query: {
              crmApiName: o.objectApiName,
              crmName: o.objectName,
            },
          }))
          this.marketingUserObjectLists.reduce((pre, cur) => {
            if (getMenuById(cur.id)) return pre
            addMenu(cur, 'user-marketing', pre)
            pre = cur.id
            return pre
          }, 'enterprise-base')
        }
      })
    },
  },
}
</script>

<style lang="less">
@icon: '../../assets/images/icon/';
.m-menu-wrapper {
  width: 200px;
  height: 100%;
  box-sizing: border-box;
  background: @color-primary;
  border-right: 1px solid @border-color-base;
  &.m-en-menu-wrapper{
      width: 250px;
  }
  .menu-wrapper {
    padding-bottom: 50px;
    > div:last-child {
      &.menu-separate-line {
        display: none;
      }
    }
  }
  .menu-title {
    display: flex;
    align-items: center;
    padding-left: 20px;
    height: 56px;
    color: @color-heading;
    font-size: 16px;
    font-weight: bold;
    background-color: @color-primary;
    border-bottom: 1px solid #e9edf5;
    box-sizing: border-box;
    font-weight: bold;
    .version {
      width: 49px;
      height: 20px;
      border-radius: 3px;
      background-color: rgba(255, 242, 229, 1);
      display: inline-block;
      margin-left: 10px;
      color: var(--color-primary06, #ff8000);
      font-size: 12px;
      font-weight: normal;
      line-height: 20px;
      text-align: center;
      cursor: pointer;
    }
    .menu-title-icon {
      margin-left: -6px;
      width: 16px;
      height: 19px;
      cursor: pointer;
      background: url('@{icon}icon-arrow-orange.png') center / cover no-repeat;
      border: 8px solid transparent;
    }
  }

  .version-header__wrapper {
    display: flex;
    align-items: center;
  }

  .menu-footer {
    display: flex;
    align-items: center;
    justify-content: space-around;
    position: absolute;
    bottom: 0;
    width: 200px;
    height: 50px;
    box-sizing: border-box;
    background: @color-primary;
    border-top: 1px solid @border-color-base;
    border-right: 1px solid @border-color-base;
    &.en-menu-footer{
      width: 250px;
    }
    .fi-split {
      width: 0;
      height: 16px;
      border-left: 1px solid #dfe2e7;
    }
    &.folded {
      width: 66px;
      height: 50px;
    }
    .footer-item {
      position: relative;
      width: 100%;
      height: 100%;
      background: transparent center / 16px no-repeat;
      cursor: pointer;
    }
    // .menu-footer-setting {
    //   background-image: url("@{icon}menu-setting.png");
    //   &:hover {
    //     background-image: url("@{icon}menu-setting-actived.png");
    //   }
    // }
    .menu-footer-question {
      background-image: url('@{icon}menu-question.png');
      &:hover {
        background-image: url('@{icon}menu-question-actived.png');
      }
    }
    // 菜单图标 - 折叠
    .menu-footer-fold {
      background-image: url('@{icon}menu-fold.png');
      &:hover {
        background-image: url('@{icon}menu-fold-actived.png');
      }
    }
    // 菜单图标 - 展开
    .menu-footer-foldx {
      background-image: url('@{icon}menu-foldx.png');
      &:hover {
        background-image: url('@{icon}menu-foldx-actived.png');
      }
    }
  }
  .menu-separate-line {
    padding: 16px 0 10px 20px;
    font-size: 13px;
    color: @color-disabled;
    border-top: 1px solid @border-color-base;
  }
  .menu-item,
  .menu-item-sub {
    padding: 0 30px 0 44px;
    height: 40px;
    line-height: 40px;
    font-size: @font-size-base;
    color: @color-title;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    transition: height 0.3s;
    border-left: 2px solid @color-primary;
    cursor: pointer;
  }
  // .menu-item-wrapper{
  //   transform-origin: top;
  //   transform: scaleY(1);
  //   transition: transform 0.3s ease-out;
  // }
  .menu-item-sub {
    padding: 10px 0 10px 44px;
    // transition: max-height 0.3s;
    line-height: 20px;
    max-height: 60px;
    height: auto;
    span{
        display: -webkit-box;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        white-space: normal;
    }
  }
  .menu-item:hover,
  .menu-item.selected,
  .menu-item-sub:hover,
  .menu-item-sub.selected {
    background-color: #f0f4fc;
    border-left: 2px solid @color-actived;
  }
  .menu-item-content {
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    .menu-arrow-icon {
      width: 16px;
      height: 16px;
      background: url('@{icon}icon-arrow-grey.png') center / cover no-repeat;
      transition: transform 0.5s;
    }
    .menu-arrow-icon.unfold {
      transform: rotate(90deg);
    }
  }
  // 动画效果
  .slide-enter-active,
  .slide-leave-active {
    transition: max-height 0.3s;
  }

  .slide-enter, .slide-leave-to /* .fade-leave-active below version 2.1.8 */ {
    .menu-item-sub {
      max-height: 0;
    }
  }

  .menu-item {
    // 菜单图标
    display: flex;
    background-position: 22px center;
    background-size: 16px;
    background-repeat: no-repeat;
    position: relative;
    &.selected,
    &:hover {
      background-color: #f0f4fc;
    }
    .iconfont, .hexagon-icon {
      width: 18px;
      margin-right: 10px;
      font-size: 18px;
      position: absolute;
      top: 50%;
      left: 18px;
      transform: translate(0, -50%);
    }
    // 菜单图标 - 首页
    // &.home {
    //   background-image: url('@{icon}menu-home.png');
    //   &.selected,
    //   &:hover {
    //     background-image: url('@{icon}menu-home-actived.png');
    //   }
    // }
    // // 菜单图标 - 运营计划
    // &.kanban {
    //   background-image: url('@{icon}kanban.png');
    //   &.selected,
    //   &:hover {
    //     background-image: url('@{icon}kanban-actived.png');
    //   }
    // }
    // // 菜单图标 - 营销日历
    // &.marketing-calendar {
    //   background-image: url('@{icon}menu-calendar.png');
    //   &.selected,
    //   &:hover {
    //     background-image: url('@{icon}menu-calendar_actived.png');
    //   }
    // }
    // // 菜单图标 - 线索工作台
    // &.lead-workbench {
    //   background-image: url('@{icon}menu-lead.png');
    //   &.selected,
    //   &:hover {
    //     background-image: url('@{icon}menu-lead-hover.png');
    //   }
    // }
    // // 菜单图标 - 会议营销
    // &.meeting-marketing-init {
    //   background-image: url('@{icon}menu-meeting.png');
    //   &.selected,
    //   &:hover {
    //     background-image: url('@{icon}menu-meeting_actived.png');
    //   }
    // }
    // &.content-marketing {
    //   background-image: url('@{icon}menu-content.png');
    //   &.selected,
    //   &:hover {
    //     background-image: url('@{icon}menu-content_actived.png');
    //   }
    // }
    // //直播营销
    // &.live-marketing {
    //   background-image: url('@{icon}liev-marketing-icon.png');
    //   &.selected,
    //   &:hover {
    //     background-image: url('@{icon}liev-marketing-icon-hover.png');
    //   }
    // }
    // // 菜单图标 - 全员营销
    // &.promotion {
    //   background-image: url('@{icon}menu-crowd.png');
    //   &.selected,
    //   &:hover {
    //     background-image: url('@{icon}menu-crowd-actived.png');
    //   }
    // }
    // // 菜单图标 - 伙伴营销
    // &.partner-marketing {
    //   background-image: url('@{icon}menu-partner.png');
    //   &.selected,
    //   &:hover {
    //     background-image: url('@{icon}menu-partner-actived.png');
    //   }
    // }
    // // 菜单图标 - 公众号营销
    // &.wechat {
    //   background-image: url('@{icon}menu-wechat.png');
    //   &.selected,
    //   &:hover {
    //     background-image: url('@{icon}menu-wechat-actived.png');
    //   }
    // }
    // // 菜单图标 - 短信营销
    // &.sms-marketing {
    //   background-image: url('@{icon}menu-sms.png');
    //   &.selected,
    //   &:hover {
    //     background-image: url('@{icon}menu-sms-actived.png');
    //   }
    // }
    // &.tel-marketing {
    //   background-image: url('@{icon}menu-tel-marketing.png');
    //   &.selected,
    //   &:hover {
    //     background-image: url('@{icon}menu-tel-marketing-actived.png');
    //   }
    // } // 菜单图标 - 邮件营销
    // &.mail-marketing {
    //   background-image: url('@{icon}menu-mail.png');
    //   &.selected,
    //   &:hover {
    //     background-image: url('@{icon}menu-mail-actived.png');
    //   }
    // }
    // // 菜单图标 - 社会化分销
    // &.social-distribution {
    //   background-image: url('@{icon}menu-tree.png');
    //   &.selected,
    //   &:hover {
    //     background-image: url('@{icon}menu-tree-actived.png');
    //   }
    // }
    // // 菜单图标 - 会员中心
    // &.member-center {
    //   background-image: url('@{icon}menu-member.png');
    //   &.selected,
    //   &:hover {
    //     background-image: url('@{icon}menu-member-actived.png');
    //   }
    // }
    // // 菜单图标 - 运营用户
    // &.user-marketing {
    //   background-image: url('@{icon}menu-active.png');
    //   &.selected,
    //   &:hover {
    //     background-image: url('@{icon}menu-active-check.png');
    //   }
    // }
    // &.marketing-user {
    //   background-image: url('@{icon}menu-manager.png');
    //   &.selected,
    //   &:hover {
    //     background-image: url('@{icon}menu-manager-actived.png');
    //   }
    // }
    // // 菜单图标 - 内容中心
    // &.promotion-apps {
    //   background-image: url('@{icon}menu-book.png');
    //   &.selected,
    //   &:hover {
    //     background-image: url('@{icon}menu-book-actived.png');
    //   }
    // }
    // // 菜单图标 -小程序管理
    // &.vapp-setting {
    //   background-image: url('@{icon}menu-link.png');
    //   &.selected,
    //   &:hover {
    //     background-image: url('@{icon}menu-link-actived.png');
    //   }
    // }
    // // 菜单图标 -官网接入
    // &.website-access-init {
    //   background-image: url('@{icon}menu-website.png');
    //   &.selected,
    //   &:hover {
    //     background-image: url('@{icon}menu-website-actived.png');
    //   }
    // }
    // // 菜单图标 - 数据简报
    // &.report {
    //   background-image: url('@{icon}menu-computer.png');
    //   &.selected,
    //   &:hover {
    //     background-image: url('@{icon}menu-computer-actived.png');
    //   }
    // }
    // // 菜单图标 - 营销流程
    // &.marketing-automation {
    //   background-image: url('@{icon}menu-process.png');
    //   &.selected,
    //   &:hover {
    //     background-image: url('@{icon}menu-process-actived.png');
    //   }
    // }
    // // 菜单图标 - 广告营销
    // &.ad-marketing {
    //   background-image: url('@{icon}menu-ad.png');
    //   &.selected,
    //   &:hover {
    //     background-image: url('@{icon}menu-ad-actived.png');
    //   }
    // }
    // // 菜单图标 - 企业微信营销
    // &.qywx-manage {
    //   background-image: url('@{icon}qywx-manage.png');
    //   &.selected,
    //   &:hover {
    //     background-image: url('@{icon}qywx-manage-actived.png');
    //   }
    // }
    // &.setting-setitems {
    //   background-image: url('@{icon}menu-setting.png');
    //   &.selected,
    //   &:hover {
    //     background-image: url('@{icon}menu-setting-actived.png');
    //   }
    // }
  }
}
.version-desc {
  font-size: 12px;
  color: #181c25;
  line-height: 17px;
  .link {
    color: var(--color-primary06, #407fff);
    cursor: pointer;
  }
}
.version-flag {
  display: flex;
  margin-left: 18px;
  height: 17px;
  white-space: nowrap;
  font-size: 12px;
  font-weight: 700;
  box-sizing: border-box;
  padding: 0 6px 0 12px;
  border-radius: 2px;
  position: relative;

  .version-logo {
    height: 18px;
    position: absolute;
    top: 0;
    left: 0;
    transform: translateX(-50%);
  }
}
</style>
