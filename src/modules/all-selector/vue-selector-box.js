/**
 * @description v2版本选人组件
 */
class VueSelectorBox {
  constructor(options) {
    this.options = options;
    this.events = {};
    this.init();
  }
  init() {
    //tabs里数据列表增加order正序
    try {
      if(this.options.tabs && this.options.tabs.length) {
        this.options.tabs.forEach(tab => {
          if (tab.data && tab.data.children && tab.data.children.length) {
            tab.data.children.forEach((item, index) => {
              if(item.order === undefined) {
                item.order = index;
              }
            });
          }
        });
      }
    } catch (error) {}
    const selectorOpts = FS.selectorParseContactV2.parseContacts(this.options);
    console.log('selectorOpts:', selectorOpts);
    const _this = this;
    this.selector = FxUI.create({
      wrapper: 'body',
      template: `
          <fx-selector-box-v2
            ref='selectorBox'
            title="${_this.options.title||''}"
            :show.sync="showSelectorBox"
            v-bind="opts"
            @confirm="confirm"
            @cancel="cancel"
            @selector-mounted="onSelectorMounted"
          >
          <div slot="footer-left" v-if="${!!_this.options.addButton}" style="text-align:left;"><fx-button type="text" icon="fx-icon-add-2" @click="addButtonClick">${_this.options.addButton}</fx-button><div>
          </fx-selector-box-v2>
        `,
      data: function () {
        return {
          showSelectorBox: false,
          opts: selectorOpts,
        }
      },
      methods: {
        getSelectedItems(){
          const selectedItems = this.$refs.selectorBox.getSelectedItems();
          const value = selectedItems.reduce((acc, cur) => {
            if(!acc[cur.tabId]){
              acc[cur.tabId] = [];
            }
            if(cur.data)
            acc[cur.tabId].push(cur.data);

            return acc;
          }, {});
          return value;
        },
        confirm() {
          _this.emit('selected', this.getSelectedItems());
        },
        cancel() {
          _this.destroy();
          _this.emit('hide');
        },
        addButtonClick(){
          if(_this.options.addButtonClick){
            _this.options.addButtonClick();
          }
        },
        onSelectorMounted: function () {
          this.$nextTick(() => {
            this.showSelectorBox = true;
            setTimeout(() => {
              _this.emit('mounted');
              //搜索框添加自定义样式
              $(this.$refs.selectorBox.$refs.searchPanel.$el).find('.search-input').css('width', '100%');
            }, 0);
          });
        },
      },
    });
    // 临时强制显示弹窗
    setTimeout(() => {
      if (this.selector) {
        this.selector.showSelectorBox = true;
      }
    }, 300);
  }
  getSelectedItems(){
    return this.selector.getSelectedItems();
  }
  destroy() {
    this.selector.$destroy();
  }
  on(eventType, cb) {
    if (this.events[eventType]) {
      this.events[eventType].push(cb);
    } else {
      this.events[eventType] = [cb];
    }
  }
  //事件触发
  emit(eventType, ...args) {
    if (this.events[eventType]) {
      this.events[eventType].forEach(cb => {
        cb.apply(this, args);
      });
    }
  }
}

export default VueSelectorBox;