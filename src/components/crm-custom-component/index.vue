<template>
  <div
    :class="[
      'object-component-wrapper',
      this.objectDetailData.id === 'form_component' &&
        'object-component-form-wrapper'
    ]"
  >
    <div class="objectComponentWrapper"></div>
  </div>
</template>
<script>
import { ObjectDetailTypeEnum } from "./const";
export default {
  props: {
    marketingEventId: {
      type: String,
      default: ""
    },
    objectDetailData: {
      type: Object,
      default: () => ({})
    },
    currentTab: {
      type: String,
      default: ""
    }
  },
  //旧版
  // components: {
  //   RelatedList: FxUI.component.get('ObjectDetailRelatedlist'),
  // },
  //新版
  // components: {
  //   RelatedList(resolve) {
  //     FxUI.component
  //       .get("ObjectDetail")()
  //       .then(ObjectDetail => {
  //         // resolve(ObjectDetail.default.components.RelatedlistNew)
  //         resolve(ObjectDetail.default.components.BPMRelatedList);
  //       });
  //   }
  // },
  data() {
    return {
      apiName: "MarketingEventObj",
      dataId: this.marketingEventId,
      compInfo: this.objectDetailData.component.props || {},
      hooks: {
        parseParam(param, table) {
          // 请求数据前的参数
          return param;
        }
      }
    };
  },
  watch: {
    objectDetailData: {
      handler(newVal) {
        if (newVal && newVal.id && this.currentTab && /^custom-/.test(this.currentTab)) {
          this.$nextTick(() => {
            this.initComponent()
          })
        }
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    // 确保在组件挂载时，如果有有效的 currentTab 和 objectDetailData 才初始化
    if (this.currentTab && /^custom-/.test(this.currentTab) && this.objectDetailData && this.objectDetailData.id) {
      this.$nextTick(() => {
        this.initComponent()
      })
    }
  },
  methods: {
    initComponent() {
      const _id = this.objectDetailData.id;
      const _component =
        ObjectDetailTypeEnum[_id] || ObjectDetailTypeEnum["relatedlist"];
      if (_component) {
        FxUI.component
          .get("ObjectDetail")()
          .then(ObjectDetail => {
            const ObjectDetailComponent = Vue.extend(
              ObjectDetail.default.components[_component]
            );
            new ObjectDetailComponent({
              propsData: {
                apiName: this.apiName,
                dataId: this.dataId,
                compInfo: this.compInfo,
                hooks: this.hooks
              }
            }).$mount(".objectComponentWrapper");
          });
      }
    }
  }
};
</script>

<style lang="less" scoped>
.object-component-wrapper {
  margin: 10px 0;
  padding: 0 !important;
  margin-top: 0;
  background: #fff;
  border-radius: @border-radius-base;
}
.object-component-form-wrapper {
  padding: 0 10px !important;
}
</style>