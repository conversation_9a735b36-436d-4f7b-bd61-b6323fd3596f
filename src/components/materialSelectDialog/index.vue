<template>
  <fx-dialog
    :visible.sync="visible"
    :title="$t('marketing.commons.tjnr_f75488')"
    width="800px"
    :before-close="handleClose"
    ref="materialSelectDialog"
    id="guide__material-select-dialog"
    custom-class="material-select-dialog"
  >
    <div class="material-select-content">
      <div class="material-grid">
        <div
          v-for="(item, index) in materialTypes"
          :key="index"
          class="material-item"
          @click="handleSelect(item)"
        >
          <div class="material-icon">
            <img :src="item.icon" :alt="item.title">
          </div>
          <div class="material-title">{{ item.title }}</div>
          <div class="material-desc">{{ item.description }}</div>
        </div>
      </div>
    </div>
    <template #footer>
      <fx-button size="small" plain  @click="handleClose">{{ $t('marketing.commons.gb_b15d91') }}</fx-button>
    </template>
  </fx-dialog>
</template>

<script>

export default {
  name: 'MaterialSelectDialog',
  data() {
    return {
      visible: false,
      selectedType: '',
      materialTypes: [
        {
          type: 'private_site',
          title: $t('marketing.commons.xjwym_cc26bf'),
          description: $t('marketing.commons.cjghdxgdxc_d668f4'),
          icon: require('@/assets/images/material/site.png')
        },
        {
          type: 'content_center',
          title: $t('marketing.commons.xznr_bcbe25'),
          description: $t('marketing.commons.cnrzxxzwzc_1840e5'),
          icon: require('@/assets/images/material/material.png')
        },
        {
          type: 'external_content',
          title: $t('marketing.commons.yywbnr_6d566f'),
          description: $t('marketing.commons.zctjwbnrlj_2f138a'),
          icon: require('@/assets/images/material/externalicon.png')
        },
        {
          type: 'upload_pdf',
          title: $t('marketing.commons.scwj_a6fc9e'),
          description: $t('marketing.commons.jzcscyyndw_55a3e2'),
          icon: require('@/assets/images/material/file.png')
        }
      ]
    }
  },
  methods: {
    show() {
      this.visible = true
    },
    handleClose() {
      this.visible = false
      this.selectedType = ''
    },
    handleSelect(item) {
      this.selectedType = item.type
      this.$emit('select', this.selectedType)
    },
  }
}
</script>

<style lang="scss" scoped>
.material-select-dialog {
  /deep/ .el-dialog__body {
    padding: 56px;
  }
}

.material-select-content {
  .material-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
  }

  .material-item {
    padding: 30px 10px;
    border: 1px solid var(--color-neutrals05);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    position: relative;

    &:hover{
      border-color: var(--color-primary06,#ff8000);
    }
    &.active {
      border-color: var(--color-primary06,#ff8000);
      &::before {
        content: '';
        position: absolute;
        top: -1px;
        left: -1px;
        width: 24px;
        height: 24px;
        background: url('../../assets/images/material/selected.png') no-repeat;
        background-size: contain;
        z-index: 1;
      }
    }
    .material-icon {
      width: 80px;
      height: 80px;
      margin-bottom: 10px;

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }

    .material-title {
      font-size: 14px;
      font-weight: 500;
      color: #181C25;
      margin-bottom: 4px;
    }

    .material-desc {
      font-size: 12px;
      color: #91959E;
      line-height: 1.5;
    }
  }
}
</style>

