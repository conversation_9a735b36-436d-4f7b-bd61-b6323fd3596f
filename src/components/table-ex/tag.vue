<template>
  <div
    ref="$wrapper"
    class="v-table-custom-tag"
  >
    <fx-popover
      trigger="click"
      popper-class="marketing-tag-popover"
      width="300"
      :placement="placement"
    >
      <div
        slot="reference"
        class="tag_wrapper"
      >
        <template v-if="tags && tags.length > 0">
          <template v-for="(item, index) in tags">
            <span
              v-if="index < displayTagsNum"
              :key="index"
              :class="['tag', index + 1 === tags.length && 'last']"
            >{{ item[nameKey] + (item.secondTagName ? `：${item.secondTagName}` : '') }}</span>
          </template>
          <span
            v-if="displayTagsNum < tags.length"
            :class="['tag']"
          />
        </template>

        <template v-else>
          {{ emptyText }}
        </template>
      </div>
      <div class="tag_wrapper_pop">
        <SmartLayout
          :gutter="10"
          child-type="auto"
        >
          <span
            v-for="(item, index) in tags"
            :key="index"
            class="tag_pop"
          >{{ (item[nameKey] + (item.secondTagName ? `：${item.secondTagName}` : '')) }}</span>
        </SmartLayout>
      </div>
    </fx-popover>
  </div>
</template>

<script>
/*
 ** @desc          ElTableEx 扩展组件 - 两行文本省略号
 ** @params        columns 元素中添加 exCompnent: 'text' 配置参数
 **                data 元素中对应字段数据为 { text: '', link: 'wxlink', pathName: 'route', pathParams: {id: 1}, isRedirect: false, ishtml: false } 配置参数
 */
import _ from 'lodash'
import { redirectToFS } from '@/utils/index.js'
import SmartLayout from '@/components/SmartLayout/index.vue'

export default {

  components: {
    SmartLayout,
  },
  props: {
    data: {
      type: Array,
      default: () => [],
    },
    nameKey: {
      type: String,
      default: 'firstTagName',
    },
    emptyText: {
      type: String,
      default: '--',
    },
    placement: {
      type: String,
      default: 'bottom',
    },
  },
  data() {
    return {
      tags: [],
    }
  },
  computed: {
    tips() {
      if (this.data.tips === 0 || this.data.tips) {
        return this.data.tips.toString()
      }

      if (this.data.text === 0 || this.data.text) {
        this.data.text.toString()
      }

      return ''
    },
    displayTagsNum() {
      return this._getNumsTag2Show(this.tags)
    },
  },
  watch: {
    data() {
      this.tags = this.data || []
    },
  },
  mounted() {
    this.tags = this.data || [] // 到 mounted 时机再赋值，使得渲染 tags 时能获取当前元素的总宽度，从而计算出最多展示多少个 tag
  },

  methods: {
    handleClickText() {
      const {
        link, pathName, pathParams, isRedirect,
      } = this.data
      this.$emit('click', this.data)
      if (!isRedirect) return

      if (link) {
        redirectToFS(link)
      }

      if (pathName) {
        this.$router.push({ name: pathName, params: pathParams })
      }
    },
    /* 获取可显示标签数量 */
    _getNumsTag2Show(tags) {
      const width = this.$refs.$wrapper.offsetWidth
      // console.log('_getNumsTag2Show', tags.map(item => item.combineName).join('、'), 'width:' , width);
      let acc = 0
      let len = 0
      _.some(tags, (tag, index) => {
        acc
          += (tag[this.nameKey] + (tag.secondTagName ? `${$t('marketing.commons.m_e929ba', { data: ({ option0: tag.secondTagName }) })}` : '')).replace(/[^\x00-\xff]/g, 'xx')
            .length * 6 + 26 + 16 // 26: margin+padding  16: “更多标签”区块的宽度

        if (acc >= width) {
          // console.log('tag acc width', tag.combineName, acc, width, len)
          return true // 退出遍历
        }
        len = index + 1
        // console.log('tag acc width', tag.combineName, acc, width, len)
        return false
      })
      return len
    },
  },
}
</script>

<style lang="less">
@basePath: '../../assets/images/';
.v-table-custom-tag {
  cursor: pointer;
  .tag_wrapper {
    display: flex;
    align-items: center;
    overflow: hidden;
    white-space: nowrap;
    gap: 8px;
    .tag {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 1px 8px;
      line-height: 18px;
      color: #545861;
      font-size: 12px;
      border-radius: 4px;
      background: #F2F4FB;
      box-sizing: border-box;
      border: 0.5px solid #DEE1E8;
    }
    .tag:last-child:not(.last) {
      width: 26px;
      background: #F2F4FB url('@{basePath}/icon/ellipsis-icon.png') center/14px no-repeat;
      height: 20px;
    }
  }
}
.tag_wrapper_pop {
  max-height: 240px;
  overflow: auto;
  white-space: normal;
  .tag_pop {
    display: inline-block;
    padding: 1px 8px;
    line-height: 18px;
    color: #545861;
    font-size: 12px;
    background: #F2F4FB;
    box-sizing: border-box;
    text-align: center;
    border-radius: 4px;
    border: 0.5px solid #DEE1E8;
    background: #F2F4FB;
  }
}
.marketing-tag-popover.el-popper {
  padding: 12px;
  border-radius: 8px;
}
</style>
