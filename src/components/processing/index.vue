<template>
    <fx-dialog
      :visible.sync="visible"
      width="480px"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :showHeader="false"
      custom-class="processing-dialog"
    >
      <div class="processing-content">
        <template v-if="status === 'failed'">
          <div class="error-icon">
            <i class="fx-icon-jingshi" />
          </div>
          <div class="processing-text processing-error">
            <div class="title">{{ $t('marketing.components.processing.hbscyzdysc_849f04', { data: ({ option0: currentStep, option1: totalSteps }) }) }}</div>
            <div class="desc">
              {{ $t('marketing.components.processing.hbscgczydy_038776') }}
            </div>
          </div>
          <div class="action-buttons">
            <fx-button class="fx-btn" type="text" @click="handleContinue"
              >{{ $t('marketing.components.processing.jxsc_d46030') }}</fx-button
            >
            <fx-button class="fx-btn" type="text" @click="close">{{ $t('marketing.commons.qx_625fb2') }}</fx-button>
          </div>
        </template>

        <template v-else>
          <div class="title">{{ title || $t('marketing.commons.schb_0cdeac') }}</div>
          <fx-progress
            type="circle"
            :percentage="percentage"
            :stroke-width="4"
            :width="112"
          />
          <div class="processing-text">
            <div class="desc">{{ desc || $t('marketing.components.processing.zzschb_b24947') }}</div>
          </div>

          <fx-button
            v-if="!isCompleted"
            type="text"
            class="fx-btn"
            @click="handleStop"
          >{{    stopBtnText || $t('marketing.commons.zzsc_fa1bb2')}}</fx-button>
          <fx-button v-else type="text" class="fx-btn" @click="handleCompleted">
            {{ $t('marketing.commons.ywc_fad522') }}
          </fx-button>
        </template>
      </div>
    </fx-dialog>
</template>

<script>

export default {
  name: "Processing",
  props: {
    // 标题
    title: {
      type: String,
      default: ""
    },
    // 描述文字
    desc: {
      type: String,
      default: ""
    },
    // 终止按钮文字
    stopBtnText: {
      type: String,
      default: ""
    },
    // 当前步骤
    currentStep: {
      type: Number,
      default: 0
    },
    // 总步骤数
    totalSteps: {
      type: Number,
      default: 10
    },
    // 生成状态：processing/failed
    status: {
      type: String,
      default: "processing"
    },
    // 是否显示
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      percentage: 0,
      isCompleted: false,
      dialogVisible: false
    };
  },
  computed: {
    // 计算进度百分比
    progressPercentage() {
      return Math.floor((this.currentStep / this.totalSteps) * 100);
    }
  },
  watch: {
    currentStep: {
      handler(val) {
        this.percentage = this.progressPercentage;
        if (val === this.totalSteps) {
          this.isCompleted = true;
        }
      },
      immediate: true
    },
    visible: {
      handler(val) {
        this.dialogVisible = val;
      },
      immediate: true
    }
  },
  created() {
    // 监听页面刷新
    window.addEventListener("beforeunload", this.handleBeforeUnload);
  },
  beforeDestroy() {
    window.removeEventListener("beforeunload", this.handleBeforeUnload);
  },
  methods: {
    // 处理继续生成
    handleContinue() {
      this.$emit("continue");
    },
    // 处理终止操作
    async handleStop() {
      try {
        FxUI.MessageBox.confirm($t('marketing.components.processing.qryzzhbscm_17e528'), $t('marketing.commons.ts_02d981'), {
          confirmButtonText: $t('marketing.commons.qd_38cf16'),
          cancelButtonText: $t('marketing.commons.qx_625fb2'),
          type: "warning"
        }).then(async (res) => {
          this.handleCompleted();
        }).catch(() => {
        });
      } catch (err) {
      }
    },
    // 处理页面刷新
    handleBeforeUnload(e) {
      if (!this.isCompleted) {
        e.preventDefault();
        e.returnValue = $t('marketing.components.processing.czwwcqrylk_2b15b2');
      }
    },
    handleCompleted() {
      this.close();
      this.$emit("completed");
    },
    // 关闭弹窗
    close() {
      this.$emit("update:visible", false);
    }
  }
};
</script>

<style lang="less" scoped>
.processing-dialog {
  .processing-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    .title {
      color: var(--color-neutrals19);
      text-align: center;
      font-size: 16px;
      font-style: normal;
      font-weight: 700;
      line-height: 24px; /* 150% */
      margin-bottom: 16px;
    }
  }

  .error-icon {
    .fx-icon-jingshi {
      font-size: 57px;
      &:before {
        color: #f56c6c;
      }
    }
  }

  .processing-text {
    .desc {
      color: var(--color-neutrals15);
      text-align: center;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px; /* 142.857% */
      margin-top: 20px;
    }
    &.processing-error {
      .title {
        margin-top: 20px;
      }
      .desc {
        margin-top: 10px;
      }
    }
  }

  .action-buttons {
    margin-top: 20px;
    display: flex;
    gap: 16px;
  }
  .fx-btn {
    color: var(--color-info06);
    font-size: 14px;
    padding: 0;
    margin-top: 16px;
  }
}
</style>
