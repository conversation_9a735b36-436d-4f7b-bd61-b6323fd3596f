<template>
  <div class="process-flow">
    <div class="process-flow-header">
      <div class="process-flow-title">
        {{ title }}
      </div>
      <div class="process-flow-right">
        <fx-link :underline="false" type="standard" class="fold-btn-link" href="https://help.fxiaoke.com/93d5/9188/78fd/7326" target="_blank">
          {{ $t('marketing.components.ProcessFlow.rhwzhyyx_f11664') }}
        </fx-link>
      <div class="fold-btn" @click="toggleFold">
        <i
            :class="['iconfont', isCollapsed ? 'icondown1' : 'iconup']"
          ></i>
      </div>
      </div>
    </div>
    <!-- 进度条容器 -->
    <div class="progress-bar">
      <div
        v-for="(step, index) in processedSteps"
        :key="index"
        class="step-item"
        :class="{
          selected: realCurrentStep === index,
          'first-step': index === 0,
          'last-step': index === processedSteps.length - 1
        }"
        @click="handleStepClick(index)"
      >
        <div class="step-text">
          <i v-if="step.icon" :class="['iconfont', step.icon]"></i>
          {{ step.title }}
        </div>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-wrapper" v-show="!isCollapsed">
      <div
        v-for="(step, stepIndex) in steps"
        :key="stepIndex"
        v-show="realCurrentStep === stepIndex"
        class="step-content-group"
      >
        <div
          v-for="(category, categoryIndex) in step.items"
          :key="categoryIndex"
          class="category-box"
        >
          <div class="category-title">{{ category.title }}</div>
          <div
            v-for="(item, itemIndex) in category.subItems"
            :key="itemIndex"
            class="content-item"
          >
            <div 
              class="item-title"
              :class="{ 'disabled': item.action && !item.enabled }"
              style="cursor: pointer;"
            >
              <span class="dot"></span>
              <fx-link :underline="false" type="standard" class="action-link" @click.prevent="item.action && item.enabled && handleAction(item)">
                {{ item.title }}
              </fx-link>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "ProcessFlow",
  props: {
    steps: {
      type: Array,
      required: true
    },
    currentStep: {
      type: Number,
      default: 0
    },
    title: {
      type: String,
      default: ''
    },
    // 会议状态
    currentStatus: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isCollapsed: false,
      realCurrentStep: 0
    };
  },
  computed: {
    processedSteps() {
      return this.steps.map((step, idx) => {
        const newStep = { ...step };
        newStep.icon = 'icononprogress';
        // 当前状态为"开始前"
        if (this.currentStatus === 'before') {
          this.realCurrentStep = 0;
          if (idx === 0) {
            newStep.icon = 'icononprogress';
          }
        } else if (this.currentStatus === 'processing') {
          this.realCurrentStep = 1;
          if (idx === 0) {
            newStep.icon = 'icondone';
          } else if (idx === 1) {
            newStep.icon = 'icononprogress';
          }
        } else if (this.currentStatus === 'after') {
          this.realCurrentStep = 2;
          if (idx === 0 || idx === 1) {
            newStep.icon = 'icondone';
          } else if (idx === 2) {
            newStep.icon = 'icononprogress';
          }
        }
        return newStep;
      });
    }
  },
  methods: {
    handleAction(item) {
      if (!item.enabled) return;
      this.$emit("action-click", item);
    },
    handleStepClick(index) {
      // 如果当前是折叠状态，则自动展开
      if (this.isCollapsed) {
        this.isCollapsed = false;
        // 保存折叠状态到本地存储
        localStorage.setItem('MarketingMeetingProcessFlowCollapsed', false);
      }
      this.realCurrentStep = index;
    },
    toggleFold() {
      this.isCollapsed = !this.isCollapsed;
      // 保存折叠状态到本地存储，时效30天
      const expires = Date.now() + 30 * 24 * 60 * 60 * 1000;
      localStorage.setItem('MarketingMeetingProcessFlowCollapsed', this.isCollapsed);
      localStorage.setItem('MarketingMeetingProcessFlowCollapsedExpires', expires);
    }
  },
  mounted() {
    // 检查本地存储的折叠状态是否过期
    const collapsedExpires = localStorage.getItem('MarketingMeetingProcessFlowCollapsedExpires');
    if (collapsedExpires && Date.now() < parseInt(collapsedExpires)) {
      this.isCollapsed = localStorage.getItem('MarketingMeetingProcessFlowCollapsed') === 'true';
    } else {
      this.isCollapsed = false;
      // 清除过期的存储
      localStorage.removeItem('MarketingMeetingProcessFlowCollapsed');
      localStorage.removeItem('MarketingMeetingProcessFlowCollapsedExpires');
    }
  }
};
</script>

<style lang="less" scoped>
.process-flow {
  background: #fff;
  padding: 16px 12px;
  border-radius: 8px;
  .process-flow-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;

      .process-flow-title {
        font-size: 14px;
        font-weight: 700;
        color: var(--color-neutrals19)
      }

      .process-flow-right {
        display: flex;
        align-items: center;
        gap: 10px;
        .fold-btn-link {
          font-size: 12px;
        }
      }

      .fold-btn {
        cursor: pointer;
        .iconfont {
          font-size: 16px;
        }
      }
    }
  .progress-bar {
    display: flex;
    position: relative;
    gap: 10px;
    .step-item {
      flex: 1;
      position: relative;
      text-align: center;
      z-index: 2;
      cursor: pointer;
      .step-text {
        display: inline-block;
        padding: 7px;
        background: #D7F6E5;
        color: #545861;
        font-size: 12px;
        line-height: 22px;
        position: relative;
        transition: all 0.3s;
        width: 100%;
        box-sizing: border-box;
        .iconfont {
          font-size: 12px;
          margin-right: 5px;
          color: #30C776;
        }
        
        &::after {
          content: "";
          position: absolute;
          right: -11px;
          top: 0;
          width: 12px;
          height: 37px;
          clip-path: polygon(93% 50%, 0 0, 0 99%);
          background: inherit;
          z-index: 5;
        }
        &::before {
          content: "";
          position: absolute;
          left: -1px;
          top: 0;
          width: 12px;
          height: 37px;
          background: #fff;
          clip-path: polygon(93% 50%, 0 0, 0 99%);
        }
      }
      &:first-child {
        .step-text {
          &::before {
            display: none;
          }
        }
      }
      &:last-child {
        .step-text {
          &::after {
            display: none;
          }
        }
      }

      &.selected {
        .step-text {
          background: #30c776;
          color: #fff;
          &::after {
            border-color: #30c776;
          }
          &::before {
            border-color: #30c776;
          }
          .iconfont{
            color: #fff;
          }
        }
      }
      
      &.first-step {
        .step-text {
          border-radius: 8px 0 0 8px;
        }
      }
      &.last-step {
        .step-text {
          border-radius: 0 8px 8px 0;
        }
      }
    }
  }

  .content-wrapper {
    margin-top: 10px;
    .step-content-group {
      display: flex;
      gap: 12px;
    }

    .category-box {
      background: #f7f8fa;
      border: 1px solid #dee1e8;
      border-radius: 8px;
      padding: 16px;
      flex: 1;
      min-width: 150px;
      .category-title {
        color: #181c25;
        font-size: 14px;
        font-weight: 700;
        line-height: 20px;
        margin-bottom: 14px;
      }

      .content-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 14px;
        &:last-child {
          margin-bottom: 0;
        }

        .item-title {
          color: var(--color-neutrals15);
          font-size: 12px;
          display: flex;
          align-items: center;
          .dot {
            width: 6px;
            height: 6px;
            background: #d9d9d9;
            border-radius: 50%;
            margin-right: 6px;
          }
          .action-link{
            font-size: 12px;
          }
        }
      }
    }
  }
}
</style>
