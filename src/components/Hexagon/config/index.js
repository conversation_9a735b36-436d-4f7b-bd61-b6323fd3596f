import { components, button, genContainerConfig, genStepButtonConfig } from "./components";
import {createId} from '../utils/index'

/**
 * 字段关联关系map
 * 组件之间字段属性可进行关联，关联后会自动同步字段值，例如：字段A关联字段B则字段A值变更则会同步修改字段B
 * 仅对相同布局组件内的组件有效
 * demo:
 * product: {
 *  comp: 'paybutton',//关联组件名
 *  fields: {//需要同步的字段map
 *   product: "product"//同步product组件字段product -> paybutton组件的字段product
 *  }
 * }
 */
const signupButtonItemStyle = {
  color: '#fff',
  fontSize: 16,
  lineHeight: 45,
  textAlign: 'center',
  letterSpacing: 0,
}

const signupButtonConfig = {
  ...button,
  style: {
    ...button.style,
    borderRadius: 6,
  },
  type: 'signupbutton',
  typeValue: 'meeting',
  label: $t('marketing_pd.config.hybman_a14bdc'),
  name: $t('marketing_pd.config.hybm_bb1d71'),
  required: false,
  isFormComp: false,
  memberAutoSignup: false,
  memberAutoSignupButtonStyle: {
    textAlign: 'center',
    color: '#181c25',
    fontSize: 16,
    lineHeight: 45,
  },
  memberAutoSignupButton: true,
  memberAutoSignupButtonText: $t('marketing_pd.commons.hydl_83e581'),
  member: {},
  objectId: '',
  objectTitle: '',
  objectName: '', // 物料名称
  status: 'before', // 进度状态；before：开始前 processing：进行中 after：结束后
  schedule: {}, // 进度配置内容
}

const defaultFormComponents = [
  {
    "id": "95c7c286d1762ff0",
    "label": $t('marketing_pd.commons.xm_60d045'),
    "name": $t('marketing_pd.commons.xm_60d045'),
    "title": "",
    "type": "input",
    "typeValue": "text",
    "fieldName": "name",
    "customFieldName": "name",
    "defaultValueOpen": false,
    "defaultValue": "",
    "globalCacheField": "",
    "defaultValueType": "manual",
    "required": true,
    "placeholder": $t('marketing_pd.commons.qsrxm_8093e3'),
    "isFormComp": true,
    "style": {
      "color": "#181C25",
      "width": 345,
      "fontSize": 14,
      "paddingBottom": 0,
      "paddingTop": 0,
      "paddingLeft": 12,
      "paddingRight": 12,
      "borderStyle": "solid",
      "borderWidth": 1,
      "borderRadius": 3,
      "borderColor": "#e9edf5",
      "marginLeft": 15,
      "marginRight": 15,
      "marginTop": 15,
      "position": "relative"
    },
    "titleStyle": {
      "color": "#181C25",
      "fontSize": 14,
      "lineHeight": 16,
      "paddingBottom": 6,
      "paddingTop": 6,
      "whiteSpace": "normal"
    },
    "placeholderStyle": {
      "color": "#cbcccf"
    },
    "inputStyle": {
      "height": 45,
      "color": "#181c25",
      "background": "#fff"
    },
    "sort": 0
  },
  {
    "id": "ac4d167b4ceb824d",
    "label": $t('marketing_pd.commons.sjh_8098e2'),
    "name": $t('marketing_pd.commons.sjh_8098e2'),
    "title": "",
    "type": "input",
    "typeValue": "number",
    "fieldName": "phone",
    "customFieldName": "phone",
    "pattern": "^1[0-9]\\d{9}$",
    "defaultValue": "",
    "defaultValueOpen": false,
    "globalCacheField": "",
    "defaultValueType": "manual",
    "required": true,
    "verify": false,
    "enableInternationalCode": false,
    "weChatAuthorizationButton": false,
    "placeholder": $t('marketing_pd.commons.qsrsjh_6e4f4b'),
    "isFormComp": true,
    "weChatAuthorizationButtonStyle": {
      "color": "#fff",
      "background": "#09BB07",
      "fontSize": 14,
      "borderStyle": "solid",
      "borderWidth": 0,
      "borderRadius": 3,
      "borderColor": "#e9edf5"
    },
    "verifyButtonStyle": {
      "color": "#181C25",
      "background": "#ffffff",
      "fontSize": 14,
      "borderStyle": "solid",
      "borderWidth": 1,
      "borderRadius": 3,
      "borderColor": "#e9edf5"
    },
    "titleStyle": {
      "color": "#181C25",
      "fontSize": 14,
      "lineHeight": 16,
      "paddingBottom": 6,
      "paddingTop": 6,
      "whiteSpace": "normal"
    },
    "style": {
      "color": "#181C25",
      "width": 345,
      "fontSize": 14,
      "paddingBottom": 0,
      "paddingTop": 0,
      "paddingLeft": 12,
      "paddingRight": 12,
      "borderStyle": "solid",
      "borderWidth": 1,
      "borderRadius": 3,
      "borderColor": "#e9edf5",
      "marginLeft": 15,
      "marginRight": 15,
      "marginTop": 15,
      "position": "relative"
    },
    "placeholderStyle": {
      "color": "#cbcccf"
    },
    "inputStyle": {
      "height": 45,
      "color": "#181c25",
      "background": "#fff"
    },
    "sort": 1
  },
  {
    "id": "840335cac27313d1",
    "label": $t('marketing_pd.commons.tj_939d53'),
    "name": $t('marketing_pd.commons.tj_939d53'),
    "tip": $t('marketing_pd.commons.tjcg_23b62e'),
    "type": "button",
    "position": "none",
    "required": false,
    "isFormComp": true,
    "noDeletion": true,
    "wrapStyle": {
      "position": "none",
    },
    "style": {
      "height": 45,
      "width": 345,
      "fontSize": 16,
      "background": "#409EFF",
      "borderRadius": 0,
      "color": "#fff",
      "letterSpacing": 0,
      "lineHeight": 45,
      "textAlign": "center",
      "margin": "0 auto",
      "boxShadow": "0px 0px 0px rgba(0,0,0,.1)",
      "boxShadowLeft": 0,
      "boxShadowTop": 0,
      "boxShadowRadius": 0,
      "boxShadowColor": "rgba(0,0,0,.1)",
      "borderWidth": 0,
      "borderStyle": "none",
      "borderColor": "#e9edf5",
      "marginLeft": 15,
      "marginRight": 15,
      "marginBottom": 15,
      "marginTop": 15,
    },
    "sort": 2
  }
]
const RELATED_COMP_FIELDS = {
  product: {
    comp: "paybutton",
    fields: {
      product: "product"
    }
  },
  paybutton: {
    comp: "product",
    fields: {
      product: "product"
    }
  }
};
/**
 * 多页单页设计模式下布局组件自动生成子组件配置
 */
const AUTO_RENDER_COMP = {
  // form: {
  //   button: {
  //     style: {
  //       position: "absolute",
  //       left: 15,
  //       top: 175
  //     }
  //   }
  // },
  form: {
    "id": "a5a439a9e66911a2",
    "name": $t('marketing_pd.commons.bd_eee1e2'),
    "key": "form-container",
    "type": "container",
    "typeValue": "form",
    "components": defaultFormComponents,
    "current": 0,
    "slideIndex": 0,
    "layout": "single",
    "fillType": "color",
    "fillMethod": "filling",
    "typesetting": "flow",
    "visualStyle": {
      "overflowX": "hidden",
      "overflowY": "auto",
      "height": 30
    },
    "style": {
      "width": 375,
      "overflow": "hidden",
      "position": "relative",
      "backgroundColor": "",
      "backgroundImage": ""
    },
    "sort": 0
  },
  order: {
    product: {
      style: {
        position: "absolute",
        left: 0,
        top: 10
      }
    },
    paybutton: {
      style: {
        position: "absolute",
        left: 15,
        top: 175
      }
    }
  },
  "step-form": {
    id: "1591346581375",
    layout: "multiple",
    components: [
      {
        id: "1591346572665",
        name: $t('marketing_pd.commons.fbbd_e7d713'),
        key: "form-container",
        type: "container",
        typeValue: "step-form",
        fillType: "color",
        fillMethod: "filling",
        components: genDefaultFrom('step-form'),
        current: 0,
        slideIndex: 0,
        layout: "single",
        typesetting: "flow",
        style: {
          width: 375,
          height: 'auto',
          overflow: "hidden",
          position: "relative"
        }
      },
      {
        id: "1591346572666",
        name: $t('marketing_pd.commons.fbbd_e7d713'),
        key: "form-container",
        type: "container",
        typeValue: "step-form",
        fillType: "color",
        fillMethod: "filling",
        typesetting: "flow",
        components: [
          genStepButtonConfig({
            name: $t('marketing_pd.commons.tj_939d53'),
            tip: $t('marketing_pd.commons.tjcg_23b62e'),
          }),
        ],
        current: 0,
        slideIndex: 0,
        layout: "single",
        style: {
          width: 375,
          height: 'auto',
          overflow: "hidden",
          position: "relative"
        }
      }
    ]
  },
  personal: {
    id: "1609839587705",
    name: $t('marketing_pd.commons.grxx_eab129'),
    type: "container",
    components: [
      {
        id: "1609827982595",
        name: $t('marketing_pd.commons.zdybj_fe6488'),
        key: "auto-container",
        type: "container",
        typeValue: "auto",
        components: [
          {
            name: $t('marketing_pd.commons.tx_4c50ee'),
            type: "image",
            fieldName: "memberAvatar",
            images: [
              {
                url: "",
                action: {}
              }
            ],
            imageGap: 4,
            style: {
              display: "flex",
              width: 60,
              height: 60,
              paddingBottom: 0,
              paddingLeft: 0,
              paddingRight: 0,
              paddingTop: 0,
              borderRadius: 60,
              background: "rgba(255, 255, 255, 0)",
              backgroundRepeat: "no-repeat",
              backgroundSize: "cover",
              backgroundPosition: "center center",
              borderWidth: 2,
              borderStyle: "solid",
              borderColor: "rgba(233, 237, 245, 1)",
              left: 20,
              top: 20,
              position: "absolute"
            },
            sort: 5,
            id: 1610160900360
          },
          {
            id: 1610162666138,
            name: $t('marketing_pd.commons.nc_23eb0e'),
            type: "text",
            value: $t('marketing_pd.commons.nc_23eb0e'),
            fieldName: "memberName",
            style: {
              paddingBottom: 6,
              paddingLeft: 0,
              paddingRight: 0,
              paddingTop: 6,
              background: "rgba(255, 255, 255, 0)",
              fontSize: 16,
              lineHeight: 16,
              borderWidth: 0,
              borderRadius: 0,
              borderStyle: "none",
              borderColor: "#e9edf5",
              color: "#fff",
              left: 90,
              top: 35,
              position: "absolute",
              width: 274
            },
            sort: 6
          }
        ],
        current: 0,
        slideIndex: 0,
        layout: "single",
        fillType: "image",
        fillMethod: "filling",
        style: {
          width: 375,
          height: 100,
          overflow: "hidden",
          position: "relative",
          backgroundColor: "",
          backgroundImage:
            "url(https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202101_20_c579ae6683a54bc9ae13211a5dad20ba.jpg)",
          backgroundSize: "cover",
          backgroundPosition: "center center",
          backgroundRepeat: "no-repeat"
        }
      }
    ],
    layout: "multiple",
    fillType: "color",
    fillMethod: "filling",
    style: {
      width: 375,
      overflow: "hidden"
    }
  },
  "tab-container": {
    id: "1591346581366",
    layout: "multiple",
    tabColor: "#181C25",
    tabActiveColor: "#ff8000",
    tabBackgroundColor: "#fff",
    tabs: [
      {
        label: $t('marketing_pd.commons.bq_14d342') + '1',
        layout: "list",
        value: 0
      },
      {
        label: $t('marketing_pd.commons.bq_14d342') + '2',
        layout: "list",
        value: 1
      }
    ],
    components: [
      {
        id: "1591346572999",
        name: $t('marketing_pd.commons.zdybj_fe6488'),
        key: "auto-container",
        type: "container",
        typeValue: "auto",
        fillType: "color",
        fillMethod: "filling",
        components: [],
        current: 0,
        slideIndex: 0,
        layout: "single",
        typesetting: "flow",
        style: {
          width: 375,
          overflow: "hidden",
          position: "relative"
        }
      },
      {
        id: "1591346572998",
        name: $t('marketing_pd.commons.zdybj_fe6488'),
        key: "auto-container",
        type: "container",
        typeValue: "auto",
        fillType: "color",
        fillMethod: "filling",
        components: [],
        current: 0,
        slideIndex: 0,
        layout: "single",
        typesetting: "flow",
        style: {
          width: 375,
          overflow: "hidden",
          position: "relative"
        }
      }
    ]
  },
  "activity-comp": {
    id: "1741161109562",
    name: $t('marketing.commons.hdxq_4553b4'),
    comp: 'activity-comp',
    type: "container",
    typeValue: 'activity-comp',
    marketingEventId: '!!marketingEventId!!',
    marketingEventTitle: '!!marketingEventTitle!!',
    maxLimit: 1,
    typesetting: "flow",
    components: [
      {
        id: 1741161441222,
        name: $t('marketing.commons.hdfm_350355'),
        type: 'image',
        fieldName: 'marketingEventCover',
        typeValue: 'marketingEventCover',
        previewEnable: false,
        images: [
          {
            url: "",
            action: {},
            uploadType: 'upload',
          }
        ],
        style: {
          paddingBottom: 0,
          paddingLeft: 0,
          paddingRight: 0,
          paddingTop: 0,
          borderWidth: 0,
          borderRadius: 0,
          borderStyle: "none",
          borderColor: "#e9edf5",
          background: 'rgba(255, 255, 255, 0)',
          backgroundRepeat: 'no-repeat',
          backgroundSize: 'cover',
          backgroundPosition: 'center center',
          position: "relative",
          width: 375,
          height: 208,
          display: 'flex',
        },
        filterConfig: {
          brightness: 100,
          grayscale: 0,
          opacity: 100,
        },
      },
      {
        id: 1741161441224,
        name: $t('marketing.commons.hdbt_902a3d'),
        type: 'text',
        value: '{'+$t('marketing.commons.hdbt_902a3d')+'}',
        typeValue: 'marketingEventTitle',
        fieldName: 'marketingEventTitle',
        style: {
          paddingBottom: 0,
          paddingLeft: 12,
          paddingRight: 12,
          paddingTop: 0,
          marginTop: 12,
          background: "#fff",
          fontSize: 18,
          lineHeight: 28,
          borderWidth: 0,
          borderRadius: 0,
          borderStyle: "none",
          color: "#181C25",
          fontWeight: 700,
          position: "relative",
          width: 375
        },
      },
      {
        id: 1741161441,
        name: $t('marketing.commons.hdsj_c799f5'),
        type: 'text',
        value: '{'+$t('marketing.commons.hdsj_c799f5')+'}',
        fieldName: 'marketingEventTime',
        typeValue: 'marketingEventTime',
        style: {
          paddingBottom: 0,
          paddingLeft: 12,
          paddingRight: 12,
          paddingTop: 0,
          marginTop: 6,
          background: "#fff",
          fontSize: 13,
          lineHeight: 18,
          borderWidth: 0,
          borderRadius: 0,
          borderStyle: "none",
          color: "#545861",
          position: "relative",
          width: 375
        },
      },
      {
        id: 1741161441226,
        name: $t('marketing.commons.hddd_9ce624'),
        type: 'text',
        value: '{'+$t('marketing.commons.hddd_9ce624')+'}',
        fieldName: 'marketingEventAddress',
        typeValue: 'marketingEventAddress',
        style: {
          paddingBottom: 0,
          paddingLeft: 12,
          paddingRight: 12,
          paddingTop: 0,
          marginTop: 6,
          background: "#fff",
          fontSize: 13,
          lineHeight: 18,
          borderWidth: 0,
          borderRadius: 0,
          borderStyle: "none",
          color: "#545861",
          position: "relative",
          width: 375
        },
      },
      {
        id: 1741161441225,
        name: $t('marketing.components.Hexagon.fzlb_e8cb18'),
        type: 'blank',
        style: {
          paddingBottom: 4,
          paddingLeft: 0,
          paddingRight: 0,
          paddingTop: 4,
          marginTop: 12,
          background: "#F7F8FA",
          lineHeight: 16,
          borderWidth: 0,
          borderRadius: 0,
          borderStyle: "none",
          color: "#333333",
          position: "relative",
          width: 375
        },
      },
      {
        id: 1741161441226,
        name: $t('marketing.commons.xq_f26225'),
        type: 'text',
        value: $t('marketing.commons.xq_f26225'),
        noDeletion: false,
        style: {
          paddingBottom: 0,
          paddingLeft: 12,
          paddingRight: 12,
          paddingTop: 0,
          marginTop: 14,
          background: "#fff",
          fontSize: 12,
          lineHeight: 18,
          borderWidth: 0,
          borderRadius: 0,
          borderStyle: "none",
          borderColor: "#e9edf5",
          color: "#545861",
          position: "relative",
          width: 375,
        },
      },
      {
        id: 1741161441226,
        name: $t('marketing.commons.hdxq_4553b4'),
        type: 'text',
        value: '{'+$t('marketing.commons.hdxq_4553b4')+'}',
        fieldName: 'marketingEventDetail',
        typeValue: 'marketingEventDetail',
        noDeletion: false,
        noEdit: true,
        style: {
          paddingBottom: 0,
          paddingLeft: 12,
          paddingRight: 12,
          paddingTop: 0,
          marginTop: 12,
          background: "#fff",
          fontSize: 16,
          lineHeight: 18,
          borderWidth: 0,
          borderRadius: 0,
          borderStyle: "none",
          borderColor: "#e9edf5",
          color: "#545861",
          position: "relative",
          width: 375,
        },
      },
      {
        id: 1741161441227,
        label: $t('marketing_pd.config.hdbm_2bc045'),
        name: $t('marketing_pd.config.hdbm_2bc045'),
        type: "signupbutton",
        typeValue: "marketingEventSignupbutton",
        position: "fixed-bottom",
        required: false,
        isFormComp: false,
        noDeletion: false,
        memberAutoSignup: false,
        memberAutoSignupButtonStyle: {
          textAlign: 'center',
          color: '#181c25',
          fontSize: 16,
          lineHeight: 45,
        },
        memberAutoSignupButton: true,
        memberAutoSignupButtonText: $t('marketing_pd.commons.hydl_83e581'),
        member: {},
        status: 'before',
        objectName: $t('marketing_pd.commons.zb_7bbe8e'),
        objectType: '!!event_form!!',
        objectId: '!!marketingEventId!!',
        schedule: {
          before: {
            yes: {
              name: $t('marketing.commons.yyy_44209e'),
              style: signupButtonItemStyle,
              action: {},
            },
            no: {
              name: $t('marketing.commons.ljyy_3ed720'),
              style: signupButtonItemStyle,
              action: {},
            },
          },
          processing: {
            yes: {
              name: $t('marketing.commons.gkzb_4dd8d5'),
              style: signupButtonItemStyle,
              action: {
                type: 'gotoLiveAddress',
              },
            },
            no: {
              name: $t('marketing.commons.bmbgk_759bbd'),
              style: signupButtonItemStyle,
              action: {},
            },
          },
          after: {
              yes: {
                name: $t('marketing.commons.gkhf_58dee7'),
                style: signupButtonItemStyle,
                action: {
                  type: 'gotoLiveAddress',
                },
              },
              no: {
                name: $t('marketing.commons.gkhf_58dee7'),
                style: signupButtonItemStyle,
                action: {
                  type: 'inside',
                },
              },
          },
        },
        wrapStyle: {
          position: 'fixed',
          left: 0,
          bottom: 0,
          right: 0,
          zIndex: 1,
          background: '#ffffff',
          width: 375,
        },
        style: {
          height: 45,
          width: 345,
          fontSize: 16,
          background: "#409EFF",
          borderRadius: 0,
          color: "#fff",
          letterSpacing: 0,
          lineHeight: 45,
          borderRadius: 6,
          textAlign: "center",
          marginLeft: 15,
          marginRight: 15,
          marginBottom: 15,
          marginTop: 15,
          boxShadow: "0px 0px 0px rgba(0,0,0,.1)",
          boxShadowLeft: 0,
          boxShadowTop: 0,
          boxShadowRadius: 0,
          boxShadowColor: "rgba(0,0,0,.1)",
          borderWidth: 0,
          borderStyle: "none",
          borderColor: "#e9edf5",
        },
        sort: 2,
      }
    ],
    layout: "single",
    fillType: "color",
    fillMethod: "filling",
    style: {
      width: 375,
      overflow: "hidden",
      position: "relative",
      background: "#fff"
    }
  },
};

//自定义布局模式下
const AUTO_RENDER_COMP_CUSTOM = {
  form: {
    button: {
      style: {
        position: "absolute",
        left: 15,
        top: 175,
        marginTop: 0,
        marginLeft: 0,
        marginBottom: 0,
        marginRight: 0,
      }
    }
  },
  order: {
    product: {
      style: {
        position: "absolute",
        left: 0,
        top: 10
      }
    },
    paybutton: {
      style: {
        position: "absolute",
        left: 15,
        top: 175
      }
    }
  },
  "step-form": {
    id: "1591346581375",
    layout: "multiple",
    components: [
      {
        id: "1591346572665",
        name: $t('marketing_pd.commons.fbbd_e7d713'),
        key: "form-container",
        type: "container",
        typeValue: "step-form",
        fillType: "color",
        fillMethod: "filling",
        typesetting: "flow",
        components: genDefaultFrom('step-form'),
        current: 0,
        slideIndex: 0,
        layout: "single",
        style: {
          width: 375,
          height: 'auto',
          overflow: "hidden",
          position: "relative"
        }
      },
      {
        id: "1591346572666",
        name: $t('marketing_pd.commons.fbbd_e7d713'),
        key: "form-container",
        type: "container",
        typeValue: "step-form",
        fillType: "color",
        fillMethod: "filling",
        typesetting: "flow",
        components: [
          genStepButtonConfig({
            name: $t('marketing_pd.commons.tj_939d53'),
            tip: $t('marketing_pd.commons.tjcg_23b62e'),
          })
        ],
        current: 0,
        slideIndex: 0,
        layout: "single",
        style: {
          width: 375,
          height: 'auto',
          overflow: "hidden",
          position: "relative"
        }
      }
    ]
  },
  personal: {
    id: "1609839587705",
    name: $t('marketing_pd.commons.grxx_eab129'),
    type: "container",
    components: [
      {
        id: "1609827982595",
        name: $t('marketing_pd.commons.zdybj_fe6488'),
        key: "auto-container",
        type: "container",
        typeValue: "auto",
        components: [
          {
            name: $t('marketing_pd.commons.tx_4c50ee'),
            type: "image",
            fieldName: "memberAvatar",
            images: [
              {
                url: "",
                action: {}
              }
            ],
            imageGap: 4,
            style: {
              display: "flex",
              width: 60,
              height: 60,
              paddingBottom: 0,
              paddingLeft: 0,
              paddingRight: 0,
              paddingTop: 0,
              borderRadius: 60,
              background: "rgba(255, 255, 255, 0)",
              backgroundRepeat: "no-repeat",
              backgroundSize: "cover",
              backgroundPosition: "center center",
              borderWidth: 2,
              borderStyle: "solid",
              borderColor: "rgba(233, 237, 245, 1)",
              left: 20,
              top: 20,
              position: "absolute"
            },
            sort: 5,
            id: 1610160900360
          },
          {
            id: 1610162666138,
            name: $t('marketing_pd.commons.nc_23eb0e'),
            type: "text",
            value: $t('marketing_pd.commons.nc_23eb0e'),
            fieldName: "memberName",
            style: {
              paddingBottom: 6,
              paddingLeft: 0,
              paddingRight: 0,
              paddingTop: 6,
              background: "rgba(255, 255, 255, 0)",
              fontSize: 16,
              lineHeight: 16,
              borderWidth: 0,
              borderRadius: 0,
              borderStyle: "none",
              borderColor: "#e9edf5",
              color: "#fff",
              left: 90,
              top: 35,
              position: "absolute",
              width: 274
            },
            sort: 6
          }
        ],
        current: 0,
        slideIndex: 0,
        layout: "single",
        fillType: "image",
        fillMethod: "filling",
        style: {
          width: 375,
          height: 100,
          overflow: "hidden",
          position: "relative",
          backgroundColor: "",
          backgroundImage:
            "url(https://www.fxiaoke.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202101_20_c579ae6683a54bc9ae13211a5dad20ba.jpg)",
          backgroundSize: "cover",
          backgroundPosition: "center center",
          backgroundRepeat: "no-repeat"
        }
      }
    ],
    layout: "multiple",
    fillType: "color",
    fillMethod: "filling",
    style: {
      width: 375,
      overflow: "hidden"
    }
  },
  "tab-container": {
    id: "1591346581366",
    layout: "multiple",
    tabColor: "#181C25",
    tabActiveColor: "#ff8000",
    tabBackgroundColor: "#fff",
    tabs: [
      {
        label: $t('marketing_pd.commons.bq_14d342') + '1',
        layout: "list",
        value: 0
      },
      {
        label: $t('marketing_pd.commons.bq_14d342') + '2',
        layout: "list",
        value: 1
      }
    ],
    components: [
      {
        id: "1591346572999",
        name: $t('marketing_pd.commons.zdybj_fe6488'),
        key: "auto-container",
        type: "container",
        typeValue: "auto",
        fillType: "color",
        fillMethod: "filling",
        components: [],
        current: 0,
        slideIndex: 0,
        layout: "single",
        typesetting: "flow",
        style: {
          width: 375,
          overflow: "hidden",
          position: "relative"
        }
      },
      {
        id: "1591346572998",
        name: $t('marketing_pd.commons.zdybj_fe6488'),
        key: "auto-container",
        type: "container",
        typeValue: "auto",
        fillType: "color",
        fillMethod: "filling",
        components: [],
        current: 0,
        slideIndex: 0,
        layout: "single",
        typesetting: "flow",
        style: {
          width: 375,
          overflow: "hidden",
          position: "relative"
        }
      }
    ]
  },
};

/**
 * 表单设计模式下布局组件自动生成子组件配置
 */
const FORM_PAGE_AUTO_RENDER_COMP = {
  form: {
    id: "8ad37d4d4df988a0",
    components: defaultFormComponents
  }
};

/**
 * 表单设计定位布局模式下布局组件自动生成子组件配置
 */
const FORM_PAGE_AUTO_RENDER_COMP_CUSTOM = {
  form: {
    button: {
      style: {
        position: "absolute",
        left: 15,
        top: 500
      }
    }
  }
};

/**
 *
 * @param {String} compType 组件类型
 * @param {String} designMode 设计模式
 * @returns
 */
function getAutoRenderCompChildren(compType, designMode = "multi-page", typesetting = "flow") {
  if (designMode === "form-page") {
    if(typesetting === "flow"){
      return FORM_PAGE_AUTO_RENDER_COMP[compType];
    }else{
      return FORM_PAGE_AUTO_RENDER_COMP_CUSTOM[compType];
    }
  } else {
    if(typesetting === "flow"){
      return AUTO_RENDER_COMP[compType];
    } else {
      return AUTO_RENDER_COMP_CUSTOM[compType];
    }
  }
}

function genDefaultFrom(typesetting = "flow") {
  let target = JSON.parse(JSON.stringify(defaultFormComponents));
  target.map((item) => {
    item.id = createId();
  });
  if(typesetting === "absolute") {
    target.map((item) => {
      if(item.fieldName === 'name') {
        item.style = {
          ...item.style,
          position: 'absolute',
          top: 16, // 42
          left: 15,
          paddingBottom: 0,
          paddingTop: 0,
          paddingLeft: 12,
          paddingRight: 12,
          marginLeft: 0,
          marginRight: 0,
          marginTop: 0,
        }
      } else if (item.fieldName === 'phone') {
        item.style = {
          ...item.style,
          position: 'absolute',
          top: 76,
          left: 15,
          width: 345,
          paddingBottom: 0,
          paddingTop: 0,
          paddingLeft: 12,
          paddingRight: 12,
          marginLeft: 0,
          marginRight: 0,
          marginTop: 0,
        }
      } else if (item.type === 'button') {
        item.style = {
          ...item.style,
          position: 'absolute',
          top: 136,
          left: 15,
          width: 345,
          paddingBottom: 0,
          paddingTop: 0,
          paddingLeft: 'auto',
          paddingRight: 'auto',
          marginLeft: 0,
          marginRight: 0,
          marginTop: 0,
        }
      }
    });
  }else if(typesetting === "step-form") {
    target[2] = genStepButtonConfig({
      hasBackBtn: false
    })
  }
  return target;
}

export {
  components,
  button,
  genContainerConfig,
  genStepButtonConfig,
  RELATED_COMP_FIELDS,
  AUTO_RENDER_COMP,
  FORM_PAGE_AUTO_RENDER_COMP,
  getAutoRenderCompChildren,
  genDefaultFrom,
};
