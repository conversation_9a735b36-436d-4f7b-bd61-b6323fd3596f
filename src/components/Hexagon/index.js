import Hexagon from './package/MultiPageDesigner.vue'
import SinglePageDesigner from './package/SinglePageDesigner.vue'
import FormDesigner from './package/FormDesigner.vue'
import PageRender from './package/PageRender.vue'
import MenuIcon from './components/common/MenuIcon.vue'
import store from './store/index.js'
import { predefineColors } from './utils/const.js'
import ColorPicker from './components/comp/components/ColorPicker.vue'

if (!Element.prototype.matches) {
  Element.prototype.matches = Element.prototype.webkitMatchesSelector
    || Element.prototype.msMatchesSelector
}

if (!Element.prototype.closest) {
  Element.prototype.closest = function (s) {
    let el = this

    do {
      if (el.matches(s)) return el
      el = el.parentElement || el.parentNode
    } while (el !== null && el.nodeType === 1)
    return null
  }
}

// store 命名空间
const namespace = 'hexagon'

/**
 * 组件store
 * 请在使用组件时，动态注册module
 * store.registerModule(namespace, componentStore)
 */
const componentStore = store

export {
  namespace,
  componentStore,
  /**
   * SinglePageDesigner
   * 单页面设计器
   */
  SinglePageDesigner,
  /**
   * FormDesigner
   * 表单设计器
   */
  FormDesigner,
  /**
   * PageRender
   * 页面渲染器
   */
  PageRender,
  MenuIcon,
  ColorPicker,
  predefineColors,
}

/**
 * Hexagon
 * 站点设计器（多页面设计）
 */
export default Hexagon
