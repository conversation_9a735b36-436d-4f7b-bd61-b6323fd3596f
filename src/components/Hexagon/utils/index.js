import structuredClone from '@ungap/structured-clone'

export const debounce = (fn, ms = 0) => {
  let timeoutId;
  const clear = () => {
    if (timeoutId) clearTimeout(timeoutId);
  };
  return function (...args) {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => fn.apply(this, args), ms);
    return {
      time: timeoutId,
      clear
    };
  };
};

export const throttle = (fn, wait) => {
  let inThrottle, lastFn, lastTime;
  return function () {
    const context = this,
      args = arguments;
    if (!inThrottle) {
      fn.apply(context, args);
      lastTime = Date.now();
      inThrottle = true;
    } else {
      clearTimeout(lastFn);
      lastFn = setTimeout(function () {
        if (Date.now() - lastTime >= wait) {
          fn.apply(context, args);
          lastTime = Date.now();
        }
      }, Math.max(wait - (Date.now() - lastTime), 0));
    }
  };
};

export const listenBySelector = function (
  container,
  eventType,
  selector,
  handler
) {
  let selectors = [
    {
      selector,
      handler
    }
  ];
  function _realHandler(ev) {
    if (ev.button !== 0) return;
    for (let i = 0; i < selectors.length; i++) {
      const matchedChild = ev.target.closest(selectors[i].selector);
      if (matchedChild) {
        const stopPropagation = selectors[i].handler.call(
          matchedChild,
          ev,
          matchedChild
        );
        if (stopPropagation) {
          break;
        }
      }
    }
  }

  container.addEventListener(eventType, _realHandler);

  const resultHandler = {
    and(selector, handler) {
      selectors.push({
        selector,
        handler
      });
      return resultHandler;
    },
    clear() {
      container.removeEventListener(eventType, _realHandler);
      selectors = [];
    }
  };
  return resultHandler;
};

export const deepClone = data => structuredClone(data);

export const peekStyles = (style = {}) => {
  const newStyle = {};
  Object.keys(style).forEach(key => {
    if (key === "opacity") {
      newStyle[key] = style[key] + "%";
    } else if (key === "transformRotate") {
      newStyle["transform"] = `rotate(${style[key]}deg)`;
    } else if (style[key] === +style[key] && key !== "zIndex" && key !== "zoom") {
      newStyle[key] = style[key] + "px";
    } else {
      newStyle[key] = style[key];
    }
  });
  return newStyle;
};

export const supportsPointerEvents = (() => {
  const dummy = document.createElement("_");
  if (!("pointerEvents" in dummy.style)) return false;
  dummy.style.pointerEvents = "auto";
  dummy.style.pointerEvents = "x";
  document.body.appendChild(dummy);
  var r = getComputedStyle(dummy).pointerEvents === "auto";
  document.body.removeChild(dummy);
  return r;
})();

export function unique(array, compare = (a, b) => a === b) {
  const result = [];
  for (let i = 0, len = array.length; i < len; i++) {
    const current = array[i];
    if (result.findIndex(v => compare(v, current)) === -1) {
      result.push(current);
    }
  }
  return result;
}

export const checkArrayWithPush = (target, key, value) => {
  if (Array.isArray(target[key])) {
    target[key].push(value);
  } else {
    target[key] = [value];
  }
};

export const getMaxDistance = arr => {
  const num = arr.sort((a, b) => a - b);
  return num[num.length - 1] - num[0];
};

/**
 * rootDispatch
 * @param {String} name
 * @param {*} payload
 * @param {String|Boolean} type
 */
export function rootDispatch(name, payload) {
  return function (type = "designer") {
    this.dispatch(`hexagon/${type ? type + "/" : ""}${name}`, payload);
  };
}

export const arrayTool = {
  move(arr, from, to) {
    if (to >= arr.length) {
      var k = to - arr.length + 1;
      while (k--) {
        arr.push(undefined);
      }
    }
    arr.splice(to, 0, arr.splice(from, 1)[0]);
    this.sort(arr);
    return arr;
  },
  insert(arr, index, item) {
    arr.splice(index, 0, item);
    this.sort(arr);
    return arr;
  },
  copy(arr, index) {
    const [copyItem] = arr.slice(index, index + 1);
    copyItem &&
      arr.splice(
        index,
        0,
        deepClone({
          ...copyItem,
          sort: index + 1,
          id: new Date().getTime()
        })
      );
  },
  sort(arr) {
    for (let i = 0; i < arr.length; i++) {
      if(arr[i]) {
        arr[i].sort = i;
      }
    }
  },
  remove(arr, index) {
    arr.splice(index, 1);
    this.sort(arr);
    return arr;
  }
};

export function getVidFromTencentVideo(url) {
  let vid = "";
  if (url && url.indexOf("qq.com") !== -1) {
    try {
      const splstr = url.split(".html")[0].split("/");
      const splvid = splstr[splstr.length - 1];
      //腾讯视频vid为11位字符，不符合返回空
      vid = splvid && splvid.length === 11 ? splvid : "";
    } catch (error) { }
  }
  return vid;
}

export const createId = () => {
  const S4 = function () {
    return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
  };
  // return (S4() + S4() + "-" + S4() + "-" + S4() + "-" + S4() + "-" + S4() + S4() + S4());
  return (S4() + S4() + S4() + S4());
};

//检查颜色是否是深色
export function isDark(color) {
  // Variables for red, green, blue values
  var r, g, b, hsp;

  // Check the format of the color, HEX or RGB?
  if (color.match(/^rgb/)) {
    // If RGB --> store the red, green, blue values in separate variables
    color = color.match(
      /^rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d+(?:\.\d+)?))?\)$/
    );

    r = color[1];
    g = color[2];
    b = color[3];
  } else {
    // If hex --> Convert it to RGB: http://gist.github.com/983661
    color = +("0x" + color.slice(1).replace(color.length < 5 && /./g, "$&$&"));

    r = color >> 16;
    g = (color >> 8) & 255;
    b = color & 255;
  }

  // HSP (Highly Sensitive Poo) equation from http://alienryderflex.com/hsp.html
  hsp = Math.sqrt(0.299 * (r * r) + 0.587 * (g * g) + 0.114 * (b * b));

  // Using the HSP value, determine whether the color is light or dark
  if (hsp > 127.5) {
    return false;
  }
  return true;
}

//base64转file
export function b64toFile(b64Data, filename) {
  const arr = b64Data.split(",");
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  let n = bstr.length,
    u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new File([u8arr], filename, {
    type: mime
  });
}

export const genCompOffsetInfoText = ({ width, height, left, top }) => {
  return `${$t("marketing_pd.commons.k_ea5a1c")}:${parseInt(width)} ${height !== undefined
      ? $t("marketing_pd.commons.g_4296d7") + ":" + (height !== "auto" ? parseInt(height) : "auto")
      : ""
    } X:${parseInt(left)} Y:${parseInt(top)}`;
};

export function formatDateTime(millisecond, format) {
  var thisTime = new Date(millisecond);
  var o = {
    "M+": thisTime.getMonth() + 1 || "", //月份
    "D+": thisTime.getDate() || "", //日
    "h+": thisTime.getHours() || "", //小时
    "m+": thisTime.getMinutes() || "", //分
    "s+": thisTime.getSeconds() || "", //秒
    "q+": Math.floor((thisTime.getMonth() + 3) / 3) || "", //季度
    S: thisTime.getMilliseconds() || "" //毫秒
  };
  if (/(Y+)/.test(format))
    format = format.replace(
      RegExp.$1,
      ((thisTime.getFullYear() || "") + "").substr(4 - RegExp.$1.length)
    );
  for (var k in o)
    if (new RegExp("(" + k + ")").test(format))
      format = format.replace(
        RegExp.$1,
        RegExp.$1.length == 1 ? o[k] : ("00" + o[k]).substr(("" + o[k]).length)
      );
  return format;
}

export function getSystemThemeColor() {
  if (window.FS && typeof window.FS.getColorInfo === 'function') {
    const colorInfo = window.FS.getColorInfo()
    const {
      colors = {},
    } = colorInfo || {}

    return colors['06'] || ''
  }

  return ''
}
