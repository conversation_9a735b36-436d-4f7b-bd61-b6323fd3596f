<template>
  <div :class="[$style.SettingWrap]">
    <div :class="$style.header">
      <div :class="$style.comp_title">{{ title }}</div>
      <div :class="$style.tabs_wrap">
        <Tabs
          v-model="tabValue"
          v-if="showTabs"
          :item="tabs"
          :itemStyle="{ width: '50px' }"
          @input="(val) => $emit('input', val)"
        />
      </div>
    </div>
    <div :class="$style.content"><slot></slot></div>
  </div>
</template>
<script>
import Tabs from "./Tabs.vue";
export default {
  components: {
    Tabs,
  },
  props: {
    value: {
      type: String,
      default: "content", //tab type: 1.conetnt 2.style
    },
    title: {
      type: String,
      default: $t('marketing_pd.commons.sz_e366cc'),
    },
    data: {
      type: Object,
      default: () => ({}),
    },
    hideTabs: {
      type: Array,
      default: () => [
        "blank",
        "gridcontianer",
        "container",
        "page",
        "menu",
        "gridcontainer",
        "followwechat",
        "wechatvideo",
        "videohomepage",
        "videolive",
        "rectangle",
        'minivipmemberinfo',
        'miniviplevel',
        'minivipbaseinfo',
        'minivippointsdetails',
        'minivipswitchmembershipplan',
        'minivipswitchstores',
        'minivipswitchstores',
        'minivipcoupon',
        'authlogin',
      ],
    },
  },
  data() {
    return {
      tabValue: this.value,
    };
  },
  computed: {
    showTabs() {
      return this.hideTabs.indexOf(this.data.type) === -1;
    },
    tabs() {
      const { layoutTabType } = this.$store.state.hexagon || {};
      const { type } = this.data || {};
      const tabs = [
        { label: $t('marketing_pd.commons.nr_2d711b'), value: 'content' },
        { label: $t('marketing_pd.commons.ys_390037'), value: 'style' },
      ]
      if(layoutTabType === 'flow' && ["radio", "checkbox", "region", "cascade", "input", "date", "file", "fileupload"].includes(type)){
        tabs.push({ label: $t('marketing_pd.commons.lj_29fd44'), value: 'logic' });
      }
      return tabs;
    },
  },
  watch: {
    value(value) {
      this.tabValue = value;
    },
  },
};
</script>
<style lang="less" module>
.SettingWrap {
  padding-bottom: 15px;
  .header {
    height: 49px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #e9edf5;
    padding: 0 13px;
  }
  .comp_title {
    flex: 1;
    color: #181c25;
    font-size: 13px;
    margin-right: 10px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  .tabs_wrap {
    text-align: right;
  }
  :global {
    .hexagon__tabs_item {
      margin-left: 10px;
    }
  }
}
</style>
