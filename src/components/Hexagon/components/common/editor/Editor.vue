<template>
  <div :class="[$style.editor, 'fs__vue-quill']">
    <div
      id="fs-quill-toolbar"
      :class="$style.editor_toolbar"
    >
      <Toolbar
        :value="editorToolbarStyle"
        @input="handleToolChange"
      />
    </div>
    <div ref="editor" />
  </div>
</template>
<script>
import Quill from 'quill'
import Toolbar from './Toolbar.vue'
import { defaultOptions } from './config.js'
import { formatEditorStyle, formatToolbarStyle } from './util.js'
import 'quill/dist/quill.snow.css'

const SizeStyle = Quill.import('attributors/style/size')
const AlignStyle = Quill.import('attributors/style/align')
const Header = Quill.import('formats/header')
const Font = Quill.import('attributors/style/font')
// const Icons = Quill.import("ui/icons");
// import IconArray from "./icons";

// //替换图标
// IconArray.forEach(item => {
//   Icons[item.name] = item.svg;
// });

const AvailableLineHeights = []
for (let i = 0; i <= 100; i += 1) {
  AvailableLineHeights.push(`${i}px`)
}
const AvailableLetterSpacing = []
for (let i = 0; i <= 60; i += 1) {
  AvailableLetterSpacing.push(`${i}px`)
}

const Parchment = Quill.import('parchment')
const lineHeightConfig = {
  scope: Parchment.Scope.INLINE,
  whitelist: AvailableLineHeights,
}
const lineHeightClass = new Parchment.Attributor.Class(
  'lineheight',
  'ql-line-height',
  lineHeightConfig,
)
const lineHeightStyle = new Parchment.Attributor.Style(
  'lineheight',
  'line-height',
  lineHeightConfig,
)
const letterSpacingConfig = {
  scope: Parchment.Scope.INLINE,
  whitelist: AvailableLetterSpacing,
}
const letterSpacingClass = new Parchment.Attributor.Class(
  'letterspacing',
  'ql-letter-spacing',
  letterSpacingConfig,
)
const letterSpacingStyle = new Parchment.Attributor.Style(
  'letterspacing',
  'letter-spacing',
  letterSpacingConfig,
)
Parchment.register(lineHeightClass)
Parchment.register(lineHeightStyle)
Parchment.register(letterSpacingClass)
Parchment.register(letterSpacingStyle)

// custom font
const Fonts = [
  {
    name: 'Arial',
    value: 'Helvetica, Arial, sans-serif',
  },
  // {
  //   name: "Tahoma",
  //   value: "Tahoma, Geneva, sans-serif"
  // },
  // {
  //   name: "Verdana",
  //   value: "Verdana, Geneva, sans-serif"
  // },
  {
    name: $t('marketing_pd.commons.ht_e0b004'),
    value: 'SimHei, STHeiti',
  },
  {
    name: $t('marketing_pd.commons.st_71b068'),
    value: 'SimSun, NSimSun, STSong, STFangsong',
  },
  // {
  //   name: "微软雅黑",
  //   value: "Microsoft YaHei, Hiragino Sans GB, STHeiti"
  // }
]
// const Fonts = ['微软雅黑', '宋体', '新宋体', '仿宋', '楷体', '黑体', 'Arial', 'Arial Black','Times New Roman', 'Courier New', 'Tahoma', 'Verdana'];
// Font.whitelist = ['Arial Black, Gadget, sans-serif', 'Comic Sans MS, cursive, sans-serif', 'Impact, Charcoal, sans-serif', 'Tahoma, Geneva, sans-serif', 'Trebuchet MS, Helvetica, sans-serif', 'Verdana, Geneva, sans-serif'];
Font.whitelist = Fonts.map(item => item.value)
Quill.register(Font, true)

// coustom header
Header.create = function (value) {
  const node = document.createElement(`h${value}`)
  node.style.cssText = `font-size:${(6 - value) * 4 + 12}px;`
  return node
}
// coustom fon size
const fontSizes = []
for (let i = 10; i <= 96; i += 1) {
  fontSizes.push(`${i}px`)
}
SizeStyle.whitelist = fontSizes

Quill.register(Header, true)
Quill.register(SizeStyle, true)
Quill.register(AlignStyle, true)

// const styleMaps = (maps => {
//   Object.keys(maps).forEach(key => {
//     maps[maps[key]] = key
//   })
//   return maps;
// })({
//   fontSize: 'size',
//   fontFamily: "font",
//   lineHeight: 'lineheight',
//   color: "color",
//   background: "background",
//   textAlign: "align",
//   fontWeight: "bold",
//   letterSpacing: "letterspacing",
//   textDecoration: "none",
//   'line-through': 'strike',
//   underline: 'underline',
//   fontStyle: "italic"
// });
export default {
  components: {
    Toolbar,
  },
  props: {
    value: {
      type: String,
      default: '',
    },
    options: Object,
    disabled: Boolean,
    placeholder: {
      type: String,
      default: $t('marketing_pd.commons.qsrnr_a11cc7'),
    },
    fonts: {
      type: Array,
      default: () => Fonts,
    },
    fontSizes: {
      type: Array,
      default: () => fontSizes,
    },
    colors: {
      type: Array,
      default: () => [
        '#91959E',
        '#181c25',
        '#ffffff',
        '#409EFF',
        '#e9edf5',
        '#ff4500',
        '#ff8c00',
        '#ffd700',
        '#90ee90',
        '#00ced1',
        '#1e90ff',
        '#c71585',
        'rgba(255, 69, 0, 0.68)',
        'rgb(255, 120, 0)',
      ],
    },
    headers: {
      type: Array,
      default: () => [1, 2, 3, 4, 5, 6],
    },
  },
  data() {
    return {
      AvailableLineHeights,
      AvailableLetterSpacing,
      editorToolbarStyle: {
        fontSize: 14,
        fontFamily: 'Helvetica, Arial, sans-serif',
        lineHeight: 20,
        color: '#333333',
        background: 'none',
        textAlign: 'left',
        fontWeight: 'normal',
        letterSpacing: 0,
        textDecoration: 'none',
        fontStyle: 'normal',
      },
    }
  },
  watch: {
    value(val) {
      if (this.quill) {
        if (
          val
          && val !== this.quill.root.innerHTML
          && !this.quill.hasFocus()
        ) {
          this.quill.root.innerHTML = val
        }
      }
    },
    disabled(newVal, oldVal) {
      if (this.quill) {
        this.quill.enable(!newVal)
      }
    },
  },
  mounted() {
    this._initialize()
  },
  beforeDestroy() {
    this.quill = null
    delete this.quill
  },
  methods: {
    _initialize() {
      const editorDom = this.$refs.editor
      this.quill = new Quill(editorDom, {
        ...defaultOptions,
        ...this.options,
        modules: {
          toolbar: null,
        },
        placeholder: this.placeholder,
      })
      this._watchSelectChange()
      this._watchTextChange()
      this._disabled()
      this._initContent()
      // option max height
      const toolbarOptions = this.$el.querySelectorAll('.ql-picker-options')
      for (let i = 0; i < toolbarOptions.length; i += 1) {
        toolbarOptions[i].style.maxHeight = `${editorDom.clientHeight}px`
      }
    },
    // disabled
    _disabled() {
      console.log('disabled', this.disabled)
      if (this.quill) this.quill.enable(this.disabled !== true)
    },
    _watchSelectChange() {
      if (this.quill) {
        this.quill.on('selection-change', range => {
          if (!range) {
            this.$emit('blur', this.quill)
          } else {
            this.$emit('focus', this.quill)
          }
        })
      }
    },
    _watchTextChange() {
      if (this.quill) {
        this.quill.on('text-change', (delta, oldDelta, source) => {
          let html = this.$refs.editor.children[0].innerHTML
          if (html === '<p><br></p>') html = ''
          const text = this.quill.getText()
          html = html.replace(/<span class="ql-cursor">.*?<\/span>/g, '')

          // const reg = /&quot;([\s\S]+)&quot;/g
          // html = html.replace(reg, ($0, $1) => `${$1.split(' ').join('-')}`)

          /**
           * 设置图层显示和隐藏的时候，会触发input事件，但是此刻setting中的数据是之前旧的，
           * 导致设置失败，这里增加一个异步，保证数据更新到最新
           * bug：https://www.tapd.cn/20185301/bugtrace/bugs/view/1120185301001313735
           */
          setTimeout(() => {
            this.$emit('input', html)
            this.$emit('text-change', html, text)
          }, 300)
        })
      }
      this.quill.on('selection-change', (range, oldRange, source) => {
        if (range) {
          let style = {}
          if (range.length === 0) {
            style = this.quill.getFormat(range.index)
          } else {
            style = this.quill.getFormat(range.index, range.length)
          }
          // console.log('selection-change>>', style)
          // 只更新有变化的属性，避免触发不必要的更新
          const newStyle = Object.keys(style).reduce((s, k) => Object.assign(s, formatToolbarStyle(k, style[k])), {})
          Object.keys(newStyle).forEach(key => {
            if (this.editorToolbarStyle[key] !== newStyle[key]) {
              this.$set(this.editorToolbarStyle, key, newStyle[key])
            }
          })
        }
      })
    },
    _initContent() {
      if (this.value && this.quill) this.quill.root.innerHTML = this.value
    },
    handleToolChange(style) {
      Object.keys(style).forEach(key => {
        if (style[key] !== this.editorToolbarStyle[key]) {
          const s = formatEditorStyle(key, style[key])
          if (s.length) this.quill.format(...formatEditorStyle(key, style[key]))
          else {
            this.quill.format('strike', false)
            this.quill.format('underline', false)
          }
          this.editorToolbarStyle[key] = style[key]
        }
      })
    },
  },
}
</script>
<style lang="less" module>
.editor {
  width: 100%;
  height: 100%;
  min-height: 350px;
  display: flex;
  flex-direction: column;
  .editor_toolbar {
    :global{
      .el-select{
        margin-bottom: 0;
      }
    }
  }
  :global {
    .ql-editor {
      line-height: 20px;
      font-size: 14px;
      font-family: "Helvetica Neue", Helvetica, "PingFang SC",
        "Hiragino Sans GB", "Microsoft YaHei", "\5FAE\8F6F\96C5\9ED1", Arial,
        sans-serif;
    }
    .ql-editor ol li:not(.ql-direction-rtl), .ql-editor ul li:not(.ql-direction-rtl){
      padding-left: 0;
    }
    .ql-container.ql-snow {
      flex: 1;
      max-height: 293px;
      overflow-y: auto;
      background-color: #FFF;
      &.ql-disabled{
        .ql-blank{
          &::before{
            display: none;
          }
        }
      }
    }
    .ql-snow {
      border-color: #e9edf5;
      border-top: 0;
    }
    .ql-snow {
      .ql-picker {
        outline: none;
        &:hover,
        &:after,
        &:before,
        * {
          outline: none;
        }
      }
    }
    em {
      font-style: italic;
    }
  }
}
</style>
