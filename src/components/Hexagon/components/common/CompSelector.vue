<template>
  <div
      :style="{
        zIndex: zIndex
      }"
      :class="[
          $style.CompSelectorContainer,
          'hexagon__grid-comp-event',
           data.id === currentComp.id && $style.CompSelectorSelected
           ]"
           @mouseenter="mouseenter"
           @mouseleave="mouseleave"
           @mousedown.stop
           :index="index"
           @click.stop="selectItem">

    <div :class="$style.CompSelectorBorder">
      <div :class="$style.CompSelectorItem" @mouseenter="moveTarget = 'top'"></div>
      <div :class="$style.CompSelectorItem" @mouseenter="moveTarget = 'bottom'"></div>
    </div>
    <div @mousedown.stop="dragMousedown" :class="[$style.CompSelectorHandle, canFixDrag? '' : 'hexagon__page-comp-draghandle']"></div>
    <span v-if="canDelete" :class="[$style.CompSelectorDelete, 'el-icon-close']" @click.stop.prevent="deleteItem"></span>
    <span v-if="canChangeHeight" :class="[$style.dragPoint, 'hexagon__page-comp-dragpoint']" @mousedown.stop="mousedown"></span>
    <span v-if="canCopy" :class="[$style.CompSelectorCopy, 'el-icon-copy-document']" @click.stop="copyItem"></span>
    <span v-show="showOffsetInfo" :class="$style.offset">{{ genOffsetInfo(data.style) }}</span>
  </div>
</template>
<script>
import { mapState } from 'vuex';
import bodyEvent from "../../mixin/bodyEvent";
import { arrayTool } from "../../utils";
export default {
  props: {
    zIndex: {
      type: Number,
      default: 1
    },
    data: {
      type: Object,
      default: ()=> {}
    },
    index: {
      type: Number,
      default: 0
    },
    currentComp: {
      type: Object,
      default: ()=> {}
    },
    isGrid: {
      type: Boolean,
      default: false
    }
  },
  mixins: [bodyEvent],
  data() {
    return {
      moveTarget: 'top', // top, bottom
      isDrag: false,
      startY: 0,
      startX: 0,
      moveY: -1,
      moveX: -1,
      currCompLeft: 0,
      currCompTop: 0,
      currCompHeight: 0,
      currCompMaxHeight: 10000,
      doubleClickLastTriggerTime: null,
      isSuspensionDragMove: false,
    };
  },
  computed: {
    ...mapState('hexagon/designer', {
      viewportStyle: 'viewportStyle',
    }),
    showOffsetInfo() {
      if(this.isGrid) {
        return false
      }
      return this.data.type !== 'suspension'
            && this.data.type !== 'contact'
            && this.data.type !== 'videolive'
            && this.data.position !== 'fixed-bottom-radius'
            && !(this.data.type === 'video' && this.data.layoutType === 'icon-only')
    },
    canCopy() {
      if(this.isGrid || [
        'form-container',
        'grid',
        'tab-container'
      ].indexOf(this.data.key) >= 0 || this.data.noEdit || this.data.isFormComp) {
        return false
      }
      return true
      // return [
      //   'image',
      //   'text',
      //   'video',
      //   'slider',
      //   'blank',
      //   'line',
      //   'qrcode',
      //   'wechatvideo',
      //   'suspension',
      //   'contact',
      //   'menu'
      // ].indexOf(this.data.type) >= 0 &&!this.data.isFormComp;
    },
    canFixDrag() {
      return this.data.type === "suspension" ||
          this.data.type === "contact" ||
          (this.data.type === "videolive" && this.data.layout === 1)
          || (this.data.type === 'video' && this.data.layoutType === 'icon-only')
    },
    isPresetActivityTemplate() {
      return this.$route.query.templateType === 'activityComp'
    },
    canDelete() {
      // 会议主页的表单组件不能删除
      if(this.isPresetActivityTemplate && this.data.typeValue === 'form') {
        return false
      }
      // 由于之前模板那里刷了nodeletion = true  但是需求改成了允许删除 所以这里需要特殊处理
      if(this.data.typeValue === 'marketingEventSignupbutton') {
        return true
      }
      return !(this.data.noDeletion || (this.data.type === 'button' && this.data.isFormComp) || this.data.type === 'gridBlankItem')
    },
    canChangeHeight() {
      if(this.isGrid) {
        return false
      }
      return ['image', 'slider', 'blank', 'button', 'container', 'tel', 'qrcode', 'signupbutton', 'wechatvideo'].indexOf(this.data.type) !== -1 && this.data.typesetting !== 'flow'
    },
  },
  mounted() {
    this.globalMouseMoveHandler = this.onBodyMouseMove(this.mousemove);
    this.globalMouseUpHandler = this.onBodyMouseUp(this.mouseup);
  },
  beforeDestroy() {
    this.globalMouseMoveHandler.clear();
    this.globalMouseUpHandler.clear();
  },
  methods: {
    isDragFix(item) {
      if (
          item.type === "suspension" ||
          item.type === "contact" ||
          (item.type === "videolive" && item.layout === 1)
          || (item.type === 'video' && item.layoutType === 'icon-only')
      )
        return true;
      return false;
    },
    dragMousedown(ev) {
      if(!this.canFixDrag) {
        return
      }
      this.isSuspensionDragMove = true;
      this.startY = ev.pageY;
      this.startX = ev.pageX;
      this.currCompLeft = this.data.wrapStyle.left;
      this.currCompTop = this.data.wrapStyle.top;
    },
    mouseenter() {
      this.$emit('mouseenter', this.index, this.moveTarget)
    },
    mouseleave() {
      this.$emit('mouseleave')
    },
    genOffsetInfo(style) {
      const width = this.viewportStyle.width;
      if (!style) {
        return `${$t('marketing_pd.commons.k_ea5a1c')}:${width} ${$t('marketing_pd.commons.g_4296d7')}:auto`;
      }
      return `${$t('marketing_pd.commons.k_ea5a1c')}:${width} ${$t('marketing_pd.commons.g_4296d7')}:${
        this.moveY > -1
          ? parseInt(this.moveY)
          : style.height !== undefined && style.height !== "auto"
          ?  parseInt(style.height)
          : "auto"
      }`;
    },
    selectItem() {
      this.$emit('selectItem', this.data, this.index)
      //双击画板编辑
      if (
        this.doubleClickLastTriggerTime &&
        new Date() - this.doubleClickLastTriggerTime < 300
      ) {
       // 为了兼容旧数据sort值不对的问题，每次双击进去都重排一遍
      if(this.data.type === 'container' && this.data.components && this.data.components.length > 0) {
        arrayTool.sort(this.data.components)
        // 有多个子container 时 子container 也要重排一遍
        if(this.data.layout === 'multiple') {
          this.data.components.map((item)=> {
              if(item.type === 'container' && item.components && item.components.length > 0) {
                arrayTool.sort(item.components)
              }
          })
        }
      }
      //双击选择器事件
      this.$emit('dbClickItem', this.data, this.index)
      }
      this.doubleClickLastTriggerTime = new Date();
    },
    deleteItem() {
      this.$emit('deleteItem', this.data, this.index)
    },
    copyItem() {
      this.$emit('copyItem', this.data, this.index)
    },
    mousemove(ev) {
      if(this.isDrag) {
        let scale = 375 / this.viewportStyle.width;
        let y = ev.pageY - this.startY;
        if(this.data.type === 'image') {
          y = y * scale; 
        }
        this.moveY =  this.currCompHeight + y;
        if (this.moveY < 30) {
          this.moveY = 30;
        }
        if(this.moveY > this.currCompMaxHeight){
          this.moveY = this.currCompMaxHeight;
          return
        }
       
        
        this.$emit('updateItem', this.data, {
          height: this.moveY
        })
      }
       //更新悬浮按钮组件位置信息
      if (this.isSuspensionDragMove) {
        const [left, top] = [
          ev.pageX - this.startX + this.currCompLeft,
          ev.pageY - this.startY + this.currCompTop,
        ];
        this.$emit('updateItem', this.data, {}, {
          top,
          left,
        });
      }
    },
    mouseup() {
      this.isDrag = false;
      this.isSuspensionDragMove = false;
    },
    mousedown(ev) {
      this.startY = ev.pageY;
      this.isDrag = true;
      this.currCompHeight = this.data.style.height;
    }
  }
};
</script>

<style lang="less" module>
.CompSelectorContainer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  .CompSelectorHandle {
    height: 100%;
    width: 100%;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 8;
  }
  .CompSelectorBorder {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 7;
    display: flex;
    flex-direction: column;
    .CompSelectorItem {
      flex: 1;
    }
  }
  .CompSelectorDelete {
    position: absolute;
    top: 0;
    right: 0;
    color: #fff;
    background: var(--color-primary06,#ff8000);
    cursor: pointer;
    font-size: 12px;
    opacity: 0;
    width: 15px;
    height: 15px;
    line-height: 15px;
    text-align: center;
    z-index: 9;
  }
  .dragPoint {
    position: absolute;
    bottom: 2px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--color-primary06,#ff8000);
    cursor: ns-resize;
    width: 5px;
    height: 5px;
    z-index: 9;
    opacity: 0;
  }
  .offset {
    color: #fff;
    font-size: 12px;
    background-color: rgba(0, 0, 0, 0.3);
    position: absolute;
    top: -18px;
    left: 0;
    padding: 0 5px;
    opacity: 0;
  }
  .CompSelectorCopy {
    position: absolute;
    top: 0;
    left: 0;
    color: #fff;
    background: var(--color-primary06,#ff8000);
    cursor: pointer;
    font-size: 12px;
    opacity: 0;
    width: 15px;
    height: 15px;
    line-height: 15px;
    text-align: center;
    z-index: 9;
  }
  &:hover {
    .CompSelectorBorder {
      border: 2px solid var(--color-primary06,#ff8000);
    }
    .CompSelectorDelete {
      opacity: 1;
    }
    .offset {
      opacity: 1;
    }
    .CompSelectorCopy {
      opacity: 1;
    }
    .dragPoint {
      opacity: 1;
    }
  }
}
</style>
