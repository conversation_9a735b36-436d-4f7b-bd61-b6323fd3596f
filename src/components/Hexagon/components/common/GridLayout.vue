<template>
  <div :class="[$style.layout, 'hexagon__layoutSetting']">
    <div
      v-for="item in layoutOptions"
      :key="item.value"
      :class="[
        $style.item,
        'hexagon__layoutSetting_item',
        item.value === value ? $style.selected : '',
        disabled ? $style.disabled : ''
      ]"
      @click="handleItemClick(item.value)"
    >
      <div v-if="item.value === value" :class="$style.checkmark"></div>
      <div :class="$style.preview">
        <div
          v-for="(ratio, index) in getRatios(item.value)"
          :key="index"
          :class="$style.col"
          :style="{ flex: ratio }"
        ></div>
      </div>
    </div>
  </div>
</template>

<script>
const layoutOptions = [
  { value: '1' },
  { value: '1,1' },
  { value: '1,2' },
  { value: '2,1' },
  { value: '1,1,1' },
  { value: '1,2,1' },
  { value: '1,1,1,1' }
]

export default {
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    options: {
      type: Array,
      default: () => layoutOptions
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    layoutOptions() {
      return this.options.length ? this.options : layoutOptions;
    }
  },
  methods: {
    handleItemClick(value) {
      if (this.disabled) return;
      this.$emit('input', value);
      this.$emit('change', value);
    },
    getRatios(value) {
      return value.split(',').map(Number);
    }
  }
}
</script>

<style lang="less" module>
.layout {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.item {
  flex: 0 0 calc(25% - 6px);
  cursor: pointer;
  background: #fff;
  border-radius: 2px;
  transition: all 0.2s;
  position: relative;
  overflow: hidden;
  border: 1px solid #E9EDF5;
  padding: 1px;

  &::before {
    content: '';
    display: block;
    padding-top: 65%;
  }

  &:hover {
    border-color: var(--color-primary06, #407FFF);
  }

  &.disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }

  &.selected {
    border-color: var(--color-primary06, #407FFF);

    &::after {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 0;
      height: 0;
      border-top: 16px solid var(--color-primary06, #407FFF);
      border-right: 16px solid transparent;
    }

    &::before {
      content: '';
      display: block;
      padding-top: 65%;
    }

    .checkmark {
      content: "";
      position: absolute;
      top: 3px;
      left: 3px;
      width: 6px;
      height: 3px;
      border: 1px solid #fff;
      border-top: none;
      border-right: none;
      transform: rotate(-45deg);
      z-index: 2;
    }

    .col {
      background: var(--color-primary06, #407FFF);
      opacity: 0.2;
    }
  }
}

.preview {
  position: absolute;
  top: 4px;
  left: 4px;
  right: 4px;
  bottom: 4px;
  display: flex;
  gap: 2px;
}

.col {
  background: #F5F5F5;
  transition: all 0.2s;
  border-radius: 1px;
}
</style>

