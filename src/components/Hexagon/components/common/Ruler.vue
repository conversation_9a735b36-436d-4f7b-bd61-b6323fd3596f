<template>
  <div :class="$style.ruler">
    <!-- 水平标尺 -->
    <div :class="$style.horizontalRuler">
      <!-- 刻度线 -->
      <div :class="$style.tickContainer">
        <div v-for="tick in getVisibleTicks('horizontal')" :key="`h-tick-${tick.value}`"
             :class="[$style.tick, getTick(tick.type)]"
             :style="{ left: `${tick.position}px` }">
        </div>
      </div>
      
      <!-- 数字标签 - 只渲染0和100的倍数 -->
      <div v-for="i in getNumbersArray(width, contentX)" :key="`h-num-${i.value}`" 
           :class="$style.rulerNumberContainer"
           :style="{ left: `${i.position}px` }">
        <span :class="$style.rulerNumber">{{ i.value }}</span>
      </div>
    </div>
    
    <!-- 垂直标尺 -->
    <div :class="$style.verticalRuler">
      <!-- 刻度线 -->
      <div :class="$style.tickContainer">
        <div v-for="tick in getVisibleTicks('vertical')" :key="`v-tick-${tick.value}`"
             :class="[$style.tick, getTick(tick.type)]"
             :style="{ top: `${tick.position}px` }">
        </div>
      </div>
      
      <!-- 数字标签 - 只渲染0和100的倍数 -->
      <div v-for="i in getNumbersArray(height, contentY)" :key="`v-num-${i.value}`" 
           :class="$style.rulerNumberContainer"
           :style="{ top: `${i.position}px` }">
        <span :class="$style.rulerNumber">{{ i.value }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Ruler',
  props: {
    width: {
      type: Number,
      required: true
    },
    height: {
      type: Number,
      required: true
    },
    offsetX: {
      type: Number,
      default: 0
    },
    offsetY: {
      type: Number,
      default: 0
    },
    contentX: {
      type: Number,
      default: 20
    },
    contentY: {
      type: Number,
      default: 20
    }
  },
  methods: {
    // 获取刻度样式类
    getTick(type) {
      switch(type) {
        case 'major': return this.$style.majorTick;
        case 'medium': return this.$style.mediumTick;
        case 'minor': return this.$style.minorTick;
        default: return '';
      }
    },

    // 获取可见刻度线
    getVisibleTicks(direction) {
      const isHorizontal = direction === 'horizontal';
      const size = isHorizontal ? this.width : this.height;
      const contentOffset = isHorizontal ? this.contentX : this.contentY;
      
      // 计算可见区域的起始结束位置
      const startPixel = -200; // 向前多渲染一些
      const endPixel = size + 200; // 向后多渲染一些
      
      const ticks = [];
      
      // 计算可见区域的刻度范围（像素）
      const startValue = startPixel - contentOffset;
      const endValue = endPixel - contentOffset;
      
      // 计算开始的主刻度位置（向前取整到100的倍数）
      const startMajor = Math.floor(startValue / 100) * 100;
      
      // 生成所有可见的刻度
      for (let i = startMajor; i <= endValue; i += 10) {
        const position = contentOffset + i;
        if (position >= startPixel && position <= endPixel) {
          // 确定刻度类型
          let type = 'minor';
          if (i % 100 === 0) {
            type = 'major';
          } else if (i % 50 === 0) {
            type = 'medium';
          }
          
          ticks.push({
            position,
            value: i,
            type
          });
        }
      }
      
      return ticks;
    },
    
    // 生成数字标签数组（只包含0和100的倍数）
    getNumbersArray(size, contentOffset) {
      const result = [];
      const existingValues = new Set(); // 用于避免重复值
      
      // 计算可见区域的起始结束位置（向外扩展一些以确保边缘数字可见）
      const startPixel = -100;
      const endPixel = size + 100;
      
      // 添加0点标记（如果在可见范围内）
      if (contentOffset >= startPixel && contentOffset <= endPixel) {
        result.push({
          value: 0,
          position: contentOffset
        });
        existingValues.add(0);
      }
      
      // 计算当前可见区域的刻度范围
      const startValue = startPixel - contentOffset;
      const endValue = endPixel - contentOffset;
      
      // 计算开始的数字位置（向前取整到100的倍数）
      const startNumberValue = Math.floor(startValue / 100) * 100;
      
      // 每100单位生成一个数字
      for (let value = startNumberValue; value <= endValue; value += 100) {
        if (value === 0 || existingValues.has(value)) continue; // 跳过0和已存在的值
        
        const position = contentOffset + value;
        if (position >= startPixel && position <= endPixel) {
          result.push({
            value,
            position
          });
          existingValues.add(value);
        }
      }
      
      return result;
    }
  }
}
</script>

<style lang="less" module>
.ruler {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: 20;
  width: 100%;
  height: 100%;
  overflow: visible;
}

.horizontalRuler {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 20px;
  background-color: rgba(245, 245, 245, 0.9);
  border-bottom: 1px solid #ddd;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  
  .tickContainer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: visible;
  }
  
  .tick {
    position: absolute;
    top: 0;
    width: 1px;
    background-color: #ccc;
  }
  
  .majorTick {
    height: 12px;
    background-color: #666;
  }
  
  .mediumTick {
    height: 8px;
    background-color: #999;
  }
  
  .minorTick {
    height: 4px;
    background-color: #ccc;
  }
  
  .rulerNumberContainer {
    position: absolute;
    top: 3px;
    transform: translateX(-50%);
    z-index: 30;
    
    .rulerNumber {
      font-size: 10px;
      color: #666;
      white-space: nowrap;
      background-color: rgba(245, 245, 245, 0.8);
      padding: 0 2px;
      border-radius: 2px;
    }
  }
}

.verticalRuler {
  position: absolute;
  top: 0;
  left: 0;
  width: 20px;
  height: 100%;
  background-color: rgba(245, 245, 245, 0.9);
  border-right: 1px solid #ddd;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  
  .tickContainer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: visible;
  }
  
  .tick {
    position: absolute;
    left: 0;
    height: 1px;
    background-color: #ccc;
  }
  
  .majorTick {
    width: 12px;
    background-color: #666;
  }
  
  .mediumTick {
    width: 8px;
    background-color: #999;
  }
  
  .minorTick {
    width: 4px;
    background-color: #ccc;
  }
  
  .rulerNumberContainer {
    position: absolute;
    transform: translateY(-50%);
    left: -11px;
    width: 30px;
    text-align: right;
    z-index: 30;
    
    .rulerNumber {
      font-size: 10px;
      color: #666;
      white-space: nowrap;
      background-color: rgba(245, 245, 245, 0.8);
      padding: 0 2px;
      border-radius: 2px;
      display: inline-block;
      writing-mode: vertical-lr;
      transform: rotate(180deg);
      line-height: 1.2;
      text-align: center;
    }
  }
}
</style> 