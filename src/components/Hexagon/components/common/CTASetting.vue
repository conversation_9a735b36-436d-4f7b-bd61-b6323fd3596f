<template>
  <div :class="[$style.cta_setting, sceneType === 'page' ? $style.cta_page : '']" :style="wrapStyle">
    <div :class="[$style.setting_item, 'hexagon__setting_item']">
      <div :class="[$style.title, 'hexagon__card-header']">
        {{ $t('marketing.components.Hexagon.sz_de3a05') }}
        <fx-tooltip v-if="!desc" effect="dark" :content="$t('marketing.components.Hexagon.kyywysqgzg_fc9e8b')" placement="top">
          <i class="el-icon-info" style="margin-left: 3px;" />
        </fx-tooltip>
        <fx-tooltip v-if="usedByCta" effect="dark" :content="$t('marketing.components.Hexagon.yydqwymybz_654f34')" placement="top">
          <fx-switch size="small" :disabled="true" style="margin-left: 10px;" />
        </fx-tooltip>
        <fx-switch v-else :value="ctaConfig && ctaConfig.enable || false" size="small" @change="handleCtaChange" style="margin-left: 10px;" />
      </div>
      <div v-if="desc" :class="[$style.desc, 'hexagon__setting_desc']" style="margin-bottom: 0;margin-top: 5px;">
        {{ desc }}<a href="https://help.fxiaoke.com/93d5/0f81/5171" target="_blank" style="margin-left: 10px;">{{
                  $t('marketing.commons.ljgd_06a3c1') }}</a>
      </div>
    </div>
    <template v-if="ctaConfig && ctaConfig.enable">
      <div :class="[$style.setting_item, 'hexagon__setting_item']">
        <div :class="[$style.con, 'hexagon__setting_con']">
          <PickerInput :class="$style.picker_input" :placeholder="$t('marketing.components.Hexagon.qxzzj_18d4f8')"
            :text="ctaConfig && ctaConfig.ctaName || ''" style="width:100%" @click="handlePickerInputClick" />
        </div>
      </div>
    </template>
    <CtaDialog v-if="ctaDialogVisible" :visible.sync="ctaDialogVisible" :sceneType="sceneType"
      @confirm="handleCtaDialogConfirm" />
  </div>
</template>
<script>
import PickerInput from '@/components/picker-input';
import CtaDialog from '@/pages/cta/components/cta-dialog.vue';
import Card from './Card.vue'
export default {
  components: {
    PickerInput,
    CtaDialog,
    Card,
  },
  props: {
    desc: {
      type: String,
      default: $t('marketing.components.Hexagon.kyywysqgzg_fc9e8b'),
    },
    data: {
      type: Object,
      default: () => ({})
    },
    wrapStyle: {
      type: Object,
      default: () => ({})
    },
    sceneType: {
      type: String,
      default: 'page'// all, page:页面级, button:自带按钮触发, manual:手动触发
    }
  },
  data() {
    return {
      ctaDialogVisible: false,
      ctaConfig: this.data || {},
    }
  },
  watch: {
    data(val) {
      this.ctaConfig = val || {};
    }
  },
  computed: {
    usedByCta() {
      return this.$store.state.hexagon.usedByCta;
    }
  },
  methods: {
    handleCtaChange(val) {
      this.ctaConfig.enable = val
      this.handleChange();
    },
    handlePickerInputClick() {
      this.ctaDialogVisible = true;
    },
    handleCtaDialogConfirm(data) {
      this.ctaConfig.ctaId = data.id;
      this.ctaConfig.ctaName = data.name;
      this.handleChange();
    },
    handleChange() {
      this.$emit('change', this.ctaConfig);
    }
  }
}
</script>
<style lang="less" module>
.cta_setting {
  &.cta_page {
    padding: 24px 14px 14px 14px;

    .title {
      color: #181c25;
      font-size: 14px;
      line-height: 20px;
      font-weight: 700;
    }
  }

  .desc {
    line-height: 1.5;
  }
}
</style>
