import ActionType from './ActionType.vue'
import ActionContent from './ActionContent.vue'
import CTASetting from '../CTASetting.vue'
import { deepClone } from '../../../utils/index.js'
import { actions, getActionByValue } from '../../../config/action.js'
import { defaultFileSendMailTemplate } from '@/components/Hexagon/config/components.js'

// 默认动作类型数据结构
const action = {
  type: '', // inside:页面配置ID；outside：跳转外部URL;miniprogram:外部小程序(预留项目前暂不支持设置);form:我的报名;file:文件下载地址
  id: '', // type为inside时，H5或者小程序跳转内部配置页面所需ID；type为form时，提交的表单组件ID
  url: '', // type为outside时，H5直接取此字段；type为miniprogram时，url为h5备用跳转地址, type为file为文件地址
  query: '', // URL链接参数
  label: '', // 动作名称
  openInTopWindow: false, // 当type为outside有效，针对iframe嵌入微页面场景，跳转外部链接是否使用顶部窗口打开链接
  miniprogram: {
    // type为miniprogram时，跳转外部小程序参数，支持类型：wechat、baidu、alipay，
    wechat: {
      appId: '',
      originalId: '',
      path: '',
    },
    baidu: {
      appId: '',
      path: '',
    },
  },
  content: {}, // 跳转内容, id:内容ID contentType:内容类型 objectType: 内容类型
  customizeLinkParams: [], // 自定义页面参数
  phone: '',
  chatTargetUid: '', // 聊天目标用户ID
  address: '', // 检索地址
  location: {}, // 地址经纬信息
  email: {
    title: $t('marketing.commons.xzwjtz_3de6b6'),
    sender: '',
    applyUser: '',
    html: defaultFileSendMailTemplate,
  },
  emailAttach: {},
  ctaConfig: {},//cta参数
}

export default {
  components: {
    ActionType,
    ActionContent,
    CTASetting,
  },
  props: {
    compType: {
      type: String,
      default: '',
    },
    value: {
      type: Object,
      default: () => ({}),
    },
    title: {
      type: String,
      default: $t('marketing_pd.commons.djhdz_f91dcc'),
    },
    // action name
    name: {
      type: String,
      default: '', // form_action:表单提交动作
    },
    // 禁用名单
    exclude: {
      type: Array,
      default: () => [],
    },
    // 使用名单
    include: {
      type: Array,
      default: () => [],
    },
    extendActions: {
      type: Array,
      default: () => [],
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    doNotRenderContentSlots: {
      type: Boolean,
      default: false,
    },
    // 扩展参数
    extendParams: Object,
  },
  data() {
    return {
      options: actions,
      form: [
        {
          label: $t('marketing_pd.commons.tjsq_0830b4'),
          value: '1',
        },
      ],
      action: deepClone(action),
    }
  },
  computed: {
    actionOption() {
      let options = [...this.options]
      // 包含动作类型计算
      if (this.include.length) {
        options = options.filter(
          item => this.include.indexOf(item.value) !== -1 || !item.value,
        )
      }

      // 排除动作类型计算
      options = options.reduce((arr, item) => {
        // 不是表单后动作过滤APL代码
        if (
          item.value === 'function'
          && this.name !== 'form_action'
          && this.name !== 'list_action'
        ) {
          return arr
        } if (
          item.value === 'shareToWeChatFriends'
          && this.name !== 'suspension'
        ) {
          return arr
        } if (this.exclude.indexOf(item.value) !== -1) {
          return arr
        }
        arr.push(item)
        return arr
      }, [])

      /**
       * 扩展动作
       */
      options = options.concat(this.extendActions)
      console.log(options,'options')
      return options
    },
    showCta(){
      //按钮图片组件显示cta设置
      return ["button", "image", "document"].indexOf(this.compType) !== -1
    }
  },
  watch: {
    value: {
      deeop: true,
      handler() {
        this.mergeAction()
      },
    },
  },
  mounted() {
    // fileDownload变更为fileDownloadV2，对之前的旧数据做个兼容
    // if (this.value && this.value.type === 'fileDownload') {
    //   this.setActionValue({
    //     type: 'fileDownloadV2',
    //   })
    // }
  },
  methods: {
    handleActionChange(value) {
      let actionName = ''
      if (value) {
        const actionItem = getActionByValue(value) || {}
        actionName = actionItem.label
      }
      this.action.type = value
      this.action.label = actionName
      this.action.id = ''
      this.action.url = ''
      this.handleActionContentChange()
    },
    mergeAction() {
      this.action = deepClone({
        ...deepClone(action),
        ...this.value,
      })

      console.log(this.action,'this.action')
    },
    setActionValue(val) {
      this.action = {
        ...this.action,
        ...val,
      }
      this.handleActionContentChange()
    },
    handleActionContentChange() {
      this.action = {
        ...this.action,
        extendParams: this.extendParams || {},
      }
      this.$emit('input', deepClone(this.action))
    },
  },
  created() {
    this.mergeAction()
  },
  render(h) {
    // slot scope
    const scope = {
      action: this.action,
      option: this.actionOption,
      setActionValue: this.setActionValue,
    }
    // 获取组件根节点slots
    const getParentScopedSlots = parent => {
      const compName = parent.$options.name
      if (compName && compName.toLocaleLowerCase() === 'hexagon') {
        return parent.$scopedSlots
      }
      return (parent.$parent && getParentScopedSlots(parent.$parent)) || null
    }

    const $rootScopedSlots = getParentScopedSlots(this.$parent)

    // action slot
    if ($rootScopedSlots && $rootScopedSlots.action) {
      return h('div', $rootScopedSlots.action(scope))
    }

    const actionTypeSlots = ($rootScopedSlots
        && $rootScopedSlots['action:type']
        && $rootScopedSlots['action:type'](scope))
      || null

    const $rootScopedSlotsContent = $rootScopedSlots
      && $rootScopedSlots['action:content']
      && $rootScopedSlots['action:content'](scope)

    const actionContentSlots = (!this.doNotRenderContentSlots
      && $rootScopedSlotsContent)
      || null
    // console.log('-------------------\n$rootScopedSlots:', $rootScopedSlots, '\n-------------------');
    return h(
      'div',
      {
        class: 'hexagon__action-wrap',
      },
      [
        actionTypeSlots || h('action-type', {
          props: {
            title: this.title,
            value: this.action.type,
            options: this.actionOption,
            disabled: this.disabled,
          },
          on: {
            input: val => {
              this.handleActionChange(val)
            },
          },
        }),
        actionContentSlots || h('action-content', {
          props: {
            value: this.action,
            compType: this.compType,
          },
          on: {
            input: actionItem => {
              this.action = actionItem
              this.handleActionContentChange()
            },
          },
        }),
        this.$slots['action:extendContent'],
        this.showCta && !!this.action.type && h('CTASetting', {
          props: {
            data: this.action.ctaConfig || {},
            sceneType: 'manual',
            desc: "",
            wrapStyle: {
              padding: 0,
              marginTop: '20px',
            }
          },
          on: {
            change: data => {
              this.action.ctaConfig = data;
              this.handleActionContentChange()
            },
          },
        }) || null,
      ],
    )
  },
}
