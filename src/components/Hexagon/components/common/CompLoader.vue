<template>
  <component
    :is="componentMaps[item.type]"
    v-if="componentMaps[item.type]"
    :data="item"
    :parent="parent"
    :isGrid="isGrid"
    :edit-type="editType"
    :pc-adaptation="pcAdaptation"
    :usageType="usageType"
  />
</template>
<script>
import { deepClone } from '../../utils/index.js'
// import { getParentInstanceByComponentName } from "../../utils/util";
import VContainer from '../comp/Container.vue'
import VGridContainer from '../comp/GridContainer.vue'
import VText from '../comp/Text.vue'
import VImage from '../comp/Image.vue'
import VVideo from '../comp/Video.vue'
import VSlider from '../comp/Slider.vue'
import VInput from '../comp/Input.vue'
import VDate from '../comp/Date.vue'
import VTime from '../comp/Time.vue'
import VLocation from '../comp/Location.vue'
import VRadio from '../comp/Radio.vue'
import VCheckbox from '../comp/Checkbox.vue'
import VButton from '../comp/Button.vue'
import VWechat from '../comp/Wechat.vue'
import VLine from '../comp/Line.vue'
import VRectangle from '../comp/Rectangle.vue'
import VBlank from '../comp/Blank.vue'
import VArticle from '../comp/Article.vue'
import VTel from '../comp/Tel.vue'
import VPaybutton from '../comp/Paybutton.vue'
import VProduct from '../comp/Product.vue'
import VRegion from '../comp/Region.vue'
import VQrcode from '../comp/Qrcode.vue'
import VAgreement from '../comp/Agreement.vue'
import VFile from '../comp/File.vue'
import VSuspension from '../comp/Suspension.vue'
import VPersonal from '../comp/Personal.vue'
import VMenu from '../comp/Menu.vue'
import VFollowWechat from '../comp/FollowWechat.vue'
import VEventsList from '../comp/EventsList.vue'
import VWechatVideo from '../comp/WechatVideo.vue'
import VVideoHomePage from '../comp/VideoHomePage.vue'
import VVideoLive from '../comp/VideoLive.vue'
import VContact from '../comp/Contact.vue'
import VDhtcoupon from '../comp/dht/DhtCoupon.vue'
import VDhtproduct from '../comp/dht/DhtProduct.vue'
import VDhtpromotionproduct from '../comp/dht/Dhtpromotionproduct.vue'
import VStepButton from '../comp/StepButton.vue'
import VImageCaptcha from "../comp/ImageCaptcha.vue";
import VDocument from '../comp/Document.vue'
import VFileupload from '../comp/Fileupload.vue'
import VMiniVipMemberInfo from '../comp/miniVip/MiniVipMemberInfo.vue'
import VMiniVipLevel from '../comp/miniVip/MiniVipLevel.vue'
import VMiniVipPointsDetails from '../comp/miniVip/MiniVipPointsDetails.vue'
import VMiniVipSwitchMemberShipPlan from '../comp/miniVip/MiniVipSwitchMemberShipPlan.vue'
import VMiniVipSwitchStores from '../comp/miniVip/MiniVipSwitchStores.vue'
import VMiniVipCoupon from '../comp/miniVip/MiniVipSwitchCoupon.vue'
import VAuthLogin from '../comp/miniVip/AuthLoginTransition.vue'
// 组件
const components = {
  VGridContainer,
  VContainer,
  VText,
  VImage,
  VVideo,
  VSlider,
  VInput,
  VDate,
  VTime,
  VLocation,
  VRadio,
  VCheckbox,
  VButton,
  VWechat,
  VLine,
  VRectangle,
  VBlank,
  VArticle,
  VTel,
  VPaybutton,
  VProduct,
  VRegion,
  VQrcode,
  VAgreement,
  VFile,
  VSuspension,
  VPersonal,
  VMenu,
  VFollowWechat,
  VEventsList,
  VWechatVideo,
  VVideoHomePage,
  VVideoLive,
  VContact,
  VDhtcoupon,
  VDhtproduct,
  VDhtpromotionproduct,
  VStepButton,
  VMiniVipMemberInfo,
  VMiniVipLevel,
  VMiniVipPointsDetails,
  VMiniVipSwitchMemberShipPlan,
  VMiniVipSwitchStores,
  VMiniVipCoupon,
  VDocument,
  VImageCaptcha,
  VFileupload,
  VAuthLogin,
}

// 组件maps
export const componentMaps = Object.keys(components).reduce(
  (map, key) => {
    map[key.toLocaleLowerCase().substr(1)] = key
    return map
  },
  {
    content: VEventsList,
    cascade: VRadio,
    livebutton: VButton,
    signupbutton: VButton,
  },
)

export default {
  components,
  props: {
    editType: {
      type: String,
      default: 'page',
    },
    data: {
      type: Object,
      default: () => ({}),
    },
    parent: {
      type: Object,
      default: () => ({}),
    },
    isGrid: {
      type: Boolean,
      default: false,
    },
    pcAdaptation: {
      type: Boolean,
      default: false,
    },
    usageType: {
      type: String,
      default: 'outside',
    },
  },
  data() {
    return {
      componentMaps,
    }
  },
  computed: {
    item() {
      const newItem = deepClone(this.data)
      // canvas 处理
      if (this.editType === 'canvas') {
        delete newItem.style.position
      } else {
        // if (this.pcAdaptation) {

        // PC端下字体不放大
        // if (newItem.type === "text") {
        //   if (newItem.value) {
        //     newItem.value = `<div style="zoom:${
        //       375 / pageRender.$props.width
        //     };text-aligb:center;">${newItem.value}</div>`;
        //   }
        // }
        // }
      }
      return newItem
    },
  },
}
</script>
