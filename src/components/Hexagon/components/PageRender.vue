<template>
  <div
    :class="[
      $style.PageRender,
      'hexagon__page_render',
      usageType === 'outside' ? $style.outside : '',
    ]"
    :style="{
      ...this.styles,
      ...contentStyle,
      width: `${width}px`,
    }"
  >
    <div
      v-if="headerOpts.isCustomMiniapp && showNavbar"
      :class="$style.navbar"
      :style="{
        position: 'absolute',
        pointerEvents: 'none',
        zIndex: 1000,
        background: headerOpts.isCustomMiniapp ? headerOpts.fpHeaderBackgroundColor : 'transparent',
      }"
    >
      <div :class="$style.status_bar">
        <svg :class="[$style.icon, fontColor === 1 ? $style.black : '']" width="349" height="12" viewBox="0 0 349 12" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M1.335 8.94C1.815 9.46 2.4 9.72 3.09 9.72C4.59 9.72 5.38 8.475 5.46 5.985C4.84 6.795 4.08 7.2 3.18 7.2C2.22 7.2 1.46 6.91 0.9 6.33C0.3 5.72 0 4.845 0 3.705C0 2.595 0.345 1.69 1.035 0.99C1.695 0.33 2.515 0 3.495 0C4.635 0 5.565 0.435 6.285 1.305C7.085 2.285 7.485 3.685 7.485 5.505C7.485 7.485 7.05 9.01 6.18 10.08C5.41 11.04 4.425 11.52 3.225 11.52C2.015 11.52 1 11.1 0.18 10.26L1.335 8.94ZM3.6 5.625C4.32 5.625 4.93 5.23 5.43 4.44C5.25 2.59 4.615 1.665 3.525 1.665C3.095 1.665 2.74 1.835 2.46 2.175C2.17 2.545 2.025 3.055 2.025 3.705C2.025 4.985 2.55 5.625 3.6 5.625Z" /><path d="M10.663 5.82C10.273 5.82 9.94801 5.685 9.68801 5.415C9.42801 5.145 9.29801 4.805 9.29801 4.395C9.29801 3.985 9.42801 3.64 9.68801 3.36C9.94801 3.09 10.273 2.955 10.663 2.955C11.063 2.955 11.393 3.09 11.653 3.36C11.913 3.64 12.043 3.985 12.043 4.395C12.043 4.805 11.913 5.145 11.653 5.415C11.393 5.685 11.063 5.82 10.663 5.82Z" /><path d="M9.68801 11.1C9.94801 11.38 10.273 11.52 10.663 11.52C11.063 11.52 11.393 11.38 11.653 11.1C11.913 10.83 12.043 10.49 12.043 10.08C12.043 9.67 11.913 9.33 11.653 9.06C11.393 8.78 11.063 8.64 10.663 8.64C10.273 8.64 9.94801 8.78 9.68801 9.06C9.42801 9.33 9.29801 9.67 9.29801 10.08C9.29801 10.49 9.42801 10.83 9.68801 11.1Z" /><path fill-rule="evenodd" clip-rule="evenodd" d="M21.5259 8.43H20.2059V11.31H18.1509V8.43H13.4259V6.93L17.5659 0.21H20.2059V6.75H21.5259V8.43ZM18.1509 6.75V4.485C18.1509 4.245 18.1809 3.48 18.2409 2.19H18.1809C17.8909 2.82 17.5959 3.42 17.2959 3.99L15.5709 6.75H18.1509Z" /><path d="M25.5589 9.525H23.1739V11.31H29.8489V9.525H27.7639V0.21H26.1289C25.4389 0.63 24.5789 0.94 23.5489 1.14V2.505H25.5589V9.525Z" /><path d="M311.067 2.59448C313.292 2.59457 315.431 3.44869 317.044 4.98028C317.165 5.09852 317.359 5.09703 317.479 4.97693L318.639 3.8066C318.7 3.74569 318.734 3.66318 318.733 3.57733C318.733 3.49148 318.698 3.40937 318.637 3.34917C314.405 -0.703058 307.729 -0.703058 303.497 3.34917C303.435 3.40933 303.401 3.49141 303.4 3.57726C303.399 3.66311 303.433 3.74565 303.494 3.8066L304.655 4.97693C304.774 5.09721 304.968 5.09871 305.09 4.98028C306.702 3.44859 308.842 2.59447 311.067 2.59448Z" /><path d="M314.374 7.67571C313.468 6.85594 312.289 6.40199 311.067 6.40206C309.846 6.40257 308.668 6.85648 307.762 7.67571C307.64 7.79206 307.447 7.78954 307.327 7.67003L306.168 6.4997C306.107 6.4384 306.073 6.35513 306.074 6.26857C306.075 6.18201 306.11 6.09939 306.173 6.03926C308.932 3.47485 313.205 3.47485 315.964 6.03926C316.026 6.09939 316.062 6.18196 316.063 6.2685C316.063 6.35504 316.03 6.43831 315.969 6.4997L314.809 7.67003C314.69 7.78954 314.497 7.79206 314.374 7.67571Z" /><path d="M313.389 8.9639C313.391 9.05068 313.357 9.13434 313.295 9.19514L311.29 11.2171C311.231 11.2766 311.151 11.31 311.067 11.31C310.983 11.31 310.903 11.2766 310.845 11.2171L308.839 9.19514C308.777 9.1343 308.743 9.0506 308.745 8.96383C308.747 8.87705 308.784 8.79487 308.849 8.7367C310.129 7.65443 312.005 7.65443 313.286 8.7367C313.35 8.79492 313.388 8.87712 313.389 8.9639Z" /><path d="M297.4 0.31H296.4C295.848 0.31 295.4 0.757715 295.4 1.31V9.97667C295.4 10.529 295.848 10.9767 296.4 10.9767H297.4C297.952 10.9767 298.4 10.529 298.4 9.97667V1.31C298.4 0.757715 297.952 0.31 297.4 0.31Z" /><path d="M292.733 2.64333H291.733C291.181 2.64333 290.733 3.09105 290.733 3.64333V9.97667C290.733 10.529 291.181 10.9767 291.733 10.9767H292.733C293.286 10.9767 293.733 10.529 293.733 9.97667V3.64333C293.733 3.09105 293.286 2.64333 292.733 2.64333Z" /><path d="M288.067 4.97667H287.067C286.514 4.97667 286.067 5.42438 286.067 5.97667V9.97667C286.067 10.529 286.514 10.9767 287.067 10.9767H288.067C288.619 10.9767 289.067 10.529 289.067 9.97667V5.97667C289.067 5.42438 288.619 4.97667 288.067 4.97667Z" /><path d="M282.4 6.97667H283.4C283.952 6.97667 284.4 7.42438 284.4 7.97667V9.97667C284.4 10.529 283.952 10.9767 283.4 10.9767H282.4C281.848 10.9767 281.4 10.529 281.4 9.97667V7.97667C281.4 7.42438 281.848 6.97667 282.4 6.97667Z" /><path d="M325.733 4.13334V7.2C325.733 7.94674 325.733 8.3201 325.879 8.60532C326.006 8.8562 326.21 9.06018 326.461 9.18801C326.746 9.33333 327.12 9.33333 327.867 9.33333H341.6C342.347 9.33333 342.72 9.33333 343.005 9.18801C343.256 9.06018 343.46 8.8562 343.588 8.60532C343.733 8.3201 343.733 7.94674 343.733 7.2V4.13333C343.733 3.3866 343.733 3.01323 343.588 2.72801C343.46 2.47713 343.256 2.27316 343.005 2.14532C342.72 2 342.347 2 341.6 2H327.867C327.12 2 326.746 2 326.461 2.14532C326.21 2.27316 326.006 2.47713 325.879 2.72801C325.733 3.01323 325.733 3.3866 325.733 4.13334Z" /><path fill-rule="evenodd" clip-rule="evenodd" d="M323.733 4.26666C323.733 2.77319 323.733 2.02646 324.024 1.45603C324.28 0.95426 324.688 0.546312 325.189 0.290649C325.76 0 326.506 0 328 0H341.467C342.96 0 343.707 0 344.277 0.290649C344.779 0.546312 345.187 0.95426 345.443 1.45603C345.733 2.02646 345.733 2.77319 345.733 4.26667V7.06667C345.733 8.56014 345.733 9.30688 345.443 9.87731C345.187 10.3791 344.779 10.787 344.277 11.0427C343.707 11.3333 342.96 11.3333 341.467 11.3333H328C326.506 11.3333 325.76 11.3333 325.189 11.0427C324.688 10.787 324.28 10.3791 324.024 9.87731C323.733 9.30688 323.733 8.56014 323.733 7.06667V4.26666ZM328 1C327.237 1 326.724 1.00078 326.33 1.03301C325.947 1.06429 325.764 1.12013 325.643 1.18166C325.33 1.34144 325.075 1.59641 324.915 1.91002C324.853 2.03076 324.798 2.21381 324.766 2.59664C324.734 2.99114 324.733 3.50343 324.733 4.26666V7.06667C324.733 7.8299 324.734 8.34219 324.766 8.7367C324.798 9.11953 324.853 9.30257 324.915 9.42332C325.075 9.73692 325.33 9.99189 325.643 10.1517C325.764 10.2132 325.947 10.269 326.33 10.3003C326.724 10.3326 327.237 10.3333 328 10.3333H341.467C342.23 10.3333 342.742 10.3326 343.137 10.3003C343.519 10.269 343.702 10.2132 343.823 10.1517C344.137 9.99189 344.392 9.73692 344.552 9.42332C344.613 9.30257 344.669 9.11953 344.7 8.7367C344.732 8.34219 344.733 7.82991 344.733 7.06667V4.26667C344.733 3.50343 344.732 2.99114 344.7 2.59664C344.669 2.21381 344.613 2.03076 344.552 1.91002C344.392 1.59641 344.137 1.34144 343.823 1.18166C343.702 1.12013 343.519 1.06429 343.137 1.03301C342.742 1.00078 342.23 1 341.467 1H328Z" /><path d="M346.733 3.66667C347.538 4.00544 348.061 4.79353 348.061 5.66667C348.061 6.5398 347.538 7.32789 346.733 7.66667V3.66667Z" />
        </svg>
      </div>
      <div
        :class="$style.title"
        :style="{ color: fontColor === 1 ? '#000' : '#fff' }"
      >
        {{ headerOpts.fpHideTitle ? "" : title }}
      </div>
    </div>
    <div :class="$style.hexagon__page_wrap" ref="pageContent">
      <div
        :class="[$style.page_content, 'hexagon__page']"
        :style="{
          minHeight: '600px',
          height:
            (scrollHeight === 'auto' && scrollHeight) || scrollHeight + 'px',
        }"
      >
        <slot name="page:before" :data="pageData.comps"></slot>
        <Draggable
          :class="['hexagon__draggable', $style.draggable]"
          v-model="pageData.comps"
          :disabled="disabled"
          :options="dragOptions"
          handle=".hexagon__page-comp-draghandle"
          @start="dragging = true"
          @end="dragging = false"
          :move="checkMove"
          @change="$emit('change', pageData.comps)"
        >
          <div
            v-for="(item, index) in pageData.comps"
            :key="item.id"
            :index="index"
            :id="item.id"
            :name="item.type"
            :type="item.type"
            :type-value="item.typeValue"
            :class="[
              $style.page_comp,
              'hexagon__page-comp',
              item.type !== 'suspension' && item.type !== 'contact' && item.type !== 'videolive' && !(item.type === 'video' && item.layoutType === 'icon-only')
                ? 'hexagon__page-comp-drag'
                : '',
            ]"
            :style="style(item)"
          >
            <VContainer
              v-if="item.type === 'container'"
              :data="item"
              :pcAdaptation="pcAdaptation"
              :parent="parent"
              :editType="'page'"
            />
            <VGridContainer
              v-else-if="item.type === 'gridcontainer'"
              :data="item"
              :pcAdaptation="pcAdaptation"
              :parent="parent"
              :usageType="usageType"
              :editType="'page'"
            />
            <VCompLoader
              v-else
              :data="item"
              :pcAdaptation="pcAdaptation"
              :editType="'page'"
              :parent="parent"
            />
            <slot :dragging="dragging" :data="item" :index="index" :parent="parent"></slot>
          </div>
        </Draggable>
        <slot name="page:after" :data="pageData.comps"></slot>
      </div>
      <CarouselPoint
        v-if="backgroundOpts.mode === 3 && backgroundOpts.carouselPointIsShow"
        :type="backgroundOpts.carouselPointType"
        :active-index="backgroundOpts.carouselPointActiveIndex"
        :count="backgroundOpts.carouselImgs.length"
        :deactivateColor="backgroundOpts.carouselPointColor"
        :activeColor="backgroundOpts.carouselPointActiveColor"
        :style="`bottom:${backgroundOpts.carouselPointBottom}px;left:${backgroundOpts.indicatorLeft}px`"
        :carouselPointSetting="backgroundOpts"
      />
      <Popup
        :show="popupOpts.isPreview"
        :coverUrl="popupOpts.coverUrl"
        :width="popupOpts.width"
        :borderRadius="popupOpts.borderRadius"
        :positionY="popupOpts.positionY"
      />
    </div>
  </div>
</template>
<script>
import { mapState } from 'vuex'

import Draggable from "vuedraggable";
import VContainer from "../components/comp/Container.vue";
import VGridContainer from "../components/comp/GridContainer.vue";
import VPageLoader from "../components/common/PageLoader.vue";
import VCompLoader from "../components/common/CompLoader.vue";
import statusbar from "../assets/statusbar3.svg";
import CarouselPoint from "./common/CarouselPoint.vue";
import { deepClone, peekStyles } from "../utils";
import Popup from './common/Popup.vue'

export default {
  name: "pagerender",
  components: {
    Draggable,
    VContainer,
    VGridContainer,
    VPageLoader,
    VCompLoader,
    CarouselPoint,
    Popup,
  },
  props: {
    title: {
      type: String,
      default: "",
    },
    data: {
      type: Array | Object,
      default: () => [],
    },
    parent: {
      type: Object,
      default: () => {},
    },
    disabled: {
      type: Boolean,
      default: true,
    },
    width: {
      type: Number,
      default: 375,
    },
    scrollHeight: {
      type: Number | String,
      default: 600,
    },
    contentStyle: {
      type: Object,
      default: () => ({}),
    },
    usageType: {
      type: String,
      default: "outside",
    },
    showNavbar: {
      type: Boolean,
      default: true,
    },
    pcAdaptation: {
      type: Boolean,
      default: false,
    },
    pageBackgroundOpts: {
      type: Object,
      default: () => ({}),
    },
    pagePopupOpts: {
      type: Object,
      default: () => ({}),
    },
    pageHeaderOpts: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      statusbar,
      pageData: {},
      dragging: false,
      styles: {},
      headerOpts: {},
      backgroundOpts: {},
      popupOpts: {},
    };
  },
  computed: {
    dragOptions() {
      return {
        animation: 200,
        scroll: document.querySelector(".el-tabs__content"),
        // draggable: ".hexagon__page-comp-draghandle",
        // filter: ".hexagon__page-comp-dragpoint"
      };
    },
    isFix() {
      return function (item) {
        if (
          item.type === "suspension" ||
          item.type === "contact" ||
          (item.type === "videolive" && item.layout === 1)
          || (item.type === 'video' && item.layoutType === 'icon-only')
        )
          return true;
        return false;
      };
    },
    fontColor() {
      if (this.headerOpts && Object.keys(this.headerOpts).length > 0) {
        return this.headerOpts.fpFontColor || 1
      }
      // 默认黑色
      return 1
    },
  },
  watch: {
    data() {
      this.initPageData();
    },
    pageHeaderOpts: {
      deep: true,
      handler: function (val) {
        this.headerOpts = val || {};
      },
    },
    pageBackgroundOpts: {
      deep: true,
      handler: function (val) {
        this.backgroundOpts = val || {};
      },
    },
    pagePopupOpts: {
      deep: true,
      handler: function (val) {
        this.popupOpts = deepClone(val || {})
      },
    }
  },
  created() {
    this.initPageData();
  },
  mounted() {
    this.headerOpts = this.pageHeaderOpts || {};
    this.backgroundOpts = this.pageBackgroundOpts || {};
    this.popupOpts = this.pagePopupOpts || {};
  },
  methods: {
    initPageData() {
      let comps = [],
        pageStyle = {};
      if (this.data instanceof Array) {
        comps = this.data;
      } else {
        const { components, style, headerOpts, backgroundOpts, popupOpts } = this.data;
        comps = components || [];
        pageStyle = style;
        this.headerOpts = headerOpts || {};
        this.backgroundOpts = backgroundOpts || {};
        this.popupOpts = popupOpts || {}
        this.styles = style;
      }
      //过滤空的项，解决模版错误导致的问题
      // comps = comps.filter(item => !!item);
      this.pageData = {
        comps,
        style: pageStyle,
      };
    },
    checkMove() {
      return true;
    },
    disableDrag({ wrapStyle = {} }) {
      return wrapStyle.position === "fixed";
    },
    style(componentData) {
      // if(wrapStyle.isVisual && wrapStyle.visual) {
      //   return peekStyles(
      //       {
      //         ...(wrapStyle || {}),
      //         position:
      //             (wrapStyle.position === "fixed" && "absolute") || wrapStyle.position,
      //         height:  wrapStyle.visualStyle.height
      //       },
      //       []
      //   );
      // }
      const {
        wrapStyle = {},
        type = "",
        typeValue,
        layout = '',
        layoutType = "",
        iconSize = 0,
      } = componentData;
      const newStyle = {
        ...(wrapStyle || {}),
        position:
          (wrapStyle.position === "fixed" && "absolute") || wrapStyle.position,
      };

      if(['button', 'stepbutton','signupbutton', 'tel'].includes(type)) {
        if(wrapStyle.position === "fixed") {
          newStyle.position = 'fixed';
          // if(this.pcAdaptation) {
          //   newStyle.paddingLeft = wrapStyle.paddingLeft * (this.width / 375)
          //   newStyle.paddingRight = wrapStyle.paddingRight * (this.width / 375)
          // }
        }
      }

      if (type === "video" && layoutType === "icon-only") {
        newStyle.width = iconSize;
        newStyle.height = iconSize;
        newStyle.position = "absolute";
      }

      if (type === "video" && layoutType !== "icon-only") {
        newStyle.top = 0;
        newStyle.left = 0;
      }
      // 悬浮按钮PC端适配时，位置转换
      if(this.pcAdaptation && ['suspension', 'contact'].includes(type)) {
        if(newStyle && newStyle.left && newStyle.left > (375 / 2)) {
          newStyle.right = 375 - newStyle.left - newStyle.width;
          delete newStyle.left;
        }
      }


      // 这里是为了解决视频号直播卡片布局会占满全屏的问题
      if(type === "videolive" && layout === 0) {
        return peekStyles({}, [])
      }

      return peekStyles(newStyle, []);
    },
  },
};
</script>
<style lang="less" module>
@import url("../assets/iconfont.less");
.PageRender {
  background-color: #fff;
  width: 100%;
  transform: translate(0, 0);
  // position: relative;
  :global {
    * {
      box-sizing: border-box;
    }
    ul,
    ol {
      padding: inherit;
      margin: inherit;
      padding-left: 16px;
    }
    ul {
      list-style: disc;
    }
    ol {
      list-style: decimal;
    }
    em {
      font-style: italic;
    }
  }
  .navbar {
    // background-color: #f2f3f5;
    .status_bar {
      // background-image: url(../assets/statusbar.jpg);
      height: 44px;
      background-size: 100% 100%;
      img {
        width: 100%;
      }
      .icon {
        width: 100%;
        height: 50px;
        padding: 12px;
        path {
          fill: #fff;
        }
      }
      .black {
        path {
          fill: #000;
        }
      }
      .white {
        path {
          fill: #fff;
        }
      }
    }
    .title {
      height: 35px;
      line-height: 35px;
      color: #181c25;
      font-size: 16px;
      text-align: center;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      padding: 0 20px;
    }
  }
  .hexagon__page_wrap {
    position: relative;
  }
  &.outside {
    .page_content {
      overflow: hidden;
      overflow-y: auto;
    }
  }

  .draggable{
    &::before{
      display: block;
      content: " ";
      height: 1px;
    }
  }
}
</style>
