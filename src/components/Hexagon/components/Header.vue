<template>
  <div :class="$style.header">
    <div :class="$style.logo"><slot name="title"><span :class="$style.title">Hexagon</span>{{ $t('marketing_pd.components.Header.sjq_4448ab') }}</slot></div>
    <div :class="$style.view">
      <div :class="$style.switchView">
          <span @click="switchView('mobile')" :class="[$style.switchViewItem, $style.switchViewMobile, viewportMode === 'mobile' ? $style.active : '']">
            <span class="fx-icon-shouji"></span>
          </span>
        <span @click="switchView('pc')" :class="[$style.switchViewItem, $style.switchViewPC, viewportMode === 'pc' ? $style.active : '']">
            <span class="fx-icon-diannao"></span>
          </span>
      </div>
    </div>
    <div :class="$style.content">
      <slot name="content"></slot>
    </div>
    <div :class="$style.right">
      <slot name="extend-right"></slot>
      <slot name="right">
        <Button size="small" type="primary" @click="_handleSave">{{ $t('marketing_pd.commons.bc_be5fbb') }}</Button>
        <Button size="small" type="primary" @click="_handleFinish">{{ $t('marketing_pd.components.Header.bcbtc_8ed8b0')
        }}</Button>
        <Button size="small" @click="$emit('close')">{{ $t('marketing_pd.commons.qx_625fb2') }}</Button>
      </slot>
    </div>
  </div>
</template>
<script>
import { mapActions, mapState } from 'vuex';
export default {
  components: {
    Button: FxUI.Button,
  },
  data() {
    return {
      dialogVisible: false,
      src: 'data:image/png;base64,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'
    }
  },
  computed: {
    ...mapState('hexagon/designer', {
      viewportMode: 'viewportMode',
      pageData: 'pageData',
    }),
  },
  methods: {
    ...mapActions('hexagon/designer', {
      // publish: 'publish',
      getPageData: 'getPageData',
      updatePageComp: 'updatePageComp',
      editPage: "editPage",
      setViewportMode: 'setViewportMode',
    }),
    async _handleSave() {
      this.dialogVisible = true
      const data = await this.getPageData();
      this.$emit('save', data);
    },
    switchView(view) {
      this.setViewportMode(view);
      if (!this.pageData.pcAdaptation && view === 'pc') {
        this.$message({
          offset: 80,
          message: $t('marketing.components.Hexagon.dqymspfswd_718f79'),
          type: 'warning',
        });
      }
    },
    async _handleFinish() {
      this.dialogVisible = true
      const data = await this.getPageData();
      this.$emit('finish', data);
    },
  },
}
</script>
<style lang="less" module>
.header {
  height: 54px;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  font-size: 13px;
  border-bottom: 1px solid #e9edf5;

  .view {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .content {
    width: 150px;
    display: flex;
    align-items: flex-end;
    padding-right: 20px;
  }

  .logo {
    font-size: 22px;

    .title {
      display: inline-block;
      margin-right: 5px;
    }
  }

  .right {
    :global {
      .el-button {
        font-size: 14px;
      }

      .el-icon--right {

      }
    }
  }

  .switchView {
    display: flex;

    .switchViewItem {
      cursor: pointer;
      height: 32px;
      width: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;

      &.switchViewMobile {
        border-radius: 4px 0 0 4px;
        border: 1px solid #C1C5CE;
        border-right: none;

        &.active {
          border: 1px solid var(--color-primary06, #ff8000);
        }
      }

      &.switchViewPC {
        border-radius: 0 4px 4px 0;
        border: 1px solid #C1C5CE;
        border-left: none;

        &.active {
          border: 1px solid var(--color-primary06, #ff8000);
        }
      }

      &.active {
        span {
          &:before {
            color: var(--color-primary06, #ff8000);
          }
        }
      }
    }
  }
}

.page_preview {
  display: flex;
  align-items: center;
  padding-bottom: 60px;
  padding-top: 20px;

  .block {
    flex: 1;
    text-align: center;
    padding: 0 20px;
    border-right: 1px solid #e9edf5;

    &:last-child {
      border-right: 0;
    }

    .nstration {
      display: block;
      margin-bottom: 20px !important;
    }
  }
}

.custom_dialog {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
</style>

