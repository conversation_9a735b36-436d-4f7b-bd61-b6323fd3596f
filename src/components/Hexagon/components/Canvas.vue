<template>
  <div :class="[$style.Canvas__wrap, 'hexagon__canvas_scroll', { [$style['Canvas__wrap--agent']]: contextProps.isAgent }]">
    <div :style="{...pageStyle, backgroundPosition: `0 ${backgroundPositionTop}`}">
      <div
        ref="canvas"
        :class="[$style.canvas, 'hexagon__canvas']"
        :style="styles"
      >
        <div :class="$style.canvas_tabs">
          <div
            v-for="(item, index) in canvasTabs"
            :key="index"
            :class="[
              $style.tab_item,
              (canvas.compData.slideIndex === index && $style.active) || ''
            ]"
            @click.stop="ev => handleChangeTabs(index, ev)"
          >
            <div :class="$style.bg" />
            <span>{{ item.name }}</span>
            <i
              v-if="
                canvas.comp.length > 1 &&
                  canvas.compData.typeValue !== 'tab-container'
                && canvas.compData.typeValue !== 'activity-comp'
              "
              :class="['el-icon-close', $style.tab_close]"
              @click.stop="ev => handleRemoveTabs(ev, index)"
            />
          </div>
          <div
            v-show="canvas.compData.typeValue !== 'tab-container' && canvas.compData.typeValue !== 'activity-comp'"
            :class="[$style.tab_item, $style.tab_add]"
            @click="handleNewTabs"
          >
            <i :class="['el-icon-plus', $style.addicon]" />
          </div>
        </div>
        <!-- 导航布局导航条 -->
        <ContainerTabs
          v-if="canvas.compData.typeValue === 'tab-container'"
          :data="canvas.compData"
          @tabClick="handleChangeTabs"
        />
        <AlignGuides v-if="isNotFlowContainer" />
        <template v-if="isNotFlowContainer">
          <div
            v-if="componentLength"
            ref="canvasWrap"
            v-selectable="{
              selectedGetter: selectedGetter,
              selectedSetter: selectedSetter,
              selectingSetter: selectingSetter
            }"
            :class="$style.canvas_selection"
            :style="containerStyle"
            data-items=".hexagon__canvas-comp"
            data-box=".hexagon__canvas-selection"
            data-exclude=".hexagon__canvas-comp"
          >
            <div :class="[$style.selection, 'hexagon__canvas-selection']" />
            <div
              v-for="(item, index) in components"
              :id="item.id"
              :key="item.id"
              :index="index"
              :class="[
                $style.canvas_comp,
                !isNotFlowContainer ? $style.canvas_flowcomp : '',
                'hexagon__canvas-comp',
                'hexagon__canvas-comp-event',
                (!!canvas.selected[index] && 'selected') || '',
                (!!selecting[index] && 'selecting') || ''
              ]"
              :style="{
                left: item.typeValue === 'marketingEventSignupbutton' ? (item.wrapStyle.bottom + 'px') : item.style.left + 'px',
                top: item.style.top + 'px',
                display: item.style.display || 'block',
                position: item.typeValue === 'marketingEventSignupbutton' && item.wrapStyle.position,
                bottom: item.typeValue === 'marketingEventSignupbutton' && (item.wrapStyle.bottom + 'px')
              }"
            >
              <VCompLoader
                :data="item"
                :edit-type="'canvas'"
                :parent="parent"
                :pc-adaptation="pcAdaptation"
              />
              <DeformationBox
                :data="item"
                :index="index"
              />
            </div>
          </div>
        </template>
        <div
          v-else
          :class="$style.adaptive_container"
          :style="containerStyle"
        >
          <AdaptiveContainer
            :show-navbar="false"
            :show-page-style="false"
            :components="components"
            :pc-adaptation="pcAdaptation"
            :parent="parent"
            :width="getGridOrDefaultWidth()"
            @change="handleComponentChange"
          />
        </div>
        <Drag
          v-if="isNotFlowContainer"
          :height="canvasRealHeight"
          :style="{ top: inputValue + 'px' }"
          @input="handleCanvasDragHeight"
          @change="value => handleChangeCanvasHeight(value, 'input')"
        />

        <div
          v-show="canvas.compData.isVisual && canvas.compData.visual"
          :class="[$style.visual]"
          :style="{ height: visualStyle.height + 'px' }"
        />
      </div>
      <!-- 图层 -->
      <Layer
          v-if="isNotFlowContainer"
          :comps="components"
      />
    </div>
    <!-- 撤销恢复按钮 -->
    <div
      v-if="isNotFlowContainer"
      :class="$style.undo_restore"
    >
      <div
        :class="[$style.undo, disableUndo ? $style.disabled : '']"
        @click.stop="undoHistoryRecord"
      >
        <i :class="[$style.icon, 'hexagon-icon', 'hicon-chexiao1']" />
        <p>{{ $t('marketing_pd.components.Canvas.cx_bd9fcf') }}</p>
      </div>
      <div
        :class="[$style.restore, disableRestore ? $style.disabled : '']"
        @click.stop="restoreHistoryRecord"
      >
        <i :class="[$style.icon, 'hexagon-icon', 'hicon-huifu']" />
        <p>{{ $t('marketing_pd.components.Canvas.hf_c7db6d') }}</p>
      </div>
    </div>
    <ContextMenu :components="components" />
  </div>
</template>
<script>
import { mapState, mapActions } from 'vuex'
import selectable, {
  setOptions,
  setSelectableItems,
} from '../utils/selectable.js'
import { deepClone, rootDispatch } from '../utils/index.js'
import VCompLoader from './common/CompLoader.vue'
import AdaptiveContainer from './AdaptiveContainer.vue'
import Layer from './common/Layer.vue'
import DeformationBox from './common/DeformationBox.vue'
import AlignGuides from './common/AlignGuides.vue'
import Drag from './common/Drag.vue'
import ContainerTabs from './common/ContainerTabs.vue'
import ContextMenu from './common/ContextMenu.vue'
import mixinUtil from '../mixin/mixinUtil.js'
import rulerIcon from '../assets/rule.png'
import rulerHoverIcon from '../assets/rule_hover.png'

export default {
  components: {
    VCompLoader,
    AdaptiveContainer,
    Layer,
    DeformationBox,
    AlignGuides,
    Drag,
    ContainerTabs,
    ContextMenu,
  },
  directives: { selectable },
  mixins: [mixinUtil],
  data() {
    return {
      ruler: false,
      vRules: [],
      hRules: [],
      rulerIcon,
      rulerHoverIcon,
      canvasHeight: 0,
      canvasRealHeight: 0,
      inputValue: 0,
      maxHeight: 10000,
      minHeight: 0,
      marks: {
        5000: '0',
        1500: '1500',
        0: '5000',
      },
      selecting: [],
      backgroundPositionTop: 0,
    }
  },
  props: {
    pcAdaptation: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    ...mapState('hexagon', {
      layoutTabValue: 'layoutTabValue',
      layoutTabs: 'layoutTabs',
      contextProps: 'contextProps',
    }),
    ...mapState('hexagon/designer', {
      pageMaps: 'pageMaps',
      pageData: 'pageData',
      viewportStyle: 'viewportStyle',
    }),
    ...mapState('hexagon/canvas', {
      copyCompTemp: 'copyCompTemp',
      canvasStyle: 'canvasStyle',
      canvasOffset: 'canvasOffset',
      historyRecord: 'historyRecord',
      pageTopOffsetMap: 'pageTopOffsetMap',
    }),
    canvas() {
      return this.$store.state.hexagon.canvas
    },
    visualStyle() {
      return this.$store.state.hexagon.canvas.compData.visualStyle || {}
    },
    components() {
      const { comp, compData } = this.canvas
      if (compData.layout === 'multiple') {
        return (
          (comp[compData.slideIndex]
            && deepClone(comp[compData.slideIndex].components))
          || []
        )
      }
      return comp
    },
    slideIndex() {
      const { compData } = this.canvas
      if (compData.layout === 'multiple') {
        return compData.slideIndex
      }

      return -1
    },
    parent() {
      const { comp, compData } = this.canvas;
      if(compData.layout === 'multiple') {
        return comp[compData.slideIndex] || {}
      }else {
        return compData
      }
    },
    componentLength() {
      return this.components.length
    },
    canvasTabs() {
      const { comp, compData } = this.canvas
      if (compData.layout === 'multiple') {
        // 导航布局显示导航
        if (compData.typeValue === 'tab-container') {
          return compData.tabs.map(item => ({
            name: `${item.label}`,
            value: item.value,
          }))
        }
        return comp.map((item, i) => ({
          name: `${item.name}${i + 1}`,
          value: i,
        }))
      }
      return [{ name: `${compData.name}1`, value: 0 }]
    },
    isNotFlowContainer() {
      const { comp, compData } = this.canvas
      let typesetting
      if (compData.layout === 'multiple') {
        typesetting = (comp[compData.slideIndex]
            && comp[compData.slideIndex].typesetting)
          || ''
      } else {
        typesetting = compData.typesetting
      }
      return typesetting !== 'flow'
    },
    pageStyle() {
      const { style = {} } = this.pageData
      const newStyle = { 
        ...style,
        width: this.getGridOrDefaultWidth() + 'px'
       }

      if (this.isNotFlowContainer) {
        newStyle.height = `${this.inputValue}px`
      }

      return newStyle
    },
    containerStyle() {

      return {}
    },
    styles() {
      const { canvasStyle } = this
      const newStyle = {}
      
      Object.keys(canvasStyle).forEach(key => {
        if (canvasStyle[key] === +canvasStyle[key]) {
          newStyle[key] = `${canvasStyle[key]}px`
        } else {
          newStyle[key] = canvasStyle[key]
        }
      })
      newStyle.width = this.getGridOrDefaultWidth() + 'px';
      
      newStyle.height = this.isNotFlowContainer
        ? `${this.canvasRealHeight}px`
        : 'auto'

      return newStyle
    },
    tooltipTop() {
      return `${(this.canvasRealHeight / this.maxHeight) * 100}%`
    },
    currentEditingContainerData() {
      const { compData } = this.canvas
      if (compData.layout === 'multiple') {
        return compData.components[compData.slideIndex] || {}
      }
      return compData || {}
    },
    disableUndo() {
      const { index } = this.historyRecord[this.currentEditingContainerData.id] || {}
      return index === 0
    },
    disableRestore() {
      const { index, list = [] } = this.historyRecord[this.currentEditingContainerData.id] || {}
      return index === list.length - 1
    },
  },
  watch: {
    'canvas.compData.id': {
      handler(val) {
        if (val) {
          // 需要计算偏移量差值，153是页面布局容器y轴定位信息
          this.backgroundPositionTop = `${-this.pageTopOffsetMap[val] + 153}px`
          let { compData } = this.$store.state.hexagon.canvas;
          if(compData.layout === 'multiple') {
            let curernt = compData.components[compData.slideIndex];
            if(curernt) {
              this.setPureCanvasStyle(curernt.style)
            }
          }
        }
      },
    },
    componentLength() {
      this.$nextTick(() => setSelectableItems(this.$refs.canvasWrap))
    },
    canvasStyle: {
      deep: true,
      handler() {
        this.calcCanvasStyle()
      },
      immediate: true,
    },
    canvasOffset() {
      this.calcCanvasStyle()
    },
    // slideIndex() {
    //   this.enableComponentsResetPosition = false
    //   this.$nextTick(() => {
    //     this.enableComponentsResetPosition = true
    //   })
    // },
    // components(cur, prev) {
    //   debugger;
    //   // 新增组件时候，组件自动往下堆叠
    //   if (this.enableComponentsResetPosition && cur.length - prev.length === 1) {
    //     const clonedCur = deepClone(cur)
    //     const targetComp = clonedCur.find(el => {
    //       if (prev.findIndex(item => item.id === el.id) > -1) {
    //         return false
    //       }

    //       return true
    //     })
    //     console.log('targetComp>>', targetComp)
    //     const resetPosition = () => {
    //       const marginTop = 12
    //       let initTop = 0
    //       const className = `#pane-${this.layoutTabValue} .${this.$style.canvas_selection} .${this.$style.canvas_comp}`
    //       const nodeList = [...document.querySelectorAll(className)]

    //       nodeList.forEach((el, index) => {
    //         const {
    //           fieldName, type, id, isFormComp,
    //         } = clonedCur[index]

    //         // 排除提交按钮和新增组件自己
    //         if ((['stepbutton', 'button'].includes(type) && fieldName === undefined && isFormComp) || id === targetComp.id) {
    //           return
    //         }

    //         const { top } = window.getComputedStyle(el)
    //         const { height } = el.getBoundingClientRect()
    //         const curTop = parseInt(top || 0, 10) + parseInt(height || 0, 10) + marginTop

    //         if (curTop > initTop) {
    //           initTop = curTop
    //         }
    //       })

    //       if (initTop) {
    //         targetComp.style.top = initTop
    //         this.setCanvasComp(clonedCur)
    //       }
    //     }

    //     // 文本组件有一个怪异的问题，调整完位置后，又被一个神奇的力量拉回到了顶部位置
    //     // 这里用高延迟来处理
    //     if (targetComp.type === 'text') {
    //       setTimeout(resetPosition, 200)
    //     } else {
    //       this.$nextTick(resetPosition)
    //     }
    //   }
    // },
  },
  mounted() {
    // if (this.calcOffsetTime) clearTimeout(this.calcOffsetTime)
    // this.calcOffsetTime = setTimeout(() => this.calculateOffset(), 500)
    setOptions(this.$refs.canvasWrap, {
      scrollingFrame: document.querySelector('.el-tabs__content'),
    })
    // this.enableComponentsResetPosition = true
  },
  methods: {
    ...mapActions('hexagon/canvas', [
      'setCanvasOffset',
      'addCanvasTabs',
      'changeCanvasTabs',
      'removeCanvasTabs',
      'setCanvasStyle',
      'setPureCanvasStyle',
      'setCanvasSelected',
      'undoHistoryRecord',
      'restoreHistoryRecord',
      'setCanvasComp',
    ]),
    selectedGetter() {
      return this.canvas.selected
    },
    selectedSetter(v) {
      this.setCanvasSelected(v)
    },
    selectingSetter(v) {
      this.selecting = v
    },
    handleCanvasDragHeight(value) {
      this.canvasRealHeight = value
    },
    calcCanvasStyle() {
      this.canvasHeight = this.maxHeight - this.canvasStyle.height
      this.canvasRealHeight = this.canvasStyle.height
      this.inputValue = this.canvasStyle.height
    },
    handleNewTabs() {
      this.addCanvasTabs()
    },
    handleRemoveTabs(ev, index) {
      ev.preventDefault()
      this.removeCanvasTabs(index)
    },
    handleChangeTabs(index, ev) {
      if (ev) ev.preventDefault()
      this.changeCanvasTabs(index)
    },
    handleFormatTooltip(value) {
      value = this.maxHeight - value
      return value > 0 ? value : '0'
    },
    handleChangeCanvasHeight(height, type) {
      height = height > this.maxHeight ? this.maxHeight : height;
      height = height < this.minHeight ? this.minHeight : height;

      //获取当前tab页，如果是在grid布局下的canvas。就从grid布局组件上找。
      let currentTab = this.layoutTabs.find((item)=>{
        return item.name === this.layoutTabValue
      });
      let canvas = {};
      if(currentTab && currentTab.parent) {
        if(currentTab.preParent) {
          let preParentNode = deepClone(this.pageMaps[currentTab.preParent.id]);
          let parentNode = null;
          preParentNode.components.forEach((item) => {
            if(item.id === currentTab.parent.id) {
              parentNode = item;
            }
          })
          if(parentNode) {
            parentNode.components.forEach((item) => {
              if(item.id === this.layoutTabValue) {
                canvas = item;
              }
            })
          }
        }else {
          let parentNode = deepClone(this.pageMaps[currentTab.parent.id]);
          parentNode.components.forEach((item) => {
            if(item.id === this.layoutTabValue) {
              canvas = item;
            }
          })
        }
        
      }else {
        canvas = deepClone(this.pageMaps[this.layoutTabValue] || {})
      }
      this.setCanvasStyle({
        ...canvas,
        style: {
          ...this.canvasStyle,
          height: type === 'input' ? +height : this.maxHeight - height,
        },
      })
    },
    handleChangeRealCanvasHeight(value) {
      this.canvasRealHeight = this.maxHeight - value
    },
    handleRuler() {
      this.ruler = !this.ruler
    },
    handleComponentChange(comps) {
      this.setCanvasComp(comps)
    },
    // calculateOffset() {
    //   if (!this.$refs.canvas) return
    //   this.getDesignerEditAreaOffset(this.$refs.canvas, ({ x, y }) => {
    //     this.setCanvasOffset({
    //       x,
    //       y,
    //     })
    //   })
    // },
    onBodyKeyDown(handler) {
      const _handler = ev => {
        ev = ev || window.event
        handler.call(ev.target, ev, ev.target)
      }
      document.body.addEventListener('keydown', _handler)

      return () => {
        document.body.removeEventListener('keydown', _handler)
      }
    },
    calcGridItemWidth(viewportWidth, gridGap, gridLayoutList, gridIndex, sum) {
      return Math.round((viewportWidth - gridGap * (gridLayoutList.length - 1)) * (gridIndex / sum)) + 'px';
    },
    getGridOrDefaultWidth() {
      let currentTab = this.layoutTabs.find(item => item.name === this.layoutTabValue);
      if (
        currentTab &&
        currentTab.parent &&
        currentTab.parent.type === 'gridcontainer' &&
        this.pcAdaptation
      ) {
        let index = -1;
        currentTab.parent.components.forEach((item, i) => {
          if (item.id === this.canvas.compData.id) index = i;
        });
        if (index === -1) return this.viewportStyle.width;

        // 健壮性处理
        let gridLayoutList = [];
        if (typeof currentTab.parent.gridLayout === 'string') {
          gridLayoutList = currentTab.parent.gridLayout
            .split(',')
            .map(item => parseInt(item, 10))
            .filter(num => !isNaN(num) && num > 0);
        }
        if (!gridLayoutList.length || index >= gridLayoutList.length) {
          return this.viewportStyle.width;
        }

        const gridGap = parseInt(currentTab.parent.gridGap, 10) || 0;
        const gridIndex = gridLayoutList[index];
        const sum = gridLayoutList.reduce((acc, cur) => acc + cur, 0);
        if (!sum || !gridIndex) return this.viewportStyle.width;

        // 返回数字
        return Math.round((this.viewportStyle.width - gridGap * (gridLayoutList.length - 1)) * (gridIndex / sum));
      } else {
        return this.viewportStyle.width;
      }
    },
  },
}
</script>
<style lang="less" module>
.Canvas__wrap {
  position: relative;
  flex: 1;
  display: flex;
  justify-content: center;
  padding: 0;
  -moz-user-select: none;
  -khtml-user-select: none;
  user-select: none;
  min-width: auto;
  min-height: auto;
  overflow: visible;
  position: relative;
  &--agent {
    min-height: calc(~"100% - 104px");
  }
  .visual {
    width: 100%;
    border: 1px dashed rgba(249, 249, 249, 1);
    position: absolute;
    top: 0;
    z-index: 1;
    background: rgba(0, 0, 0, 0.2);
  }
  .undo_restore {
    width: 50px;
    height: 120px;
    display: flex;
    flex-direction: column;
    position: fixed;
    right: 400px;
    top: 152px;
    background-color: #fff;
    box-shadow: 0px 2px 8px 0px rgba(233, 237, 245, 1);
    border: 1px solid rgba(233, 237, 245, 1);
    z-index: 1;
    > div {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #545861;
      font-size: 14px;
      cursor: pointer;
      &:hover {
        background-color: rgba(142, 180, 255, 0.2);
      }
      .icon {
        font-size: 18px;
        font-weight: bold;
        color: #6e7285;
      }
      &.disabled {
        cursor: not-allowed;
        background-color: #fff;
        opacity: 0.5;
      }
    }
  }
  .canvas_selection,
  .adaptive_container {
    height: 100%;
    width: 100%;
    position: relative;
  }
  // .adaptive_container {
  //   padding-top: 42px;
  // }
  .canvas {
    width: 100%;
    height: 300px;
    box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.08);
    // background: #fff;
    position: relative;
    overflow: unset !important;
    transform: translate(0, 0);
    .canvas_tabs {
      position: absolute;
      left: 100%;
      top: 0;
      color: #181c25;
      margin-left: 16px;
      box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.08);
      .tab_item {
        height: 30px;
        line-height: 30px;
        padding: 0 10px;
        background-color: #fff;
        white-space: nowrap;
        // box-shadow: 3px 0 5px rgba(0, 0, 0, 0.08) inset;
        cursor: pointer;
        position: relative;
        transition: color 0.5s ease;
        font-size: 12px;
        border-bottom: 1px solid #e9edf5;
        &:last-child {
          border-bottom: 0;
        }
        span {
          position: relative;
          z-index: 1;
        }
        .tab_close {
          color: #181c25;
          font-size: 16px;
          position: absolute;
          display: block;
          width: 30px;
          height: 30px;
          line-height: 30px;
          top: 0;
          right: -30px;
          text-align: center;
          transition: all 0.2s ease;
          opacity: 0;
          &:hover {
            color: var(--color-primary06,#407FFF);
            transform: rotate(-90deg) scale(1.2);
          }
        }
        &:hover {
          .tab_close {
            opacity: 1;
          }
        }
        &.tab_add {
          color: var(--color-primary06,#407FFF);
          font-size: 16px;
          text-align: center;
          .addicon {
            font-weight: bolder;
          }
        }
        // &:after{
        //   display: inline-block;
        //   content: " ";
        //   width: 0;
        //   overflow: hidden;
        //   background-color: #fff;
        //   position: absolute;
        //   top: 0;
        //   left: 0;
        //   bottom: 0;
        //   transition: width .5s ease;
        // }
        .bg {
          width: 0;
          overflow: hidden;
          background-color: var(--color-primary06,#ff8000);
          position: absolute;
          top: 0;
          left: 0;
          bottom: 0;
          transition: width 0.5s ease;
        }
        &.active {
          box-shadow: none;
          color: #fff;
          .bg {
            width: 100%;
          }
          // &:after{
          //   width: 100%;
          // }
        }
      }
    }
    .selection {
      position: absolute;
      border: 1px dotted var(--color-primary06,#ff8000);
      z-index: 9999;
      top: 0;
      left: 0;
      cursor: default;
      display: none;
    }
    .canvas_comp {
      position: absolute;
      &.canvas_flowcomp {
        position: relative;
      }
    }
    .vRules {
      position: absolute;
      width: 40px;
      left: -40px;
      top: 0;
      .rule_item {
        height: 10px;
        display: block;
        color: #909399;
        position: relative;
        font-size: 10px;
        text-align: right;
        padding-right: 10px;
        &:after {
          display: block;
          content: "";
          height: 0.5px;
          width: 6px;
          background-color: #b9b9b9;
          position: absolute;
          right: 0;
          top: 0;
        }
        &.l {
          &:after {
            width: 12px;
          }
        }
      }
    }
    .hRules {
      position: absolute;
      height: 40px;
      left: 0;
      top: -40px;
      display: flex;
      align-items: flex-end;
      .rule_item {
        width: 10px;
        display: inline-block;
        color: #909399;
        position: relative;
        font-size: 10px;
        text-align: right;
        padding-bottom: 10px;
        &:after {
          display: block;
          content: "";
          height: 6px;
          width: 1px;
          background-color: #b9b9b9;
          position: absolute;
          left: 0;
          bottom: 0;
        }
        &.l {
          &:after {
            height: 12px;
          }
        }
      }
    }
  }
  .canvas_operate {
    height: 280px;
    width: 40px;
    position: fixed;
    z-index: 999;
    top: 160px;
    left: calc(~"50vw - 245px");
    &.showrule {
      margin-left: -20px;
    }
    .ruler {
      margin-bottom: 10px;
      padding-left: 14px;
      img {
        height: 16px;
        display: block;
        cursor: pointer;
      }
      .icon_black {
        display: none;
      }
      &.show {
        .icon {
          display: none;
        }
        .icon_black {
          display: block;
        }
      }
      &:hover {
        .show;
      }
    }
    .canvas_height {
      height: 260px;
      position: relative;
      .tooltip_runway {
        height: 200px;
        position: absolute;
        top: 0;
        right: 30px;
      }
      .tooltip {
        position: absolute;
        font-size: 10px;
        color: #909399;
        top: 0;
        right: 0;
        text-align: right;
        transform: translate(0, -50%);
        background-color: #fafafa;
        // padding: 5px 15px;
        color: var(--color-info06,#407FFF);
        // box-shadow: 0 0 20px rgba(255,255,255,1);
      }
      .slider_showinput {
        :global {
          .el-input__inner {
            padding: 0;
            height: 20px;
            line-height: 20px;
            width: 45px;
            text-align: center;
            margin-top: 10px;
            margin-left: -22px;
          }
        }
      }
    }
    :global {
      .el-slider {
        .el-slider__stop {
          width: 4px;
          height: 4px;
        }
        &.is-vertical .el-slider__marks-text {
          font-size: 10px;
          left: auto;
          right: 10px;
        }
        .el-slider__input {
          width: 40px;
          margin-left: -12px;
          .el-input__inner {
            height: 20px;
            line-height: 20px;
            padding: 0 !important;
          }
        }
        .el-slider__button-wrapper {
          left: -16px;
        }
        .el-slider__runway,
        .el-slider__bar {
          width: 4px;
        }
        .el-slider__runway {
          background-color: var(--color-primary06,#ff8000);
        }
        .el-slider__bar {
          background-color: #e4e7ed;
        }
        .el-slider__button {
          width: 10px;
          height: 10px;
          border: solid 2px var(--color-primary06,#ff8000);
          background-color: #ffffff;
        }
      }
    }
  }
}
</style>
