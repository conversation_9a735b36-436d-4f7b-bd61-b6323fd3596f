<template>
  <div ref="viewport" :class="[$style.Viewport, 'hexagon__viewport', { [$style['Viewport--agent']]: contextProps.isAgent }]">
    <Tabs
      v-model="layoutTabValue"
      :class="[
        $style.Viewport__tabs,
        designMode === 'form-page' ? $style.hideTab : ''
      ]"
      type="border-card"
      @tab-remove="removeTab"
      @tab-click="handleTabClick"
    >
      <TabPane
        v-for="item in layoutTabs"
        :closable="item.name !== 'page'"
        :key="item.name"
        :label="
          item.name === 'page' ? designer.pageData.name || $t('marketing_pd.commons.ym_59ceff') : item.title
        "
        :name="item.name"
      >
        <div
          ref="viewportContent"
          :class="[$style.viewportContent, isDragging && $style.dragging, isContentDragging && $style.contentDragging]"
          @mousedown.self="handleMouseDown"
          @wheel="handleWheel"
        >
          <!-- 基础层 - 总是可拖动，完全透明 -->
          <div :class="$style.baseLayer" @mousedown="handleBaseLayerMouseDown"></div>

          <!-- 空格键按下时显示的遮罩层 -->
          <div v-if="isSpacePressed" :class="$style.dragOverlay" @mousedown="handleContentDragStart"></div>
          <Ruler
            :width="viewportWidth"
            :height="viewportHeight"
            :content-x="contentPositionX"
            :content-y="contentPositionY"
          />

          <div
            :class="$style.contentContainer"
            :style="contentContainerStyle"
            @mouseenter="handleMouseEnter"
            @mouseleave="handleMouseLeave"
           >
            <component
              v-if="item.name === layoutTabValue"
              :ref="'container'"
              v-bind:is="item.component"
              @mounted="onComponentMounted"
              :pc-adaptation="pcAdaptation && viewportMode === 'pc'"
            ></component>
          </div>
        </div>
      </TabPane>
    </Tabs>
  </div>
</template>

<script>
import { mapState, mapActions } from "vuex";
import Page from "./Page.vue";
import Canvas from "./Canvas.vue";
import Grid from './Grid.vue';
import Ruler from './common/Ruler.vue';

export default {
  components: {
    Tabs: FxUI.Tabs,
    TabPane: FxUI.TabPane,
    Page,
    Canvas,
    Grid,
    Ruler
  },
  data() {
    return {
      isDragging: false,
      lastMouseX: 0,
      lastMouseY: 0,
      scrollStartX: 0,
      scrollStartY: 0,
      contentWidth: 0,
      contentHeight: 0,
      viewportWidth: 0,   // 视口宽度
      viewportHeight: 0,  // 视口高度
      elementOffsetX: 0,  // 内容元素相对于视口的 X 偏移
      elementOffsetY: 0,  // 内容元素相对于视口的 Y 偏移
      isContentDragging: false, // 内容是否正在拖动
      contentPositionX: 20, // 内容容器X坐标
      contentPositionY: 20, // 内容容器Y坐标
      contentDragStartX: 0, // 内容拖动开始X位置
      contentDragStartY: 0, // 内容拖动开始Y位置
      activeViewportContent: null, // 当前活动的视口内容元素
      observers: {}, // 存储每个tab的observer
      eventListeners: {}, // 存储每个tab的事件监听器
    };
  },
  computed: {
    ...mapState("hexagon", {
      layoutTabs: "layoutTabs",
      designer: "designer",
      designMode: "designMode",
      contextProps: "contextProps",
    }),
    ...mapState("hexagon/designer", {
      isSpacePressed: "isSpacePressed"
    }),
    pcAdaptation() {
      return this.designer.pageData.pcAdaptation
    },

    viewportMode() {
      return this.designer.viewportMode
    },

    layoutTabValue: {
      get() {
        return this.$store.state.hexagon.layoutTabValue;
      },
      set(val) {
        this.setTabValue(val);
      }
    },
    // 内容容器位置样式
    contentContainerStyle() {
      return {
        left: `${this.contentPositionX}px`,
        top: `${this.contentPositionY}px`
      };
    }
  },
  watch: {
    isSpacePressed(val) {
      if (!val) {
        this.isContentDragging = false;
      }
    },
    layoutTabValue(name) {
      this.$nextTick(() => {
        this.tabChange(name);
        this.updateContentSize();
        this.updateViewportSize();
      });
    },
    'designer.viewportStyle': {
      handler() {
        this.$nextTick(() => {
          this.updateContentSize();
          this.updateViewportSize(false);
        });
      },
      deep: true
    }
  },
  methods: {
    ...mapActions("hexagon", ["setTabValue", "removeTab", "tabChange"]),
    ...mapActions("hexagon/designer", ["setIsMouseInPage"]),
    initPosition() {
      // 获取视窗宽度
      this.contentPositionY = 50;
      this.contentPositionX = (this.viewportWidth - this.contentWidth) / 2;
      if(this.contentPositionX < 0) {
        this.contentPositionX = 20;
        this.contentPositionY = 20;
      }
    },
    // 处理基础层的鼠标按下事件
    handleBaseLayerMouseDown(e) {
      // 如果空格键被按下，则拖动内容
      if (this.isSpacePressed) {
        this.handleContentDragStart(e);
      } else {
        // 否则使用新的拖动方法
        this.handleMouseDown(e);
      }
    },
    // 开始拖动内容区域
    handleContentDragStart(e) {
      if (this.isSpacePressed) {
        this.isContentDragging = true;
        this.contentDragStartX = e.clientX;
        this.contentDragStartY = e.clientY;
        document.addEventListener('mousemove', this.handleContentDragMove);
        document.addEventListener('mouseup', this.handleContentDragEnd);
        e.preventDefault();
      }
    },
    // 拖动内容区域
    handleContentDragMove(e) {
      if (this.isSpacePressed && this.isContentDragging) {
        const deltaX = e.clientX - this.contentDragStartX;
        const deltaY = e.clientY - this.contentDragStartY;

        this.contentPositionX += deltaX;
        this.contentPositionY += deltaY;

        this.contentDragStartX = e.clientX;
        this.contentDragStartY = e.clientY;
      }
    },
    // 结束拖动内容区域
    handleContentDragEnd() {
      this.isContentDragging = false;
      document.removeEventListener('mousemove', this.handleContentDragMove);
      document.removeEventListener('mouseup', this.handleContentDragEnd);
    },
    handleTabClick(vnode) {
      const oldTabName = this.layoutTabValue;
      const newTabName = this.layoutTabs[vnode.index].name;

      this.$nextTick(() => {
        // 清理旧tab的资源
        this.cleanupTabResources(oldTabName);

        // 设置新tab的资源
        this.setupTabResources(newTabName);

        // 获取新的活动容器并计算偏移
        const activeContainer = this.getActiveContainer();
        if (activeContainer) {
          activeContainer.calculateOffset && activeContainer.calculateOffset();
        }
        // 更新尺寸
        this.updateContentSize();
        this.updateViewportSize();
      });
    },
    handleMouseDown(e) {
      // 检查是否点击在可拖动组件上
      const isDraggableComponent = e.target.closest('[draggable="true"]') ||
                                 e.target.closest('.draggable') ||
                                 e.target.closest('.component-drag-handle');

      if (!isDraggableComponent) {
        this.isDragging = true;
        this.lastMouseX = e.clientX;
        this.lastMouseY = e.clientY;

        // 如果已经禁用了滚动条，我们应该直接移动内容位置而不是滚动
        this.contentDragStartX = e.clientX;
        this.contentDragStartY = e.clientY;

        document.addEventListener('mousemove', this.handleViewportDragMove);
        document.addEventListener('mouseup', this.handleViewportDragEnd);

        e.preventDefault();
      }
    },
    handleWheel(e) {
      // 防止默认行为
      e.preventDefault();

      // 使用 deltaY 来调整内容位置
      // 设置一个合适的滚动速度因子
      const scrollFactor = 0.5;

      // 根据滚轮方向移动内容
      this.contentPositionY -= e.deltaY * scrollFactor;
    },
    updateContentSize() {
      this.$nextTick(() => {
        const activeContainer = this.getActiveContainer();
        if (activeContainer) {
          const el = activeContainer.$el;
          this.contentWidth = el.offsetWidth;
          this.contentHeight = el.offsetHeight;
          this.elementOffsetX = 0;
          this.elementOffsetY = 0;
        }
      });
    },
    updateViewportSize(needInit = true) {
      this.$nextTick(() => {
        const activeViewportContent = this.getActiveViewportContent();
        if (activeViewportContent) {
          this.viewportWidth = activeViewportContent.clientWidth;
          this.viewportHeight = activeViewportContent.clientHeight;
          if(needInit) {
            this.initPosition();
          }
        }
      });
    },
    // 获取当前活动的容器
    getActiveContainer() {
      if (!this.$refs.container || !Array.isArray(this.$refs.container)) {
        return null;
      }

      // 获取当前活动tab的索引
      let activeTabIndex = -1;
      if(this.layoutTabs && this.layoutTabs.length > 0) {
        activeTabIndex = this.layoutTabs.findIndex(tab => tab.name === this.layoutTabValue);
      }
      if (activeTabIndex === -1) {
        return null;
      }

      return this.$refs.container[activeTabIndex];
    },
    // 获取当前活动的视口内容
    getActiveViewportContent() {
      if (!this.$refs.viewportContent || !Array.isArray(this.$refs.viewportContent)) {
        return null;
      }
      let activeTabIndex = -1;
      if(this.layoutTabs && this.layoutTabs.length > 0) {
        activeTabIndex = this.layoutTabs.findIndex(tab => tab.name === this.layoutTabValue);
      }
      if (activeTabIndex === -1) {
        return null;
      }
      return activeTabIndex !== -1 ? this.$refs.viewportContent[activeTabIndex] : null;
    },

    // 清理特定tab的观察器和事件监听器
    cleanupTabResources(tabName) {
      // 清理 ResizeObserver
      if (this.observers[tabName]) {
        this.observers[tabName].disconnect();
        delete this.observers[tabName];
      }

      // 清理事件监听器
      if (this.eventListeners[tabName]) {
        const { scroll } = this.eventListeners[tabName];
        if (scroll && scroll.element) {
          scroll.element.removeEventListener('scroll', scroll.handler);
        }
        delete this.eventListeners[tabName];
      }
    },

    // 为特定tab设置观察器和事件监听器
    setupTabResources(tabName) {
      const activeContainer = this.getActiveContainer();
      const activeViewportContent = this.getActiveViewportContent();

      if (!activeContainer || !activeViewportContent) return;

      // 创建新的 ResizeObserver
      this.observers[tabName] = new ResizeObserver(() => {
        this.updateContentSize();
        this.updateViewportSize(false);
      });

      // 观察容器元素
      if (activeContainer.$el) {
        this.observers[tabName].observe(activeContainer.$el);
      }

      // 观察视口内容元素
      this.observers[tabName].observe(activeViewportContent);

      // 设置滚动事件监听器
      this.eventListeners[tabName] = {
        scroll: {
          element: activeViewportContent,
          handler: this.updateContentSize
        }
      };
      activeViewportContent.addEventListener('scroll', this.eventListeners[tabName].scroll.handler);
    },

    // 新增方法：视口拖动移动
    handleViewportDragMove(e) {
      if (!this.isDragging) return;

      const deltaX = e.clientX - this.contentDragStartX;
      const deltaY = e.clientY - this.contentDragStartY;

      this.contentPositionX += deltaX;
      this.contentPositionY += deltaY;

      this.contentDragStartX = e.clientX;
      this.contentDragStartY = e.clientY;
    },
    // 新增方法：视口拖动结束
    handleViewportDragEnd() {
      this.isDragging = false;
      document.removeEventListener('mousemove', this.handleViewportDragMove);
      document.removeEventListener('mouseup', this.handleViewportDragEnd);
    },
    onComponentMounted() {
      this.$nextTick(() => {
        // 设置当前tab的资源
        this.setupTabResources(this.layoutTabValue);
        this.updateContentSize();
        this.updateViewportSize(false);
      });
    },
    handleMouseEnter() {
      this.setIsMouseInPage(true);
    },
    handleMouseLeave() {
      this.setIsMouseInPage(false);
    }
  },
  mounted() {
    this.$nextTick(() => {
      // 初始化当前tab的资源
      this.setupTabResources(this.layoutTabValue);
      this.updateContentSize();
      this.updateViewportSize();

      // 监听视口大小变化
      window.addEventListener('resize', this.updateViewportSize);

      // 给容器添加焦点
      if (this.$refs.viewport) {
        this.$refs.viewport.focus();
      }

    });
  },
  beforeDestroy() {
    // 清理所有tab的资源
    if(this.layoutTabs && this.layoutTabs.length > 0) {
      this.layoutTabs.forEach(tab => {
        this.cleanupTabResources(tab.name);
      });
    }

    // 移除全局事件监听器
    window.removeEventListener('resize', this.updateViewportSize);
  },
};
</script>

<style lang="less" module>
.Viewport {
  height: calc(~"100vh - 54px");
  flex: 1;
  min-width: 400px;
  overflow: hidden;
  &--agent {
    height: 100%;
    :global {
    .el-tabs__content {
      height: 100%!important;
    }
  }
  }
  .Viewport__tabs {
    height: 100%;
    &.hideTab {
      :global {
        .el-tabs__nav-wrap {
          display: none;
        }
      }
    }

    .viewportContent {
      width: 100%;
      height: 100%;
      overflow: hidden;
      padding: 0;
      cursor: default;
      position: relative;
      background-color: #f5f5f5; /* 浅灰色背景 */
      /* 移除旧的棋盘格背景 */
      /* background-image: repeating-conic-gradient(#fff 0% 25%, #f0f0f0 25% 50%); */
      /* background-size: 40px 40px; */

      /* 添加点阵背景 */
      background-image: radial-gradient(#d0d0d0 1px, transparent 1px);
      background-size: 20px 20px;

      &.dragging {
        cursor: grabbing;
        user-select: none;
      }

      &.contentDragging {
        cursor: grabbing;
        user-select: none;
      }
    }
  }
  :global {
    .el-tabs--border-card {
      border: 0;
      background-color: #fafafa;
      .el-tabs__header {
        background: #f2f2f2 !important;
        border-color: #e9edf5;
        height: 49px;
        .el-tabs__item {
          height: 50px;
          line-height: 50px;
          font-size: 13px;
          max-width: 130px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          color: #91959e;
          &.is-active {
            background-color: #fafafa;
            color: #181c25;
          }
        }
      }
    }
    .el-tabs__content {
      position: unset;
      padding: 0 !important;
      height: calc(~"100vh - 104px");
      overflow-y: auto !important;
      .el-tab-pane {
        height: 100%;
      }
    }
  }
  .contextmenu {
    width: 110px;
    padding: 10px 0;
    position: fixed;
    top: 150px;
    left: 50%;
    z-index: 99999;
    background-color: #fff;
    border: 1px solid #e9edf5;
    color: #181c25;
    font-size: 14px;
    box-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.08);
    .optionmenu {
      line-height: 30px;
      padding: 0 15px;
      cursor: pointer;
      &.disable {
        color: #999;
        cursor: default;
        &:hover {
          background: none;
        }
      }
      &:hover {
        background-color: #f2f2f2;
      }
    }
  }
}

// 基础层 - 位于最底层，用于处理全局拖动事件
.baseLayer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  cursor: move; // 添加光标样式，提示用户可拖动
}

// 遮罩层样式 - 完全透明
.dragOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  z-index: 10;
  cursor: grabbing;
}

.contentContainer {
  position: absolute;
  display: block;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  z-index: 1;
}

.rulerContainer {
  position: relative;
  display: block;
  margin: 0;
  padding: 0;
  width: max-content;
  max-width: 100%;
  box-sizing: border-box;
}

.rulerWrapper {
  position: relative;
  display: inline-block;
  margin: 0;
  padding: 0;
  min-width: 100%;
  box-sizing: border-box;
}
</style>
