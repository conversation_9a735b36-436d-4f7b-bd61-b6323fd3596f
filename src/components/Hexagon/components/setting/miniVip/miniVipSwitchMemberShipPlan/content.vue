<template>
  <div class="member-info-wrapper">
    <Card :title="$t('SFA.pagedesigner.main.showformcontent')">
      <div class="tips-content">
        {{ tipsText }}
      </div>
      <div :class="['setting_item', 'hexagon__setting_item', 'hexagon__setting_row']" v-for="item in compOptions"
        :key='item.label' v-show="computeShow(item.comp)">
        <div :class="['title', 'hexagon__setting_title']">
          <span>
            {{ item.label }}
          </span>
          <span v-show="item.tips" class="fx-icon-question f-title-help crm-ui-title" :data-title="item.tips" data-pos="bottom"></span>
        </div>
        <div :class="['con', 'hexagon__setting_con']">
          <div :class="['con', 'hexagon__setting_con']">
            <template v-if="item.comp === 'showIcon'">
              <fx-switch v-model="settingData.isShowIcon" size="mini"> </fx-switch>
            </template>
            <template v-else-if="item.comp === 'IconPicker'">
              <IconPicker v-bind="$attrs" :value="settingData.iconInfo || {}" :icon-layout="'line'"
                @input="handleIconInput" />
            </template>
            <template v-else-if="item.comp === 'showWhatPlan'">
              <fx-radio-group v-model="settingData.showWhatPlan">
                <fx-radio :label="1" size="mini">{{ $t('SFA.pagedesigner.record.plan.allplans')
                  }}</fx-radio>
                <fx-radio :label="2" size="mini">{{ $t('SFA.pagedesigner.record.plan.myplans')
                  }}</fx-radio>
              </fx-radio-group>
            </template>
            <template v-else-if="item.comp === 'autoShowPlan'">
              <fx-switch v-model="settingData.autoShowPlan" size="mini"></fx-switch>
            </template>
          </div>
        </div>
      </div>
    </Card>
  </div>
</template>

<script>
import Content from '../../components/Content.vue'
import Card from '../../../common/Card.vue'
import IconPicker from '../../../common/IconPicker.vue'

export default {
  components: {
    Card,
    IconPicker,
  },
  extends: Content,
  props: {},
  data() {
    return {
      tipsText: $t('SFA.pagedesigner.form.switch.plan_and_store_show_rule_tips'),
      compOptions: [
        {
          label: $t('SFA.pagedesigner.form.member.showicon'),
          comp: 'showIcon',
        },
        {
          label: $t('SFA.pagedesigner.form.member.seticon'),
          comp: 'IconPicker',
        },
        {
          label: $t('SFA.pagedesigner.form.switch.showplan'),
          comp: 'showWhatPlan',
        },
        {
          label: $t('SFA.pagedesigner.form.switch.autoshowplan'),
          comp: 'autoShowPlan',
          tips: $t('SFA.pagedesigner.form.switch.autoshowplan.tips'),
        },
      ],
      settingData: {
        iconInfo: {
        },
        // 显示图标开关设置
        isShowIcon: false,
        autoShowPlan: false,
        showWhatPlan: 1,
      },
    }
  },
  computed: {
    canshowIcon() {
      return this.settingData.isShowIcon
    },
  },
  watch: {
    settingData: {
      handler(cur) {
        if (cur) {
          this.setting.settingData = cur
          this.handleValueChange()
        }
      },
      deep: true,
      immediate: true,
    },
  },
  created() {
    if (this.data.settingData) {
      this.settingData = JSON.parse(JSON.stringify(this.data.settingData))
    }
  },
  mounted() {

  },
  methods: {
    computeShow(comp) {
      if (comp === 'IconPicker') {
        return this.canshowIcon
      }
      return true
    },
    handleIconInput(icon) {
      this.settingData.iconInfo = icon
    },
  },
}
</script>

<style scoped lang="less">
.member-info-wrapper {
  .tips-content {
    margin-bottom: 12px;
    color: #91959E;
  }
}
</style>
