<template>
  <div class="member-info-wrapper">
    <fx-tabs v-model="tabsOptionsActive">
      <fx-tab-pane :label="item.label" :name="item.id" v-for="item in tabsOptions" :key="item.id">
        <Card :title="item.label">
          <div :class="['setting_item', 'hexagon__setting_item', 'hexagon__setting_row']" v-for="item in item.comps"
            v-show="computeShow(item.comp)" :key='item.label'>
            <div :class="['title', 'hexagon__setting_title']">
              {{ item.label }}
            </div>
            <div :class="['con', 'hexagon__setting_con']">
              <div :class="['con', 'hexagon__setting_con']">
                <template v-if="item.comp === 'container'">
                  <Background v-bind="$attrs" v-model="settingData[tabsOptionsActive].container" v-on="$listeners"
                    @input="handleContainerChange" />
                </template>
                <template v-else-if="item.comp === 'switchText'">
                  <fx-input size="mini" :maxlength="16" show-word-limit v-model="settingData[tabsOptionsActive].switchText"></fx-input>
                </template>
                <template v-else-if="item.comp === 'LevelText'">
                  <fx-input size="mini" v-model="settingData[tabsOptionsActive].LevelText"></fx-input>
                </template>
                <template v-else-if="item.comp === 'openText'">
                  <fx-input size="mini" v-model="settingData[tabsOptionsActive].openText"></fx-input>
                </template>
                <template v-else-if="item.comp === 'IconPicker'">
                  <IconPicker :value="setting.iconInfo || {}" :upload="upload"
                              :icon-layout="setting.iconLayoutType || 'line'" @input="handleIconInput" />
                </template>
                <template v-else-if="item.comp === 'showOtherPlan'">
                  <fx-switch v-model="settingData[tabsOptionsActive].showOtherPlan" size="mini"/>
                </template>
                <template v-else-if="item.comp === 'clickAction'">
                  <Action
                    v-model="settingData[tabsOptionsActive].clickAction"
                    :title="$t('marketing_pd.commons.djsj_6ad26b')" name="list_action"
                    @input="handleDataSourceActionChange" />
                </template>
                <template v-else-if="item.comp === 'otherPlanType'">
                  <fx-select
                    v-model="settingData[tabsOptionsActive].otherPlanType"
                    :options="rangeOptions"
                    size="mini"/>
                </template>
              </div>
            </div>
          </div>
        </Card>
      </fx-tab-pane>
    </fx-tabs>
  </div>
</template>

<script>
// extend里面有关键变量与方法直接使用 setting的数据由comp与setting通过vuex共用
/* <Title
  :setting="setting"
  :title-visible="false"
  @change="handleValueChange"
/> */
import Content from '../../components/Content.vue'
import Card from '../../../common/Card.vue'
import Background from '../../../common/Background.vue'
import IconPicker from '../../../common/IconPicker.vue'
import Action from '../../../common/action/index.js'

export default {
  components: {
    Card,
    Background,
    IconPicker,
    Action,
  },
  extends: Content,
  props: {
  },
  data() {
    return {
      settingData: {
      },
      rangeOptions: [
        { label: $t('SFA.pagedesigner.form.level.all'), value: 'all' },
        { label: $t('SFA.pagedesigner.form.level.join'), value: 'join' },
      ],
      tabsOptionsActive: 'join',
      tabsOptions: [
        {
          label: $t('SFA.pagedesigner.form.level.hasjoinshow'),
          id: 'join',
          comps: [
            {
              label: $t('SFA.pagedesigner.form.level.showhasother'),
              comp: 'showOtherPlan',
            },
            {
              label: $t('SFA.pagedesigner.form.level.other_plan_type'),
              comp: 'otherPlanType',
            },
            {
              label: $t('SFA.pagedesigner.form.level.switchplantext'),
              comp: 'switchText',
            },
            {
              label: $t('SFA.pagedesigner.form.level.backgroundshow'),
              comp: 'container',
            },
            {
              label: $t('SFA.pagedesigner.form.level.pointprocessname'),
              comp: 'LevelText',
            },
            {
              label: $t('SFA.pagedesigner.form.level.cardactionclick'),
              comp: 'clickAction',
            },
          ],
        },
        {
          label: $t('SFA.pagedesigner.form.level.nojoinshow'),
          id: 'nojoin',
          comps: [
            {
              label: $t('SFA.pagedesigner.form.level.showhasother'),
              comp: 'showOtherPlan',
            },
            {
              label: $t('SFA.pagedesigner.form.level.other_plan_type'),
              comp: 'otherPlanType',
            },
            {
              label: $t('SFA.pagedesigner.form.level.switchplantext'),
              comp: 'switchText',
            },
            {
              label: $t('SFA.pagedesigner.form.level.backgroundshow'),
              comp: 'container',
            },
            {
              label: $t('SFA.pagedesigner.form.level.opentext'),
              comp: 'openText',
            },
          ],
        },
      ],

    }
  },
  computed: {
    hasOpenPlan() {
      return this.settingData[this.tabsOptionsActive].showOtherPlan
    },
  },
  watch: {
    settingData: {
      handler(cur) {
        this.setting.settingData = cur
        this.handleValueChange()
      },
      deep: true,
    },
    tabsOptionsActive: {
      handler(cur) {
        this.settingData.defaultShow = cur
      },
      deep: true,
    },
  },
  created() {
    if (this.data.settingData) {
      this.settingData = JSON.parse(JSON.stringify(this.data.settingData))
    }
  },
  mounted() {

  },
  methods: {
    computeShow(comp) {
      if (comp === 'otherPlanType') {
        return this.settingData[this.tabsOptionsActive].showOtherPlan
      }
      return true
    },
    handleDataSourceActionChange(action) {
      if (action.type !== 'function') {
        action.functionApiName = ''
        action.functionName = ''
      }
      this.setting.dataSourceAction = action
      this.handleValueChange()
    },
    handleContainerChange(val) {
      Object.assign(this.setting, val)
      this.handleValueChange()
    },
  },
}
</script>

<style scoped lang="less">
.member-info-wrapper {
  & /deep/ .el-tabs__nav-scroll {
    display: flex;
    justify-content: center;
  }
}
</style>
