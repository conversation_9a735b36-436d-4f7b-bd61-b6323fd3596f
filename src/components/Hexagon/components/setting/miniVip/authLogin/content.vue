<template>
  <div class="member-info-wrapper">
    <Card :title="$t('SFA.pagedesigner.main.showformcontent')">
      <template v-for="item in compOptions">
        <div
          v-show="computeShow(item.comp)"
          :key="item.label"
          :class="[
            'setting_item',
            'hexagon__setting_item',
            'hexagon__setting_row',
          ]"
        >
          <div :class="['title', 'hexagon__setting_title']">
            {{ item.label }}
            <span
              v-show="item.tips"
              class="fx-icon-question f-title-help crm-ui-title"
              :data-title="item.tips"
              data-pos="bottom"
            />
          </div>
          <div :class="['con', 'hexagon__setting_con']">
            <template v-if="item.comp === 'loginType'">
              <fx-select
                v-model="settingData.loginType"
                :options="loginTypeOptions"
                size="mini"
              />
            </template>
            <template v-else-if="item.comp === 'h5Url'">
              <div class="h5-url-wrapper">
                <fx-input
                  v-model="settingData.h5Url"
                  type="textarea"
                  size="small"
                  :rows="5"
                  :placeholder="$t('SFA.pagedesigner.common.enterH5Url')"
                />
                <div class="h5-url-tips">
                  {{ tips }}
                </div>
              </div>
            </template>
          </div>
        </div>
      </template>
    </Card>
  </div>
</template>

<script>
import Content from '../../components/Content.vue'
import Card from '../../../common/Card.vue'

const tips = $t(
  'SFA.pagedesigner.common.loginTypeTips',
  {
    url: 'https://crm.ceshi112.com/hlcrm/avah5?upstreamEa=EA&appId=FSAID_xxxxx&apiName=XxxxObj&fsAppId=FSAID_xxxxx#/object_list/path/path',
  },
)

export default {
  components: {
    Card,
  },
  extends: Content,
  props: {},
  data() {
    return {
      tips,
      loginTypeOptions: [
        {
          label: $t('SFA.pagedesigner.common.interconnected'),
          value: 'interconnected',
        },
        // {
        //   label: $t('SFA.pagedesigner.common.sfaLogin'),
        //   value: 'sfaLogin',
        // },
      ],
      compOptions: [
        {
          label: $t('SFA.pagedesigner.common.loginType'),
          comp: 'loginType',
        },
        {
          label: $t('SFA.pagedesigner.common.enterH5Url'),
          comp: 'h5Url',
          // tips,
        },
      ],
      settingData: {
        loginType: 'interconnected',
        h5Url: '',
      },
    }
  },
  watch: {
    settingData: {
      handler(cur) {
        if (cur) {
          this.setting.settingData = cur
          this.handleValueChange()
        }
      },
      deep: true,
    },
  },
  created() {
    if (this.data.settingData) {
      this.settingData = JSON.parse(JSON.stringify(this.data.settingData))
    }
  },
  methods: {
    handleContainerChange(val) {
      Object.assign(this.setting, val)
      this.handleValueChange()
    },
    computeShow() {
      return true
    },
  },
}
</script>

<style scoped lang="less">
.member-info-wrapper {
  padding: 0 12px;
  & /deep/ .el-textarea__inner {
    border: 1px solid #dcdfe6;
  }
  .h5-url-tips {
    color: #999;
    font-size: 12px;
    margin-top:12px;
    word-break: break-all;
  }
}
</style>
