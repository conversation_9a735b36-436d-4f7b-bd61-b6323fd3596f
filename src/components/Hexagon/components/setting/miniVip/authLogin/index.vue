<script>
import Setting from '../../Setting.vue'
import Content from './content.vue'

export default {
  components: {
    Content,
  },
  extends: Setting,
  mounted() {
    /** 兼容写法
     * 等全部组件都换成新样式后，可以移除
    */
    const dom = $('.hexagon__settting_wrap .hexagon__setting_body')
    dom.addClass('reset')
    /** 兼容写法 */
  },
  destroyed() {
    /** 兼容写法
     * 等全部组件都换成新样式后，可以移除
    */
    const dom = $('.hexagon__settting_wrap .hexagon__setting_body')
    dom.removeClass('reset')
    /** 兼容写法 */
  },
}
</script>
