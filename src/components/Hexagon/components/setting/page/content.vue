<template>
  <div :class="$style.content">
    <Card
      :class="$style.card"
      :title="$t('marketing.components.Hexagon.bj_5aefca')"
    >
      <div :class="[$style.setting_item, 'hexagon__setting_item']">
        <div :class="[$style.title, 'hexagon__setting_title']">{{ $t('marketing.components.Hexagon.dspfs_091084')}}</div>
        <div :class="[$style.con, 'hexagon__setting_con']">
          <div :class="$style.adaptionSelector">
            <div
              :class="[$style.adaptionOption, !setting.pcAdaptation && $style.selected]"
              @click="handleAdaptionModeChange('stretch')"
            >
              <div :class="$style.adaptionIcon">
                <i :class="[$style['icon-stretch']]"></i>
              </div>
              <div :class="$style.adaptionTitle">{{ $t('marketing.components.Hexagon.dbls_651280') }}</div>
              <div :class="$style.adaptionDesc">{{ $t('marketing.components.Hexagon.syyzyzyddt_9a049a')}}</div>
            </div>
            <div
              :class="[$style.adaptionOption, setting.pcAdaptation && $style.selected]"
              @click="handleAdaptionModeChange('responsive')"
            >
              <div :class="$style.adaptionIcon">
                <i :class="[$style['icon-responsive']]"></i>
              </div>
              <div :class="$style.adaptionTitle">{{ $t('marketing.components.Hexagon.xys_3179f9') }}</div>
              <div :class="$style.adaptionDesc">{{ $t('marketing.components.Hexagon.syyxyzdtgd_5fca9a')}}</div>
            </div>
          </div>
        </div>
      </div>
      <template v-if="setting.pcAdaptation">
        <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
          <div :class="[$style.title, 'hexagon__setting_title']">
            {{ $t('marketing.components.Hexagon.zdnrkdsz_009167') }}
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']">
            <fx-select
              v-model="setting.pageWidthMode"
              size="mini"
              :options="  [
                      { value: 'responsive', label: $t('marketing.components.Hexagon.xyskd_b0e2f3') },
                      { value: 'fixed', label: $t('marketing.components.Hexagon.gdkd_b591ae') },
                    ]"
              @change="handlePageWidthModeChange"
            />
          </div>
        </div>
        <div v-if="setting.pageWidthMode === 'fixed'" :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
          <div :class="[$style.title, 'hexagon__setting_title']">
            {{ $t('marketing.components.Hexagon.gdkdz_b7dde5') }}
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']">
            <PxAndRateInput v-model="setting.pageWidth" @change="handlePageWidthChange" />
          </div>
        </div>
      </template>
      <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
        <div :class="[$style.title, 'hexagon__setting_title']">
          {{ $t('marketing.components.Hexagon.ymbj_1dff74') }}
        </div>
        <div :class="[$style.con, 'hexagon__setting_con']">
          <fx-select
            v-model="setting.pageBackgroundMode"
            size="mini"
            :options="[
              { value: '', label: $t('marketing.pages.setting.mr_18c634') },
              { value: 1, label: $t('marketing_pd.commons.ys_6b36c6') },
              { value: 2, label: $t('marketing_pd.commons.dt_16d199') },
            ]"
            @change="handlePageBackgroundModeChange"
          />
        </div>
      </div>
      <template v-if="setting.pageBackgroundMode === 1">
        <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
          <div :class="[$style.title, 'hexagon__setting_title']">
            {{ $t('marketing_pd.commons.bjys_4573a7') }}
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']">
            <ColorPicker
              v-model="setting.pageBackgroundColor"
              popper-class="hexagon__setting-colorpicker"
              size="mini"
              show-alpha
              :predefine="predefineColors"
              @change="handleValueChange"
            />
          </div>
        </div>
      </template>
      <template v-if="setting.pageBackgroundMode === 2">
        <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
          <div :class="[$style.title, 'hexagon__setting_title']">
            {{ $t('marketing_pd.commons.sctp_ce6855') }}
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']">
            <Uploader
              v-bind="$attrs"
              v-model="pageBackgroundUrl"
              :class="$style.bguploader"
              :has-delete="true"
              name="backgroundImage"
              v-on="$listeners"
              @uploaded="handlePageBackgroundLoad"
            />
          </div>
        </div>
        <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
          <div :class="[$style.title, 'hexagon__setting_title']">
            {{ $t('marketing_pd.components.setting.tptcfs_0d685a') }}
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']">
            <fx-select
              v-model="setting.pageBackgroundFillType"
              size="mini"
              :options="[
                { value: 'horizontal-filling', label: $t('marketing_pd.commons.hxtc_0a741a') },
                { value: 'filling', label: $t('marketing_pd.commons.qptc_efe427') },
                { value: 'stretch', label: $t('marketing_pd.commons.ls_e39d3b') },
                { value: 'tiled', label: $t('marketing_pd.commons.pp_e1ff2c') },
                { value: 'centered', label: $t('marketing_pd.commons.jz_0bbc2e') },
              ]"
              @change="handlePageBackgroundStyle"
            />
          </div>
        </div>
      </template>
    </Card>
    <Card
      :class="$style.card"
      :title="$t('marketing_pd.components.setting.dbl_c45d8a')"
    >
      <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
        <div :class="[$style.title, 'hexagon__setting_title']">
          {{
            designMode === "form-page"
              ? $t("marketing_pd.commons.bdbt_f24371")
              : $t("marketing_pd.commons.ymbt_8d6b59")
          }}
        </div>
        <div :class="[$style.con, 'hexagon__setting_con']">
          <Input
            v-model="setting.name"
            size="mini"
            :placeholder="$t('marketing_pd.commons.qsr_02cc4f')"
            maxlength="30"
            show-word-limit
            @input="handleValueChange"
          />
        </div>
      </div>
      <div
        v-if="designMode !== 'form-page'"
        :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']"
      >
        <div
          :class="[$style.title, 'hexagon__setting_title']"
          style="display: flex; algin-items: center"
        >
          <span>{{ $t('marketing_pd.components.setting.zdyxcxdbl_6491bd') }} </span>
          <fx-tooltip
            effect="dark"
            width="140px"
            placement="bottom-end"
            :content="$t('marketing_pd.components.setting.cckszxcxzx_c2fc84')"
          >
            <i
              class="card-item__title-tip hexagon-icon hicon-qita4liang"
              style="color: #c1c5ce; margin-left: 4px; top: 1px; position: relative;"
            />
          </fx-tooltip>
        </div>
        <div :class="[$style.con, 'hexagon__setting_con']">
          <fx-switch
            v-model="setting.headerOpts.isCustomMiniapp"
            size="small"
            @change="handleValueChange"
          />
        </div>
      </div>
      <template v-if="designMode !== 'form-page' && setting.headerOpts.isCustomMiniapp">
        <div style="border-radius:4px;background:#F7F8FA;padding:14px 12px">
          <div style="color:#181c25;font-weight:700;margin-bottom:12px;">
            {{ $t('marketing_pd.components.setting.xcxdblys_b00a36') }}
          </div>
          <div
            :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']"
          >
            <div :class="[$style.title, 'hexagon__setting_title']">
              {{ $t('marketing_pd.commons.bjs_2f97db') }}
            </div>
            <div :class="[$style.con, 'hexagon__setting_con']">
              <fx-color-picker
                v-model="setting.headerOpts.fpHeaderBackgroundColor"
                popper-class="hexagon__setting-colorpicker"
                size="mini"
                show-alpha
                :predefine="predefineColors"
                @change="handleValueChange"
              />
            </div>
          </div>
          <div
            :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']"
          >
            <div :class="[$style.title, 'hexagon__setting_title']">
              {{ $t('marketing_pd.components.setting.btjtbs_f76602') }}
            </div>
            <div :class="[$style.con, 'hexagon__setting_con']">
              <fx-select
                v-model="setting.headerOpts.fpFontColor"
                size="mini"
                :options="[
                  { value: 1, label: $t('marketing_pd.components.setting.hs_9d2d1f') },
                  { value: 2, label: $t('marketing_pd.components.setting.bs_2fc96b') },
                ]"
                @change="handleValueChange"
              />
            </div>
          </div>
          <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
            <div :class="[$style.title, 'hexagon__setting_title']">
              {{ $t('marketing_pd.components.setting.ycbt_4e9ce9') }}
            </div>
            <div :class="[$style.con, 'hexagon__setting_con']">
              <fx-switch
                v-model="setting.headerOpts.fpHideTitle"
                size="small"
                @change="handleValueChange"
              />
            </div>
          </div>
        </div>
        <div
          :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']"
          style="margin-top:12px"
        >
          <div
            :class="[$style.title, 'hexagon__setting_title']"
            style="display: flex; algin-items: center"
          >
            <span>{{ $t('marketing_pd.components.setting.xfdbl_210ca8') }}</span>
            <fx-tooltip
              effect="dark"
              width="140px"
              placement="bottom-end"
              :content="$t('marketing_pd.components.setting.ybyyxcxdbl_90dabe')"
            >
              <i
                class="card-item__title-tip hexagon-icon hicon-qita4liang"
                style="color: #c1c5ce; margin-left: 4px; top: 1px; position: relative;"
              />
            </fx-tooltip>
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']">
            <fx-switch
              v-model="setting.headerOpts.isCustomSticky"
              size="small"
              @change="handleValueChange"
            />
          </div>
        </div>
        <template v-if="setting.headerOpts.isCustomSticky">
          <div style="border-radius:4px;background:#F7F8FA;padding:14px 12px">
            <div style="color:#181c25;font-weight:700;margin-bottom:12px;">
              {{ $t('marketing_pd.components.setting.xfdblys_2398d9') }}
            </div>
            <div
              :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']"
            >
              <div :class="[$style.title, 'hexagon__setting_title']">
                {{ $t('marketing_pd.commons.bjs_2f97db') }}
              </div>
              <div :class="[$style.con, 'hexagon__setting_con']">
                <fx-color-picker
                  v-model="setting.headerOpts.headerBackgroundColor"
                  popper-class="hexagon__setting-colorpicker"
                  size="mini"
                  show-alpha
                  :predefine="predefineColors"
                  @change="handleValueChange"
                />
              </div>
            </div>
            <div
              :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']"
            >
              <div :class="[$style.title, 'hexagon__setting_title']">
                {{ $t('marketing_pd.components.setting.btjtbs_f76602') }}
              </div>
              <div :class="[$style.con, 'hexagon__setting_con']">
                <fx-select
                  v-model="setting.headerOpts.fontColor"
                  size="mini"
                  :options="[
                    { value: 1, label: $t('marketing_pd.components.setting.hs_9d2d1f') },
                    { value: 2, label: $t('marketing_pd.components.setting.bs_2fc96b') },
                  ]"
                  @change="handleValueChange"
                />
              </div>
            </div>
          </div>
        </template>
      </template>
    </Card>

    <Card
      v-if="designMode !== 'form-page'"
      :class="$style.card"
      :title="$t('marketing_pd.components.setting.nrbj_5392f5')"
    >
      <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
        <div :class="[$style.title, 'hexagon__setting_title']">
          {{ $t('marketing_pd.components.setting.bjys_f893bc') }}
        </div>
        <div :class="[$style.con, 'hexagon__setting_con']">
          <fx-select
            v-model="setting.backgroundOpts.mode"
            size="mini"
            :options="[
              { value: 1, label: $t('marketing_pd.commons.ys_6b36c6') },
              { value: 2, label: $t('marketing_pd.commons.dt_16d199') },
              { value: 3, label: $t('marketing_pd.components.setting.lbt_0c0180') },
            ]"
            @change="handleBackgroundModeChange"
          />
        </div>
      </div>
      <template v-if="setting.backgroundOpts.mode === 1">
        <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
          <div :class="[$style.title, 'hexagon__setting_title']">
            {{ $t('marketing_pd.commons.bjys_4573a7') }}
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']">
            <ColorPicker
              v-model="setting.style.backgroundColor"
              popper-class="hexagon__setting-colorpicker"
              size="mini"
              show-alpha
              :predefine="predefineColors"
              @change="handleValueChange"
            />
          </div>
        </div>
      </template>
      <template v-if="setting.backgroundOpts.mode === 2">
        <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
          <div :class="[$style.title, 'hexagon__setting_title']">
            {{ $t('marketing_pd.commons.sctp_ce6855') }}
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']">
            <Uploader
              v-bind="$attrs"
              v-model="backgroundUrl"
              :class="$style.bguploader"
              :has-delete="true"
              name="backgroundImage"
              v-on="$listeners"
              @uploaded="handleBackgroundLoad"
            />
          </div>
        </div>
        <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
          <div :class="[$style.title, 'hexagon__setting_title']">
            {{ $t('marketing_pd.components.setting.tptcfs_0d685a') }}
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']">
            <fx-select
              v-model="setting.backgroundFillType"
              size="mini"
              :options="[
                { value: 'horizontal-filling', label: $t('marketing_pd.commons.hxtc_0a741a') },
                { value: 'filling', label: $t('marketing_pd.commons.qptc_efe427') },
                { value: 'stretch', label: $t('marketing_pd.commons.ls_e39d3b') },
                { value: 'tiled', label: $t('marketing_pd.commons.pp_e1ff2c') },
                { value: 'centered', label: $t('marketing_pd.commons.jz_0bbc2e') },
              ]"
              @change="handleBackgroundStyle"
            />
          </div>
        </div>
      </template>
      <template v-if="setting.backgroundOpts.mode === 3">
        <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
          <div :class="[$style.title, 'hexagon__setting_title']">
            {{ $t('marketing_pd.commons.sctp_ce6855') }}
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']">
            <div style="line-height: 16px; color: #91959E;">
              {{ $t("marketing_pd.components.setting.mztpkdbndy_20c219") }}
            </div>
            <ImageSelect
              v-bind="$attrs"
              :class="$style.mt_10"
              :data="setting.backgroundOpts.carouselImgs"
              v-on="$listeners"
              @changeFileList="handleImageSelectChange"
              @changeActive="handleImageSelectActive"
            />
          </div>
        </div>
        <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
          <div :class="[$style.title, 'hexagon__setting_title']">
            {{ $t('marketing_pd.components.setting.tptcfs_0d685a') }}
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']">
            <fx-select
              v-model="setting.backgroundOpts.fillType"
              size="mini"
              :options="[
                { value: 'horizontal-filling', label: $t('marketing_pd.commons.hxtc_0a741a') },
                { value: 'filling', label: $t('marketing_pd.commons.qptc_efe427') },
                { value: 'stretch', label: $t('marketing_pd.commons.ls_e39d3b') },
                { value: 'tiled', label: $t('marketing_pd.commons.pp_e1ff2c') },
                { value: 'centered', label: $t('marketing_pd.commons.jz_0bbc2e') },
              ]"
              @change="handleBackgroundStyle"
            />
          </div>
        </div>
        <CarouselPointSetting
          v-model="setting.backgroundOpts"
          :can-bottom="true"
          @change="carouselPointSettingChange"
        />
      </template>
    </Card>

    <Card
      :class="$style.card"
      :title="$t('marketing_pd.components.setting.fxsz_b3cec2')"
    >
      <div :class="[$style.setting_item, 'hexagon__setting_item']">
        <div :class="[$style.title, 'hexagon__setting_title']">
          {{ $t("marketing_pd.components.setting.fxbt_382e6f") }}
        </div>
        <div :class="[$style.con, 'hexagon__setting_con']">
          <Input
            v-model="setting.shareOpts.title"
            size="mini"
            :placeholder="$t('marketing_pd.commons.qsr_02cc4f')"
            type="textarea"
            :rows="3"
            maxlength="100"
            show-word-limit
            @input="handleValueChange"
          />
        </div>
      </div>
      <div :class="[$style.setting_item, 'hexagon__setting_item']">
        <div :class="[$style.title, 'hexagon__setting_title']">
          {{ $t("marketing_pd.components.setting.fxms_8fcba7") }}
        </div>
        <div :class="[$style.con, 'hexagon__setting_con']">
          <Input
            v-model="setting.shareOpts.desc"
            size="small"
            type="textarea"
            :rows="3"
            maxlength="100"
            show-word-limit
            :placeholder="$t('marketing_pd.commons.qsr_02cc4f')"
            @input="handleValueChange"
          />
        </div>
      </div>
      <div :class="[$style.setting_item, 'hexagon__setting_item']">
        <div :class="[$style.title, 'hexagon__setting_title']">
          {{ $t("marketing_pd.components.setting.fxyfmt_cef49e") }}
        </div>
        <div :class="[$style.desc, 'hexagon__setting_desc']">
          {{ $t("marketing_pd.components.setting.jyccxszcgs_e6126e") }}
          <span @click="() => (isShowImageViewer = true)">
            {{ $t("marketing_pd.components.setting.ckfmsjgf_5a954d") }}
          </span>
        </div>
        <div :class="[$style.con, $style.cover_upload, 'hexagon__setting_con']">
          <Uploader
            v-bind="$attrs"
            v-model="setting.shareOpts.imgUrl"
            name="shareImage"
            :has-delete="true"
            :picture-size="{ width: 300, height: 300 }"
            v-on="$listeners"
            @uploaded="handleShareImgLoad"
          />
        </div>
      </div>
      <div
        v-if="designMode !== 'form-page'"
        :class="[$style.setting_item, 'hexagon__setting_item']"
      >
        <div :class="[$style.title, 'hexagon__setting_title']">
          {{ $t("marketing_pd.components.setting.fxhb_9c7858") }}
        </div>
        <div :class="[$style.con, $style.cover_upload, 'hexagon__setting_con']">
          <Uploader
            v-bind="$attrs"
            v-model="setting.shareOpts.sharePosterUrl"
            type="poster"
            name="sharePoster"
            :has-delete="true"
            :picture-size="{ width: 300, height: 300 }"
            v-on="$listeners"
            @uploaded="handleSharePosterLoad"
          />
        </div>
      </div>
      <Action
        v-if="designMode !== 'form-page'"
        :value="setting.dataSourceAction"
        name="list_action"
        :include="['function']"
        :title="$t('marketing_pd.components.setting.sjy_c11322')"
        @input="handleDataSourceActionChange"
      />
      <div
        :class="{
          'hexagon__setting_item': true,
          [$style.setting_item]: true,
          [$style.mt_10]: setting.dataSourceAction && setting.dataSourceAction.type
        }"
      >
        <div :class="[$style.title, 'hexagon__setting_title']">
          {{ $t("marketing_pd.components.setting.xtmrtswayy_2efd76") }}
        </div>
        <div :class="[$style.con, 'hexagon__setting_con']">
          <Select
            v-model="setting.language"
            size="small"
            :placeholder="$t('marketing.commons.qxz_708c9d')"
            @change="handleValueChange"
          >
            <Option
              v-for="item in languages"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            />
          </Select>
          <p style="margin-top: 10px; color: #91959e; font-size: 12px">
            {{ $t("marketing_pd.components.setting.tsggnmqjzc_9ce0d8") }}
          </p>
        </div>
      </div>
    </Card>
    <Card
      :class="$style.card"
      :title="$t('marketing_pd.components.setting.sydc_ba2e7f')"
    >
      <div slot="title" style="flex: 1;padding-left: 10px;">
        <fx-switch
          :value="popupIsShow"
          size="small"
          @change="handlePopupShowChange"
        />
      </div>
      <div :class="[$style.setting_item, 'hexagon__setting_item']">
        <div :class="[$style.desc, 'hexagon__setting_desc']">
          {{ $t('marketing_pd.components.setting.kyyhdxchdy_9374ea') }}
        </div>
        <div :class="[$style.row, 'hexagon__setting_row']">
          <fx-checkbox
            v-if="popupIsShow"
            :value="!!setting.popupOpts.isPreview"
            style="margin-left: 12px"
            size="small"
            @change="handlePopupPreview"
          >
            {{ $t('marketing_pd.components.setting.yldc_beb1b2') }}
          </fx-checkbox>
        </div>
      </div>
      <template v-if="popupIsShow">
        <div :class="[$style.setting_item, 'hexagon__setting_item']">
          <div :class="[$style.con, $style.popup_upload, 'hexagon__setting_con']">
            <Uploader
              v-bind="$attrs"
              v-model="setting.popupOpts.coverUrl"
              style="width: 98px;height: 164px;"
              v-on="$listeners"
              @uploaded="handleValueChange"
            />
          </div>
        </div>
        <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
          <div :class="[$style.title, 'hexagon__setting_title']">
            {{ $t('marketing_pd.commons.kd_c28479') }}
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']">
            <fx-slider
              :value="setting.popupOpts.width || 280"
              :min="100"
              :max="375"
              show-input
              :show-input-controls="false"
              input-size="mini"
              @input="(val) => popupOptsSettingChange('width', val)"
            />
          </div>
        </div>
        <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
          <div :class="[$style.title, 'hexagon__setting_title']">
            {{ $t('marketing_pd.commons.yj_0103eb') }}
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']">
            <fx-slider
              :value="setting.popupOpts.borderRadius || 16"
              :min="0"
              :max="100"
              show-input
              :show-input-controls="false"
              input-size="mini"
              @input="(val) => popupOptsSettingChange('borderRadius', val)"
            />
          </div>
        </div>
        <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
          <div :class="[$style.title, 'hexagon__setting_title']">
            {{ $t('marketing_pd.commons.wz_d4d2a6') }}
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']">
            <fx-slider
              :value="setting.popupOpts.positionY || 50"
              :min="0"
              :max="100"
              show-input
              :show-input-controls="false"
              input-size="mini"
              @input="(val) => popupOptsSettingChange('positionY', val)"
            />
          </div>
        </div>
        <Action
          v-model="setting.popupOpts.action"
          :title="$t('marketing_pd.commons.djhdz_f91dcc')"

          @input="handleValueChange"
        />
        <div :class="[$style.setting_item, $style.mt_10, 'hexagon__setting_item']">
          <div :class="[$style.title, 'hexagon__setting_title']">
            {{ $t('marketing_pd.components.setting.dccs_bfabc2') }}
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']">
            <fx-radio-group
              v-model="setting.popupOpts.mode"
              @change="handleValueChange"
            >
              <fx-radio :label="1">
                {{ $t('marketing_pd.components.setting.mcjrdc_87a025') }}
              </fx-radio>
              <fx-radio :label="2">
                <div style="display:inline-flex;align-items:center;">
                  {{ $t('marketing.pages.mini_app.jdcyc_efbc7c') }}
                  <fx-tooltip :content="$t('marketing_pd.components.setting.xscdctphhz_8cf77b')">
                    <img
                      style="width:13px;height:13px;margin-left:6px;"
                      :src="require('$page-designer/assets/icon-question.png')"
                      alt=""
                    >
                  </fx-tooltip>
                </div>
              </fx-radio>
            </fx-radio-group>
          </div>
        </div>
      </template>
    </Card>
    <CTASetting :data="setting.ctaConfig" @change="handleCtaChange"/>
    <ElImageViewer
      v-show="isShowImageViewer"
      ref="ElImageViewer"
      :on-close="() => (isShowImageViewer = false)"
      :url-list="imageUrls"
      :z-index="602"
    />
    <Dialog
      v-if="setting.pcAdaptation"
      :class="$style.pcpreview"
      :title="$t('marketing_pd.components.setting.dxgyl_35ea00')"
      size="big"
      :fullscreen="true"
      :show-fullscreen-switch="true"
      append-to-body
      :visible="pcpreviewVisible"
      @close="pcpreviewVisible = false"
    >
      <VPageRender
        ref="page"
        :title="$t('marketing_pd.components.setting.yl_645dbc')"
        :data="pageCompList"
        :disabled="true"
        :show-navbar="false"
        :width="1125"
        :pc-adaptation="setting.pcAdaptation"
        :scroll-height="'auto'"
      />
    </Dialog>
  </div>
</template>
<script>
import Card from '../../common/Card.vue'
import Uploader from '../../common/Uploader.vue'
import Content from '../components/Content.vue'
import Action from '../../common/action/index.js'
import { predefineColors } from '../../../utils/const.js'
import pirtureCutterStandard from '../../../assets/picture-standard.jpg'
import ImageSelect from '../../common/ImageSelect.vue'
import CarouselPointSetting from '../../common/CarouselPointSetting.vue'
import { defaultPageData } from '../../../config/components.js'
import VPageRender from '../../PageRender.vue'
import ColorPicker from '../../comp/components/ColorPicker.vue'
import CTASetting from '../../common/CTASetting.vue'
import PxAndRateInput from '../components/PxAndRateInput.vue'
import { mapActions } from 'vuex'
export default {
  components: {
    Card,
    Input: FxUI.Input,
    ColorPicker,
    Uploader,
    Select: FxUI.Select.components.ElSelect,
    Option: FxUI.Select.components.ElSelect.components.ElOption,
    Action,
    ElImageViewer: FxUI.Image.extends.components.ImageViewer,
    FxSwitch: FxUI.Switch,
    FxTooltip: FxUI.Tooltip,
    Dialog: FxUI.Dialog,
    ImageSelect,
    CarouselPointSetting,
    VPageRender,
    CTASetting,
    PxAndRateInput,
  },
  extends: Content,
  data() {
    return {
      pcpreviewVisible: false,
      backgroundUrl: '',
      pageBackgroundUrl: '',
      pageBackgroundColor: '',
      languages: [
        {
          name: $t('marketing_pd.components.setting.jtzw_d688a3'),
          value: 'zh-CN',
        },
        { name: $t('marketing.components.Hexagon.ftzw_dd16f5'), value: 'zh-TW' },
        { name: $t('marketing_pd.components.setting.yw_f9fb6a'), value: 'en' },
        { name: $t('marketing.components.Hexagon.ry_671c0d'), value: 'ja-JP' },
        { name: $t('marketing.components.Hexagon.fy_8607ec'), value: 'fr-FR' },
        { name: $t('marketing.components.Hexagon.dy_a9f4df'), value: 'de' },
        { name: $t('marketing.components.Hexagon.ey_fc9096'), value: 'ru-RU' },
      ],
      predefineColors,
      backgroundColor: '',
      isShowImageViewer: false,
      imageUrls: [pirtureCutterStandard],
      mergeDone: false,
      headerOptsMergeDone: false,
    }
  },
  computed: {
    designMode() {
      return this.$store.state.hexagon.designMode
    },
    viewportMode() {
      return this.$store.state.hexagon.designer.viewportMode
    },
    pageCompList() {
      return this.$store.state.hexagon.designer.pageCompList
    },
    popupIsShow() {
      return Boolean(this.setting.popupOpts.show)
    },
  },
  watch: {
    // 合并页面背景相关参数，为什么写在这里是因为在 mounted 不起作用
    'setting.headerOpts': {
      deep: true,
      handler(val) {
        if (val && !this.headerOptsMergeDone) {
          this.mergeConfig(this.setting.headerOpts, defaultPageData.headerOpts)
          this.headerOptsMergeDone = true
          this.handleValueChange()
        }
      },
    },
    'setting.backgroundOpts': {
      deep: true,
      handler(val) {
        if (val && !this.mergeDone) {
          this.mergeConfig(this.setting.backgroundOpts, defaultPageData.backgroundOpts)
          this.mergeDone = true
          this.handleValueChange()
        }
      },
    },
    'setting': {
      immediate: true,
      handler(val) {
        // 确保适配模式有默认值
        if (val && !val.adaptionMode) {
          this.$set(this.setting, 'adaptionMode', 'stretch');
        }
        // 确保最大内容宽度有默认值
        if (val && !val.pageWidthMode) {
          this.$set(this.setting, 'pageWidthMode', 'responsive');
        }
      }
    },
    'setting.style.backgroundImage': {
      deep: true,
      handler(val) {
        if (this.setting.backgroundOpts.mode === 2) {
          this.parseBgurl()
        }
      },
    },
    'setting.style.backgroundColor': {
      deep: true,
      handler(val) {
        if (this.setting.backgroundOpts.mode === 1) {
          this.backgroundColor = val
        }
      },
    },
    'setting.pageBackgroundImage': {
      deep: true,
      handler(val) {
        if (this.setting.pageBackgroundMode === 2) {
          this.parsePageBgurl()
        }
      },
    },
  },
  mounted() {
    /** 兼容写法
     * 等全部组件都换成新样式后，可以移除
     */
    const dom = $('.hexagon__settting_wrap .hexagon__setting_body')
    dom.addClass('reset')
    /** 兼容写法 */

    this.parseBgurl()
    this.parsePageBgurl()
    this.$nextTick(() => {
      this.setViewportMode(this.viewportMode);
    });
  },
  destroyed() {
    /** 兼容写法
     * 等全部组件都换成新样式后，可以移除
     */
    const dom = $('.hexagon__settting_wrap .hexagon__setting_body')
    dom.removeClass('reset')
    /** 兼容写法 */
  },
  methods: {
    ...mapActions('hexagon/designer', {
      setViewportMode: 'setViewportMode',
    }),
    handleAdaptionModeChange(mode) {
      if (mode === 'stretch') {
        this.setting.pcAdaptation = false;
      } else {
        this.setting.pcAdaptation = true;
        this.setting.pageWidthMode = 'fixed';
        this.setting.pageWidth = '1200px';
      }
      this.handleValueChange();
      this.$nextTick(() => {
        this.setViewportMode(this.viewportMode);
      });
    },
    handlePageWidthModeChange(mode) {
      this.setting.pageWidthMode = mode;
      // 设置默认值
      if (mode === 'fixed') {
        this.setting.pageWidth = '1200px';
      }
      this.$nextTick(() => {
        this.setViewportMode(this.viewportMode);
      });
      this.handleValueChange();
    },
    handlePageWidthChange() {
      this.$nextTick(() => {
        this.setViewportMode(this.viewportMode);
      });
      this.handleValueChange();
    },
    handlePopupShowChange(val) {
      this.setting.popupOpts.show = Number(val)
      this.setting.popupOpts.isPreview = !!val
      this.handleValueChange()
    },
    handleCtaChange(data) {
      if(!this.setting.ctaConfig){
        this.setting.ctaConfig = {};
      }
      this.setting.ctaConfig = data || {}
      this.handleValueChange()
    },
    handlePopupPreview(val) {
      this.setting.popupOpts.isPreview = val
      this.handleValueChange()
    },
    handlePopupCoverUploaded(data) {
      this.handleValueChange()
    },
    popupOptsSettingChange(key, value) {
      if (key) {
        this.setting.popupOpts[key] = value
      }
      this.handleValueChange()
    },
    carouselPointSettingChange(key, value) {
      if (key) {
        this.setting.backgroundOpts[key] = value
      }
      this.handleValueChange()
    },
    handleBackgroundModeChange(mode) {
      if (mode === 1) {
        this.setting.style.backgroundColor = this.backgroundColor
        this.setting.style.backgroundImage = ''
      } else if (mode === 2) {
        this.setting.style.backgroundColor = ''
        this.setting.style.backgroundImage = `url(${this.backgroundUrl})`
      } else if (mode === 3) {
        this.setting.style.backgroundColor = ''
        this.setting.style.backgroundImage = this.setting.backgroundOpts
          .carouselImgs.length
          ? `url(${
            this.setting.backgroundOpts.carouselImgs[
              this.setting.backgroundOpts.carouselPointActiveIndex
              ].url
          })`
          : ''
      }
      this.setting.backgroundOpts.mode = mode
      this.handleValueChange()
    },
    handlePageBackgroundModeChange(mode) {
      if (mode === 1) {
        this.setting.pageBackgroundColor = this.pageBackgroundColor;
        this.setting.pageBackgroundImage = '';
      } else if (mode === 2) {
        this.setting.pageBackgroundColor = '';
        this.setting.pageBackgroundImage = `url(${this.pageBackgroundUrl})`
      }
      this.setting.pageBackgroundMode = mode
      this.handleValueChange()
    },
    handleImageSelectChange(images) {
      this.setting.backgroundOpts.carouselImgs = images
      this.setting.style.backgroundImage = this.setting.backgroundOpts
        .carouselImgs.length
        ? `url(${
          this.setting.backgroundOpts.carouselImgs[
            this.setting.backgroundOpts.carouselPointActiveIndex
            ].url
        })`
        : ''
      this.handleBackgroundStyle(this.setting.backgroundOpts.fillType)
      this.handleValueChange()
    },
    handleImageSelectActive(index) {
      this.setting.backgroundOpts.carouselPointActiveIndex = index
      this.setting.style.backgroundImage = this.setting.backgroundOpts.carouselImgs.length
      && this.setting.backgroundOpts.carouselImgs.length > index
        ? `url(${this.setting.backgroundOpts.carouselImgs[index].url})`
        : ''
      this.handleValueChange()
    },
    delCarouselImages(index) {
      this.setting.backgroundOpts.carouselImgs.splice(index, 1)
    },
    parseBgurl() {
      const url = this.setting.style.backgroundImage
      const matched = url.match(/url\((.*)\)/)
      if (matched) {
        // eslint-disable-next-line prefer-destructuring
        this.backgroundUrl = matched[1]
      }
    },
    parsePageBgurl() {
      if(this.setting.pageBackgroundImage) {
        const url = this.setting.pageBackgroundImage
        const matched = url.match(/url\((.*)\)/)
        if (matched) {
          this.pageBackgroundUrl = matched[1]
        }
      }
    },
    handleBackgroundLoad({ url }) {
      const { backgroundFillType = 'filling' } = this.setting
      this.setting.style.backgroundImage = url ? `url(${url})` : ''
      this.handleBackgroundStyle(backgroundFillType)
    },
    handlePageBackgroundLoad({ url }) {
      const { pageBackgroundFillType = 'filling' } = this.setting
      this.setting.pageBackgroundImage = url ? `url(${url})` : '';
      this.handlePageBackgroundStyle(pageBackgroundFillType)
    },
    handleShareImgLoad(data) {
      console.log('handleShareImgLoad: ', data)
      const { originalImageAPath, cutOffsetList } = data
      this.setting.shareOpts.originalImageAPath = originalImageAPath || ''
      this.setting.shareOpts.cutOffsetList = cutOffsetList || []
      this.handleValueChange()
    },
    handleSharePosterLoad(data) {
      console.log('handleSharePosterLoad: ', data)
      const { sharePosterUrl, sharePosterAPath } = data
      this.setting.shareOpts.sharePosterUrl = sharePosterUrl
      this.setting.shareOpts.sharePosterAPath = sharePosterAPath
      this.handleValueChange()
    },
    handleBackgroundStyle(type) {
      let backgroundStyle = {}
      switch (type) {
        case 'stretch':
          backgroundStyle = {
            backgroundSize: '100% 100%',
            backgroundPosition: 'unset',
            backgroundRepeat: 'no-repeat',
          }
          break
        case 'filling':
          backgroundStyle = {
            backgroundSize: 'cover',
            backgroundPosition: 'center center',
            backgroundRepeat: 'no-repeat',
          }
          break
        case 'tiled':
          backgroundStyle = {
            backgroundSize: 'unset',
            backgroundRepeat: 'repeat',
            backgroundPosition: 'unset',
          }
          break
        case 'centered':
          backgroundStyle = {
            backgroundSize: 'unset',
            backgroundPosition: 'center center',
            backgroundRepeat: 'no-repeat',
          }
          break
        case 'horizontal-filling':
          backgroundStyle = {
            backgroundSize: '100% auto',
            backgroundPosition: 'unset',
            backgroundRepeat: 'no-repeat',
          }
          break
        default:
          backgroundStyle = {
            backgroundSize: 'unset',
          }
          break
      }
      this.setting.style = {
        ...this.setting.style,
        ...backgroundStyle,
      }
      this.handleValueChange()
    },
    handlePageBackgroundStyle(type) {
      let backgroundStyle = {}
      switch (type) {
        case 'stretch':
          backgroundStyle = {
            backgroundSize: '100% 100%',
            backgroundPosition: 'unset',
            backgroundRepeat: 'no-repeat',
          }
          break
        case 'filling':
          backgroundStyle = {
            backgroundSize: 'cover',
            backgroundPosition: 'center center',
            backgroundRepeat: 'no-repeat',
          }
          break
        case 'tiled':
          backgroundStyle = {
            backgroundSize: 'unset',
            backgroundRepeat: 'repeat',
            backgroundPosition: 'unset',
          }
          break
        case 'centered':
          backgroundStyle = {
            backgroundSize: 'unset',
            backgroundPosition: 'center center',
            backgroundRepeat: 'no-repeat',
          }
          break
        case 'horizontal-filling':
          backgroundStyle = {
            backgroundSize: '100% auto',
            backgroundPosition: 'unset',
            backgroundRepeat: 'no-repeat',
          }
          break
        default:
          backgroundStyle = {
            backgroundSize: 'unset',
          }
          break
      }
      this.setting.pageBackgroundStyle = backgroundStyle;
      this.handleValueChange()
    },
    handleDataSourceActionChange(action){
      //type不是执行apl函数时，清apl数据
      if(action.type !== 'function'){
        action.functionApiName = "";
        action.functionName = "";
      }
      this.setting.dataSourceAction = action;
      this.handleValueChange();
    },
  },
}
</script>
<style lang="less" module>

.adaptionSelector {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
  flex-direction: column;
}

.adaptionOption {
  position: relative;
  flex: 1;
  cursor: pointer;
  padding: 8px 10px 8px 44px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fff;

  &:hover {
    border-color: #c0c4cc;
  }

  &.selected {
    border-color: var(--color-primary06, #407FFF);
    &::before {
      content: "\A0";
      display: inline-block;
      border: 2px solid #fff;
      border-top-width: 0;
      border-right-width: 0;
      width: 7px;
      height: 4px;
      transform: rotate(-50deg);
      position: absolute;
      top: 2px;
      left: 2px;
      z-index: 3;
    }

    &::after {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 0;
      height: 0;
      border-top: 20px solid var(--color-primary06, #407FFF);
      border-right: 20px solid transparent;
    }
  }
}

.adaptionIcon {
  position: absolute;
  display: block;
  justify-content: center;
  width: 24px;
  height: 24px;
  left: 10px;
  top: 17px;

  i {
    width: 100%;
    height: 100%;
    display: block;
  }
  .icon-responsive {
    background: url('../../../assets/icon/responsiveicon.svg') no-repeat center center;
  }
  .icon-stretch {
    background: url('../../../assets/icon/stretchicon.svg') no-repeat center center;
  }
}

.adaptionTitle {
  font-size: 12px;
  color: #181C25;
  font-weight: 500;
  margin-bottom: 4px;
}

.adaptionDesc {
  font-size: 12px;
  color: #91959E;
}

.pcpreview{
  :global{
    .el-dialog__body{
      height: calc(100vh - 45px);
      padding: 0;
      overflow: hidden;
      overflow-y: auto;
      .hexagon__page_render{
        margin: 0 auto;
      }
    }
  }
}
.content {
  .bguploader {
    width: 82px;
  }

  .pc_adptbtn {
    display: flex;
    align-items: center;
  }
  .pc_preview {
    color: var(--color-info06, #407fff);
    margin-left: 10px;
    cursor: pointer;
  }
  .flex_row {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
  }

  .flex_col {
    display: flex;
    flex-direction: column;
  }

  .split_line {
    height: 1px;
    margin: 20px 0;
    background: #dee1e8;
  }

  .carousel_image__wrapper {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 60px;
    margin: 8px 8px 0 0;
  }

  .carousel_image__wrapper:nth-child(4n + 4) {
    margin-right: 0;
  }

  .carousel_image {
    width: 100%;
    height: 100%;
  }

  .mt_10 {
    margin-top: 10px;
  }

  .mt_20 {
    margin-top: 20px;
  }

  .close_icon {
    position: absolute;
    border-radius: 50%;
    width: 8px;
    height: 8px;
    font-size: 16px;
    color: #c1c5ce;
    background-color: #fff;
    top: 0;
    right: 0;
    transform: translate(4px, -4px);
    cursor: pointer;
    i {
      position: absolute;
      top: 0;
      left: 0;
      transform: translate(-4px, -4px);
    }
  }

  .carousel_uploader {
    box-sizing: border-box;
    :global {
      .upload__box {
        width: 58px;
        height: 58px;
      }
    }
  }

  .cover_upload {
    :global {
      .upload__box {
        width: auto;
        height: 145px;
      }
    }
  }

  .popup_upload {
    width: 98px;
    height: 164px;
    :global {
      .upload__box {
        width: 98px;
        height: 164px;
      }
    }
  }
}
</style>
