<template>
  <div />
</template>
<script>
import compMixins from '../compMixins.js'
import { predefineColors } from '../../../utils/const.js'

export default {
  mixins: [compMixins],
  data() {
    return {
      predefineColors,
      showTitle: !!this.data.title,
      titleHeight: 30,
      prevTitleHeight: 0,
    }
  },
  watch: {
    'data.title': {
      handler(title) {
        this.showTitle = !!title
      },
    },
  },
  computed: {
    isActivityComponent() {
      return (
        this.setting.fieldName === 'marketingEventDetail' ||
        this.setting.fieldName === 'channelsName' ||
        this.setting.fieldName === 'marketingEventTitle' ||
        this.setting.fieldName === 'marketingEventTime' ||
        this.setting.fieldName === 'marketingEventAddress'
      )
    }
  },
  methods: {
    handleTitleTextInput({ text, style }) {
      this.setting.title = text

      this.setting.titleStyle = {
        ...(this.setting.titleStyle || {}),
        ...style,
      }

      this.setting.style.color = style.color
      this.setting.style.fontSize = style.fontSize
      if (this.isActivityComponent) {
        this.setting.style = {
          ...this.setting.style,
          ...style,
        }
      }

      this.handleValueChange()

      // this.$nextTick(() => {
      //   //默认无高度组件无需计算高度
      // if (height !== undefined && height !== null) {
      //   if (!this.titleNode) {
      //     this.titleNode = document.querySelector(
      //       `.hexagon__canvas .hexagon__comp-title[comp-id="${this.data.id}"]`
      //     );
      //   }
      //   this.titleHeight = this.titleNode.clientHeight;
      //   console.log(this.titleNode, this.titleHeight, 666,text && !this.showTitle, !text && this.showTitle);
      //   //显示标题时，把组件高度增加
      //   if (text) {
      //     this.setting.style.height = height + this.titleHeight - (this.prevTitleHeight ? this.prevTitleHeight : 0);
      //     this.prevTitleHeight = this.titleHeight;
      //   }

      //   //没有标题时，把组件高度减少
      //   if (!text) {
      //     this.setting.style.height = height - this.titleHeight;
      //   }
      //   this.handleValueChange();
      // }
      // })
    },
    handlePlaceholderTextInput({ text, style }) {
      this.setting.placeholder = text
      this.setting.placeholderStyle = {
        ...(this.setting.placeholderStyle || {}),
        ...style,
      }
      this.handleValueChange()
    },
    handleConfirmPlaceholderTextInput({ text, style }) {
      this.setting.confirmPlaceholder = text
      this.setting.confirmPlaceholderStyle = {
        ...(this.setting.confirmPlaceholderStyle || {}),
        ...style,
      }
      this.handleValueChange()
    },
  },
}
</script>
