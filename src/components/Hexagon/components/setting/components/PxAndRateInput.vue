<template>
  <div :class="$style.pxRateInput">
    <fx-input-number
      :class="$style.inputNumber"
      v-model="numValue"
      size="mini"
      :min="currentMin"
      :max="currentMax"
      controls-position="right"
      @change="handleInputChange"
    />
    <fx-select
      v-model="unit"
      size="mini"
      :class="$style.unitSelect"
      :options="unitOptions"
      @change="handleUnitChange"
    />
  </div>
</template>

<script>
export default {
  name: 'PxAndRateInput',
  props: {
    value: {
      type: String,
      default: '100%'
    },
    min: {
      type: Number,
      default: 0
    },
    max: {
      type: Number,
      default: 2000
    },
    
  },
  data() {
    return {
      numValue: 100,
      unit: '%',
      unitOptions: [
        { label: 'px', value: 'px' },
        { label: '%', value: '%' }
      ]
    }
  },
  computed: {
    currentMin() {
      return this.unit === '%' ? 0 : this.min;
    },
    currentMax() {
      return this.unit === '%' ? 100 : this.max;
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(newVal) {
        this.parseValue(newVal);
      }
    }
  },
  methods: {
    parseValue(val) {
      if (!val) {
        this.numValue = 100;
        this.unit = '%';
        return;
      }
      
      // 处理百分比
      if (val.endsWith('%')) {
        this.unit = '%';
        this.numValue = parseInt(val, 10) || 100;
        // 确保百分比值不超过100
        if (this.numValue > 100) {
          this.numValue = 100;
        }
        return;
      }
      
      // 处理px
      if (val.endsWith('px')) {
        this.unit = 'px';
        this.numValue = parseInt(val, 10) || 0;
        return;
      }
      
      // 如果没有单位，默认为百分比
      this.unit = '%';
      this.numValue = parseInt(val, 10) || 100;
      // 确保百分比值不超过100
      if (this.numValue > 100) {
        this.numValue = 100;
      }
    },
    
    formatValue() {
      return `${this.numValue}${this.unit}`;
    },
    
    handleInputChange() {
      // 确保百分比不超过100
      if (this.unit === '%' && this.numValue > 100) {
        this.numValue = 100;
      }
      this.$emit('input', this.formatValue());
      this.$emit('change', this.formatValue());
    },
    
    handleUnitChange() {
      // 单位切换时调整值的范围
      if (this.unit === '%' && this.numValue > 100) {
        this.numValue = 100;
      }
      this.$emit('input', this.formatValue());
      this.$emit('change', this.formatValue());
    }
  }
};
</script>

<style lang="less" module>
.pxRateInput {
  display: flex;
  align-items: center;
  .inputNumber {
    flex: 1;
  }
}

.unitSelect {
  width: 50px !important;
  margin-left: 8px !important;
}
</style>