<template>
  <div class="video-content">
    <VDialog
      :visible.sync="isVideoNumIdVisible"
      :title="$t('marketing_pd.components.setting.rhhqsph_7fbbd3')"
      width="500px"
      :cancel-text="$t('marketing_pd.components.setting.wzdl_fe0337')"
      :show-confirm="false"
      @onClose="isVideoNumIdVisible = false"
    >
      <div style="margin-bottom:16px">
        {{ $t('marketing_pd.components.setting.hqsphdxydl_56e97d') }}
      </div>
      <img
        width="100%"
        src="../../../assets/<EMAIL>"
        alt=""
      >
    </VDialog>

    <VDialog
      width="700px"
      :visible.sync="isVideoIdVisible"
      :title="$t('marketing_pd.components.setting.rhhqsp_cc52a5')"
      :cancel-text="$t('marketing_pd.components.setting.wzdl_fe0337')"
      :show-confirm="false"
      @onClose="isVideoIdVisible = false"
    >
      <div style="margin-bottom:16px">
        {{ $t('marketing_pd.components.setting.hqspdxydls_a717b9') }}
      </div>
      <img
        width="100%"
        src="../../../assets/<EMAIL>"
        alt=""
      >
    </VDialog>

    <Card title="">
      <div :class="['setting-item', 'hexagon__setting_item']">
        <div :class="['setting-row', 'hexagon__setting_title']">
          <div class="setting-title">
            {{ $t('marketing_pd.components.setting.sph_684e72') }}
          </div>
          <div
            class="get-info"
            @click="isVideoNumIdVisible = true"
          >
            {{ $t('marketing_pd.components.setting.rhhq_183c27') }}
          </div>
        </div>
        <div :class="['hexagon__setting_con']">
          <fx-input
            v-model="setting.videoNumId"
            size="mini"
            :placeholder="$t('marketing_pd.commons.qsr_02cc4f')"
            @input="handleValueChange"
          />
        </div>
      </div>

      <div :class="['setting-item', 'hexagon__setting_item']">
        <div :class="['setting-row', 'hexagon__setting_title']">
          <div class="setting-title">
            {{ $t('marketing_pd.components.setting.sp_3bbf4d') }}
          </div>
          <div
            class="get-info"
            @click="isVideoIdVisible = true"
          >
            {{ $t('marketing_pd.components.setting.rhhq_183c27') }}
          </div>
        </div>
        <div :class="['hexagon__setting_con']">
          <fx-input
            v-model="setting.videoId"
            size="mini"
            :placeholder="$t('marketing_pd.commons.qsr_02cc4f')"
            @input="handleValueChange"
          />
        </div>
      </div>

      <div :class="['setting-item', 'hexagon__setting_item', 'hexagon__setting_row']">
        <div :class="['setting-title', 'hexagon__setting_title']">
          {{ $t('marketing_pd.commons.sphmc_c23013') }}
        </div>
        <div :class="['hexagon__setting_con']">
          <fx-input
            v-model="setting.videoName"
            size="mini"
            :placeholder="$t('marketing_pd.commons.qsr_02cc4f')"
            @input="handleValueChange"
          />
        </div>
      </div>

      <div :class="['setting-item', 'hexagon__setting_item', 'hexagon__setting_row']">
        <div :class="['setting-title', 'hexagon__setting_title']">
          {{ $t('marketing_pd.components.setting.spfm_39b75e') }}
        </div>
        <div :class="['hexagon__setting_con']">
          <fx-select
            v-model="setting.coverPageType"
            size="mini"
            :options="[
              { value: 'upload', label: $t('marketing_pd.commons.sctp_ce6855') },
              { value: 'url', label: $t('marketing_pd.commons.tpdz_f7a82c') },
            ]"
            @change="handleValueChange"
          />
          <div
            v-if="setting.coverPageType === 'upload'"
            class="uploader"
          >
            <Uploader
              v-model="setting.url"
              :upload="upload"
              :picture-size="{ width: 750, height: 1020 }"
              @uploaded="handleValueChange"
            />
          </div>
          <div
            v-else
            style="margin-top: 10px"
          >
            <fx-input
              v-model="setting.url"
              size="mini"
              :placeholder="$t('marketing_pd.commons.qsrtpdz_6af1e9')"
              @change="handleValueChange"
            />
          </div>
        </div>
      </div>

      <div :class="['setting-item', 'hexagon__setting_item', 'hexagon__setting_row']">
        <div :class="['setting-title', 'hexagon__setting_title']">
          {{ $t('marketing_pd.commons.gd_c1df04') }}
        </div>
        <div :class="['hexagon__setting_con']">
          <Slider
            v-model="setting.style.height"
            :min="0"
            :max="5000"
            show-input
            :show-input-controls="false"
            input-size="mini"
            @change="handleValueChange"
          />
        </div>
      </div>
    </Card>
  </div>
</template>
<script>
import Content from '../components/Content.vue'
import Uploader from '../../common/Uploader.vue'
import VDialog from '../../common/Dialog.vue'
import Card from '../../common/Card.vue'

export default {
  components: {
    Uploader,
    Slider: FxUI.Slider,
    VDialog,
    Card,
  },
  extends: Content,
  props: {
    upload: {
      type: Function,
      default: async () => false,
    },
  },
  data() {
    return {
      isVideoNumIdVisible: false,
      isVideoIdVisible: false,
    }
  },
  methods: {},
}
</script>
<style lang="less" scoped>
.video-content {
  .uploader {
    margin-top: 12px;
  }
}
.get-info {
  color: #0c6cff;
  cursor: pointer;
  font-size: 12px;
}
.setting-item {
  margin-top: 20px;
  margin-bottom: 20px;
  .setting-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
  }
  .setting-title {
    width: 60px;
    font-size: 12px;
    line-height: 18px;
    color: #181c25;
  }
}
</style>
