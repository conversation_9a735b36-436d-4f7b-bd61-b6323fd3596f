/** * 兼容旧直播数据接口字段live */
<template>
  <div :class="$style.content">
    <!-- 报名按钮设置 -->
    <template
      v-if="setting.type === 'livebutton' || setting.type === 'signupbutton'"
    >
      <Card
        :open="true"
        :title="$t('marketing_pd.components.setting.annr_5d0ae2')"
      >
      <!-- 市场活动报名按钮不展示报名按钮相关配置 因为市场活动报名按钮的配置在容器组件中 -->
        <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']" v-if="!(setting.typeValue === 'marketingEventSignupbutton')">
          <div :class="[$style.title, 'hexagon__setting_title']">
            {{ setting.placeholder || $t('marketing.commons.xzhd_a05f05') }}
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']">
            <div :class="$style.picker">
              <input
                type="text"
                disabled
                :value="setting.objectTitle || setting.liveTitle"
              >
              <div>
                <span class="el-icon-search" />
                <div
                  :class="$style.pickerEventHandle"
                  @click="beforeUpload"
                />
              </div>
            </div>
          </div>
        </div>
        <div
          v-if="setting.objectId || setting.liveId || ((isTemplate && !isPresetActivityTemplate) && setting.typeValue === 'marketingEventSignupbutton')"
          :class="[$style.setting_item, 'hexagon__setting_item']"
        >
          <div :class="[$style.schedule]">
            <div
              v-if="scheduleTabs.length > 1"
              :class="[$style.schedule_tabs]"
            >
              <div
                v-for="item in scheduleTabs"
                :key="item.status"
                :class="[
                  $style.schedule_tabs_item,
                  item.status === status ? $style.active : '',
                ]"
                @click="handleScheduleTabChange(item.status)"
              >
                {{ item.name }}
              </div>
            </div>
            <div :class="[$style.schedule_con]">
              <div :class="[$style.setting_item, 'hexagon__setting_item']">
                <div :class="[$style.title, 'hexagon__setting_title']">
                  {{ $t('marketing_pd.commons.wbm_e32239') }}-{{ $t('marketing_pd.commons.anmc_cf6e87') }}
                </div>
                <div :class="[$style.con, 'hexagon__setting_con']">
                  <TextInput
                    :value="{
                      text: schedule[status].no.name,
                      style: schedule[status].no.style,
                    }"
                    @input="(val) => handleScheduleTextInputChange(val, 'no')"
                  />
                </div>
              </div>
              <div :class="[$style.setting_item, 'hexagon__setting_item']">
                <div :class="[$style.con, 'hexagon__setting_con']">
                  <Action
                    v-model="schedule[status].no.action"
                    :compType="setting.type"
                    :title="`${$t('marketing_pd.commons.wbm_e32239')}-${$t('marketing_pd.commons.tjhtz_7ff2db')}`"
                    :extend-params="{
                      marketingEventId: setting.marketingEventId || '',
                    }"
                    :extend-actions="extendActions"
                    @input="handleValueChange"
                  />
                </div>
              </div>
              <div :class="[$style.setting_item, 'hexagon__setting_item']">
                <div :class="[$style.title, 'hexagon__setting_title']">
                  {{ $t('marketing_pd.commons.ybm_4166d8') }}-{{ $t('marketing_pd.commons.anmc_cf6e87') }}
                </div>
                <div :class="[$style.con, 'hexagon__setting_con']">
                  <TextInput
                    :value="{
                      text: schedule[status].yes.name,
                      style: schedule[status].yes.style,
                    }"
                    @input="(val) => handleScheduleTextInputChange(val, 'yes')"
                  />
                </div>
              </div>
              <div :class="[$style.setting_item, 'hexagon__setting_item']">
                <div :class="[$style.con, 'hexagon__setting_con']">
                  <Action
                    v-model="schedule[status].yes.action"
                    :compType="setting.type"
                    :title="`${$t('marketing_pd.commons.ybm_4166d8')}-${$t('marketing_pd.commons.tjhtz_7ff2db')}`"
                    :extend-params="{
                      marketingEventId: setting.marketingEventId || '',
                    }"
                    :extend-actions="extendActions"
                    @input="handleValueChange"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 模板场景下也可以 -->
        <template v-if="isOpenMember">
          <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
            <div :class="[$style.title, 'hexagon__setting_title']">
              {{ $t('marketing_pd.commons.hyyjbm_839991') }}
            </div>
            <div :class="[$style.con, 'hexagon__setting_con']">
              <ElSwitch
                :value="
                  setting.memberAutoSignup === 'true'
                    ? true
                    : setting.memberAutoSignup
                "
                size="small"
                @input="handleMemberAutoSignupChange"
              />
            </div>
          </div>
          <div
            v-if="setting.memberAutoSignup"
            :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']"
          >
            <div :class="[$style.title, 'hexagon__setting_title']">
              {{ $t('marketing_pd.commons.hydlrkkg_82d69d') }}
            </div>
            <div :class="[$style.con, 'hexagon__setting_con']">
              <ElSwitch
                :value="
                  setting.memberAutoSignupButton === undefined
                    ? true
                    : setting.memberAutoSignupButton
                "
                size="small"
                @input="handleMemberAutoSignupButtonChange"
              />
            </div>
          </div>
        </template>
        <div
          v-if="setting.memberAutoSignup && setting.memberAutoSignupButton"
          :class="[
            $style.setting_item,
            $style.autoSignupButton,
            'hexagon__setting_item',
          ]"
        >
          <div :class="[$style.title, 'hexagon__setting_title']">
            {{ $t('marketing_pd.commons.anmc_cf6e87') }}
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']">
            <TextInput
              :value="{
                text: setting.memberAutoSignupButtonText || '',
                style: setting.memberAutoSignupButtonStyle || {},
              }"
              @input="handleMemberAutoSignupButtonTextChange"
            />
          </div>
        </div>
      </Card>
    </template>
    <template v-else>
      <Card
        v-if="hasLayout"
        :open="true"
        :title="$t('marketing_pd.commons.bj_5aefca')"
      >
        <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
          <div :class="[$style.title, 'hexagon__setting_title']">
            {{ $t('marketing_pd.commons.bj_5aefca') }}
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']">
            <LayoutSetting
              v-model="setting.position"
              :options="positionTypeOption"
              @input="handleChangeLayout"
            />
          </div>
        </div>
      </Card>
      <Card
        :open="true"
        :title="$t('marketing_pd.components.setting.anztys_cd8583')"
      >
        <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
          <div :class="[$style.title, 'hexagon__setting_title']">
            {{ $t('marketing_pd.components.setting.anlx_3ee6ef') }}
          </div>
          <div :class="[$style.con, $style.layout, 'hexagon__setting_con']">
            <fx-select
              v-model="setting.layoutType"
              size="mini"
              :options="[
                { value: 'text-only', label: $t('marketing_pd.components.setting.jxsanmc_14b801'), },
                { value: 'icon-only', label: $t('marketing_pd.components.setting.jxstb_c479dc'), },
                { value: 'icon-and-text', label: $t('marketing_pd.components.setting.tbanmc_667023'), },
              ]"
              @change="handleLayoutTypeChange"
            />
          </div>
        </div>
        <div
          v-if="setting.wrapStyle"
          :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']"
        >
          <div :class="[$style.title, 'hexagon__setting_title']">
            {{ $t('marketing_pd.commons.bjs_2f97db') }}
          </div>
          <div :class="[$style.con, $style.layout, 'hexagon__setting_con']">
            <ColorPicker
              v-model="setting.wrapStyle.background"
              popper-class="hexagon__setting-colorpicker"
              size="mini"
              show-alpha
              :predefine="predefineColors"
              @change="handleValueChange"
            />
          </div>
        </div>
        <div
          v-if="setting.layoutType !== 'text-only'"
          :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']"
        >
          <div :class="[$style.title, 'hexagon__setting_title']">
            {{ $t('marketing_pd.components.setting.cdtbys_d23183') }}
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']">
            <LayoutSetting
              v-model="setting.iconLayoutType"
              :options="iconLayoutOption"
              :wrap-style="{flexWrap: 'wrap', justifyContent: 'flex-start'}"
              @input="handleValueChange"
            />
          </div>
        </div>
        <div
          v-if="setting.layoutType !== 'text-only'"
          :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']"
        >
          <div :class="[$style.title, 'hexagon__setting_title']">
            {{ $t('marketing_pd.commons.tbdx_ef79da') }}
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']">
            <Slider
              :value="setting.iconSize || 16"
              :min="12"
              :max="100"
              show-input
              :show-input-controls="false"
              input-size="mini"
              @input="handleIconSizeValueChange"
            />
          </div>
        </div>
        <div
          v-if="setting.layoutType !== 'icon-only'"
          :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']"
        >
          <div :class="[$style.title, 'hexagon__setting_title']">
            {{ $t('marketing_pd.components.setting.wzdx_93ab99') }}
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']">
            <Slider
              :value="setting.style.fontSize || 16"
              :min="12"
              :max="100"
              show-input
              :show-input-controls="false"
              input-size="mini"
              @input="handleFontSizeValueChange"
            />
          </div>
        </div>
        <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
          <div :class="[$style.title, 'hexagon__setting_title']">
            {{ $t('marketing_pd.commons.dqfs_d5bc35') }}
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']">
            <fx-select
              v-model="setting.textAlign"
              size="mini"
              :options=" [
                { value: 'left', label: $t('marketing_pd.commons.zdq_413f48'), },
                { value: 'center', label: $t('marketing_pd.commons.jzdq_56c17b'), },
                { value: 'column', label: $t('marketing_pd.components.setting.sxjz_906936'), },
              ]"
              @change="handleValueChange"
            />
          </div>
        </div>
      </Card>
      <Card
        v-if="setting.buttonList && setting.buttonList.length > 0"
        :open="true"
        title=""
      >
        <div
          slot="title"
          :class="[$style.titleTips]"
        >
          {{ $t('marketing_pd.components.setting.ansz_8b9e79') }}<span>{{ $t('marketing_pd.components.setting.bcgg_768e70') }}</span>
        </div>
        <Draggable

          :value="setting.buttonList"
          handle=".drag-handler"
          :options="{
            animation: 200,
            chosenClass: 'hexagon__com-chosen',
            dragClass: 'hexagon__com-drag',
          }"
          @input="handleComponentChange"
          @start="dragging = true"
          @end="dragging = false"
        >
          <SubCard
            v-for="(item, ind) in setting.buttonList"
            :key="ind"
            title=""
          >
            <div
              slot="title"
              :class="[$style.SubCardTitle]"
            >
              <div :class="[$style.DragHandlerIcon, 'drag-handler']">
                <img :src="dragIcon">
              </div>{{ $t('marketing_pd.commons.an_fa9663') }} {{ ind + 1 }}<div
                v-if="setting.buttonList.length > 1"
                :class="[$style.deleteIcon]"
                @click="handleDelete(ind)"
              >
                <i class="el-icon-delete" />
              </div>
            </div>
            <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
              <div :class="[$style.subTitle]">
                {{ $t('marketing_pd.commons.anmc_cf6e87') }}
              </div>
              <div :class="[$style.con, 'hexagon__setting_con']">
                <fx-input
                  v-model="item.name"
                  size="small"
                  @input="handleValueChange"
                />
              </div>
            </div>
            <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
              <div :class="[$style.subTitle]">
                {{ $t('marketing_pd.commons.bjs_2f97db') }}
              </div>
              <div :class="[$style.con, 'hexagon__setting_con']">
                <ColorPicker
                  v-model="item.style.background"
                  popper-class="hexagon__setting-colorpicker"
                  size="mini"
                  show-alpha
                  :predefine="predefineColors"
                  @change="handleValueChange"
                />
              </div>
            </div>
            <div
              v-if="setting.layoutType !== 'icon-only'"
              :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']"
            >
              <div :class="[$style.subTitle]">
                {{ $t('marketing_pd.components.setting.wzs_94e49c') }}
              </div>
              <div :class="[$style.con, 'hexagon__setting_con']">
                <ColorPicker
                  v-model="item.style.color"
                  popper-class="hexagon__setting-colorpicker"
                  size="mini"
                  show-alpha
                  :predefine="predefineColors"
                  @change="handleValueChange"
                />
              </div>
            </div>
            <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
              <div :class="[$style.subTitle]">
                {{ $t('marketing_pd.commons.bk_961534') }}
              </div>
              <div :class="[$style.con, 'hexagon__setting_con']">
                <MiniBorder
                  v-model="item.style"
                  @input="handleValueChange"
                />
              </div>
            </div>
            <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
              <div :class="[$style.subTitle]">
                {{ $t('marketing_pd.commons.yj_0103eb') }}
              </div>
              <div :class="[$style.con, 'hexagon__setting_con']">
                <Slider
                  v-model="item.style.borderRadius"
                  :min="0"
                  :max="100"
                  show-input
                  :show-input-controls="false"
                  input-size="mini"
                  @input="handleValueChange"
                />
              </div>
            </div>
            <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
              <div :class="[$style.subTitle]">
                {{ $t('marketing_pd.commons.djhdz_f91dcc') }}
              </div>
              <div :class="[$style.con, 'hexagon__setting_con']">
                <Action
                  v-model="item.action"
                  :compType="setting.type"
                  :title="''"
                  :name="setting.isFormComp ? 'form_action' : ''"
                  :exclude="setting.isFormComp ? ['miniprogram'] : []"
                  @input="handleValueChange"
                />
              </div>
            </div>
            <div
              v-if="setting.layoutType !== 'text-only'"
              :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']"
            >
              <div :class="[$style.subTitle]">
                {{ $t('marketing_pd.commons.sztb_912ec6') }}
              </div>
              <div :class="[$style.con, 'hexagon__setting_con']">
                <IconPicker
                  :value="item.iconInfo || {}"
                  :upload="upload"
                  :icon-layout="setting.iconLayoutType || 'line'"
                  @input="handleiItemIconInput($event, ind)"
                />
              </div>
            </div>
          </SubCard>
        </Draggable>
        <div
          v-if="setting.buttonList.length < 4"
          :class="[$style.addFileItem]"
          @click="addFileItem"
        >
          <i class="el-icon-plus" />{{ $t('marketing_pd.components.setting.tjan_c641fe') }}
        </div>
      </Card>
      <Card
        v-else
        :open="true"
        :title="$t('marketing_pd.components.setting.annr_5d0ae2')"
      >
        <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
          <div :class="[$style.subTitle]">
            {{ $t('marketing_pd.commons.anmc_cf6e87') }}
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']">
            <fx-input
              v-model="setting.name"
              size="small"
              @input="handleValueChange"
            />
          </div>
        </div>
        <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
          <div :class="[$style.subTitle]">
            {{ $t('marketing_pd.commons.bjs_2f97db') }}
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']">
            <ColorPicker
              v-model="setting.style.background"
              popper-class="hexagon__setting-colorpicker"
              size="mini"
              show-alpha
              :predefine="predefineColors"
              @change="handleValueChange"
            />
          </div>
        </div>
        <div
          v-if="setting.layoutType !== 'icon-only'"
          :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']"
        >
          <div :class="[$style.subTitle]">
            {{ $t('marketing_pd.components.setting.wzs_94e49c') }}
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']">
            <ColorPicker
              v-model="setting.style.color"
              popper-class="hexagon__setting-colorpicker"
              size="mini"
              show-alpha
              :predefine="predefineColors"
              @change="handleValueChange"
            />
          </div>
        </div>
        <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
          <div :class="[$style.subTitle]">
            {{ $t('marketing_pd.commons.bk_961534') }}
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']">
            <MiniBorder
              v-model="setting.style"
              @input="handleValueChange"
            />
          </div>
        </div>
        <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
          <div :class="[$style.subTitle]">
            {{ $t('marketing_pd.commons.yj_0103eb') }}
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']">
            <Slider
              v-model="setting.style.borderRadius"
              :min="0"
              :max="100"
              show-input
              :show-input-controls="false"
              input-size="mini"
              @input="handleValueChange"
            />
          </div>
        </div>
        <div
          v-if="setting.isFormComp"
          :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']"
        >
          <div :class="[$style.title, 'hexagon__setting_title']">
            {{ $t('marketing_pd.commons.tjcghwa_43c257') }}
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']">
            <fx-input
              v-model="setting.tip"
              size="small"
              :placeholder="$t('marketing_pd.commons.qsr_02cc4f')"
              maxlength="30"
              show-word-limit
              @input="handleValueChange"
            />
          </div>
        </div>
        <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
          <div :class="[$style.subTitle]">
            {{ $t('marketing_pd.commons.djtz_01db4f') }}
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']">
            <Action
              v-model="setting.action"
              :compType="setting.type"
              :title="$t('marketing_pd.commons.tjhdz_a31316')"
              :name="setting.isFormComp ? 'form_action' : ''"
              :exclude="setting.isFormComp ? ['miniprogram'] : []"
              @input="handleValueChange"
            />
          </div>
        </div>
        <div
          v-if="setting.layoutType !== 'text-only'"
          :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']"
        >
          <div :class="[$style.subTitle]">
            {{ $t('marketing_pd.commons.sztb_912ec6') }}
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']">
            <IconPicker
              :value="setting.iconInfo || {}"
              :upload="upload"
              :icon-layout="setting.iconLayoutType || 'line'"
              @input="handleIconInput"
            />
          </div>
        </div>
      </Card>
    </template>
  </div>
</template>
<script>
import { mapState, mapActions } from 'vuex'
import Draggable from 'vuedraggable'
import TextInput from '../../common/TextInput.vue'
import Content from '../components/Content.vue'
import Action from '../../common/action/index.js'
import IconPicker from '../../common/IconPicker.vue'
import LayoutSetting from '../../common/LayoutSetting.vue'
import Card from '../../common/Card.vue'
import SubCard from '../../common/SubCard.vue'
import { predefineColors } from '../../../utils/const.js'
import MiniBorder from '../../common/MiniBorder.vue'
import ColorPicker from '../../comp/components/ColorPicker.vue'

import iconLayoutLine from '$page-designer/assets/icon-layout-line.svg'
import iconLayoutFlat from '$page-designer/assets/icon-layout-flat.svg'
import iconLayoutCircle from '$page-designer/assets/icon-layout-circle.svg'
import buttonType1 from '$page-designer/assets/buttonType1.svg'
import buttonType2 from '$page-designer/assets/buttonType2.svg'
import dragIcon from '$page-designer/assets/drag.svg'

const LIVE_PLATFORM_ENUM = {
  VHALL: 1,
  OTHER: 2,
  XIAOE: 3,
  POLYV: 4,
  CHANNELS: 5,
  MUDU: 6,
}

export default {
  components: {
    ElSwitch: FxUI.Switch,
    Slider: FxUI.Slider,
    ColorPicker,
    TextInput,
    Action,
    IconPicker,
    LayoutSetting,
    Card,
    SubCard,
    MiniBorder,
    Draggable,
  },
  extends: Content,
  props: {
    upload: {
      type: Function,
      default: async () => false,
    },
  },
  data() {
    return {
      LIVE_PLATFORM_ENUM,
      dragIcon,
      predefineColors,
      iconLayoutOption: [
        { value: 'line', icon: iconLayoutLine },
        { value: 'flat', icon: iconLayoutFlat },
        { value: 'circle', icon: iconLayoutCircle },
      ],
      positionTypeOption: [
        {
          label: $t('marketing_pd.commons.jcan_f9622f'),
          value: 'none',
          icon: buttonType2,
        },
        {
          label: $t('marketing_pd.commons.dbxfan_292379'),
          value: 'fixed-bottom',
          icon: buttonType1,
        },
      ],
      eventFormOptions: [
        {
          label: $t('marketing_pd.commons.zb_7bbe8e'),
          value: 'live_marketing',
        },
        {
          label: $t('marketing_pd.commons.hy_0103eb'),
          value: 'conference_marketing',
        },
        {
          label: $t('marketing_pd.commons.hd_36c6f5'),
          value: 'online_marketing',
        },
      ],
    }
  },
  computed: {
    ...mapState('ActivityMarketing', ['liveDetail']),
    ...mapState('Member', ['isOpenMember']),
    status() {
      return this.setting.status || this.setting.liveStatus || 'before'
    },
    hasLayout() {
      if (this.editType === 'grid') {
        return false
      } if (this.editType === 'canvas') {
        const { layoutTabType } = this.$store.state.hexagon || {}
        return layoutTabType === 'flow'
      }
      return true
    },
    schedule() {
      return this.setting.schedule || this.setting.live || {}
    },
    scheduleTabs() {
      const { objectName = $t('marketing_pd.commons.zb_7bbe8e') } = this.setting || {}
      const suffixMap = {
        before: $t('marketing_pd.components.setting.q_d8cbd0'),
        processing: $t('marketing_pd.components.setting.z_aed1df'),
        after: $t('marketing_pd.components.setting.h_2038d4'),
      }
      return Object.keys(this.schedule).map(status => ({
        name: objectName + suffixMap[status],
        status,
      }))
    },
    // 判断该组件是否是预设活动主页模板组件
    isPresetActivityTemplate() {
      return this.$route.params.siteId === 'template' && this.$route.query.templateType === 'activityComp'
    },
    isTemplate() {
      return this.$route.params.siteId === 'template'
    },
    extendActions(){
      let data = []
      if(this.setting.typeValue === 'live' && this.liveDetail.livePlatform === this.LIVE_PLATFORM_ENUM.CHANNELS){
        data.push({
          label: $t('marketing_pd.components.setting.tzsphzb_089b9a'),
          value: 'gotoLive'
        })
      }
      // 模版场景下  后动作允许选择跳转直播地址
      if(this.setting.typeValue === 'marketingEventSignupbutton' && (this.setting.objectType === 'live_marketing' || this.setting.objectType === '!!event_form!!')){
        data.push({
          label: $t('marketing.components.Hexagon.tzzbdz_4bf07c'),
          value: 'gotoLiveAddress'
        })
      }
      return data
    }
  },
  watch: {
    'setting.marketingEventId': {
      handler(val) {
        if (val && this.setting.typeValue === 'live') {
          this.queryLiveDetail({
            id: val,
          })
        }
      },
    },
  },
  mounted() {
    if (this.setting.marketingEventId && this.setting.typeValue === 'live') {
      this.queryLiveDetail({
        id: this.setting.marketingEventId,
      })
    }
    // 为了适配旧数据没有wrapStyle 属性
    if (!this.setting.wrapStyle) {
      this.setting.wrapStyle = {
        position: 'none',
      }
      this.handleValueChange()
    }

    // 旧数据缺失marginLeft, marginRight导致PC端按钮不居中
    if (this.setting.isFormComp && this.setting.type === 'button') {
      const { marginLeft, marginRight } = this.setting.style
      if (marginLeft === undefined && marginRight === undefined) {
        this.setting.style.marginLeft = 15
        this.setting.style.marginRight = 15
      }

      this.handleValueChange()
    }
  },
  methods: {
    ...mapActions('ActivityMarketing', ['queryLiveDetail']),
    async beforeUpload({ target }) {
      const data = await this.upload({
        target,
        name: this.setting.name,
        type:
          this.setting.type === 'livebutton' ? 'live' : this.setting.typeValue,
        data: this.setting,
      })
      if (data) {
        this.setting = {
          ...this.setting,
          ...data,
          schedule: {
            ...this.schedule,
            ...data.schedule,
          },
        }
        console.log('this.setting哈哈哈哈哈哈', this.setting)
        this.handleValueChange()
      }
    },
    handleChangeLayout(type) {
      this.setting.position = type
      let wrapStyle = {}
      let style = {}
      switch (type) {
        case 'fixed-bottom':
          wrapStyle = {
            ...this.setting.wrapStyle,
            position: 'fixed',
            left: 0,
            bottom: 0,
            right: 0,
            zIndex: 1,
            paddingTop: 12,
            paddingLeft: 15,
            paddingRight: 15,
            paddingBottom: 28,
            // padding: '12px 12px 28px',
            background: '#ffffff',
          }
          style = {
            marginBottom: 0,
            marginLeft: 0,
            marginRight: 0,
            marginTop: 0,
          }
          break

        case 'none':
          wrapStyle = {
            position: 'relative',
          }
          style = {
            marginBottom: 15,
            marginLeft: 'auto',
            marginRight: 'auto',
            marginTop: 15,
          }
          break
        default:
          break
      }
      this.setting.wrapStyle = wrapStyle
      this.setting.style = {
        ...this.setting.style,
        ...style,
      }
      this.handleValueChange()
    },
    handleDelete(index) {
      if (this.setting.buttonList.length > 1) {
        this.setting.buttonList.splice(index, 1)
        this.handleValueChange()
      }
    },
    addFileItem() {
      if (this.setting.buttonList.length < 4) {
        this.setting.buttonList.push({
          name: $t('marketing_pd.config.an_fa9663'),
          iconInfo: {
            icon: '',
            iconType: 'iconfont',
            iconStyle: {
              color: '',
            },
          },
          action: {},
          style: {
            background: '#409EFF',
            color: '#ffffff',
            borderRadius: 3,
            borderWidth: 0,
            borderStyle: 'none',
            borderColor: '#e9edf5',
          },
        })
      }
    },
    handleiItemIconInput(val, index) {
      this.setting.buttonList[index].iconInfo = val
      this.handleValueChange()
    },
    handleComponentChange(val) {
      this.setting.buttonList = val
      this.handleValueChange()
    },
    handleMemberAutoSignupChange(value) {
      this.setting.memberAutoSignup = value
      this.handleValueChange()
    },
    handleMemberAutoSignupButtonChange(value) {
      this.setting.memberAutoSignupButton = value
      this.handleValueChange()
    },
    handleIconSizeValueChange(val) {
      this.setting.iconSize = val
      this.handleValueChange()
    },
    handleFontSizeValueChange(val) {
      this.setting.style.fontSize = val
      this.handleValueChange()
    },
    handleScheduleTabChange(status) {
      this.setting.status = status
      this.handleValueChange()
    },
    handleMemberAutoSignupButtonTextChange({ text, style }) {
      this.setting.memberAutoSignupButtonText = text
      this.setting.memberAutoSignupButtonStyle = style
      this.handleValueChange()
    },
    handleScheduleTextInputChange({ text, style }, status) {
      const scheduleData = this.schedule[this.status][status] || {}
      scheduleData.name = text
      scheduleData.style = {
        ...scheduleData.style,
        ...style,
      }
      this.setting.schedule = this.schedule
      this.handleValueChange()
    },
    handleIconInput(icon) {
      this.setting.iconInfo = icon
      this.handleValueChange()
    },
    handleLayoutTypeChange(type) {
      this.setting.layoutType = type
      this.handleValueChange()
    },
  },
}
</script>
<style lang="less" module>
.content {
  .picker {
    width: 100%;
    height: 32px;
    display: flex;
    border: 1px solid #e9edf5;
    box-sizing: border-box;
    border-radius: 3px;
    cursor: pointer;
    position: relative;
    .pickerEventHandle {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
    }
    input {
      flex: 1 0 auto;
      padding: 7px 0 7px 10px;
      border-color: transparent;
      cursor: pointer;
      border-right: 1px solid #e9edf5;
    }
    > div {
      &:last-child {
        width: 36px;
        height: 32px;
        text-align: center;
        line-height: 32px;
        > .el-icon-search {
          font-size: 16px;
          color: #b4b6c0;
          font-weight: 600;
        }
      }
    }
    .disabled {
      background-color: #f5f7fa;
      // border-color: #e4e7ed;
      color: #c0c4cc;
      cursor: not-allowed;
    }
  }
  .schedule {
    background-color: #fafafa;
    border-radius: 3px;
    border: 1px solid #e9edf5;
    &_tabs {
      display: flex;
      border-bottom: 1px solid #e9edf5;
      &_item {
        flex: 1;
        line-height: 40px;
        height: 40px;
        text-align: center;
        color: #91959e;
        font-size: 12px;
        box-sizing: border-box;
        cursor: pointer;
        &.active {
          color: #181c25;
          border-bottom: 3px solid var(--color-primary06, #407FFF);
        }
      }
    }
    &_con {
      padding: 12px;
      // :global {
      //   .hexagon__toolbar-tool {
      //     margin-left: 12px;
      //   }
      // }
    }
  }
  .autoSignupButton {
    background-color: #fafafa;
    border-radius: 3px;
    border: 1px solid #e9edf5;
    padding: 12px;
    // :global {
    //   .hexagon__toolbar-tool {
    //     margin-left: 12px;
    //   }
    // }
  }
  .layout {
    :global {
      .fx-radio {
        display: block;
        line-height: 22px;
      }
    }
  }
}
.SubCardTitle {
  display: flex;
  align-items: center;
  position: relative;
  width: 100%;
}
.DragHandlerIcon {
  height: 16px;
  cursor: pointer;
  padding-right: 4px;
  position: relative;
  left: -4px;
  img {
    width: 16px;
    height: 16px;
  }
}
.deleteIcon {
  position: absolute;
  right: 0;
  top: 0;
  cursor: pointer;
  color: rgb(64, 158, 255);
}
.addFileItem {
  width: 100%;
  height: 36px;
  border-radius: 3px;
  background: #F7F7F9;
  border: 1px solid #DEE1E8;
  color: #FF7C19;
  line-height: 36px;
  text-align: center;
  cursor: pointer;
}
.subTitle {
  width: 100px;
  margin-top: 0;
  padding: 4px 0;
  line-height: 20px;
  overflow: inherit;
  word-wrap: normal;
  text-overflow: initial;
  white-space: inherit;
}
.titleTips {
  span {
    color: #91959E;
    font-size: 12px;
    font-weight: 400;
  }
}
</style>
