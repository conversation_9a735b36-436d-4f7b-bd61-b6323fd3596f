<template>
  <div :class="$style.content">
    <Card
      :open="true"
      :title="$t('marketing_pd.commons.bj_5aefca')"
    >
      <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
        <div :class="[$style.con, 'hexagon__setting_con']">
          <GridLayout v-model="setting.gridLayout" @change="onGridLayoutChange"/>
        </div>
      </div>
      <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
        <div :class="[$style.title, 'hexagon__setting_title']">{{ $t('marketing.components.Hexagon.jj_60bac4') }}</div>
        <div :class="[$style.con, 'hexagon__setting_con']">
          <RadioGroup v-model="setting.gridGap" size="small" @change="handleValueChange">
            <Radio :label="0">{{ $t('marketing.components.Hexagon.x_391b8f') }}</Radio>
            <Radio :label="8">{{ $t('marketing.components.Hexagon.z_aed1df') }}</Radio>
            <Radio :label="12">{{ $t('marketing.components.Hexagon.d_ab18e3') }}</Radio>
          </RadioGroup>
        </div>
      </div>
      <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
        <div :class="[$style.title, 'hexagon__setting_title']">{{ $t('marketing.components.Hexagon.dqfs_d5bc35') }}</div>
        <div :class="[$style.con, 'hexagon__setting_con']">
          <div :class="$style.alignButtons">
            <div
              v-for="align in alignOptions"
              :key="align.value"
              :class="[
                $style.alignButton,
                setting.gridAlign === align.value ? $style.active : ''
              ]"
              @click="handleAlignChange(align.value)"
            >
              <div
                :class="$style.alignIcon"
                :style="{ maskImage: `url(${align.icon})`, WebkitMaskImage: `url(${align.icon})` }"
              ></div>
            </div>
          </div>
        </div>
      </div>
      <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
        <div :class="[$style.title, 'hexagon__setting_title']">{{ $t('marketing.components.Hexagon.rqzxgd_7a27fb') }}</div>
        <div :class="[$style.con, 'hexagon__setting_con']">
          <Slider
            v-model="setting.gridMinHeight"
            size="mini"
            :min="0"
            :max="1000"
            show-input
            :show-input-controls="false"
            input-size="mini"
            @change="handleValueChange"
          />
        </div>
      </div>
      <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
        <div :class="[$style.title, 'hexagon__setting_title']">
          {{ $t('marketing_pd.commons.bj_8e1b94') }}
        </div>
        <div :class="[$style.con, 'hexagon__setting_con']">
          <Background
            v-bind="$attrs"
            v-model="setting"
            v-on="$listeners"
            @input="handleValueChange"
          />
        </div>
      </div>
    </Card>
  </div>
</template>

<script>
import Card from '../../common/Card.vue'
import Style from '../components/Style.vue'
import Background from '../../common/Background.vue'
import GridLayout from '../../common/GridLayout.vue'

// 导入图标
import alignTopIcon from '../../../assets/icon/align-top-icon.svg'
import alignCenterIcon from '../../../assets/icon/align-middle-icon.svg'
import alignBottomIcon from '../../../assets/icon/align-bottom-icon.svg'

export default {
  components: {
    Slider: FxUI.Slider,
    Radio: FxUI.Radio,
    RadioGroup: FxUI.RadioGroup,
    Card,
    Background,
    ColorPicker: FxUI.ColorPicker,
    GridLayout,
  },
  extends: Style,
  data() {
    return {
      defaultGridSettings: {
        gridLayout: '1,1,1',  // 默认三等分布局
        gridGap: 0,          // 默认中等间距
        gridAlign: 'flex-start', // 默认顶部对齐
        gridMinHeight: 0,  // 默认最小高度
      },
      alignOptions: [
        { value: 'flex-start', icon: alignTopIcon, label: $t('marketing.components.Hexagon.dbdq_2a6ad2') },
        { value: 'center', icon: alignCenterIcon, label: $t('marketing.components.Hexagon.czjz_4117e8') },
        { value: 'flex-end', icon: alignBottomIcon, label: $t('marketing.components.Hexagon.dbdq_d68c21') }
      ]
    }
  },
  methods: {
    onGridLayoutChange(value) {
      this.handleValueChange();
    },
    handleAlignChange(value) {
      this.setting.gridAlign = value;
      this.handleValueChange();
    }
  },
  created() {
    // 初始化默认值
    if (!this.setting) {
      this.setting = {};
    }

    // 确保所有 grid 相关属性都有默认值
    Object.entries(this.defaultGridSettings).forEach(([key, defaultValue]) => {
      if (this.setting[key] === undefined) {
        this.$set(this.setting, key, defaultValue);
      }
    });
    this.handleValueChange();
  }
}
</script>

<style lang="less" module>
.content {
  .setting_item {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .title {
    font-size: 12px;
    color: #181C25;
    margin-bottom: 8px;
  }

  .con {
    width: 100%;
  }
}

.alignButtons {
  display: flex;
}

.alignButton {
  width: 38px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #E9EDF5;
  border-right: 1px solid transparent;
  border-radius: 2px;
  cursor: pointer;
  transition: all 0.2s;
  padding: 8px;
  background: #fff;
  color: #737C8C;

  &:first-child {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
  }

  &:last-child {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    border-right: 1px solid #E9EDF5;
  }

  &:hover {
    border: 1px solid var(--color-primary06, #407FFF);

    .alignIcon {
      background-color: var(--color-primary06, #407FFF);
    }
  }

  &.active {
    border: 1px solid var(--color-primary06, #407FFF);
    background: #fff;

    .alignIcon {
      background-color: var(--color-primary06, #407FFF);
    }
  }
}

.alignIcon {
  width: 16px;
  height: 16px;
  transition: all 0.2s;
  mask-repeat: no-repeat;
  mask-position: center;
  mask-size: contain;
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-position: center;
  -webkit-mask-size: contain;
  background-color: #737C8C;
}
</style>
