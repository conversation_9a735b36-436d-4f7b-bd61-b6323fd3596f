<template>
  <div :class="$style.content">
    <Card
      :open="true"
      :title="$t('marketing_pd.commons.bj_5aefca')"
    >
      <template
        v-if="isResetVisible"
        slot="title"
      >
        <fx-button
          :class="$style.reset"
          size="small"
          type="text"
          @click="handleResetStyle"
        >
          {{ $t('marketing_pd.components.setting.zzys_1e5685') }}
        </fx-button>
      </template>
      <div :class="[$style.setting_item, 'hexagon__setting_item']">
        <div :class="[$style.con, 'hexagon__setting_con']">
          <LayoutSetting
            v-model="setting.layout"
            :options="layoutOption"
            :wrap-style="{flexWrap: 'wrap', justifyContent: 'flex-start'}"
            :item-style="{flex: 'none', width: '30%', margin: '0 6px 10px 0'}"
            @input="handleValueChange"
          />
          <div :class="$style.tip">
            <template v-if="setting.layout === 'horizontal-scrolling-layout'">
              * {{ $t('marketing_pd.components.setting.gbjzdzsgqb_d10d28') }}
            </template>
          </div>
        </div>
      </div>
    </Card>

    <Card
      :open="true"
      :title="$t('marketing_pd.commons.kjys_2a8d5a', {data: ({'option0': typeName})})"
    >
      <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
        <div :class="[$style.title, 'hexagon__setting_title']">
          {{ $t('marketing_pd.components.setting.zbtys_2f44bb') }}
        </div>
        <div :class="[$style.con, 'hexagon__setting_con']">
          <ColorPicker
            v-model="setting.titleColor"
            popper-class="hexagon__setting-colorpicker"
            size="mini"
            show-alpha
            :predefine="predefineColors"
            @change="handleValueChange"
          />
        </div>
      </div>
      <div
        v-if="showDescColorSetting"
        :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']"
      >
        <div :class="[$style.title, 'hexagon__setting_title']">
          {{ $t('marketing_pd.components.setting.fbtys_9798c2') }}
        </div>
        <div :class="[$style.con, 'hexagon__setting_con']">
          <ColorPicker
            v-model="setting.descColor"
            popper-class="hexagon__setting-colorpicker"
            size="mini"
            show-alpha
            :predefine="predefineColors"
            @change="handleValueChange"
          />
        </div>
      </div>
      <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
        <div :class="[$style.title, 'hexagon__setting_title']">
          {{ $t('marketing_pd.components.setting.bjds_a586a7') }}
        </div>
        <div :class="[$style.con, 'hexagon__setting_con']">
          <ColorPicker
            v-model="setting.componentBackgroundColor"
            popper-class="hexagon__setting-colorpicker"
            size="mini"
            show-alpha
            :predefine="predefineColors"
            @change="handleComponentBackgroundColorChange"
          />
        </div>
      </div>
    </Card>

    <Card
      v-if="isShowSearchSwitch"
      :open="true"
      :title="$t('marketing_pd.components.comp.ss_e5f71f')"
    >
      <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
        <div :class="[$style.title, 'hexagon__setting_title']">
          {{ $t('marketing_pd.components.setting.ssgn_e31cad') }}
        </div>
        <div :class="[$style.con, 'hexagon__setting_con']">
          <el-switch
            v-model="setting.enableSearch"
            size="small"
            @change="handleValueChange"
          />
        </div>
      </div>
      <template v-if="setting.enableSearch">
        <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
          <div :class="[$style.title, 'hexagon__setting_title']">
            {{ $t('marketing_pd.components.setting.ssys_4c419a') }}
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']">
            <LayoutSetting
              v-model="setting.searchLayout"
              :options="searchLayoutOption"
              @input="handleValueChange"
            />
          </div>
        </div>
        <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
          <div :class="[$style.title, 'hexagon__setting_title']">
            {{ $t('marketing_pd.components.setting.sskntswz_e7fcfe') }}
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']">
            <fx-input
              v-model="setting.searchPlaceholder"
              size="mini"
              @input="handleValueChange"
            />
          </div>
        </div>
        <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
          <div :class="[$style.title, 'hexagon__setting_title']">
            {{ $t('marketing_pd.components.setting.sskds_1a5293') }}
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']">
            <ColorPicker
              v-model="setting.searchBackgroundColor"
              popper-class="hexagon__setting-colorpicker"
              size="mini"
              show-alpha
              :predefine="predefineColors"
              @change="handleValueChange"
            />
          </div>
        </div>
        <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
          <div :class="[$style.title, 'hexagon__setting_title']">
            {{ $t('marketing_pd.components.setting.ssbkys_24db6b') }}
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']">
            <ColorPicker
              v-model="setting.searchBorderColor"
              popper-class="hexagon__setting-colorpicker"
              size="mini"
              show-alpha
              :predefine="predefineColors"
              @change="handleValueChange"
            />
          </div>
        </div>
        <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
          <div :class="[$style.title, 'hexagon__setting_title']">
            {{ $t('marketing_pd.components.setting.sswztbys_a8309f') }}
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']">
            <ColorPicker
              v-model="setting.searchFontColor"
              popper-class="hexagon__setting-colorpicker"
              size="mini"
              show-alpha
              :predefine="predefineColors"
              @change="handleValueChange"
            />
          </div>
        </div>
        <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
          <div :class="[$style.title, 'hexagon__setting_title']">
            {{ $t('marketing_pd.components.setting.sszwfys_3e0bb5') }}
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']">
            <ColorPicker
              v-model="setting.searchPlaceholderFontColor"
              popper-class="hexagon__setting-colorpicker"
              size="mini"
              show-alpha
              :predefine="predefineColors"
              @change="handleValueChange"
            />
          </div>
        </div>
      </template>
    </Card>

    <Card
      :open="true"
      :title="$t('marketing_pd.components.setting.zsnr_bdb25e')"
    >
      <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
        <div :class="[$style.title, 'hexagon__setting_title']">
          {{ $t('marketing_pd.commons.xzzsfw_c4c4cf', {data: ({'option0': typeName})}) }}
        </div>
        <div :class="[$style.con, 'hexagon__setting_con']">
          <fx-select
            v-model="setting.range"
            size="mini"
            :placeholder="$t('marketing_pd.commons.qxzzsfw_bab3c3', {data: ({'option0': typeName})})"
            :is-option-group="setting.type === 'content'"
            :options="eventsOption"
            @change="handleTypeChange"
          />
          <fx-select
            v-if="setting.range === 'all' && isLegacyData"
            v-model="setting.marketingEventTypes"
            size="mini"
            style="margin-top: 10px"
            multiple
            :options="[
              { value: 'conference', label: $t('marketing_pd.commons.hy_ebcb81') },
              { value: 'live', label: $t('marketing_pd.commons.zb_7bbe8e') },
              { value: 'multivenue_marketing', label: $t('marketing.commons.dhchd_aacab5') },
            ]"
            @change="handleValueChange"
          />
          <fx-select
            v-else-if="!isLegacyData && setting.range === 'all'"
            v-model="setting.marketingEventForms"
            size="mini"
            style="margin-top: 10px"
            multiple
            :options="[
              { value: 'conference_marketing', label: $t('marketing_pd.commons.hy_ebcb81') },
              { value: 'live_marketing', label: $t('marketing_pd.commons.zb_7bbe8e') },
              { value: 'multivenue_marketing', label: $t('marketing.pages.setting.dhdzh_0bf57e') },
              { value: 'online_marketing', label: $t('marketing.commons.xsyx_6c9e91') },
            ]"
            @change="handleValueChange"
          />
          <div
            v-if="setting.range.indexOf('custom') !== -1"
            :class="$style.event_picker"
          >
            <div
              v-if="setting.range == 'custom-product-more' || setting.range == 'custom-article-more' || setting.range == 'custom-site-more'"
              :class="$style.add"
              @click="handlePickMarketingEvent"
            >
              <i class="el-icon-plus" />{{ $t('marketing_pd.components.setting.szlbzsfz_c92b6d') }}
            </div>
            <div
              v-if="setting.range != 'custom-product-more'
                && setting.range != 'custom-article-more'
                && setting.range != 'custom-site-more'
                && setting.range != 'custom-article-single'
                && setting.range != 'custom-product-single'"
              :class="$style.add"
              @click="handlePickMarketingEvent"
            >
              <i class="el-icon-plus" />{{ $t('marketing_pd.commons.tjzd_bdfb36', {data: ({'option0': typeName})}) }}
            </div>
          </div>
        </div>
      </div>
    </Card>
  </div>
</template>
<script>
import Content from '../components/Content.vue'
import LayoutSetting from '../../common/LayoutSetting.vue'
import Card from '../../common/Card.vue'
import ColorPicker from '../../comp/components/ColorPicker.vue'
import { getDefaultConfigByValue } from '../../../config/components.js'

import bigImageCardLayout from '$page-designer/assets/big-image-card-layout.svg'
import singleRowCardLayout from '$page-designer/assets/single-row-card-layout.svg'
import doubleRowCardLayout from '$page-designer/assets/double-row-card-layout.svg'
import cardListLayout from '$page-designer/assets/card-list-layout.svg'
import listLayout from '$page-designer/assets/list-layout.svg'
import horizontalScrollingLayout from '$page-designer/assets/horizontal-scrolling-layout.svg'
import iconSearchSmallRound from '$page-designer/assets/icon-search-small-round.svg'
import iconSearchBigRound from '$page-designer/assets/icon-search-big-round.svg'

export const DEFAULT_SETTING = {
  'big-image-card-layout': {
    titleColor: '#FFFFFF',
    componentBackgroundColor: '#FFFFFF',
    searchBackgroundColor: '#F2F3F5',
    searchBorderColor: '#FFFFFF',
    searchFontColor: '#181C25',
    searchPlaceholderFontColor: '#C1C5CE',
    style: {
      borderStyle: 'none',
      borderWidth: 0,
      borderColor: '#e9edf5',
      borderRadius: 0,
      paddingTop: 8,
      paddingRight: 8,
      paddingBottom: 8,
      paddingLeft: 8,
      gap: 8,
      boxShadow: '0px 0px 0px rgba(0,0,0,.1)',
      boxShadowLeft: 0,
      boxShadowTop: 0,
      boxShadowRadius: 0,
      boxShadowColor: 'rgba(0,0,0,.1)',
    },
  },
  column: {
    titleColor: '#181C25',
    descColor: '#91959E',
    componentBackgroundColor: '#F0F1F3',
    searchBackgroundColor: '#FFFFFF',
    searchBorderColor: '#FFFFFF',
    searchFontColor: '#181C25',
    searchPlaceholderFontColor: '#C1C5CE',
    style: {
      borderStyle: 'none',
      borderWidth: 0,
      borderColor: '#e9edf5',
      borderRadius: 0,
      paddingTop: 8,
      paddingRight: 8,
      paddingBottom: 8,
      paddingLeft: 8,
      gap: 8,
      boxShadow: '0px 0px 0px rgba(0,0,0,.1)',
      boxShadowLeft: 0,
      boxShadowTop: 0,
      boxShadowRadius: 0,
      boxShadowColor: 'rgba(0,0,0,.1)',
    },
  },
  grid: {
    titleColor: '#181C25',
    componentBackgroundColor: '#F0F1F3',
    searchBackgroundColor: '#FFFFFF',
    searchBorderColor: '#FFFFFF',
    searchFontColor: '#181C25',
    searchPlaceholderFontColor: '#C1C5CE',
    style: {
      borderStyle: 'none',
      borderWidth: 0,
      borderColor: '#e9edf5',
      borderRadius: 3,
      paddingTop: 10,
      paddingRight: 10,
      paddingBottom: 10,
      paddingLeft: 10,
      gap: 10,
      boxShadow: '0px 0px 0px rgba(0,0,0,.1)',
      boxShadowLeft: 0,
      boxShadowTop: 0,
      boxShadowRadius: 0,
      boxShadowColor: 'rgba(0,0,0,.1)',
    },
  },
  'card-list-layout': {
    titleColor: '#181C25',
    descColor: '#91959E',
    componentBackgroundColor: '#F0F1F3',
    searchBackgroundColor: '#FFFFFF',
    searchBorderColor: '#FFFFFF',
    searchFontColor: '#181C25',
    searchPlaceholderFontColor: '#C1C5CE',
    style: {
      borderStyle: 'none',
      borderWidth: 0,
      borderColor: '#e9edf5',
      borderRadius: 0,
      paddingTop: 8,
      paddingRight: 0,
      paddingBottom: 8,
      paddingLeft: 0,
      gap: 8,
      boxShadow: '0px 0px 0px rgba(0,0,0,.1)',
      boxShadowLeft: 0,
      boxShadowTop: 0,
      boxShadowRadius: 0,
      boxShadowColor: 'rgba(0,0,0,.1)',
    },
  },
  row: {
    titleColor: '#181C25',
    descColor: '#91959E',
    componentBackgroundColor: '#FFFFFF',
    searchBackgroundColor: '#F2F3F5',
    searchBorderColor: '#FFFFFF',
    searchFontColor: '#181C25',
    searchPlaceholderFontColor: '#C1C5CE',
    style: {
      borderColor: '#DEE1E8',
      paddingTop: 8,
      paddingRight: 0,
      paddingBottom: 0,
      paddingLeft: 0,
      gap: 0,
    },
  },
  'horizontal-scrolling-layout': {
    titleColor: '#181C25',
    descColor: '#91959E',
    componentBackgroundColor: '#FFFFFF',
    searchBackgroundColor: '#F2F3F5',
    searchBorderColor: '#FFFFFF',
    searchFontColor: '#181C25',
    searchPlaceholderFontColor: '#C1C5CE',
    style: {
      borderStyle: 'none',
      borderWidth: 0,
      borderColor: '#e9edf5',
      borderRadius: 6,
      paddingTop: 20,
      paddingRight: 16,
      paddingBottom: 20,
      paddingLeft: 16,
      gap: 10,
      boxShadow: '0px 0px 10px rgba(0,0,0,.1)',
      boxShadowLeft: 0,
      boxShadowTop: 0,
      boxShadowRadius: 10,
      boxShadowColor: 'rgba(0,0,0,.1)',
    },
  },
}

let settingCache = {}

export default {
  components: {
    ColorPicker,
    LayoutSetting,
    ElSwitch: FxUI.Switch,
    Card,
  },
  extends: Content,
  props: {
    upload: {
      type: Function,
      default: async () => false,
    },
  },
  data() {
    return {
      isLegacyData: false,
      layoutOption: [
        {
          label: $t('marketing_pd.components.setting.dtkpbj_3f75c9'),
          value: 'big-image-card-layout',
          icon: bigImageCardLayout,
        },
        {
          label: $t('marketing_pd.components.setting.dpkpbj_7687fe'),
          value: 'column',
          icon: singleRowCardLayout,
        },
        {
          label: $t('marketing_pd.components.setting.spkpbj_9ccf87'),
          value: 'grid',
          icon: doubleRowCardLayout,
        },
        {
          label: $t('marketing_pd.components.setting.kplbbj_8e784a'),
          value: 'card-list-layout',
          icon: cardListLayout,
        },
        {
          label: $t('marketing_pd.components.setting.lbbj_f1d5f5'),
          value: 'row',
          icon: listLayout,
        },
        {
          label: $t('marketing_pd.components.setting.hxhdbj_470588'),
          value: 'horizontal-scrolling-layout',
          icon: horizontalScrollingLayout,
        },
      ],
      searchOpenOptionMap: [
        'site', 'custom-site-more', 'article', 'custom-article-single', 'custom-article-more', 'product', 'custom-product-single', 'custom-product-more', 'filter',
      ],
      searchLayoutOption: [
        {
          label: $t('marketing_pd.components.setting.xyj_f8c782'),
          value: 'small-round',
          icon: iconSearchSmallRound,
        },
        {
          label: $t('marketing_pd.components.setting.dyj_e0882b'),
          value: 'big-round',
          icon: iconSearchBigRound,
        },
      ],
    }
  },
  computed: {
    typeName() {
      return this.setting.type === 'content' ? $t('marketing_pd.commons.nr_2d711b') : $t('marketing_pd.commons.hd_36c6f5')
    },
    eventsOption() {
      const { type, layout } = this.setting
      /**
       * 内容组件可选项
       */
      if (type === 'content') {
        return [
          {
            label: $t('marketing_pd.components.setting.wym_5fd4fb'),
            options: [
              { label: $t('marketing_pd.components.setting.dgfzwym_be020a'), value: 'site' },
              { label: $t('marketing_pd.components.setting.mzbqwym_0f04b1'), value: 'site-tags' },
              { label: $t('marketing.commons.dgfzwym_a53623'), value: 'custom-site-more', disabled: layout === 'horizontal-scrolling-layout' },
              { label: $t('marketing_pd.components.setting.zdjtwym_071c39'), value: 'custom-site' },
            ],
          },
          {
            label: $t('marketing_pd.commons.wz_c75625'),
            options: [
              { label: $t('marketing_pd.components.setting.sywz_b82ef9'), value: 'article' },
              { label: $t('marketing_pd.components.setting.dgfzwz_1860e3'), value: 'custom-article-single' },
              { label: $t('marketing_pd.components.setting.dgfzwz_3e586a'), value: 'custom-article-more', disabled: layout === 'horizontal-scrolling-layout' },
              { label: $t('marketing_pd.components.setting.mzbqwz_d6b62e'), value: 'article-tags' },
              { label: $t('marketing_pd.components.setting.zdwz_6e2095'), value: 'custom-article' },
            ],
          },
          {
            label: $t('marketing_pd.commons.cp_a01543'),
            options: [
              { label: $t('marketing_pd.components.setting.sycp_167620'), value: 'product' },
              { label: $t('marketing_pd.components.setting.dgfzcp_7b5039'), value: 'custom-product-single' },
              { label: $t('marketing_pd.components.setting.dgfzcp_636c4a'), value: 'custom-product-more', disabled: layout === 'horizontal-scrolling-layout' },
              { label: $t('marketing_pd.components.setting.mzbqcp_135952'), value: 'product-tags' },
              { label: $t('marketing_pd.components.setting.zdjtcp_980a18'), value: 'custom-product' },
            ],
          },
        ]
      }
      return [
        {
          label: $t('marketing_pd.components.setting.syhd_aab1a3'),
          value: 'all',
        },
        {
          label: $t('marketing.commons.mztjhd_aacab5'),
          value: 'filter',
        },
        {
          label: $t('marketing_pd.components.setting.mzbqhd_37c6cc'),
          value: 'activity-tags',
        },
        {
          label: $t('marketing_pd.components.setting.zdhd_242a3e'),
          value: 'custom',
        },
      ]
    },
    isShowSearchSwitch() {
      return this.setting.layout !== 'horizontal-scrolling-layout'
    },
    showDescColorSetting() {
      return ['column', 'card-list-layout', 'row', 'horizontal-scrolling-layout'].indexOf(this.setting.layout) > -1
    },
    isResetVisible() {
      const { layout } = this.setting
      const settingValue = this.getSettingValue(layout)
      const isEqual = (obj1, obj2) => {
        const keys = Object.keys(obj1)
        for (let i = 0; i < keys.length; i += 1) {
          const key = keys[i]
          if (typeof obj1[key] === 'object') {
            return isEqual(obj1[key], obj2[key])
          }

          if (obj1[key] !== obj2[key]) {
            return false
          }
        }

        return true
      }

      return !isEqual(settingValue, DEFAULT_SETTING[layout])
    },
  },
  watch: {
    'setting.layout': function (newVal, oldVal) {
      const settingValue = this.getSettingValue(oldVal)
      settingCache[oldVal] = settingValue

      this.setSettingValue(newVal)

      // 在多分组情况下，选择横向滚动布局，重置选项，因为横向滚动布局不支持多分组
      if (newVal === 'horizontal-scrolling-layout' && this.setting.range === 'custom-product-more') {
        this.setting.range = 'product'
      }
      if (newVal === 'horizontal-scrolling-layout' && this.setting.range === 'custom-article-more') {
        this.setting.range = 'article'
      }
      if (newVal === 'horizontal-scrolling-layout' && this.setting.range === 'custom-site-more') {
        this.setting.range = 'site'
      }
      this.handleValueChange()
    },
    'setting.range': function (newVal, oldVal) {
      if (newVal === 'custom-product-more' || newVal === 'custom-article-more' || newVal === 'custom-site-more') {
        this.setting.style.paddingLeft = 0
        this.setting.style.paddingTop = 0
        this.setting.classification = []
      }

      if (oldVal === 'custom-product-more' || oldVal === 'custom-article-more' || oldVal === 'custom-site-more') {
        const {
          style: {
            paddingLeft = 0,
            paddingTop = 0,
          },
        } = DEFAULT_SETTING[this.setting.layout]

        this.setting.style.paddingLeft = paddingLeft
        this.setting.style.paddingTop = paddingTop
      }

      this.handleValueChange()
    },
  },
  mounted() {
    settingCache = {}

    const { type,marketingEventTypes,range } = this.setting
    console.log('this.settingbeforee',this.setting)
    const defaultConfig = getDefaultConfigByValue(type)
    this.mergeConfig(this.setting, defaultConfig.config)
    // 判断是否为历史数据格式
    this.isLegacyData = marketingEventTypes && 
      Array.isArray(marketingEventTypes) && 
      marketingEventTypes.length > 0 && 
      range === 'all';
    this.handleValueChange()
  },
  destroyed() {
    settingCache = {}
  },
  methods: {
    handleTypeChange() {
      // if(!this.isShowSearchSwitch){
      //   this.setting.enableSearch = false;
      // }else{
      //   this.setting.enableSearch = true;
      // }
      const { type, range = '' } = this.setting
      if (type === 'content') {
        let contentObjectType
        if (range.indexOf('site') !== -1) {
          contentObjectType = 26
        } else if (range.indexOf('article') !== -1) {
          contentObjectType = 6
        } else if (range.indexOf('product') !== -1) {
          contentObjectType = 4
        }
        this.setting.contentObjectType = contentObjectType
        this.setting.contentObjectIds = []
        this.setting.siteGroupId = '-1'
      } else {
        this.setting.marketingEventIds = []
      }
      this.setting.items = []
      this.handleValueChange()
    },
    async handlePickMarketingEvent({ target }) {
      const { type } = this.setting
      const data = await this.upload({
        target,
        name: type,
        type,
        data: this.setting,
      })
      if (data) {
        this.setting = {
          ...this.setting,
          ...data,
          // classification: [
          //   {
          //     key: '66653545436',
          //     label: '分类1'
          //   },
          //   {
          //     key: '66653545436',
          //     label: '分类2'
          //   },
          // ]
        }
        this.handleValueChange()
      }
    },
    handleComponentBackgroundColorChange(color) {
      this.setting.componentBackgroundColor = color
      this.handleValueChange()
    },

    getSettingValue(layoutType) {
      const defaultSetting = DEFAULT_SETTING[layoutType]
      const copy = (keys, origin) => {
        const target = {}
        keys.forEach(key => {
          if (typeof origin[key] !== 'object') {
            target[key] = origin[key]
          } else {
            target[key] = copy(Object.keys(defaultSetting[key]), origin[key])
          }
        })

        return target
      }

      return copy(Object.keys(defaultSetting), this.setting)
    },

    setSettingValue(layoutType) {
      const cacheSetting = settingCache[layoutType] || DEFAULT_SETTING[layoutType]
      const copy = (target, origin) => {
        const keys = Object.keys(origin)
        keys.forEach(key => {
          if (typeof origin[key] !== 'object') {
            target[key] = origin[key]
          } else {
            copy(target[key], origin[key])
          }
        })
      }

      if (cacheSetting) {
        copy(this.setting, cacheSetting)
      }
    },

    handleResetStyle() {
      const { layout } = this.setting
      settingCache[layout] = undefined

      this.setSettingValue(layout)
      this.handleValueChange()
    },
  },
}
</script>
<style lang="less" module>
.content {
  .reset {
    font-size: 12px;
    padding: 0;
  }
  .style_item {
    margin-bottom: 15px;
    font-size: 12px;
  }
  .flex_item {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .event_picker {
    margin-top: 10px;
    .add {
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--color-info06,#407FFF);
      font-size: 13px;
      border: 1px solid #e9edf5;
      cursor: pointer;
      i {
        font-size: 15px;
        color: var(--color-info06,#407FFF);
        font-weight: bold;
        margin-right: 5px;
      }
    }
  }
  .search_switch{
    margin-top: 10px;
    display: flex;
    padding-left: 15px;
    span{
      font-size: 12px;
      margin-right: 5px;
    }
  }

  .tip {
    font-size: 12px;
    line-height: 18px;
    color: #91959E;
    margin-top: 10px;
  }
}
</style>
