<template>
  <div :class="$style.content">
    <!-- 活动封面不需要展示布局 -->
    <Card
      :open="true"
      v-if="data.fieldName !== 'marketingEventCover'"
      :title="$t('marketing_pd.commons.bj_5aefca')"
    >
      <div :class="[$style.setting_item, 'hexagon__setting_item']">
        <div :class="[$style.con, 'hexagon__setting_con']">
          <LayoutSetting
            v-model="imageType"
            :options="imageOption"
            :disabled="setting.noEdit"
            @input="handleChangeLayout"
          />
        </div>
      </div>
    </Card>
    <Card
      :open="true"
      :title="$t('marketing_pd.components.setting.tpnr_67d0dc')"
    >
      <div :class="$style.imageWrap">
        <div
          v-for="(item, i) in setting.images"
          :key="i"
          :class="$style.image"
        >
          <Collapse
            :open="true"
            :title="$t('marketing_pd.commons.tp_20def7')+ (i + 1)"
          >
            <div
              v-if="!setting.fieldName"
              :class="[$style.setting_item, 'hexagon__setting_item']"
            >
              <div
                :class="[$style.title, 'hexagon__setting_title']"
                style="margin-bottom: 15px"
              >
                <!-- 上传图片（{{ ++i }}） -->
                <RadioGroup
                  v-model="item.uploadType"
                  @change="handleValueChange"
                >
                  <Radio label="upload">
                    {{ $t('marketing_pd.commons.sctp_ce6855') }}
                  </Radio>
                  <Radio label="url">
                    {{ $t('marketing_pd.commons.tpdz_f7a82c') }}
                  </Radio>
                </RadioGroup>
              </div>
              <div
                v-if="item.uploadType === 'url'"
                :class="[$style.con, 'hexagon__setting_con']"
              >
                <Input
                  v-model="item.url"
                  size="small"
                  :placeholder="$t('marketing_pd.commons.qsrtpdz_6af1e9')"
                  @change="handleValueChange"
                />
              </div>
              <div
                v-else
                :class="[$style.con, 'hexagon__setting_con']"
              >
                <Uploader
                  v-bind="$attrs"
                  v-model="item.url"
                  style="width: 82px;"
                  :disabled="setting.noEdit"
                  v-on="$listeners"
                  @uploaded="data => handleImageUploaded(data, i)"
                />
              </div>
            </div>
            <Action
              v-model="item.action"
              :compType="setting.type"
              @input="handleValueChange"
            />
          </Collapse>
        </div>
      </div>
    </Card>
    <Card
      v-if="!previewSettingDisabled"
      :open="true"
      :title="$t('marketing.components.Hexagon.zs_027446')"
    >
      <div
        :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']"
      >
        <div
          :class="[$style.title, 'hexagon__setting_title']"
          style="display: flex; align-items: center;"
        >
          <span>{{ $t('marketing.components.Hexagon.djhkfd_355b35') }}</span>
        </div>
        <div :class="[$style.con, 'hexagon__setting_con']">
          <fx-switch
            v-model="setting.previewEnable"
            size="small"
            :disabled="previewSettingDisabled"
            @change="handleValueChange"
          />
        </div>
      </div>
    </Card>
  </div>
</template>
<script>
import Card from '../../common/Card.vue'
import Content from '../components/Content.vue'
import Collapse from '../../common/Collapse.vue'
import Uploader from '../../common/Uploader.vue'
import Action from '../../common/action/index.js'
import LayoutSetting from '../../common/LayoutSetting.vue'
import picture1 from '../../../assets/picture_1.jpg'
import picture2 from '../../../assets/picture_2.jpg'
import picture3 from '../../../assets/picture_3.jpg'

export default {
  components: {
    Collapse,
    Action,
    Uploader,
    RadioGroup: FxUI.RadioGroup,
    Radio: FxUI.Radio,
    Input: FxUI.Input,
    LayoutSetting,
    Card,
  },
  extends: Content,
  data() {
    return {
      qrcodeType: 0,
      maxWidth: 375,
      imageOption: [
        {
          label: $t('marketing_pd.commons.dt_16d199'),
          icon: picture1,
          value: 1,
        },
        {
          label: $t('marketing_pd.components.setting.st_fc05ec'),
          icon: picture2,
          value: 2,
        },
        {
          label: $t('marketing_pd.components.setting.st_8069ae'),
          icon: picture3,
          value: 3,
        },
      ],
      imageType: this.data.images.length,
    }
  },
  computed: {
    previewSettingDisabled() {
      const hasActive = this.setting.images.find(item => (item.action && item.action.type))
      return !!hasActive
    },
  },
  watch: {
    imageType: {
      deep: true,
      handler(val) {
        const images = []
        for (let index = 0; index < val; index += 1) {
          const element = this.setting.images[index]
          if (element) {
            images.push(element)
          } else {
            images.push({
              url: '',
              uploadType: 'upload',
              action: {},
            })
          }
        }
        this.setting.images = images
      },
    },
    previewSettingDisabled() {
      if (this.previewSettingDisabled) {
        this.setting.previewEnable = false
        this.handleValueChange()
      }
    },
  },
  methods: {
    handleImageUploaded({ width, height, url }, i) {
      // 单图首次上传自适应
      if (this.imageType === 1) {
        const { style } = this.setting
        height = parseInt(((style.width || this.maxWidth) / width) * height, 10)
        width = style.width || this.maxWidth
        this.setting.style = {
          ...style,
          width,
          height,
        }
      }
      this.handleValueChange()
    },
    handleChangeLayout() {
      this.$nextTick(() => this.handleValueChange())
    },
  },
}
</script>
<style lang="less" module>
.content {
  .imageWrap {
    .image {
      border-radius: 3px;
      // background-color: #fafafa;
      // padding: 20px 15px;
      // padding-bottom: 10px;
      // margin-bottom: 10px;
      // border: 1px solid #eaeef5;
    }
  }
  :global {
    .upload__box{
        background-color: #fff;
      }
    .image-uploader .el-upload {
      border: 1px dashed #ddd;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }
    .image-uploader .el-upload:hover {
      border-color: var(--color-primary06,#ff8000);
    }
    .image-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 138px;
      height: 138px;
      line-height: 138px;
      text-align: center;
    }
    .image {
      width: 138px;
      height: 138px;
      display: block;
    }
  }
  // .upload{
  //   margin-top: 10px;
  //   height: 149px;
  //   :global{
  //     .hexagon__uploader,.upload__box, .image__uploader{
  //       width: 100%;
  //       height: 100%;
  //     }
  //   }
  // }
}
</style>
