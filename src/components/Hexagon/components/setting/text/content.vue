<template>
  <div :class="$style.content">
    <Card
      :open="true"
      :title="$t('marketing_pd.commons.nr_2d711b')"
    >
    <!-- 活动详情是富文本格式 -->
      <div
        v-if="(setting.fieldName && setting.fieldName !== 'marketingEventDetail')"
        :class="[$style.setting_item, 'hexagon__setting_item']"
      >
        <div :class="[$style.title, 'hexagon__setting_title']">
          {{ $t('marketing_pd.components.setting.zdys_d4027d') }}
        </div>
        <div :class="[$style.con, 'hexagon__setting_con']">
          <TextInput
            :value="{ text: setting.value, style: setting.style }"
            :disabled="true"
            @input="handleTitleTextInput"
          />
        </div>
      </div>
      <!-- 活动详情是富文本格式，并且是实际的活动 -->
      <div v-else-if="setting.fieldName === 'marketingEventDetail' && conferenceId" :class="[$style.con,$style.conConference, 'hexagon__setting_con']">
          <div :class="[$style.title, 'hexagon__setting_title']">
            <fx-button
              type="primary"
              size="small"
              style="width: 100%;"
              @click="handleEditEventDetail"
            >
            {{ $t('marketing_pd.components.setting.bjhyxq_e7b8d4') }}
          </fx-button>
          </div>
      </div>
      <div
        v-else
        :class="[$style.setting_item, 'hexagon__setting_item']"
      >
        <div :class="[$style.con, 'hexagon__setting_con']">
          <Editor
            ref="editor"
            :key="setting.id"
            v-model="setting.value"
            :colors="predefineColors"
            :disabled="setting.noEdit"
            @text-change="handleValueChange"
          />
        </div>
      </div>
      <Action
        v-model="setting.action"
        name="text_action"
        @input="handleValueChange"
      />
    </Card>
  </div>
</template>
<script>
import Card from '../../common/Card.vue'
import TextInput from '../../common/TextInput.vue'
import { Editor } from '../../common/editor/index.js'
import Action from '../../common/action/index.js'
import { predefineColors } from '../../../utils/const.js'
import Content from '../components/Content.vue'

export default {
  components: {
    TextInput,
    Editor,
    Action,
    Card,
  },
  extends: Content,
  data() {
    return {
      predefineColors,
    }
  },
  props: {
    upload: {
      type: Function,
      default: async () => false,
    },
  },
  computed: {
    conferenceId() {
      return this.$route.query.conferenceId
    },
  },
  methods: {
    async handleEditEventDetail({ target }) {
      const data = await this.upload({
        target,
        name: 'conference_content',
        type: 'conference_content',
        data: {
          ...this.setting,
          article: {
            content: this.setting.value,
          }
        },
      })
      if (data && data.article) {
        this.setting = {
          ...this.setting,
          value: decodeURIComponent(data.article.content),
          conferenceId: this.conferenceId,
        }
        console.log('handleEditEventDetail', this.setting)
        this.handleValueChange()
      }
    },
  },

}
</script>
<style lang="less" module>
.content {
}
.conConference{
  margin: 12px 0;
}
</style>
