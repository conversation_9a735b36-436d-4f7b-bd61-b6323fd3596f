<template>
  <div :class="$style.content">
    <!-- 市场活动容器特有设置 -->
    <Card
      v-if="setting.typeValue === 'activity-comp' && !isPresetActivityTemplate"
      :open="true"
      :title="$t('marketing_pd.commons.nr_2d711b')"
    >
      <!-- 活动详情容器特有设置 -->
      <template>
        <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
          <div :class="[$style.title, 'hexagon__setting_title']">
            {{ $t('marketing.commons.xzhd_a05f05') }}
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']">
            <div :class="$style.picker">
              <input
                type="text"
                disabled
                :value="setting.marketingEventTitle || ''"
              />
              <div>
                <span class="el-icon-search" />
                <div
                  :class="$style.pickerEventHandle"
                  @click="handleSelectActivity"
                />
              </div>
            </div>
          </div>
        </div>
      </template>
    </Card>
    <Card
      v-if="['form-container', 'auto-container','activity-container'].includes(container.key)"
      :title="$t('marketing_pd.components.setting.bjbj_3b4f23')"
    >
      <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
        <LayoutSetting
          :value="container.typesetting || 'absolute'"
          :options="container.typeValue === 'form' ? formLayoutOption : formLayoutOptionNoForm"
          type="text"
          :wrap-style="{flexWrap: 'wrap', justifyContent: 'flex-start'}"
          :item-style="{flex: 'none', width: '100%', margin: '0 0 10px 0'}"
          :disabled="!isCanUsedFormLayout"
          @input="handleContainerTypeChange"
        />
      </div>
      <p
        v-if="!isCanUsedFormLayout"
        :class="$style.update_tip"
      >
        {{ $t('marketing_pd.commons.qhbdbjxygx_afcde3') }}，<a @click="handleUpdate">{{ $t('marketing_pd.commons.qwsj_afcd23') }}</a>
      </p>
    </Card>
    <Card
      v-if="container.typeValue === 'step-form'"
      :title="$t('marketing_pd.components.setting.tjgz_064a7c')"
    >
      <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
        <div :class="[$style.title, 'hexagon__setting_title']">
          {{ $t('marketing_pd.components.setting.kqfbtj_3ed230') }}
        </div>
        <div :class="[$style.con, 'hexagon__setting_con']">
          <fx-switch
            v-model="setting.isStepSubmit"
            size="small"
            @change="handleValueChange"
          />
        </div>
      </div>
    </Card>
    <Card
      v-if="setting.key === 'tab-container' && editType === 'canvas'"
      :title="$t('marketing_pd.components.setting.bjbj_3b4f23')"
    >
      <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
        <div :class="[$style.title, 'hexagon__setting_title']">
          {{ $t('marketing_pd.commons.bj_5aefca') }}
        </div>
        <div :class="[$style.con, 'hexagon__setting_con']">
          <fx-select
            v-model="setting.tabs[setting.slideIndex].layout"
            :disabled="container.components && container.components.length > 0"
            size="mini"
            :options="[
              { value: 'list', label: $t('marketing_pd.components.setting.sjlbbj_6f8577') },
              { value: 'canvas', label: $t('marketing_pd.commons.zdybj_fe6488') },
            ]"
            @change="handleContainerTypesettingChange"
          />
        </div>
      </div>
    </Card>

    <Card
      v-if="
        setting.key !== 'tab-container' ||
          (setting.key === 'tab-container' && editType === 'canvas')
      "
      :open="true"
      :title="$t('marketing_pd.commons.wg_afcde2')"
    >
      <div
        v-if="container.key === 'form-container' && container.typesetting === 'flow'"
        :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']"
      >
        <div :class="[$style.title, 'hexagon__setting_title']">
          {{ $t('marketing_pd.components.setting.bdbj_faf4e7') }}
        </div>
        <div :class="[$style.con, 'hexagon__setting_con']">
          <LayoutSetting
            v-model="setting.formLayout"
            :options="formLayoutTypeOption"
            @input="handleFormLayoutValueChange"
          />
        </div>
      </div>
      <div
        v-if="container.typesetting !== 'flow'"
        :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']"
      >
        <div :class="[$style.title, 'hexagon__setting_title']">
          {{ $t('marketing_pd.commons.gd_c1df04') }}
        </div>
        <div :class="[$style.con, 'hexagon__setting_con']">
          <Slider
            v-model="container.style.height"
            :min="30"
            :max="10000"
            show-input
            :show-input-controls="false"
            input-size="mini"
            @change="handleContainerChange"
          />
        </div>
      </div>
      <div
        v-if="container.isVisual"
        :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']"
      >
        <div :class="[$style.title, 'hexagon__setting_title']">
          {{ $t('marketing_pd.components.setting.sfsygdbjjz_73f9e6') }}
        </div>
        <div :class="[$style.con, 'hexagon__setting_con']">
          <fx-switch
            v-model="container.visual"
            size="small"
          />
        </div>
      </div>
      <div
        v-if="container.isVisual && container.visual"
        :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']"
      >
        <div :class="[$style.title, 'hexagon__setting_title']">
          {{ $t('marketing_pd.components.setting.ksqy_516bf9') }}
        </div>
        <div :class="[$style.con, 'hexagon__setting_con']">
          <Slider
            v-model="container.visualStyle.height"
            :min="30"
            :max="container.style.height"
            show-input
            :show-input-controls="false"
            input-size="mini"
            @change="handleContainerChange"
          />
        </div>
      </div>

      <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
        <div :class="[$style.title, 'hexagon__setting_title']">
          {{ $t('marketing_pd.commons.bj_8e1b94') }}
        </div>
        <div :class="[$style.con, 'hexagon__setting_con']">
          <Background
            v-bind="$attrs"
            v-model="container"
            v-on="$listeners"
            @input="handleContainerChange"
          />
        </div>
      </div>
    </Card>
    <template v-if="setting.key === 'tab-container' && editType !== 'canvas'">
      <Card :title="$t('marketing_pd.components.setting.dhnr_6b72ab')">
        <div :class="[$style.setting_item, 'hexagon__setting_item']">
          <div :class="[$style.title, 'hexagon__setting_title']">
            {{ $t('marketing_pd.commons.bj_5aefca') }}
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']">
            <LayoutSetting
              v-model="setting.tabLayout"
              :options="layoutOption"
              :wrap-style="{flexWrap: 'wrap', justifyContent: 'flex-start'}"
              :item-style="{flex: 'none', width: '100%', margin: '0 0 10px 0'}"
              @input="handleValueChange"
            />
          </div>
        </div>
        <template v-if="setting.tabLayout === 'capsule'">
          <div :class="[$style.setting_item, 'hexagon__setting_item']">
            <div :class="[$style.title, 'hexagon__setting_title']">
              {{ $t('marketing_pd.components.setting.jnwx_495106') }}
            </div>
            <div :class="[$style.con, 'hexagon__setting_con']">
              <LayoutSetting
                v-model="setting.capsuleType"
                :options="capsuleTypeOption"
                @input="handleValueChange"
              />
            </div>
          </div>
        </template>
      </Card>
      <Card :title="$t('marketing_pd.components.setting.dhys_0fcdb9')">
        <template v-if="setting.tabLayout === 'underline'">
          <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
            <div :class="[$style.title, 'hexagon__setting_title']">
              {{ $t('marketing_pd.commons.fgx_8c47ec') }}
            </div>
            <div :class="[$style.con, 'hexagon__setting_con']">
              <fx-switch
                v-model="setting.enableDividingLine"
                size="small"
                @change="handleValueChange"
              />
            </div>
          </div>
          <div
            v-if="setting.enableDividingLine"
            :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']"
          >
            <div :class="[$style.title, 'hexagon__setting_title']">
              {{ $t('marketing_pd.commons.fgxys_53d085') }}
            </div>
            <div :class="[$style.con, 'hexagon__setting_con']">
              <ColorPicker
                v-model="setting.dividingLineColor"
                popper-class="hexagon__setting-colorpicker"
                size="mini"
                show-alpha
                :predefine="predefineColors"
                @change="handleValueChange"
              />
            </div>
          </div>
          <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
            <div :class="[$style.title, 'hexagon__setting_title']">
              {{ $t('marketing_pd.components.setting.xztxhxys_7cbe17') }}
            </div>
            <div :class="[$style.con, 'hexagon__setting_con']">
              <fx-select
                v-model="setting.underlineType"
                size="mini"
                :options="[
                  { value: 'long-line', label: $t('marketing_pd.components.setting.zx_77dba3') },
                  { value: 'middle-line', label: $t('marketing_pd.components.setting.zzx_8e23bb') },
                  { value: 'short-line-center', label: $t('marketing_pd.components.setting.dxjz_7f5a07') },
                  { value: 'short-line-left', label: $t('marketing_pd.components.setting.dxjz_0df03d') },
                ]"
                @change="handleValueChange"
              />
            </div>
          </div>
        </template>

        <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
          <div :class="[$style.title, 'hexagon__setting_title']">
            {{ $t('marketing_pd.components.setting.dhztys_47e2b5') }}
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']">
            <ColorPicker
              v-model="setting.tabColor"
              popper-class="hexagon__setting-colorpicker"
              size="mini"
              show-alpha
              :predefine="predefineColors"
              @change="handleValueChange"
            />
          </div>
        </div>
        <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
          <div :class="[$style.title, 'hexagon__setting_title']">
            {{ $t('marketing_pd.components.setting.dhztdx_190bf7') }}
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']">
            <Slider
              v-model="setting.tabFontSize"
              size="mini"
              :min="0"
              :max="100"
              show-input
              :show-input-controls="false"
              input-size="mini"
              @change="handleValueChange"
            />
          </div>
        </div>

        <div
          v-if="setting.tabLayout === 'capsule'"
          :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']"
        >
          <div :class="[$style.title, 'hexagon__setting_title']">
            {{ $t('marketing_pd.components.setting.dhjnys_063d8d') }}
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']">
            <ColorPicker
              v-model="setting.tabCapsuleColor"
              popper-class="hexagon__setting-colorpicker"
              size="mini"
              show-alpha
              :predefine="predefineColors"
              @change="handleValueChange"
            />
          </div>
        </div>

        <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
          <div :class="[$style.title, 'hexagon__setting_title']">
            {{ $t('marketing_pd.components.setting.dhxzzys_283c8a') }}
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']">
            <ColorPicker
              v-model="setting.tabActiveColor"
              popper-class="hexagon__setting-colorpicker"
              size="mini"
              show-alpha
              :predefine="predefineColors"
              @change="handleValueChange"
            />
          </div>
        </div>

        <div
          v-if="setting.tabLayout === 'capsule'"
          :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']"
        >
          <div :class="[$style.title, 'hexagon__setting_title']">
            {{ $t('marketing_pd.components.setting.dhxzjnys_dd0c2b') }}
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']">
            <ColorPicker
              v-model="setting.tabActiveCapsuleColor"
              popper-class="hexagon__setting-colorpicker"
              size="mini"
              show-alpha
              :predefine="predefineColors"
              @change="handleValueChange"
            />
          </div>
        </div>

        <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
          <div :class="[$style.title, 'hexagon__setting_title']">
            {{ $t('marketing_pd.components.setting.dhbjys_048e32') }}
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']">
            <ColorPicker
              v-model="setting.tabBackgroundColor"
              popper-class="hexagon__setting-colorpicker"
              size="mini"
              show-alpha
              :predefine="predefineColors"
              style="float:right"
              @change="handleValueChange"
            />
          </div>
        </div>
        <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
          <div :class="[$style.title, 'hexagon__setting_title']">
            {{ $t('marketing_pd.components.setting.dhbjgd_6256cd') }}
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']">
            <Slider
              v-model="setting.tabHeight"
              size="mini"
              :min="40"
              :max="375"
              show-input
              :show-input-controls="false"
              input-size="mini"
              @change="handleValueChange"
            />
          </div>
        </div>
      </Card>

      <Card :title="$t('marketing_pd.components.setting.dhcd_ff36f6')">
        <div :class="[$style.setting_item, 'hexagon__setting_item']">
          <div :class="[$style.con, 'hexagon__setting_con']">
            <div :class="$style.tabs">
              <Draggable
                v-model="setting.tabs"
                handle=".tabitem-handle"
                @change="handleTabChange"
              >
                <div
                  v-for="(item, index) in setting.tabs"
                  :key="item.value"
                  :class="$style.tabItem"
                >
                  <img
                    :class="[$style.drag, 'tabitem-handle']"
                    :src="require('../../../assets/drag.png')"
                  >
                  <Input
                    v-show="item.editing"
                    ref="tabInput"
                    v-model="item.label"
                    :class="$style.input"
                    size="mini"
                    :maxlength="10"
                    show-word-limit
                  />
                  <div
                    v-show="!item.editing"
                    :class="$style.title"
                  >
                    {{ item.label }}
                  </div>
                  <div :class="$style.opts">
                    <template v-if="!item.editing">
                      <span @click="handleTabEdit(index)">{{ $t('marketing_pd.components.setting.bj_95b351') }}</span>
                      <!-- <Popover
                    title="确定移除吗？"
                    @confirm="handleTabRemove(index)"
                  > -->
                      <span
                        v-show="setting.tabs.length > 1"
                        slot="reference"
                        @click="handleTabRemove(index)"
                      >{{ $t('marketing_pd.commons.yc_86048b') }}</span>
                    <!-- </Popover> -->
                    </template>
                    <template v-else>
                      <span @click="handleTabEditSave(index)">{{ $t('marketing_pd.commons.bc_be5fbb') }}</span>
                    </template>
                  </div>
                </div>
              </Draggable>
              <div
                :class="[$style.tabItem, $style.add]"
                @click="handleTabAdd"
              >
                <i class="el-icon-plus" />{{ $t('marketing_pd.components.setting.tjdh_4b742d') }}
              </div>
            </div>
          </div>
        </div>
      </Card>
    </template>

    <footer style="padding: 0 14px 20px;">
      <div
        v-if="container.key !== 'form-container' && setting.key !== 'tab-container'"
        :class="[$style.setting_item, 'hexagon__setting_item']"
      >
        <div :class="[$style.con, 'hexagon__setting_con']">
          <Action
            v-model="container.action"
            :title="$t('marketing_pd.commons.djsj_6ad26b')"
            name="list_action"
            @input="handleContainerChange"
          />
        </div>
      </div>
      <div
        v-if="layoutTabValue === 'page'"
        :class="[$style.setting_item, 'hexagon__setting_item']"
      >
        <div :class="[$style.con, 'hexagon__setting_con']">
          <Button
            type="primary"
            size="mini"
            @click="handleEditCanvas"
          >
            {{ $t('marketing_pd.components.setting.bjbj_3b4f23') }}
          </Button>
        </div>
      </div>
    </footer>
  </div>
</template>
<script>
import { mapActions, mapState } from 'vuex'
import Draggable from 'vuedraggable'
import Card from '../../common/Card.vue'
import Style from '../components/Style.vue'
import Action from '../../common/action/index.js'
import Background from '../../common/Background.vue'
import LayoutSetting from '../../common/LayoutSetting.vue'
import ColorPicker from '../../comp/components/ColorPicker.vue'
import { genContainerConfig, genStepButtonConfig, genDefaultFrom } from '../../../config/index.js'
import { deepClone, arrayTool } from '../../../utils/index.js'

import capsuleLayout from '$page-designer/assets/capsule-layout.svg'
import underlineLayout from '$page-designer/assets/underline-layout.svg'
import iconSearchSmallRound from '$page-designer/assets/icon-search-small-round.svg'
import iconSearchBigRound from '$page-designer/assets/icon-search-big-round.svg'

import formlayout1 from '$page-designer/assets/formlayout1.svg'
import formlayout2 from '$page-designer/assets/formlayout2.svg'

const DEFAULT_SETTING = {
  underline: {
    enableDividingLine: true,
    dividingLineColor: '#DEE1E8',
    underlineType: 'long-line',
    tabColor: '#181C25',
    tabActiveColor: '#F86E30',
    tabBackgroundColor: '#FFFFFF',
    tabHeight: 40,
    tabFontSize: 16,
  },
  capsule: {
    tabColor: '#91959E',
    tabCapsuleColor: '#EFEFEF',
    tabActiveColor: '#FFFFFF',
    tabActiveCapsuleColor: '#0C6CFF',
    tabBackgroundColor: '#FFFFFF',
    tabHeight: 40,
    tabFontSize: 16,
    capsuleType: 'big-round',
  },
}

// 版本大小比较
const compareVersion = (v1, v2) => {
  const v1Arr = v1.split('.')
  const v2Arr = v2.split('.')
  for (let i = 0; i < v1Arr.length; i += 1) {
    if (i >= v2Arr.length) {
      return 1
    }
    const num1 = parseInt(v1Arr[i], 10)

    const num2 = parseInt(v2Arr[i], 10)
    if (num1 > num2) {
      return 1
    } if (num1 < num2) {
      return -1
    }
  }
  if (v1Arr.length > v2Arr.length) {
    return -1
  }
  return 0
}

let settingCache = {}

export default {
  components: {
    Draggable,
    Slider: FxUI.Slider,
    Card,
    Button: FxUI.Button,
    ColorPicker,
    Input: FxUI.Input,
    Action,
    Background,
    LayoutSetting,
  },
  extends: Style,
  data() {
    return {
      container: {
        style: {},
        visualStyle: {
          visual: false,
        },
      },
      formLayoutOption: [
        {
          label: $t('marketing_pd.commons.jyms_afs56cde1'),
          desc: $t('marketing_pd.commons.djzcbdhzdwx_afcde1'),
          value: 'flow',
        },
        {
          label: $t('marketing_pd.commons.zdybjms_af61cde1'),
          desc: $t('marketing_pd.commons.dbdysyjgyq_afcde166'),
          value: 'absolute',
        },
      ],
      formLayoutOptionNoForm: [
        {
          label: $t('marketing_pd.commons.jyms_afs56cde1'),
          desc: $t('marketing.components.Hexagon.djzczjzdwx_d08251'),
          value: 'flow',
        },
        {
          label: $t('marketing_pd.commons.zdybjms_af61cde1'),
          desc: $t('marketing.components.Hexagon.dbjysyjgyq_04ac54'),
          value: 'absolute',
        },
      ],
      layoutOption: [
        {
          label: '',
          value: 'underline',
          icon: underlineLayout,
        },
        {
          label: '',
          value: 'capsule',
          icon: capsuleLayout,
        },
      ],
      capsuleTypeOption: [
        {
          label: $t('marketing_pd.components.setting.xyj_f8c782'),
          value: 'small-round',
          icon: iconSearchSmallRound,
        },
        {
          label: $t('marketing_pd.components.setting.dyj_e0882b'),
          value: 'big-round',
          icon: iconSearchBigRound,
        },
      ],
      formLayoutTypeOption: [
        {
          label: $t('marketing_pd.commons.lbbj_f1d5f5'),
          value: 'default',
          icon: formlayout1,
        },
        {
          label: $t('marketing_pd.components.setting.kpbj_e5384e'),
          value: 'card',
          icon: formlayout2,
        },
      ],
    }
  },
  computed: {
    ...mapState('hexagon', {
      layoutTabValue: 'layoutTabValue',
      supportFlowFormMpVersion: 'supportFlowFormMpVersion',
    }),
    ...mapState('ActivityMarketing', ['marketingEventDetail']),
    miniappInfo() {
      return this.$store.state.MiniappInfo || {}
    },
    isCanUsedFormLayout() {
      const { isCustomMiniappOpen, miniappInfo } = this.miniappInfo || {}
      if (!isCustomMiniappOpen) {
        return true
      }
      return miniappInfo.currentCodeVersion && compareVersion(miniappInfo.currentCodeVersion, this.supportFlowFormMpVersion) >= 0
    },
    bg() {
      const url = this.container.style.backgroundImage
      const matched = (url && url.match(/url\((.*)\)/)) || ''
      if (matched) {
        return matched[1]
      }
      return ''
    },
    isPresetActivityTemplate() {
      return this.$route.params.siteId === 'template'
    },
  },
  props: {
    upload: {
      type: Function,
      default: async () => false,
    },
  },
  watch: {
    data: {
      deep: true,
      handler() {
        this.setContainerData()
      },
    },
    'container.visual': {
      handler() {
        this.handleContainerChange()
      },
    },
    'setting.tabLayout': function (newVal, oldVal) {
      if (oldVal) {
        const settingValue = this.getSettingValue(oldVal)
        settingCache[oldVal] = settingValue
      }

      this.setSettingValue(newVal)
      this.handleValueChange()
    },
  },
  async mounted() {
    settingCache = {}
    this.setContainerData()

    const { key, tabLayout } = this.setting
    if (key === 'tab-container' && tabLayout === undefined) {
      this.setting.tabLayout = 'underline'
    }
  },
  destroyed() {
    settingCache = {}
  },
  methods: {
    ...mapActions('hexagon', {
      addTab: 'addTab',
      editTab: 'editTab',
    }),
    ...mapActions('ActivityMarketing', ['getMarketingEventDetailById']),

    handleUpdate() {
      const route = this.$router.resolve({
        name: 'miniapp-setting',
      })
      window.open(route.href, '_blank')
    },
    handleContainerTypeChange(type) {
      const { components } = this.container || {}
      if (type === this.container.typesetting) {
        return
      }
      const triggerContainerChange = () => {
        this.handleContainerTypesettingChange(type === 'flow' ? 'list' : type)
      }

      // 切换编辑模式时，如果已添加组件，提示清空
      if (components && components.length > 0) {
        this.$confirm($t('marketing_pd.commons.qhbjmshytjdzjjbqk_afcds6de3'), $t('marketing_pd.commons.ts_02d981'), {
          confirmButtonText: $t('marketing_pd.commons.qd_38cf16'),
          cancelButtonText: $t('marketing_pd.commons.qx_625fb2'),
          type: 'warning',
        }).then(() => {
          this.container.components = []
          // 如果是分步表单，那么默认加上分步按钮且不可删除
          if (this.container.typeValue === 'step-form') {
            const newBtn = genStepButtonConfig({
              hasBackBtn: this.setting.slideIndex !== 0,
            })
            const newStyle = {
              height: 45,
              width: 345,
              left: 16,
              top: 165,
              position: 'absolute',
              margin: '0 auto',
            }
            if (type === 'flow') {
              // newStyle.margin = '15px auto';
              newStyle.marginLeft = 'auto'
              newStyle.marginRight = 'auto'
              newStyle.marginTop = 15
              newStyle.marginBottom = 15
              delete newStyle.margin
              delete newStyle.position
              delete newStyle.left
              delete newStyle.top
            }
            this.container.components = [
              {
                ...newBtn,
                style: {
                  ...newStyle,
                },
              },
            ]
          }else if(this.container.typeValue === 'form') {
            // 简易布局与自定义布局互相切换时，也默认给【姓名】【手机号】【提交】三个默认组件，且，提交按钮不许删除
            this.container.components = genDefaultFrom(type);
          }
          triggerContainerChange()
        })
      } else {
        triggerContainerChange()
      }
    },
    handleContainerTypesettingChange(value) {
      let typesetting = ''
      if (value === 'list') {
        typesetting = 'flow'
        this.$set(this.container, 'style', {
          ...this.container.style,
          height: 'auto',
        })
      } else {
        this.$set(this.container, 'style', {
          ...this.container.style,
          height: 235,
        })
        typesetting = 'absolute'
      }
      this.$set(this.container, 'typesetting', typesetting)
      const { id, typeValue } = this.data
      this.editTab({
        name: id,
        data: {
          type: typesetting === 'flow' ? 'flow' : typeValue,
        },
      })
      this.handleContainerChange()
    },
    handleTabEdit(index) {
      const { tabs } = this.setting
      this.$set(this.setting.tabs, index, {
        ...tabs[index],
        editing: true,
      })
      this.$nextTick(() => {
        this.$refs.tabInput[index].focus()
      })
    },
    handleTabEditSave(index) {
      const { tabs } = this.setting
      this.$set(this.setting.tabs, index, {
        ...tabs[index],
        editing: false,
      })
      this.handleValueChange()
    },
    handleTabChange({ moved }) {
      arrayTool.move(this.setting.components, moved.oldIndex, moved.newIndex)
      this.handleValueChange()
    },
    handleTabRemove(index) {
      const { tabs } = this.setting
      const { components } = this.setting
      tabs.splice(index, 1)
      this.$set(
        this.setting,
        'tabs',
        tabs.map((item, i) => ({ ...item, value: i })),
      )

      components.splice(index, 1)
      this.$set(this.setting, 'components', components)
      if (this.setting.slideIndex >= components.length) {
        this.$set(this.setting, 'slideIndex', 0)
      }
      this.handleValueChange()
    },
    handleTabAdd() {
      const { tabs } = this.setting
      const index = tabs.length
      this.setting.tabs.push({
        label: `${$t('marketing_pd.commons.bq_14d342')}${index + 1}`,
        value: index,
        editing: true,
      })
      // 添加默认内容区
      this.setting.components.push(
        genContainerConfig({
          id: +new Date(),
          name: $t('marketing_pd.commons.zdybj_fe6488'),
          key: 'auto-container',
          typeValue: 'auto',
          typesetting: 'flow', // 新增的tab布局默认为数据列表布局
        }),
      )
      this.handleValueChange()
      this.$nextTick(() => {
        this.$refs.tabInput[index].focus()
      })
    },
    handleImageUploaded(url) {
      this.container.style.backgroundImage = url ? `url(${url})` : ''
      if (url) this.container.style.backgroundColor = ''
      this.handleContainerChange()
    },
    handleBgColorChange() {
      this.container.style.backgroundImage = ''
      this.handleContainerChange()
    },
    handleFormLayoutValueChange(val) {
      if (this.setting.layout === 'multiple') {
        this.setting.components.map(item => {
          item.formLayout = val
        })
        this.container.components.map(item => {
          item.formLayout = val
        })
      }
      this.container.formLayout = val
      this.handleContainerChange()
    },
    handleFillTypeChange() {
      // if (this.container.fillType === "color") {
      //   this.container.style.backgroundImage = "";
      // } else {
      //   this.container.style.backgroundColor = "";
      // }
      this.handleContainerChange()
    },
    setContainerData() {
      const data = this.setting
      if (data.layout === 'multiple') {
        this.container = data.components[data.slideIndex]
      } else {
        this.container = data
      }
    },
    handleEditCanvas() {
      const {
        id, sort, typesetting, layout, slideIndex, components,
      } = this.data
      let type = typesetting
      if (layout === 'multiple') {
        try {
          type = components[slideIndex].typesetting === 'flow' ? 'flow' : 'auto'
        } catch (error) { /* empty */ }
      }
      this.addTab({
        name: String(id),
        index: Number(sort),
        type,
      })
    },
    handleContainerChange() {
      const { layout, slideIndex } = this.setting

      if (layout === 'multiple') {
        this.setting.components[slideIndex] = deepClone({
          ...this.setting.components[slideIndex],
          ...this.container,
        })
        this.setting.style = {
          ...this.setting.style,
          ...this.container.style,
        }
      } else {
        this.setting = {
          ...this.setting,
          ...this.container,
        }
      }
      this.handleValueChange()
    },
    getSettingValue(layoutType) {
      const defaultSetting = DEFAULT_SETTING[layoutType]
      const copy = (keys, origin) => {
        const target = {}
        keys.forEach(key => {
          if (typeof origin[key] !== 'object') {
            target[key] = origin[key]
          } else {
            target[key] = copy(Object.keys(defaultSetting[key]), origin[key])
          }
        })

        return target
      }

      return copy(Object.keys(defaultSetting), this.setting)
    },
    setSettingValue(layoutType) {
      const cacheSetting = settingCache[layoutType] || DEFAULT_SETTING[layoutType]
      const copy = (target, origin) => {
        const keys = Object.keys(origin)
        keys.forEach(key => {
          if (typeof origin[key] !== 'object') {
            target[key] = origin[key]
          } else {
            copy(target[key], origin[key])
          }
        })
      }

      if (cacheSetting) {
        copy(this.setting, cacheSetting)
      }
    },
    async handleSelectActivity({ target }) {
      const data = await this.upload({
        target,
        name: this.setting.name,
        type: 'activity-comp',
        data: this.setting,
      })
      
      if (data) {
        // 获取活动详情
        await this.getMarketingEventDetailById({
          id: data.objectId
        })
        // 更新容器配置
        this.setting = {
          ...this.setting,
          marketingEventId: data.marketingEventId,
          marketingEventTitle: data.marketingEventTitle,
        }
        // 查找并更新子组件中的报名按钮配置 - 传入选择活动时的额外数据
        this.updateActivityCompConfig(data)
        this.handleValueChange()
      }
    },
    // 添加新方法用于更新报名按钮配置
    updateActivityCompConfig(data) {
      // 递归查找报名按钮组件
      const findAndUpdateSignupButton = (components) => {
        if (!components) return
        components.forEach(comp => {
          // 检查是否是报名按钮组件 传入了data的时候，说明是选择市场活动 需要更新报名按钮配置
          if (comp.type === 'signupbutton' && comp.typeValue === 'marketingEventSignupbutton' && data) {
            // 更新报名按钮配置
            const newConfig = {
              ...comp,
              ...data,
            }
            Object.assign(comp, newConfig)
          } else if(comp.fieldName === 'marketingEventCover' 
            || comp.fieldName === 'channelsAvatar' 
          ){
            const newConfig = {
                ...comp,
                images: [{
                  url: this.marketingEventDetail[comp.fieldName] || comp.images[0].url
                }]
              }
              Object.assign(comp, newConfig)
          } else if(comp.fieldName === 'marketingEventTitle' ||
            comp.fieldName === 'marketingEventTime' ||
            comp.fieldName === 'marketingEventDetail' ||
            comp.fieldName === 'channelsName'
          ){
            const newConfig = {
              ...comp,
              value: this.marketingEventDetail[comp.fieldName] || comp.value
            }
            Object.assign(comp, newConfig)
          } else if( comp.fieldName === 'marketingEventAddress'){
            const newConfig = {
              ...comp,
              value: this.marketingEventDetail[comp.fieldName] || comp.value,
              isShowMap: this.marketingEventDetail.isShowMap || false
            }
            Object.assign(comp, newConfig)
          }
          // 递归处理子组件
          if (comp.components) {
            findAndUpdateSignupButton(comp.components)
          }
        })
        if(!data) {
          this.handleValueChange()
        }
      }
      // 从容器组件开始查找
      if (this.setting.components) {
        findAndUpdateSignupButton(this.setting.components)
      }
    }
  },
}
</script>
<style lang="less" module>
.content {
  .tabs {
    .tabItem {
      height: 50px;
      display: flex;
      align-items: center;
      border-radius: 3px;
      background-color: #fff;
      border: 1px solid rgba(233, 237, 245, 1);
      margin-bottom: 10px;
      .drag {
        width: 8px;
        height: 13px;
        margin-left: 5px;
        cursor: pointer;
      }
      .input {
        flex: 1;
        margin: 0 5px;
        :global {
          .el-input__inner {
            font-size: 13px;
            color: #181c25;
          }
        }
      }
      .title {
        flex: 1;
        padding: 0 5px;
        font-size: 13px;
        color: #181c25;
      }
      .opts {
        span {
          color: var(--color-info06,#407FFF);
          font-size: 12px;
          margin-right: 10px;
          cursor: pointer;
        }
      }

      &.add {
        color: var(--color-info06,#407FFF);
        font-size: 13px;
        text-align: center;
        justify-content: center;
        cursor: pointer;
        i {
          font-size: 14px;
          font-weight: bold;
          margin-right: 5px;
        }
      }
    }
  }
.picker {
    width: 100%;
    height: 32px;
    display: flex;
    border: 1px solid #e9edf5;
    box-sizing: border-box;
    border-radius: 3px;
    cursor: pointer;
    position: relative;
    .pickerEventHandle {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
    }
    input {
      flex: 1 0 auto;
      padding: 7px 0 7px 10px;
      border-color: transparent;
      cursor: pointer;
      border-right: 1px solid #e9edf5;
    }
    > div {
      &:last-child {
        width: 36px;
        height: 32px;
        text-align: center;
        line-height: 32px;
        > .el-icon-search {
          font-size: 16px;
          color: #b4b6c0;
          font-weight: 600;
        }
      }
    }
    .disabled {
      background-color: #f5f7fa;
      // border-color: #e4e7ed;
      color: #c0c4cc;
      cursor: not-allowed;
    }
  }
}

</style>
