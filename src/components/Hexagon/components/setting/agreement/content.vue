<template>
  <div :class="$style.content">
    <Card :title="$t('marketing_pd.commons.nr_2d711b')">
      <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
        <div :class="[$style.title, 'hexagon__setting_title']">
          {{ $t('marketing_pd.commons.zdmc_e99641') }}
          <Tooltip
            effect="dark"
            :content="$t('marketing_pd.commons.zdmcyyysxs_050cf6')"
            placement="top"
          >
            <i class="el-icon-info" />
          </Tooltip>
        </div>
        <div :class="[$style.con, 'hexagon__setting_con']">
          <fx-input
            v-model="setting.name"
            size="small"
            :placeholder="$t('marketing_pd.commons.qsrnr_a11cc7')"
            @input="handleValueChange"
          />
        </div>
      </div>

      <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
        <div :class="[$style.title, 'hexagon__setting_title']">
          {{ $t('marketing_pd.components.setting.xygs_dc8d33') }}
        </div>
        <div :class="[$style.con, 'hexagon__setting_con']">
          <Select
            v-model="setting.num"
            size="small"
            :placeholder="$t('marketing_pd.components.setting.qxzgs_57c242')"
            @change="handleAgreementNumChange"
          >
            <Option
              v-for="(item, i) in options"
              :key="i"
              :label="$t('marketing_pd.components.setting.g_e98a1d', {data: ({'option0': i + 1})})"
              :value="i + 1"
            />
          </Select>
        </div>
      </div>

      <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
        <div :class="[$style.title, 'hexagon__setting_title']">
          {{ $t('marketing_pd.commons.xztbjs_063f28') }}
        </div>
        <div :class="[$style.con, 'hexagon__setting_con']">
          <ColorPicker
            v-model="setting.checkedColor"
            popper-class="hexagon__setting-colorpicker"
            size="mini"
            show-alpha
            :predefine="predefineColors"
            @change="handleValueChange"
          />
        </div>
      </div>

      <div :class="[$style.setting_item, 'hexagon__setting_item', 'hexagon__setting_row']">
        <div :class="[$style.title, 'hexagon__setting_title']">
          {{ $t('marketing_pd.components.setting.wgxts_fe1822') }}
        </div>
        <div :class="[$style.con, 'hexagon__setting_con']">
          <Input
            v-model="setting.placeholder"
            size="small"
            @input="handleValueChange"
          />
        </div>
      </div>

      <div :class="[$style.setting_item, 'hexagon__setting_item']">
        <div :class="[$style.title, 'hexagon__setting_title']">
          {{ $t('marketing_pd.commons.bt_32c65d') }}
        </div>
        <div :class="[$style.con, 'hexagon__setting_con']">
          <TextInput
            :value="{ text: setting.tplTitle, style: setting.titleStyle }"
            @input="handleAgreementTitleInput"
          />
        </div>
      </div>
    </Card>
    <Card :title="$t('marketing_pd.components.setting.xynr_5223c1')">
      <div
        v-if="!setting.num || setting.num === 1"
        :class="$style['agreement-item']"
      >
        <div :class="[$style.setting_item, 'hexagon__setting_item']">
          <div :class="[$style.title, 'hexagon__setting_title']">
            {{ $t('marketing_pd.components.setting.xynr_5223c1') }}
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']"  style="margin-bottom: 10px">
            <RadioGroup
              v-model="setting.agreementType"
              size="mini"
              @input="handleValueChange"
            >
              <Radio :label="'outLink'">{{ $t('marketing.commons.tzlj_dec2eb') }}</Radio>
              <Radio :label="'text'">{{ $t('marketing.components.Hexagon.sdtx_843dc5') }}</Radio>
            </RadioGroup>
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']">
            <fx-input
              v-if="setting.agreementType === 'outLink'"
              v-model="setting.outLink"
              size="small"
              :placeholder="$t('marketing.components.Hexagon.qsrlj_e43a84')"
              @input="handleValueChange"
            />
            <Editor
              v-else
              ref="editor"
              :key="setting.id"
              v-model="setting.value"
              @text-change="handleValueChange"
            />
          </div>
        </div>
      </div>

      <div
        v-for="(item, i) in setting.agreements"
        v-else
        :key="i"
        :class="$style['agreement-item']"
      >
        <div :class="[$style.setting_item, 'hexagon__setting_item']">
          <div :class="[$style.title, 'hexagon__setting_title']">
            {{ $t('marketing_pd.components.setting.xymc_e31a20', {data: ({'option0': i + 1})}) }}
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']">
            <TextInput
              :value="{ text: item.title, style: item.titleStyle }"
              @input="val => handleAgreementContentTitleInput(val, i)"
            />
          </div>
        </div>
        <div
          :class="[$style.setting_item, 'hexagon__setting_item']"
          style="margin-top: 20px"
        >
          <div :class="[$style.title, 'hexagon__setting_title']">
            {{ $t('marketing_pd.components.setting.xynr_3ea51d', {data: ({'option0': i + 1})}) }}
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']" style="margin-bottom: 10px">
            <RadioGroup
              v-model="item.agreementType"
              size="mini"
              @change="val => handleAgreementTypeChange(val, i)"
            >
              <Radio :label="'outLink'">{{ $t('marketing.commons.tzlj_dec2eb') }}</Radio>
              <Radio :label="'text'">{{ $t('marketing.components.Hexagon.sdtx_843dc5') }}</Radio>
            </RadioGroup>
          </div>
          <div :class="[$style.con, 'hexagon__setting_con']">
            <fx-input
              v-if="item.agreementType === 'outLink'"
              v-model="item.outLink"
              size="small"
              :placeholder="$t('marketing.components.Hexagon.qsrlj_e43a84')"
              @input="val => handleOutLinkChange(val, i)"
            />
            <Editor
              v-else
              ref="editor"
              :key="setting.id"
              :value="item.value"
              @text-change="val => handleAgreementContentChange(val, i)"
            />
          </div>
        </div>
      </div>
    </Card>
  </div>
</template>
<script>
import Card from '../../common/Card.vue'
import { Editor } from '../../common/editor/index.js'
import TextInput from '../../common/TextInput.vue'
import Content from '../components/Content.vue'
import { peekStyles } from '../../../utils/index.js'
import { predefineColors } from '../../../utils/const.js'
import ColorPicker from '../../comp/components/ColorPicker.vue'

export default {
  components: {
    Editor,
    Input: FxUI.Input,
    Tooltip: FxUI.Tooltip,
    RadioGroup: FxUI.RadioGroup,
    Radio: FxUI.Radio,
    TextInput,
    Select: FxUI.Select.components.ElSelect,
    Option: FxUI.Select.components.ElSelect.components.ElOption,
    Card,
    ColorPicker,
  },
  extends: Content,
  data() {
    return {
      options: new Array(6),
      predefineColors,
    }
  },
  watch: {
    setting(val) {
      // 新增  agreementType outLink 属性，旧数据需要做一下兼容
      if(!val.agreementType) {
        val.agreementType = 'text';
        val.outLink = '';
        if(val.agreements && val.agreements.length > 0) {
          val.agreements.map((item) => {
            if(!item.agreementType) {
              item.agreementType = 'text';
              item.outLink = '';
            }
          })
        }
      }
    }
  },
  methods: {
    _parseTplTitle() {
      const { agreements, num, tplTitle } = this.setting
      let title = tplTitle
      // 单多个协议处理逻辑
      if (num && num > 1) {
        agreements.forEach((item, i) => {
          const reg = new RegExp(`{${$t('marketing_pd.components.setting.xy_faa1ad')}${i + 1}}`, 'g')
          const titleStyle = peekStyles(item.titleStyle || {})
          title = title.replace(reg, () => {
            const a = document.createElement('a')
            Object.keys(titleStyle).forEach(attr => {
              a.style[attr] = titleStyle[attr]
            })
            a.setAttribute('index', i)
            a.innerText = item.title
            return a.outerHTML
          })
        })
      }
      this.setting.title = title
      this.handleValueChange()
    },
    handleAgreementTitleInput({ text, style }) {
      this.setting.tplTitle = text
      this.setting.titleStyle = style
      this._parseTplTitle()
    },
    handleAgreementNumChange() {
      const { num } = this.setting
      if (num > 1) {
        let tplTitle = $t('marketing_pd.components.setting.ydbty_ed8fae')
        this.setting.agreements = new Array(num).fill({}).reduce((arr, item, i) => {
          const iTitle = $t('marketing_pd.components.setting.xy_faa1ad') + (i + 1)
          tplTitle += `${i > 0 ? $t('marketing_pd.components.setting.y_3b0459') : ''}{${iTitle}}`
          arr.push({
            title: iTitle,
            value: '',
            titleStyle: {},
            agreementType: 'text', // 协议类型，text 原来的文本类型 outLink 外部链接
            outLink: '', // 外部链接
          })
          return arr
        }, [])
        this.setting.tplTitle = tplTitle
      } else {
        this.setting.tplTitle = $t('marketing_pd.commons.ydbtyysbhx_08485a')
        this.setting.title = $t('marketing_pd.commons.ydbtyysbhx_08485a')
      }
      this._parseTplTitle()
    },
    handleAgreementContentTitleInput({ text, style }, i) {
      this.setting.agreements[i].title = text
      this.setting.agreements[i].titleStyle = style
      this._parseTplTitle()
    },
    handleAgreementTypeChange(val, i) {
      this.setting.agreements[i].agreementType = val
      this._parseTplTitle()
    },
    handleOutLinkChange(val, i) {
      this.setting.agreements[i].outLink = val
      this._parseTplTitle()
    },
    handleAgreementContentChange(val, i) {
      this.setting.agreements[i].value = val
      this._parseTplTitle()
    },
  },
}
</script>
<style lang="less" module>
.content {
  .agreement-item {
    border-radius: 3px;
    border: 1px solid #DEE1E8;
    background: #F7F7F9;
    padding: 8px 14px 20px;
    margin-bottom: 10px;
  }
}
</style>
