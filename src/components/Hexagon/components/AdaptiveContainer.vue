<template>
  <div :class="[$style.AdaptiveContainer, 'hexagon__adaptive-container']">
    <VPageRender
      ref="page"
      :title="pageTitle"
      :contentStyle="styles"
      :data="components"
      :disabled="false"
      :showNavbar="showNavbar"
      :pageBackgroundOpts="pageBackgroundOpts"
      :pageHeaderOpts="pageHeaderOpts"
      :pagePopupOpts="pagePopupOpts"
      :parent="parent"
      usageType="inside"
      :width="width || viewportStyle.width"
      :pc-adaptation="pcAdaptation"
      @change="handlePageChange"
    >
      <template slot="page:before">
        <div :class="[$style.focus_area, 'hexagon__focus-area']" v-show="false">
          {{ $t('marketing_pd.components.AdaptiveContainer.fdcwz_c6fdc4') }}
        </div>
      </template>
      <CompSelector
        slot-scope="scope"
        :data="scope.data"
        :index="scope.index"
        :parent="scope.parent"
        :dragging="scope.dragging"
        :currentComp="currentComp"
        @selectItem="setCurrentItem"
        @deleteItem="deleteItem"
        @dbClickItem="dbClickItem"
        @mouseenter="mouseenter"
        @mouseleave="mouseleave"
        @copyItem="copyItem"
        @updateItem="updateItem"
      />

      <!-- <div
        :class="[$style.page_comp_hover, 'hexagon__page-comp-hover']"
        :style="{ pointerEvents: scope.data.visual&&scope.data.isVisual? 'none': 'auto' }"
        v-if="!designer.displayFocusArea"
        slot-scope="scope"
      >
        <div
          class="hexagon__page-comp-draghandle"
          v-if="!isDragFix(scope.data)"
        ></div>
        <span
          class="hexagon__page-comp-hover-copy el-icon-copy-document"
          v-if="scope.data.key !== 'form-container' && !scope.data.noEdit"
          :id="scope.data.id"
          :index="scope.index"
        ></span>
        <span
          v-if="!(scope.data.noDeletion || (scope.data.type === 'button' && scope.data.isFormComp))"
          class="hexagon__page-comp-hover-close el-icon-close"
          :id="scope.data.id"
          :index="scope.index"
        ></span>
        <span
          v-show="
            scope.data.type !== 'suspension'
            && scope.data.type !== 'contact'
            && scope.data.type !== 'videolive'
            && scope.data.position !== 'fixed-bottom-radius'
            && !(scope.data.type === 'video' && scope.data.layoutType === 'icon-only')
          "
          :class="$style.offset"
          >{{ genOffsetInfo(scope.data.style) }}</span
        >
        <span
          v-show="
            [
              'image',
              'slider',
              'blank',
              'button',
              'container',
              'tel',
              'qrcode',
              'signupbutton',
              'wechatvideo',
            ].indexOf(scope.data.type) !== -1 &&
              scope.data.typesetting !== 'flow'
          "
          :class="[$style.dragPoint, 'hexagon__page-comp-dragpoint']"
          @mousedown.stop="mousedown"
        ></span>
      </div> -->
    </VPageRender>
  </div>
</template>
<script>
import { mapActions, mapState } from "vuex";
import VPageRender from "./PageRender.vue";
import VContainer from "./comp/Container.vue";
import VCompLoader from "./common/CompLoader.vue";
import mixinUtil from "../mixin/mixinUtil";
import CompSelector from './common/CompSelector.vue';


export default {
  components: {
    VPageRender,
    VContainer,
    VCompLoader,
    CompSelector
  },
  mixins: [mixinUtil],
  props: {
    pcAdaptation: {
      type: Boolean,
      default: false,
    },
    parent: {
      type: Object,
      default: () => {},
    },
    components: {
      type: Array,
      default: () => [],
    },
    showNavbar: {
      type: Boolean,
      default: true,
    },
    showPageStyle: {
      type: Boolean,
      default: true,
    },
    width: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      doubleClickLastTriggerTime: null,
    };
  },
  computed: {
    ...mapState('hexagon', {
      layoutTabValue: 'layoutTabValue',
    }),
    designer() {
      return this.$store.state.hexagon.designer;
    },
    currentComp() {
      return   this.designer.propertySetting || {};
    },
    viewportStyle() {
      return this.$store.state.hexagon.designer.viewportStyle
    },
    styles() {
      const { viewportStyle, pageData } = this.$store.state.hexagon.designer;
      const newStyles = {
        width: `${this.width || viewportStyle.width}px`,
        minHeight: `${viewportStyle.height}px`,
        backgroundColor: 'transparent',
      };

      if (this.showPageStyle) {
        return {
          ...newStyles,
          ...pageData.style,
        }
      }

      return newStyles;
    },
    pageTitle() {
      const { title, name } = this.designer.pageData;
      return name || title || "";
    },
    // isDragFix() {
    //   return function(item) {
    //     if (
    //       item.type === "suspension" ||
    //       item.type === "contact" ||
    //       (item.type === "videolive" && item.layout === 1)
    //       || (item.type === 'video' && item.layoutType === 'icon-only')
    //     )
    //       return true;
    //     return false;
    //   };
    // },
    pageHeaderOpts() {
      return this.designer.pageData.headerOpts
    },
    pageBackgroundOpts() {
      return this.designer.pageData.backgroundOpts
    },
    pagePopupOpts() {
      return this.designer.pageData.popupOpts
    },
  },
  watch: {},
  mounted() {},
  methods: {
    ...mapActions("hexagon", {
      addTab: "addTab",
      addGridTab: 'addGridTab',
    }),
    ...mapActions("hexagon/designer", [
      "setViewportStyle",
      "setPageData",
      "setPageCompSettingData",
      "copyPageComp",
      "removePageComp",
      "updatePageComp",
    ]),
    ...mapActions("hexagon/canvas", [
      "copyCanvasComp",
      "setCanvasSelected",
      "removeCanvasComp",
      "setCanvasComp",
      "updateCanvasComp"
    ]),
    // init() {
    //   this.bodyMouseMoveHandler = this.onBodyMouseMove(this.mousemove);
    //   this.bodyMouseUpHandler = this.onBodyMouseUp(this.mouseup);
    // },
    setCurrentItem(item) {
      this.setPageCompSettingData(item);
    },
    deleteItem(item, index) {
      FxUI.MessageBox.confirm($t('marketing_pd.commons.qdyscgzjsf_b9ef40'), $t('marketing_pd.commons.ts_02d981'), {
        confirmButtonText: $t('marketing_pd.commons.qd_38cf16'),
        cancelButtonText: $t('marketing_pd.commons.qx_625fb2'),
        type: "warning",
      }).then(() => {
        if (this.layoutTabValue === "page") {
          this.removePageComp(index);
        } else if(this.layoutTabType !== 'grid'){
          this.removeCanvasComp(index);
        }
    
        FxUI.Message({
          type: "success",
          message: $t('marketing_pd.commons.sccg_0007d1'),
        });
      });
    },
    getCanvasTypesetting(compData) {
      const { layout, slideIndex, components, typesetting } = compData;
      if (layout === "multiple") {
        return components[slideIndex].typesetting || "absolute";
      } else {
        return typesetting || "absolute";
      }
    },

    dbClickItem(item, index) {
      if(item.type === 'gridcontainer') {
        this.addGridTab({
          name: item.id,
          index: index,
          type: 'grid',
        });
      } else if(item.type === 'container') {
        const canvasTypesetting = this.getCanvasTypesetting(item);
        this.addTab({
          name: item.id,
          index: index,
          type:
            canvasTypesetting === "flow"
              ? "flow"
              : item.typeValue,
        });
      }
    },
    updateItem(item, style = {}, wrapStyle = {}) {
      if(this.layoutTabValue === "page") {
        this.updatePageComp({
          ...item,
          style: {
            ...item.style,
            ...style,
          },
          wrapStyle: {
            ...item.wrapStyle,
            ...wrapStyle,
          }
        });
      } else if(this.layoutTabType !== 'grid'){
        this.updateCanvasComp({
          ...item,
          style: {
            ...item.style,
            ...style,
          },
        });
      }
    },
    mouseenter() {
    },
    mouseleave() {
    },
    copyItem(item, index) {
      if(this.layoutTabValue === "page") {
        this.copyPageComp(index);
      } else if(this.layoutTabType !== 'grid'){
        this.copyCanvasComp(index);
      }
    },
    // genOffsetInfo(style) {
    //   const width = this.viewportStyle.width;
    //   if (!style) {
    //     return `${$t('marketing_pd.commons.k_ea5a1c')}:${width} ${$t('marketing_pd.commons.g_4296d7')}:auto`;
    //   }
    //   return `${$t('marketing_pd.commons.k_ea5a1c')}:${width} ${$t('marketing_pd.commons.g_4296d7')}:${
    //     this.moveY > -1
    //       ? parseInt(this.moveY)
    //       : style.height !== undefined && style.height !== "auto"
    //       ?  parseInt(style.height)
    //       : "auto"
    //   }`;
    // },
    // mousedown(ev) {
    //   const compWrap = ev.target.closest(".hexagon__page-comp");
    //   this.currCompIndex = compWrap.getAttribute("index") - 0;
    //   this.dragElement = compWrap.children[0];
    //   this.currCompHeight = this.dragElement.offsetHeight;
    //   this.moveY = this.currCompHeight;
    //   this.startY = ev.pageY;
    //   this.isDrag = true;
    // },
    // mousemove(ev) {
    //   if (!this.isDrag) return;
    //   this.moveY = this.currCompHeight + (ev.pageY - this.startY);
    //   if (this.moveY < 30) {
    //     this.moveY = 30;
    //   }
    //   if(this.moveY > this.currCompMaxHeight){
    //     this.moveY = this.currCompMaxHeight;
    //     return
    //   }
    //   if (this.dragElement) {
    //     this.dragElement.style.height = this.moveY + "px";
    //   }
    // },
    // mouseup() {
    //   if (this.isDrag) {
    //     const data = this.components[this.currCompIndex] || {};
    //     if(data.isVisual && data.visual) {
    //       if(this.moveY > data.style.height) {
    //         this.moveY = data.style.height
    //         if (this.dragElement) {
    //           this.dragElement.style.height = this.moveY + "px";
    //         }
    //       }
    //       this.updatePageComp({
    //         ...data,
    //         style: {
    //           ...data.style,
    //         },
    //         visualStyle: {
    //           ...data.visualStyle,
    //           height: this.moveY,
    //         },
    //       });
    //     } else {
    //       this.updatePageComp({
    //         ...data,
    //         style: {
    //           ...data.style,
    //           height: this.moveY
    //         }
    //       });
    //     }
    //     this.moveY = -1;
    //     this.isDrag = false;
    //     this.dragElement = null;
    //   }
    // },
    handlePageChange(list) {
      this.$emit("change", list);
    },
  },
  // beforeDestroy() {
  //   this.bodyMouseMoveHandler.clear();
  //   this.bodyMouseUpHandler.clear();
  // },
};
</script>
<style lang="less" module>
.AdaptiveContainer {
  position: relative;
  box-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.08);
  width: 100%;
  // padding-bottom: 50px;
  .focus_area {
    min-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f3f9ff;
    color: var(--color-primary06,#ff8000);
    text-align: center;
    border: 1px dashed var(--color-primary06,#ff8000);
    font-size: 12px;
  }
  .page_comp_hover {
    position: relative;
    z-index: 2;
    .offset {
      color: #fff;
      font-size: 12px;
      background-color: rgba(0, 0, 0, 0.3);
      position: absolute;
      top: -18px;
      left: 0;
      padding: 0 5px;
    }
    .dragPoint {
      display: inline-block;
      width: 5px;
      height: 5px;
      background-color: var(--color-primary06,#ff8000);
      position: absolute;
      left: 50%;
      bottom: 0;
      transform: translate(-50%, 0);
      cursor: ns-resize;
      pointer-events: auto;
    }
  }
  .heightTip {
    position: absolute;
    bottom: 0;
    right: 380px;
    padding-right: 40px;
    color: #91959e;
    font-size: 14px;
    margin-bottom: -6px;
    &::after {
      position: absolute;
      right: 0;
      bottom: 6px;
      content: " ";
      display: inline-block;
      width: 35px;
      border-bottom: 1px dashed #91959e;
    }
  }
  :global {
    .hexagon__page {
      height: auto !important;
      min-height: 680px;
      overflow: initial;
      padding-bottom: 20px;
    }
  }
}
:global {
  .hexagon__page-comp {
    position: relative;
    .hexagon__page-comp-hover {
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      width: 100%;
      border: 2px solid var(--color-primary06,#ff8000);
      display: none;
      //pointer-events: none;
      .hexagon__page-comp-draghandle {
        width: 100%;
        height: 100%;
      }
      .hexagon__page-comp-hover-copy,
      .hexagon__page-comp-hover-close {
        height: 15px;
        width: 15px;
        position: absolute;
        top: 0;
        background-color: var(--color-primary06,#ff8000);
        color: #fff;
        font-size: 12px;
        text-align: center;
        line-height: 15px;
        cursor: pointer;
        pointer-events: auto;
      }
      .hexagon__page-comp-hover-close {
        right: 0;
      }
    }
    &:hover {
      .hexagon__page-comp-hover {
        display: block;
      }
    }
  }
}
</style>
