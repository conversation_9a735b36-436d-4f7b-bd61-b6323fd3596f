<template>
  <div
    :class="[$style.PageWrapper, 'hexagon__page-wrapper']"
    :style="`width: ${designer.viewportStyle.width}px`"
  >
    <div
      :class="[$style.Page, 'hexagon__page-layout']"
      :style="`width: ${designer.viewportStyle.width}px`"
    >
      <AdaptiveContainer
        ref="adaptiveContainer"
        :components="designer.pageCompList"
        :pcAdaptation="pcAdaptation"
        @change="handleComponentChange"
      />
    </div>
  </div>
</template>
<script>
import { mapActions } from "vuex";
import AdaptiveContainer from "./AdaptiveContainer.vue";

export default {
  props: {
    pcAdaptation: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    AdaptiveContainer,
  },
  computed: {
    designer() {
      return this.$store.state.hexagon.designer;
    },
  },
  methods: {
    ...mapActions("hexagon/designer", [
      "setViewportStyle",
      "setPageData",
      "updatePageComp"
    ]),
    calculateOffset() {
      if(this.$refs.adaptiveContainer && this.$refs.adaptiveContainer.calculateOffset) {
        this.$refs.adaptiveContainer.calculateOffset();
      }
    },
    handleComponentChange(comps) {
      this.setPageData(comps);
    }
  },
};
</script>
<style lang="less" module>
.PageWrapper {
  margin: auto;
  position: relative;
}
.Page {
  box-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.08);
  transform: translate(0, 0);
  position: relative;
  margin: 0;
  .focus_area {
    min-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f3f9ff;
    color: var(--color-primary06,#ff8000);
    text-align: center;
    border: 1px dashed var(--color-primary06,#ff8000);
    font-size: 12px;
  }
  .page_comp_hover {
    position: relative;
    .offset {
      color: #fff;
      font-size: 12px;
      background-color: rgba(0, 0, 0, 0.3);
      position: absolute;
      top: -18px;
      left: 0;
      padding: 0 5px;
    }
    .dragPoint {
      display: inline-block;
      width: 5px;
      height: 5px;
      background-color: var(--color-primary06,#ff8000);
      position: absolute;
      left: 50%;
      bottom: 0;
      transform: translate(-50%, 0);
      cursor: ns-resize;
    }
  }
  .heightTip {
    position: absolute;
    bottom: 0;
    right: 380px;
    padding-right: 40px;
    color: #91959e;
    font-size: 14px;
    margin-bottom: -6px;
    &::after {
      position: absolute;
      right: 0;
      bottom: 6px;
      content: " ";
      display: inline-block;
      width: 35px;
      border-bottom: 1px dashed #91959e;
    }
  }
  :global {
    .hexagon__page {
      height: auto !important;
      min-height: 680px;
      overflow: initial;
      padding-bottom: 20px;
    }
  }
}
:global {
  .hexagon__page-comp {
    position: relative;
    .hexagon__page-comp-hover {
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      width: 100%;
      border: 2px solid var(--color-primary06,#ff8000);
      display: none;
      .hexagon__page-comp-draghandle {
        width: 100%;
        height: 100%;
      }
      .hexagon__page-comp-hover-copy,
      .hexagon__page-comp-hover-close {
        height: 15px;
        width: 15px;
        position: absolute;
        top: 0;
        background-color: var(--color-primary06,#ff8000);
        color: #fff;
        font-size: 12px;
        text-align: center;
        line-height: 15px;
        cursor: pointer;
      }
      .hexagon__page-comp-hover-close {
        right: 0;
      }
    }
    &:hover {
      .hexagon__page-comp-hover {
        display: block;
      }
    }
  }
}
</style>
