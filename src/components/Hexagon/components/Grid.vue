<template>
    <div :class="[$style.Canvas__wrap_container]" ref="gridcontainer" :style="style">
      <Draggable
        :value="components"
        handle=".hexagon__page-comp-draghandle"
        @input="handleComponentChange"
        @start="dragging = true"
        @end="dragging = false"
        :options="{
          animation: 200,
          chosenClass: 'hexagon__com-chosen',
          dragClass: 'hexagon__com-drag',
        }"
        :class="[$style.grid_container, $style['grid_container-gap-' + compData.gridGap]]"
      >
        <div
          v-for="(item, index) in components"
            :key="item.id"
            :style="getFlexItemStyle(index)"
            :class="[$style.grid_container__comp, item.type === 'gridBlankItem' ? $style.is_empty : '']"
          >
              <div v-if="item.type === 'gridBlankItem'" :class="$style.canvas__tip">{{$t('marketing.components.Hexagon.zjtzdzl_b06634')}}</div>
              <template v-else>
                <VContainer
                  v-if="item.type === 'container'"
                  :data="item"
                  :parent="compData"
                  :isGrid="true"
                  :pcAdaptation="pcAdaptation"
                  :editType="'page'"
                />
                <GridChild
                  v-if="item.type === 'gridcontainer'"
                  :data="item"
                  :currentIndex="index"
                  :parent="compData"
                  :pcAdaptation="pcAdaptation"
                  :editType="'page'"
                  :usageType="'inside'"
                />
                <VCompLoader
                  v-else
                  :data="item"
                  :parent="compData"
                  :pcAdaptation="pcAdaptation"
                  :editType="'page'"
                  :usageType="'inside'"
                  :isGrid="true"
                />
              </template>
              <CompSelector
                  v-if="item.type !== 'gridcontainer'"
                  v-show="!dragging"
                  :data="item"
                  :index="index"
                  :parent="compData"
                  :currentComp="currentComp"
                  :isGrid="true"
                  @selectItem="setCurrentItem"
                  @deleteItem="deleteItem"
                  @dbClickItem="dbClickItem"
                  @mouseenter="mouseenter"
                  @mouseleave="mouseleave"
                  @updateItem="updateItem"
                />
          </div>
        </Draggable>
    </div>
</template>
<script>
import { mapState, mapActions } from 'vuex';
import Drag from './common/Drag.vue';
import Draggable from "vuedraggable";
import { getStyle } from '../utils/editor';
import mixinUtil from '../mixin/mixinUtil.js';
import VContainer from './comp/Container.vue';
import VCompLoader from './common/CompLoader.vue';
import CompSelector from './common/CompSelector.vue';
import GridChild from './GridChild.vue';

let timeHelder = null;
export default {
  components: {
    CompSelector,
    Draggable,
    Drag,
    VContainer,
    VCompLoader,
    GridChild
  },
  data() {
    return {
      dragging: false
    }
  },
  props: {
    pcAdaptation: {
      type: Boolean,
      default: false,
    },
  },
  mixins: [mixinUtil],
  computed: {
    moveTargetIndex() {
      return this.$store.state.hexagon.grid.moveTargetIndex
    },
    flexList(){
      if(this.compData.gridLayout) {
        const gridLayout = this.compData.gridLayout.split(',').map(item => parseInt(item))
        return gridLayout
      }else {
        return null
      }
    },
    contentStyle() {
      if(this.pcAdaptation) {
        return {
          ...getStyle(this.compData.style),
          height: 'auto',
          gap: this.compData.gridGap + 'px',
          width: this.viewportStyle.width + 'px',
        }
      }else {
        return {
          ...getStyle(this.compData.style),
          gap: this.grid.gridGap + 'px',
        }
      }
    },
    ...mapState('hexagon', {
      layoutTabValue: 'layoutTabValue',
      layoutTabType: 'layoutTabType',

    }),
    ...mapState('hexagon/designer', {
      activeCompData: state => state.propertySetting,
      pageData: 'pageData',
      pageMaps: 'pageMaps',
      viewportStyle: 'viewportStyle',
      isDesignerDrag: 'isDrag',
    }),
    ...mapState('hexagon/grid', {
      currentComp: 'currentComp',
      gridStyle: 'gridStyle'
    }),

    grid() {
      return this.$store.state.hexagon.grid
    },

    style() {
     return {
      ...getStyle({...this.gridStyle, width: this.viewportStyle.width}),
      height: 'auto'
     }
    },
    components() {
      const { comp } = this.grid
      if(this.compData.gridLayout) {
        return this.flexList.map((item, index) => {
          return comp[index] || {type: 'gridBlankItem'}
        })
      }else {
        return comp || [];
      }
    },
    compData() {
      return this.grid.compData
    },

  },
  watch: {},
  mounted() {},
  methods: {
    ...mapActions("hexagon/designer", [
      "setViewportStyle",
      "setPageData",
      "updatePageComp",
      "setPageCompSettingData"
    ]),
    ...mapActions("hexagon/grid", [
      "setGridOffset",
      "setGridStyle",
      "setGridComp",
      "setCurrentComp",
      "removeGridCompById",
      "updateAllGridDataToPage",
      "doCopyGridComp",
      "updateCompStyle",
      "setMoveTargetIndex"
    ]),
    ...mapActions("hexagon/canvas", ["setPageTopOffsetMap"]),
    ...mapActions("hexagon", [
      "addTabInGrid",
    ]),
    updateItem(item, style) {
      this.updatePageComp({
        ...item,
        style: {
          ...item.style,
          ...style,
        }
      });
    },
    mouseenter(index) {
      this.setMoveTargetIndex(index)
    },
    mouseleave() {
      this.setMoveTargetIndex(-1)
    },
    getFlexItemStyle(index) {
      let style = {
        justifyContent: this.compData.gridAlign,
      };
      if(this.compData.gridMinHeight) {
        style.minHeight = this.compData.gridMinHeight + 'px';
      }
      
      if(this.pcAdaptation) {
        if(this.flexList) {
          style.flex = this.flexList[index];
          style.width = 'auto';
        }else {
          style.width = '375px';
        }
      }else {
        style.width = '100%';
      }
      return style
    },
    dbClickItem(item) {
      if(item.type === 'container') {
        const {
          id,
          typesetting,
          compNum,
          typeValue,
          sort,
        } = item;
        // if(typeValue === 'auto') {
        //   const el = document.getElementById(`${id}`)
        //   if(el) {
        //     const rect = el.getBoundingClientRect()
        //     if(rect.y) {
        //       this.setPageTopOffsetMap({
        //         id: id,
        //         offset: rect.y
        //       })
        //     }
        //   }
        // }
        this.addTabInGrid({
          name: id,
          index: compNum || sort,
          type: typesetting === 'flow' ? 'flow' : typeValue,
          parent: this.compData
        });
      }
    },
    setCurrentItem(item) {
      if(item.type === 'gridBlankItem') {
        this.onGridContainerClick();
        return
      }
      this.setPageCompSettingData({
        ...item,
      });
      this.setCurrentComp(item);
    },
    onGridContainerClick() {
      this.setPageCompSettingData({
        ...this.compData
      });
      this.setCurrentComp({});
    },
    deleteItem(item) {
      FxUI.MessageBox.confirm(
          $t('marketing_pd.commons.qdyscgzjsf_b9ef40'),
          $t('marketing_pd.commons.ts_02d981'),
  {
            confirmButtonText: $t('marketing_pd.commons.qd_38cf16'),
            cancelButtonText: $t('marketing_pd.commons.qx_625fb2'),
            type: "warning",
          }
      ).then(() => {
        this.removeGridCompById({
          id: item.id,
        });
        FxUI.Message({
          type: "success",
          message: $t('marketing_pd.commons.sccg_0007d1'),
        });
      });
      return true;
    },
    copyItem(item) {
      this.doCopyGridComp(item);
    },
    setDraging(isDraging) {
      this.isDraging = isDraging
    },

    handleComponentChange(comps) {
      this.setGridComp({comps})
    },
  },
}
</script>
<style lang="less" module>
  .Canvas__wrap_container {
    width: 374px;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.15);
    position: relative;
    background: rgb(249, 249, 249);
    transform: translate(0, 0);

    .grid_container {
      display: flex;
      flex-wrap: wrap;
      width: 100%;
      height: 100%;
      justify-content: left;
      align-content: flex-start;

      &.grid_container-gap-0 {
        gap: 0;
      }

      &.grid_container-gap-8 {
        gap: 8px;
      }

      &.grid_container-gap-12 {
        gap: 12px;
      }

      .grid_container__comp {
        display: flex;
        flex-direction: column;
        position: relative;
        width: 375px;
        height: auto;
        overflow: auto;
        &:last-child {
          margin-right: auto;
        }
        &.is_empty {
          &:after {
            content: '';
            display: block;
            border: 1px dashed #ddd;
            width: calc(100% - 2px);
            height: calc(100% - 2px);
          }
        }
      }
      .canvas__tip {
        color: #999;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        opacity: 0.3;
        font-size: 18px;
      }
    }
  }
.hexagon__com-chosen {
  border: 1px solid red;
}
</style>
