<template>
  <div>
    <div
      v-if="data.isVisual && data.visual"
      :class="[$style.visualWrap, 'hexagon__page-comp-draghandle']"
      :style="{ height: data.visualStyle.height + 'px' }"
    >
      <div
        :id="data.id"
        :class="[
          $style.Container,
          data.typesetting === 'flow' ? $style.AdaptiveContainer:'',
          data.typeValue === 'tab-container' ? $style.TabContainer:'',
          'hexagon__designer-comp',
          'hexagon__designer-container',
        ]"
        :style="styles"
      >
        <ContainerTabs
          v-if="data.typeValue === 'tab-container'"
          :data="data"
          scene-type="page"
        />
        <div
          v-if="isHasComp"
          :style="componentsStyle"
        >
          <div
            v-for="item in components"
            :key="item.id"
            :class="[$style.container__comp, 'hexagon_contianer__comp']"
            :style="genWrapStyle(item)"

          >
            <VCompLoader
              :data="item"
              :pc-adaptation="pcAdaptation"
              :edit-type="'container'"
              :parent="subParent"
              :isGrid="isGrid"
            />
          </div>
        </div>
        <div
          v-if="!isHasComp"
          :class="$style.canvas__tip"
        >
          {{ $t('marketing_pd.components.comp.sjbj_23799c') }}
        </div>
      </div>
    </div>

    <template v-else>
      <div
        :id="data.id"
        :class="[
          $style.Container,
          data.typesetting === 'flow' ? $style.AdaptiveContainer:'',
          data.typeValue === 'tab-container' ? $style.TabContainer:'',
          'hexagon__designer-comp',
          'hexagon__designer-container',
          data.typesetting === 'flow' && !isHasComp ? $style.minHeight:'',
        ]"
        :style="styles"
      >
        <ContainerTabs
          v-if="data.typeValue === 'tab-container'"
          :data="data"
          scene-type="page"
        />
        <div
          v-if="isHasComp"
          :style="componentsStyle"
        >
          <div
            v-for="item in components"
            :key="item.id"
            :style="genWrapStyle(item)"
            :class="[$style.container__comp, 'hexagon_contianer__comp']"
          >
            <VCompLoader
              :data="item"
              :pc-adaptation="pcAdaptation"
              :edit-type="'container'"
              :parent="subParent"
              :isGrid="isGrid"
            />
          </div>
        </div>
        <div
          v-if="!isHasComp"
          :class="$style.canvas__tip"
        >
          {{ $t('marketing_pd.components.comp.sjbj_23799c') }}
        </div>
      </div>
    </template>
  </div>
</template>
<script>
import VCompLoader from '../common/CompLoader.vue'
import ContainerTabs from '../common/ContainerTabs.vue'
import mixins from './components/mixins.js'
import { peekStyles } from '../../utils/index.js'
import { mapState, mapActions } from 'vuex'
export default {
  components: {
    VCompLoader,
    ContainerTabs,
  },
  mixins: [mixins],
  props: {
    pcAdaptation: {
      type: Boolean,
      default: false,
    },
    isGrid: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    subParent() {
      const { components, layout, current } = this.data;
      if(layout === 'multiple') {
        return components[current] || {}
      }else {
        return this.data
      }
    },
    components() {
      const { components, layout, current } = this.data
      if (layout === 'multiple') {
        return (components[current] && components[current].components) || []
      }

      return components
    },
    componentsStyle() {
      const {
        components, layout, current, typeValue,
      } = this.data
      const style = {}

      if (typeValue === 'tab-container') {
        style.position = 'relative'
        // style.paddingTop = tabHeight || 40
      }

      if (layout === 'multiple') {
        const {
          style: { height = 'auto' } = {},
        } = components[current] || {}

        style.height = height
      }

      if (layout === 'multiple') {
        const {
          typesetting,
        } = components[current] || {}
        if(typesetting === 'flow'){
          delete style.height
        }
      }else{
        const {
          typesetting,
        } = this.data || {}
        if(typesetting === 'flow'){
          delete style.height
        }
      }
      return peekStyles(style)
    },
    isHasComp() {
      return this.components && this.components.length
    },
    ...mapState('ActivityMarketing', {
      marketingEventDetail: 'marketingEventDetail',
    }),
  },
  methods: {
    genWrapStyle(componentData) {
      const {
        wrapStyle = {},
        type = "",
        layout = '',
        layoutType = "",
        iconSize = 0,
        typeValue = '',
      } = componentData;
      const newStyle = {
        ...(wrapStyle || {}),
        position:
          (wrapStyle.position === "fixed" && "absolute") || wrapStyle.position,
      };

      if(['button', 'stepbutton'].includes(type) || typeValue === 'marketingEventSignupbutton') {
        if(wrapStyle.position === "fixed") {
          newStyle.position = 'fixed'
        }
      }

      if (type === "video" && layoutType === "icon-only") {
        newStyle.width = iconSize;
        newStyle.height = iconSize;
        newStyle.position = "absolute";
      }

      if (type === "video" && layoutType !== "icon-only") {
        newStyle.top = 0;
        newStyle.left = 0;
      }
      // 这里是为了解决视频号直播卡片布局会占满全屏的问题
      if(type === "videolive" && layout === 0) {
        return peekStyles({}, [])
      }
      return peekStyles(newStyle, []);
    },
    ...mapActions('ActivityMarketing', {
      getMarketingEventDetailById: 'getMarketingEventDetailById',
    }),
    updateActivityCompConfig() {
      const replaceData = ()=>{
        const { components } = this.data
        if(!components || !components.length) return
        components.forEach(comp => {
          if(comp.fieldName === 'marketingEventCover' || comp.fieldName === 'channelsAvatar') {
            const newConfig = {
                ...comp,
                images: [{
                  url: this.marketingEventDetail[comp.fieldName]
                }]
              }
              Object.assign(comp, newConfig)
          } else if(
            comp.fieldName === 'marketingEventTitle' ||
            comp.fieldName === 'marketingEventTime' ||
            comp.fieldName === 'channelsName'
          ){
            const newConfig = {
              ...comp,
              value: this.marketingEventDetail[comp.fieldName] || comp.value
            }
            Object.assign(comp, newConfig)
          }else if(comp.fieldName === 'marketingEventAddress'){
            const newConfig = {
              ...comp,
              value: this.marketingEventDetail[comp.fieldName] || comp.value,
              isShowMap: this.marketingEventDetail.isShowMap || false
            }
            Object.assign(comp, newConfig)
          }else if(comp.fieldName === 'marketingEventDetail' ){
              const article = this.marketingEventDetail.eventForm === 'conference_marketing' ? 
              {
                type: 'article',
                conference: true,
                article: {
                  content: this.marketingEventDetail.marketingEventDetail,
                  type: 'tile'
                },
              } : {};
              const newConfig = {
                ...comp,
                value: this.marketingEventDetail[comp.fieldName] || comp.value,
                ...article
              }
              Object.assign(comp, newConfig)
            }
        })
      }
      replaceData()
      console.log(this.data,'this.data')
    }
  },
  async mounted() {
    if(this.data.typeValue === 'activity-comp' && this.data.marketingEventId && this.data.marketingEventId !== '!!marketingEventId!!') {
      await this.getMarketingEventDetailById({
        id: this.data.marketingEventId
      })
      this.updateActivityCompConfig()
    }
  }
}
</script>
<style lang="less" module>
.visualWrap {
  overflow-y: auto;
  overflow-x: hidden;
  border: 1px dashed rgba(189, 191, 195, 1)
}
.Container {
  word-break: break-all;
  position: relative;
  &.minHeight{
    min-height: 50px;
  }
  .visualWrap {
    overflow-y: auto;
    overflow-x: hidden;
    //opacity: 0;
  }
  &.TabContainer{
    min-height: 42px;
  }
  &.ActivityComp{
    min-height: 600px;
  }
  .canvas__tip {
    color: #999;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0.3;
    font-size: 18px;
  }
  .container__comp {
    // position: absolute;
  }
}

</style>
