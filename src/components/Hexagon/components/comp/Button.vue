<template>
  <div
    :id="data.id"
    :class="[
      $style.Button,
      'hexagon__designer-comp',
      'hexagon__designer-button'
    ]"
    :style="styles"
  >
    <div
      v-if="data.buttonList && data.buttonList.length > 0"
      :class="[$style.ButtonCon, $style['button_inner_' + data.textAlign]]"
      :style="buttonConStyles"
    >
      <div
        v-for="(item, ind) in data.buttonList"
        :key="ind"
        :class="[$style.ButtonItem]"
        :style="getItemStyles(item)"
      >
        <div
          v-if="checkIsShowIcon(item)"
          :class="$style.ButtonItemIcon"
        >
          <FontIcon
            v-if="item.iconInfo.iconType === 'iconfont'"
            :class="$style.iconfont"
            :icon-info="item.iconInfo"
            :icon-layout="data.iconLayoutType || 'line'"
            :font-size="data.iconSize || 16"
          />
          <img
            v-else
            :class="$style.iconimg"
            :src="item.iconInfo.icon"
          >
        </div>
        <div
          v-if="data.layoutType !== 'icon-only'"
          :class="$style.ButtonText"
        >
          {{ item.name }}
        </div>
      </div>
    </div>
    <div
      v-else
      :class="[$style.ButtonCon, $style.button_inner, $style['button_inner_' + data.textAlign]]"
      :style="innerStyles"
    >
      <div
        v-if="data.memberAutoSignup && (data.memberAutoSignupButton || data.memberAutoSignupButton === undefined)"
        :class="$style.login"
        :style="loginStyles"
      >
        {{ data.memberAutoSignupButtonText || $t('marketing_pd.commons.hydl_83e581') }}
      </div>
      <div
        :class="$style.liveButton"
        :style="signupButtonStyles"
      >
        <div
          v-if="showIcon"
          :class="$style.icon"
        >
          <FontIcon
            v-if="data.iconInfo.iconType === 'iconfont'"
            :class="$style.iconfont"
            :icon-info="data.iconInfo"
            :icon-layout="data.iconLayoutType || 'line'"
            :font-size="data.iconSize || 16"
          />
          <img
            v-else
            :class="$style.iconimg"
            :src="data.iconInfo.icon"
          >
        </div>
        <template v-if="data.layoutType !== 'icon-only'">
          <div :class="$style.ButtonText">
            {{ buttonName }}
          </div>
        </template>
      </div>
    </div>
  </div>
</template>
<script>
import { deepClone } from '../../utils/index.js'
import mixins from './components/mixins.js'
import FontIcon from '../common/FontIcon.vue'

export default {
  components: {
    FontIcon,
  },
  mixins: [mixins],
  props: {
    editType: {
      type: String,
      default: '',
    },
    data: {
      type: Object,
      default: () => ({
        id: '',
        name: '',
        value: '',
        style: {},
      }),
    },
  },
  data() {
    return {
      model_selected: 1,
    }
  },
  computed: {
    showIcon() {
      const { layoutType, iconInfo } = this.data
      return (layoutType === 'icon-only' || layoutType === 'icon-and-text')
        && iconInfo
        && iconInfo.icon
    },
    styles() {
      const { style } = this.data
      const newStyles = deepClone(style)
      newStyles.borderRadius = style.borderRadius + newStyles.borderWidth
      if (this.editType === 'canvas') {
        delete newStyles.background
        // delete newStyles.borderRadius;  // 会导致边框圆角没有效果
        delete newStyles.border

        if (this.data.buttonList && this.data.buttonList.length > 0) {
          delete newStyles.boxShadow
        }
        return this.peekStyles(newStyles)
      }

      return {}
    },
    innerStyles() {
      const { style } = this.data
      const newStyles = deepClone(style)
      newStyles.borderRadius = style.borderRadius + newStyles.borderWidth
      if (this.editType === 'canvas') {
        return {}
      }
      delete newStyles.background
      // delete newStyles.borderRadius;  // 会导致边框圆角没有效果
      delete newStyles.border
      return {
        ...this.peekStyles(newStyles),
      }
    },
    buttonConStyles() {
      const { style } = this.data
      const newStyles = deepClone(style)
      if (this.editType === 'canvas') {
        return {}
      }
      delete newStyles.background
      delete newStyles.border
      delete newStyles.borderRadius
      delete newStyles.boxShadow
      return this.peekStyles(newStyles)
    },
    loginStyles() {
      const { style, memberAutoSignupButtonStyle } = this.data
      return this.peekStyles({
        borderRadius: style.borderRadius,
        ...(memberAutoSignupButtonStyle || {}),
      })
    },
    signupButtonStyles() {
      const {
        style, type, objectId, status, schedule,typeValue
      } = this.data
      // 当前选中状态的按钮样式
      let buttonStyle = {}
      if ((type === 'signupbutton' && objectId) || typeValue === 'marketingEventSignupbutton') {
        buttonStyle = schedule[status] ? schedule[status].no.style : {}
      }
      return this.peekStyles({
        ...buttonStyle,
        background: style.background,
        borderRadius: style.borderRadius,
        border: style.border,
      })
    },
    buttonName() {
      const {
        name, type, objectId, status, schedule,typeValue
      } = this.data
      if ((type === 'signupbutton' && objectId) || typeValue === 'marketingEventSignupbutton') {
        return schedule[status] ? schedule[status].no.name : ''
      }
      return name
    },
  },
  methods: {
    checkIsShowIcon(item) {
      const { layoutType } = this.data
      const { iconInfo } = item
      return (layoutType === 'icon-only' || layoutType === 'icon-and-text')
        && iconInfo
        && iconInfo.icon
    },
    getItemStyles(item) {
      const { style } = item
      const newStyles = deepClone(style)
      newStyles.borderRadius = style.borderRadius + newStyles.borderWidth
      return {
        ...this.peekStyles(newStyles),
        boxShadow: this.data.style.boxShadow,
        height: `${this.data.style.height}px`,
        lineHeight: `${this.data.style.lineHeight}px`,
      }
    },
  },
}
</script>
<style lang="less" module>
.Button {
  word-break: break-all;
  .button_inner{
    height: 100%;
    display: flex;
    .login{
      width: 30%;
      background-color: #fff;
      text-align: center;
      border: 1px solid #CED1D9;
      margin-right: 10px;
      color: #181c25;
      overflow: hidden;
    }
    .liveButton{
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;

      .ButtonText {
        line-height: normal;
        white-space: normal;
      }
    }

    .icon {
      margin-right: 4px;
      display: flex;
      align-items: center;

      &:last-child {
        margin-right: 0;
      }
    }
    .iconfont{
      font-size: 16px;
      vertical-align: middle;
    }
    .iconimg{
      width: 16px;
      height: 16px;
      font-size: 0;
      vertical-align: middle;
    }
  }
  .ButtonCon {
    display: flex;
    .ButtonItem {
      display: flex;
      align-items: center;
      justify-content: center;
      flex: 1;
      text-align: center;
      margin-right: 10px;
      overflow: hidden;
      .ButtonItemIcon {
        margin-right: 4px;
        line-height: normal;
      }
      .ButtonText {
        line-height: normal;
        white-space: normal;
      }
      &:last-child {
        margin-right: 0;
      }
    }
    &.button_inner_left {
      .ButtonItem,
      .liveButton {
        justify-content: flex-start !important;
        padding-left: 16px;
      }
    }
    &.button_inner_column {
      .ButtonItem,
      .liveButton {
        flex-direction: column !important;
      }
      .ButtonItemIcon {
        line-height: 120%;
        margin-right: 0;
      }
      .ButtonText {
        line-height: 180%;
      }
    }
  }
}
</style>
