<template>
  <div
      :id="data.id"
      :class="[$style.grid_container, pcAdaptation ? 'hexagon__grid-container-pc' : '', 'hexagon__grid-container']"
      :style="styles"
  >
    <div
      v-for="(item, index) in components"
      :key="item.id"
      :style="getFlexItemStyle(index)"
      :class="[$style.grid_container__comp, (item.type === 'gridBlankItem' && usageType !== 'outside') ? $style.is_empty : '']">
          <div v-if="item.type === 'gridBlankItem'" :style="{display: usageType === 'outside' ? 'none' : 'block'}" :class="$style.canvas__tip">
              {{ $t('marketing_pd.components.comp.sjbj_23799c') }}
          </div>
          <VContainer
            v-else-if="item.type === 'container'"
            :data="item"
            :pcAdaptation="pcAdaptation"
            :parent="data"
            :editType="'page'"
            :isGrid="true"
          />
          <VGridContainer
            v-else-if="item.type === 'gridContainer'"
            :data="item"
            :parent="data"
            :pcAdaptation="pcAdaptation"
            :editType="'page'"
            :usageType="usageType"
          />
          <VCompLoader
            v-else
            :data="item"
            :parent="data"
            :pcAdaptation="pcAdaptation"
            :editType="'page'"
            :usageType="usageType"
            :isGrid="true"
          />

    </div>
  </div>
</template>
<script>

import { getStyle } from '../../utils/editor';
import VContainer from "./Container.vue";
import VCompLoader from '../common/CompLoader.vue';
import { mapState } from 'vuex';


export default {
  props: {
    editType: {
      type: String,
      default: '',
    },
    pcAdaptation: {
      type: Boolean,
      default: false,
    },
    usageType: {
      type: String,
      default: 'outside',
    },
    data: {
      type: Object,
      default: () => ({
        id: '',
        name: '',
        components: [],
        style: {},
      }),
    },
    parent: {
      type: Object,
      default: () => ({}),
    },
  },

  computed: {
    ...mapState('hexagon/designer', {
      viewportStyle: 'viewportStyle',
    }),
    flexList(){
      if(this.data.gridLayout) {
        const gridLayout = this.data.gridLayout.split(',').map(item => parseInt(item))
        return gridLayout
      }else {
        return null
      }
    },
    styles(){
      if(this.pcAdaptation) {
        return {
          ...getStyle(this.data.style),
          height: 'auto',
          gap: this.data.gridGap + 'px',
          width: this.parent.type === 'gridcontainer' ? '100%' : this.viewportStyle.width + 'px',
        }
      }else {
        return {
          ...getStyle(this.data.style),
          gap: this.data.gridGap + 'px',
        }
      }
    },
    components() {
      const { components } = this.data
      if(this.flexList) {
        return this.flexList.map((item, index) => {
          return components[index] || {type: 'gridBlankItem'}
        })
      }else {
        return components
      }
    },
  },
  methods: {
    getFlexItemStyle(index) {
      let style = {
        justifyContent: this.data.gridAlign,
      };
      if(this.data.gridMinHeight) {
        style.minHeight = this.data.gridMinHeight + 'px';
      }
      if(this.pcAdaptation) {
        if(this.data.gridLayout) {
          style.flex = this.flexList ? this.flexList[index] : 1;
          style.width = 'auto'
        }else {
          style.width = '375px'
        }
      }else {
        style.width = '100%'
      }
      return style
    }
  },
  beforeCreate() {
    this.$options.components.VCompLoader = VCompLoader;
    this.$options.components.VContainer = VContainer;
  },
}
</script>
<style lang="less" module>
.grid_container {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: left;
  align-content: flex-start;

  .grid_container__comp {
    display: flex;
    flex-direction: column;
    position: relative;
    width: 375px;
    height: auto;
    overflow: auto;
    &.is_empty {
      &:after {
        content: '';
        display: block;
        border: 1px dashed #ddd;
        width: calc(100% - 2px);
        height: calc(100% - 2px);
      }
    }
  }
  .canvas__tip {
    color: #999;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0.3;
    font-size: 18px;
  }
}
</style>
