<template>
  <div
    :id="data.id"
    ref="image"
    :class="[
      $style.ImageWrap,
      'hexagon__designer-comp',
      'hexagon__designer-image'
    ]"
    :style="imageStyle.wrapStyle"
  >
    <div
      v-for="(item, i) in data.images"
      :key="i"
      :class="[$style.Image]"
      :style="{
        ...imageStyle.itemStyle,
        background: item.url ? 'none' : '#E9EDF5',
        marginLeft: data.imageGap + 'px'
      }"
    >
      <div
        v-if="!item.url"
        :class="$style.uploader"
      >
        <span v-if="data.fieldName === 'marketingEventCover'">{{ data.name }}</span>
        <i
          v-else-if="data.fieldName"
          class="el-icon-user-solid"
          style="fontSize: 20px"
        /><span v-else>{{ $t('marketing_pd.commons.tp_20def7') }}</span>
      </div>
      <img
        v-else
        :src="item.url"
        :class="$style.comp__image"
        :style="{ filter: imageStyle.filterConfig, objectFit: 'cover' }"
        alt=""
      >
    </div>
  </div>
</template>
<script>
import mixins from './components/mixins.js'

export default {
  mixins: [mixins],
  computed: {
    imageStyle() {
      const style = this.deepClone(this.data.style)
      const filterConfig = this.deepClone(this.data.filterConfig || {})
      const filterKeys = Object.keys(filterConfig)
      const _styleFilter = filterKeys.map(key => `${key}(${filterConfig[key] / 100})`).join(' ')

      const itemStyle = {}
      this.detachAttr(
        style,
        ['borderRadius', 'borderWidth', 'borderColor', 'borderStyle'],
        itemStyle,
      )
      return {
        wrapStyle: this.peekStyles(style),
        itemStyle: this.peekStyles(itemStyle),
        filterConfig: _styleFilter,
      }
    },
  },
}
</script>
<style lang="less" module>
.ImageWrap {
  display: flex;
  .Image {
    word-break: break-all;
    position: relative;
    flex: 1;
    overflow: hidden;
    margin-left: 12px;
    &:first-child {
      margin-left: 0 !important;
    }
  }
  .comp__image {
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center center;
    transform: translate3d(0,0,0);
  }
  .uploader {
    // background-color: #E9EDF5;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #c1c5ce;
    font-size: 12px;
  }
}
</style>
