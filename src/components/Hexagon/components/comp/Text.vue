<template>
  <div :class="data.fieldName === 'marketingEventAddress' && data.isShowMap && $style.addressDetail">
    <div
      :id="data.id"
      :class="[$style.Text, 'hexagon__designer-comp',
      'hexagon__designer-text',
      data.fieldName === 'marketingEventDetail' && $style.htmlDetail]"
      :style="styles"
      v-html="data.value || data.name"
    />
    <i v-if="data.fieldName === 'marketingEventAddress' && data.isShowMap" :class="[$style.icondingwei, 'iconfont icondingwei']"></i>
  </div>
</template>
<script>
import mixins from './components/mixins.js'

export default {
  mixins: [mixins],
}
</script>
<style lang="less" module>
.Text {
  white-space: pre-wrap;
  word-wrap: break-word;
  min-width: 20px;
  line-height: 20px;
  font-size: 14px;

  :global{
    h1{
      font-size: 2em;
    }
    h2{
      font-size: 1.5em;
    }
    h3{
      font-size: 1.17em;
    }
    h4{
      font-size: 1em;
    }
    h5{
      font-size: .83em;
    }
    h6{
      font-size: .67em;
    }
    em{
      font-style: italic;
    }
  }
}
.htmlDetail{
  p{
    max-width: 100%;
    img{
      max-width: 100%;
    }
  }
}
.addressDetail{
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  padding-right: 20px;
}
.icondingwei{
  cursor: pointer;
  font-size: 16px;
  color: #0C6CFF;
}
</style>
