<!-- 登录中转页面组件 -->
<template>
  <div class="auth-login-transition">
    <!-- loading状态 -->
    <div
      v-if="settingData.status === 'loading'"
      class="loading-container"
    >
      <div class="loading-spinner"></div>
      <div class="loading-text">{{ $t("SFA.pagedesigner.common.logging") }}</div>
    </div>

    <!-- 错误状态 -->
    <div v-if="settingData.status === 'error'" class="error-container">
      <div class="error-icon">
        <i class="iconfont icon-error"></i>
      </div>
      <div class="error-message">{{ $t("SFA.pagedesigner.common.loginError") }}</div>
      <div class="error-tip">{{ $t("SFA.pagedesigner.common.checkConfig") }}</div>
    </div>
  </div>
</template>

<script>
import mixins from '../components/mixins.js'

export default {
  name: 'AuthLoginTransition',
  mixins: [mixins],
  props: {},
  data() {
    return {
      settingData: {
        // loading, error
        status: 'loading',
      },
    }
  },
  computed: {},
  methods: {}
}
</script>

<style scoped lang="less">
.auth-login-transition {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 621px;
  background-color: #fff;

  .loading-container {
    text-align: center;

    .loading-spinner {
      width: 40px;
      height: 40px;
      margin: 0 auto 16px;
      border: 2px solid #f3f3f3;
      border-top: 2px solid #3498db;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    .loading-text {
      font-size: 14px;
      color: #666;
    }
  }

  .error-container {
    text-align: center;
    padding: 24px;

    .error-icon {
      font-size: 48px;
      color: #ff522a;
      margin-bottom: 16px;
    }

    .error-message {
      font-size: 16px;
      color: #333;
      margin-bottom: 8px;
      font-weight: 500;
    }

    .error-tip {
      font-size: 14px;
      color: #666;
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
