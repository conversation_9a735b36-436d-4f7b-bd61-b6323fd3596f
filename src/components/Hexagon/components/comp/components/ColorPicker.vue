<template>
  <div
    :class="$style['color-picker-container']"
  >
    <fx-color-picker
      ref="colorPicker"
      v-model="currentValue"
      :disabled="disabled"
      :size="size"
      :show-alpha="showAlpha"
      :color-format="activeColorFormat"
      :popper-class="popperClass"
      :hide-color-panel="hideColorPanel"
      :hide-footer="hideFooter"
      @change="handleColorChange"
      @active-change="handleActiveChange"
    >
      <template #footer>
        <div
          :class="$style['color-picker-footer']"
          @click="handleMenuHide"
        >
          <div :class="$style.options">
            <!-- <fx-select
              v-model="activeColorFormat"
              :options="colorFormatOptions"
              :class="$style.select"
              size="mini"
              :popper-append-to-body="false"
            /> -->
            <fx-input
              v-model="currentTextValue"
              size="mini"
              :class="$style.input"
              @change="handleColorTextChange"
            />
            <fx-button
              size="mini"
              plain
              :class="$style.submit"
              @click="handleConfirm"
            >
              {{ $t('marketing_pd.commons.qd_38cf16') }}
            </fx-button>
            <fx-button
              size="mini"
              type="text"
              :class="$style.clear"
              @click="handleClear"
            >
              {{ $t('marketing_pd.components.comp.qk_288f0c') }}
            </fx-button>
          </div>
          <template v-if="predefine && predefine.length">
            <div :class="$style.block">
              <div :class="$style['block-title']">
                {{ $t('marketing_pd.components.comp.xtysys_ec1b89') }}
              </div>
              <div :class="$style['block-content']">
                <div
                  v-for="item in predefine"
                  :key="item"
                  :class="{
                    [$style['color-unit']]: true,
                    [$style.active]: isEqual(item, currentValue)
                  }"
                  :style="{ backgroundColor: item }"
                  @click="handleChooseColor(item)"
                />
              </div>
            </div>
          </template>
          <div :class="$style.block">
            <div :class="$style['block-title']">
              {{ $t('marketing_pd.components.comp.qycyys_0e7c90') }}
              <span>{{ $t('marketing_pd.components.comp.djsbyjsc_28b724') }}</span>
            </div>
            <div :class="$style['block-content']">
              <div
                v-for="item in brandColorList"
                :key="item"
                :style="{ backgroundColor: item }"
                :class="{
                  [$style['color-unit']]: true,
                  [$style.active]: isEqual(item, currentValue)
                }"
                @contextmenu="handleMenuShow($event, item)"
                @click="handleChooseColor(item)"
              />
              <fx-popover
                placement="top"
                trigger="hover"
                :content="$t('marketing_pd.components.comp.tjdqs_891198')"
                :popper-class="$style['color-plus-popper']"
              >
                <div
                  slot="reference"
                  :class="[$style['color-unit'], $style.plus]"
                  @click="handleAddColor"
                >
                  <span class="fx-icon-add-2" />
                </div>
              </fx-popover>
              <div
                v-if="contentMenuVisible"
                :class="$style['content-menu-wrap']"
                :style="contentMenuStyle"
              >
                <div
                  :class="$style['content-menu-item']"
                  @click="handleDeleteColor"
                >
                  {{ $t('marketing_pd.commons.sc_2f4aad') }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </fx-color-picker>
  </div>
</template>

<script>
import Color from 'color'
import { mapState, mapActions } from 'vuex'
import request from '$page-designer/utils/request.js'
import { predefineColors } from '$page-designer/utils/const.js'

const emptyColor = [
  undefined,
  null,
  'null',
  '',
  'none',
  'inherit',
  'unset',
  'transparent',
  'initial',
]

export default {
  /**
   * 和FXUI保持同步：https://fe.firstshare.cn/fxui/#/zh-CN/component/color-picker
   */
  props: {
    value: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    size: {
      type: String,
      default: '',
    },
    showAlpha: {
      type: Boolean,
      default: false,
    },
    colorFormat: {
      type: String,
      default: 'hex',
    },
    popperClass: {
      type: String,
      default: '',
    },
    predefine: {
      type: Array,
      default: () => (predefineColors),
    },
    hideColorPanel: {
      type: Boolean,
      default: false,
    },
    hideFooter: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      colorFormatOptions: [
        { value: 'hsl', label: 'HSL' },
        { value: 'hsv', label: 'HSV' },
        { value: 'rgb', label: 'RGB' },
        { value: 'hex', label: 'HEX' },
      ],
      activeColorFormat: 'rgb',
      currentValue: '',
      currentTextValue: '',
      contentMenuVisible: false,
      contentMenuPosition: {},
    }
  },
  computed: {
    ...mapState('Hexagon', {
      brandColorList: 'brandColorList',
    }),
    contentMenuStyle() {
      const keys = Object.keys(this.contentMenuPosition)
      return keys.map(key => {
        const value = this.contentMenuPosition[key]
        if (typeof value === 'string') {
          return `${key}: ${value};`
        }

        return `${key}: ${value}px;`
      })
        .join('')
    },
  },
  watch: {
    value() {
      this.currentValue = this.value
      this.currentTextValue = this.value
    },
  },
  mounted() {
    this.currentValue = this.value
    this.currentTextValue = this.value

    const defaultColorFormat = this.showAlpha ? 'rgb' : 'hex'
    this.activeColorFormat = this.colorFormat || defaultColorFormat
  },
  methods: {
    ...mapActions('Hexagon', {
      updateBrandColorList: 'updateBrandColorList',
    }),
    isValidColor(color) {
      if (!color) return false

      try {
        // 尝试用 Color 库解析颜色
        return Color(color)
      } catch (e) {
        return false
      }
    },
    handleColorTextChange(val) {
      // 校验字符串是否合法的颜色字符串
      if (this.isValidColor(val)) {
        if (this.activeColorFormat === 'hex') {
          this.currentValue = Color(val).hex()
        }

        if (this.activeColorFormat === 'rgb') {
          this.currentValue = Color(color).rgb().string()
        }

        this.$emit('input', this.currentValue)
      } else {
        this.$emit('input', '')
      }
    },
    isEqual(color1, color2) {
      if (
        emptyColor.indexOf(color1) > -1
        || emptyColor.indexOf(color2) > -1
      ) {
        return false
      }

      if (color1 === color2) {
        return true
      }

      const color1Obj = Color(color1).object()
      const color2Obj = Color(color2).object()

      return color1Obj.r === color2Obj.r
        && color1Obj.g === color2Obj.g
        && color1Obj.b === color2Obj.b
        && color1Obj.alpha === color2Obj.alpha
    },
    handleActiveChange(color) {
      if (this.activeColorFormat === 'hex') {
        this.currentValue = Color(color).hex()
      }

      if (this.activeColorFormat === 'rgb') {
        this.currentValue = Color(color).rgb().string()
      }

      this.currentTextValue = this.currentValue
      this.$emit('input', this.currentValue)
      this.$emit('active-change', this.currentValue)
    },
    handleColorChange(color) {
      if (this.activeColorFormat === 'hex') {
        this.currentValue = Color(color).hex()
      }

      if (this.activeColorFormat === 'rgb') {
        this.currentValue = Color(color).rgb().string()
      }

      // 清空颜色
      if (color === null) {
        this.currentValue = ''
      }

      this.currentTextValue = this.currentValue
      this.$emit('input', this.currentValue)
      this.$emit('change', this.currentValue)
    },
    handleConfirm() {
      if (this.$refs.colorPicker) {
        this.$refs.colorPicker.confirmValue()
        this.$refs.colorPicker.hide()
      }
    },
    handleClear() {
      if (this.$refs.colorPicker) {
        this.$refs.colorPicker.clearValue()
        this.$refs.colorPicker.hide()
      }
    },
    handleChooseColor(color) {
      if (this.activeColorFormat === 'hex') {
        this.currentValue = Color(color).hex()
      }

      if (this.activeColorFormat === 'rgb') {
        this.currentValue = Color(color).rgb().string()
      }

      this.currentTextValue = this.currentValue
      this.$emit('input', this.currentValue)
    },
    handleMenuShow(e, color) {
      e.preventDefault()

      const {
        offsetLeft = 0,
        offsetTop = 0,
        offsetWidth = 0,
        parentNode,
      } = e.target
      const offsetBottom = parentNode.offsetHeight - offsetTop
      const isMoreHalf = parentNode.offsetWidth / 2 < offsetLeft

      this.contentMenuPosition = {
        left: isMoreHalf ? 'auto' : offsetLeft,
        right: isMoreHalf ? parentNode.offsetWidth - offsetLeft - offsetWidth : 'auto',
        bottom: offsetBottom + 8,
      }
      this.contentMenuVisible = true
      this.stashColor = color
    },
    handleMenuHide() {
      this.contentMenuVisible = false
    },
    handleDeleteColor() {
      this.contentMenuVisible = false

      if (this.stashColor) {
        const payload = {
          colorConfigs: [
            {
              color: new Color(this.stashColor).rgb().toString(),
              type: 0,
            },
          ],
        }

        request('/marketing/hexagon/updateHexagonBackgroundColorSetting', { data: payload })
          .then(res => {
            const { errCode, errMsg = '', data = [] } = res || {}
            if (errCode === 0) {
              this.updateBrandColorList(data)
              FxUI.Message.success($t('marketing_pd.commons.sccg_0007d1'))
              return
            }

            FxUI.Message.error(errMsg || $t('marketing_pd.components.comp.scsb_acf066'))
          })
          .catch(() => {
            FxUI.Message.error($t('marketing_pd.components.comp.scsb_acf066'))
          })
      }
    },
    handleAddColor() {
      if (emptyColor.indexOf(this.currentValue) > -1) {
        FxUI.Message.warning($t('marketing_pd.components.comp.qxzys_0ea47d'))
        return
      }

      const payload = {
        colorConfigs: [
          {
            color: new Color(this.currentValue).rgb().toString(),
            type: 1,
          },
        ],
      }
      request('/marketing/hexagon/updateHexagonBackgroundColorSetting', { data: payload })
        .then(res => {
          const { errCode, errMsg = '', data = [] } = res || {}
          if (errCode === 0) {
            this.updateBrandColorList(data)
            FxUI.Message.success($t('marketing_pd.components.comp.tjcg_3fdaea'))
            return
          }

          FxUI.Message.error(errMsg || $t('marketing_pd.components.comp.tjsb_6452a0'))
        })
        .catch(() => {
          FxUI.Message.error($t('marketing_pd.components.comp.tjsb_6452a0'))
        })
    },
  },
}
</script>

<style lang="less" module>
.color-picker-container {
  display: flex;
  align-items: center;
}
.color-picker-footer {
  padding-top: 4px;
  padding-bottom: 10px;

  .options {
    display: flex;
    align-items: center;

    .input {
      flex: 1;
      margin-right: 15%;
      margin-bottom: 0 !important;
    }
  }

  .block {
    margin-top: 10px;
    text-align: left;

    .block-title {
      font-size: 12px;
      line-height: 18px;
      color: #181C25;

      span {
        color: #91959E;
      }
    }

    .block-content {
      display: flex;
      flex-wrap: wrap;
      position: relative;

      .color-unit {
        width: 20px;
        height: 20px;
        border-radius: 3px;
        margin: 8px 8px 0 0;
        box-sizing: border-box;
        cursor: pointer;
        border: 1px solid #e9edf5;

        &.active {
          box-shadow: 0 0 3px 2px #ff8000;
        }

        &.plus {
          border: 1px dashed #DEE1E8;
          font-size: 12px;
          color: #DEE1E8;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      .content-menu-wrap {
        box-shadow: 0px 2px 6px 0px #00000026;
        border-radius: 4px;
        background: #FFF;
        font-size: 12px;
        color: #181C25;
        position: absolute;
        z-index: 10;

        .content-menu-item {
          height: 28px;
          min-width: 52px;
          box-sizing: border-box;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          padding: 0 14px;

          &:hover {
            background: #E6F4FF;
          }
        }
      }
    }
  }
}
.color-plus-popper {
  font-size: 12px !important;
  min-width: auto !important;
  padding-top: 6px !important;
  padding-bottom: 6px !important;
  color: #181C25 !important;
}
</style>
