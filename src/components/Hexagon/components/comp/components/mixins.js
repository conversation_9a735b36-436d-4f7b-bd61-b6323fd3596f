import { peekStyles, deepClone } from '../../../utils/index.js'
import { getParentInstanceByComponentName } from '../../../utils/util.js'
import { mapState } from 'vuex'
const baseOn345 = ['input', 'date', 'radio', 'checkbox', 'region', 'cascade', 'file', 'button', 'stepbutton', 'signupbutton'];

export default {
  props: {
    editType: {
      type: String,
      default: '',
    },
    data: {
      type: Object,
      default: () => ({
        id: '',
        name: '',
        components: [],
        style: {},
      }),
    },
    pcAdaptation: {
      type: Boolean,
      default: false,
    },
    isGrid: {
      type: Boolean,
      default: false,
    },
    parent: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      $pageViewInstance: null,
    }
  },
  computed: {
    ...mapState('hexagon/designer', {
      viewportStyle: 'viewportStyle',
    }),
    isInCardForm() {
      let result = this.parent && this.parent.key === 'form-container' && this.parent.typesetting === 'flow' && this.parent.formLayout === 'card';
      return result;
    },
    styles() {
      const {
        style, layout, current, components, type, key, typesetting,
      } = this.data
      let newStyle = { ...style }
      if (layout === 'multiple' && components && components[current]) {
        newStyle = { ...components[current].style }
      }
      // 悬浮按钮fixed转absolute
      if (type === 'suspension' || type === 'contact') {
        newStyle.position = 'absolute'

        const { layoutType = 'circle' } = this.data
        const { borderRadius } = newStyle
        if (!layoutType || layoutType === 'circle') {
          newStyle.borderRadius = borderRadius
          delete newStyle.borderTopLeftRadius
          delete newStyle.borderBottomLeftRadius
        }

        if (layoutType === 'left-half-circle') {
          newStyle.borderTopLeftRadius = borderRadius
          newStyle.borderBottomLeftRadius = borderRadius
          delete newStyle.borderRadius
        }

        if (layoutType === 'rect') {
          delete newStyle.borderTopLeftRadius
          delete newStyle.borderBottomLeftRadius
          newStyle.borderRadius = borderRadius
        }
      }

      if (type === 'container' && key === 'tab-container') {
        newStyle.minHeight = this.data.tabHeight
        delete newStyle.height
        delete newStyle.overflow
      }

      if (typesetting === 'flow') {
        delete newStyle.height
      }

      if (type === 'checkbox' || type === 'radio') {
        delete newStyle.paddingBottom
        delete newStyle.paddingLeft
        delete newStyle.paddingRight
        delete newStyle.paddingTop
      }

      return this.formatStyles(newStyle)
    },
    titleStyle() {
      const { titleStyle = {} } = this.data
      return this.formatStyles(titleStyle)
    },
    wrapStyle() {
      const { style } = this.data
      let newStyle = deepClone(style)
      newStyle = this.formatStyles(newStyle || {})

      delete newStyle.paddingLeft
      delete newStyle.paddingRight
      delete newStyle.paddingTop
      delete newStyle.paddingBottom
      newStyle.borderWidth = 0
      return newStyle
    },
    contentStyle() {
      const { style } = this.data
      let newStyle = deepClone(style)
      delete newStyle.top
      delete newStyle.left
      delete newStyle.position
      delete newStyle.width
      delete newStyle.height
      delete newStyle.marginTop
      delete newStyle.marginBottom
      delete newStyle.marginLeft
      delete newStyle.marginRight
      // delete newStyle.paddingLeft;
      // delete newStyle.paddingRight;
      // delete newStyle.paddingTop;
      // delete newStyle.paddingBottom;
      newStyle = this.formatStyles(newStyle || {})

      return newStyle
    },
    inputStyle() {
      const { inputStyle = {} } = this.data
      return this.formatStyles(inputStyle)
    },
    placeholderStyle() {
      const { placeholderStyle = {} } = this.data
      return this.formatStyles(placeholderStyle)
    },
    confirmPlaceholderStyle() {
      const { confirmPlaceholderStyle = {} } = this.data
      return this.formatStyles(confirmPlaceholderStyle)
    },
    pageViewProps() {
      return this.$pageViewInstance.$props || {
        pcAdaptation: this.pcAdaptation,
        width: this.viewportStyle.width,
        data: []
      }
    },
  },
  created() {
    this.$pageViewInstance = getParentInstanceByComponentName(this.$parent, 'pagerender') || {}
  },
  methods: {
    findIsInGrid(data, rootList) {
      let isInGrid = false;
      const loop = (target, list) => {
        list.map((item) => {
          if (item.id === target.id) {
            isInGrid = true;
          }
          if (!isInGrid && item.components && item.components.length > 0) {
            loop(target, item.components)
          }
        })
      }
      rootList.map((item) => {
        if (item.type === 'gridcontainer') {
          if(!isInGrid && item.components && item.components.length > 0) {
            loop(data, item.components)
          }
        }
      })
      return isInGrid
    },
    formatStyles(styles) {
      // 获取根组件的传入参数
      const rootProps = this.pageViewProps
      // 开启pc适配
      if (rootProps.pcAdaptation) {
        let needScaleCsskey = ['width']
        const container = this.parent || {}
        // 针对会议容器特殊\不在布局内组件处理，容器ID为1586854599692代表是预设模版的详情容器ID
        const isConferenceContainer = container.conference || container.id === '1586854599692'
        // 会议容器下，图片宽度不放大
        if (isConferenceContainer && this.data.type === 'image') {
          needScaleCsskey = needScaleCsskey.splice(needScaleCsskey.indexOf('width') + 1, 1)
        }
        // 悬浮按钮宽度不处理
        if(this.data.type === 'suspension'|| this.data.type === 'contact') {
          needScaleCsskey = [];
          if(styles && styles.left && styles.left > (375 / 2)) {
            styles.right = 375 - styles.left;
            delete styles.left;
          }
        }

        if (container.type === 'container' && !isConferenceContainer) {
          needScaleCsskey.push(...['left', 'right'])
        }
        // 自定义布局里面的组件不做额外处理
        const isInAbsolute = container.type === 'container' && container.typesetting !== 'flow';
        if(isInAbsolute) {
          needScaleCsskey = [];
        }
        // 文件上传组件不做额外处理
        // if(this.data.type === 'file') {
        //   needScaleCsskey = [];
        // }
        // 栅格布局 里面的container不做额外处理。
        // if(container.type === 'container') {
        //   if(this.findIsInGrid(this.data, rootProps.data)) {
        //     needScaleCsskey = [];
        //   }
        // }
         // 栅格布局下不需要改变其宽度以及位置
         if(this.isGrid && !isInAbsolute) {
            needScaleCsskey = [];
            // 栅格布局下，容器宽度为100%
            if(baseOn345.includes(this.data.type)) {
              let marginLeft = styles.marginLeft === 'auto' ? 15 : (styles.marginLeft || 15);
              let marginRight = styles.marginRight === 'auto' ? 15 : (styles.marginRight || 15);
              styles.width = 'calc(100% - ' + (marginLeft + marginRight) + 'px)';
            }else {
              styles.width = '100%';
            }
         }

        // 不在容器内的图片高度适应
        if (this.data.type === 'image' && !container.type) {
          needScaleCsskey.push('height')
        }

        // if(needScaleCsskey.includes('width') && styles['width']) {
        //   needScaleCsskey.push('marginLeft', 'marginRight')
        // }
        needScaleCsskey.forEach(key => {
          if (styles && styles[key]) {
            const cssName = styles[key]
            if (cssName === +cssName) {
              if(baseOn345.includes(this.data.type)) {
                let marginLeft = styles.marginLeft === 'auto' ? 15 : (styles.marginLeft || 15);
                let marginRight = styles.marginRight === 'auto' ? 15 : (styles.marginRight || 15);
                styles[key] = cssName * ((rootProps.width - (marginLeft + marginRight)) / 345)
              } else {
                styles[key] = cssName * (rootProps.width / 375)
              }
            }
          }
        })
      }
      if(this.isInCardForm) {
        styles.borderWidth = 0;
      }

      return peekStyles(styles)
    },
    deepClone,
    peekStyles(styles) {
      return this.formatStyles(styles)
    },
    /**
     * 分离属性，
     * @param {*} all
     * @param {*} detach
     */
    detachAttr(all, detach, parent) {
      // for (const allKey in all) {
      //   for (let i = 0; i < detach.length; i++) {
      //     if (allKey === detach[i]) {
      //       parent[allKey] = all[allKey]
      //       delete all[allKey]
      //     }
      //   }
      // }

      const keys = Object.keys(all)
      keys.forEach(key => {
        if (detach.indexOf(key) > -1) {
          parent[key] = all[key]
          delete all[key]
        }
      })
    },
  },
}
