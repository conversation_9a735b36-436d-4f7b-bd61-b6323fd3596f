<template>
  <div style="overflow:hidden;">
    <div
      :id="data.id"
      :class="[$style.EventsList, $style[data.layout], 'hexagon__designer-comp', pageViewProps.pcAdaptation ? [$style.pc,'hexagon__comp-pc']:'']"
      :style="styles.wrapStyle"
    >
      <div
        v-if="isMultipleGroup(data.range)
          && data.classification && data.classification.length != 0 && data.layout !== 'horizontal-scrolling-layout'"
        :class="[$style.classProduct]"
        :style="styles.classProductStyle"
      >
        <div
          v-for="(item) in data.classification"
          :key="item.key"
          :class="[$style.classItem, item.key == (data.currentItem || data.classification[0].key)? $style.activeClassItem: '']"
        >
          <div :class="[$style.mark, item.key == (data.currentItem || data.classification[0].key) ? $style.activeMark: '']" />
          {{ item.label }}
        </div>
      </div>
      <div
        style="flex: 1"
        :class="$style.content"
      >
        <div
          v-if="enableSearch && data.layout !== 'horizontal-scrolling-layout'"
          :class="[$style.search_input, $style[searchLayout]]"
          :style="styles.searchStyle"
        >
          <v-input
            v-model="keyWord"
            :placeholder="data.searchPlaceholder || $t('marketing_pd.components.comp.ss_e5f71f')"
            @change="queryContent"
          >
            <i
              slot="prefix"
              class="el-input__icon el-icon-search"
            />
          </v-input>
        </div>
        <div
          v-if="tagFilterVisible"
          :class="$style['tag-filter-wrap']"
        >
          <div
            v-for="(value, name) in tagFilterMap"
            :key="name"
            :class="$style['tag-filter-item']"
          >
            <div :class="$style['tag-filter-header']">
              {{ name }}
            </div>
            <div :class="$style['tag-filter-body']">
              <div :class="$style['tag-filter-body-overflow']">
                <div :class="$style['tag-filter-body-wrap']">
                  <div
                    v-for="item in value.list"
                    :key="item.tagId"
                    :class="{
                      [$style['tag-item']]: true,
                      [$style['tag-item-active']]: item.tagId === value.active,
                    }"
                  >
                    {{ item.secondTagName }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          :class="$style['list-wrap']"
          :style="{ width: listWrapWidth }"
        >
          <div
            v-for="(item, index) in lists"
            :key="index"
            :class="[$style.item, isMultipleGroup(data.range) && $style.classificationItem]"
            :style="styles.itemStyle"
          >
            <div :class="$style.image">
              <VImage
                v-if="item.url"
                :class="$style.img"
                :src="item.url"
                fit="cover"
              />
              <span v-else>{{ $t('marketing_pd.commons.tp_20def7') }}</span>
            </div>
            <div :class="$style.memo">
              <div
                :class="$style.title"
                :style="`color:${data.titleColor}`"
              >
                {{ item.title }}
              </div>
              <div
                :class="$style.desc"
                :style="`color:${data.descColor}`"
              >
                {{ item.description || item.startTime }}
              </div>
              <div
                v-if="item.typeName"
                :class="[$style.desc, $style.type]"
              >
                {{ item.typeName }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import mixins from './components/mixins.js'
import request from '../../utils/request.js'
import { formatDateTime } from '../../utils/index.js'
import { DEFAULT_SETTING } from '../setting/eventslist/content.vue'

export default {
  components: {
    VImage: FxUI.Image,
    VInput: FxUI.Input,
  },
  mixins: [mixins],
  data() {
    return {
      dataList: [],
      keyWord: '',
    }
  },
  computed: {
    lists() {
      const { type } = this.data
      if (this.dataList.length) {
        return this.dataList || []
      }
      // if (items.length && range !== "all") {
      //   return items;
      // }
      let itemMap = {
        title: $t('marketing_pd.components.comp.hdbt_902a3d'),
        description: $t('marketing_pd.components.comp.kssj_592c59'),
        // typeName: "线下活动"
      }
      if (type === 'content') {
        itemMap = {
          title: $t('marketing_pd.components.comp.nrbt_c9b763'),
          description: $t('marketing_pd.components.comp.nrms_0ebe8d'),
        }
      }
      return [itemMap, itemMap]
    },
    searchLayout() {
      return this.data.searchLayout || 'small-round'
    },
    enableSearch() {
      if (this.data.enableSearch === undefined) {
        return true
      }

      return this.data.enableSearch
    },
    styles() {
      const { layout } = this.data
      const { componentBackgroundColor = DEFAULT_SETTING[layout].componentBackgroundColor } = this.data
      const style = { ...this.data.style }
      const { gap = DEFAULT_SETTING[layout].style.gap, paddingRight = DEFAULT_SETTING[layout].style.paddingRight } = style
      const wrapStyle = {
        backgroundColor: componentBackgroundColor,
      }
      // const dir = ["Top", "Bottom", "Left", "Right"];
      // padding转margin
      // dir.forEach(key => {
      //   style[`margin${key}`] = style[`padding${key}`];
      //   delete style[`padding${key}`];
      // });

      delete style.width
      delete style.background
      delete style.gap

      style.marginBottom = gap

      if (this.data.layout === 'row') {
        delete style.borderRadius
        delete style.borderStyle
        delete style.borderWidth
        delete style.boxShadow
        delete style.boxShadowColor
        delete style.boxShadowLeft
        delete style.boxshadowRadius
        delete style.boxShadowTop
      }

      if (this.data.layout === 'grid') {
        style.width = `calc(50% - ${gap / 2}px)`
        style.marginRight = gap
      }

      if (this.data.layout === 'horizontal-scrolling-layout') {
        style.marginRight = gap
        delete style.marginBottom
        delete style.height
      }

      const searchStyle = {
        marginBottom: gap,
        color: this.data.searchPlaceholderFontColor || DEFAULT_SETTING[layout].searchPlaceholderFontColor,
        backgroundColor: this.data.searchBackgroundColor || DEFAULT_SETTING[layout].searchBackgroundColor,
        borderColor: this.data.searchBorderColor || DEFAULT_SETTING[layout].searchBorderColor,
      }
      // 如果卡片没有左右边距，搜索栏留默认10px边距
      if (paddingRight === 0) {
        searchStyle.marginLeft = 10
        searchStyle.marginRight = 10
      }

      // 多分组模式下，搜索组件添加一个上边距
      if (this.isMultipleGroup(this.data.range)) {
        searchStyle.marginTop = gap
      }

      this.detachAttr(
        style,
        ['paddingTop', 'paddingBottom', 'paddingLeft', 'paddingRight'],
        wrapStyle,
      )
      return {
        wrapStyle: this.peekStyles(wrapStyle),
        itemStyle: this.peekStyles(style),
        searchStyle: this.peekStyles(searchStyle),
        classProductStyle: this.peekStyles({
          marginRight: paddingRight,
        }),
      }
    },
    listWrapWidth() {
      const { style, layout } = this.data

      if (layout === 'horizontal-scrolling-layout' && !this.pageViewProps.pcAdaptation) {
        const len = this.lists.length
        return `${219 * len + style.gap * (len - 1)}px`
      }

      return 'auto'
    },
    marketingEventAudit() {
      const {
        MarketingEventSet: {
          marketingEventAudit = false,
        } = {},
      } = this.$store.state

      return marketingEventAudit
    },
    isSorted() {
      const { type = 'content', range = '' } = this.data
      if ((type === 'content' && (range === 'custom-site' || range === 'custom-article' || range === 'custom-product')) || (range === 'custom')) {
        return true
      }
      return false
    },
    tagFilterVisible() {
      const { queryType, showFilter, tags = [] } = this.data.materialTagFilter || {}
      return (this.data.range === 'article-tags' || this.data.range === 'product-tags' || this.data.range === 'site-tags')
        && queryType === 1
        && showFilter
        && tags
        && tags.length > 0
    },
  },
  watch: {
    'data.range': function (newVal, oldVal) {
      if (newVal !== oldVal) {
        this.queryLists()
      }
    },
    'data.marketingEventTypes': function (newVal, oldVal) {
      if ((newVal || []).length !== (oldVal || []).length) {
        this.queryLists()
      }
    },
    'data.marketingEventForms': function (newVal, oldVal) {
      if ((newVal || []).length !== (oldVal || []).length) {
        this.queryLists()
      }
    },
    'data.marketingEventIds': function (newVal, oldVal) {
      if (oldVal.length !== newVal.length) {
        this.queryLists()
      }
    },
    'data.contentObjectIds': function (newVal, oldVal) {
      // 考虑排序情况
      if (oldVal.length !== newVal.length) {
        this.queryLists()
      }
    },
    'data.siteGroupId': function (newVal, oldVal) {
      if (oldVal !== newVal) {
        this.queryLists()
      }
    },
    'data.classification': function (newVal, oldVal) {
      if (oldVal !== newVal) {
        this.queryLists()
      }
    },
    'data.currentItem': function (newVal, oldVal) {
      if (oldVal !== newVal) {
        this.queryLists()
      }
    },
    'data.materialTagFilter': function (newVal, oldVal) {
      if (oldVal !== newVal) {
        this.queryLists()
        this.getTagFilterMap()
      }
    },
    // 数组对象 判断相等
    'data.activityFilter': {
      handler(newVal, oldVal) {
        if (JSON.stringify(oldVal) !== JSON.stringify(newVal)) {
          this.queryLists()
        }
      },
      deep: true,
    },
    'data.orderType': function(newVal,oldVal){
      if (oldVal!= undefined && oldVal !== newVal) {
        this.queryLists()
      }
    }
  },
  created() {
    this.queryLists()
    this.getTagFilterMap()
  },
  methods: {
    queryActivity() {
      const { range, marketingEventForms, marketingEventTypes, marketingEventIds, orderType } = this.data
      let params = {}
      if (range === 'all') {
        const activityTypes = []
        marketingEventTypes && marketingEventTypes.length > 0 && marketingEventTypes.forEach(key => {
          let name
          if (key === 'conference') {
            name = '3'
          } else if (key === 'live') {
            name = 'live_marketing'
          } else {
            name = key
          }
          activityTypes.push(name)
        })
        params = {
          isAllActivityType: true,
          activityTypes,
          eventFormList: marketingEventForms || [],
        }
      }

      if (range === 'custom') {
        params = {
          isAllActivityType: false,
          marketingEventIds,
        }
      }

      if (range === 'activity-tags') {
        const {
          materialTagFilter: {
            queryType = 2,
            tags = [],
          } = {},
        } = this.data
        params = {
          isAllActivityType: false,
          materialTagFilter: {
            type: queryType,
            materialTagIds: tags.map(el => el.tagId),
          },
        }
      }

      if (range === 'filter') {
        const { activityFilter } = this.data
        const filterData = (activityFilter && activityFilter[0]) || null
        params = {
          isAllActivityType: false,
          filterData,
        }
      }

      if (this.isSorted) {
        params.orderByIds = true
      }

      if (this.marketingEventAudit) {
        params.filterData = {
          objectAPIName: 'MarketingActivityObj',
          query: {
            filters: [{
              fieldName: 'life_status',
              fieldValues: ['normal'],
              operator: 'EQ',
            }],
            orders: [{
              fieldName: 'last_modified_time',
              isAsc: false,
            }],
          },
        }
      }
      // 自定义活动排序可以拖动  所以走默认的orderType
      params.orderType = range !== 'custom' ?  orderType : 0
      request('/marketing/customizeContent/listActivityList', {
        data: {
          ...params,
          pageNum: 1,
          pageSize: 10,
        },
      }).then(({ errCode, data }) => {
        if (errCode === 0) {
          // const typeNameMaps = {
          //   live_marketing: '线上直播',
          //   3: '线下会议',
          //   multivenue_marketing: '多会场',
          // }
          this.dataList = (data.result || []).map(item => ({
            ...item,
            // typeName: typeNameMaps[item.eventType],
            description: formatDateTime(item.startTime, 'YYYY-MM-DD hh:mm'),
          }))
        }
      })
    },
    isMultipleGroup(range) {
      return range === 'custom-product-more' || range === 'custom-article-more' || range === 'custom-site-more'
    },
    queryContent() {
      let defaultClassificationId = ''
      if (this.data.range && this.isMultipleGroup(this.data.range)) {
        if (this.data.currentItem) {
          defaultClassificationId = this.data.currentItem || ''
        } else {
          defaultClassificationId = this.data.classification ? this.data.classification[0].key || '' : ''
        }
      }
      const {
        contentObjectType, contentObjectIds, siteGroupId = '', materialTagFilter,
      } = this.data
      const params = {
        contentObjectType,
        groupId: defaultClassificationId || siteGroupId,
        keyWord: this.keyWord,
      }
      if (this.isSorted) {
        params.orderByIds = true
      }

      // 指定物料类型
      const specifyRangeList = ['custom', 'custom-site', 'custom-article', 'custom-product']
      if (specifyRangeList.includes(this.data.range)) {
        params.contentObjectIds = contentObjectIds && contentObjectIds.length ? contentObjectIds : []
        delete params.groupId
      } else {
        params.contentObjectIds = null
      }

      const tagRangeList = ['site-tags', 'article-tags', 'product-tags']

      if (tagRangeList.includes(this.data.range) && materialTagFilter && materialTagFilter.tags.length > 0) {
        params.materialTagFilter = {
          type: materialTagFilter.queryType,
          materialTagIds: materialTagFilter.tags.map(el => el.tagId),
        }

        delete params.groupId
      }

      request('/marketing/customizeContent/listContentList', {
        data: {
          ...params,
          pageNum: 1,
          pageSize: 10,
        },
      }).then(({ errCode, data }) => {
        if (errCode === 0) {
          // const typeNameMaps = {
          //   26: "微页面",
          //   4: "产品",
          //   6: "文章",
          //   16: "表单"
          // };
          this.dataList = (data.result || []).map(item => ({
            ...item,
            // typeName: typeNameMaps[item.objectType],
            // startTime: formatDateTime(item.createTime, "YYYY-MM-DD hh:mm"),
            url: item.thumbnailUrl,
          }))
        }
      })
    },
    queryLists() {
      if (this.data.type === 'content') {
        this.queryContent()
      } else {
        this.queryActivity()
      }
    },
    getTagFilterMap() {
      if (!this.tagFilterVisible) {
        this.tagFilterMap = {}
      }

      const filterMap = {}
      const { tags = [] } = this.data.materialTagFilter || {}
      tags.forEach(tag => {
        const { firstTagName } = tag

        if (!filterMap[firstTagName]) {
          filterMap[firstTagName] = {
            active: 'ALL',
            list: [
              {
                firstTagName,
                secondTagName: $t('marketing.commons.qb_a8b0c2'),
                tagId: 'ALL',
              },
            ],
          }
        }

        filterMap[firstTagName].list.push({ ...tag })
      })

      this.tagFilterMap = filterMap
    },
  },
}
</script>
<style lang="less" module>
.EventsList {
  display: flex;
  .classProduct {
    width: 100px;
    min-height: 100px;
    float: left;
    margin-right: 5px;
    background: #F6F5F9;
    padding-top: 8px;
    flex: none;

    .classItem {
      height: 48px;
      line-height: 48px;
      padding: 0 10px;
      .mark {
        width: 3px;
        height: 18px;
        float: left;
        margin-top: 16px;
        margin-left: -9px;
      }
      .activeMark {
        background: #FF8000;
      }

    }
    .activeClassItem {
      background: #ffffff;
      text-overflow: ellipsis;
      display: -webkit-box;
      word-break: break-all;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
      overflow: hidden;
      }
  }
  .search_input{
    background: #F2F3F5;
    border: 1px solid #c1c5ce;
    :global {
      .el-input__inner {
        border: none;
        background: inherit;
        color: inherit;
        height: 36px;
        line-height: 36px;
        border-radius: 0;
        border: none;

        &::placeholder {
          color: inherit;
        }
      }

      .el-input__prefix {
        color: inherit;
      }
      .el-input__icon {
        line-height: 36px;
      }
    }

    &.small-round {
      border-radius: 6px;
    }

    &.big-round {
      border-radius: 36px;
    }
  }
  .tag-filter-wrap {
    padding: 10px 0;
    border-bottom: 1px solid #DEE1E8;
    background: #FFF;

    .tag-filter-item {
      margin-bottom: 10px;
      display: flex;
      align-items: center;

      &:last-of-type {
        margin-bottom: 0;
      }

      .tag-filter-header {
        height: 28px;
        min-width: 40px;
        padding: 0 10px;
        background: #FFF;
        font-size: 12px;
        color: #181C25;
        display: flex;
        align-items: center;
        justify-content: center;
        flex: none;
      }

      .tag-filter-body {
        flex: 1;
        height: 28px;
        overflow: hidden;

        .tag-filter-body-overflow {
          overflow-y: hidden;
          overflow-x: auto;
          height: 50px;
        }

        .tag-filter-body-wrap {
          height: 28px;
          display: flex;
        }

        .tag-item {
          padding: 0 8px;
          font-size: 12px;
          color: #545861;
          height: 28px;
          display: inline-block;
          line-height: 28px;
          text-align: center;
          background: #F2F3F5;
          border-radius: 4px;
          margin-right: 8px;
          flex: none;
          cursor: pointer;

          &.tag-item-active {
            background-color: #FFF7E6;
            color: #FF8000;
          }

          &:last-of-type {
            margin-right: 0;
          }
        }
      }
    }
  }
  .item {
    display: flex;
    margin-bottom: 10px;
    vertical-align: top;

    &:last-of-type {
      margin-bottom: 0 !important;
    }
    .image {
      width: 100px;
      height: 80px;
      background: #e9edf5;
      border-radius: 4px;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0;
      .img {
        flex: 1;
        width: 100%;
        height: 100%;
      }
      span {
        color: #c1c5ce;
        font-size: 12px;
      }
    }
    .memo {
      min-width: 0;
      flex: 1;
      margin-left: 5px;
      padding-bottom: 5px;
      .title {
        font-size: 14px;
        color: #181c25;
        margin-bottom: 6px;
        margin-top: 3px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        word-wrap: break-word;
        word-break: break-all;
      }
      .desc {
        color: #91959e;
        font-size: 12px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        word-wrap: break-word;
        word-break: break-all;
        &.type {
          margin-top: 20px;
        }
      }
    }
  }

  &.column {
    background: #F6F5F9;
    .item {
      flex-direction: column;
      background: #FFF;
      .image {
        width: 100%;
        padding-top: 50.14%; // 180/356 = 0.5014
        border-radius: 0;
        position: relative;
        height: 0;

        .img {
          position: absolute;
          left: 0;
          top: 0;
          right: 0;
          bottom: 0;
        }
        span {
          position: absolute;
          left: 0;
          top: 0;
          right: 0;
          bottom: 0;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      .memo {
        margin: 0;
        padding: 12px 14px;

        .title {
          -webkit-line-clamp: 1;
          color: #000;
          font-size: 16px;
          line-height: 24px;
          margin: 0;
        }

        .desc {
          color: #91959E;
          font-size: 14px;
          line-height: 20px;
          margin-top: 3px;
        }
      }
      .type {
        display: none;
      }
    }
    // .classificationItem{
    //   .image{
    //     width: auto;
    //     height: 137px;
    //   }
    // }
  }

  &.grid {
    .item {
      display: inline-block;
      background: #FFF;
      overflow: hidden;

      &:nth-child(2n + 0) {
        margin-right: 0 !important;
      }
      .image {
        width: 100%;
        padding-top: 100%;
        border-radius: 0;
        position: relative;
        height: 0;

        .img {
          position: absolute;
          left: 0;
          top: 0;
          right: 0;
          bottom: 0;
        }
        span {
          position: absolute;
          left: 0;
          top: 0;
          right: 0;
          bottom: 0;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
      .memo {
        margin: 0;
        padding: 12px 14px;

        .title {
          -webkit-line-clamp: 2;
          color: #181C25;
          font-size: 16px;
          line-height: 24px;
          height: 48px;
          margin: 0;
        }
      }
      .type, .desc {
        display: none;
      }
    }
  }

  &.big-image-card-layout {
    .item {
      position: relative;
      overflow: hidden;
      .image {
        width: 100%;
        padding-top: 56.18%; // 200/356 = 0.5618
        border-radius: 0;
        position: relative;
        height: 0;

        .img {
          position: absolute;
          left: 0;
          top: 0;
          right: 0;
          bottom: 0;
        }
        span {
          position: absolute;
          left: 0;
          top: 0;
          right: 0;
          bottom: 0;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
      .memo {
        position: absolute;
        left: 0;
        bottom: 0;
        right: 0;
        margin: 0;
        padding: 0;

        .desc, .type {
          display: none;
        }
        .title {
          height: 48px;
          line-height: 48px;
          background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.9) 100%);
          padding: 0 14px;
          font-size: 16px;
          font-weight: bold;
          color: #FFF;
          -webkit-line-clamp: 1;
          margin: 0;
        }
      }
    }
  }

  &.card-list-layout, &.row {
    .item {
      background: #FFF;
      padding: 10px;

      .memo {
        padding: 0;
        margin-left: 10px;
        display: flex;
        flex-direction: column;
        justify-content: center;

        .title {
          font-size: 16px;
          line-height: 24px;
          margin: 0;
        }

        .desc {
          font-size: 14px;
          -webkit-line-clamp: 1;
          margin-top: 6px;
        }
      }
    }
  }

  &.row {
    .item {
      position: relative;

      &::after {
        content: '';
        position: absolute;
        left: 10px;
        right: 10px;
        bottom: 0;
        height: 1px;
        background: #DEE1E8;
      }
    }
  }

  &.horizontal-scrolling-layout {
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }

    .list-wrap {
      width: 3000px;
      white-space: nowrap;
    }
    .item {
      width: 219px;
      height: 240px;
      background: #FFF;
      padding: 10px;
      margin-bottom: 0;
      flex-direction: column;
      display: inline-block;

      &:last-of-type {
      margin-right: 0 !important;
    }

      .image {
        width: 100%;
        padding-top: 55%; // 110/200 = 0.55
        border-radius: 3px;
        position: relative;
        height: 0;

        .img {
          position: absolute;
          left: 0;
          top: 0;
          right: 0;
          bottom: 0;
        }
        span {
          position: absolute;
          left: 0;
          top: 0;
          right: 0;
          bottom: 0;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      .memo {
        padding: 10px 0;

        .type {
          display: none;
        }
        .desc {
          margin-top: 8px;
          font-size: 12px;
          line-height: 18px;
          color: #91959E;
        }
        .title {
          line-height: 24px;
          font-size: 16px;
          color: #FFF;
          -webkit-line-clamp: 2;
          margin: 0;
        }
      }
    }
  }

  /**
    PC样式
  */
  &.pc{
   &.big-image-card-layout,&.column,&.grid{
    .list-wrap{
      // display: flex;
    }
    .item {
      display: inline-block!important;
      width: calc(33.33% - 6px)!important;
      margin-right: 8px!important;
      &:nth-child(3n){
        margin-right: 0!important;
      }
    }
   }
  }
}
</style>
