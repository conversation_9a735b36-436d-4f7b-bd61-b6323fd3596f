import mutations from "./mutations";
import { deepClone, rootDispatch } from '../utils';
import { getAutoRenderCompChildren } from '../config/index.js'

const getInitialState = () => ({
  compData: {},
  comp: [],
  gridStyle: {
    height: 300,
    width: 374
  },
  gridOffset: {
    x: 0,
    y: 0
  },
  currentComp: {},
  moveTargetIndex: -1,
  // 用户栅格布局嵌套子栅格布局
  childGridComp: {},
  moveTargetChildIndex: -1,
});

export const state = () => getInitialState();

const genNewCompData = (payload, newComps) => {
  let newPayload = deepClone(payload);

  if(newPayload.type === 'button') {
      newPayload.style.marginTop = 15;
  }
  let maxNum = 0;
  newComps.map((item) => {
    if(maxNum < item.compNum) {
      maxNum = item.compNum || 0;
    }
  });
  newPayload.compNum = maxNum + 1;
  newPayload.sort = maxNum;
  newPayload = autoRender(newPayload)

  return {
    ...newPayload,
  }
};

const getIndexById = (id, list) => {
  let targetIndex = -1;
  for(let i = 0; i < list.length; i++) {
    if(list[i] && list[i].id && list[i].id === id) {
      targetIndex = i;
      break;
    }
  }
  return targetIndex;
}



// 自动生成布局组件子组件
const autoRender = (payload) => {
  if (payload.type === 'container') {
    const createCompMaps = getAutoRenderCompChildren(payload.typeValue) || {};
    if (createCompMaps) {
      // 是否根据自组件数据进行创建
      const isRenderFormData = !!createCompMaps.id;
      if (isRenderFormData) {
        const { components } = createCompMaps
        payload = {
          ...payload,
          ...createCompMaps,
          id: payload.id || createId(),
        }
        payload.components = [...components]
      }
    }
  }
  return payload
}
// 
const doPushComp = (newComp, newItem, compData) => {
  let targetIndex = -1;
  for(let i = 0; i < newComp.length; i++) {
    if(newComp[i].type === 'gridBlankItem') {
      targetIndex = i;
      break;
    }
  }
  // 如果存在空白组件，则替换空白组件
  if(targetIndex !== -1) {
    newComp.splice(targetIndex, 1, newItem);
  }else {
    // 
    if(compData.gridLayout) {
      let count = compData.gridLayout.split(',').length;
      if(count > newComp.length) {
        newComp.push(newItem);
      }else {
        newComp.splice(count - 1, 1, newItem);
      }
    }else {
      newComp.push(newItem);
    }
  }
  return newComp;
}
const actions = {
  addGridComp(
    {
      commit,
      state: { comp, compData },
      dispatch
    },
    payload,
  ) {
    let newComp = deepClone(comp);
    let newItem = genNewCompData(payload, newComp);
    commit("setState", {
      comp: doPushComp(newComp, newItem, compData)
    });

    dispatch("updateAllGridDataToPage");
    rootDispatch("calcCompCount").call(this, "components");
    //自动打开组件设置
    setTimeout(() => rootDispatch("setPageCompSettingData", deepClone(newItem)).call(this), 160);
  },
  addDragGridComp(
    {
      commit,
      state: { 
        comp, 
        compData, 
        moveTargetIndex, 
        moveTargetChildIndex,
        childGridComp
      },
      dispatch
    }, 
    payload,
  ) {
    if(childGridComp && childGridComp.type === 'gridcontainer') {
      // 只能在栅格布局下嵌套一层栅格
      if(payload.type === 'gridcontainer') {
        return;
      }
      if(moveTargetChildIndex === -1) {
        return;
      }
      if(moveTargetIndex === -1) {
        return;
      }
      let newComp = deepClone(comp);
      let newChildComp = deepClone(childGridComp.components);
      let newItem = genNewCompData(payload, newChildComp);
      newChildComp[moveTargetChildIndex] = {
        ...newItem
      };
      newComp[moveTargetIndex] = {
        ...childGridComp,
        components: newChildComp
      }
      commit("setState", {
        comp: newComp
      });
      dispatch("updateAllGridDataToPage");
      rootDispatch("calcCompCount").call(this, "components");
      //自动打开组件设置
      setTimeout(() => rootDispatch("setPageCompSettingData", deepClone(newItem)).call(this), 160);
    }else {
      if(moveTargetIndex === -1) {
        return;
      }
      let newComp = deepClone(comp);
      let newItem = genNewCompData(payload, newComp);
      newComp[moveTargetIndex] = {
        ...newItem,
      };
      commit("setState", {
        comp: newComp
      });
      dispatch("updateAllGridDataToPage");
      rootDispatch("calcCompCount").call(this, "components");
      //自动打开组件设置
      setTimeout(() => rootDispatch("setPageCompSettingData", deepClone(newItem)).call(this), 160);
    }
  },
  //设置画布坐标位置
  setGridOffset({ state, commit }, offset) {
    commit("setState", {
      gridOffset: {
        ...state.gridOffset,
        ...offset
      }
    });
  },
  setGridStyle({ state, commit }, style) {
    commit("setState", {
      gridStyle: {
        ...state.gridStyle,
        ...style
      }
    });
  },
  doCopyGridComp({ state, commit }, payload) {

  },
  setMoveTargetIndex({ state, commit }, index) {
    commit("setState", {
      moveTargetIndex: index
    });
  },
  setChildGridComp({state, commit}, payload) {
    commit("setState", {
      childGridComp: payload,
    });
  },
  setMoveTargetChildIndex({state, commit}, index) {
    commit("setState", {
      moveTargetChildIndex: index
    });
  },
  setGridComp({state, commit, dispatch}, {comps, parent}) {
    if(parent) {
      let newComp = deepClone(state.comp);
      let targetIndex = getIndexById(parent.id, newComp);
      if(targetIndex !== -1) {
        newComp[targetIndex] = {
          ...parent,
          components: comps
        }
      }
      commit("setState", {
        comp: newComp,
      });
    }else {
      commit("setState", {
        comp: comps,
      });
    }
   
    dispatch("updateAllGridDataToPage");
  },
  updateCompStyle({state, commit, dispatch}, payload) {
    let newComp = deepClone(state.comp).map((item) => {
      if(item.id === payload.data.id) {
        item.style = {
          ...item.style,
          ...payload.style,
        }
      }
      return item;
    });
    commit("setState", {
      comp: newComp
    });
    dispatch("updateAllGridDataToPage");
  },
  setCurrentComp({state, commit}, comp) {
    commit("setState", {
      currentComp: comp
    });
  },
  removeGridCompById({state, commit, dispatch}, {
    id,
    parent
  }) {
    if(parent && parent.type === 'gridcontainer') {
      let newComp = deepClone(state.comp);
      let newChildComp = deepClone(parent.components);
      let targetIndex = getIndexById(id, newChildComp);
     
      if(targetIndex !== -1) {
        newChildComp[targetIndex] = {
          type: 'gridBlankItem',
        }
      }
      let moveTargetIndex = getIndexById(parent.id, newComp);
      if(moveTargetIndex !== -1) {
        newComp[moveTargetIndex] = {
          ...parent,
          components: newChildComp
        }
      }
      commit("setState", {
        comp: newComp,
        currentComp: {},
        childGridComp: {},
        moveTargetIndex: -1,
        moveTargetChildIndex: -1,
      });
    }else {
      let targetIndex = getIndexById(id, state.comp);
      let newComp = deepClone(state.comp);

      if(targetIndex !== -1) {
        newComp[targetIndex] = {
          type: 'gridBlankItem',
        }
      }
      commit("setState", {
        comp: newComp,
        currentComp: {}
      });
    }
    
    dispatch("updateAllGridDataToPage");
    rootDispatch("calcCompCount").call(this, "components");
    setTimeout(() => rootDispatch("setPageCompSettingData", deepClone(state.compData)).call(this), 160);
  },
  updatePropertyById({state, commit, dispatch}, payload) {
    if(payload.id === state.compData.id) {
      commit("setState", {
        compData: payload,
      });
      dispatch('setGridStyle', payload.style);
    }else {
      let newList = deepClone(state.comp);
      let targetIndex = getIndexById(payload.id, newList);
      if(targetIndex !== -1) {
        newList[targetIndex] = payload;
      }else {
        // 如果当前组件是栅格布局，则需要遍历子组件
        let childList = [];
        for(let i = 0; i < newList.length; i++) {
          if(newList[i].type === 'gridcontainer') {
            childList = deepClone(newList[i].components);
            let childTargetIndex = getIndexById(payload.id, childList);
            if(childTargetIndex !== -1) {
              childList[childTargetIndex] = payload;
              newList[i] = {
                ...newList[i],
                components: childList
              }
              break;
            }
          }
        }
      }
      // let newList = state.comp.map(item => {
      //   if(item.id === payload.id) {
      //     return payload;
      //   }else {
      //     return item;
      //   }
      // });
      commit("setState", {
        comp: newList,
      });
    }
    rootDispatch("setPageCompSettingData", payload).call(this);
    dispatch("updateAllGridDataToPage");
  },
  updateAllGridDataToPage({state, commit}) {
    const {
      designer: { pageMaps },
      layoutTabValue
    } = this.state.hexagon;
    let data = pageMaps[layoutTabValue];
    const newCompData = {
      ...data,
      ...state.compData,
      style: {
        ...data.style,
        ...state.gridStyle
      },
      components: state.comp
    };
    let updateDate = deepClone(newCompData);
    commit("setState", {
      compData: updateDate,
    });
    rootDispatch("updateGridData", updateDate).call(this);
  },
  updateGridDataFromCanvas({state, commit}, payload) {
    if(payload.id === state.compData.id) {
      commit("setState", {
        comp: payload.components,
        compData: payload,
      });
    }
    // 由调用方直接同步到page下面
  }
};

export default {
  namespaced: true,
  state,
  mutations: mutations(getInitialState()),
  actions
};
