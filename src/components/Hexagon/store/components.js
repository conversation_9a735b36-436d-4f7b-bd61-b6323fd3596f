import { components, button } from '../config'
import { parseFormFieldName } from '../utils/parseFormFieldName'
import { createCompCountMaps, genCompKey } from '../utils/compCounter'
import mutations from './mutations'

const getInitialState = () => ({
  submitButton: button,
  components,
  lists: [],
  compCountMaps: {},
  formCompMaxLimit: 200, // 表单组件可添加最大数
  formCompCount: 0, // 表单组件总个数
  exceedsMaxLimit: false, // 是否超出表单组件最大数限制
  compMaxCountMaps: {},
  // 暂存需要更新的原始组件数据
  needUpdateCompData: {},
  beforeCompDataCreate: comp => comp,
})

export const state = () => getInitialState()

/**
 * 判断微页面内所有子页面是否含有表单，有则返回true
 * @param {Array} pages
 * @returns
 */
const checkFormExistsInAllPages = ({ pages = [], editPageId }) => pages.some(page => {
  if (page.content && page.id !== editPageId) {
    const pageContent = JSON.parse(page.content) || {}
    const result = (pageContent.components || []).some(
      comp => {
        if (comp.type === 'container' && comp.key === 'form-container') {
          return true
        } if (comp.type === 'gridcontainer') {
          return (comp.components || []).some(item => item.type === 'container' && item.key === 'form-container')
        }
        return false
      },
    )
    return result
  }
  return false
})

const actions = {
  /**
   * 通过计算生成组件
   */
  render({ state, commit }) {
    const compMaxCountMaps = { ...state.compMaxCountMaps }
    const needUpdateCompData = { ...state.needUpdateCompData }
    const {
      layoutTabValue = 'page', layoutTabType, pages, editPageId, canvas,
    } = this.state.hexagon

    // 所有页面中是否已存在表单
    const isFormExistsInAllPages = checkFormExistsInAllPages({
      editPageId,
      pages: pages || [],
    })
    console.log('layoutTabType:', layoutTabType,'layoutTabTypeValue:', layoutTabValue, 'canvas.compData.key:', canvas.compData.key)
    const lists = components.reduce((comps, comp) => {
      // 导航布局、自定义布局、活动组件和个人信息组件隐藏表单控件
      if (
        (layoutTabType === 'auto'
          || layoutTabType === 'personal'
          || layoutTabType === 'list'
          || layoutTabType === 'tab-container'
          || canvas.compData.key === 'activity-container'
        )
        && comp.value === 'formComp'
      ) {
        return comps
      }
      // 布局组件隐藏个人信息组件
      if (layoutTabType !== 'personal' && comp.value === 'personalComp') {
        return comps
      }
      comp = state.beforeCompDataCreate({ ...comp })
      if (comp.hide) {
        return comps
      }

      let matchedKey = 'page'
      if (layoutTabValue !== 'page') {
        if (layoutTabType === 'flow') {
          matchedKey = 'flow'
        } else if (layoutTabType === 'grid') {
          matchedKey = 'grid'
        } else {
          matchedKey = 'canvas'
        }
      }
      const subcomps = comp.components.reduce((subcomps, subcomp) => {
        // 记录组件最大限制值
        if (subcomp.max) {
          compMaxCountMaps[`${subcomp.title}`] = subcomp.max
        }
        if (subcomp.value === 'button' && layoutTabValue !== 'page' && canvas.compData.typeValue === 'step-form') {
          return subcomps
        }

        // 支付表单 & 分布表单 隐藏提交按钮
        if (['order', 'step-form'].indexOf(layoutTabType) >= 0 && layoutTabValue !== 'page' && subcomp.value === 'button') {
          return subcomps
        }
        // 表单flow布局仅允许显示表单控件和基础控件
        if (matchedKey === 'flow' && canvas.compData.key === 'form-container' && ['content', 'eventslist'].indexOf(subcomp.value) >= 0) {
          return subcomps
        }
        // 根据showIn处理页面布局与画板展示可用组件的逻辑
        if (
          subcomp.showIn.indexOf(matchedKey) !== -1
          || (layoutTabValue !== 'page'
            && subcomp.showIn.indexOf(layoutTabType) !== -1)
          || (canvas.compData.key === 'activity-container' && subcomp.showIn.indexOf('activity-comp') !== -1)
        ) {
          let disable = subcomp.disable || false
          let fieldName
          const config = subcomp.config || {}
          const compNum = state.compCountMaps[genCompKey(config)]

          // 处理表单组件fieldName生成规则
          config.isFormComp && (fieldName = parseFormFieldName(subcomp.title))

          // 页面组件数量计算，超出最大值禁止生成，当组件大于最大限制量时禁用组件
          disable = (config.isFormComp && state.exceedsMaxLimit)
            || (compNum && compNum >= subcomp.max)
            || (config.key === 'form-container' && isFormExistsInAllPages)
          // 组件创建之前钩，beforeComponentCreate
          let compData = state.beforeCompDataCreate(
            {
              ...subcomp,
              config: {
                ...config,
                ...((fieldName && { fieldName }) || {}),
              },
              disable,
            },
          )
          // 合并需要更新的组件数据
          const needUpdateCompDataArr = Object.keys(needUpdateCompData)
          if (needUpdateCompDataArr.length) {
            needUpdateCompDataArr.forEach(key => {
              const keySplit = key.split(':')
              if (config[keySplit[0]] && config[keySplit[0]] === keySplit[1]) {
                compData = {
                  ...compData,
                  ...needUpdateCompData[key],
                }
              }
            })
          }
          subcomps.push(compData)
        }
        return subcomps
      }, [])
      if (subcomps.length) {
        comps.push({
          ...comp,
          components: subcomps,
        })
      }
      return comps
    }, [])
    console.log('rerender components:', lists)
    commit('setState', {
      compMaxCountMaps,
      lists,
    })
  },
  // 更新组件原始数据
  updateOriginCompData({ commit, dispatch }, payload) {
    const needUpdateCompData = payload || {}
    commit('setState', {
      needUpdateCompData,
    })
    dispatch('render')
  },
  // 计算组件数量
  calcCompCount({ commit, dispatch, state }) {
    const { pageCompList } = this.state.hexagon.designer
    const { compMaps, formCompCount } = createCompCountMaps(pageCompList)
    commit('setState', {
      compCountMaps: compMaps,
      formCompCount,
      exceedsMaxLimit: formCompCount >= state.formCompMaxLimit,
    })
    setTimeout(() => dispatch('render'), 100)
  },
}

export default {
  namespaced: true,
  state,
  mutations: mutations(getInitialState()),
  actions,
}
