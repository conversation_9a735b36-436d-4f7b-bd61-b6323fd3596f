import {
  rootDispatch, createId, deepClone, arrayTool,
} from '../utils/index.js'
import {
  getCompFieldNameByKey,
  genComp<PERSON>ey,
} from '../utils/compCounter.js'
// import { parseFormFieldName } from "../utils/parseFormFieldName";
import { RELATED_COMP_FIELDS, genContainerConfig, genStepButtonConfig } from '../config/index.js'
import mutations from './mutations.js'

let delayToUpdate

// 修复兼容历史表单组件样式问题
function autoFixOldFormCompStyle(data) {
  // 组件高度转化成输入框高度
  if (
    ['input', 'region', 'radio', 'date', 'cascade'].indexOf(data.type) !== -1
    && data.style.height
  ) {
    (data.inputStyle || (data.inputStyle = {})).height = data.style.height
    delete data.style.height
  } else if (
    ['checkbox', 'text'].indexOf(data.type) !== -1
    && data.style.height
  ) {
    // 删除组件高度
    delete data.style.height
  }
  return data
}



/**
 * 获取当前正在编辑中容器的ID
 * @param {Object} compData
 */
function getCurrentEditingContainerId(compData) {
  if (compData.layout === 'multiple' && compData.components[compData.slideIndex]) {
    return compData.components[compData.slideIndex].id
  }
  return compData.id
}

const getInitialState = () => ({
  compData: {},
  comp: [],
  selected: [],
  copyCompTemp: [],
  pageTopOffsetMap: {},
  visualStyle: {
    visual: false,
    height: 0,
  },
  canvasStyle: {
    width: 375,
    height: 680,
  },
  canvasOffset: {
    x: 0,
    y: 0,
  },
  indices: [],
  vLines: [],
  hLines: [],
  relatedMaps: {}, // 组件关联map
  historyRecord: {}, // 记录操作历史
})

export const state = () => getInitialState()

// 画板组件生成时默认样式
const canvasCompDefaultStyle = {
  text: {
    width: 345,
    paddingLeft: 0,
    paddingRight: 0,
  },
}

// 添加到画布时默认需要添加预设默认样式的组件方法
const addCanvasCompDefaultStyle = compType => canvasCompDefaultStyle[compType] || {}

// 解析添加到画布组件的样式
const parseAddCanvasCompStyle = (compType, style) => {
  const defaultStyle = addCanvasCompDefaultStyle(compType)
  return {
    ...style,
    ...defaultStyle,
    position: 'absolute',
  }
}

// 处理添加到画布的组件数据
const parseAddCanvasCompData = compData => {
  const { type, style } = compData
  let data = compData || {}
  return {
    getData() {
      return data
    },
    defaultStyle() {
      data = { ...data, style: parseAddCanvasCompStyle(type, style) }
      return this
    },
    // 默认给自定义apiName设置默认值
    setDefaultApiName() {
      if (!data.customFieldName && data.fieldName) {
        data.customFieldName = data.fieldName
      }
      return this
    },
    // 移除flow布局下组件的绝对定位
    removeFlowLayoutPosition() {
      data.style.position = 'relative'
      delete data.style.left
      delete data.style.top
      return this
    },
    // 移除定位布局下组件的margin和转成absolute定位属性
    removeAbsoluteLayoutMargin() {
      if (data.style) {
        // data.style.position = "absolute";
        delete data.style.marginTop
        delete data.style.marginBottom;
        ['marginTop', 'marginLeft', 'marginBottom', 'marginRight'].forEach(key => {
          if (data.style[key]) {
            data.style[`padding${key.split('margin')[1]}`] = data.style[key]
            delete data.style[key]
          }
        })
      }
      return this
    },
  }
}

const actions = {
  /**
   * 存储当前组件列表数据状态到历史记录
   * @param {Object} param
   */
  addHistoryRecord({ commit, state }) {
    const containerId = getCurrentEditingContainerId(state.compData)

    const historyRecord = deepClone(state.historyRecord || {})

    const currentHistoryRecord = historyRecord[containerId] || (historyRecord[containerId] = {})

    let record = []

    if (currentHistoryRecord.list) {
      record = currentHistoryRecord.list
    }

    // 如果历史记录指针不是在历史记录最后位置则移除指针当前位置到末尾的历史记录后再添加新纪录
    if (
      currentHistoryRecord.index > -1
      && currentHistoryRecord.index !== record.length - 1
    ) {
      record.splice(currentHistoryRecord.index + 1)
    }
    record.push(state.comp)

    // 历史记录最多记录100条记录，超出删除之前记录
    const recordLen = record.length
    if (recordLen > 100) {
      record = record.splice(recordLen - 100, recordLen)
    }
    currentHistoryRecord.list = record
    currentHistoryRecord.index = recordLen - 1

    historyRecord[containerId] = currentHistoryRecord

    commit('setState', {
      historyRecord,
    })
  },
  initHistoryRecord({ state, dispatch }) {
    const containerId = getCurrentEditingContainerId(state.compData)
    // 初始化当前容器历史记录
    if (!state.historyRecord[containerId]) {
      dispatch('addHistoryRecord')
    }
  },
  /**
   * 返回上一次历史记录
   * @param {Object} param
   */
  undoHistoryRecord({ commit, state, dispatch }) {
    const containerId = getCurrentEditingContainerId(state.compData)
    const historyRecord = deepClone(state.historyRecord || {})
    const record = historyRecord[containerId] || {}

    const currentRecord = (record.list && record.list[record.index - 1]) || null

    console.log('undoHistoryRecord:', currentRecord)
    if (currentRecord) {
      historyRecord[containerId].index = record.index - 1

      commit('setState', {
        comp: currentRecord,
        historyRecord,
      })
      dispatch('saveCanvasCompDataToPageData')
      dispatch('renderRelatedMaps')
      rootDispatch('calcCompCount').call(this, 'components')
    }
  },
  /**
   * 恢复下一次历史记录
   * @param {Object} param
   */
  restoreHistoryRecord({ commit, state, dispatch }) {
    const containerId = getCurrentEditingContainerId(state.compData)
    const historyRecord = deepClone(state.historyRecord || {})
    const record = historyRecord[containerId] || {}
    const currentRecord = (record.list && record.list[record.index + 1]) || null
    if (currentRecord) {
      historyRecord[containerId].index = record.index + 1
      commit('setState', {
        comp: currentRecord,
        historyRecord,
      })
      dispatch('saveCanvasCompDataToPageData')
      dispatch('renderRelatedMaps')
      rootDispatch('calcCompCount').call(this, 'components')
    }
  },
  setCanvasLineData({ commit }, { indices, vLines, hLines }) {
    commit('setState', {
      indices,
      vLines,
      hLines,
    })
  },
  // 保存复制组件
  setCopyCompTemp({ commit }, copyCompTemp) {
    commit('setState', {
      copyCompTemp,
    })
  },
  // 设置画布坐标位置
  setCanvasOffset({ state, commit }, offset) {
    commit('setState', {
      canvasOffset: {
        ...state.canvasOffset,
        ...offset,
      },
    })
  },
  // 设置自定义布局距离页面顶部偏移量的映射
  setPageTopOffsetMap({ state, commit }, payload) {
    commit('setState', {
      pageTopOffsetMap: {
        ...state.pageTopOffsetMap,
        [payload.id]: payload.offset,
      },
    })
  },
  // 选中画布组件
  setCanvasSelected({ commit, state }, data) {
    if (data instanceof Array) {
      commit('setState', {
        selected: data,
      })
    } else {
      const selected = deepClone(state.selected)
      selected[data.index] = data.value
      commit('setState', {
        selected,
      })
    }
  },
  renderRelatedMaps({ state: { comp }, commit }) {
    // 查询组件是否有关系配置，有则放入关系map
    const relatedMaps = comp.reduce((maps, item, i) => {
      Object.keys(RELATED_COMP_FIELDS).forEach(key => {
        maps[key] = maps[key] || {
          ...RELATED_COMP_FIELDS[key],
          needUpdate: [],
        }
        if (RELATED_COMP_FIELDS[key].comp === item.type) {
          maps[key] && maps[key].needUpdate.push(i)
        }
      })
      return maps
    }, {})
    commit('setState', {
      relatedMaps,
    })
  },
  // 添加画板切换
  addCanvasTabs({ commit, state: { comp, compData, canvasStyle }, dispatch }) {
    let newComp = []
    let compLen = comp.length
    const container = genContainerConfig({
      id: createId(),
      name: compData.name,
      key: compData.key,
      typeValue: compData.typeValue,
      typesetting: compData.typesetting,
    })
    // 分步表单时，默认加上分步按钮
    if (compData.typeValue === 'step-form') {
      container.components.push(genStepButtonConfig({
        name: $t('marketing_pd.commons.tj_939d53'),
        tip: $t('marketing_pd.commons.tjcg_23b62e'),
      }))
    }

    if (compData.layout === 'multiple') {
      newComp = [...comp, container]
    } else {
      newComp = [
        {
          ...container,
          style: {
            ...container.style,
            ...canvasStyle,
          },
          components: comp,
        },
        {
          ...container,
          style: {
            ...container.style,
            ...canvasStyle,
          },
        },
      ]
      compLen = 1
    }

    commit('setState', {
      comp: newComp,
      canvasStyle: {
        ...container.style,
        ...canvasStyle,
      },
      compData: {
        ...compData,
        slideIndex: compLen,
        current: 0, // 默认显示第一个布局
        layout: 'multiple', // 变更容器布局类型
      },
    })
    dispatch('saveCanvasCompDataToPageData')
  },
  changeCanvasTabs(
    {
      commit,
      state: {
        compData, comp, canvasStyle, canvasOffset,
      },
      dispatch,
    },
    slideIndex,
  ) {
    const currComp = comp[slideIndex]
    let newStyle = {}
    if (currComp.type === 'container') {
      newStyle = currComp.style
    } else {
      newStyle = {
        ...canvasStyle,
        ...canvasOffset,
      }
    }
    commit('setState', {
      compData: {
        ...compData,
        slideIndex,
      },
      canvasStyle: newStyle,
    })
    dispatch('saveCanvasCompDataToPageData')
  },
  removeCanvasTabs({ dispatch, state, commit }, slideIndex) {
    const comp = deepClone(state.comp)
    comp.splice(slideIndex, 1)
    // 分步表单时，如果删除了第一个布局，那默认新的第一个步骤，就不用返回键
    if (state.compData.typeValue === 'step-form' && slideIndex === 0) {
      if (comp[0] && comp[0].components && comp[0].components.length) {
        comp[0].components.map(item => {
          if (item.type === 'stepbutton') {
            item.hasBackBtn = false
          }
        })
      }
    }

    if (slideIndex === state.compData.slideIndex) {
      slideIndex = 0
    } else if (slideIndex < state.compData.slideIndex) {
      slideIndex = state.compData.slideIndex - 1
    } else {
      slideIndex = state.compData.slideIndex
    }
    commit('setState', {
      comp,
      compData: {
        ...state.compData,
        slideIndex,
      },
      canvasStyle: {
        ...state.canvasStyle,
        ...comp[slideIndex].style,
      },
    })
    dispatch('saveCanvasCompDataToPageData')
  },
  // 生成组件
  addCanvasComp(
    {
      commit,
      state: { comp, compData },
      dispatch,
    },
    payload,
  ) {
    const newComp = deepClone(comp)
    const newPayload = deepClone(payload)

    const hasRegionComponent = () => {
      if (compData.layout === 'multiple') {
        const page = newComp.find(item => {
          const region = item.components.find(el => el.type === 'region')
          return !!region
        })

        return !!page
      }

      return !!newComp.find(item => item.type === 'region')
    }

    // 如果添加了多个省市区组件，需要保证field字段唯一。如果只有一个省市区组件，不做处理。
    if (newPayload.type === 'region' && hasRegionComponent()) {
      const { fields, id, name } = newPayload
      const nextFields = {}
      Object.keys(fields).forEach(key => {
        const nextKey = `region_${key}_${id}`
        nextFields[key] = {
          ...fields[key],
          value: nextKey,
        }
      })

      newPayload.fields = nextFields
      newPayload.name = `${name}_${id}`
    }
    // const newCompData = {
    //   ...payload,
    //   name: payload.name,
    //   style: parseAddCanvasCompStyle(payload.type, payload.style),
    // };
    const parser = parseAddCanvasCompData(newPayload).defaultStyle().setDefaultApiName()
    // 默认给自定义API NAME
    // if (!newCompData.customFieldName && newCompData.fieldName) {
    //   newCompData.customFieldName = newCompData.fieldName;
    // }

    if (compData.layout === 'multiple') {
      // const currComp = newComp[compData.slideIndex].components;
      /**
       * 自适应布局，不使用绝对定位
       */
      if (newComp[compData.slideIndex].typesetting === 'flow') {
        parser.removeFlowLayoutPosition()
      } else {
        parser.removeAbsoluteLayoutMargin()
      }
      const data = parser.getData()
      arrayTool.insert(
        newComp[compData.slideIndex].components,
        data.sort,
        data,
      )
    } else if (compData.typesetting === 'flow') {
      parser.removeFlowLayoutPosition()
      const data = parser.getData()
      arrayTool.insert(newComp, data.sort - 0, data)
    } else {
      parser.removeAbsoluteLayoutMargin();
      const data = parser.getData()
      newComp.push(parser.getData())
    }
    commit('setState', {
      comp: newComp,
    })
    dispatch('saveCanvasCompDataToPageData')
    dispatch('renderRelatedMaps')
    rootDispatch('calcCompCount').call(this, 'components')
    dispatch('addHistoryRecord')
    // 自动打开组件设置
    setTimeout(() => rootDispatch('setPageCompSettingData', parser.getData()).call(this), 160)
  },
  // 复制画板组件
  copyCanvasComp({ state, commit, dispatch }, selected) {
    const { compData } = state
    let comps = deepClone(state.comp)
    let newComp = []
    if (compData.layout === 'multiple') {
      newComp = [...comps[compData.slideIndex].components]
    } else {
      newComp = comps
    }
    const copyComp = idx => {
      const item = {}
      const { components } = this.state.hexagon
      const [copyItem] = newComp.slice(idx, idx + 1)
      if (copyItem && !copyItem.noDeletion) {
        const key = genCompKey(copyItem)
        // 超出限制数或超出表单限制总数，不能复制
        if (
          (copyItem.isFormComp && components.exceedsMaxLimit)
          || components.compCountMaps[key] >= components.compMaxCountMaps[key]
        ) {
          FxUI.Message.warning($t('marketing.commons.cczdxzsbnf_3b50a4'))
          return
        }

        // 复制表单组件重新生成fieldName
        if (copyItem.isFormComp) {
          item.fieldName = getCompFieldNameByKey(copyItem.label)
        }
        newComp.push({
          ...copyItem,
          ...item,
          sort: newComp.length,
          id: createId(),
          style: {
            ...copyItem.style,
            top: (copyItem.style.top || 0) + 10,
          },
        })
        // newComp.splice(idx + 1, 0, {
        //   ...copyItem,
        //   ...item,
        //   sort: idx + 1,
        //   id: createId(),
        //   style: {
        //     ...copyItem.style,
        //     top: (copyItem.style.top || 0) + 10
        //   }
        // });
      }
    }
    if (typeof selected === 'number') {
      copyComp(selected)
    } else if (selected instanceof Array) {
      selected.forEach((value, idx) => {
        value && copyComp(idx)
      })
    } else {
      return
    }

    if (compData.layout === 'multiple') {
      comps[compData.slideIndex].components = newComp
    } else {
      comps = newComp
    }
    commit('setState', {
      comp: comps,
    })
    dispatch('saveCanvasCompDataToPageData')
    dispatch('renderRelatedMaps')
    rootDispatch('calcCompCount').call(this, 'components')
    dispatch('addHistoryRecord')
  },
  deleteCanvasCompByIds({ state, commit, dispatch }, ids) {
    const { compData } = state
    let comps = deepClone(state.comp)
    let newComp = []
    if (compData.layout === 'multiple') {
      newComp = [...comps[compData.slideIndex].components]
    } else {
      newComp = comps
    }

    const deleteComp = compIds => {
      newComp = newComp.filter(item => {
        // 如果开启禁止删除则无法删除 活动报名按钮除外（由于之前模板那里刷了nodeletion = true  但是需求改成了允许删除 所以这里需要特殊处理）
        if (item && item.noDeletion && item.typeValue !== 'marketingEventSignupbutton') {
          return true
        }
        return compIds.indexOf(item.id) === -1
      })
    }

    deleteComp(ids)
    arrayTool.sort(newComp)

    if (compData.layout === 'multiple') {
      comps[compData.slideIndex].components = newComp
    } else {
      comps = newComp
    }
    commit('setState', {
      comp: comps,
    })
    dispatch('saveCanvasCompDataToPageData')
    dispatch('renderRelatedMaps')
    rootDispatch('calcCompCount').call(this, 'components')
    dispatch('addHistoryRecord')
  },
  // 删除组件
  removeCanvasComp({ state, dispatch }, index) {
    const { compData } = state
    let comps = deepClone(state.comp)
    if (compData.layout === 'multiple') {
      comps = [...comps[compData.slideIndex].components]
    }
    const ids = []
    // 分步按钮不可删除
    if (index !== undefined) {
      if (comps[index].type !== 'stepbutton') {
        ids.push(comps[index].id)
      }
    } else {
      state.selected.forEach((value, idx) => {
        if (value) {
          // 分步按钮不可删除
          if (comps[idx].type !== 'stepbutton') {
            ids.push(comps[idx].id)
          }
        }
      })
    }
    dispatch('deleteCanvasCompByIds', ids)
  },
  updateCanvasComp(
    {
      state: { compData, comp, relatedMaps },
      commit,
      dispatch,
    },
    payload,
  ) {
    const comps = deepClone(comp)
    let newComp = {}
    if (compData.layout === 'multiple') {
      newComp = comps[compData.slideIndex].components[payload.sort] || {}
    } else {
      newComp = comps[payload.sort] || {}
    }

    newComp = autoFixOldFormCompStyle({
      ...newComp,
      ...payload,
      style: {
        ...newComp.style,
        ...payload.style,
      },
    })
    if (compData.layout === 'multiple') {
      comps[compData.slideIndex].components[payload.sort] = newComp
    } else {
      comps[payload.sort] = newComp
    }
    // 如果存在关系map，则根据关系map变更需要变更组件字段值
    const { needUpdate, fields } = relatedMaps[payload.type] || {}
    if (needUpdate) {
      needUpdate.forEach(i => {
        const newComp = {
          ...comps[i],
          ...Object.keys(fields).reduce((field, key) => {
            const targetFieldVal = payload[fields[key]]
            if (targetFieldVal) {
              field[key] = targetFieldVal
            }
            return field
          }, {}),
        }
        comps[i] = newComp
      })
    }
    commit('setState', {
      comp: comps,
    })
    if (delayToUpdate) clearTimeout(delayToUpdate)
    delayToUpdate = setTimeout(() => {
      dispatch('saveCanvasCompDataToPageData')
      rootDispatch('setPageCompSettingData', newComp).call(this)
      dispatch('addHistoryRecord')
    }, 160)
  },
  setCanvasComp({ state, commit, dispatch }, compLists) {
    const { compData } = state
    const newCompList = compLists.map((item, sort) => ({
      ...item,
      sort,
    }))
    let newComp = deepClone(state.comp)
    if (compData.layout === 'multiple') {
      newComp[compData.slideIndex].components = newCompList
    } else {
      newComp = newCompList
    }
    commit('setState', {
      comp: newComp,
    })
    dispatch('saveCanvasCompDataToPageData')
  },
  setPureCanvasStyle({ state, commit }, payload) {
    commit('setState', {
      canvasStyle: {
        ...state.canvasStyle,
        ...payload,
      },
    })
  },
  setCanvasStyle({ state, commit, dispatch }, payload) {
    let newComp = deepClone(state.comp)
    const compData = deepClone({ ...state.compData, ...payload })
    if (payload.layout === 'multiple') {
      try {
        newComp[payload.slideIndex] = {
          ...newComp[payload.slideIndex],
          ...payload.components[payload.slideIndex],
        }
      } catch (error) {}
      newComp[payload.slideIndex].style = {
        ...newComp[payload.slideIndex].style,
        ...payload.style,
      }

      // 编辑布局内背景填充类型
      // try {
      //   newComp[payload.slideIndex].fillType =
      //     payload.components[payload.slideIndex].fillType;
      //   newComp[payload.slideIndex].fillMethod =
      //     payload.components[payload.slideIndex].fillMethod;
      //   //后动作
      //   newComp[payload.slideIndex].action =
      //     payload.components[payload.slideIndex].action;
      // } catch (error) {}
    } else {
      compData.fillType = payload.fillType
      compData.fillMethod = payload.fillMethod
      // 后动作
      compData.action = payload.action
      newComp = payload.components || []
    }
    commit('setState', {
      canvasStyle: {
        ...state.canvasStyle,
        ...payload.style,
      },
      comp: newComp,
      compData,
    })
    dispatch('saveCanvasCompDataToPageData')
    // rootDispatch("updatePageComp", payload).call(this);
  },
  saveCanvasCompDataToPageData({ state, commit }) {
    const {
      designer: { pageMaps },
      layoutTabValue,
      layoutTabs,
    } = this.state.hexagon

    const currentTab = layoutTabs.find(item => item.name === layoutTabValue)
    if (currentTab.type === 'grid') {
      return
    }
    // 有父节点的情况下，更新到父节点。
    if (currentTab && currentTab.parent) {
      if(currentTab.preParent) {
        let preParentNode = deepClone(pageMaps[currentTab.preParent.id]);
        let parentNode = null;
        preParentNode.components.forEach((item) => {
          if(item.id === currentTab.parent.id) {
            parentNode = item;
          }
        })
        if(parentNode) {
          parentNode.components.forEach((item) => {
            if(item.id === layoutTabValue) {
              for (const key in state.compData) {
                if (key === 'components') {
                  item[key] = state.comp
                } else if (key === 'style') {
                  item[key] = {
                    ...item[key],
                    ...state.canvasStyle,
                  }
                } else {
                  item[key] = state.compData[key]
                }
              }
              if (state.compData.layout === 'multiple') {
                delete item.style.height
                delete item.style.position
              }
              rootDispatch(
                'setLayoutTabType',
                item.typesetting === 'flow' ? 'flow' : item.typeValue === 'tab-container' ? 'auto' : item.typeValue,
              ).call(this, false)
            }
          })

          rootDispatch('updateGridDataFromCanvas', preParentNode).call(this, 'grid')
          rootDispatch('updateGridData', preParentNode).call(this)
        }
      }else {
        const parentNode = deepClone(pageMaps[currentTab.parent.id])
        parentNode.components.forEach(item => {
          if (item.id === layoutTabValue) {
            for (const key in state.compData) {
              if (key === 'components') {
                item[key] = state.comp
              } else if (key === 'style') {
                item[key] = {
                  ...item[key],
                  ...state.canvasStyle,
                }
              } else {
                item[key] = state.compData[key]
              }
            }
            if (state.compData.layout === 'multiple') {
              delete item.style.height
              delete item.style.position
            }
            rootDispatch(
              'setLayoutTabType',
              item.typesetting === 'flow' ? 'flow' : item.typeValue === 'tab-container' ? 'auto' : item.typeValue,
            ).call(this, false)
          }
        })

        rootDispatch('updateGridDataFromCanvas', parentNode).call(this, 'grid')
        rootDispatch('updateGridData', parentNode).call(this)
      }

    } else {
      // 没有父节点的情况下，更新到page节点上。
      const newCompData = {
        ...pageMaps[layoutTabValue],
        ...state.compData,
        style: {
          ...(pageMaps[layoutTabValue] && pageMaps[layoutTabValue].style ? pageMaps[layoutTabValue].style : {}),
          ...state.canvasStyle,
        },
        components: state.comp,
      }
      // 激活状态下的容器数据
      let activeCompData = deepClone(newCompData)
      // 如果是多布局容器高度随子容器控制
      if (state.compData.layout === 'multiple') {
        delete newCompData.style.height
        delete newCompData.style.position
        activeCompData = newCompData.components[newCompData.slideIndex]
        // 如果是tab容器则根据tab类型变化改变当前编辑模式类型
        // if (newCompData.typeValue === "tab-container") {
        //   const typesetting =
        //     newCompData.components[newCompData.slideIndex].typesetting;
        //   if (this.canvasTypesetting !== typesetting) {
        //     rootDispatch(
        //       "setLayoutTabType",
        //       typesetting === "flow" ? "flow" : "auto"
        //     ).call(this, false);
        //     this.canvasTypesetting = typesetting;
        //   }
        // }
      }
      // if (this.canvasTypesetting !== currTypesetting) {
      rootDispatch(
        'setLayoutTabType',
        activeCompData.typesetting === 'flow' ? 'flow' : activeCompData.typeValue === 'tab-container' ? 'auto' : activeCompData.typeValue,
      ).call(this, false)
      // this.canvasTypesetting = currTypesetting;
      // }
      rootDispatch('updatePageComp', newCompData).call(this)
    }
  },
}

export default {
  namespaced: true,
  state,
  mutations: mutations(getInitialState()),
  actions,
}
