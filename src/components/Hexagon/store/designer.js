import {
  arrayTool, deepClone, rootDispatch, createId,
} from '../utils/index.js'
import mutations from './mutations.js'
import { getAutoRenderCompChildren } from '../config/index.js'
import { defaultPageData } from '../config/components.js'

const initPageData = deepClone(defaultPageData)

const getInitialState = () => ({
  pageCompList: [],
  pageMaps: {}, // 存放以组件ID为键的字典
  pageData: initPageData,
  displayFocusArea: false,
  viewportMode: 'mobile',
  viewportStyle: {
    width: 375,
    height: 600,
    x: 0,
    y: 0,
  },
  propertySetting: initPageData,
  fontFamily: [
    'Helvetica Neue',
    'Helvetica',
    'PingFang SC',
    'Hiragino Sans GB',
    'Microsoft YaHei',
    '微软雅黑', // [ignore-i18n]
    'Arial',
    'sans-serif',
  ],
  isMouseInPage: false,
  isSpacePressed: false,
})

const initState = () => getInitialState()

//版本大小比较
const compareVersion = (v1, v2) => {
  const v1Arr = v1.split('.')
  const v2Arr = v2.split('.')
  for (let i = 0; i < v1Arr.length; i++) {
    if (i >= v2Arr.length) {
      return 1
    }
    const num1 = parseInt(v1Arr[i])

    const num2 = parseInt(v2Arr[i])
    if (num1 > num2) {
      return 1
    } else if (num1 < num2) {
      return -1
    }
  }
  if (v1Arr.length > v2Arr.length) {
    return -1
  }
  return 0
}

// 自动生成布局组件子组件
const autoRender = data => {
  const { rootState, payload } = data
  if (payload.type === 'container') {
    const createCompMaps = getAutoRenderCompChildren(payload.typeValue, data.designMode, payload.typesetting) || {}
    if (createCompMaps) {
      // 是否根据自组件数据进行创建
      const isRenderFormData = !!createCompMaps.id;
      if (isRenderFormData) {
        const { components } = createCompMaps
        data.payload = {
          ...data.payload,
          ...createCompMaps,
          id: payload.id || createId(),
        }
        data.components = [...components]
      } else {
        const comps = rootState.hexagon.components.components
        comps.forEach(comp => {
          if (comp.value === 'formComp') {
            comp.components.forEach(subcomp => {
              const { config = {} } = subcomp || {}
              if (
                Object.keys(createCompMaps).some(key => key === config.type)
              ) {
                data.components.push({
                  ...config,
                  id: createId(),
                  ...createCompMaps[config.type],
                  style: {
                    ...config.style,
                    ...createCompMaps[config.type].style,
                  },
                })
              }
            })
          }
        })
      }
    }
  }
  return data
}

const getLoopItemById = (id, list)=> {
  let target = null;
  const loop = (_id, _list)=> {
    if(!target) {
      _list.map((item) => {
        if (item.id === _id) {
          target = item
        } else if (item.components && item.components.length > 0) {
          loop(_id, item.components);
        }
      })
    }
  }
  loop(id, list)
  return target;
}

const actions = {
  editPage(
    {
      state: { pageData },
      commit,
      dispatch,
    },
    data,
  ) {
    data = { ...initPageData, ...data || {} }
    // 兼容表单历史数据，自动补全布局组件key，typeValue字段
    const components = (data.components || []).map(item => {
      const newComp = {
        ...item,
      }
      if (item.type === 'container' && item.key === undefined) {
        if (
          item.components.some(comp => {
            // 兼容历史提交按钮没有label的情况
            if (comp.type === 'button' && comp.label === undefined) {
              comp.label = $t('marketing_pd.commons.tj_939d53')
            }
            return comp.type === 'button' && comp.isFormComp
          })
        ) {
          newComp.key = 'form-container'
          newComp.typeValue = 'form'
        } else {
          newComp.key = 'auto-container'
          newComp.typeValue = 'auto'
        }
      }
      return newComp
    })
    const mewPageData = {
      ...deepClone(pageData),
      ...data,
      components,
    }
    commit('setState', {
      pageData: mewPageData,
      pageCompList: components,
      propertySetting: mewPageData || pageData || {},
    })
    dispatch('createPageCompMaps')
    rootDispatch('calcCompCount').call(this, 'components')
  },
  // 设置是否显示焦点区域
  setDisplayFocusArea({ commit }, displayFocusArea) {
    commit('setState', {
      displayFocusArea,
    })
  },
  // 设置vieport样式
  setViewportStyle({ state, commit }, payload) {
    commit('setState', {
      viewportStyle: {
        ...state.viewportStyle,
        ...payload,
      },
    })
  },
  // 设置viewport模式
  setViewportMode({ state, commit }, payload) {
    commit('setState', {
      viewportMode: payload,
    })
    let newStyle = {
      width: 375,
      height: 600,
    };
    if (payload === 'pc' && state.pageData.pcAdaptation) {
      // 当页面宽度模式为响应式时，根据页面宽度计算实际宽度
      let realWidth = 1500;
      if(state.pageData.pageWidthMode === 'fixed') {
        let count = parseInt(state.pageData.pageWidth);
        realWidth = state.pageData.pageWidth.endsWith('px') ? count : (count * 1500 / 100);
      }

      newStyle = {
        width: realWidth,
        height: 800,
      }
    }
    commit('setState', {
      viewportStyle: {
        ...state.viewportStyle,
        ...newStyle,
      },
    })
  },
  // 生成页面组件maps
  createPageCompMaps({ state, dispatch, commit }) {
    commit('setState', {
      pageMaps: state.pageCompList.reduce((map, item) => {
        if (item.key === 'auto-container') {
          const el = document.getElementById(`${item.id}`)
          if (el) {
            const rect = el.getBoundingClientRect()
            dispatch('hexagon/canvas/setPageTopOffsetMap', {
              id: item.id,
              offset: rect.y,
            }, { root: true })
          }
        }
        map[item.id] = item
        return map
      }, {}),
    })
  },
  updatePageCompMap({ state, commit }, payload) {
    const pageMaps = { ...state.pageMaps }
    pageMaps[payload.id] = payload.item
    commit('setState', {
      pageMaps,
    })
  },
  // 重新排序索引
  reSortPageComp({ state, commit, dispatch }) {
    const pageCompList = state.pageCompList.map((item, sort) => ({
      ...item,
      sort,
    }))
    commit('setState', {
      pageCompList,
    });
    dispatch('updatePage');
    if (state.propertySetting.id) {
      let target = getLoopItemById(state.propertySetting.id, pageCompList);
      if (target) {
        commit('setState', {
          propertySetting: target,
        });
      }
    }
  },
  // 生成页面组件
  async addPageComp({
    state, rootState, commit, dispatch,
  }, payload) {
    const pageCompList = await new Promise(resolve => {
      // 存放子组件数组
      const components = []
      // 转数字类型
      payload.sort -= 0
      const { designMode, supportFlowFormMpVersion } = this.state.hexagon
      const { isCustomMiniappOpen, miniappInfo } = this.state.MiniappInfo || {};
      //如果是托管小程序，必须大于9.9.0版本才能使用表单的简易布局，否则使用自定义布局
      if (payload.typesetting === 'flow' && payload.key === 'form-container' && isCustomMiniappOpen && miniappInfo.currentCodeVersion && compareVersion(miniappInfo.currentCodeVersion, supportFlowFormMpVersion) < 0) {
        payload.typesetting = 'absolute';
        payload.style.height = 235;
      }
      resolve({
        pageCompList: [...state.pageCompList],
        components,
        payload,
        rootState,
        designMode,
      })
    })
      .then(autoRender)
      .then(({ pageCompList: compList, components, payload: payloadData }) => {
        // 添加组件
        arrayTool.insert(compList, payloadData.sort, {
          ...payloadData,
          components,
          name: payloadData.name,
        });
        // 自动打开组件设置
        setTimeout(() => dispatch('setPageCompSettingData', payloadData), 160)
        return compList
      })
    commit('setState', {
      pageCompList,
    })
    console.log('addPageComp:', pageCompList);
    dispatch('reSortPageComp')
    dispatch('createPageCompMaps')
    rootDispatch('calcCompCount').call(this, 'components')
  },
  // 复制页面组件
  copyPageComp({ state, commit, dispatch }, index) {
    const pageCompList = [...state.pageCompList]
    arrayTool.copy(pageCompList, index)
    commit('setState', {
      pageCompList,
    })
    dispatch('reSortPageComp')
    dispatch('createPageCompMaps')
    rootDispatch('calcCompCount').call(this, 'components')
  },
  // 删除页面组件
  removePageComp({ state, commit, dispatch }, index) {
    const pageCompList = [...state.pageCompList]
    const { id } = pageCompList[index]
    // 移除存在的对应tab
    if (id) {
      rootDispatch('removeTab', String(id)).call(this, false)
    }
    arrayTool.remove(pageCompList, index)
    commit('setState', {
      pageCompList,
    })
    dispatch('reSortPageComp')
    dispatch('createPageCompMaps')
    rootDispatch('calcCompCount').call(this, 'components')
    dispatch('setPageCompSettingData', state.pageData)
  },
  // 排序页面组件
  sortPageComp({ state, commit, dispatch }, { fromIndex, toIndex }) {
    const pageCompList = [...state.pageCompList]
    const pageLen = pageCompList.length
    if (toIndex >= pageLen) {
      toIndex = pageLen - 1
    }
    commit('setState', {
      pageCompList: arrayTool.move(pageCompList, fromIndex, toIndex),
    })
    dispatch('reSortPageComp')
    dispatch('createPageCompMaps')
  },
  // 设置页面组件数据
  setPageData({ commit, dispatch }, pageData) {
    commit('setState', {
      pageCompList: pageData,
    })
    dispatch('reSortPageComp')
    dispatch('createPageCompMaps')
  },
  // 设置空格键是否按下
  setCanvasSpaceKeyDown({ commit }, isSpacePressed) {
    commit('setState', {
      isSpacePressed,
    })
  },
  // 生成组件设置信息
  setPageCompSettingData({ commit }, propertySetting) {
    commit('setState', {
      propertySetting,
    })
  },
  // 更新组件设置信息
  updatePageComp({ state, commit, dispatch }, payload) {
    console.log('updatePageComp:', payload)
    const pageCompList = [...state.pageCompList]
    let pageData
    // 批量更新处理
    if (payload instanceof Array) {
      payload.forEach((compData, i) => {
        pageCompList[compData.sort] = {
          ...state.pageCompList[compData.sort],
          ...compData,
        }
        dispatch('updatePageCompMap', {
          id: compData.id,
          item: compData,
        })
        commit('setState', {
          ...(i === 0 ? { propertySetting: compData } : {}),
          pageCompList,
          ...((pageData && { pageData }) || {}),
        })
      })
      return
    }
    // 页面属性设置
    if (payload.type === 'page') {
      pageData = payload
    } else {
      // 修改页面布局元素
      pageCompList[payload.sort] = {
        ...state.pageCompList[payload.sort],
        ...payload,
      }
      dispatch('updatePageCompMap', {
        id: payload.id,
        item: payload,
      })
    }
    commit('setState', {
      propertySetting: payload,
      pageCompList,
      ...((pageData && { pageData }) || {}),
    });
    rootDispatch('calcCompCount').call(this, 'components')
  },
  updateGridData({ state, commit, dispatch }, payload) {
    const pageCompList = [...state.pageCompList];
    dispatch('updatePageCompMap', {
      id: payload.id,
      item: payload,
    })
    // 修改页面布局元素
    pageCompList[payload.sort] = {
      ...state.pageCompList[payload.sort],
      ...payload,
    }
    commit('setState', {
      pageCompList,
    });
    dispatch('reSortPageComp');
    rootDispatch('calcCompCount').call(this, 'components');
  },
  getPageData({ state: { pageData, pageCompList } }) {
    const params = {
      ...pageData,
      id: pageData.id,
      version: process.env.version || '1.0.0',
      components: pageCompList,
    }
    return params
  },
  updatePage({ dispatch }){
    dispatch('getPageData').then(pageData => {
      console.log('updatePage:', pageData);
      rootDispatch('updatePage', pageData).call(this, false);
    });
  },
  setIsMouseInPage({ commit }, isMouseInPage) {
    commit('setState', {
      isMouseInPage,
    })
  }
}
export default {
  namespaced: true,
  state: initState,
  mutations: mutations(getInitialState()),
  actions,
}
