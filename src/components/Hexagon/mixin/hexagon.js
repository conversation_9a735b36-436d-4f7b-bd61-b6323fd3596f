import { mapState, mapActions, mapMutations } from 'vuex'
import Header from '../components/Header.vue'
import Designer from '../components/Designer.vue'
import PropertySetting from '../components/PropertySetting.vue'

export default {
  name: 'Hexagon',
  components: {
    Head<PERSON>,
    Designer,
    PropertySetting,
  },
  props: {
    pages: {
      type: Array,
      default: () => [],
    },
    editPageId: {
      type: String,
      default: '',
    },
    pageLoad: {
      type: Function,
      default: async () => ({}),
    },
    beforeCompDataCreate: {
      type: Function,
      default: data => data,
    },
    // 用于接受一些全局数据，比如开通插件的信息等
    globalData: {
      type: Object,
      default: () => ({}),
    },
    contextProps: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      loading: false,
      // 是否隐藏页面列表
      hidePageList: false,
    }
  },
  computed: {
    ...mapState('hexagon', {
      allPages: 'pages',
      selectedPageId: 'editPageId',
    }),
    ...mapState('hexagon/designer', {
      propertySetting: 'propertySetting',
      pageData: 'pageData',
      pageCompList: 'pageCompList',
    }),
  },
  watch: {
    propertySetting: {
      deep: true,
      handler(newVal) {
        this.$emit('setting:change', newVal)
      },
    },
  },
  created() {
    this.setCompState({
      beforeCompDataCreate: this.beforeCompDataCreate,
    })
    this.setGlobalData({
      globalData: this.globalData,
    })
    console.log(this.contextProps, 'contextProps')
    this.setContextProps({
      contextProps: this.contextProps,
    })
    this.queryWechatVideoAccount()
    this.fetchBranchColorList()
  },
  mounted() {
    // 前面太早设置的话会被重置掉这个函数，所以在这再设置一下
    this.$nextTick(() => {
      this.setCompState({
        beforeCompDataCreate: this.beforeCompDataCreate,
      })
    })
  },
  methods: {
    ...mapActions('WechatVideo', ['queryWechatVideoAccount']),
    ...mapActions("hexagon/designer", {
      getPageData: "getPageData",
      editPage: "editPage",
      updatePageComp: "updatePageComp",
      addPageComp: "addPageComp"
    }),
    ...mapActions('hexagon/canvas', {
      updateCanvasComp: 'updateCanvasComp',
    }),
    ...mapActions('hexagon', {
      setDesignerType: 'setDesignerType',
      setPages: 'setPages',
      setEditPageId: 'setEditPageId',
      resetStore: 'resetStore',
      fetchBranchColorList: 'fetchBranchColorList',
    }),
    ...mapMutations('hexagon', {
      setGlobalData: 'setGlobalData',
      setContextProps: 'setContextProps',
    }),
    ...mapMutations('hexagon/components', {
      setCompState: 'setState',
    }),
    ...mapActions('hexagon/components', {
      updateOriginCompData: 'updateOriginCompData',
    }),
    async renderPages() {
      console.log('renderPages')
      // 有页面传页面信息，没有默认生成首页
      if (this.pages instanceof Array && this.pages.length) {
        this.setPages(this.pages)
        if (this.editPageId) {
          this.setEditPageId(this.editPageId)
        }
      }
    },
    async loadPageData(pageId) {
      this.loading = true
      const data = await this.pageLoad(pageId)
      this.editPage({
        ...data,
        id: pageId,
      })
      this.loading = false
    },
    handleSave(pageData) {
      this.$emit('save', pageData, this.allPages)
    },
    handleFinish(pageData) {
      this.$emit('finish', pageData, this.allPages)
    },
    handlePageClick(i, pageData) {
      this.setEditPageId(pageData.id)
      this.$emit('update:editPageId', pageData.id)
    },
    /**
     * 保存方法，触发保存事件
     */
    async save() {
      const pageData = await this.getPageData()
      this.handleSave(pageData)
      return pageData
    },
    /**
     * 获取当前页面数据
     */
    getCurrentPageData() {
      const pageData = {
        ...this.pageData,
        components: this.pageCompList,
      }
      return pageData
    },
    /**
     * 编辑更新当前打开页面数据
     * @params { object }
     */
    updateCurrentPage(pageData) {
      this.editPage(pageData)
    },
    /**
     * 获取当前设置组件数据
     */
    getCurrentCompData() {
      // console.log(this.propertySetting, 'get')
      return this.propertySetting
    },
    /**
     * 修改当前组件设置数据
     * @params {object}
     */
    updateCurrentCompData(data) {
      // console.log(data, 'update')
      if (this.$store.state.hexagon.layoutTabType === 'flow') {
        this.updateCanvasComp(data)
      } else {
        this.updatePageComp(data)
      }
    },
  },
}
