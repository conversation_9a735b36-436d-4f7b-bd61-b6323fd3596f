export default {
  methods: {
    /**
     * 侦听全局键盘松开事件
     * @param {Function} handler
     * @returns
     */
    onBodyKeyUp(handler) {
      const _handler = ev => {
        handler.call(ev.target, ev, ev.target);
      };
      document.body.addEventListener("keyup", _handler);

      return {
        clear() {
          document.body.removeEventListener("keyup", _handler);
        }
      };
    },
    onBodyKeyDown(handler) {
      const _handler = ev => {
        handler.call(ev.target, ev, ev.target);
      };
      document.body.addEventListener("keydown", _handler);

      return {
        clear() {
          document.body.removeEventListener("keydown", _handler);
        }
      };
    },
    onBodyMouseMove(handler) {
      const _handler = ev => {
        handler.call(ev.target, ev, ev.target);
      };
      document.body.addEventListener("mousemove", _handler);

      return {
        clear() {
          document.body.removeEventListener("mousemove", _handler);
        }
      };
    },
    onBodyMouseUp(handler) {
      const _handler = ev => {
        handler.call(ev.target, ev, ev.target);
      };
      document.body.addEventListener("mouseup", _handler);

      return {
        clear() {
          document.body.removeEventListener("mouseup", _handler);
        }
      };
    }
  }
};
