import { mapActions, mapState } from "vuex";
import DraggingElement from "../utils/draggingElement";
import { debounce, throttle, deepClone, createId } from "../utils";

/**
 * 计算光标是否在容器内
 * @param { Mouse Event } event
 * @param {object} wrapStyle
 */
function calcCursorInContainer({ pageX, pageY }, wrapStyle) {
  return (
    pageX >= wrapStyle.x &&
    pageX <= wrapStyle.x + wrapStyle.width &&
    pageY >= wrapStyle.y &&
    pageY <= wrapStyle.y + wrapStyle.height
  );
}

function insertAfter(newEl, targetEl) {
  var parentEl = targetEl.parentNode;

  if (parentEl.lastChild == targetEl) {
    parentEl.appendChild(newEl);
  } else {
    parentEl.insertBefore(newEl, targetEl.nextSibling);
  }
}

export default {
  data() {
    return {
      /**
       * 是否处于拖拽生成组件操作中
       * @type {Boolean}
       */
      isDrag: false,
      // /**
      //  * 页面高度
      //  * @type {Number}
      //  */
      // pageHeight: 0,
      // /**
      //  * 画板高度
      //  * @type {Number}
      //  */
      // canvasHeight: 0,
      // /**
      //  * 滚动条高度
      //  * @type {Number}
      //  */
      // scrollTop: 0,
      /**
       * 记录双击时间间隔
       * @type {Number}
       */
      viewportWrapPosition: {
        top: 0,
        left: 0,
      },
      doubleClickLastTriggerTime: null,
    };
  },
  computed: {
    ...mapState("hexagon", {
      designer: "designer",
    }),
    layoutTabValue() {
      return this.$store.state.hexagon.layoutTabValue;
    },
    currentTab() {
      let currentTab = this.$store.state.hexagon.layoutTabs.find((item)=>{
        return item.name === this.layoutTabValue
      });
      return currentTab;
    },
    isMouseInPage() {
      return this.designer.isMouseInPage;
    },
    layoutTabType() {
      return this.$store.state.hexagon.layoutTabType;
    },
    designMode() {
      return this.$store.state.hexagon.designMode;
    },
    canvas() {
      return this.$store.state.hexagon.canvas;
    },
    grid() {
      return this.$store.state.hexagon.grid;
    }
  },
  created() {
    //生成组件列表
    this.renderCompLists();
  },
  methods: {
    ...mapActions("hexagon", {
      addTab: "addTab",
      addGridTab: 'addGridTab',
      renderCompLists: "renderCompLists",
      setTabType: "setTabType",
    }),
    ...mapActions("hexagon/designer", [
      "editPage",
      "setDisplayFocusArea",
      "addPageComp",
      "sortPageComp",
      "setPageCompSettingData",
      "copyPageComp",
      "removePageComp",
      "updatePageComp",
      "setCanvasSpaceKeyDown",
    ]),
    ...mapActions("hexagon/canvas", ["addCanvasComp", "removeCanvasComp", "setPageTopOffsetMap"]),
    ...mapActions("hexagon/grid", ["addDragGridComp"]),
    // /**
    //  * 计算设计器相关视图的大小和位置信息
    //  */
    // peekViewportOffset() {
    //   const ViewportWrap = document.querySelector(
    //     ".hexagon__viewport .el-tabs__content"
    //   );

    //   const pageWrap = document.querySelector(".hexagon__page");

    //   const canvasWrap = document.querySelector(
    //     `#pane-${this.layoutTabValue} .hexagon__canvas`
    //   );

    //   //获取滚动条高度
    //   if (ViewportWrap) {
    //     this.scrollTop = ViewportWrap.scrollTop;
    //   }
    //   //获取页面高度
    //   if (pageWrap) {
    //     this.pageHeight = pageWrap.clientHeight;
    //   }
    //   //获取画板高度
    //   if (canvasWrap) {
    //     this.canvasHeight = canvasWrap.clientHeight;
    //   }
    // },
    getViewportWrapPosition() {
        // 获取元素相对于页面左上角的位置
      const ViewportWrap = document.querySelector(
        ".hexagon__canvas_scroll"
      );
      if(ViewportWrap) {
        const item = ViewportWrap.getBoundingClientRect();
        this.viewportWrapPosition = {
          top: item.top,
          left: item.left,
        }
      }else{
        this.viewportWrapPosition = {
          top: 0,
          left: 0,
        }
      }
    },
    /**
     * 获取拖拽目标区域焦点框容器
     * @returns {Element}
     */
    getFocusIframeOfTargetAreaWrapSelector() {
      let selector;
      //页面布局下的焦点框
      if (this.layoutTabValue === "page") {
        selector = ".hexagon__page-layout .hexagon__draggable";
      } else {
        //自定义布局下的焦点框
        selector = ".hexagon__canvas .hexagon__draggable";
      }
      return document.querySelector(selector);
    },
    /**
     * 获取拖拽目标区域焦点框
     * @returns {Element}
     */
    getFocusIframeOfTargetAreaSelector() {
      let selector;
      //页面布局下的焦点框
      if (this.layoutTabValue === "page") {
        selector = ".hexagon__page-layout .hexagon__focus-area";
      } else {
        //自定义布局下的焦点框
        selector = ".hexagon__canvas .hexagon__focus-area";
      }
      return document.querySelector(selector);
    },
    showFocusIframeOfTargetArea({ pageY }, target) {
      throttle(() => {
        const matched = target.closest(".hexagon__page-comp");
        this.focusAreaElement.style.display = "flex";
        //原始组件带有默认高度时，焦点区域高度与组件默认高度一致
        if (this.currDragCompConfig && this.currDragCompConfig.style.height) {
          this.focusAreaElement.style.height =
            this.currDragCompConfig.style.height + "px";
        } else {
          this.focusAreaElement.style.height = "auto";
        }
        if (matched) {
          this.insertIndex = matched.getAttribute("index") - 0;
          const { height, top } = matched.getBoundingClientRect();
          if (pageY - top > height / 2) {
            this.insertIndex += 1;
            insertAfter(this.focusAreaElement, matched);
          } else {
            this.focusAreaWrapElement.insertBefore(
              this.focusAreaElement,
              matched
            );
          }
        } else {
          //没有匹配目标位置时，放置最后
          const matchedArea = target.closest(".hexagon__focus-area");
          if (!matchedArea) {
            this.insertIndex = this.getEditingComponentLists().length;
            this.focusAreaWrapElement.appendChild(this.focusAreaElement);
          }
        }
      }, 10)();
    },
    hideFocusIframeOfTargetArea() {
      this.focusAreaElement && (this.focusAreaElement.style.display = "none");
    },
    getCanvasTypesetting(index) {
      let compData = this.compData || {};
      if (index !== undefined) {
        const components = this.getEditingComponentLists();
        compData = components[index] || {};
      }
      const { layout, slideIndex, components, typesetting } = compData;
      if (layout === "multiple") {
        return components[slideIndex].typesetting || "absolute";
      } else {
        return typesetting || "absolute";
      }
    },
    /**
     * 获取当前编辑类型，自适应布局或定位布局
     * @returns {String} absolute|flow
     */
    getEditingType() {
      if (this.layoutTabValue === "page") {
        return "flow";
      }
      return this.getCanvasTypesetting();
    },
    /**
     * 获取当前编辑中的组件列表
     * @returns {Array}
     */
    getEditingComponentLists() {
      if (this.layoutTabValue === "page") {
        return this.designer.pageCompList || [];
      } else if(this.layoutTabType === "grid") {
        return this.grid.comp || [];
      } else{
        const { layout, slideIndex } = this.compData || {};
        if (layout === "multiple") {
          return this.canvas.comp[slideIndex].components || [];
        } else {
          return this.canvas.comp || [];
        }
      }
    },
    /**
     * 开始拖拽组件
     * @param {Event} ev
     * @param {Element} target
     * @returns {Boolean}
     */
    onCompDragStart(ev, target) {
      this.compDragTimer = debounce(() => {
        this.isDrag = true;
        this.focusAreaElement = this.getFocusIframeOfTargetAreaSelector();
        this.focusAreaWrapElement = this.getFocusIframeOfTargetAreaWrapSelector();
        this.editType = this.getEditingType();

        //创建拖拽元素
        this.draggingElement = new DraggingElement(target);
        const [compCate, compType] =
          target.getAttribute("data-key").split("-") || [];
        const { config = {} } = this.components[compCate].components[compType];
        this.currDragCompConfig = config;
        this.setDisplayFocusArea(true);
        // this.peekViewportOffset();
        this.getViewportWrapPosition();
      }, 100)();
      return true;
    },
    onCompDragEnd({ pageX, pageY }) {
      //如果光标在页面内则生成组件
      if (this.isMouseInPage && this.currDragCompConfig) {
        //生成随机字符
        const id = new Date().getTime();
        if (this.layoutTabValue === "page") {
          //生成页面布局组件
          this.addPageComp({
            ...this.currDragCompConfig,
            sort: this.insertIndex,
            id,
          });
        } else if(this.layoutTabValue === this.grid.compData.id) {
          if(this.currDragCompConfig.isFormComp){
            // 在flow布局下，如果是图片上传组件，就添加一个标志，用于区分是多图还是单图模式。
            if(this.currDragCompConfig.type === 'file' && this.currDragCompConfig.typeValue === 'image') {
              this.currDragCompConfig.fileList = [{
                customFieldName: this.currDragCompConfig.customFieldName || this.currDragCompConfig.fieldName,
                fieldName: this.currDragCompConfig.fieldName,
                name: this.currDragCompConfig.name,
                placeholderImage: this.currDragCompConfig.placeholderImage,
                required: this.currDragCompConfig.required,
              }]
            }
          }

          this.addDragGridComp({
            ...this.currDragCompConfig,
            id,
          });
        } else {

          let currComps = []
          let { comp, compData} = this.canvas;
          let targetComp = compData;
          if (compData.layout === "multiple") {
            targetComp = comp[compData.slideIndex];
            currComps = (comp[compData.slideIndex] &&
              comp[compData.slideIndex].components) ||
              []
          } else {
            currComps = comp
          }
          if(this.currDragCompConfig.isFormComp && targetComp.typesetting === 'flow'){
            // 在flow布局下，如果是图片上传组件，就添加一个标志，用于区分是多图还是单图模式。
            if(this.currDragCompConfig.type === 'file' && this.currDragCompConfig.typeValue === 'image') {
              this.currDragCompConfig.fileList = [{
                customFieldName: this.currDragCompConfig.customFieldName || this.currDragCompConfig.fieldName,
                fieldName: this.currDragCompConfig.fieldName,
                name: this.currDragCompConfig.name,
                placeholderImage: this.currDragCompConfig.placeholderImage,
                required: this.currDragCompConfig.required,
              }]
            }
          }
          // 用来修复绝对定位时，图片上传组件marginLeft 直接变成paddingLeft问题
          if(this.editType !== 'flow' && this.currDragCompConfig.type === 'file' && this.currDragCompConfig.typeValue === 'image') {
            this.currDragCompConfig.style.marginLeft = 0;
          }

          //生成canvas布局组件
          this.addCanvasComp({
            ...this.currDragCompConfig,
            sort: this.editType === 'flow' ? this.insertIndex : currComps.length,
            id,
            style: {
              ...this.currDragCompConfig.style,
              left: pageX - this.viewportWrapPosition.left,
              top: pageY - this.viewportWrapPosition.top,
            }
          });
        }
      }
      this.setDisplayFocusArea(false);
      this.currDragCompConfig = null;
      this.draggingElement &&
        this.draggingElement.remove() &&
        (this.draggingElement = null);
      this.isDrag = false;
      setTimeout(() => this.hideFocusIframeOfTargetArea(), 20);

      if (this.compDragTimer) {
        this.compDragTimer.clear();
      }
      this.suspensionDragTimer && this.suspensionDragTimer.clear();

      //清除画板所有参考线
      this.setCanvasLineData({
        vLines: [],
        hLines: [],
        indices: [],
      });
    },
    onCompMove({ pageX, pageY }, target) {
      if (this.isDrag) {
        //修改拖拽元素位置
        this.draggingElement.updatePosition({
          left: 2 + pageX,
          top: 2 + pageY,
        });
        if (this.layoutTabValue === "page") {
          //拖拽到页面区域
          if (this.isMouseInPage) {
            this.showFocusIframeOfTargetArea({ pageX, pageY }, target);
          } else {
            this.hideFocusIframeOfTargetArea();
          }
        } else {
          //拖拽到画布区域
          if (this.isMouseInPage) {
            //自适应布局显示目标区域焦点框
            if (this.editType === "flow") {
              this.showFocusIframeOfTargetArea({ pageX, pageY }, target);
            }
          } else {
            this.editType === "flow" && this.hideFocusIframeOfTargetArea();
          }
        }
      }
    },
    /**
     * 点击页面布局内组件事件
     * @param {Event} ev
     * @param {Element} target
     * @returns
     */
    onPageCompClick(ev, target) {
      const classstr = target.getAttribute("class");
      const index = target.getAttribute("index");
      const components = this.getEditingComponentLists();
      const settingData = components[index];
      settingData &&
        this.setPageCompSettingData({
          ...settingData,
          sort: index,
        });
      //阻止拖拽
      if (classstr.indexOf("hexagon__disable-drag") !== -1) {
        return true;
      }
      /**
       * 悬浮按钮在页面布局拖拽
       */
      // if (
      //   target.getAttribute("name") === "suspension" || target.getAttribute("name") === "contact" ||
      //   (target.getAttribute("name") === "videolive" &&
      //     settingData.layout === 1)
      //   || (target.getAttribute('name') === 'video' && settingData.layoutType === 'icon-only')
      // ) {
      //   this.suspensionDragTimer = debounce(() => {
      //     const { x, y } = this.designer.viewportStyle;
      //     const data = components[index] || {};
      //     this.isSuspensionDragMove = true;
      //     this.suspensionOffset = {
      //       left: ev.pageX - x - data.wrapStyle.left,
      //       top: ev.pageY - y - data.wrapStyle.top,
      //     };
      //     this.suspensionElement = target;
      //     this.fromIndex = index;
      //   }, 150)();
      // }
      return true;
    },
    /**
     * 双击画板进行编辑
     * @param {Event} ev
     * @param {Element} target
     */
    // onDoubleClickCanvasComp(ev, target) {
    //   //双击画板编辑
    //   if (
    //     this.doubleClickLastTriggerTime &&
    //     new Date() - this.doubleClickLastTriggerTime < 300
    //   ) {
    //     const index = target.getAttribute("index");
    //     const canvasTypesetting = this.getCanvasTypesetting(index);
    //     const elId = target.getAttribute("id")
    //     const typeValue = target.getAttribute("type-value")

    //     if(typeValue === 'auto') {
    //       const el = document.getElementById(`${elId}`)
    //       if(el) {
    //         const rect = el.getBoundingClientRect()
    //         if(rect.y) {
    //           this.setPageTopOffsetMap({
    //             id: elId,
    //             offset: rect.y
    //           })
    //         }
    //       }
    //     }

    //     this.addTab({
    //       name: elId,
    //       index: index,
    //       type:
    //         canvasTypesetting === "flow"
    //           ? "flow"
    //           : typeValue,
    //     });
    //   }
    //   this.doubleClickLastTriggerTime = new Date();
    // },
    /**
     * 双击栅格布局进行编辑
     * @param {Event} ev
     * @param {Element} target
     */
    // onDoubleClickGridComp(ev, target) {
    //   //双击画板编辑
    //   if (
    //     this.doubleClickLastTriggerTime &&
    //     new Date() - this.doubleClickLastTriggerTime < 300
    //   ) {
    //     const elId = target.getAttribute("id")
    //     const index = target.getAttribute("index");
    //     this.addGridTab({
    //       name: elId,
    //       index: index,
    //       type: 'grid',
    //     });
    //   }
    //   this.doubleClickLastTriggerTime = new Date();
    // },
    /**
     * 删除页面布局组件
     * @param {Event} ev
     * @param {Element} target
     * @returns {Boolean}
     */
    // onPageCompDelete(ev, target) {
    //   FxUI.MessageBox.confirm($t('marketing_pd.commons.qdyscgzjsf_b9ef40'), $t('marketing_pd.commons.ts_02d981'), {
    //     confirmButtonText: $t('marketing_pd.commons.qd_38cf16'),
    //     cancelButtonText: $t('marketing_pd.commons.qx_625fb2'),
    //     type: "warning",
    //   }).then(() => {
    //     if (this.layoutTabValue === "page") {
    //       this.removePageComp(target.getAttribute("index") - 0);
    //     } else if(this.layoutTabType !== 'grid'){
    //       this.removeCanvasComp(target.getAttribute("index") - 0);
    //     }
    //     this.onDesignerBlankAreaClick({
    //       target: this.$el,
    //     });
    //     FxUI.Message({
    //       type: "success",
    //       message: $t('marketing_pd.commons.sccg_0007d1'),
    //     });
    //   });
    //   return true;
    // },
    /**
     * 复制页面布局组件
     * @param {Event} ev
     * @param {Element} target
     * @returns {Boolean}
     */
    // onPageCompCopy(ev, target) {
    //   this.copyPageComp(target.getAttribute("index"));
    //   return true;
    // },
    /**
     * 点击设计器空白区域
     * @param {Event} ev
     * @returns {Boolean}
     */
    onDesignerBlankAreaClick(ev) {
      //排除menu菜单
      if (!ev.target.closest(".hexagon__contextmenu")) {
        this.setCanvasSelected([]);
      }
      // console.log(ev, target, this.layoutTabValue, 'click designer');
      //页面布局或表单设计模式下显示页面配置
      if (this.layoutTabValue === "page" || this.designMode === "form-page") {
        this.setPageCompSettingData(this.designer.pageData);
      } else if(this.layoutTabType === 'grid') {
        this.setPageCompSettingData(this.grid.compData);
      } else {

        debounce(() => {
          if(this.currentTab.parent) {
            if(this.currentTab.preParent) {
              const preParentData = this.designer.pageMaps[this.currentTab.preParent.id];
              let parentData = null;
              if(preParentData) {
                preParentData.components.forEach((item)=>{
                  if(item.id === this.currentTab.parent.id) {
                    parentData = item;
                  }
                })
              }
              if(parentData) {
                let compData = null;
                parentData.components.forEach((item)=>{
                  if(item.id === this.layoutTabValue) {
                    compData = item;
                  }
                })
                if(compData) {
                  const settingData = deepClone(compData);
                  this.setPageCompSettingData(settingData);
                }
              }
            }else {
              const parentData = this.designer.pageMaps[this.currentTab.parent.id];
              let compData = null;
              if(parentData) {
                parentData.components.forEach((item)=>{
                  if(item.id === this.layoutTabValue) {
                    compData = item;
                  }
                })
                if(compData) {
                  const settingData = deepClone(compData);
                  this.setPageCompSettingData(settingData);
                }
              }
            }
          }else {
            const compData = this.designer.pageMaps[this.layoutTabValue];
            if (compData) {
              const settingData = deepClone(compData);
              this.setPageCompSettingData(settingData);
            }
          }
        }, 100)();
      }
    },
    /**
     * 设计器全局键盘松开事件处理逻辑
     * @param {Event} ev
     * @returns
     */
    onDesignerGlobalKeyUpHandler(ev) {
      if (ev.keyCode === 32) {
        this.setCanvasSpaceKeyDown(false);
      }
    },
    /**
     * 设计器全局键盘按下事件处理逻辑
     * @param {Event} ev
     * @returns
     */
    onDesignerGlobalKeyDownHandler(ev) {
      console.log('onDesignerGlobalKeyDownHandler?????????', ev);
      //如果不是body是不做处理
      if (ev.target && ev.target.nodeName !== "BODY") {
        return false;
      }
      //按下delete或回格键
      if (ev.keyCode === 46 || ev.keyCode === 8) {
        if (this.layoutTabValue !== "page" && this.layoutTabType !== 'grid') {
          this.onCanvasDeleteKeyDown(ev);
          this.setCanvasSelected([]);
        }
      }
      // 按下空格键
      if (ev.keyCode === 32) {
        this.setCanvasSpaceKeyDown(true);
      }
      // ctrl c / ctrl v
      const ctrlDown = ev.ctrlKey || ev.metaKey;
      if (ev.keyCode === 67 && ctrlDown) {
        this.canvas.selected !== "" &&
          this.setCopyCompTemp(this.canvas.selected);
      } else if (ev.keyCode === 86 && ctrlDown) {
        if (this.copyCompTemp.length > 0) {
          this.copyCanvasComp(this.copyCompTemp);
          this.setCopyCompTemp([]);
        }
      }
    },
  },
};
