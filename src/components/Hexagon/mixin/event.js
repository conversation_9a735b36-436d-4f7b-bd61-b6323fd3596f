/**
 * 设计器全局事件拦截分发处理
 */
import { listenBySelector } from "../utils";
import bodyEvent from "./bodyEvent";

export default {
  mixins: [bodyEvent],
  mounted() {
    this.initializeGlobalEvents();
  },
  methods: {
    /**
     * 初始化设计器全局事件侦听和分发
     */
    initializeGlobalEvents() {
      /**
       * 侦听设计器内全局鼠标按下事件
       */
      this.globalMouseDownHandler = listenBySelector(
        this.$el,
        "mousedown",
        ".hexagon__comp_candrag",
        this.onCompDragStart
      )
        /**
         * 点击设计器视图区域自动切换为组件编辑状态
         */
        .and(".hexagon__viewport", () => {
          this.setTabType("component");
        })

        // /**
        //  * 复制页面组件方法
        //  */
        // .and(".hexagon__page-comp-hover-copy", this.onPageCompCopy)

        // /**
        //  * 删除页面组件方法
        //  */
        // .and(".hexagon__page-comp-hover-close", this.onPageCompDelete)

        // /**
        //  * 双击页面布局内画布组件方法
        //  */
        // .and(
        //   ".hexagon__page-comp[name=container]",
        //   this.onDoubleClickCanvasComp
        // )
        // /**
        //  * 双击页面布局内的栅格布局组件方法
        //  */
        // .and(
        //   ".hexagon__page-comp[name=gridcontainer]",
        //   this.onDoubleClickGridComp
        // )

        /**
         * 点击页面布局内组件方法
         */
        .and(".hexagon__page-comp", this.onPageCompClick)

        /**
         * 点击画布内组件变形点
         */
        .and(".hexagon__comp-drag-point", this.onDragCanvasCompPoint)

        /**
         * 点击画布内组件
         */
        .and(".hexagon__canvas-comp-event", this.onClickCanvasComp)

        /**
         * 点击画布内空白区域
         */
        .and(".hexagon__canvas", this.onClickCanvas)

        /**
         * 点击设计器内空白区域
         */
        .and(".hexagon__designer", this.onDesignerBlankAreaClick);

      /**
       * 侦听设计器全局鼠标移动事件
       */
      this.globalMouseMoveHandler = this.onBodyMouseMove((ev, target) => {
        /**
         * 拖拽生成组件方法
         */
        this.onCompMove(ev, target);

        /**
         * 画布内拖拽移动组件方法
         */
        this.onMoveCanvasComp(ev, target);

        /**
         * 拖拽画布内组件变形点调整组件大小
         */
        this.onDragCanvasPoint(ev, target);
      });

      /**
       * 侦听设计器全局鼠标按起事件
       */
      this.globalMouseUpHandler = this.onBodyMouseUp(ev => {
        /**
         * 拖拽生成组件方法
         */
        this.onCompDragEnd(ev);

        /**
         * 画布内拖拽移动组件方法
         */
        this.onMoveCanvasCompUp(ev);

        /**
         * 拖拽画布内组件变形点调整组件大小
         */
        this.onDragCanvasPointUp(ev);
      });

      /**
       * 侦听全局键盘按下事件
       */
      this.globalKeyDownHandler = this.onBodyKeyDown(
        this.onDesignerGlobalKeyDownHandler
      );
        /**
       * 侦听全局键盘按下事件
       */
      this.globalKeyUpHandler = this.onBodyKeyUp(
        this.onDesignerGlobalKeyUpHandler
      );
    },
    /**
     * 销毁设计器全局事件绑定
     */
    destroyGlobalEvents() {
      this.globalMouseDownHandler.clear();
      this.globalMouseMoveHandler.clear();
      this.globalMouseUpHandler.clear();
      this.globalKeyDownHandler.clear();
      this.globalKeyUpHandler.clear();
    }
  },
  beforeDestroy() {
    this.destroyGlobalEvents();
  }
};
