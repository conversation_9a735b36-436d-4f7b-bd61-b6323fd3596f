import { _ } from "core-js";
import { mapState, mapActions } from "vuex";
import {
  deepClone,
  unique,
  checkArrayWithPush,
  getMaxDistance,
  genCompOffsetInfoText,
  debounce
} from "../utils";
export default {
  data() {
    return {
      isCompMove: false,
      isDragPoint: false,
      dragPointPosition: "",
      dragPointOriginTop: 0,
      dragPointOriginLeft: 0,
      dragPointCompOriginWidth: 0,
      dragPointCompOriginHeight: 0,
      dragCompStyle: null, //拖拽组件实时样式信息
      dragPointIndex: 0,
      dragDelayTime: null,
      moveCompElemIndex: -1,
      canvasCompOffsetLeft: 0,
      canvasCompOffsetTop: 0,
      componentPoints: [],
      directions: ["tt", "bb", "ll", "rr", "tb", "lr"],
      threshold: 4,
      limit: false
    };
  },
  computed: {
    ...mapState('hexagon', {
      layoutTabValue: 'layoutTabValue',
      layoutTabs: 'layoutTabs',
    }),
    ...mapState('hexagon/designer', {
      pageMaps: 'pageMaps',
    }),
    ...mapState("hexagon/canvas", {
      compData: "compData",
      copyCompTemp: "copyCompTemp"
    }),
    currCanvasComps() {
      const { comp } = this.canvas;
      const { slideIndex, layout } = this.compData;
      if (layout === "multiple") {
        return (comp[slideIndex] && comp[slideIndex].components) || [];
      }
      return comp;
    }
  },
  methods: {
    ...mapActions("hexagon/canvas", [
      "updateCanvasComp",
      "setCanvasSelected",
      "removeCanvasComp",
      "setCanvasLineData",
      "copyCanvasComp",
      "setCopyCompTemp"
    ]),
    getCanvasBylayoutTabValue(layoutTabValue) {
    //获取当前tab页，如果是在grid布局下的canvas。就从grid布局组件上找。
      let currentTab = this.layoutTabs.find((item)=>{
        return item.name === layoutTabValue
      });
      let canvas = {};
      if(currentTab && currentTab.parent) {
        if(currentTab.preParent) {
          let preParentNode = deepClone(this.pageMaps[currentTab.preParent.id]);
          let parentNode = null;
          preParentNode.components.forEach((item) => {
            if(item.id === currentTab.parent.id) {
              parentNode = item;
            }
          })
          if(parentNode) {
            parentNode.components.forEach((item) => {
              if(item.id === layoutTabValue) {
                canvas = deepClone(item);
              }
            })
          }
        }else {
          let parentNode = deepClone(this.pageMaps[currentTab.parent.id]);
          parentNode.components.forEach((item) => {
            if(item.id === layoutTabValue) {
              canvas = deepClone(item);
            }
          })
        }
      }else {
        canvas = deepClone(this.pageMaps[layoutTabValue] || {})
      }
      return canvas
    },
    onClickCanvas() {
      this.setCanvasSelected([]);
      const settingData = this.getCanvasBylayoutTabValue(this.layoutTabValue);
      this.setPageCompSettingData(settingData);
      return true;
    },
    onCanvasDeleteKeyDown() {
      this.removeCanvasComp();
    },
    onDragCanvasCompPoint(ev, target) {
      const dragCompElement = (this.dragCompElement = target.closest(
        ".hexagon__canvas-comp"
      ));
      this.dragCompElementContent = dragCompElement.children[0];
      this.dragCompOffsetElement = dragCompElement.querySelector(
        ".hexagon__comp-offsetinfo"
      );
      const position = target.getAttribute("position");
      const index = target.getAttribute("index");
      this.dragPointIndex = index;
      this.isDragPoint = true;
      this.dragPointPosition = position;
      this.dragPointOriginTop = ev.pageY;
      this.dragPointOriginLeft = ev.pageX;

      this.dragPointCompOriginWidth = dragCompElement.clientWidth;
      this.dragPointCompOriginHeight = dragCompElement.clientHeight;
      //收集组件坐标信息
      this.moveCompElemIndex = index - 0;
      this.calcAllCompPointInfo();
      return true;
    },
    onClickCanvasComp(ev, target) {
      const { x, y } = this.canvas.canvasOffset;

      //按住ctrl时，多选组件
      this.addMode = ev.ctrlKey || ev.metaKey;

      this.dragCompElement = target;
      const index = target.getAttribute("index");
      this.moveCompElemIndex = index - 0;
      //按住150毫秒拖拽组件
      if (this.dragDelayTime) this.dragDelayTime.clear();
      this.dragDelayTime = debounce(() => {
        this.dragCompOffsetElement = target.querySelector(
          ".hexagon__comp-offsetinfo"
        );
        const group = document.querySelectorAll(
          `div[id$='${this.layoutTabValue}'].el-tab-pane .hexagon__canvas-comp.selected`
        );
        if (group.length > 1) {
          this.dragCompElement = [].slice.call(group);
        }

        this.startX = ev.pageX;
        this.startY = ev.pageY;
        this.isCompMove = true;
        this.canvasOffsetLeft = ev.pageX - target.offsetLeft - x;
        this.canvasOffsetTop = ev.pageY - target.offsetTop - y;
        this.dragPointCompOriginWidth = target.clientWidth;
        this.dragPointCompOriginHeight = target.clientHeight;
        this.dragElLeft = target.offsetLeft
        this.dragElTop = target.offsetTop
        //收集组件坐标信息
        this.calcAllCompPointInfo();
      }, 150)();
      return true;
    },
    onMoveCanvasComp({ pageX, pageY }) {
      if (!this.isCompMove) return;
      const { x, y } = this.canvas.canvasOffset;
      const [left, top] = [
        pageX - x - this.canvasOffsetLeft,
        pageY - y - this.canvasOffsetTop
      ];
      const result = this.calc(this.moveCompElemIndex)(left, top);
      if (this.dragCompElement instanceof Array) {
        this.dragCompElement.forEach(node => {
          node.style.transform = `translate3d(${pageX - this.startX}px,${pageY -
            this.startY}px,0)`;
        });
      } else {
        this.dragCompElement.style.transform = `translate3d(${result.x - this.dragElLeft}px,${result.y - this.dragElTop}px,0)`;
        // this.dragCompElement.style.left = result.x + "px";
        // this.dragCompElement.style.top = result.y + "px";
      }

      this.dragCompStyle = {
        left: result.x,
        top: result.y
      };
      if (this.dragCompOffsetElement) {
        this.dragCompOffsetElement.innerText = genCompOffsetInfoText({
          width: this.dragPointCompOriginWidth,
          height: this.dragPointCompOriginHeight,
          left: result.x,
          top: result.y
        });
      }
    },
    onMoveCanvasCompUp({ pageX, pageY }) {
      if (this.isCompMove) {
        const isMoveGroup = this.dragCompElement instanceof Array;
        if (isMoveGroup) {
          this.dragCompElement.forEach(node => {
            node.style.transform = `none`;
            const index = node.getAttribute("index") - 0;
            const nodeStyle = this.currCanvasComps[index].style;
            this.updateCanvasComp({
              sort: index,
              style: {
                left: nodeStyle.left + (pageX - this.startX),
                top: nodeStyle.top + (pageY - this.startY)
              }
            });
          });
        } else {
          this.dragCompElement.style.transform = `none`;
          if (this.dragCompStyle) {
            this.updateCanvasComp({
              sort: this.moveCompElemIndex,
              style: this.dragCompStyle
            });
            this.dragCompStyle = null;
          }
        }
      } else {
        if (this.moveCompElemIndex > -1) {
          let selected = [...(this.canvas.selected || [])];

          if (this.addMode) {
            selected[this.moveCompElemIndex] = !selected[
              this.moveCompElemIndex
            ];
          } else {
            selected = selected.map(() => false);
            selected[this.moveCompElemIndex] = true;
          }

          this.setCanvasSelected(selected);
          //点击编辑组件
          const settingData = deepClone(
            this.currCanvasComps[this.moveCompElemIndex]
          );
          this.setPageCompSettingData({
            ...settingData,
            sort: this.moveCompElemIndex
          });
        }
      }
      if (this.dragDelayTime) this.dragDelayTime.clear();
      this.moveCompElemIndex = -1;
      this.isCompMove = false;
    },
    //计算所有元素的坐标信息
    calcAllCompPointInfo() {
      this.componentPoints = this.currCanvasComps.map((child, i) => {
        const x = Number(child.style.left);
        const y = Number(child.style.top);
        const node = document.querySelector(
          `#pane-${this.layoutTabValue} .hexagon__canvas-comp[id="${
            child.id
          }"]>div:first-child`
        );
        const w = node.clientWidth;
        const h = node.clientHeight;
        // console.log(
        //   `--------\nname:${
        //     child.label
        //   }\nw:${w}\nh:${h}\nx:${x}\ny:${y}\n--------`
        // );
        // const w = child.style.width
        // const h = child.style.height
        return {
          node,
          i,
          x,
          y,
          w,
          h,
          l: x,
          r: x + w,
          t: y,
          b: y + h,
          lr: x + w / 2,
          tb: y + h / 2
        };
      });
      const { width, height } = this.canvas.canvasStyle;
      //添加画板中心坐标
      this.componentPoints.push({
        i: this.componentPoints.length,
        x: 0,
        y: 0,
        w: width,
        h: height,
        l: 0,
        r: width,
        t: 0,
        b: height,
        lr: width / 2,
        tb: height / 2
      });
    },
    // 计算是否显示辅助线
    calc(index) {
      return (x, y, w, h) => {
        const target = this.componentPoints[index];
        if (w !== undefined && h !== undefined) {
          target.w = w;
          target.h = h;
        }
        const compares = this.componentPoints.filter((_, i) => i !== index);
        if (this.limit) {
          const { limitX, limitY } = this.checkDragOut({ x, y }, target);
          x = limitX;
          y = limitY;
        }

        if (compares.length === 0) {
          return { x, y };
        }

        return this.calcAndDrawLines({ x, y }, target, compares);
      };
    },
    calcAndDrawLines(values, target, compares) {
      const { v: x, indices: indices_x, lines: vLines } = this.calcPosValues(
        values,
        target,
        compares,
        "x"
      );
      const { v: y, indices: indices_y, lines: hLines } = this.calcPosValues(
        values,
        target,
        compares,
        "y"
      );

      const indices = unique(indices_x.concat(indices_y));

      if (vLines.length && hLines.length) {
        vLines.forEach(line => {
          const compare = compares.find(({ i }) => i === line.i);
          const { length, origin } = this.calcLineValues(
            { x, y },
            target,
            compare,
            "x"
          );

          line.length = length;
          line.origin = origin;
        });

        hLines.forEach(line => {
          const compare = compares.find(({ i }) => i === line.i);
          const { length, origin } = this.calcLineValues(
            { x, y },
            target,
            compare,
            "y"
          );

          line.length = length;
          line.origin = origin;
        });
      }
      // console.log({
      //   vLines,
      //   hLines,
      //   indices,
      // }, 'line')
      this.setCanvasLineData({
        vLines,
        hLines,
        indices
      });
      return { x, y, w: target.w, h: target.h };
    },
    calcPosValues(values, target, compares, key) {
      const results = {};

      const directions = {
        x: ["ll", "rr", "lr"],
        y: ["tt", "bb", "tb"]
      };

      // filter unnecessary directions
      const validDirections = directions[key].filter(dire =>
        this.directions.includes(dire)
      );

      compares.forEach(compare => {
        validDirections.forEach(dire => {
          const {
            near,
            dist,
            value,
            origin,
            length
          } = this.calcPosValuesSingle(values, dire, target, compare, key);
          if (near) {
            checkArrayWithPush(results, dist, {
              i: compare.i,
              $: compare.$,
              value,
              origin,
              length
            });
          }
        });
      });

      const resultArray = Object.entries(results);
      if (resultArray.length) {
        const [minDistance, activeCompares] = resultArray.sort(
          ([dist1], [dist2]) => Math.abs(dist1) - Math.abs(dist2)
        )[0];
        const dist = parseInt(minDistance);
        return {
          v: values[key] - dist,
          dist: dist,
          lines: activeCompares,
          indices: activeCompares.map(({ i }) => i)
        };
      }

      return {
        v: values[key],
        dist: 0,
        lines: [],
        indices: []
      };
    },
    calcLineValues(values, target, compare, key) {
      const { x, y } = values;
      const { h: H, w: W } = target;
      const { l, r, t, b } = compare;
      const T = y,
        B = y + H,
        L = x,
        R = x + W;

      const direValues = {
        x: [t, b, T, B],
        y: [l, r, L, R]
      };

      const length = getMaxDistance(direValues[key]);
      const origin = Math.min(...direValues[key]);
      return { length, origin };
    },
    calcPosValuesSingle(values, dire, target, compare, key) {
      const { x, y } = values;
      const W = target.w;
      const H = target.h;
      const { l, r, t, b, lr, tb } = compare;
      const { origin, length } = this.calcLineValues(
        { x, y },
        target,
        compare,
        key
      );

      const result = {
        // 是否达到吸附值
        near: false,
        // 距离差
        dist: Number.MAX_SAFE_INTEGER,
        // 辅助线坐标
        value: 0,
        // 辅助线长度
        length,
        // 辅助线起始坐标
        origin
      };

      switch (dire) {
        case "lr":
          result.dist = x + W / 2 - lr;
          result.value = lr;
          break;
        case "ll":
          result.dist = x - l;
          result.value = l;
          break;
        case "rr":
          result.dist = x + W - r;
          result.value = r;
          break;
        case "tt":
          result.dist = y - t;
          result.value = t;
          break;
        case "bb":
          result.dist = y + H - b;
          result.value = b;
          break;
        case "tb":
          result.dist = y + H / 2 - tb;
          result.value = tb;
          break;
      }

      if (Math.abs(result.dist) < this.threshold + 1) {
        result.near = true;
      }

      return result;
    },

    // 检查是否拖出画板
    checkDragOut({ x, y }, target) {
      const { width, height } = this.canvas.canvasStyle;
      const w = target.w;
      const h = target.h;
      const maxLeft = width - w;
      const maxTop = height - h;

      let limitX = x;
      let limitY = y;

      if (x < 0) {
        limitX = 0;
      } else if (x > maxLeft) {
        limitX = maxLeft;
      }
      if (y < 0) {
        limitY = 0;
      }
      if (y > maxTop) {
        limitY = maxTop;
      }

      return { limitX, limitY };
    },
    /**
     *
     * @param {String} type
     * @param {Number} width
     * @param {Number} height
     * @param {Number} originWidth
     * @param {Number} originHeight
     */
    calcCompAdaptiveHeight(type, width, height, originWidth, originHeight) {
      const needToTransComp = {
        image: true
      };
      if (needToTransComp[type]) {
        height = (width / originWidth) * originHeight;
      }
      return [width, height];
    },
    onDragCanvasPoint({ pageX, pageY }) {
      if (!this.isDragPoint) return;
      const { x, y } = this.canvas.canvasOffset;
      const elemInfo = deepClone(this.currCanvasComps[this.dragPointIndex]);
      const offsetLeft = pageX - this.dragPointOriginLeft;
      const offsetTop = pageY - this.dragPointOriginTop;
      // console.log(`position:${this.dragPointPosition}\noffsetLeft:${offsetLeft}\noffsetTop:${offsetTop}\n`);

      let style = elemInfo.style;
      let [width, height] = [0, 0];
      let newLeft = style.left;
      let newTop = style.top;
      switch (this.dragPointPosition) {
        case "bottomright":
          [width, height] = this.calcCompAdaptiveHeight(
            elemInfo.type,
            this.dragPointCompOriginWidth + offsetLeft,
            this.dragPointCompOriginHeight + offsetTop,
            style.width,
            style.height
          );
          style = {
            ...style,
            width: width,
            height: height
          };
          break;
        case "bottomleft":
          [width, height] = this.calcCompAdaptiveHeight(
            elemInfo.type,
            this.dragPointCompOriginWidth - offsetLeft,
            this.dragPointCompOriginHeight + offsetTop,
            style.width,
            style.height
          );
          newLeft = style.left + offsetLeft;
          style = {
            ...style,
            width: width,
            height: height,
            left: newLeft
          };
          break;
        case "topright":
          [width, height] = this.calcCompAdaptiveHeight(
            elemInfo.type,
            this.dragPointCompOriginWidth + offsetLeft,
            this.dragPointCompOriginHeight - offsetTop,
            style.width,
            style.height
          );
          newTop = style.top + offsetTop;
          style = {
            ...style,
            width: width,
            height: height,
            top: newTop
          };
          break;
        case "topleft":
          [width, height] = this.calcCompAdaptiveHeight(
            elemInfo.type,
            this.dragPointCompOriginWidth - offsetLeft,
            this.dragPointCompOriginHeight - offsetTop,
            style.width,
            style.height
          );
          newLeft = style.left + offsetLeft;
          newTop = style.top + offsetTop;
          style = {
            ...style,
            width: width,
            height: height,
            left: newLeft,
            top: newTop
          };
          break;
        case "top":
          height = this.dragPointCompOriginHeight - offsetTop;
          height = height > 0 ? height : 0;
          newTop = style.top + offsetTop;
          style = {
            ...style,
            height,
            top: newTop
          };
          break;
        case "bottom":
          height = this.dragPointCompOriginHeight + offsetTop;
          style = {
            ...style,
            height
          };
          break;
        case "left":
          width = this.dragPointCompOriginWidth - offsetLeft;
          width = width > 0 ? width : 0;
          newLeft = style.left + offsetLeft;
          style = {
            ...style,
            width,
            left: newLeft
          };

          break;
        case "right":
          width = this.dragPointCompOriginWidth + offsetLeft;
          style = {
            ...style,
            width
          };
          break;
        default:
          break;
      }
      // console.log(
      //   `left:${this.dragPointOriginLeft -
      //     x -
      //     (width - this.dragPointCompOriginWidth)}\ntop:${this
      //     .dragPointOriginTop -
      //     y -
      //     (height -
      //       this
      //         .dragPointCompOriginHeight)}\nwidth:${width}\nheight:${height}\noriginWidth:${
      //     this.dragPointCompOriginWidth
      //   }\noriginHeight:${this.dragPointCompOriginHeight}\n`,
      // );
      this.calc(this.moveCompElemIndex)(
        style.left,
        style.top,
        style.width,
        style.height
      );
      this.dragCompElement.style.top = style.top + "px";
      this.dragCompElement.style.left = style.left + "px";
      this.dragCompElementContent.style.width = style.width + "px";
      this.dragCompElementContent.style.height = style.height + "px";
      this.dragCompStyle = style;
      if (this.dragCompOffsetElement) {
        this.dragCompOffsetElement.innerText = genCompOffsetInfoText(style);
      }
    },
    onDragCanvasPointUp() {
      if (this.isDragPoint) {
        if (this.dragCompStyle) {
          //更新组件原数据
          this.updateCanvasComp({
            sort: parseInt(this.dragPointIndex),
            style: this.dragCompStyle
          });
          this.dragCompStyle = null;
        }
      }
      this.isDragPoint = false;
    }
  }
};
