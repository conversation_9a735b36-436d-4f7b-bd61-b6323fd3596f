<template>
  <div class="event-type-selector">
    <Select
      v-model="eventType"
      class="el-select"
      size="small"
      @change="handleChange"
    >
      <Option
        v-for="item in eventTypeOptions"
        :key="item.apiName"
        :value="item.apiName"
        :label="item.fieldName"
        style="font-size: 14px;"
      />
    </Select>
  </div>
</template>

<script>
import http from "@/services/http/index.js";

export default {
  name: "EventTypeSelector",
  components: {
    Select: FxUI.Select.components.ElSelect,
    Option: FxUI.Select.components.ElSelect.components.ElOption
  },
  props: {
    type: {
      type: Number,
      default: 0
    },
    expandOptions: {
      type: Array,
      default: ()=>([])
    },
    defaultValue: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      eventType: "",
      eventTypeOptions: []
    };
  },
  methods: {
    getEventTypeOptions() {
      http.getMarketingEventCommonSetting({ type: this.type || 0}).then(res => {
        try {
          if (!res.data) {
            this.eventTypeOptions = [...this.expandOptions]
            this.eventType = this.defaultValue || this.eventTypeOptions[0]?.apiName
            this.handleChange(this.eventType)
            return
          }
          const eventTypeMapping = res.data.activityTypeMapping.filter(
            item => {
              return item.activityType === this.type || 0;
            }
          );
          if (eventTypeMapping && eventTypeMapping.length > 0) {
            this.eventTypeOptions = [
              ...this.expandOptions,
              ...eventTypeMapping[0].mapping
            ];
          }
          this.eventType = this.defaultValue || this.eventTypeOptions[0]?.apiName
          this.handleChange(this.eventType)
        } catch(e) {
          this.eventTypeOptions = [...this.expandOptions]
          this.eventType = this.defaultValue || this.eventTypeOptions[0]?.apiName
          this.handleChange(this.eventType)
        }
      }).catch(err => {
        this.eventTypeOptions = [...this.expandOptions]
        this.eventType = this.defaultValue || this.eventTypeOptions[0]?.apiName
        this.handleChange(this.eventType)
      });
    },
    handleChange(value) {
      this.$emit('change', {
        apiName: value,
        fieldName: this.eventTypeOptions.find(item => item.apiName === value)?.fieldName
      })
    },
    getEventFieldLabel(value) {
      return this.eventTypeOptions.find(item => item.apiName === value)?.fieldName
    }
  },
  mounted() {
    this.getEventTypeOptions()
  }
};
</script>

<style></style>
