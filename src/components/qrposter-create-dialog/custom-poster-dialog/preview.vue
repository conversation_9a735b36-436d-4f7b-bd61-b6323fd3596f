<template>
  <div class="custom-poster-dialog-preview">
    <div class="img-wrapper">
      <el-image v-show="!!posterImg" :src="posterImg"></el-image>
    </div>
    <div class="footer-wrapper">
      <div class="user-info">
        <el-image v-show="!!defaultAvatar"  class="user-avatar" :src="defaultAvatar"></el-image>
        <span class="user-name">{{ $t('marketing.commons.zs_615db5') }} {{ $t('marketing.components.qrposter_create_dialog.xntj_2902e8') }}</span>
      </div>
      <div class="preview-info">
        <el-image v-show="!!qrCodeUrl" class="preview-qrcode" :src="qrCodeUrl"></el-image>
        <div class="preview-tips">
          {{ $t('marketing.components.qrposter_create_dialog.zasbewm_41ea9b') }}
        </div>
        <div class="preview-tips">
          {{ $t('marketing.commons.ckxq_5b48db') }}
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import defaultAvatar from '@/assets/images/avatar.png';
import http from '@/services/http/index';
import defaultQrcode from '@/assets/images/q-rcode.png';

export default {
  components: {
Button: FxUI.Button,
ElImage: FxUI.Image
},
  props: {
    posterImg: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      defaultAvatar,
      defaultQrcode,
      qrCodeUrl: ''
    };
  },
  watch: {},
  computed: {
  },
  beforeCreate() {},
  mounted() {
    this.queryBoundMiniappInfo();
  },
  methods: {
    queryBoundMiniappInfo() {
      http
        .getBoundMiniappInfo({
          platformId: "YXT"
        })
        .then(({ errCode, data }) => {
          if (errCode === 0 && data) {
            this.qrCodeUrl = data.qrCodeUrl || this.defaultQrcode;
          }
        });
    },
  },
};
</script>
<style lang="less" scoped>
.custom-poster-dialog-preview {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-height: calc(595px * 0.62);
  min-height: calc(190px * 0.62);
  overflow-y: auto;
  margin-top: 12px;
  background: #fff;

  .img-wrapper {
    flex: 1;
    display: block;
    width: 100%;

    & > img {
      width: 100%;
      height: 100%;
    }
  }

  .footer-wrapper {
    display: flex;
    position: relative;
    font-size: calc(14px * 0.62);
    line-height: calc(20px * 0.62);
    color: #181C25;
    height: calc(130px * 0.62);
    padding: 0 calc(30px * 0.62);

    .user-info {
      display: flex;
      flex-direction: column;

      .user-avatar {
        width: calc(50px * 0.62);
        height: calc(50px * 0.62);
        border-radius: 50%;
        margin-top: calc(17px * 0.62);
      }

      .user-name {
        margin: calc(8px * 0.62) 0 calc(35px * 0.62);
      }
    }

    .preview-info {
      display: flex;
      flex-direction: column;
      align-items: center;
      position: absolute;
      right: 0;
      top: calc(-39px * 0.62);
      right: calc(30px * 0.62);

      .preview-qrcode{
        width: calc(110px * 0.62);
        height: calc(110px * 0.62);
        border-radius: 50%;
        // background: #fff;
        border: calc(12px * 0.62);
        box-sizing: border-box;
        margin-bottom: calc(3px * 0.62);
      }

      .preview-tips {
        color: #545861;
        font-size: calc(12px * 0.62);
        line-height: calc(20px * 0.62);
      }
    }
  }
}
</style>
