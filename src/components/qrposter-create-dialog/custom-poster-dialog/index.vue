<template>
  <Dialog class="custom-poster-dialog" v-if="visible" width="1000px" :title="$t('marketing.components.qrposter_create_dialog.xjfxhb_febacd')" append-to-body
    :close-on-click-modal="false" :visible="visible" :loading="isLoading" @onSubmit="handleSave"
    @onClose="handleCloseDialog">
    <div class="custom-poster-dialog-wrapper">
      <div class="wrapper__left">
        <div class="poster-preview__label">{{ $t('marketing.commons.yl_645dbc') }}</div>
        <VPreview :posterImg="backgroundImage || defaultImg"></VPreview>
      </div>
      <div class="wrapper__right">
        <div class="label">
          <em>*</em>
          <div class="label__title">{{ $t('marketing.commons.hbmb_9450e0') }}</div>
          <div class="label__tips">{{ $t('marketing.components.qrposter_create_dialog.jycckdxszd_48a615') }}</div>
        </div>
        <VAddBackground @change="handleBackgroundChange"></VAddBackground>
      </div>
    </div>
  </Dialog>
</template>
<script>


import axios from 'axios';
import http from '@/services/http/index';
import Dialog from '@/components/dialog';
import VAddBackground from '../components/VAddBackground';
import VPreview from './preview';
import QuestionTooltip from "@/components/questionTooltip";

export default {
  components: {
Dialog,
VAddBackground,
VPreview,
Button: FxUI.Button,
QuestionTooltip
},
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    defaultImg: {
      type: String,
      default: '',
    }
  },
  data() {
    return {
      backgroundImage: '', // 背景图
      isLoading: false,
      model_cktData: {}, // 创客贴数据，在选用创客贴海报背景时，才会有此数据
      flag_isCkt: false,
      coverPath: '',
    };
  },
  watch: {
  },
  computed: {
  },
  mounted() {},
  methods: {
    handleBackgroundChange(backgroundImage, cktData, isCkt, coverPath) {
      console.log('handleBackgroundChange: ', backgroundImage, cktData, isCkt);
      this.backgroundImage = backgroundImage;
      this.model_cktData = cktData;
      this.flag_isCkt = isCkt;
      this.coverPath = coverPath
    },
    async handleSave() {
      if (this.backgroundImage && this.coverPath) {
        this.$emit('selected', {
          path: this.coverPath,
          url: this.backgroundImage,
        })
        this.$emit('update:visible', false)
      } else if (this.backgroundImage) {
        this.isLoading = true;
        // const blob = await axios.get(this.backgroundImage).then(res=>res.blob());
        const blob = await axios.post(this.backgroundImage, {}, {responseType: 'blob'})
        .then(res => new Blob([res.data]))

        this.uploadFile(blob).then((res) => {
          this.$emit('selected', res);
          FxUI.Message.success($t('marketing.commons.tjcg_3fdaea'));
          this.isLoading = false;
          this.$emit('update:visible', false);
        })
      } else {
        FxUI.Message.warning($t('marketing.components.qrposter_create_dialog.qxzhbbjtp_ede0ca'));
      }
    },
    handleCloseDialog() {
      this.$emit('update:visible', false);
    },
    uploadFile(blob) {
      return new Promise((resolve, reject) => {
        const formData = new FormData();
        formData.append("type", 1);
        formData.append("file", blob, "image.png");
        formData.append("needApath", false);
        formData.append("needPermanent", false);

        http.uploadFile(formData).then(res => {
          if (res.errCode == 0) {
            resolve && resolve(res.data);
          } else {
            resolve && resolve(false);
          }
        });
      });
    },
  },
};
</script>
<style lang="less" scoped>

@images: '../../../assets/images/';

.custom-poster-dialog {

  .custom-poster-dialog-wrapper {
    height: 426px;
    margin: 0;
    box-sizing: border-box;
    display: flex;

    .wrapper__left {
      width: 281px;
      background: #fafafa;
      padding: 16px 24px;
      box-sizing: border-box;

      .poster-preview__label {
        font-size: 13px;
        color: #181c25;
      }
    }

    .wrapper__right {
      padding: 15px 30px;
      flex: 1;
      overflow: auto;

      .label {
        flex-shrink: 0;
        display: flex;
        align-items: center;

        em {
          color: #ff3f3f;
          display: block;
          width: 13px;
        }

        .label__title {
          width: 96px;
          font-size: 13px;
          color: #181c25;
        }

        .label__tips {
          font-size: 12px;
          color: #91959e;
        }
      }
    }
  }
}
</style>
