<template>
  <div
    ref="$ie"
    class="img-editor"
    :style="imgStyle"
    tabindex="0"
  >
    <img
      v-show="value"
      :class="['img', radius && 'radius']"
      :src="value"
    >
    <div
      v-if="isOpenQrcodeLogo && showLogo"
      class="qrcode-logo"
    >
      <img :src="companyLogo">
    </div>
    <span
      class="resize se"
      data-isresize="true"
      data-dir="se"
    />
    <!-- <span class="resize sw" data-isresize="true" data-dir="sw"></span> -->
  </div>
</template>

<script>
import MoveEventMixin from './move-event-mixin.vue'
import http from '@/services/http/index.js'

const scale = 750 / 240
export default {
  mixins: [MoveEventMixin],
  props: {
    value: {
      type: String,
      default: '',
    },
    radius: {
      type: Boolean,
      default: false,
    },
    qrStyle: {
      type: Object,
      default: () => {},
    },
    showLogo: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      curX: 85,
      curY: 280,
      beforeX: 0,
      beforeY: 0,

      width: 70,
      height: 70,
      beforeWidth: 0,
      beforeHeight: 0,
      isOpenQrcodeLogo: false,
      companyLogo: '',
    }
  },
  computed: {
    thisQrStyle() {
      return {
        width: (this.width) * scale, // 圆的不要减去偏移量，放的要减
        height: (this.height) * scale, // 圆的不要减去偏移量，放的要减
        // width: (this.width - (this.radius ? 0 : 3.84/2)) * scale, // 圆的不要减去偏移量，放的要减
        // height: (this.height - (this.radius ? 0 : 3.84/2)) * scale, // 圆的不要减去偏移量，放的要减
        top: (this.curY + (3.84 / 2)) * scale, // 去掉padding的偏移位置，否则移动端绘制会不准确
        left: (this.curX + (3.84 / 2)) * scale, // 去掉padding的偏移位置，否则移动端绘制会不准确
        cutXY: {
          left: 0,
          top: 0,
        },
        scale,
      }
    },
    imgStyle() {
      return {
        top: `${this.curY * scale}px`,
        left: `${this.curX * scale}px`,
        width: `${this.width * scale}px`,
        height: `${this.height * scale}px`,
      }
    },
  },
  watch: {
    thisQrStyle: {
      immediate: true,
      handler() {
        this.$emit('update:qrStyle', this.thisQrStyle)
      },
    },
  },
  mounted() {
    console.log('value', this.value)
    const me = this
    const $target = this.$refs.$ie
    this.bindMoveEvent(this.$refs.$ie, {
      mousedown(e) {
        me.beforeX = me.curX
        me.beforeY = me.curY
        me.beforeWidth = me.width
        me.beforeHeight = me.height
      },
      mousemove(e) {
        const {
          startX, startY, X, Y,
        } = me.mouse
        const newX = me.beforeX + X - startX
        const newY = me.beforeY + Y - startY
        // 不能超出父元素的边界
        if (newX >= 0 && newX <= $target.offsetParent.offsetWidth / scale - $target.offsetWidth / scale) {
          me.curX = newX
        }
        if (newY >= 0 && newY <= $target.offsetParent.offsetHeight / scale - $target.offsetHeight / scale) {
          me.curY = newY
        }
      },

      resizemove(e) {
        const {
          startX, X,
        } = me.resize
        const newX = me.beforeWidth + X - startX
        // 不能超出父元素的边界
        if (newX >= 50 && newX <= $target.offsetParent.offsetWidth / scale - $target.offsetWidth / scale) {
          me.width = newX
          me.height = newX
        }
      },
    })
    if (this.showLogo) {
      this.queryEnterpriseInfo()
    }
  },
  methods: {
    peekStyles(style) {
      const newStyle = {}
      Object.keys(style).forEach(key => {
        if (key !== 'zIndex' && typeof style[key] === 'number') {
          newStyle[key] = `${style[key] * scale}px`
        } else {
          newStyle[key] = style[key]
        }
      })
      return newStyle
    },
    XYfixed() {
      const $target = this.$refs.$ie
      if (this.curX > $target.offsetParent.offsetWidth / scale - $target.offsetWidth / scale) {
        this.curX = Math.max($target.offsetParent.offsetWidth / scale - $target.offsetWidth / scale, 0)
      }
      if (this.curY > $target.offsetParent.offsetHeight / scale - $target.offsetHeight / scale) {
        this.curY = Math.max($target.offsetParent.offsetHeight / scale - $target.offsetHeight / scale, 0)
      }
    },
    queryEnterpriseInfo() {
      http.queryEnterpriseInfo().then(res => {
        if (res && res.errCode === 0 && res.data) {
          this.isOpenQrcodeLogo = res.data.drawQrcodeElogo === 1
          this.companyLogo = res.data.iconUrl
        }
      })
    },
  },
}
</script>

<style lang="less" scoped>
.img-editor {
  position: absolute;
  // border: 1px solid #61b9ff;
  cursor: move;
  padding: 6px;
  &:hover {
    margin: -3px;
    border: 3px solid #32de6d;
  }
  .img {
    white-space: nowrap;
    width: 100%;
    height: 100%;
    -webkit-user-drag: none;
    &.radius {
      border-radius: 1000px;
    }
  }
  &:hover {
    .resize {
      display: inherit;
    }
  }
  .resize {
    display: none;
    position: absolute;
    width: 24px;
    height: 24px;
    background-color: #32de6d;
  }
  .resize.nw {
    left: -12px;
    top: -12px;
    cursor: nw-resize;
  }
  .resize.ne {
    right: -12px;
    top: -12px;
    cursor: ne-resize;
  }
  .resize.se {
    right: -12px;
    bottom: -12px;
    cursor: se-resize;
  }
  .resize.sw {
    left: -12px;
    bottom: -12px;
    cursor: sw-resize;
  }
  .qrcode-logo {
    width: 24%;
    height: 24%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    padding: 10px;
    background-color: #fff;
    border-radius: 12px;
    box-sizing: border-box;

    & > img {
      width: 100%;
    }
  }
}
</style>
