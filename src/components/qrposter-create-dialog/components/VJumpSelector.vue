<template>
  <div class="jump-selector">
    <div class="selector__choisetype">
      <el-select size="small" class="choisetype__select el-select" v-model="model_type" :placeholder="$t('marketing.commons.qxz_708c9d')" :disabled="realLockJumpType || disabledJumpType">
        <el-option v-for="item in options_jumptype" :key="item.value" :label="item.label" :value="item.value"></el-option>
      </el-select>
    </div>
    <div class="selector__content">
      <div class="content--marketing-materiel" v-show="model_type === 1 || model_type === 7">
        <div
          :class="[
            'link-input',
            'km-t-ellipsis1',
            (realLockJumpType || disabledJumpType) && 'disabled'
          ]"
          @click="handleOpenMaterielSelector"
        >
          <template v-if="!model_marketing_materiel.id">
            +&nbsp;{{ $t('marketing.commons.xztgnr_3d00cc') }}
          </template>
          <template v-else>{{ model_marketing_materiel.title }}</template>
        </div>
      </div>
      <div class="content--public-account" v-show="model_type === 2">
        <VJumpSelectorAccountSelector :marketingEventId="marketingEventId" @change="handleAccountSelectorChange"></VJumpSelectorAccountSelector>
        <!-- <el-select
          class="public-account__select"
          v-model="model_paqrcode.appId"
          placeholder="请选择公众号"
          @change="handlePublicAccountChange"
        >
          <el-option v-for="item in options_publicAccounts" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
        <el-select
          class="qrcode__select"
          v-model="model_paqrcode.qrcodeId"
          placeholder="请选择渠道二维码"
          :loading="loading_paqrcode"
          @change="handleSelectPaqrcode"
        >
          <el-option v-for="item in options_paqrcodes" :key="item.id" :label="item.label" :value="item.id"></el-option>
        </el-select> -->
      </div>
      <div class="content--url" v-show="model_type === 3">
        <Input size="small" :placeholder="$t('marketing.commons.qsr_02cc4f')" v-model="model_url"></Input>
      </div>
      <div class="content--meeting" v-show="model_type === 4">
        <Input size="small" :placeholder="$t('marketing.commons.qsr_02cc4f')" v-model="meetingTitle" :disabled="true"></Input>
      </div>
      <div class="content--meeting" v-show="model_type === 5">
        <div class="link-input km-t-ellipsis1" @click="handleOpenMaterielSelector">
          <template v-if="!model_meeting_invitation.id">
            +&nbsp;{{ $t('marketing.commons.xzyqh_f879c7') }}
          </template>
          <template v-else>{{ model_meeting_invitation.title }}</template>
        </div>
      </div>
      <div class="content--form" v-show="model_type === 6">
        <Input size="small" :placeholder="$t('marketing.commons.qsr_02cc4f')" v-model="formName" :disabled="true"></Input>
      </div>
      <div class="content--meeting" v-show="model_type === 13 && marketingEventId">
        <VJumpWorkQrCodeSelector
          :marketingEventId="marketingEventId"
          @change="handleWorkQrCodeChange"
        />
      </div>
    </div>
    <SelectMaterialDialog
      :menus="data_menus"
      :tabbar="data_tabbar"
      :marketingEventTabbar="data_marketingEventTabbar"
      v-if="showDialog"
      size="small"
      :visible="showDialog"
      :marketingEventId="marketingEventId"
      @onSubmit="handleApply"
      @onClose="showDialog = false"
    ></SelectMaterialDialog>
  </div>
</template>
<script>

import wxList from '@/modules/wx-list';
import http from '@/services/http/index';
import SelectMaterialDialog, { MaterialInstance } from '@/components/select-material-dialog';
import VJumpSelectorAccountSelector from './VJumpSelectorAccountSelector';
import VJumpWorkQrCodeSelector from './VJumpWorkQrCodeSelector.vue'
import kisvData from '@/modules/kisv-data';

export default {
  components: {
    ElSelect: FxUI.Select.components.ElSelect,
    ElOption: FxUI.Select.components.ElSelect.components.ElOption,
    Input: FxUI.Input,
    VJumpSelectorAccountSelector,
    VJumpWorkQrCodeSelector,
  },
  beforeCreate() {
    this.$options.components.SelectMaterialDialog = SelectMaterialDialog;
  },
  props: {
    lockJumpType: {
      type: Boolean,
      default: false, // 是否锁定“扫码后跳转”
    },
    marketingEventId: {
      type: String,
      default: '',
    },
    meetingId: {
      type: String,
      default: '', // 是否要创建会议海报
    },
    meetingTitle: {
      type: String,
      default: $t('marketing.commons.hy_ebcb81'), // 是否要创建会议海报
    },
    jumpModelTypeConfig: {
      type: Object,
      default: () => {},
    },
    type: {
      type: String,
      default: '',
    },
    formId: {
      type: String,
      default: '', // 创建会议海报时，可选择创建扫码后跳转"报名表单"的海报，需要传入formId
    },
    formName: {
      type: String,
      default: '', // 创建会议海报时，可选择创建扫码后跳转"报名表单"的海报，需要传入formName
    },
    objectType: {
      type: Number,
      default: 0, // 物料类型，用于预设数据
    },
    objectId: {
      type: String,
      default: '', // 物料Id，用于预设数据
    },
    // 会议新加的参数
    disabledJumpType: {
      type: Boolean,
      default: false, // 是否禁用跳转
    },
  },
  data() {
    const { model_type,model_marketing_materiel = {} } = this.jumpModelTypeConfig || {};
    return {
      realLockJumpType: false, // 是否锁定“扫码后跳转”，不直接用props.lockJumpType是因为，如果拉不到预设物料，就不锁定
      options_publicAccounts: wxList.datas.list.map((item) => ({
        label: item.wxAppName,
        value: item.appId,
      })),
      options_paqrcodes: [],

      model_type: model_type || null, // 跳转类型 1：内容  2：关注公众号 3：网页 4：会议 5：邀请函 6：报名表单(会议营销专用) 7：文章

      // 内容中心对象
      model_marketing_materiel: {
        id: model_marketing_materiel.id || '',
        type: model_marketing_materiel.type || '',
        title: model_marketing_materiel.title || '',
      },

      //公众号渠道码
      model_paqrcode: {
        appId: '', // 公众号的纷享appid
        wxAppId: '', // 公众号的微信appid
        qrcodeId: '', // 渠道二维码Id
        qrcodeUrl: '', // 渠道二维码url
      },
      model_url: '', // 链接

      // 内容中心对象
      model_meeting_invitation: {
        id: '',
        title: '',
      },
      // 员工活码
      work_qrcode: {
        id: '',
        name: '',
      },

      showDialog: false,
      loading_paqrcode: false,
      uinfo: kisvData.datas.uinfo,
    };
  },
  computed: {
    data_menus() {
      let _menus = [];
      if (this.model_type === 1) {
        _menus = ['marketingEvent', 'materials'];
      } else if (this.model_type === 4) {
        _menus = ['marketingEvent'];
      } else if (this.model_type === 5) {
        _menus = ['marketingEvent'];
      } else if (this.model_type === 7) {
        _menus = ['marketingEvent', 'materials'];
      }
      return _menus;
    },
    data_tabbar() {
      let _tabbar = [];
      if (this.model_type === 1) {
        // _tabbar = [1, 4];
        _tabbar = [10, 1, 4, 16];
      } else if (this.model_type === 4) {
        _tabbar = ['3'];
      } else if (this.model_type === 5) {
        _tabbar = ['25'];
      } else if (this.model_type === 7) {
        _tabbar = [1];
      }
      return _tabbar;
    },
    data_marketingEventTabbar() {
      let _tabbar = [];
      if (this.model_type === 1) {
        // _tabbar = [1, 4];
        _tabbar = [10, 1, 4, 16, 9999];
      } else if (this.model_type === 4) {
        _tabbar = ['3'];
      } else if (this.model_type === 5) {
        _tabbar = ['25'];
      } else if (this.model_type === 7) {
        _tabbar = [1];
      }
      return _tabbar;
    },
    options_jumptype() {
      let cur_options = [];
      const options1 = [
        { label: $t('marketing.commons.tgnr_a6ec90'), value: 1 },
        { label: $t('marketing.commons.ylgzh_97a07f'), value: 2 },
        ...(this.uinfo.bindQywxOpen ? [{ label: $t('marketing.commons.qwyghm_a3fe61'), value: 13 }] : []),
        { label: $t('marketing.commons.wydz_66b985'), value: 3 },
        { label: $t('marketing.commons.w_d81bb2'), value: -1 },
      ];
      // 会议推广海报
      const options2 = [
        // { label: '内容', value: 1 },
        { label: $t('marketing.commons.hyzy_7962bd'), value: 4 },
        // { label: '报名表单', value: 6 },
        { label: $t('marketing.commons.tgnr_a6ec90'), value: 1 },
        { label: $t('marketing.commons.ylgzh_97a07f'), value: 2 },
        { label: $t('marketing.commons.wydz_66b985'), value: 3 },
        ...(this.uinfo.bindQywxOpen ? [{ label: $t('marketing.commons.qwyghm_a3fe61'), value: 13 }] : []),
        { label: $t('marketing.commons.w_d81bb2'), value: -1 },
        // { label: '邀请函', value: 5 },
        // { label: '文章', value: 7 },
      ];
      // 会议邀约海报
      const options3 = [
        { label: $t('marketing.commons.hyzy_7962bd'), value: 4 },
        { label: $t('marketing.commons.tgnr_a6ec90'), value: 1 },
        // { label: '报名表单', value: 6 },
        { label: $t('marketing.commons.yqh_85c9ea'), value: 5 },
        ...(this.uinfo.bindQywxOpen ? [{ label: $t('marketing.commons.qwyghm_a3fe61'), value: 13 }] : []),
        { label: $t('marketing.commons.w_d81bb2'), value: -1 },
      ];
      cur_options = this.meetingId ? (this.type == 'invite' ? options3 : options2) : options1;
      // this.model_type = cur_options[0].value;
      return cur_options;
    },
    
  },
  watch: {
    model_type: {
      deep: true,
      handler(newVal) {
        console.log('====newVal====', newVal);
        this.$emit('update:jumpType', this.model_type);
        // 跳转类型 1：内容  2：关注公众号 3：网页 4：会议 5：邀请函 6：报名表单 7：文章 -1：无
        if (newVal === 1) {
          this.isReady() && this.triggerSelected(this.model_marketing_materiel);
        } else if (newVal === 2) {
          this.isReady() && this.triggerSelected(this.model_paqrcode);
        } else if (newVal === 3) {
          this.isReady() && this.triggerSelected({ url: this.model_url });
        } else if (newVal === 4) {
          console.log('====meetingId====', this.meetingId);
          this.isReady() && this.triggerSelected({ id: this.meetingId });
        } else if (newVal === 5) {
          this.isReady() && this.triggerSelected(this.model_meeting_invitation);
        } else if (newVal === 6) {
          this.isReady() && this.triggerSelected({ id: this.meetingId, formId: this.formId });
        } else if (newVal === 7) {
          this.isReady() && this.triggerSelected(this.model_marketing_materiel);
        } else if (newVal === 13) {
          this.isReady() && this.triggerSelected(this.work_qrcode);
        } else if (newVal === -1) {
          this.triggerSelected();
        }
      },
      immediate: true
    },
    model_marketing_materiel: {
      deep: true,
      handler(newVal) {
        this.isReady() && this.triggerSelected(newVal);
      },
      immediate: true
    },
    model_paqrcode: {
      deep: true,
      handler(newVal) {
        this.isReady() && this.triggerSelected(newVal);
      },
    },
    work_qrcode: {
      deep: true,
      handler(newVal) {
        this.isReady() && this.triggerSelected(newVal);
      },
    },
    model_url: {
      deep: true,
      handler(newVal) {
        this.isReady() && this.triggerSelected({ url: newVal });
      },
    },
    model_meeting_invitation: {
      deep: true,
      handler(newVal) {
        this.isReady() && this.triggerSelected(newVal);
      },
    },
    jumpModelTypeConfig: {
      handler(newVal,oldVal) {
        if (!newVal.model_type) return;
        if (this.model_type !== newVal.model_type) {
          this.model_type = newVal.model_type;
          if (this.model_type === 4) {
          } else if (this.model_type === 5) {
            this.model_meeting_invitation = newVal.model_marketing_materiel;
          }
        }
      },
    },
    objectId: {
      handler(newVal) {
        this.loadObjectDataAndRender();
      },
    },
    lockJumpType: {
      handler() {
        this.realLockJumpType = this.lockJumpType;
      },
    },
  },

  methods: {
    isReady() {
      let flag = false;
      switch (this.model_type) {
        // 内容中心
        case 1: {
          if (this.model_marketing_materiel.id) {
            return true;
          }
          break;
        }
        // 关注公众号
        case 2: {
          if ((this.model_paqrcode.appId || this.model_paqrcode.wxAppId) && this.model_paqrcode.qrcodeUrl) {
            return true;
          }
          break;
        }
        // 网页
        case 3: {
          if (this.model_url) {
            return true;
          }
          break;
        }
        // 会议详情页
        case 4: {
          console.log('====meetingId====', this.meetingId);
          if (this.meetingId) {
            return true;
          }
          break;
        }
        // 会议邀请函页
        case 5: {
          if (this.model_meeting_invitation.id) {
            return true;
          }
          break;
        }
        // 会议表单页
        case 6: {
          if (this.meetingId && this.formId) {
            return true;
          }
          break;
        }
        // 文章
        case 7: {
          if (this.model_marketing_materiel.id) {
            return true;
          }
          break;
        }
        // 员工活码
        case 13: {
          if (this.work_qrcode.id) {
            return true;
          }
          break;
        }
      }
      return flag;
    },
    triggerSelected(param) {
      const forwardType = this.getCurrentForwardType();
      console.log(forwardType,"forwardType")
      this.$emit(
        'update:selected',
        $.extend({}, param, {
          forwardType,
          jumpType: this.model_type,
        }),
      );
    },
    getCurrentForwardType() {
      const curType = this.model_type;
      console.log(this.model_type,'11111')
      let forwardType = 0;
      switch (this.model_type) {
        case 1: {
          forwardType = {
            4: 1, // 产品
            1: 2, // 文章
            16: 5, // 表单
            10: 9, // 微页面
            9999: 1900 //外部内容
          }[this.model_marketing_materiel.type];
          break;
        }
        case 2: {
          // 公众号
          forwardType = 10;
          break;
        }
        case 3: {
          // 网页
          forwardType = 7;
          break;
        }
        case 4: {
          // 会议详情
          forwardType = 3;
          break;
        }
        case 5: {
          // 邀请函
          forwardType = 4;
          break;
        }
        case 6: {
          // 报名表单
          forwardType = 8;
          break;
        }
        case 7: {
          // 文章
          forwardType = 2;
          break;
        }
        case 13: {
          // 员工活码
          forwardType = 13
          break
        }
        case -1: {
          forwardType = 11;
          break;
        }
      }
      return forwardType;
    },
    handleOpenMaterielSelector() {
      if (this.lockJumpType || this.disabledJumpType) {
        return;
      }
      this.showDialog = true;
    },
    handleApply(data) {
      this.showDialog = false;
      if (this.model_type === 1 || this.model_type === 7) {
        this.model_marketing_materiel.id = data.id;
        this.model_marketing_materiel.type = data.type;
        this.model_marketing_materiel.title = data.title;
      } else if (this.model_type === 5) {
        this.model_meeting_invitation.id = data.id;
        this.model_meeting_invitation.title = data.title;
      }
    },
    handlePublicAccountChange(appId) {
      if (!appId) return;
      this.model_paqrcode.wxAppId = wxList.datas.list.find((item) => item.appId === appId).wxAppId;
      this.ajaxGetPAQRcodeByAppId(appId);
    },
    handleSelectPaqrcode() {
      const qrcodeId = this.model_paqrcode.qrcodeId;
      qrcodeId && (this.model_paqrcode.qrcodeUrl = this.options_paqrcodes.find((item) => item.id === qrcodeId).qrUrl);
    },
    ajaxGetPAQRcodeByAppId(appId) {
      const params = {
        appId,
        qrCodeName: '',
        pageNum: 1,
        pageSize: 1000,
      };
      this.loading_paqrcode = true;
      http
        .wechatQrCode(params)
        .then((resData) => {
          this.options_paqrcodes = resData.data.map((item) => ({
            label: item.qrCodeName,
            id: item.id,
            qrUrl: item.showUrl,
          }));
        })
        .catch()
        .then(() => {
          this.loading_paqrcode = false;
        });
    },
    handleWorkQrCodeChange(data) {
      this.work_qrcode = {
        id: data.id, // 公众号的纷享appid
        url: data.url,
        qrCodeName: data.qrCodeName,
      }
    },
    handleAccountSelectorChange(data) {
      
      console.log('wxList.datas.list', wxList.datas.list);
      this.model_paqrcode = {
        wxAppId: data.appId, // 公众号的纷享appid
        qrcodeId: data.id,
        qrcodeUrl: data.url,
      }
      console.log('handleAccountSelectorChange', data, this.model_paqrcode);

    },
    async loadObjectDataAndRender() {
      if (!this.objectType || !this.objectId) {
        this.realLockJumpType = false;
        return;
      }
      const { objectType, objectId } = this;
      const result = await MaterialInstance.getMaterialDetailByObjectType(objectType, objectId);
      if (result && result.errCode === 0) {
        const data = MaterialInstance.formatDataByObjectType(objectType, result.data);
        console.log('loadObjectDataAndRender', data);
        // this.model_type = [1, 4, 5, 6, ];// model_type: 跳转类型 1：内容  2：关注公众号 3：网页 4：会议 5：邀请函 6：报名表单(会议营销专用) 7：文章
        // const shorturl = await getShortUrl(href);
        this.handleApply({
          ...data,
          type: {
            4: 4, // 产品
            6: 1, // 文章
            16: 16, // 表单
            26: 10, // 微页面
          }[objectType],
        });
      } else {
        this.realLockJumpType = false;
      }
    },
  },
  mounted() {
    this.realLockJumpType = this.lockJumpType;
    this.loadObjectDataAndRender();
  },
};
</script>
<style lang="less" scoped>
.jump-selector {
  display: flex;
  flex: 1;
  .selector__choisetype {
    padding-right: 16px;
    .choisetype__select {
      width: 180px;
    }
  }
  .selector__content {
    flex: 1;
    .link-input {
      height: 32;
      line-height: 32px;
      color: var(--color-primary06,#407FFF);
      // display: flex;
      // align-items: center;
      /* justify-content: center; */
      border: 1px solid #e9edf5;
      box-sizing: border-box;
      flex: 1;
      border-radius: 4px;
      padding: 0 12px;
      cursor: pointer;
      min-width: 150px;
      &.disabled {
        background-color: #f2f3f5;
        border-color: #c1c5ce;
        color: #545861;
        cursor: not-allowed;
      }
      .icon-add-b {
        margin-right: 7px;
      }
    }
    .public-account__select {
    }
    .qrcode__select {
      margin-left: 16px;
      width: 199px;
    }
  }
  .content--public-account {
    display: flex;
  }
}
</style>
