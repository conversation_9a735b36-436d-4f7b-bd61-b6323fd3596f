<template>
  <div class="VJumpSelectorAccountSelector">
    <div class="selector__wrapper" @click="handleSelectAccount">
      <div class="selector__name">{{qrcode.qrCodeName || placeholder}}</div>
      <div class="selector__icon el-icon-search"></div>
    </div>
    <QrcodeDialog
      v-if="showQrcodeDialog"
      :visible.sync="showQrcodeDialog"
      :marketingEventId="marketingEventId"
      @confirm="handleUploadConfirm"
      @close="handleUploadCancel"
    />
  </div>
</template>

<script>
import QrcodeDialog from '@/pages/wechat/components/qrcode-dialog.vue';
export default {
  components: {
    QrcodeDialog,
  },
  props: {
    marketingEventId: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      placeholder: $t('marketing.components.qrposter_create_dialog.qxzgzhqdew_b6c503'),
      qrcode: {},
      showQrcodeDialog: false,
    }
  },
  methods: {
    handleSelectAccount() {
      this.showQrcodeDialog = true;
    },
    handleUploadConfirm(data) {
      console.log('handleUploadConfirm', data);
      this.qrcode = data;
      this.$emit('change', data);
    },
    handleUploadCancel() {
      this.showQrcodeDialog = false;
    },
  },
}
</script>

<style lang="less" scoped>
.VJumpSelectorAccountSelector {
  width: 100%;
  .selector__wrapper {
    display: flex;
    border: 1px solid #E9EDF5;
    width: 100%;
    box-sizing: border-box;
    align-items: center;
    padding-left: 10px;
    border-radius: 4px;
    cursor: pointer;
  }
  .selector__icon {
    height: 32px;
    width: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: auto;
    border-left: 1px solid #e9edf5;
    color: #B4B6C0;
    font-size: 16px;
    font-weight: 600;
  }
}

</style>