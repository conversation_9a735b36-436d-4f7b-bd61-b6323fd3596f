<template>
  <div class="poster-editor">
    <!-- <img
      v-show="flag_isLongImage"
      :class="['icon_longimg']"
      :src="iconLongimg"
    > -->
    <span :class="'longposter-icon'" v-show="flag_isLongImage">{{ $t('marketing.commons.ct_ce9ecb') }}</span>
    <div
      ref="scroll"
      :class="[
        'poster__scroll',
        flag_isLongImage && 'long-img',
      ]"
    >
      <div
        ref="qrposter"
        class="poster__wrapper"
        :style="{
          'background-image': `url(${bgUrl})`,
          width: backgroundStyle.width750 + 'px',
          height: backgroundStyle.height750 + 'px',
        }"
        @dblclick="handleHtml2canvas"
      >
        <div
          v-if="!bgUrl"
          class="poster__emptytips"
        >
          <div class="emptytips__bg" />
        </div>
        <div class="poster__content">
          <TextEditor
            v-for="font in fonts"
            :key="font.id"
            :value="font"
          />
          <QrcodeEditor
            v-if="qrcode"
            v-show="showQrcode"
            ref="QrcodeEditor"
            :show-logo="showLogo"
            :value="qrcode"
            :radius="qrcodeRadius"
            :qr-style.sync="qrStyle"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import html2canvas from '@/../libs/html2canvas.js'
import domToImage from '@/../libs/dom-to-image.js'
// import http from '@/services/http/index.js'
import axios from 'axios'
import TextEditor from './text-editor.vue'
import QrcodeEditor from './qrcode-editor.vue'
import icon_longimg from '@/assets/images/qrposter/icon_longimg.png'
import icon_longimg_en from '@/assets/images/qrposter/icon_longimg-en.png'

import { getImageByLang } from '@/utils/i18n.js'

export default {
  components: {
    TextEditor,
    QrcodeEditor,
  },
  props: {
    backgroundImage: {
      type: String,
      default: '',
    },
    fonts: {
      type: Array,
      default() {
        return []
      },
    },
    qrcode: {
      type: String,
      default: '',
    },
    qrcodeRadius: {
      type: Boolean,
      default: false,
    },
    showLogo: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      icon_longimg: getImageByLang([icon_longimg, icon_longimg_en]),
      backgroundStyle: {},
      showQrcode: true,
      qrStyle: {},
    }
  },
  computed: {
    flag_isLongImage() {
      return this.backgroundStyle.height750 > 1334
    },
    bgUrl(){
      // 粗略判断如果是base64直接返回
      if(this.backgroundImage.startsWith('data:')) {
        return this.backgroundImage
      }
      return this.backgroundImage.indexOf(location.host) !== -1 ? this.backgroundImage : `${window.location.origin}/marketing/file/redirectDownload?url=${encodeURIComponent(this.backgroundImage)}`;
    }
  },
  watch: {
    backgroundImage() {
      const me = this
      console.log('backgroundImage', this.backgroundImage)
      const $img = new Image()
      console.log('$img', $img, $img.width, $img.height)
      $img.src = this.backgroundImage
      $img.onload = function (e) {
        console.log('$img onload', $img, $img.width, $img.height)
        me.backgroundStyle = {
          originWidth: $img.width,
          originHeight: $img.height,
          width750: 750,
          height750: (750 * $img.height) / $img.width,
          width240: 240,
          height240: (240 * $img.height) / $img.width,
        }
        if (me.backgroundStyle.height750 < 1334) {
          // me.qrStyle = {
          //   ...me.qrStyle,
          //   top: me.backgroundStyle.height240 - 100,
          // }
          me.$nextTick(() => {
            if (me.qrcode) {
              me.$refs.QrcodeEditor.XYfixed()
            }
          })
        }
      }
    },
    // 适用于一次生成多张海报
    qrcode() {
      this.$nextTick(() => {
        if (this.backgroundStyle.originHeight) {
          this.$refs.QrcodeEditor.XYfixed()
        }
      })
    },
    fonts() {
      console.log('this.fonts', this.fonts)
    },
    qrStyle() {
      console.log('this.qrStyle', this.qrStyle)
      this.handleQrMove()
    },
  },
  methods: {
    handleHtml2canvas() {
      // 这里不能用async因为有坑
      this.html2canvas(result => console.log(result))
    },
    save(callback) {
      this.saving = true
      this.showQrcode = true
      this.$nextTick(() => {
      this.html2canvas(result => {
        const blobQrposter = result.blob
        this.showQrcode = false
        this.$nextTick(() => {
          this.html2canvas(res => {
            const blobBackground = res.blob
            Promise.all([
              this.uploadFile(blobQrposter),
              this.uploadFile(blobBackground),
            ]).then(results => {
              const qrposter = results[0]
              const background = results[1]
              callback(background, qrposter, this.qrStyle, this.backgroundStyle)
              })
            })
          })
        })
      })
    },
    html2canvas(callback) {
      const posterWrap = this.$refs.qrposter

      const scrollWrap = document.querySelector('.poster__scroll');
      if(scrollWrap){
        scrollWrap.style.transform = 'scale(1)';
        //避免转图片时因滚动条位置问题导致留白
        scrollWrap.scrollTop = 0;
      }

      const ua = navigator.userAgent.toLowerCase()
      if (ua.indexOf('safari') !== -1 && ua.indexOf('chrome') === -1) {
        html2canvas(posterWrap, {
          width: this.backgroundStyle.width750,
          height: this.backgroundStyle.height750,
          logging: true,
          allowTaint: true, // 修改：允许跨域图片
          useCORS: true, // 添加：启用CORS
          backgroundColor: '#ffffff',
          // scale: 1,
          onclone: doc => {
            const scrollWrap = doc.querySelector('.poster__scroll');
            if(scrollWrap){
              scrollWrap.style.transform = 'scale(1)';
              //避免转图片时因滚动条位置问题导致留白
              scrollWrap.scrollTop = 0;
            }
          },
        })
          .then(
            canvas => {
              let blob = ''
              try {
                blob = this.base64toblob(canvas.toDataURL('image/jpeg'))
                console.log('blob', blob)
              } catch (e) {
                callback(false)
              }
              callback({ blob })
              // : URL.createObjectURL(blob)
            },
            () => {
              window.console.log('reject')
            },
          )
          .catch(() => {
          })
      } else {
        domToImage
          .toBlob(posterWrap, {
            width: this.backgroundStyle.width750,
            height: this.backgroundStyle.height750,
          })
          .then(blob => {
            callback({ blob })
          })
          .catch(() => {
            window.console.log($t('marketing.commons.dl_7f960a'))
            // 开发环境用空图片占位
            if(process.env.NODE_ENV === 'development'){
              console.warn('跨域图片，生成海报失败，已用空图片占位');
              // 生成一个空白的 Blob
              const emptyCanvas = document.createElement('canvas');
              emptyCanvas.width = this.backgroundStyle.width750;
              emptyCanvas.height = this.backgroundStyle.height750;
              emptyCanvas.getContext('2d').fillStyle = '#fff';
              emptyCanvas.getContext('2d').fillRect(0, 0, emptyCanvas.width, emptyCanvas.height);
              emptyCanvas.toBlob(blob => {
                callback({ blob });
              });
            }
          })
      }
    },

    base64toblob(code) {
      const parts = code.split(';base64,')
      const contentType = parts[0].split(':')[1]
      const raw = window.atob(parts[1])
      const rawLength = raw.length
      const uInt8Array = new Uint8Array(rawLength)

      for (let i = 0; i < rawLength; i += 1) {
        uInt8Array[i] = raw.charCodeAt(i)
      }

      return new Blob([uInt8Array], { type: contentType })
    },
    uploadFile(blob) {
      return new Promise((resolve, reject) => {
        const formData = new FormData()
        // formData.append('type', 1)
        formData.append('file', blob, 'image.png')
        formData.append('extension', 'png');
        // formData.append('needApath', false)
        // formData.append('needPermanent', false)
        axios.post(`${FS.BASE_PATH}/FSC/EM/File/UploadByForm?needCdn=true`, formData).then(res => {
          if (!res || !res.data) {
            resolve(false)
          } else {
            resolve(res.data.TempFileName)
          }
        }).catch(err => {
          resolve(false)
        })
        // http.uploadFile(formData).then(res => {
        //   if (res.errCode === 0) {
        //     resolve(res.data.path)
        //   } else {
        //     resolve(false)
        //   }
        // })
      })
    },
    handleQrMove() {
      // const moveSpacing = 40 // 移动间隙，二维码触及顶部或底部这个距离时，背景图就会滚动
      // const autoScrollDistance = 50 // 自动滚动距离
      // const autoScrollDelay = 100 // 自动滚动间隔，ms
      // const $scroll = $(this.$refs.scroll)
      // const scrollHeight = $scroll.height()
      // const scrollTop = $scroll.scrollTop()
      // const { top: qrTop, height: qrHeight } = this.qrStyle
      // clearInterval(this.AutoScroll)
      // if (qrTop + qrHeight > scrollHeight - moveSpacing) {
      //   this.AutoScroll = setInterval(() => {
      //     if ($scroll.scrollTop() + autoScrollDistance < $scroll[0].scrollHeight) {
      //       $scroll.scrollTop($scroll.scrollTop() + autoScrollDistance)
      //       this.qrStyle = {
      //         ...this.qrStyle,
      //         top: this.qrStyle.top + autoScrollDistance,
      //       }
      //     } else {
      //       $scroll.scrollTop($scroll[0].scrollHeight)
      //     }
      //   }, autoScrollDelay)
      // } else if (qrTop > moveSpacing) {
      //   this.AutoScroll = setInterval(() => {
      //     if ($scroll.scrollTop() - autoScrollDistance > 0) {
      //       $scroll.scrollTop($scroll.scrollTop() - autoScrollDistance)
      //       this.qrStyle = {
      //         ...this.qrStyle,
      //         top: this.qrStyle.top - autoScrollDistance,
      //       }
      //     } else {
      //       $scroll.scrollTop(0)
      //     }
      //   }, autoScrollDelay)
      // }
    },
  },
}
</script>

<style lang="less" scoped>
@images: "../../../assets/images/";
.poster-editor {
  width: 100%;
  height: 450px;
  overflow: hidden;
  position: relative;
  padding-bottom: 24px;
  .icon_longimg {
    position: absolute;
    top: 0;
    left: 0;
    width: 40px;
    z-index: 1;
  }
  .poster__scroll {
    overflow: auto;
    width: 774px;
    height: 1334px;
    transform: scale(0.345);
    transform-origin: 0 0;
    &::-webkit-scrollbar {
      width: 24px;
      background: #eee;
      height: 24px;
    }
    &:not(.long-img) {
      display: flex;
      align-items: center;
    }
  }
  .poster__wrapper {
    background: #eee;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
    background-size: cover;
    position: relative;
    padding: 0;
    margin: 0;
    overflow: hidden;
    .poster__emptytips {
      color: #c1c5ce;
      font-size: 12px;
      .emptytips__bg {
        background-image: url("@{images}/defualt-pic.png");
        width: 144px;
        height: 120px;
        background-size: cover;
      }
    }
    .poster__content {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;
      padding: 0;
      margin: 0;
    }
  }
}
</style>
