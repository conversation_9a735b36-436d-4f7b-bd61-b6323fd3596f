<template>
  <div class="VAddBackgroundLocal">
    <ElImage 
      class="el-image"
      :src="model_imgSrc" 
      fit="contain"
    />
  </div>
</template>

<script>

export default {
  components: {
ElImage: FxUI.Image
},
  props: {},
  data() {
    return {
      model_imgSrc: null,
    };
  },
  watch: {
    model_imgSrc() {
      this.$emit('change', this.model_imgSrc);
    },
  },
  methods: {
    setFile(file) {
      const me = this;
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onloadstart = function() {
          console.log('图片正在上传处理......');
      };
      //操作完成
      reader.onload = function(e) {
        // file 对象的属性
        const imgBase64 = reader.result;
        me.setImage(imgBase64);
      };
    },
    setImage(imgSrc) {
      this.model_imgSrc = imgSrc;
    },
  },
}
</script>

<style lang="less" scoped>
.VAddBackgroundLocal {
  width: 160px;
  height: 160px;
  border: 1px solid #E9EDF5;
  box-sizing: border-box;
  .el-image {
    width: 100%;
    height: 100%;
  }
}

</style>