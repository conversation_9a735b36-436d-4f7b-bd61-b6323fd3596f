<template>
  <div class="add-font">
    <div class="font__list">
      <div class="font__item" v-for="(font, index) in model_fontlist" :key="font.id">
        <FontStyleEditor class="font__editor" :value="font" @input="handleInput($event, index)" type="text"></FontStyleEditor>
        <span class="fx-icon-delete" @click="handleDelete(font, index)"></span>
      </div>
    </div>
    <div class="font__add" @click="handleAddFont">
      +&nbsp;{{ $t('marketing.commons.tjwz_399ca7') }}
    </div>
  </div>
</template>

<script>
import FontStyleEditor from '@/components/font-style-editor';
import _ from 'lodash';
const defaultStyle = {
  fontSize: 14,
  fontFamily: 'Microsoft YaHei, Hiragino Sans GB, STHeiti',
  lineHeight: 24,
  color: '#fff',
  background: 'none',
  textAlign: 'left',
  fontWeight: 'normal',
  letterSpacing: 0,
  textDecoration: 'none',
  fontStyle: 'normal',
  left: 85,
  top: 230,
};
export default {
  components: {
    FontStyleEditor,
  },
  data() {
    return {
      model_fontlist: [],
      idCount: 0,
    };
  },
  watch: {
    model_fontlist() {
      console.log('model_fontlist', this.model_fontlist);
      this.$emit('update:fonts', this.model_fontlist);
    },
  },
  mounted() {},
  methods: {
    handleAddFont() {
      this.idCount++;
      this.model_fontlist.push({
        id: this.idCount,
        text: $t('marketing.components.qrposter_create_dialog.qsrwz_4f6a3c'),
        style: defaultStyle,
      });
    },
    handleInput(e, index) {
      const fontlist = _.cloneDeep(this.model_fontlist);
      fontlist[index] = {...fontlist[index], ...e};
      this.model_fontlist = fontlist;
    },
    handleDelete(font, index) {
      const fontlist = _.cloneDeep(this.model_fontlist);
      fontlist.splice(index, 1);
      this.model_fontlist = fontlist;
    },
  },
};
</script>

<style lang="less" scoped>
.add-font {
  flex: 1;
  // margin-left: 25px;
  .font__list {
    .font__item {
      display: flex;
      align-items: center;
      gap: 10px;
      justify-content: space-between;
    }
  }
  .font__editor {
    flex: 1;
    margin-bottom: 10px;
  }
  .fx-icon-delete {
    color: #545861;
    font-size: 16px;
    cursor: pointer;
  }
  .font__add {
    color: var(--color-primary06);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 142.857% */
  }
}
</style>