<template>
  <div class="add-background">
    <div class="add__wrapper" v-loading="loading" v-show="!flag_showUploader">
      <div class="add-item" v-for="item in channelList" :key="item.key">
        <img :src="item.icon" class="item-icon" alt="">
        <div class="item-content">
          <div class="item-title">
            {{ item.title }}
            <span class="item-recommend" v-if="item.recommend">{{ $t('marketing.components.qrposter_create_dialog.tj_3f9810') }}</span>
          </div>
          <div class="item-desc">{{ item.desc }}</div>
          <fx-button size="mini" plain type="primary" @click="item.key === 'chuangketie' ? handleOpenCKT() : handleOpenLocal()" class="item-btn">{{ item.btnText }}</fx-button>
        </div>
      </div>
    </div>
    <div class="show__wrapper" v-show="flag_showUploader">
      <VAddBackgroundLocal
        ref="uploader"
        :isCkt="flag_isCktImg"
        @update:bgchoose="handleLocalBackgroundChoose"
        @change="handleBackgroundChange"
      >
      </VAddBackgroundLocal>
      <div class="show__btns">
        <template v-if="flag_isCktImg">
          <div class="show__btn" :underline="false" @click="handleCKTEdit">
            <i class="iconfont">&#xe643;</i>
            {{ $t('marketing.components.qrposter_create_dialog.bjhb_7ccd40') }}
          </div>
          <div class="show__btn" :underline="false" @click="handleOpenCKT">
            <i class="iconfont">&#xe645;</i>
            {{ $t('marketing.components.qrposter_create_dialog.zxzz_3cd967') }}
          </div>
          <div class="show__btn" :underline="false" @click="handleOpenLocal">
            <i class="iconfont">&#xe644;</i>{{  $t('marketing.commons.xztp_ba9fc4')}}<!-- <input class="add__input j-yxt-localUpload" type="file" /> -->
          </div>
        </template>
        <template v-else>
          <div class="show__btn" :underline="false" @click="handleOpenCKT">
            <i class="iconfont">&#xe645;</i>{{ $t('marketing.commons.zzhb_80c7a4') }}
          </div>
          <div class="show__btn" :underline="false" @click="handleOpenLocal">
            <i class="iconfont">&#xe644;</i>{{ $t('marketing.commons.zxsc_bc8851') }}
            <!-- <input class="add__input j-yxt-localUpload" type="file" /> -->
          </div>
        </template>
      </div>
    </div>
    <!-- 用于检测是否支持第三方cookie（safari浏览器默认不支持） -->
    <iframe src="https://mindmup.github.io/3rdpartycookiecheck/start.html" style="display: none;" />
    <NoCookieDialog :visible.sync="isShowNoCookieDialog" />
    <NoLocalstorageDialog :visible.sync="isShowNoLocalstorageDialog" />
    <PayUpdateDialog :visible.sync="isShowPayUpdateDialog" v-if="isShowPayUpdateDialog" />
    <PictureSelector
      :visible.sync="isShowCutterDialog"
      @submit="handlePictureSelectorSubmit"
      :cutSize="{
      }"
    />
  </div>
</template>

<script>
import CryptoJS from 'crypto-js';
import CktDesign from "@chuangkit/chuangkit-design"; // 创客贴
// import $script from 'scriptjs';
import VAddBackgroundLocal from './VAddBackgroundLocal';
import NoCookieDialog from './NoCookieDialog';
import NoLocalstorageDialog from './NoLocalstorageDialog';
import PayUpdateDialog from './PayUpdateDialog';
import PictureSelector from '@/components/PictureSelector';

export default {
  components: {
    VAddBackgroundLocal,
    NoCookieDialog,
    NoLocalstorageDialog,
    PayUpdateDialog,
    PictureSelector
  },
  data() {
    return {
      data_cktOption: {},
      flag_initUploader: false,
      flag_showUploader: false,
      flag_isCktImg: false,
      model_addType: null,
      flag_thirdPartyCookieSupported: true,
      loading: false,
      model_beforeCutImg: '',
      model_cktData: {},
      isShowNoCookieDialog: false,
      isShowNoLocalstorageDialog: false,
      isShowPayUpdateDialog: false,
      isShowCutterDialog: false,
      cover: '',
      channelList: [
        {
          key: 'chuangketie',
          title: $t('marketing.commons.zzhb_80c7a4'),
          desc: $t('marketing.components.qrposter_create_dialog.jxckthlhbs_cebdc9'),
          icon: require('@/assets/images/qrposter/chuangketie.png'),
          recommend: true,
          btnText: $t('marketing.components.qrposter_create_dialog.ljzz_d8a662')
        },
         {
          key: 'local',
          title: $t('marketing.commons.xztp_ba9fc4'),
          desc: $t('marketing.components.qrposter_create_dialog.zcgszdbncg_4d56c5'),
          icon: require('@/assets/images/qrposter/local.png'),
          recommend: false,
          btnText: $t('marketing.components.qrposter_create_dialog.ljxz_c67e9c')
        },
      ]
    }
  },
  methods: {
    // 点击制作海报
    async handleOpenCKT() {
      if (!this.flag_thirdPartyCookieSupported) {
        if (this.getMyBrowser() === 'Safari') {
          this.isShowNoCookieDialog = true;
        } else {
          this.isShowNoLocalstorageDialog = true;
        }
        return;
      }
      // const option = {
      //   app_id: '879e93c5b1364591aaf4de6feeb7f8cb',
      //   expire_time: + new Date() + 24 * 3600 * 1000, // 过期时间
      //   user_flag: '74164',
      //   device_type : 1,
      //   kind_id: 166,
      //   charge_type: 0,
      //   coop_material_limit: 1,
      //   coop_font_limit: 1,
      //   charging_template_limit: 1,
      // };
      // option.sign = this.getSign(option);
      const option = this.data_cktOption;

      console.log('CktDesign option', option);
      const cktDesign = new CktDesign(option);
      cktDesign.open();

      window.chuangkitComplete = this.handleCktComplete
    },
    // 点击本地上传
    handleOpenLocal() {
      // this.flag_initUploader = true;
      // this.$nextTick(() => {
      //   $(this.$refs.uploader.$el).find('input[type=file]').click();
      // })
      this.isShowCutterDialog = true;
    },
    // 本地方式 - 选择了一张图片后的回调
    handleLocalBackgroundChoose() {
      this.flag_isCktImg = false;
    },
    // 海报尺寸编辑器回调
    handleBackgroundChange(backgroundImage) {
      this.flag_showUploader = true;
      this.$emit('change', backgroundImage, this.model_cktData, this.flag_isCktImg, this.coverPath)
    },
    // 海报尺寸编辑器重置
    handleUploaderReset() {
      this.model_beforeCutImg = null;
      this.flag_initUploader = false;
      this.flag_isCktImg = false;
      this.$nextTick(() => {
        // flag_initUploader置为false后，会触发uploader组件的destroy，导致触发handleBackgroundChange
        this.flag_showUploader = false;
      })
    },
    setImageInUploader(imgUrl) {
      this.$refs.uploader.setImage(imgUrl);
    },
    // 创客贴海报编辑
    async handleCKTEdit() {
      // const option = {
      //   app_id: '879e93c5b1364591aaf4de6feeb7f8cb',
      //   expire_time: + new Date() + 24 * 3600 * 1000, // 过期时间
      //   user_flag: '74164',
      //   device_type : 1,
      //   design_id: this.model_cktData.designId,
      //   charge_type: 0,
      //   coop_material_limit: 2,
      //   coop_font_limit: 2,
      //   charging_template_limit: 2,
      // };
      // option.sign = this.getSign(option);
      const option = await this.queryChuangKeTieJsSdkOption({
        designId: this.model_cktData.designId,
      });
      const cktDesign = new CktDesign(option);
      cktDesign.open();
      window.chuangkitComplete = this.handleCktComplete;
    },
    // 获取创客贴JSSDK初始化配置参数
    async queryChuangKeTieJsSdkOption(option) {
      const res = await YXT_ALIAS.http.queryChuangKeTieJsSdkOption({
        ...option,
      });
      if (res && res.errCode === 0) {
        return res.data.optionMap;
      } else {
        return {};
      }
    },
    // 创客贴完成回调
    async handleCktComplete(cktData) {
      console.log('chuangkitComplete', cktData);
      if (cktData.type === 'interrupted') {
        // 需要付费的回调
        this.handleCktNeedPay();
      }
      if (cktData.kind !== 2) {
        return;
      }
      this.loading = true;
      const url = await this.syncChuangKeTiePoster({
        designId: cktData['design-id'],
        url: `http:${cktData['source-urls']}` // thumb-urls
      });
      if (url) {
        this.model_beforeCutImg = url;
        this.setImageInUploader(url);
        this.flag_initUploader = true;
        this.flag_showUploader = true;
        this.flag_isCktImg = true;
        this.model_cktData = {...cktData, designId: cktData['design-id']};
        this.$emit('change', url, this.model_cktData, this.flag_isCktImg);
      }
    },
    handleCktNeedPay() {
      this.isShowPayUpdateDialog = true;
    },
    async syncChuangKeTiePoster({designId, url}) {
      const res = await YXT_ALIAS.http.syncChuangKeTiePoster({designId, url});
      this.loading = false;
      if (res && res.errCode === 0) {
        return this.isDev() ? `/marketing/file/redirectDownload?url=${res.data.url}` : res.data.url; // /marketing/file/redirectDownload?
      } else {
        return null;
      }
    },
    // 前端计算签名（废弃）
    getSign(option) {
      const comSecret = '4E064121EC038DD17A4285EE6376CCC6'; // 企业唯一秘钥(测试用，仅对应 appId:879e93c5b1364591aaf4de6feeb7f8cb)
      const _option = Object.assign({}, option);
      delete _option.sign;
      const pArr = [];
      for (const key in _option) {
        if (_option[key] || _option[key] === 0) {
          pArr.push(key + '=' + _option[key]);
        }
      }
      const sign = CryptoJS.MD5(pArr.sort().join('&') + comSecret).toString().toUpperCase();
      return sign;
    },
    isDev() {
      return window.APP_MARKETING_ENV === 'DEV'; // 注入当前环境  DEV:开发环境 PRO:否则是生产环境
    },
    thirdPartyCookieCheck() {
      this.message_thirdPartyCookieCheck = (evt) =>  {
        // console.log('postmessage', evt);
        if(evt.origin !== 'https://mindmup.github.io') {
          return;
        }
        let supported = false;
        if (evt.data === 'MM:3PCunsupported') {
          this.flag_thirdPartyCookieSupported = false;
        } else if (evt.data === 'MM:3PCsupported') {
          this.flag_thirdPartyCookieSupported = true;
        }
        console.log('thirdPartyCookieCheck', this.flag_thirdPartyCookieSupported);
      };
      window.addEventListener("message", this.message_thirdPartyCookieCheck, false);
    },
    getMyBrowser() {
      var userAgent = navigator.userAgent; //取得浏览器的userAgent字符串
      var isOpera = userAgent.indexOf("Opera") > -1;
      if (isOpera) {
          return "Opera"
      }; //判断是否Opera浏览器
      if (userAgent.indexOf("Firefox") > -1) {
          return "FF";
      } //判断是否Firefox浏览器
      if (userAgent.indexOf("Chrome") > -1){
        return "Chrome";
      }
      if (userAgent.indexOf("Safari") > -1) {
          return "Safari";
      } //判断是否Safari浏览器
      if (userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1 && !isOpera) {
          return "IE";
      }; //判断是否IE浏览器
    },
    handlePictureSelectorSubmit(file) {

      console.log('handlePictureSelectorSubmit', file);
      this.cover = file.url;
      this.coverPath = file.photoPath
      if(window.APP_MARKETING_ENV === 'DEV') {
        this.cover = this.cover.replace(/www/g, 'wow');
      }
      this.$refs.uploader.setImage(this.cover);
    },
  },
  async mounted() {
    const me = this;
    this.thirdPartyCookieCheck();
    this.data_cktOption = await this.queryChuangKeTieJsSdkOption({
      kindId: 166,
    });
    $('.add-background').on('change', '.j-yxt-localUpload', function () {
      // console.log('this.files[0]', this);
      me.$refs.uploader.setFile(this.files[0]);
    });
  },
  beforeDestroy() {
    window.removeEventListener('message', this.message_thirdPartyCookieCheck);
  },
}
</script>

<style lang="less" scoped>
.add-background {
  .add__input {
    opacity: 0;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
  .add__wrapper {
    display: flex;
    gap: 10px;
    .add-item {
      padding: 10px;
      display: flex;
      align-items: center;
      border-radius: 4px;
      border: 1px solid var(--color-neutrals05);
      flex: 1;
      gap: 10px;
      .item-icon {
        width: 80px;
        height: 80px;
      }
      .item-content {
        display: flex;
        flex-direction: column;
        justify-content: center;
        gap: 6px;
        flex: 1;
        .item-title {
          font-size: 14px;
          color: #181C25;
          font-weight: 700;
          line-height: 20px;
          .item-recommend {
            padding: 1px 4px;
            margin-left: 4px;
            border-radius: 2px;
            background: var(--Red-01, #FFF5F0);
            color: var(--Red-02, #FF4D4F);
            color: #FF522A;
            font-size: 11px;
            transform: none;
            -webkit-transform: none;
            line-height: 11px;
            display: inline-block;
          }
        }
        .item-desc {
          font-size: 12px;
          color: #91959E;
          line-height: 18px;
        }
        /deep/.fx-button{
          width: max-content;
          padding: 0 8px;
          height: 24px;
        }
      }
    }
  }
  .show__wrapper {
    display: flex;
    .show__btns {
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
      margin-left: 22px;
      .show__btn {
        margin-top: 8px;
        font-size: 14px;
        color: #606266;
        cursor: pointer;
        position: relative;
        &:hover {
          color: #267CFF;
        }
        .iconfont {
          margin-right: 10px;
        }
      }
    }
  }
}

</style>
