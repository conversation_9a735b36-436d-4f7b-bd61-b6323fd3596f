<template>
  <div class="common-header">
    <!-- 左侧标题 -->
    <div class="header-left">
      <h2>{{ title }}</h2>
    </div>

    <!-- 中间步骤导航 -->
    <div class="header-steps">
      <fx-steps-v2 @change="onChange"  class="header-steps-list" :active="currentStep" canClickFinished>
        <fx-step-v2
          class="header-steps-item"
          v-for="(item, index) in steps"
          :key="index"
          :title="item.title"
        ></fx-step-v2>
      </fx-steps-v2>
    </div>

    <!-- 右侧按钮组 -->
    <div class="header-actions">
      <fx-button
        v-for="(btn, index) in buttons"
        :key="index"
        :disabled="btn.disabled"
        :plain="btn.plain"
        :type="btn.type || 'default'"
        :loading="btn.loading"
        size="mini"
        @click="handleButtonClick(btn)"
        >{{ btn.text }}</fx-button
      >
    </div>
  </div>
</template>

<script>
export default {
  name: "MeetingHeader",
  props: {
    // 标题
    title: {
      type: String,
      default: $t(
        "marketing.components.MarketingActivityMappingSettings.xjzswym_d781ee"
      ) // 新建专属微页面
    },
    // 步骤配置
    steps: {
      type: Array,
      default: () => []
    },
    // 当前步骤
    currentStep: {
      type: Number,
      default: 1
    },
    // 按钮配置数组
    buttons: {
      type: Array,
      default: () => [
        {
          text: $t("marketing.components.dialog.qr_5e199d"), // 确认
          type: "default",
          action: "cancel"
        },
        {
          text: $t("marketing.components.PictureSelector.ljbc_5e2d1e"), // 立即保存
          type: "primary",
          action: "next"
        }
      ]
    }
  },
  methods: {
    handleButtonClick(btn) {
      // 触发按钮点击事件，传递按钮配置信息
      this.$emit("button-click", btn.action);
    },
    onChange(val){
      this.$emit("step-change", val.index || 1);
    }
  },
};
</script>

<style lang="less" scoped>
.common-header {
  padding: 12px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .header-left h2 {
    color: var(--color-neutrals19);
    font-family: "Source Han Sans CN";
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 28px; /* 155.556% */
    margin-right: 12px;
  }
  .header-steps {
    flex: 1;
    .header-steps-list {
      .header-steps-item {
        cursor: pointer;
        font-size: 13px;
      }
    }
  }
  .header-actions {
    display: flex;
    gap: 12px;
  }
}

</style>
