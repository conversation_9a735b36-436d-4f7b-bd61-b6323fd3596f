<!-- 组件说明 -->
<template>
  <div class="sop-kanban-init__wrapper">
    <div class="tip">{{ $t('marketing.components.sop_kanban.cmbxj_df7747') }}</div>
    <div :class="['item-wrapper']" v-loading="loading">
      <kanbanTemplateItem
        v-for="(item, index) in templateList"
        :key="index"
        :item="item"
        :marketingEventId="marketingEventId"
        :systemTemplate="true"
        v-on="$listeners"
      ></kanbanTemplateItem>
    </div>
  </div>
</template>

<script>
import kanbanTemplateItem from "./components/template-item";
import http from "@/services/http/index";
export default {
  components: {
    kanbanTemplateItem
  },
  data() {
    return {
      loading: true,
      templateList: []
    };
  },
  props: {
    marketingEventId: {
      type: String,
      default: ""
    },
    sceneType: {
      type: String,
      default: ""
    }
  },
  computed: {},
  methods: {
    listBoardTemplateBySceneType() {
      http
        .listBoardTemplateBySceneType({ sceneType: this.sceneType })
        .then(res => {
          this.loading = false;
          if (res && res.errCode == 0) {
            this.templateList = res.data;
          }
        });
    }
  },
  mounted() {},
  created() {
    this.listBoardTemplateBySceneType();
  },
  destroyed() {} //生命周期 - 销毁完成
};
</script>

<style lang="less" scoped>
.sop-kanban-init__wrapper {
  background-color: #fff;
  height: 100%;
  overflow: auto;
  border-radius: 8px;
  .tip {
    margin-left: 13px;
    padding-top: 22px;
    margin-bottom: 21px;
    color: #545861;
    font-size: 14px;
  }
  .item-wrapper {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    margin-left: 13px;
  }
  .item-wrapper-extra {
    justify-content: center;
  }
}
</style>
