<template>
  <div class="marketing__user__action-item">
    <div class="item-date">
      {{ actionData.date }}
    </div>
    <div class="action-list">
      <div
        v-for="item in actionData.items"
        :key="item.id"
        class="action-card"
      >
        <div :class="['card-icon',!item.marketingScene && 'icon_default']">
          <div :class="['icon', item.marketingScene ?'icon_'+(MARKET_ICON_MAP[item.marketingScene] || item.marketingSceneType)  : 'icon_default']" />
          <span>{{ item.createShortTime }}</span>
        </div>
        <div class="card-content">
          <div class="card-header">
            <!-- 会员行为 -->
            <template v-if="type == 'member'">
              <div>
                <span v-html="item.actionName" />
                <a
                  v-if="item.attachObjectName"
                  :class="['link', 'unlink']"
                  target="_blank"
                  rel="noopener noreferrer"
                >{{ item.attachObjectName }}</a>
              </div>
            </template>
            <!-- 在物料中拨打电话、获取员工微信、以及播放视频的时候描述不同 -->
            <template v-else-if="[1002022,1002023,1002024,1002025,1002026].includes(item.actionType)">
              <!-- 由于营销动态都直接使用后端返回的actionName（为中文）所以这里不处理多语 -->
              <span>{{ zaiText + (OBJECT_TO_NAME[item.objectType || 'other'] || '') }}</span>
              <template v-if="[13,26,16,6,4].includes(item.objectType)">
                <a
                  :class="[item.jumpLink && 'link']"
                  :href="item.jumpLink || ''"
                  target="_blank"
                >{{ item.objectName }}</a>
              </template>
              <template v-if="item.objectType === 1">
                <span>{{item.objectName}}</span>
              </template>
              <span v-html="item.actionName" />
              <span>              {{
                item.count > 1
                  ? item.count + $t("marketing.commons.c_7229ec")
                  : ""
              }}</span>
            </template>
            <template v-else>
              <span v-html="item.actionName" />
              <!-- 会议: 13、直播: 30、微页面：26、表单：16、文章：6、产品:4、官网: 29 -->
              <template v-if="[13,26,16,6,4].includes(item.objectType)">
                <a
                  :class="[item.jumpLink && 'link']"
                  :href="item.jumpLink || ''"
                  target="_blank"
                >{{ item.objectName }}</a>
              </template>
              <!-- 邮件 -->
              <template v-else-if="item.objectType === 31">
                <span
                  v-if="item.objectName"
                  class="link"
                  @click="handleMailPreview(item.objectId)"
                >{{ item.objectName }}</span>
              </template>
              <!-- 打开直播对象 -->
              <template v-else-if="item.objectType === 30">
                <span
                  v-if="item.objectName"
                  class="link"
                  @click="handleMarketingActivity(item.marketingEventId,'MarketingEventObj')"
                >{{ item.objectName }}</span>
              </template>
              <!-- 短信 -->
              <template v-else-if="item.objectType === 22">
                <span
                  v-if="item.objectName"
                  class="link"
                  @click="handleSmsPreview(item.objectId)"
                >{{ item.objectName }}</span>
              </template>
              <!-- 文件资料 -->
              <!-- <template v-else-if="item.objectType === 34">
              <span
                v-if="item.objectName"
                class="link"
                :title="item.objectName"
                @click="handleMailPreview(item.objectId)"
              >{{ item.objectName }}</span>
            </template> -->
              <!-- 营销关键词对象 -->
              <template v-else-if="item.objectType === 10004">
                <span
                  v-if="item.objectId"
                  :class="['link']"
                  @click="handleMarketingActivity(item.objectId,'MarketingKeywordObj')"
                >{{ item.objectName }}</span>
              </template>
              <!-- 其他处理逻辑 -->
              <template v-else>
                <span v-if="item.objectName">{{ item.objectName }}</span>
              </template>
              <span>              {{
                item.count > 1
                  ? item.count + $t("marketing.commons.c_7229ec")
                  : ""
              }}</span>
            </template>
          </div>

          <div class="card-main">
            <div
              v-show="item.marketingScene"
              class="item"
            >
              <span class="title">{{ $t('marketing.commons.yxcj_2aa63c') }}：</span>
              <span>{{ item.marketingScene }}</span>
            </div>
            <div
              v-show="item.spreadChannel"
              class="item"
            >
              <span class="title">{{ $t('marketing.components.user_action.hdqd_b86873') }}：</span>
              <span>{{ item.spreadChannel }}</span>
            </div>
            <div
              v-show="item.channelAccountPlatform"
              class="item"
            >
              <span class="title">{{ $t('marketing.components.user_action.qdpt_0f3640') }}：</span>
              <span>{{ item.channelAccountPlatform }}</span>
            </div>
            <div
              v-show="item.channelAccountName"
              class="item"
            >
              <span class="title">{{ $t('marketing.components.user_action.qdzh_6a1705') }}：</span>
              <span>{{ item.channelAccountName }}</span>
            </div>
            <div
              v-show="item.marketingEventName"
              class="item"
            >
              <span class="title">{{ $t('marketing.store.schd_3634ed') }}：</span>
              <span
                class="link"
                @click="handleMarketingActivity(item.marketingEventId,'MarketingEventObj')"
              >{{ item.marketingEventName }}</span>
            </div>
            <div
              v-show="item.spreadFsUserName"
              class="item"
            >
              <span class="title">{{ $t('marketing.components.market_promotion_details.tgyg_c9d3ba') }}：</span>
              <span>{{ item.spreadFsUserName }}</span>
            </div>
            <div
              v-show="item.fromUserMarketingName"
              class="item"
            >
              <span class="title">{{ $t('marketing.components.user_action.zfyh_273b7e') }}：</span>
              <span>{{ item.fromUserMarketingName }}</span>
            </div>
            <div
              v-show="item.spreadType"
              class="item"
            >
              <span class="title">{{ $t('marketing.commons.tgfs_8577b8') }}：</span>
              <span>{{ item.spreadType }}</span>
            </div>
            <div
              v-show="item.client"
              class="item"
            >
              <span class="title">{{ $t('marketing.components.user_action.khd_efc688') }}：</span>
              <span>{{ item.client }}</span>
            </div>
            <div
              v-show="item.ipQCellCore"
              class="item"
            >
              <span class="title">{{ $t('marketing.components.user_action.gsd_33b97f') }}：</span>
              <span>{{ item.ipQCellCore }}</span>
            </div>
            <div
              v-show="item.ctaName"
              class="item"
            >
              <span class="title">{{ $t('marketing.pages.cta.zj_e540d1') }}：</span>
              <span>{{ item.ctaName }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <sms-preview
      :sms-id="smsId"
      :is-show-preview="showSmsPreview"
      @onClose="handleCloseSmsPreview"
    />
    <!-- 邮件预览 -->
    <MailPreviewDialog
      v-if="showMailPreview"
      :task-id="mailId"
      :visible="showMailPreview"
      @closePreview="showMailPreview = false"
    />
  </div>
</template>
<script>
import { MARKET_ICON_MAP, OBJECT_TO_NAME } from './utils.js'
import SmsPreview from '../sms-preview/index.vue'
import MailPreviewDialog from '@/pages/mail-marketing/components/mail-preview-dialog.vue'

const { redirectToFS, requireAsync } = require('@/utils')

export default {
  components: {
    SmsPreview,
    MailPreviewDialog,
  },
  props: {
    actionData: {
      type: Object,
      default: () => ({}),
    },
    type: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      smsId: '',
      showSmsPreview: false,
      showMailPreview: false,
      mailId: '',
      action: {},
      MARKET_ICON_MAP,
      OBJECT_TO_NAME,

      zaiText: '在', // [ignore-i18n]
    }
  },
  beforeDestroy() {
    this.$detail && this.$detail.destroy()
  },
  methods: {
    handleMarketingActivity(id, apiName = 'MarketingEventObj') {
      // 打开市场活动详情
      requireAsync('crm-components/showdetail/showdetail', Detail => {
        new Detail({
          apiName,
          showMask: true,
          zIndex: 3000,
        }).show(id)
      })
    },
    handleSmsPreview(id) {
      console.log('id: ', id)
      this.smsId = id
      this.showSmsPreview = true
    },
    handleMailPreview(id) {
      this.mailId = id
      this.showMailPreview = true
    },
    handleCloseSmsPreview() {
      this.showSmsPreview = false
    },
    handleRedirectTo(_item) {
      const params = {
        appId: _item.wxServiceAccountId,
        appName: _item.wxServiceAccountName,
      }
      redirectToFS(`?#mp/wxusermsg/=/param-${JSON.stringify(params)}`)
    },
  },
}
</script>
<style lang="less" scoped>
@basePath: "../../assets/images/";
.marketing__user__action-item {
  margin: 0 30px 0 14px;
  &:last-child{
    .action-list{
      .action-card{
        &:last-child{
          .card-content{
            margin-bottom: 0;
          }
        }
      }
    }
  }
  .item-date {
    margin: 0 0 15px 40px;
    position: relative;
    width: 150px;
    &::before {
      content: "";
      width: 8px;
      height: 8px;
      border-radius: 50%;
      border: 4px solid #0c6cff;
      display: block;
      position: absolute;
      left: -33px;
      top: 0;
    }
    &::after {
      content: "";
      width: 2px;
      height: 14px;
      background: #F2F3F5;
      position: absolute;
      top: 17px;
      left: -26px;
    }
  }
  .action-list {
    .action-card {
      display: flex;
      .card-icon {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-right: 10px;
        position: relative;
        .icon {
          width: 24px;
          height: 24px;
          background-size: contain;
          margin-bottom: 5px;
          &.icon_live {
          background-image: url("@{basePath}material-icon/video.png");
          }
          &.icon_content {
            background-image: url("@{basePath}material-icon/site.png");
          }
          &.icon_activity {
            background-image: url("@{basePath}material-icon/activity.png");
          }
          &.icon_advertise {
            background-image: url("@{basePath}material-icon/advertise.png");
          }
          &.icon_wechat {
            background-image: url("@{basePath}material-icon/wechat.png");
          }
          &.icon_qywx {
            background-image: url("@{basePath}material-icon/qywx.png");
          }
          &.icon_website {
            background-image: url("@{basePath}material-icon/website.png");
          }
          &.icon_conference {
            background-image: url("@{basePath}material-icon/conference.png");
          }
          &.icon_other {
            background-image: url("@{basePath}material-icon/other.png");
          }
        }
        .icon_default{
          width: 8px;
          height: 8px;
          background-color: #C1C5CE;
          border-radius: 50%;
        }
        &::before{
          content: '';
          width: 2px;
          background: #F2F3F5;
          position: absolute;
          top: 45px;
          left: 50%;
          transform: translateX(-50%);
          height: calc(100% - 51px);
        }
        span {
          color: #91959e;
          font-size: 12px;
          font-weight: 400;
          line-height: 18px;
        }
      }
      .icon_default{
         &::before{
          content: '';
          width: 2px;
          background: #F2F3F5;
          position: absolute;
          top: 30px;
          left: 50%;
          transform: translateX(-50%);
          height: calc(100% - 34px);
        }
      }
      .card-content {
        // border: 1px solid #dee1e8;
        box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.10);
        padding: 17px 14px;
        margin-bottom: 25px;
        flex: 1;
        .card-header {
          font-size: 14px;
          line-height: 18px;
          padding-bottom: 8px;
          border-bottom: 1px dashed #dee1e8;
        }
        .card-main {
          font-size: 12px;
          display: flex;
          flex-wrap: wrap;
          padding-top: 7px;
          .item {
            margin: 10px 50px 0 0;
          }
        }
      }
    }
  }
  .link {
    color: #0c6cff;
    cursor: pointer;
  }
  .unlink {
    cursor: text;
    color: #333333;
    font-weight: 600;
    text-decoration: none;
  }
}
</style>
