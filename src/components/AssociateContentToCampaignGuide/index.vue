<template>
  <div :class="$style.associate_content_guide">
    <div v-for="item in guideList" :class="$style.associate_content_guide_item" @click="onCommand(item.command)" :key="item.title">
      <div :class="$style.guide_item_img">
        <img :src="item.icon" />
      </div>
      <div :class="$style.guide_item_title">{{ item.title }}</div>
      <div :class="$style.guide_item_content">{{ item.content }}</div>
    </div>
    <FileToHexagon
      :disabled="disabledFileUpload"
      :class="$style.associate_content_guide_item"
      :marketing-event-id="marketingEventId"
      @completed="handleFileToHexagonCompleted"
      @onUpload="val => disabledFileUpload = val"
    >
      <div :class="$style.guide_item_img">
        <img :src="setp4Icon" />
      </div>
      <div :class="$style.guide_item_title">{{ $t('marketing.commons.scwj_a6fc9e') }}</div>
      <div :class="$style.guide_item_content">{{ $t('marketing.commons.jzcscyyndw_55a3e2') }}</div>
    </FileToHexagon>

  </div>
</template>

<script>
// 组件需配合AssociateContentToCampaignButton 一起使用，直接调用它的方法
import FileToHexagon from '@/components/file-to-hexagon/index.vue'
const setp1Icon = require('@/assets/images/guideStep1.svg');
const setp2Icon = require('@/assets/images/guideStep2.svg');
const setp3Icon = require('@/assets/images/guideStep3.svg');
const setp4Icon = require('@/assets/images/guideStep4.svg');
export default {
  components: {
    FileToHexagon
  },
  props: {
    marketingEventId: String,
  },
  data() {
    return {
      setp4Icon: setp4Icon,
      disabledFileUpload: false,
      guideList: [
        {
          title: $t('marketing.components.AssociateContentToCampaignButton.xjzswym_d781ee'),
          content: $t('marketing.components.AssociateContentToCampaignGuide.cjghdxgdxc_d668f4'),
          icon: setp1Icon,
          command: 'private_site'
        },
        {
          title: $t('marketing.commons.cnrzxxz_1c0d28'),
          content: $t('marketing.components.AssociateContentToCampaignGuide.cnrzxxzwzc_1840e5'),
          icon: setp2Icon,
          command: 'content_center'
        },
        {
          title: $t('marketing.components.AssociateContentToCampaignButton.yywbnr_6d566f'),
          content: $t('marketing.components.AssociateContentToCampaignGuide.zctjwbnrlj_2f138a'),
          icon: setp3Icon,
          command: 'external_content'
        },
      ]
    };
  },
  methods: {
    handleFileToHexagonCompleted() {
      this.$emit('onMaterialAdded')
    },
    onCommand(command) {
      this.$emit('onCommand', command)
    }
  }
}
</script>

<style lang="less" module>
.associate_content_guide {
  display: flex;
  padding: 100px 12px 200px 12px;
  align-items: center;
  justify-content: center;

  .associate_content_guide_item {
    display: flex;
    width: 160px;
    height: 209px;
    border-radius: 4px;
    border: 1px solid #DEE1E8;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    padding: 10px;
    box-sizing: border-box;
    margin-right: 16px;
    transition: all 0.2s ease;

    &:last-child {
      margin-right: 0;
    }

    &:hover {
      box-shadow: 0px 0px 10px 0px #0000001A;
      border: 1px solid #fff;
    }

    .guide_item_img {
      width: 80px;
      height: 80px;

      img {
        max-width: 100%;
        max-height: 100%;
      }
    }

    .guide_item_title {
      color: #181C25;
      font-family: Source Han Sans CN;
      font-size: 14px;
      font-weight: 400;
      line-height: 20px;
      margin: 10px 0 5px;
    }

    .guide_item_content {
      font-family: Source Han Sans CN;
      font-size: 12px;
      font-weight: 400;
      line-height: 18px;
      color: #91959E;
      text-align: center;
      height: 36px;
    }
  }
}
</style>
