<template>
  <VDialog
    class="SpreadSmsDialog"
    :title="$t('marketing.components.SpreadSmsDialog.bjdxnr_b8833c')"
    width="820px"
    :visible="visible"
    append-to-body
    :ok-text="$t('marketing.commons.bc_be5fbb')"
    :cancel-text="$t('marketing.commons.qx_625fb2')"
    @onSubmit="handleSubmit"
    @onClose="handleClose"
  >
    <div class="SpreadSmsDialog__body">
      <SpreadSmsDialogPreview
        class="SpreadSmsDialog__preview"
        :sms-content="model_content"
        :sms-content-type="model_template.useTemplate ? 1 : 2"
      />
      <ElForm
        ref="form"
        class="SpreadSmsDialog__form"
        label-width="90px"
        label-position="left"
        :model="formData"
        :rules="formRules"
      >
        <ElFormItem
          :label="$t('marketing.components.SpreadSmsDialog.fsmb_bf880e')"
          required
        >
          <fx-input :placeholder="$t('marketing.components.SpreadSmsDialog.qxzdxmb_86a60d')" v-model="model_template.template.name" class="template-select__bar" @focus="handleChooseTemplate"></fx-input>
        </ElFormItem>
        <div class="valid-error" v-if="!this.model_template.template.id && templateIdError">
          {{ $t('marketing.components.SpreadSmsDialog.qxzdxmb_86a60d') }}
        </div>
        <!-- recipient.sendRange === 1:为Excel上传 -->
         <ElFormItem
          :label="$t('marketing.components.SpreadSmsDialog.szcs_ceef98')"
          v-if="model_content.indexOf('}') > 0 && model_template.template.tplVersion === 1 && recipient.sendRange !== 1"
          required
        >
          <smsParams
            ref="smsParams"
            :campaignInfoAble="[1,2,4].includes(sceneType)"
            :params="model_content"
            :platform-id="model_template.template.providerId"
            :validateParams="validateSmsParams"
            @onChange="handleSmsVarArgChange"
            :marketingEventId="marketingEventId"
          />
        </ElFormItem>
        <ElFormItem
          :label="$t('marketing.commons.fsnr_51c25d')"
          required
        >
          <SpreadSmsDialogContent
            ref="$SmsContent"
            v-model="model_content"
            :param-data-list="data_paramDataList"
            :scene="scene"
            :template="model_template"
          />
        </ElFormItem>
        <ElFormItem
        >
            <fx-switch style="margin-right: 5px;" v-model="formData.filterReceived" size="small"></fx-switch>
            {{$t('marketing.components.SpreadDialog.gldqhdzysd_75f81f')}}
        </ElFormItem>
        <div
          v-if="!isPoster"
          class="tips"
        >
          {{ $t('marketing.components.SpreadSmsDialog.smdxxxnrzh_b3d6af') }}
        </div>
      </ElForm>
      <!-- 推广只允许营销模板，活动通知允许营销也允许通知模板 -->
      <smsTemplateDialog
        v-if="smsTemplateDialogVisible"
        :tplTypeList="(sceneType === 0 || sceneType === 3) ? ['PROMOTION'] : ['all','PROMOTION','NOTIFICATION']"
        :show-real-content='(recipient.sendRange == 1)'
        :visible.sync="smsTemplateDialogVisible"
        @update:submit="confirmSmsTemplateSelected"
      />
    </div>
  </VDialog>
</template>

<script>
import VDialog from '@/components/dialog/index.vue'
import SpreadSmsDialogPreview from './SpreadSmsDialogPreview.vue'
import SpreadSmsDialogTemplate from './SpreadSmsDialogTemplate.vue'
import SpreadSmsDialogContent from './SpreadSmsDialogContent.vue'
import smsTemplateDialog from '@/pages/promotion-activity/sms/components/sms-template-dialog.vue'
import smsParams from '@/pages/promotion-activity/sms/components/sms-params.vue'

export default {
  components: {
VDialog,
ElForm: FxUI.Form,
ElFormItem: FxUI.FormItem,
SpreadSmsDialogPreview,
SpreadSmsDialogTemplate,
SpreadSmsDialogContent,
smsTemplateDialog,
smsParams
},
  /**
   * 海报隐藏提示
   */
  inject: {
    isPoster: {
      default: false,
    },
  },
  props: {
    // 短信模板场景 0 其他营销  1其他通知  2会议报名  3会议邀约  4直播报名  5直播邀约  6：EXCEL）
    sceneType: {
      type: Number,
      default: 0,
    },
    recipient: {
      type: Object,
      default: () => {},
    },
    visible: {
      type: Boolean,
      default: false,
    },
    scene: {
      type: String,
      default: '',
    },
    material: {
      type: Object,
      default() {
        return {}
      },
    },
    marketingEventId:{
      type: String,
      default: ''
    }
  },
  data() {
    return {
      formData: {
        sendData: {
          content: '',
          msgType: 2,
        },
        marketingObj: {
          id: '5f8958d6e8458d0001a68c13',
        },
        filterReceived: false,
      },
      formRules: {

      },
      data_paramDataList: [],
      data_signature: this.$store.state.SmsMarketing.signature,
      model_template: {
        useTemplate: true,
        template: {},
      },
      model_content: '',
      smsTemplateDialogVisible: false,
      // 模板版本 0旧模板 1营销通新模板
      smsVarArgsParams: {
        smsVarArgs: [],
        paramsValid: true
      },
      validateSmsParams: false,
      templateIdError: false
    }
  },
  computed: {
    // currentSceneType() {
    //   debugger
    //   // currentSceneType为最终传给后台的短信模板场景的值
    //   if (this.recipient.sendRange === 1) {
    //     // 选择发送对象为上传excel,不论当前活动为什么类型，固定sceneType传6 （产品逻辑）
    //     this.initData()
    //     return 6
    //   } if (this.recipient.sendRange === 2) {
    //     // 发送对象为选择目标人群，则取sceneType  根据当前活动类型
    //     this.initData()
    //     this.handleContentReset()
    //     return this.sceneType
    //   }
    //   // 其他情况如：会议/直播列表
    //   return this.sceneType
    // },
  },
  watch: {
    model_template() {
      console.log('model_template', this.model_template)
    },
    async material() {
      if (this.data_paramDataList.length === 0) {
        await this.getSmsParamList()
      }
      this.handleContentReset()
    }
  },
  mounted() {
    this.initData()
    if(this.recipient.sendRange === 2){
      this.handleContentReset()
    }
    this.getSmsParamList()
  },
  methods: {
    initData() {
      this.data_paramDataList = []
      this.model_content = `【${this.$store.state.SmsMarketing.signature}】`
      this.model_template = {
        useTemplate: true,
        template: {},
      }
    },
    async handleSubmit() {
      if(!this.model_template.template.id){
        this.templateIdError = true
        return
      }
      let _templateContent = (this.model_content.indexOf(`【${this.data_signature}】`) != -1) ? this.model_content.substring(`【${this.data_signature}】`.length) : this.model_content
      let smsVarArgs = [], paramsValid = true
      // 如果发送对象不是Excel 并且不是旧模板
      if(this.recipient.sendRange !== 1 && this.model_template.template.tplVersion === 1){
        smsVarArgs = this.smsVarArgsParams.smsVarArgs
        paramsValid = this.smsVarArgsParams.paramsValid
        this.validateSmsParams = true
      }
      if(!paramsValid) return
      if (this.sceneType === 2 || this.sceneType === 3) {
        // 从会议参会人员/邀约人员过来
        this.data_paramDataList.forEach(item => {
          const str = `{${item.name}}`
          const replaceStr = `{${item.value}}`
          _templateContent = _templateContent.replace(str, replaceStr)
        })
      }
      const submitParams = {
        // marketingUserGroupIds: [],
        eventType: 2, // 事件类型 1保存为草稿 2 发送
        sceneType: this.sceneType, // 短信发送来源渠道 2-会议门票 3-会议邀请 4-直播通知
        content: this.model_content,
        // templateContent: _templateContent, // 模板内容
        templateId: this.model_template.template.id,
        shortUrlMap: {}, // 需要拼接营销活动id的短链map
        // taPath: null, // Excel的taPath
        filterReceived: this.formData.filterReceived,
      }
      if(this.recipient.sendRange !== 1){
        submitParams.smsVarArgs = smsVarArgs
      }
      this.$emit('submit', submitParams)
    },
    handleClose() {
      this.$emit('update:visible', false)
    },
    async handleContentReset() {
      // 会议通知和直播通知预设模板
      if (this.sceneType === 2 || this.sceneType === 4) {
        const presetTemplate = await this.getPresetTemplate()
        if (!presetTemplate) {
          return
        }

        this.model_content = presetTemplate.templateContent
        this.model_template = {
          useTemplate: true,
          template: {
            id: presetTemplate.templateId,
            name: presetTemplate.templateName,
            content: presetTemplate.templateContent,
          },
        }
      }
    },
    async getSmsParamList() {
      // sceneType场景值： 2-会议门票 3-会议邀请 4-直播通知 5-直播邀约
      if (
        this.sceneType === 2
        || this.sceneType === 3
        || this.sceneType === 3
        || this.sceneType === 4
      ) {
        const res = await YXT_ALIAS.http.getSmsParamList({
          sceneType: this.sceneType,
        })
        if (res && res.errCode === 0) {
          this.data_paramDataList = res.data.paramDescResults
        }
      }
    },
    handleSpreadSmsDialogContentMounted() {
      console.log('handleSpreadSmsDialogContentMounted')
      this.handleContentReset()
    },
    handleContentReady() {
      console.log('handleContentReady')
    },
    async getPresetTemplate() {
      const res = await YXT_ALIAS.http.getPresetTemplate({
        sceneType: this.sceneType,
      })
      if (res && res.errCode === 0) {
        return res.data
      }

      return undefined
    },
    handleChooseTemplate(){
      if(!this.marketingEventId){
        FxUI.Message.warning($t('marketing.commons.qxxzschd_493166'))
        return
      }
      this.smsTemplateDialogVisible = true
    },
    confirmSmsTemplateSelected(template = {}){
      const { providerId } = template
      const contentPrefix = (providerId === 'mw' && this.data_signature) ? `【${this.data_signature}】` : ''
      this.model_content =`${contentPrefix}` + template.content
      this.model_template = {
        useTemplate: true,
        template: template,
      }
    },
    handleSmsVarArgChange(data){
      this.smsVarArgsParams = data
    }
  },
}
</script>

<style lang="less" scoped>
.SpreadSmsDialog {
  /deep/ .el-dialog__body {
    padding: 0;
  }
  .SpreadSmsDialog__body {
    display: flex;
  }
  .SpreadSmsDialog__preview {
    flex-shrink: 0;
    padding: 22px 19px 17px 17px;
    background: #fafafa;
  }
  .SpreadSmsDialog__form {
    flex: 1;
    padding: 12px 14px 5px 18px;
    box-sizing: border-box;
    /deep/ .el-form-item__label {
      font-size: 13px;
      color: #181c25;
    }
  }
  .tips {
    color: #737c8c;
    font-size: 12px;
  }
  .valid-error{
    color: var(--color-danger06, #ff522a);
    font-size: 12px;
    line-height: 1;
    padding-top: 4px;
    margin: -20px 0 10px 90px;
  }
}
</style>
