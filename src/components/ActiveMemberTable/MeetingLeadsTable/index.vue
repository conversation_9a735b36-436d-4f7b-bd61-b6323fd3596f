<script>
import BaseTable from "../BaseTable";
import ExtendTemplate from "./ExtendTemplate";
import http from "@/services/http/index";
import { formatStringByEmpty } from "@/utils";
import mixins from "./mixins";
const formatter = (rowData, column, cellValue) =>
  formatStringByEmpty(cellValue);

export default {
  extends: BaseTable,
  components: {
ExtendTemplate
},
  mixins: [mixins],
  props: {
    autoOpenNotification: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      tableSource:'meeting',
      queryType: 0,
      tableName: 'meeting-leads-table',
      /**
       * 消息通知场景参数
       */
      noticeInfo: {
        scene: "meeting",
        sceneType: 2 //短信营销场景
      },

      // 弹窗相关
      isShowParticipantsAddDialog: false,
      isShowParticipantsImportDialog: false,
      isShowStatusImportDialog: false,

      isShowParticipantsSigninDialog: false,
      isShowParticipantsInviteDialog: false,
      isShowParticipantsNoticeDialog: false,
      isShowParticipantsGroupSelector: false,
      isShowParticipantsReivewDialog: false,
      isShowParticipantsCrmResaveDialog: false,
      isShowParticipantsFormDialog: false,
    };
  },
  computed: {
    conferenceDetail() {
      return this.$store.state.MeetingMarketing.conferenceDetail;
    },
    conferenceId() {
      return this.conferenceDetail.id;
    }
  },
  methods: {
    async getDatas(params) {
      if(!this.conferenceId) return {};
      const { errCode, data = {} } = await http.queryConferenceParticipants({
        conferenceId: this.conferenceId,
        ...params
      });
      if(errCode === 0){
        return data
      }
      return {};
    },
    addExtensionColumns(columns) {
      return columns.concat([
        {
          prop: "inviteUserName",
          label: $t('marketing.commons.yyr_3995ec'),
          minWidth: "100px",
          formatter
        },
        {
          prop: "inviteStatus",
          label: $t('marketing.commons.yyzt_9fe7a3'),
          minWidth: "100px",
          formatter
        },
        {
          prop: "reviewStatus",
          label: $t('marketing.commons.shzt_b6d0e9'),
          minWidth: "100px",
          formatter
        },
        {
          prop: "code",
          label: $t('marketing.commons.chm_4d111e'),
          minWidth: "100px",
          formatter
        },
        {
          prop: "signStatus",
          label: $t('marketing.commons.qdzt_e9c1f2'),
          minWidth: "100px",
          formatter
        },
        {
          prop: "signTime",
          label: $t('marketing.commons.qdsj_a232e7'),
          minWidth: "100px",
          formatter
        },
        {
          prop: "groupName",
          label: $t('marketing.commons.fz_829abe'),
          minWidth: "120px",
          exComponent: "tag",
          formatter
        }
      ]);
    },
    parseExtensionData(data) {
      const _pushItem = data;
      // 签到状态
      _pushItem.signStatus = ["", "", $t('marketing.commons.yqd_e81a9d'), $t('marketing.commons.wqd_52ece7')][_pushItem.signStatus];
      // 审核状态
      _pushItem.reviewStatus = [$t('marketing.commons.dsh_5cb424'), $t('marketing.commons.shytg_9a7aa0'), $t('marketing.commons.shwtg_f50e9d')][
        _pushItem.reviewStatus
      ];
      // 邀约状态
      _pushItem.inviteStatus = [$t('marketing.commons.yyy_4a0741'), $t('marketing.commons.dyy_e6ebc4')][_pushItem.inviteStatus];
      // 分组
      _pushItem.groupName = _pushItem.groupName
        ? _pushItem.groupName.map(item => ({
            firstTagName: item
          }))
        : [];
      return _pushItem;
    },
    exportListDataHandler(params, callback) {
      const opts = {
        action: "exportConferenceParticipants",
        params: {
          conferenceId: this.conferenceId
        }
      };
      this.exportListData(opts).then(() => {
        callback();
      });
    },
    setOperationButton(buttons) {
      return [
        {
          name: $t('marketing.commons.tjchry_a38838'),
          func: this.handleShowAddDialog
        },
        {
          name: $t('marketing.commons.drwbbmsj_0aec68'),
          func: this.handleImport
        },
        {
          name: $t('marketing.commons.drgxqdzt_4bb8e3'),
          func: this.handleStatusImport
        },
        ...buttons
      ];
    },
    handleShowAddDialog(params, callback) {
      this.isShowParticipantsAddDialog = true;
      callback();
    },
    handleImport() {
      if (!this.conferenceDetail.formId) {
        FxUI.Message.warning($t('marketing.components.ActiveMemberTable.hyzywpzbmb_99b314'));
        return;
      }
      this.isShowParticipantsImportDialog = true;
    },
    handleStatusImport() {
      this.isShowStatusImportDialog = true;
    },

    handleInvite() {
      this.isShowParticipantsInviteDialog = true;
    },
    handleReview() {
      this.isShowParticipantsReivewDialog = true;
    },
    handleNotice() {
      this.isShowParticipantsNoticeDialog = true;
    },
    handleGroup() {
      this.isShowParticipantsGroupSelector = true;
    },
    handleSignin() {
      this.isShowParticipantsSigninDialog = true;
    },
    handleResave() {
      this.isShowParticipantsCrmResaveDialog = true;
    }
  },
  created() {
    console.log('i am Meeting Created');
  },
};
</script>
