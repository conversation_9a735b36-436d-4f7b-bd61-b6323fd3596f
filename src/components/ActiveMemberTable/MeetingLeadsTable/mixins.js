/*
 * @Author: <EMAIL>
 * @Date: 2021-06-24 17:18:32
 * @Description: 
 */
export default {
  data() {
    return {
       /**
       * 来源渠道列表
       * @type {Array}
       */
      userGroups: [],
      filterMenus: [
        {
          value: null,
          fieldName: "inviteStatus",
          fieldLabel: $t('marketing.commons.yyzt_9fe7a3'),
          options: [
            {
              value: null,
              label: $t('marketing.commons.qb_a8b0c2'),
            }, {
              value: 1,
              label: $t('marketing.commons.dyy_e6ebc4'),
            }, {
              value: 0,
              label: $t('marketing.commons.yyy_4a0741'),
            }
          ]
        },
        {
          value: null,
          fieldName: "reviewStatus",
          fieldLabel: $t('marketing.commons.shzt_b6d0e9'),
          options: [
            {
              value: null,
              label: $t('marketing.commons.qb_a8b0c2'),
            }, {
              value: 0,
              label: $t('marketing.commons.dsh_5cb424'),
            }, {
              value: 2,
              label: $t('marketing.commons.shwtg_f50e9d'),
            }, {
              value: 1,
              label: $t('marketing.commons.shytg_9a7aa0'),
            }
          ]
        },
        {
          value: null,
          fieldName: "signStatus",
          fieldLabel: $t('marketing.commons.qdzt_e9c1f2'),
          options: [
            {  
              value: null,
              label: $t('marketing.commons.qb_a8b0c2'),
            }, {
              value: 3,
              label: $t('marketing.commons.wqd_52ece7'),
            }, {
              value: 2,
              label: $t('marketing.commons.yqd_e81a9d'),
            }
          ]
        },
        {
          value: null,
          fieldName: "saveCrmStatus",
          fieldLabel: $t('marketing.commons.crxszt_a3195f'),
          options: [
            {
              value: null,
              label: $t('marketing.commons.qb_a8b0c2'),
            },
            {
              value: 0,
              label: $t('marketing.commons.ycr_0844f1'),
            },
            {
              value: 3,
              label: $t('marketing.commons.ygl_998d15'),
            },
            {
              value: 1,
              label: $t('marketing.commons.crsb_459d3d'),
            },
            // {
            //   value: 2,
            //   label: $t('marketing.commons.zfdgl_46c644'),
            // },
            // {
            //   value: 99,
            //   label: $t('marketing.commons.dcl_047109'),
            // }
          ]
        },
      ],
      filterButtons: [{
          name: $t('marketing.commons.yytz_e943fc'),
          func: this.handleInvite,
          id: 'guide__invite-btn'
        },
        {
          name: $t('marketing.commons.sh_cf13b1'),
          func: this.handleReview
        },
        {
          name: $t('marketing.commons.hytz_14e997'),
          func: this.handleNotice,
          id: 'guide__all-participants-notification'
        },
        {
          name: $t('marketing.commons.szfz_194cfa'),
          func: this.handleGroup
        },
        {
          name: $t('marketing.commons.sdbq_cc7410'),
          func: this.handleSignin,
          id: 'guide__manual-update-participants-sign-in-status'
        },
        {
          name: $t('marketing.commons.zxcrxs_f70585'),
          func: this.handleResave
        },
        {
          name: $t('marketing.commons.sc_2f4aad'),
          func: this.handleDelete
        },
      ],
    }
  },
  methods: {
    async queryConferenceUserGroup() {
      const res = await YXT_ALIAS.http.queryConferenceUserGroup({conferenceId: this.conferenceId});
      if (res && res.errCode === 0) {
        this.userGroups = res.data || [];
        //初始化渠道下拉选择框
        let userGroupFilter = {
          value: null,
          fieldName: "groupUserId",
          fieldLabel: $t('marketing.commons.fzsx_e380b5'),
          options: [
            {
              label: $t('marketing.commons.qb_a8b0c2'),
              value: null
            }
          ]
        };
        this.userGroups.forEach(item => {
          userGroupFilter.options.push({
            label: item.groupName,
            value: item.id
          });
        });
        this.filterMenus.push(userGroupFilter);
      }
    },
  },
  created() {
    if (!this.userGroups.length) {
      this.queryConferenceUserGroup();
    }
  },
}
