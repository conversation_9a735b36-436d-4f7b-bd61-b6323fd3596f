import http from "@/services/http";
import kisvData from '@/modules/kisv-data';
export default {
  data() {
    return {
      /**
       * 列表加载状态
       * @type {Boolean}
       */
      loading: false,
      /**
       * 列表数据
       * @type {Array}
       */
      lists: [],
      /**
       * 列数据
       * @type {Array}
       */
      columns: [],
      /**
       * 选中人员
       * @type {Array}
       */
      selectedItems: [],
      /**
       * 是否全选
       * @type {Boolean}
       */
      selectedAll: false,
      /**
       * 选中活动成员ID
       * @type {String}
       */
      selectedCampaignId: "",
      /**
       * 查询类型：0:会议 1:直播 2:营销活动
       * @type {Number}
       */
      queryType: 1,
      /**
       * 右上角操作按钮
       * @type {Array}
       */
      operationButtons: [
        {
          name: $t('marketing.commons.dc_55405e'),
          func: this.exportListDataHandler,
          showTooltip: true,
          tooltip: $t('marketing.components.ActiveMemberTable.dcwjdbtjwn_b881f5')
        },
        {
          name: $t('marketing.commons.sx_694fc5'),
          func: "refresh"
        }
      ],
      /**
       * 筛选菜单
       * @type {Array}
       */
      filterMenus: [
        {
          value: "",
          fieldName: "",
          options: [
            {
              label: $t('marketing.commons.qb_a8b0c2'),
              value: null
            }
          ]
        }
      ],
      /**
       * 筛选字段集合
       * @type {Object}
       */
      filterParams: {},
      /**
       * 过滤导航操作按钮
       * @type {Array}
       */
      filterButtons: [
        {
          name: $t('marketing.commons.zbtz_49ce7b'),
          func: this.sendNotice
        },
        {
          name: $t('marketing.commons.zxcrxs_f70585'),
          func: this.saveResave
        },
        {
          name: $t('marketing.commons.sc_2f4aad'),
          func: this.handleDelete
        },
      ],
      /**
       * 分页配置数据
       * @type {Object}
       */
      pageNavigationData: {
        layout: "prev, pager, next, total, sizes, jumper",
        pageNum: 1,
        totalCount: 0,
        pageSize: 10,
        pagerCount: 5,
        pageSizes: [10, 20, 30, 40]
      },
      /**
       * 消息通知场景参数
       * @type {Object}
       */
      noticeInfo: {
        scene: "live",
        sceneType: 4
      },
      /**
       * 搜索关键词
       * @type {String}
       */
      searchKeyword: "",
      /**
       * @type {Boolean}
       */
      isShowCrmResaveDialog: false,
      /**
       * @type {Boolean}
       */
      isShowNoticeDialog: false,
      /**
       * @type {Boolean}
       */
      isShowFormDialog: false,
      /**
       * @type {Function}
       */
      getRowKeys: function(row) {
        return row.id;
      },
      /**
       * 来源渠道列表
       * @type {Array}
       */
      channelLists: [],
      /**
       * 会员详情数据
       * @type {Object}
       */
      memberDetailData: {},
      /**
       * @type {Boolean}
       */
      memberDetailVisible: false,
      /**
       * @type {Array}
       */
       filterSelectedList: [],
      /**
       * @type {String}
       */
      sourceId: '',
      /**
       * 页面初始配置
       * @type {Object}
       */
      vDatas: kisvData.datas,
       /**
       * 重新存入dialog文字提示
       * @type {Object}
       */
      crmResaveText:$t('marketing.commons.qrhsxchryc_8fcf4b')
    };
  },
  computed: {
    selectedCount() {
      return this.selectedItems.length || 0;
    }
  },
  watch: {
    selectedItems() {
      console.log("selectedItems change", this.selectedItems);
    },

    sourceId() {
      this.queryColumns();
      this.createOperationButton();
      this.createFilterButton();
      this.setFilterMenuDefaultValue();
      this.queryLists();
      this.getWalletStatus();
    }
  },
  methods: {
    /**
     * 调用父实现的方法
     * @param {String} methodName 父类方法名
     */
    callSuper(methodName) {
      this.constructor.super.options.methods[methodName].call(this);
    },
    /**
     * 获取所有活动成员列表数据
     * 全选功能使用
     * @returns {Object}
     */
    async getAllActiveMember() {
      console.log(this.marketingEventId,'this.marketingEventId')
      if (!this.marketingEventId) return [];
      const params = {
        ...this.filterParams,
        marketingEventId: this.marketingEventId,
        queryType: this.queryType
      };
      if (this.searchKeyword) {
        params.keyword = this.searchKeyword;
      }
      delete params.pageNum;
      let result = [];
      try {
        const { errCode, data } = await http.queryAllCampaignData(params);
        // 这里返回的数据是 { id: xxx,name: xxx } 活动成员table那里使用的是campaignId
        if (errCode === 0 && Array.isArray(data)) {
          result = data.map(item => {
            return {
              ...item,
              campaignId: item.id
            }
          });
        }
      } catch (error) {
        console.log(error);
      }
      return result;
    },
    /**
     * 获取渠道列表
     */
    async queryChannelList() {
      /**
       * 官网线索表单不需要来源渠道
       */
      if (this.tableSource === "website-clue") {
        return;
      }
      await http.queryChannelList().then(({ errCode, data }) => {
        if (errCode === 0) {
          this.channelLists = data || [];
          //初始化渠道下拉选择框
          let channelValueFilter = {
            value: null,
            fieldName: "channelValue",
            fieldLabel: $t('marketing.commons.lyqd_027e67'),
            options: [
              {
                label: $t('marketing.commons.qb_a8b0c2'),
                value: null
              }
            ]
          };
          this.channelLists.forEach(item => {
            channelValueFilter.options.push({
              label: item.label,
              value: item.value
            });
          });
          this.filterMenus.push(channelValueFilter);
        }
      });
    },

     // 是否开通企业钱包插件
     getWalletStatus() {
      let walletStatus = this.vDatas.uinfo['bindWallet'];
      let payFilters = {
        value: null,
        fieldName: 'payStatus',
        fieldLabel: $t('marketing.commons.zfzt_510fa2'),
        options: [
          {
            value: 0,
            label: $t('marketing.commons.qb_a8b0c2'),
          },
          {
            value: 1,
            label: $t('marketing.commons.wzf_608afd'),
          },
          {
            value: 2,
            label: $t('marketing.commons.yzf_8d02a5'),
          },
        ],
      };
      const hasInclude = this.filterMenus.find(el => el.fieldName === 'payStatus');
      if (walletStatus && !hasInclude) {
        this.filterMenus.push(payFilters);
      }
    },

    /**
     * 按分页拉取活动成员列表
     * @param {Object} params
     */
    async queryLists(params = {}) {
      const { pageSize, pageNum, totalCount } = this.pageNavigationData || {};
      params = {
        ...this.filterParams,
        pageSize,
        pageNum,
        ...(params || {})
      };
      if (this.searchKeyword) {
        params.keyword = this.searchKeyword;
      }
      this.loading = true;
      if (params.selectedAll) {
        params.pageSize = totalCount || pageSize;
        params.pageNum = 1;
        delete params.selectedAll;
      } else {
        this.pageNavigationData.pageNum = params.pageNum || 1;
      }

      //来源渠道为空时
      if (!this.channelLists.length) {
        await this.queryChannelList();
      }
      if(this.$refs.tableHeader)
      this.$refs.tableHeader.refreshHeaderData();
      const data = await this.getDatas(params);
      this.loading = false;
      this.pageNavigationData.totalCount = data?.totalCount || 0;
      this.lists =
        this.parseData(data, {
          channelLists: this.channelLists
        }) || [];
    },
    /**
     * 获取表头数据
     */
    queryColumns() {
      const columns = this.getColumns();
      this.columns = columns || [];
    },
    /**
     *重置列表菜单
     */

    reset() {
      //重置搜索关键词为空
      this.searchKeyword = "";
      //重置过滤菜单为默认值状态
      this.filterMenus = this.filterMenus.map(item => ({
        ...item,
        value: this.filterMenuDefaultValues[item.fieldName]
      }));
      this.filterParams = {};
    },
    /**
     * 刷新列表
     */
    refresh() {
      this.reset();
      this.queryLists();
    },
    /**
     * 操作按钮回调
     */
    bindHeaderTriggerEvent(func, params) {
      this[func](params);
    },
    /**
     * 生成操作按钮
     */
    createOperationButton() {
      this.$set(
        this,
        "operationButtons",
        this.setOperationButton(this.operationButtons)
      );
    },
    /**
     * 生成筛选条件按钮
     */
    createFilterButton() {
      this.$set(
        this,
        "filterButtons",
        this.setFilterButton(this.filterButtons)
      );
    },
    // 通知提醒
    sendNotice() {
      this.isShowNoticeDialog = true;
    },
    sendNoticeHandler() {
      // this.isShowNoticeDialog = false;
      // FxUI.Message.success("通知成功");
      this.$emit("sendNoticeCompleted");
    },
    // 重新存入Crm线索
    saveResave() {
      this.isShowCrmResaveDialog = true;
      if(this.tableSource == 'website-clue'){
        this.crmResaveText = $t('marketing.commons.jhdsxcrsbd_fedc65');
      }
    },
    saveResaveHandler() {
      this.isShowCrmResaveDialog = false;
      this.clearSelect();
      this.$emit("saveResaveCompleted");
    },
    // 表单详情
    showFormData(row) {
      this.selectedCampaignId = row.id;
      this.isShowFormDialog = true;
    },
    // 显示活动成员详情页
    showCampaign(row) {
      console.log("活动成员详情页", row);
      // campaignmembersobj
      CRM.api.show_detail({
        apiname: "CampaignMembersObj",
        id: row.campaignMembersObjId
      });
    },
    showClue(row) {
      console.log("crm对象详情页", row);
      if (row.saveCrmStatus === 0 || row.saveCrmStatus === 3) {
        CRM.api.show_detail({
          apiname: row.bindCrmObjectApiName,
          id: row.bindCrmObjectId
        });
      }
    },
    /**
     * 全选
     */
    async selectAll() {
      const selectedItems = await this.getAllActiveMember();
      console.log(selectedItems,'getAllActiveMember')
      //选中全部人员
      this.clearSelect();
      await this.$nextTick()
      selectedItems.forEach(item => {
        this.$refs.table.toggleRowSelection(item, true);
      });
      this.selectionChange(selectedItems);
      this.lists = this.lists.slice(0, this.pageNavigationData.pageSize);
      this.selectedAll = true;
    },
    // 清空选择
    selectClear() {
      this.selectedItems = [];
      this.$refs.table.clearSelection();
    },
    selectionChange(val) {
      this.selectedItems = val;
      console.log(this.selectedItems,'this.selectedItems')
    },
    pageChange(data) {
      this.pageNavigationData.pageNum = data.pageNum;
      this.pageNavigationData.pageSize = data.pageSize;
      this.queryLists();
    },
    filterSubmit(params) {
      this.filterParams = params;
      this.selectedItems = [];
      this.queryLists(params);
    },
    filterChange(params) {
      this.filterSelectedList = params;
    },
    handleTagItemClose(index) {
      const tag = this.filterSelectedList.splice(index, 1)[0];
      tag.fieldName && this.$delete(this.filterParams, tag.fieldName)
      this.queryLists();
    },
    handleTagsClear() {
      this.filterSelectedList.forEach(item => {
        item.fieldName && this.$delete(this.filterParams, item.fieldName)
      })
      this.filterSelectedList = [];
      this.queryLists();
    },
    clearSelect() {
      this.selectedAll = false;
      this.$refs.table.clearSelection();
    },
    /**
     * 设置过滤菜单默认值
     */
    setFilterMenuDefaultValue() {
      this.filterMenuDefaultValues = this.filterMenus.reduce((maps, item) => {
        maps[item.fieldName] = item.value;
        return maps;
      }, {});
    },
    // 展示会员用户详情侧滑
    handleShowMemberDetail(name, rowData) {
      if (this.memberDetailFetching || !rowData.memberId) return;
      this.memberDetailFetching = true;
      http
        .getUserMarketingAccountByMemberId({
          memberId: rowData.memberId
        })
        .then(
          res => {
            this.memberDetailFetching = false;
            if (res.errCode) return FxUI.Message.error($t('marketing.commons.hqxqxxsb_35fd2d'));
            this.memberDetailData = res.data;
            this.memberDetailVisible = true;
          },
          () => FxUI.Message.error($t('marketing.commons.hqxqxxsb_35fd2d'))
        );
    },
    handleClueDetail() {},
    handleDelete(){
      const campaignIds = []
      console.log(this.selectedItems,'this.handleDeletehandleDelete')
      this.selectedItems.forEach(item=>{
        if(item && item.campaignId){
          campaignIds.push(item.campaignId)
        }
      })
      console.log(campaignIds,'选中的id')
      FxUI.MessageBox.confirm($t('marketing.commons.qrscdqxztj_5bc3c3'), $t('marketing.commons.ts_02d981'), {
        confirmButtonText: $t('marketing.commons.qd_38cf16'),
        cancelButtonText: $t('marketing.commons.qx_625fb2'),
        type: 'warning',
      }).then(async () => {
        const res = await http.deleteCampaignEnrollData({campaignIds})
        if(res && res.errCode === 0){
          this.clearSelect()
          FxUI.Message.success($t('marketing.commons.sccg_0007d1'))
          this.queryLists()
        } else {
          FxUI.Message.success($t('marketing.pages.setting.scsbqzs_89976e'))
        }
      }).catch((err)=>{
        console.log('err: ', err);
      })
    },
  },
  created() {
    this.queryColumns();
    this.createOperationButton();
    this.createFilterButton();
    this.setFilterMenuDefaultValue();
  },
  mounted(){
    this.queryLists();
    this.getWalletStatus();
  }
};
