<template>
  <div class="active-member__header">
    <div class="header__left">
      <div class="active-member__title">{{ title }}</div>
    </div>
    <div class="header__content">
      <slot
        ><HeaderContent
          ref="HeaderContent"
          :tableSource="tableSource"
          :marketingEventId="marketingEventId"
          :sourceId="sourceId"
          @action="handleHeaderContentAction"
      /></slot>
    </div>
    <div class="header__right">
      <div class="active-member__seach">
        <fx-input
          class="search-input"
          type="input"
          size="small"
          :placeholder="$t('marketing.commons.ssmchsjh_dd8006')"
          v-model="keyword"
          @change="handleKeywordChange"
        ></fx-input>
      </div>
      <VFilter
        ref="Filter"
        class="active-member__filter"
        v-model="filterParams"
        :filterMenus.sync="filterMenus"
        @filter-submit="filterSubmit"
        @filter-change="filterChange"
        v-if="showFilter"
      ></VFilter>
      <div class="active-member__line"></div>
      <div class="active-member__add" v-if="buttons.length">
        <ElButton
          v-for="(item, i) in buttons"
          type="default"
          size="small"
          id="guide-active-member__add"
          @click="handleOuterButtonClick(item.func, i)"
          :loading="buttonLoadingArray[i] || false"
          :key="i"
        >
          {{ item.name }}
        </ElButton>
      </div>
      <div class="active-member__more" v-if="moreButtons.length">
        <ElDropdown @command="(func) => handleButtonClick(func)">
          <ElButton size="small" class="el-icon-more"></ElButton>
          <ElDropdownMenu slot="dropdown">
            <ElDropdownItem style="display: flex;align-items:center;" v-for="(item, i) in moreButtons" :key="i" :command="item.func">
              {{
              item.name
            }}
            <questionTooltip
              effect="dark"
              :offset="192"
              v-if="tableSource === 'meeting' && item.showTooltip"
            >
              <span
                slot="question-content"
              >
                {{ item.tooltip }}
              </span>
            </questionTooltip>
            </ElDropdownItem>
          </ElDropdownMenu>
        </ElDropdown>
      </div>
    </div>
  </div>
</template>

<script>
import HeaderContent from './HeaderContent';
import VFilter from './Filter';
import questionTooltip from '@/components/questionTooltip';
export default {
  components: {
ElButton: FxUI.Button,
ElDropdown: FxUI.Dropdown,
ElDropdownItem: FxUI.DropdownItem,
ElDropdownMenu: FxUI.DropdownMenu,
HeaderContent,
VFilter,
questionTooltip
},
  props: {
    tableSource: {
      type: String,
      default: '',
    },
    marketingEventId: {
      type: String,
      default: '',
    },
    showFilter: {
      type: Boolean,
      default: true,
    },
    title: {
      type: String,
      default: '',
    },
    searchKeyword: {
      type: String,
      default: '',
    },
    operationButtons: {
      type: Array,
      default: () => [],
    },
    filterParams: {
      type: Object,
      default: () => ({}),
    },
    filterMenus: {
      type: Array,
      default: () => [],
    },
    sourceId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      keyword: this.searchKeyword,
      buttonLoadingArray: [],
    };
  },
  computed: {
    buttons() {
      console.log(this.operationButtons,'this.',this.tableSource)
      return this.operationButtons.slice(0, 1) || [];
    },
    moreButtons() {
      return this.operationButtons.slice(1) || [];
    },
  },
  watch: {
    searchKeyword(val) {
      this.keyword = val;
    },
  },
  methods: {
    refreshHeaderData() {
      this.$refs.HeaderContent.initFilterList();
    },
    filterSubmit(val) {
      this.$emit('filter-submit', val);
    },
    filterChange(val) {
      this.$emit('filter-change', val);
    },
    handleKeywordChange(val) {
      this.$emit('update:searchKeyword', val);
      this.handleButtonClick('queryLists', {
        pageNum: 1,
        ...(val ? { keyword: val } : {}),
      });
    },
    executionButtonMethod(func, params = {}, callback) {
      if (typeof func === 'function') {
        func(params || {}, callback);
      } else {
        this.$emit('eventTap', func, params || {}, callback);
      }
    },
    handleOuterButtonClick(func, i) {
      this.$set(this.buttonLoadingArray, i, true);
      this.executionButtonMethod(func, {}, () => {
        this.$set(this.buttonLoadingArray, i, false);
      });
    },
    handleButtonClick(func, params) {
      this.executionButtonMethod(func, params, () => {});
    },
    handleHeaderContentAction(action, params) {
      console.log('handleHeaderContentAction', action, params);
      if (action === 'fastfilter') {
        // this.$refs.Filter.handleClear();
        // this.filterChange(params);
        const [key] = Object.keys(params);
        const value = params[key];
        const filterMenuSelects = JSON.parse(JSON.stringify(this.$refs.Filter.filterMenuSelects));
        filterMenuSelects.forEach((item) => (item.value = []));
        filterMenuSelects.filter((item) => item.fieldName === key)[0].value = value;
        this.$refs.Filter.filterMenuSelects = filterMenuSelects;
        this.$refs.Filter.handleSubmit();
      }
    },
  },
  mounted() {
  },
};
</script>

<style lang="less" scoped>
.active-member__header {
  display: flex;
  justify-content: space-between;
  padding: 10px 20px;
  align-items: center;
  border-bottom: 1px solid #e9edf5;
  .header__left {
    display: flex;
  }
  .header__content {
    flex: 1;
  }
  .header__right {
    display: flex;
    align-items: center;
  }
  .active-member__title {
    font-size: 14px;
    color: #181c25;
    // font-weight: 600;
    display: flex;
    align-items: center;
  }
  .active-member__data {
    display: flex;
    margin-left: 30px;
    .data__item {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 32px;
      box-sizing: border-box;
      border-radius: 32px;
      color: #fff;
      font-size: 13px;
      background: #91959e;
      margin-right: 10px;
      padding: 0 18px;
      cursor: pointer;
      &.err {
        background: #ff405d;
      }
      &.selected {
        background: #545861;
      }
    }
  }
  .active-member__seach {
    margin-left: 10px;
  }
  .active-member__filter {
    margin-left: 10px;
  }
  .active-member__line {
    height: 30px;
    border-right: 1px solid #e9edf5;
    margin-left: 10px;
  }
  .active-member__add {
    margin-left: 10px;
    .iconfont {
      color: #b4b6c0;
      margin-right: 5px;
      font-size: 14px;
    }
  }
  .active-member__more {
    margin-left: 10px;
  }
}
</style>
