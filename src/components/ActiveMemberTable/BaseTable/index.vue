<template>
  <div class="active-member__wrapper" v-show="vShow">
    <div class="active-member__body">
      <div class="active-member__table" v-loading="loading">
        <Header
          :title="title"
          :searchKeyword.sync="searchKeyword"
          :operationButtons="operationButtons"
          @eventTap="bindHeaderTriggerEvent"
          @filter-submit="filterSubmit"
          @filter-change="filterChange"
          :filterParams="filterParams"
          :filterMenus.sync="filterMenus"
          :showFilter="showFilter"
          :tableSource="tableSource"
          :marketingEventId="marketingEventId"
          :sourceId="sourceId"
          ref="tableHeader"
          ><slot name="header-content"></slot
        ></Header>
        <div class="tags__operation" v-if="!!filterSelectedList.length">
          <div class="tags__area">
            <div
              class="tags__item"
              v-for="item, index in filterSelectedList"
              :key="item.fieldName"
            >
              <div class="item__text">{{ item.text }}</div>
              <div class="item__close" @click="handleTagItemClose(index)">
                <i class="el-icon-close"></i>
              </div>
            </div>
          </div>
          <ElButton class="tags__clear" type="text" @click="handleTagsClear">{{ $t('marketing.components.ActiveMemberTable.qkbq_896579') }}</ElButton>
        </div>
        <div class="table__operation" v-if="tableName != 'fast-service-table'">
          <div class="operation__selection">{{ $t('marketing.commons.yxzr_5b4907', {data: ({'option0': selectedCount})}) }}<template
              v-if="
                pageNavigationData.totalCount > pageNavigationData.pageSize &&
                  selectedCount === pageNavigationData.pageSize && tableSource != 'website-clue'
              "
            >
              ，<span @click="selectAll"
                >{{ $t('marketing.commons.xzqbr_ebd230', {data: ({'option0': pageNavigationData.totalCount})}) }}</span
              >
            </template>
            <template v-if="selectedAll">
              ，<span class="active-member__unselected" @click="clearSelect"
                >{{ $t('marketing.components.ActiveMemberTable.qxqx_a04144') }}</span
              >
            </template>
          </div>
          <div class="operation__buttons">
            <ElButton
              v-for="(item, i) in filterButtons"
              size="mini"
              :id="item.id"
              @click="item.func"
              :key="i"
              :disabled="selectedCount == 0"
              >{{ item.name }}</ElButton
            >
            <slot :disabled="selectedCount == 0" :selectedItems="selectedItems"></slot>
          </div>
          <!-- <div class="operation__close" @click="clearSelect">
            <i class="iconfont">&#xe642;</i>
          </div> -->
        </div>
        <!-- custom:cule-action事件是官网线索展示使用 -->
        <v-table
          ref="table"
          @selection-change="selectionChange"
          :columns="columns"
          :emptyText="$t('marketing.commons.zwsj_21efd8')"
          :data="lists"
          :settable="true"
          :tid="tableName"
          enableCustomColumn
          :row-style="{ cursor: 'pointer' }"
          :row-key="getRowKeys"
          @refreshTable="queryLists"
          @custom:campaign-action="showCampaign"
          @custom:clue-action="showClue"
          @custom:cule-action="handleClueDetail"
          @custom:formData-action="showFormData"
          @click:text="handleShowMemberDetail"
        ></v-table>
        <v-pagen
          @change="pageChange"
          :pagedata.sync="pageNavigationData"
          style="padding-left: 0"
        ></v-pagen>
        <!-- 通知提醒 -->
        <SpreadDialog
          v-if="isShowNoticeDialog"
          :title="$t('marketing.components.ActiveMemberTable.xxtz_d1d4c3')"
          :visible.sync="isShowNoticeDialog"
          :submitBtnText="$t('marketing.commons.qrfs_e8dc70')"
          :scene="noticeInfo.scene"
          :marketingEventId="marketingEventId"
          :recipients="selectedItems"
          :material="materialInfo"
          @sendCompleted="sendNoticeHandler"
          :sceneType="noticeInfo.sceneType"
        ></SpreadDialog>
        <!-- 重新存入crm线索 -->
        <CrmResaveDialog
          :visible.sync="isShowCrmResaveDialog"
          :selectedItems="selectedItems"
          @participantsResave="saveResaveHandler"
          :tableSource="tableSource"
          :text="crmResaveText"
        ></CrmResaveDialog>
        <!-- 查看表单详情 -->
        <!-- v-if="isShowFormDialog" -->
        <FormDialog
          v-if="isShowFormDialog"
          :visible.sync="isShowFormDialog"
          :campaignId="selectedCampaignId"
        ></FormDialog>
        <UserDetail
          :userId="memberDetailData"
          v-if="memberDetailVisible"
          :visible="memberDetailVisible"
          @close="memberDetailVisible = false"
        />
      </div>
    </div>
    <ExtendTemplate />
  </div>
</template>

<script>
import VTable from "@/components/table-ex";
import VPagen from "@/components/kitty/pagen";
import SpreadDialog from "@/components/SpreadDialog";
import UserDetail from "@/pages/user/detail";
import Header from "./Header";
import Empty from "./Empty";
import VFilter from "./Filter";
import CrmResaveDialog from "./CrmResaveDialog";
import FormDialog from "./FormDialog";
import ExtendTemplate from "./ExtendTemplate";
import http from "@/services/http";
import util from "@/services/util";
import tableTools from "./table-tools";
import mixins from "./mixins";
export default {
  mixins: [tableTools, mixins],
  components: {
ElButton: FxUI.Button,
ElLink: FxUI.Link,
ElDialog: FxUI.Dialog,
VTable,
VPagen,
Header,
Empty,
VFilter,
CrmResaveDialog,
SpreadDialog,
FormDialog,
UserDetail,
ExtendTemplate
},
  props: {
    showFilter: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: ""
    },
    marketingEventId: {
      type: String,
      default: ""
    },
    materialInfo: {
      type: Object,
      default: () => ({})
    },
  },
  data() {
    return {
      vShow: true,
      tableName: 'active-member-table',
    };
  },
  methods: {
    /**
     * 获取列表数据方法
     */
    async getDatas() {
      return {
        result: []
      };
    },
    /**
     * 提供扩展列参数做处理方法
     */
    parseExtensionData(data) {
      return data;
    },
    /**
     * 添加扩展列方法
     */
    addExtensionColumns(columns) {
      return columns;
    },
    /**
     * 排序列方法
     */
    sortColumns(columns) {
      return columns;
    },
    exportListDataHandler(params, callback) {
      this.exportListData(params).then(() => {
        callback();
      });
    },
    /**
     * 导出列表数据方法
     */
    exportListData(opts) {
      return new Promise(resolve => {
        util.exportoFile(opts, () => {
          resolve();
        });
      });
    },
    /**
     * 设置操作按钮方法
     */
    setOperationButton(buttons) {
      return buttons;
    },
    /**
     * 设置过滤导航操作按钮方法
     */
    setFilterButton(buttons) {
      return buttons;
    },
    commandHandler(commandName) {
      return commandName;
    }
  },
  created() {
    console.log("i am BaseTable Created");
  }
};
</script>

<style lang="less" scoped>
.active-member__wrapper {
  flex: 1;
  width: 100%;
  background: #fff;
  border-radius: 2px;
  box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  .active-member__body {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  .active-member__table {
    flex: 1;
    display: flex;
    flex-direction: column;
    .table__operation {
      padding: 12px 22px;
      display: flex;
      border-bottom: 1px solid #e9edf5;
      .operation__selection {
        display: flex;
        align-items: center;
        font-size: 13px;
        margin-right: 16px;
        span {
          cursor: pointer;
          font-size: 12px;
          color: @color-link;
        }
      }
      .operation__close {
        margin-left: auto;
        margin-right: 0px;
        color: #999;
        display: flex;
        align-items: center;
        cursor: pointer;
        .iconfont {
          font-size: 12px;
          font-weight: 600;
          color: #999;
        }
        &:hover {
          .iconfont {
            color: #656464;
          }
        }
      }
    }
    .tags__operation {
      padding: 12px 16px;
      display: flex;
      align-items: center;
      border-bottom: 1px solid #e9edf5;

      .tags__area {
        flex: 1;
        display: flex;
      }

      .tags__item {
        background: #F2F3F5;
        height: 28px;
        display: flex;
        align-items: center;
        padding: 0 5px 0 9px;
        .item__text {
          font-size: 12px;
          color: #181C25;
        }
        .item__close {
          cursor: pointer;
          i {
            font-size: 12px;
            margin-left: 6px;
            color: #9c9c9c;
          }
        }
      }

      .tags__item + .tags__item {
        margin-left: 10px;
      }

      .tags__clear {
        margin-left: 16px;
        font-size: 12px;
        padding: 0;
      }
    }
  }
}
</style>
