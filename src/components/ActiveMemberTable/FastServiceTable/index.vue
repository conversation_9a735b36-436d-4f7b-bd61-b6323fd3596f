<script>
import BaseTable from "../BaseTable";
import http from "@/services/http/index";
import _ from "lodash";
import util from "@/services/util/index";
import { eFieldPush } from "@/utils/custom-form-tools";
import { formatStringByEmpty } from "@/utils";
const formatter = (rowData, column, cellValue) =>
  formatStringByEmpty(cellValue);
import mixins from './mixins'
import kisvData from "@/modules/kisv-data";


/**
 * 查询渠道类型 0 市场活动 1 营销活动 2 官网来源
 */
const SOURCE_TYPE_WEBSITE = 2;

export default {
  extends: BaseTable,
  mixins: [mixins],
  props: {
    /**
     * 官网线索使用
     */
    sourceId: {
      type: String,
      default: ""
    },
  },
  data() {
    return {
      tableSource:"fast-service",
      tableName: 'fast-service-table',
      queryType: 1,
      vShow: false,
      vDatas: kisvData.datas,
    };
  },
  methods: {
    async getDatas(params) {
      const res = await http.getOfficialWebsiteLeads({
        ...params
      });
      if((!this.vDatas.uinfo['showCustomerServiceEnabled'] && !this.vDatas.uinfo['marketingCustomerIntegration'])) {
        this.vShow = false
        return
      } else {
        this.vShow = true
      }
      return res.data;
    },
    handleClueDetail(row) {
      if (row.save_crm_status === 0 || row.save_crm_status === 3) {
        if(!row.otherCrmObjectBind) return;
        CRM.api.show_detail({
          apiname: row.otherCrmObjectBind.apiName,
          id: row.otherCrmObjectBind.objectId
        });
      }
    },
    exportListDataHandler(params, callback) {
      const query = {
        pageNum:1,
        pageSize:99999
      };
      const opts = {
        action: "exportOnlineServiceData",
        params:query
      };
      this.exportListData(opts).then(() => {
        callback();
      });
    },
  }
};
</script>
