<script>
import BaseTable from "../BaseTable";
import http from "@/services/http/index";
import _ from "lodash";
import util from "@/services/util/index";
import { eFieldPush } from "@/utils/custom-form-tools";
import { formatStringByEmpty } from "@/utils";
const formatter = (rowData, column, cellValue) =>
  formatStringByEmpty(cellValue);

/**
 * 查询渠道类型 0 市场活动 1 营销活动 2 官网来源
 */
const SOURCE_TYPE_WEBSITE = 2;

export default {
  extends: BaseTable,
  props: {
    /**
     * 官网线索使用
     */
    sourceId: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      tableSource: "website-clue",
      queryType: 1,
      tableName: "website-clue-table",
      filterMenus: [
        {
          value: null,
          fieldName: "saveCrmStatus",
          fieldLabel: $t('marketing.commons.crxszt_a3195f'),
          options: [
            {
              value: null,
              label: $t('marketing.commons.qb_a8b0c2')
            },
            {
              value: 0,
              label: $t('marketing.commons.ycr_0844f1')
            },
            {
              value: 99,
              label: $t('marketing.commons.dcl_047109')
            }
          ]
        },
        {
          value: null,
          fieldName: "marketingSourceName",
          fieldLabel: $t('marketing.commons.xslyqd_954733'),
          options: [
            {
              value: "baidu",
              label: $t('marketing.commons.bd_a3f4a5')
            },
            {
              value: "sogou",
              label: $t('marketing.commons.sg_10d247')
            },
            {
              value: "so360",
              label: $t('marketing.commons.ss_56f1c8')
            },
            {
              value: "bing",
              label: $t('marketing.commons.by_343825')
            },
            {
              value: "sm",
              label: $t('marketing.commons.sm_9e5ea3')
            },
            {
              value: "toutiao",
              label: $t('marketing.commons.jrtt_b3b3e9')
            },
            {
              value: "tx_news",
              label: $t('marketing.commons.txxw_0469c2')
            },
            {
              value: "sohu_news",
              label: $t('marketing.commons.shxw_006167')
            },
            {
              value: "ifeng",
              label: $t('marketing.commons.fhw_c1ba4a')
            },
            {
              value: "news_163",
              label: $t('marketing.commons.wyxw_c217f2')
            },
            {
              value: "wechat",
              label: $t('marketing.commons.wx_cfbf6f')
            },
            {
              value: "weibo",
              label: $t('marketing.commons.wb_fb050d')
            },
            {
              value: "zhihu",
              label: $t('marketing.commons.zh_d2fca5')
            },
            {
              value: "other",
              label: $t('marketing.commons.qt_5a0afc')
            }
          ]
        }
      ],
      filterButtons: [
        {
          name: $t('marketing.commons.zxcrxs_f70585'),
          func: this.saveResave,
        },
      ],
      lockedColumns: false
    };
  },
  methods: {
    async getDatas(params) {
      const { errCode, data = {} } = await http.queryMultipleFormUserData({
        sourceType: SOURCE_TYPE_WEBSITE,
        sourceId: this.sourceId,
        ...params
      });
      console.log('getDatas', data)
      if(errCode === 0){
        if(!this.lockedColumns) {
          console.log('getDatas 2', data)
          this.columns = this.getColumns(data);
          this.lockedColumns = true;
        }
        return data
      }
      return {};
    },
    exportListDataHandler(params, callback) {
      const opts = {
        action: "exportLiveEnrollList",
        params: {
          marketingEventId: this.marketingEventId
        }
      };
      this.exportListData(opts).then(() => {
        callback();
      });
    },
    getColumns(data) {
      console.log("有没有到这个里", data);
      const columns = [];
      // 表头 - 来自form的
      columns.push({
        minWidth: 55,
        prop: "multi-select",
        label: $t('marketing.commons.dx_443f46'),
        unsettable: true,
        reserveSelection: true,
        exComponent: "selection"
      });
      data &&
        data.otherData &&
        data.otherData.forEach(item => {
          const column = {
            prop: item.apiName,
            label: item.label,
            minWidth: "100px",
            formatter
          };
          if (item.type === "image") {
            column.exComponent = "thumbnail";
          }
          columns.push(column);
        });
      columns.push({
        prop: "createTime",
        label: $t('marketing.commons.tjsj_fb4cc0'),
        minWidth: "100px",
        formatter
      });
      columns.push({
        prop: "utmCampaig",
        label: "utm_campaign",
        minWidth: "100px",
        formatter
      });
      columns.push({
        prop: "utmContent",
        label: "utm_content",
        minWidth: "100px",
        formatter
      });
      columns.push({
        prop: "utmMedium",
        label: "utm_medium",
        minWidth: "100px",
        formatter
      });
      columns.push({
        prop: "utmSource",
        label: "utm_source",
        minWidth: "100px",
        formatter
      });
      columns.push({
        prop: "utmTerm",
        label: "utm_term",
        minWidth: "100px",
        formatter
      });
      columns.push({
        prop: "marketingSourceType",
        label: $t('marketing.commons.qdlx_474684'),
        minWidth: "100px",
        formatter
      });
      columns.push({
        prop: "marketingSourceName",
        label: $t('marketing.commons.fwly_968ee5'),
        minWidth: "100px",
        formatter
      });
      columns.push({
        prop: "objectName",
        label: $t('marketing.commons.tgnr_a6ec90'),
        minWidth: "100px",
        formatter
      });
      columns.push({
        prop: "saveCrm",
        label: $t('marketing.commons.crdxzt_65f744'),
        exComponent: "errState",
        minWidth: "100px",
        formatter
      });
      columns.push({
        prop: "relevantCrm",
        label: $t('marketing.commons.gldxxs_8e0b05'),
        exComponent: "operation",
        minWidth: "100px",
        formatter
      });
      // this.lockedColumns = true;
      return columns;
    },
    parseData(data) {
      const datas = [];
      const list = _.cloneDeep(data.result);
      list.forEach(item => {
        const submitContent = item.submitContent;
        const submitContentMap = item.submitContentMap;
        delete item.submitContent;
        delete item.submitContentMap;
        let _pushItem = Object.assign(submitContent, submitContentMap, item);
        _pushItem.createTime = util.formatDateTime(
          _pushItem.createTime,
          "YYYY-MM-DD hh:mm"
        );
        if (_pushItem.saveCrmStatus == 2) {
          _pushItem.saveCrm = {
            showSaveCrm: true
          };
        } else if (_pushItem.saveCrmStatus == 3) {
          _pushItem.saveCrm = {
            errs: false,
            text: $t('marketing.commons.ygl_998d15'),
            tips: _pushItem.saveCrmErrorMessage
          };
        } else {
          _pushItem.saveCrm = {
            errs: _pushItem.saveCrmStatus === 1,
            text:
              _pushItem.saveCrmStatus === null
                ? "--"
                : _pushItem.saveCrmStatus === 0
                ? $t('marketing.commons.ycr_0844f1')
                : $t('marketing.commons.crsb_459d3d'),
            tips: _pushItem.saveCrmErrorMessage
          };
        }
        _pushItem.relevantCrm = [
          {
            id: "cule",
            name:
              _pushItem.saveCrmStatus === 0 || _pushItem.saveCrmStatus === 3
                ? (_pushItem.name || "") +
                  "(" +
                  util.getCrmObjectNameByApiname(
                    _pushItem.otherCrmObjectBind && _pushItem.otherCrmObjectBind.apiName
                  ) +
                  ")"
                : "--"
          }
        ];
        // 处理可扩展字段的展示
        eFieldPush(_pushItem, data);

        _pushItem.utmTerm = submitContent.utmTerm;
        _pushItem.utmCampaig = submitContent.utmCampaig;
        _pushItem.utmContent = submitContent.utmContent;
        _pushItem.utmMedium = submitContent.utmMedium;
        _pushItem.utmSource = submitContent.utmSource;
        _pushItem.marketingSourceName = submitContent.marketingSourceName;
        _pushItem.marketingSourceType = submitContent.marketingSourceType;
        datas.push(_pushItem);
      });

      return datas;
    },
    handleClueDetail(row) {
      if (row.saveCrmStatus === 0 || row.saveCrmStatus === 3) {
        if(!row.otherCrmObjectBind) return;
        CRM.api.show_detail({
          apiname: row.otherCrmObjectBind.apiName,
          id: row.otherCrmObjectBind.objectId
        });
      }
    },
    exportListDataHandler(params, callback) {
      const query = {
        sourceId: this.sourceId,
        sourceType: SOURCE_TYPE_WEBSITE
      };
      const opts = {
        action: "exportMultipleFormEnrollsData",
        params: query
      };
      this.exportListData(opts).then(() => {
        callback();
      });
    }
  }
};
</script>
