import articleIcon from '@/assets/images/material-icon/article.png'
import productIcon from '@/assets/images/material-icon/product.png'
import cardIcon from '@/assets/images/material-icon/card.png'
import imageIcon from '@/assets/images/material-icon/image.png'
import externalIcon from '@/assets/images/material-icon/external.png'
import siteIcon from '@/assets/images/material-icon/site.png'
import fileIcon from '@/assets/images/material-icon/file.png'
import videoIcon from '@/assets/images/material-icon/video.png'

import { createMpMaterialUrl, createMaterialDetailUrlByObjectType, getVideoBaseOrigin } from "@/utils/createMaterialDetailUrl";

import _ from 'lodash'

export const materialTypeEnum = {
  article: 1,
  image: 2,
  conference: 3,
  product: 4,
  file: 500,
  video: 501,
  card: 6,
  site: 10,
  poster: 24,
  external: 8888, // 只用做前端区分类型
}

export const ObjectTypeToMaterial = {
  6: materialTypeEnum.article,
  2: materialTypeEnum.image,
  13: materialTypeEnum.conference,
  4: materialTypeEnum.product,
  26: materialTypeEnum.site,
  24: materialTypeEnum.poster,
}

export const selectorDialogEnum = {
  [materialTypeEnum.article]: 'selectMaterialDialog',
  [materialTypeEnum.product]: 'selectMaterialDialog',
  [materialTypeEnum.site]: 'selectMaterialDialog',
  [materialTypeEnum.image]: 'selectMaterialDialog',
  [materialTypeEnum.poster]: 'selectMaterialDialog',
  [materialTypeEnum.conference]: 'selectMaterialDialog',
  [materialTypeEnum.video]: 'isShowVideoDialog',
  [materialTypeEnum.file]: 'isShowFileSelectorVisible',
}

export const materialLabelEnum = {
  [materialTypeEnum.article]: $t('marketing.commons.wz_c75625'),
  [materialTypeEnum.image]: $t('marketing.commons.tp_20def7'),
  [materialTypeEnum.poster]: $t('marketing.commons.hb_ff361e'),
  [materialTypeEnum.conference]: $t('marketing.commons.hy_ebcb81'),
  [materialTypeEnum.video]: $t('marketing.commons.sp_7fcf42'),
  [materialTypeEnum.product]: $t('marketing.commons.cp_a01543'),
  [materialTypeEnum.file]: $t('marketing.commons.wj_2a0c47'),
  [materialTypeEnum.card]: $t('marketing.commons.mp_758853'),
  [materialTypeEnum.site]: $t('marketing.commons.wym_5fd4fb'),
  [materialTypeEnum.external]: $t('marketing.commons.wbnr_6ecce8'),
}

export const materialTypes = [
  {
    icon: siteIcon,
    label: $t('marketing.commons.wym_5fd4fb'),
    materialType: materialTypeEnum.site,
    objectType: 26,
    selector: selectorDialogEnum[materialTypeEnum.site],
  },
  {
    icon: articleIcon,
    label: $t('marketing.commons.wz_c75625'),
    materialType: materialTypeEnum.article,
    objectType: 6,
    selector: selectorDialogEnum[materialTypeEnum.article],
  },
  {
    icon: productIcon,
    label: $t('marketing.commons.cp_a01543'),
    materialType: materialTypeEnum.product,
    objectType: 4,
    selector: selectorDialogEnum[materialTypeEnum.product],
  },
  {
    icon: imageIcon,
    label: $t('marketing.commons.tp_20def7'),
    materialType: materialTypeEnum.image,
    objectType: 2,
    selector: selectorDialogEnum[materialTypeEnum.image],
  },
  {
    icon: videoIcon,
    label: $t('marketing.commons.sp_7fcf42'),
    materialType: materialTypeEnum.video,
    objectType: 17,
    selector: selectorDialogEnum[materialTypeEnum.video],
  },
  {
    icon: fileIcon,
    label: $t('marketing.commons.wj_2a0c47'),
    materialType: materialTypeEnum.file,
    objectType: 8,
    selector: selectorDialogEnum[materialTypeEnum.file],
  },
  {
    icon: cardIcon,
    label: $t('marketing.commons.mp_758853'),
    materialType: materialTypeEnum.card,
    objectType: 1,
  },
  {
    icon: externalIcon,
    label: $t('marketing.commons.wbnr_6ecce8'),
    materialType: materialTypeEnum.external,
    objectType: 8888,
  },
  // {
  //   icon: articleIcon,
  //   label: '活动',
  //   materialType: 3,
  //   objectType: 13,
  //   selector: selectorDialogEnum.material,
  // },
]

export const attachTypeEnum = {
  link: 1,
  image: 2,
  video: 3,
  miniprogram: 4,
  file: 5,
  outLink: 6,
  outMiniProgram: 7,
}

export const attachTypeFieldEnum = {
  [attachTypeEnum.link]: 'link',
  [attachTypeEnum.outLink]: 'link',
  [attachTypeEnum.image]: 'image',
  [attachTypeEnum.video]: 'video',
  [attachTypeEnum.miniprogram]: 'miniprogram',
  [attachTypeEnum.outMiniProgram]: 'miniprogram',
  [attachTypeEnum.file]: 'file',
}
const { enterpriseAccount } = FS.contacts.getCurrentEmployee() || {}

export const getFilePreviewUrl = (file = {}) => {
  const { id, fileName, ext } = file
  const host = /localhost|127.0.0.1/.test(window.location.origin) ? 'https://crm.ceshi112.com' : window.location.origin
  return `${host}/proj/page/marketing-file?id=${id}&ea=${enterpriseAccount || ''}&name=${encodeURIComponent(fileName)}.${ext}&spreadOrigin=qywx&marketingActivityId=!!marketingActivityId!!&marketingEventId=!!marketingEventId!!`
}

export const getVideoPreviewUrl = (video = {}) => {
  const { id, name } = video
  const host = getVideoBaseOrigin()
  return `${host}/proj/page/marketing-player?id=${id}&ea=${enterpriseAccount || ''}&name=${encodeURIComponent(name)}&spreadOrigin=qywx&marketingActivityId=!!marketingActivityId!!&marketingEventId=!!marketingEventId!!`
}


export const formatAttachToMedia = (attaches = []) => attaches.map(attach => {
  switch (attach.materialType) {
    case materialTypeEnum.image:
    case materialTypeEnum.poster:
      return {
        attachmentType: attachTypeEnum.image,
        image: {
          imagePath: attach.aPath,
          imageUrl: attach.image,
          materialId: attach.id,
          materialType: attach.materialType === materialTypeEnum.poster ? 5 : attach.materialType,
          name: attach.title,
          size: attach.size,
        },
      }
    case materialTypeEnum.video:
      return {
        attachmentType: attachTypeEnum.video,
        video: {
          videoId: attach.id,
          videoName: attach.name,
          videoUrl: getVideoPreviewUrl(attach),
          videoCoverUrl: attach.cover,
        },
      }
    case materialTypeEnum.file:
      return {
        attachmentType: attachTypeEnum.file,
        file: {
          fileId: attach.id,
          fileName: attach.fileName,
          ext: attach.ext,
          fileUrl: getFilePreviewUrl(attach),
        },
      }
    case materialTypeEnum.card:
      return {
        attachmentType: attachTypeEnum.miniprogram,
        miniprogram: {
          miniProgramType: 3,
          page: `pkgs/pkg-card/pages/detail/card-detail?byshare=1&ea=${enterpriseAccount || ''}`,
        },
      }
    case materialTypeEnum.external:
      if (attach.media === attachTypeEnum.outLink) {
        return {
          attachmentType: attachTypeEnum.outLink,
          link: {
            title: attach.h5Title,
            picPath: attach.coverPath,
            picUrl: attach.image,
            url: attach.url,
            linkType: 2,
            appendMktParam: attach.appendMktParam,
          },
        }
      }
      return {
        attachmentType: attachTypeEnum.outMiniProgram,
        miniprogram: {
          title: attach.title,
          picPath: attach.coverPath,
          picUrl: attach.image,
          page: attach.path,
          appId: attach.appId,
          miniProgramType: 2,
          appendMktParam: attach.appendMktParam,
        },
      }
    default:
      if (attach.media === attachTypeEnum.link) {
        return {
          attachmentType: attachTypeEnum.link,
          link: {
            title: attach.title,
            picPath: attach.coverPath || attach.aPath,
            picUrl: attach.image,
            desc: attach.summary || attach.shareDesc || attach.desc,
            url: createMaterialDetailUrlByObjectType(attach.objectType, {
              id: attach.id,
              marketingActivityId: '!!marketingActivityId!!',
              marketingEventId: '!!marketingEventId!!',
              wxAppId: '!!wxAppId!!',
              byshare: 1,
            }).url,
            page: createMpMaterialUrl(attach.id, attach.objectType),
            materialId: attach.id,
            materialType: attach.materialType,
            objectType: attach.objectType,
            objectName: attach.name,
            linkType: 1,
          },
        }
      }
      return {
        attachmentType: attachTypeEnum.miniprogram,
        miniprogram: {
          title: attach.title,
          picPath: attach.coverPath || attach.aPath,
          picUrl: attach.image,
          desc: attach.summary || attach.shareDesc || attach.desc,
          url: createMaterialDetailUrlByObjectType(attach.objectType, {
            id: attach.id,
            marketingActivityId: '!!marketingActivityId!!',
            marketingEventId: '!!marketingEventId!!',
            wxAppId: '!!wxAppId!!',
            byshare: 1,
          }).url,
          page: createMpMaterialUrl(attach.id, attach.objectType),
          materialId: attach.id,
          materialType: attach.materialType,
          objectType: attach.objectType,
          objectName: attach.name,
          miniProgramType: 1,
        },
      }
  }
})

export const formatMediaToAttach = (medias = []) => medias.map(media => {
  const _media = media[attachTypeFieldEnum[media.attachmentType]] || {}
  let ret = {}
  switch (media.attachmentType) {
    case attachTypeEnum.image:
      ret = {
        aPath: _media.imagePath,
        image: _media.imageUrl,
        id: _media.materialId,
        materialType: _media.materialType === 5 ? materialTypeEnum.poster : _media.materialType,
        title: _media.name,
        size: _media.size,
      }
      break
    case attachTypeEnum.video:
      ret = {
        id: _media.videoId,
        name: _media.videoName,
        url: _media.videoUrl,
        cover: _media.videoCoverUrl,
        materialType: materialTypeEnum.video,
      }
      break
    case attachTypeEnum.file:
      ret = {
        id: _media.fileId,
        fileName: _media.fileName,
        ext: _media.ext,
        url: _media.fileUrl,
        materialType: materialTypeEnum.file,
      }
      break
    case attachTypeEnum.link:
      if (!_media.materialId) { // 判断如果没有物料id就走外部链接，兼容旧数据
        ret = {
          h5Title: _media.title,
          coverPath: _media.picPath,
          image: _media.picUrl,
          url: _media.url,
          linkType: 2,
          materialType: materialTypeEnum.external,
          media: attachTypeEnum.outLink,
        }
      } else {
        ret = {
          title: _media.title,
          aPath: _media.picPath,
          image: _media.picUrl,
          desc: _media.desc,
          summary: _media.desc,
          shareDesc: _media.desc,
          mpUrl: _media.page,
          url: _media.url,
          id: _media.materialId,
          materialType: _media.materialType,
          objectType: _media.objectType,
          name: _media.objectName,
          linkType: 1,
        }
      }
      break
    case attachTypeEnum.outLink:
      ret = {
        h5Title: _media.title,
        coverPath: _media.picPath,
        image: _media.picUrl,
        url: _media.url,
        appendMktParam: _media.appendMktParam,
        linkType: 2,
        materialType: materialTypeEnum.external,
      }
      break
    case attachTypeEnum.miniprogram:
      if (_media.miniProgramType === 3) {
        ret = {
          miniProgramType: 3,
          page: _media.page,
          materialType: materialTypeEnum.card,
        }
      } else if (_media.miniProgramType === 2) { // 判断如果没有物料id就走外部小程序，兼容旧数据
        ret = {
          title: _media.title,
          coverPath: _media.picPath,
          image: _media.picUrl,
          path: _media.page,
          appId: _media.appId,
          miniProgramType: 2,
          materialType: materialTypeEnum.external,
          media: attachTypeEnum.outMiniProgram,
        }
      } else {
        ret = {
          title: _media.title,
          aPath: _media.picPath,
          image: _media.picUrl,
          desc: _media.desc,
          summary: _media.desc,
          shareDesc: _media.desc,
          url: _media.url,
          mpUrl: _media.page,
          id: _media.materialId,
          materialType: _media.materialType,
          objectType: _media.objectType,
          name: _media.objectName,
          miniProgramType: 1,
        }
      }
      break
    case attachTypeEnum.outMiniProgram:
      ret = {
        title: _media.title,
        coverPath: _media.picPath,
        image: _media.picUrl,
        path: _media.page,
        appId: _media.appId,
        appendMktParam: _media.appendMktParam,
        miniProgramType: 2,
        materialType: materialTypeEnum.external,
      }
      break
    default:
      break
  }
  return { media: media.attachmentType, ...ret }
})

export const filterMaterialMaps = (attaches = []) => {
  const _attaches = attaches.filter(attach => ([
    materialTypeEnum.article,
    materialTypeEnum.product,
    materialTypeEnum.site,
    // materialTypeEnum.poster,
    materialTypeEnum.file,
    materialTypeEnum.video,
    materialTypeEnum.conference,
  ].includes(attach.materialType))).map(attach => ({
    objectId: attach.id,
    contentType: attach.materialType === materialTypeEnum.poster ? 5 : attach.materialType,
  }))
  return _.uniqBy(_attaches, 'objectId')
}
