<template>
  <VDialog
    class="SpreadAllStaffDialog"
    :title="title"
    width="1080px"
    :visible="visible"
    append-to-body
    :cancel-text="$t('marketing.commons.qx_625fb2')"
    :loading="isSubmiting"
    @onSubmit="handleSubmit"
    @onClose="handleClose"
  >
    <div class="SpreadAllStaffDialog__body">
      <div
        v-if="showPreviewImg"
        class="poster__wrapper"
      >
        <!-- <img
          v-show="flag_isLongImage"
          :class="['icon_longimg']"
          :src="icon_longimg"
        > -->
        <span :class="'longposter-icon'" v-show="flag_isLongImage">{{ $t('marketing.commons.ct_ce9ecb') }}</span>
        <div
          :class="['qrposter__cover', flag_isLongImage && 'long-img']"
        >
          <div
            v-if="!posterUrl"
            class="cover__emptytips"
          >
            <!-- 自定义海报 -->
            <div class="emptytips__bg" />
          </div>
          <ElImage
            v-else
            :class="[
              'cover__image',
            ]"
            :src="posterUrl"
            :preview-src-list="[posterUrl]"
            :z-index="99999"
          />
        </div>
      </div>
      <SpreadAllStaffDialogMain
        ref="main"
        :default-marketing-event-id="marketingEventId"
        :material="data_materiel"
        @submited="handleSubmited"
      />
    </div>
  </VDialog>
</template>

<script>
import VDialog from '@/components/dialog'
import SpreadAllStaffDialogMain from './SpreadAllStaffDialogMain'
import icon_longimg from '@/assets/images/qrposter/icon_longimg.png'
import icon_longimg_en from '@/assets/images/qrposter/icon_longimg-en.png'

import { getImageByLang } from '@/utils/i18n.js'

export default {
  components: {
    VDialog,
    SpreadAllStaffDialogMain
  },
  props: {
    title: {
      type: String,
      default: $t('marketing.components.SpreadSideslipDialog.qytg_966c63'),
    },
    visible: {
      type: Boolean,
      default: false,
    },
    marketingEventId: {
      type: String,
      default() {
        return ''
      },
    },
    material: {
      type: Object,
      default() {
        return {}
      },
    },
    flag_isLongImage: {
      type: Boolean,
      default: false,
    },
    posterUrl: {
      type: String,
      defalut: '',
    },
    showPreviewImg: {
      type: Boolean,
      defalut: false,
    },
  },
  data() {
    return {
      isSubmiting: false,
      icon_longimg: getImageByLang([icon_longimg, icon_longimg_en]),
    }
  },
  computed: {
    data_materiel() {
      return {
        id: this.marketingEventId,
        contentType: this.material.type,
        contentId: this.material.id,
        siteType: this.material.siteType,
        conferenceId: this.material.conferenceId,
      }
    },
  },
  created() {
    console.log(this.posterUrl, 'posterUrlposterUrl', this.showPreviewImg, 'show[re', this.flag_isLongImage, 'flag_isLongImage')
  },
  /**
   * 调用父组件刷新方法
   */
  inject: {
    refresh: {
      default() {},
    },
  },
  methods: {
    handleSubmit() {
      this.$refs.main.handleSend(valid => {
        console.log(valid,"handleSendhandleSendhandleSend")
        // valid && (this.isSubmiting = true)
      })
    },
    handleSubmited() {
      this.isSubmiting = false;
      FxUI.Message.success($t('marketing.components.SpreadAllStaffDialog.qytgcgkyqw_24783a'));
      this.$emit('update:visible', false);
      if(typeof this.refresh === 'function'){
        this.refresh();
      }
    },
    handleClose() {
      this.$emit('update:visible', false)
    },
  },
}
</script>

<style lang="less" scoped>
.SpreadAllStaffDialog {
  /deep/ .el-dialog__body {
    padding: 0 16px 20px 0;
  }
  .SpreadAllStaffDialog__body{
    display: flex;
  }
  .SpreadDialog__form {
    flex: 1;
    /deep/ .el-form-item__label {
      font-size: 13px;
      color: #181C25;
    }
  }
  .poster__wrapper{
      position: relative;
      // width: 300px;
      height: 540px;
      display: flex;
      padding: 20px 20px 0 20px;
      overflow: hidden;
      background-color: #FAFAFA;
      .icon_longimg {
        position: absolute;
        width: 40px;
        z-index: 1;
      }
      .qrposter__cover {
        width: 250px;
        height: 550px;
        background: #FAFAFA;
        overflow: auto;
        box-sizing: border-box;
        background-size: cover;
        // box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.2);
        line-height: 0;
        &:not(.long-img) {
          display: flex;
          justify-content: center;
          align-items: center;
        }
        .cover__emptytips {
          color: #c1c5ce;
          font-size: 12px;
          .emptytips__bg {
            background-image: url('../../assets/images/defualt-pic.png');
            width: 70px;
            height: 57px;
            background-size: cover;
          }
        }
        .cover__image {
          width: 100%;
          box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.2);
        }
      }
  }
}
</style>
