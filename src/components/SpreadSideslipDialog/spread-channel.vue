<template>
    <div class="spread-channel">
      <div class="block">
        <div class="block__label">{{ $t('marketing.commons.tgqd_054ed9') }}</div>
        <div class="block__main">
          <ChannelSelector
            v-if="isShowSelector"
            @change="handleChannelChange"
            @changeText="handleChannelChangeText"
            @wechatSelected="handleWechatSelected"
            @wechatSelectedText="handleWechatSelectedText"
          />
          <div>
            <ElButton
              :disabled="isDisabled"
              class="button"
              type="default"
              size="mini"
              @click="handleGen"
              >{{ $t('marketing.components.SpreadSideslipDialog.scewmlj_4588f6') }}</ElButton
            >
          </div>
        </div>
      </div>
      <div class="record" v-if="this.tableData.length > 0">
        <div class="table">
          <FxTable :data="tableData">
            <FxTableColumn prop="channelLabel" :label="$t('marketing.components.SpreadSideslipDialog.ewmqd_ff5117')">
              <template v-slot="{ row }">
                <ElButton class="table-btn" type="text" @click="openQrDialog(row)">{{
                  row.channelLabel
                }}</ElButton>
              </template>
            </FxTableColumn>
            <FxTableColumn prop="createTime" :label="$t('marketing.components.SpreadSideslipDialog.scsj_b683c8')"></FxTableColumn>
            <FxTableColumn :label="$t('marketing.commons.cz_2b6bc0')">
              <template v-slot="{ $index }">
                <fx-button class="btn-del" type="text" @click="del($index)"
                  >{{ $t('marketing.commons.sc_2f4aad') }}</fx-button
                >
              </template>
            </FxTableColumn>
          </FxTable>
        </div>
      </div>

      <VDialog
        :visible="qrCodeVisible"
        :title="
          `${$t('marketing.commons.ewmylj_9c69e5')} - ` +
            (curDetail.id ? curDetail.channelLabel : channelTitle)
        "
        @onSubmit="handleSubmit"
        @onClose="handleClose(true)"
        :okText="$t('marketing.components.SpreadSideslipDialog.bcjl_c76ca0')"
        :cancelText="$t('marketing.commons.gb_b15d91')"
        :showConfirm="!curDetail.id"
      >
        <QrCode
          :channelValue="curChannelValue"
          :wxAppId="wxAppId"
          :material="material"
          :channelId="channelId"
          :marketingEventId="marketingEventId"
          :isHoxagonPreview="isHoxagonPreview"
          ref="qrCode"
          v-if="qrCodeVisible"
        ></QrCode>
      </VDialog>
    </div>
</template>

<script>
import ChannelSelector from "@/components/ChannelSelector";
import http from "@/services/http/index";
import VDialog from "@/components/dialog";
import QrCode from "./qr-code.vue";
import { confirm,alert } from "@/utils/globals";

export default {
  components: {
    ElButton: FxUI.Button,
    ChannelSelector,
    VDialog,
    QrCode,
    FxTable: FxUI.Table,
    FxTableColumn: FxUI.TableColumn
  },
  props: {
    marketingEventId: {
      type: String,
      default: ""
    },
    material: {
      type: Object,
      default() {
        return {};
      }
    },
    /**
     * 是否会议推广,暂时不用，不传给子组件
     */
    isConferenceForm: {
      type: Boolean,
      default: false
    },
    /**
     * 是否物料推广
     */
    isHoxagonPreview: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      tableData: [],
      qrCodeVisible: false,
      channelValue: "",
      channelLable: "",
      wxAppId: "",
      wxAppIdLabel: "",
      isShowSelector: true,
      channelId: "",
      disItems: [],
      existAppId: [],
      curDetail: {}
    };
  },
  computed: {
    isDisabled() {
      return !this.channelValue;
    },
    curChannelValue() {
      return this.channelValue;
    },
    channelTitle() {
      return `${this.channelLable}${
        this.wxAppId ? ":" + this.wxAppIdLabel : ""
      }`;
    }
  },
  watch: {
    material(val) {
      this.querySpreadChannelList();
    }
  },
  methods: {
    async del(index) {
      let isConfirm = await confirm($t('marketing.commons.qdsc_7eee52'));
      if (!isConfirm) return;
      let res = await http.deleteSpreadChannelInfo({
        id: this.tableData[index].id
      });
      if (res && res.errCode == 0) {
        FxUI.Message.success($t('marketing.commons.sccg_43593d'));
        this.tableData.splice(index, 1);
        this.getItemsAndAppIds(this.tableData);
      }
    },
    async querySpreadChannelList() {
      if (!this.material.id) return;
      let params = {
        objectId: this.material.id,
        objectType: this.material.objectType
      };
      /**
       * 活动营销，直播营销，会议营销marketingEventId
       * 小程序微站，公司官网，活动中心，企业内容hexagonSiteId
       */
      if (this.isHoxagonPreview) {
        params.hexagonSiteId = this.material.id;
      } else {
        params.marketingEventId = this.marketingEventId;
      }
      let res = await http.querySpreadChannelList(params);
      if (res && res.errCode === 0) {
        this.tableData = res.data || [];
        this.getItemsAndAppIds(this.tableData);
      }
    },
    getItemsAndAppIds(data) {
      let existAppId = [];
      let disItems = [];
      data.forEach(item => {
        if (item.channelValue === "offiaccount") {
          if (item.wxAppId) existAppId.push(item.wxAppId);
          disItems.push(
            `${item.channelValue}:${item.wxAppId ? item.wxAppId : ""}`
          );
        } else {
          disItems.push(item.channelValue);
        }
      });
      this.disItems = disItems;
      this.existAppId = existAppId;
    },
    handleChannelChange(value) {
      this.wxAppId = "";
      this.channelValue = value;
    },
    handleChannelChangeText(text) {
      this.channelLable = text;
    },
    handleWechatSelected(wxAppId) {
      this.wxAppId = wxAppId;
    },
    handleWechatSelectedText(text) {
      this.wxAppIdLabel = text;
    },
    handleClose(isCancel) {
      this.qrCodeVisible = false;
      /**
       * 保存后重置
       */
      if (!isCancel) {
        this.channelValue = "";
        this.channelLable = "";
        this.wxAppId = "";
        this.wxAppIdLabel = "";
        this.resetSelector();
      }
      /**
       * 查看详情关闭后
       */
      if (this.channelId) {
        this.channelId = "";
        this.curDetail = {};
        this.channelValue = "";
      }
    },
    resetSelector() {
      this.isShowSelector = false;
      setTimeout(() => {
        this.isShowSelector = true;
      }, 0);
    },
    handleSubmit() {
      this.$refs.qrCode.handleSave(() => {
        this.querySpreadChannelList();
        this.handleClose();
      });
    },
    openQrDialog(channel) {
      this.channelId = channel.id;
      this.channelValue = channel.channelValue;
      this.qrCodeVisible = true;
      this.curDetail = channel;
    },
    handleGen() {
      let channelValue;
      if (this.channelValue.includes("other")) {
        channelValue = decodeURI(this.channelValue.replace(/\+\+/g, "%"));
      } else if (this.channelValue === "offiaccount") {
        channelValue = `${this.channelValue}:${this.wxAppId}`;
      } else {
        channelValue = this.channelValue;
      }
      if (this.disItems.includes(channelValue)) {
        alert($t('marketing.components.SpreadSideslipDialog.gtgqddewmj_8a28a8'));
        return;
      }
      this.qrCodeVisible = true;
    }
  },
  created() {
    this.querySpreadChannelList();
  }
};
</script>

<style lang="less" scoped>
.spread-channel {
  margin-top: 15px;
  .block {
    display: flex;
    .block__label {
      margin-right: 18px;
      line-height: 32px;
      color: #181c25;
      font-size: 14px;
    }
    .block__main {
      display: flex;
      .button{
        margin-left: 10px;
        height: 34px;
      }
    }
  }
}
.record {
  margin-top: 10px;
  .table {
    width: 660px;
    border-top: 1px solid #e9edf5;
    border-left: 1px solid #e9edf5;
    border-right: 1px solid #e9edf5;
    .table-btn{
      padding: 0;
      font-size: 12px;
      color: var(--color-info06,#0c6cff);
    }
    .btn-del {
      padding: 0;
      font-size: 12px;
      color: var(--color-info06,#0c6cff);
    }
    /deep/ .el-table__cell{
      font-size: 12px;
      padding: 7px 8px;
    }
  }
}
</style>
