<template>
    <sideslip-popup
      class="SpreadSideslipDialog"
      :visible="visible"
      width="700px"
      @close="handleClose"
    >
      <div class="dialog__header">
        {{ $t('marketing.commons.tg_9a8e91') }}
      </div>
      <div class="diglog-material-preview">
        <div class="material-left">
          <img :src="material.image" alt="">
        </div>
        <div class="material-main">
          <div class="material-title">{{ material.title || '--' }}</div>
          <div class="materil-info">
            <div class="info-item">
              {{ $t('marketing.commons.cjz_ec37bb') }}{{ material.creatorName || '--' }}
            </div>
            <div class="info-item">
              {{ $t('marketing.commons.gxsj_780fb9') }}{{ material.updateTime }}
            </div>
          </div>
        </div>
      </div>
      <div class="dialog__body">
        <!-- 表单侧滑隐藏群发 -->
        <div
          v-if="material.objectType != 16"
          class="body__block flex-space"
        >
          <div class="body__left">
            <div class="block__title">
              {{ $t('marketing.commons.qf_30269e') }}
            </div>
            <div class="block__desc">
              {{ $t('marketing.components.SpreadSideslipDialog.xmbkhqzplf_dad9d0') }}
            </div>
          </div>
          <div class="body__right">
            <ElButton
              class="button"
              type="primary"
              size="mini"
              @click="isShowSpreadDialog = true"
            >
              {{ $t('marketing.components.SpreadSideslipDialog.qfhdnr_b46ea4') }}
            </ElButton>
          </div>
        </div>
        <div
          v-if="material.objectType != 16"
          class="body__block flex-space"
        >
          <div class="body__left">
            <div class="block__title">
              {{ $t('marketing.components.SpreadSideslipDialog.qytg_966c63') }}
            </div>
            <div class="block__desc">
              {{ $t('marketing.components.SpreadSideslipDialog.xygxftgrwt_a2603d') }}
            </div>
          </div>
          <div class="body__right">
            <ElButton
              class="button"
              type="primary"
              size="mini"
              plain
              @click="handlePromotionActivityStaff"
            >
              {{ $t('marketing.components.SpreadSideslipDialog.fqqytg_b8e836') }}
            </ElButton>
          </div>
        </div>
       <div class="body__block">
          <div class="body__left">
            <div class="block__title">
              {{ $t('marketing.commons.ewmylj_9c69e5') }}
            </div>
            <div class="block__desc">
              {{ $t('marketing.commons.xztgqdhqbt_e41c68') }}
            </div>
          </div>
          <div class="block__main">
            <SpreadChannel
              ref="spreadChannel"
              :marketing-event-id="marketingEventId"
              :material="spreadMaterial"
            />
            <!-- <channel-history
              :marketingEventId="marketingEventId"
              :materil="spreadMaterial"
              @detail="showDetail"
              :disItems.sync="disItems"
              ref="history"
            ></channel-history> -->
          </div>
        </div>
        <div
          class="body__block"
        >
          <div class="body__left">
            <div class="block__title" style="display: flex;align-items: center;gap: 8px;">
             {{ $t('marketing.commons.ygzztg_305a39') }}
             <fx-switch
             size="mini"
              v-model="material.isMobileDisplay"
              @change="handleMobileDisplayChange"
             />
             <a href="https://help.fxiaoke.com/93d5/9188/0f9e/b30c" target="_blank" style="margin-left: 4px;line-height: 16px;">
                <i class="fx-icon-question" style="color: #91959E;font-size: 16px;"></i>
              </a>
            </div>
            <div class="block__desc">
              {{ $t('marketing.components.SpreadSideslipDialog.zyxzszxsgn_95ce0b') }}
            </div>
          </div>
          <div class="block__main" style="margin-top: 10px;" v-show="material.isMobileDisplay">
            <!-- 会议在生成推广内容时 物料id是微页面id 物料类型是微页面 所以生成宣传语的时候也使用微页面相关-->
            <div class="block__sub-title">{{ $t('marketing.commons.mrtgy_c36201') }}</div>
            <SloganAddBox
              :object-id="material.objectType == 13 ? material.activityDetailSiteId : material.id"
              :object-type="material.objectType == 13 ? 26 : material.objectType"
              :show-save-btn="true"
            />
          </div>
        </div>
      </div>
      <SpreadDialog
        v-if="isShowSpreadDialog"
        :title="$t('marketing.commons.qf_30269e')"
        :visible.sync="isShowSpreadDialog"
        :marketing-event-id="marketingEventId"
        :material="material"
        :scene-type="sceneType"
        :show-crm-object-params="true"
        @submit="handleSpreadDialogSubmit"
        @getFialedChannel="getFialedChannel"
      />
      <SpreadAllStaffDialog
        v-if="isShowSpreadAllStaffDialog"
        :visible.sync="isShowSpreadAllStaffDialog"
        :marketing-event-id="marketingEventId"
        :title="$t('marketing.components.SpreadSideslipDialog.qytg_966c63')"
        :material="material"
        @close="isShowSpreadAllStaffDialog = false"
      />
      <SpreadPartnerDialog
        v-if="isShowSpreadPartnerDialog"
        :visible.sync="isShowSpreadPartnerDialog"
        :marketing-event-id="marketingEventId"
        :material="material"
        @close="isShowSpreadPartnerDialog = false"
      />
      <Dialog
        :title="$t('marketing.commons.ts_02d981')"
        :visible.sync="failedDialogVisible"
        width="700px"
        append-to-body
        custom-class="dialog__failchannel"
      >
        <div class="tip__container">
          <div class="tip__body">
            <div class="tip__header">
              <i
                :class="
                  successChannel.length > 0
                    ? 'el-icon-success'
                    : 'el-icon-warning'
                "
              />
              <p>{{ successChannel.length > 0 ? $t('marketing.components.SpreadSideslipDialog.fswb_2d74c4') : $t('marketing.components.SpreadSideslipDialog.qbfssb_887d89') }}</p>
            </div>
            <div
              v-for="(item, i) in successChannel"
              :key="i"
              class="success__box"
            >
              <i class="el-icon-success" />
              <p>{{ item }}{{ $t('marketing.commons.fscg_9db9a7') }}}</p>
            </div>
            <div
              v-for="(item, i) in failedChannel"
              :key="i"
              class="failed__box"
            >
              <i class="el-icon-warning" />
              <p>{{ item }}{{ $t('marketing.commons.fssb_9ca6a3') }}}</p>
            </div>
          </div>
        </div>
        <span
          slot="footer"
          class="dialog-footer"
        >
          <fx-button
            size="small"
            @click="failedDialogVisible = false"
          >{{ $t('marketing.commons.gb_b15d91') }}</fx-button>
        </span>
      </Dialog>
    </sideslip-popup>
</template>

<script>
import SideslipPopup from '@/components/sideslip-popup/index.vue'
import SpreadChannel from './spread-channel.vue'
import SpreadDialog from '@/components/SpreadDialog'
import SpreadAllStaffDialog from '@/components/SpreadAllStaffDialog'
import SpreadPartnerDialog from '@/components/SpreadPartnerDialog'
import kisvData from '@/modules/kisv-data'
import SloganAddBox from '@/components/slogan-adddialog/index.vue'
import {
  getTime,materialTitle, materialPhoto,
} from '@/pages/content-marketing/func.js'
import http from '@/services/http/index.js'


export default {
  components: {
    SideslipPopup,
    SpreadChannel,
    SpreadDialog,
    SpreadAllStaffDialog,
    ElButton: FxUI.Button,
    Dialog: FxUI.Dialog,
    SpreadPartnerDialog,
    SloganAddBox,
  },
  props: {
    // 短信模板场景 0其他营销短信 1其他通知短信  2会议报名  3会议邀约  4直播报名  5直播邀约  6：EXCEL）
    sceneType: {
      type: Number,
      default: 0,
    },
    visible: {
      type: Boolean,
      default: false,
    },
    marketingEventId: {
      type: String,
      default: '',
    },
    material: {
      type: Object,
      default() {
        return {}
      },
    },
    isConferenceForm: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      vDatas: kisvData.datas,
      isShowSpreadPartnerDialog: false,
      isShowSpreadDialog: false,
      isShowSpreadAllStaffDialog: false,
      failedDialogVisible: false,
      // 发送成功的渠道
      successChannel: [],
      // 发送失败的渠道
      failedChannel: [],
    }
  },
  computed: {
    spreadMaterial() {
      if (this.material.objectType == 13) {
        // 会议主页进入微页面，但要带上objectType和objectId
        return {
          id: this.material.activityDetailSiteId,
          objectType: 26,
          targetObjectType: 13,
          targetObjectId: this.material.id,
          title: this.material.title,
        }
      }
      return this.material
    },
  },
  watch: {
    material() {
      console.log('material', this.material)
    },
  },
  mounted() {
    this.getFialedChannel()
  },
  methods: {
    handleSpreadDialogSubmit() {},
    getFialedChannel(successChannel, failedChannel) {
      this.successChannel = typeof successChannel === 'undefined' ? [] : successChannel
      this.failedChannel = typeof failedChannel === 'undefined' ? [] : failedChannel

      for (let i = 0; i < this.successChannel.length; i++) {
        if (this.successChannel[i] === 'sms') {
          this.successChannel[i] = $t('marketing.commons.dx_485c3a')
        } else if (this.successChannel[i] === 'wechat') {
          this.successChannel[i] = $t('marketing.commons.gzh_215fee')
        } else if (this.successChannel[i] === 'email') {
          this.successChannel[i] = $t('marketing.commons.yj_e9e805')
        } else {
          this.successChannel[i] = $t('marketing.commons.qywx_ff17b9')
        }
      }

      for (let i = 0; i < this.failedChannel.length; i++) {
        if (this.failedChannel[i] === 'sms') {
          this.failedChannel[i] = $t('marketing.commons.dx_485c3a')
        } else if (this.failedChannel[i] === 'wechat') {
          this.failedChannel[i] = $t('marketing.commons.gzh_215fee')
        } else if (this.failedChannel[i] === 'email') {
          this.failedChannel[i] = $t('marketing.commons.yj_e9e805')
        } else {
          this.failedChannel[i] = $t('marketing.commons.qywx_ff17b9')
        }
      }

      if (this.failedChannel.length > 0) {
        this.failedDialogVisible = true
      }
    },
    handlePromotionActivityStaff() {
      this.isShowSpreadAllStaffDialog = true
      // this.$router.push({
      //   name: "promotion-activity-staff",
      //   params: { type: "create" },
      //   query: {
      //     id: this.marketingEventId,
      //     contentType: this.material.type,
      //     contentId: this.material.id,
      //   },
      // })
    },
    handleClose() {
      this.$emit('close')
    },
    materialPhoto,
    materialTitle,
    getTime,
    async handleMobileDisplayChange(value) {
      const { id, objectType } = this.spreadMaterial
      const res = await http.setContentMobileDisplay({
        marketingEventId: this.marketingEventId,
        objectId: id,
        objectType,
        isMobileDisplay: value,
      })
      if (res.errCode === 0) {
        FxUI.Message.success($t('marketing.commons.szcg_f6088e'))
        this.$emit('refresh')
      } else {
        FxUI.Message.error($t('marketing.commons.szsb_9f9603'))
        this.material.isMobileDisplay = !value
      }
    },
  },
}
</script>

<style lang="less" scoped>
.SpreadSideslipDialog {
  .dialog__header {
    padding-left: 15px;
    height: 48px;
    line-height: 48px;
    background-color: #f6f9fc;
    color: #181c25;
    font-size: 16px;
    flex-shrink: 0;
  }

  .diglog-material-preview{
    padding: 12px;
    border-bottom: 1px solid var(--color-neutrals05);
    display: flex;
    align-items: center;
    gap: 12px;
    .material-left{
      width: 120px;
      height: 80px;
      border-radius: 4px;
      img{
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 4px;
      }
    }
    .material-main{
      .material-title{
        color: var(--color-neutrals19);
        font-size: 16px;
        font-weight: 700;
        line-height: 24px;
        margin-bottom: 8px;
      }
      .materil-info{
        display: flex;
        gap: 20px;
        color: var(--color-neutrals11);
        font-size: 12px;
        font-weight: 400;
        line-height: 18px; /* 150% */
      }
    }
  }
  .dialog__body {
    .body__block {
      padding: 20px 20px 20px 0;
      margin-left: 12px;
      border-bottom: 1px dashed var(--color-neutrals05);
      &:last-child{
        border-bottom: none;
      }
      .block__title {
        font-size: 14px;
        color: #181c25;
        position: relative;
        font-weight: bold;
        padding-left: 11px;
        &::before {
          position: absolute;
          top: 50%;
          left: 0px;
          content: " ";
          width: 2px;
          height: 12px;
          background: var(--color-primary06,#ff8000);
          transform: translate(0, -50%);
        }
      }
      .block__desc {
        font-size: 12px;
        color: #91959E;
        padding-left: 11px;
        margin-top: 6px;
      }
      .block__main {
        margin-top: 20px;
        padding-left: 11px;
        .button {
          width: 140px;
        }
        .block__sub-title{
          color: var(--color-neutrals19);
          font-feature-settings: 'liga' off, 'clig' off;
          /* Text/14 */
          font-family: "Source Han Sans CN";
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 20px; /* 142.857% */
          margin-bottom: 10px;
        }
      }
    }
    .flex-space{
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }
}
.dialog__failchannel {
  .el-dialog__body {
    .tip__container {
      margin-left: 250px;
      height: 300px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      .tip__header {
        margin-bottom: 30px;
        width: 150px;
        display: flex;
        flex-direction: column;
        align-items: center;
        .el-icon-success {
          color: #67c23a;
          font-size: 50px;
          margin-bottom: 10px;
        }
        .el-icon-warning {
          color: red;
          font-size: 50px;
          margin-bottom: 10px;
        }
        p {
          font-size: 22px;
          font-weight: 600;
          color: black;
        }
      }
      .success__box {
        display: flex;
        align-items: center;
        margin-bottom: 6px;
        i {
          color: #67c23a;
          font-size: 22px;
          margin-right: 10px;
        }
        p {
          font-size: 18px;
        }
      }
      .failed__box {
        display: flex;
        align-items: center;
        margin-bottom: 6px;
        i {
          color: red;
          font-size: 22px;
          margin-right: 6px;
        }
        p {
          font-size: 18px;
        }
      }
    }
  }
}
.qr-content {
  padding: 0 5px;
}
</style>
