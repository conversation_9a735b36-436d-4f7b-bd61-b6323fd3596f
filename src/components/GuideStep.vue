<template>
  <div v-if="show" class="marketing__guide-container">
    <!-- 指示条：只在有多个高亮元素时显示 -->
    <div v-if="tooltipReady && mainArrow && mainArrowCircle && normalizedTargets.length > 1">
      <div class="marketing__guide-arrow" :style="mainArrow"></div>
      <div class="marketing__guide-arrow-circle" :style="mainArrowCircle">
        <div class="arrow-outer"></div>
        <div class="arrow-inner"></div>
      </div>
    </div>
    <!-- 只渲染一层SVG遮罩和所有高亮边框 -->
    <div
      v-if="show && highlightRects.length > 0"
      class="guide-mask-svg"
      :style="{position: 'fixed', top: 0, left: 0, zIndex: 9998, pointerEvents: 'auto', width: viewportWidth + 'px', height: viewportHeight + 'px'}"
      @click="onMaskClick($event)"
    >
      <svg :width="viewportWidth" :height="viewportHeight" style="position:absolute;top:0;left:0;">
        <defs>
          <mask id="guide-mask">
            <rect :width="viewportWidth" :height="viewportHeight" fill="white"/>
            <rect
              v-for="(rect, i) in highlightRects"
              :key="i"
              :x="rect.x"
              :y="rect.y"
              :width="rect.width"
              :height="rect.height"
              :rx="rect.radius"
              :ry="rect.radius"
              fill="black"
            />
          </mask>
          <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
            <feDropShadow dx="0" dy="0" stdDeviation="4" flood-color="#409eff" flood-opacity="0.15"/>
          </filter>
          <!-- 为每个高亮区域生成clipPath，clip掉被上层覆盖的部分 -->
          <clipPath v-for="(rect, i) in highlightRects" :id="`clip-guide-${i}`" :key="'clip-'+i">
            <rect :x="rect.x" :y="rect.y" :width="rect.width" :height="rect.height" :rx="rect.radius" :ry="rect.radius" />
            <rect v-if="i < highlightRects.length - 1" :x="highlightRects[highlightRects.length-1].x" :y="highlightRects[highlightRects.length-1].y" :width="highlightRects[highlightRects.length-1].width" :height="highlightRects[highlightRects.length-1].height" :rx="highlightRects[highlightRects.length-1].radius" :ry="highlightRects[highlightRects.length-1].radius" fill="white"/>
          </clipPath>
        </defs>
        <rect
          :width="viewportWidth"
          :height="viewportHeight"
          fill="rgba(0,0,0,0.5)"
          mask="url(#guide-mask)"
        />
        <!-- 每个高亮区域都画边框，重叠部分clip掉，只显示最上层完整边框 -->
        <rect
          v-for="(rect, i) in highlightRects"
          :key="'border-'+i"
          :x="rect.x"
          :y="rect.y"
          :width="rect.width"
          :height="rect.height"
          :rx="rect.radius"
          :ry="rect.radius"
          fill="none"
          stroke="#409eff"
          stroke-width="2"
          stroke-dasharray="6,4"
          filter="url(#shadow)"
          pointer-events="none"
          :clip-path="i < highlightRects.length - 1 ? `url(#clip-guide-${i})` : undefined"
        />
      </svg>
    </div>

    <!-- 高亮目标元素，支持多个 -->
    <template v-if="currentStep">
      <div
        v-for="(target, index) in normalizedTargets"
        :key="index"
        class="marketing__guide-highlight"
        :style="getHighlightStyle(target, index, normalizedTargets.length - 1)"
        :data-highlight="target"
      ></div>
    </template>

    <!-- 引导提示框 -->
    <div
      ref="tooltip"
      :class="[
        'marketing__guide-tooltip',
        { 'marketing__guide-tooltip-flex': steps.length === 1 },
        { 'marketing__guide-tooltip-hidden': !currentStep || !tooltipReady }
      ]"
      :style="{ visibility: tooltipReady ? 'visible' : 'hidden' }"
    >
      <!-- 提示内容 -->
      <div v-if="currentStep" class="marketing__guide-content">
        <div class="marketing__guide-num" v-if="this.steps.length > 1">
          {{ currentStepIndex + 1 }}
        </div>
        <div class="marketing_guide-main">
          <div class="marketing__guide-title" v-if="currentStep.title">
            {{ currentStep.title }}
          </div>
          <div class="marketing__guide-desc">
            {{ currentStep.content }}
          </div>
        </div>
      </div>

      <!-- 按钮和步骤点同行区域 -->
      <div class="marketing__guide-footer" v-if="currentStep && steps.length > 1">
        <div class="guide-step-progress">
          {{ currentStepIndex + 1 }}/{{ steps.length }}
        </div>
        <div class="guide-step-btn-group">
          <fx-button
            v-if="currentStepIndex > 0"
            class="marketing__guide-btn"
            size="mini"
            @click.stop="prev"
          >
            {{ $t("marketing.commons.syb_eeb690") }}
          </fx-button>
          <fx-button
            v-if="!isLastStep"
            class="marketing__guide-btn primary"
            size="mini"
            @click.stop="next"
          >
            {{ $t("marketing.commons.xyb_38ce27") }}
          </fx-button>
          <fx-button
            v-if="isLastStep"
            class="marketing__guide-btn primary"
            size="mini"
            @click.stop="next"
          >
            {{ $t("marketing.commons.wzdl_fe0337") }}
          </fx-button>
        </div>
      </div>

      <template v-if="currentStep && steps.length === 1">
        <div class="guide-close-btn" @click="finish(true)">
          <span class="fx-icon-close"></span>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  name: "MarketingGuideStep",
  props: {
    steps: {
      type: Array,
      required: true,
      validator: steps =>
        steps.every(step => {
          // 支持 target 为字符串或字符串数组
          return (
            (typeof step.target === "string" || Array.isArray(step.target)) &&
            step.content
          );
        })
    },
    value: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      currentStepIndex: 0,
      show: this.value,
      popperInstance: null,
      isStepTransitioning: false,
      tooltipReady: false, // 控制 tooltip 是否准备就绪
      mainArrow: null, // 指示条样式
      mainArrowCircle: null, // 圆形箭头样式
      isUpdatingHighlight: false, // 高亮计算锁
      updateHighlightTimer: null, // 防抖 timer
      highlightRects: [], // SVG高亮区域坐标
      viewportWidth: window.innerWidth, // 响应式视口宽度
      viewportHeight: window.innerHeight // 响应式视口高度
    };
  },

  computed: {
    currentStep() {
      return this.steps[this.currentStepIndex];
    },

    isLastStep() {
      return this.currentStepIndex === this.steps.length - 1;
    },

    // 将 target 统一处理为数组格式
    normalizedTargets() {
      if (!this.currentStep) return [];
      return Array.isArray(this.currentStep.target)
        ? this.currentStep.target
        : [this.currentStep.target];
    },

    // 只渲染最后一个高亮边框
    lastHighlightRect() {
      if (!this.highlightRects || this.highlightRects.length === 0) return null;
      return this.highlightRects[this.highlightRects.length - 1];
    }
  },

  watch: {
    value(val, oldVal) {
      this.show = val;
      if (val && !oldVal && this.steps && this.steps.length > 0) {
        this.currentStepIndex = 0;
        // 首次打开时，确保 DOM 已渲染
        this.$nextTick(() => {
          setTimeout(() => {
            this.scheduleUpdateHighlightAreas();
          }, 0);
        });
      }
    },
    currentStepIndex() {
      this.scheduleUpdateHighlightAreas();
    }
  },

  methods: {
    // 计算所有高亮区域的坐标和尺寸，供SVG遮罩和边框使用
    updateHighlightRects() {
      if (!this.currentStep || !this.normalizedTargets.length) {
        this.highlightRects = [];
        return;
      }
      const radius = 4; // 圆角半径
      this.highlightRects = this.normalizedTargets.map(target => {
        const el = document.querySelector(target);
        if (!el) return null;
        let rect = el.getBoundingClientRect();

        // 修正弹窗内 target 的坐标
        if (this.currentStep.dialog && this.currentStep.dialogEl) {
          const dialogEl = document.querySelector(this.currentStep.dialogEl);
          if (dialogEl) {
            const dialogRect = dialogEl.getBoundingClientRect();
            // 修正为相对于视口的坐标（考虑弹窗的 offsetTop/offsetLeft）
            rect = {
              top: rect.top - dialogRect.top + dialogEl.offsetTop,
              left: rect.left - dialogRect.left + dialogEl.offsetLeft,
              width: rect.width,
              height: rect.height
            };
          }
        }

        return {
          x: rect.left - 2,
          y: rect.top - 2,
          width: rect.width + 4,
          height: rect.height + 4,
          radius
        };
      }).filter(Boolean);
    },

    // 防抖触发高亮计算
    scheduleUpdateHighlightAreas() {
      clearTimeout(this.updateHighlightTimer);
      this.updateHighlightTimer = setTimeout(() => {
        this.updateHighlightAreas();
        this.updateHighlightRects(); // 新增：同步SVG高亮区域
      }, 0);
    },

    // 极致性能优化：高亮引导只等待一次渲染，立即高亮
    async updateHighlightAreas() {
      if (this.isUpdatingHighlight) return;
      this.isUpdatingHighlight = true;
      clearTimeout(this.updateHighlightTimer);
      if (!this.currentStep || !this.normalizedTargets.length) {
        this.isUpdatingHighlight = false;
        return;
      }
      const tooltip = this.$refs.tooltip;
      if (!tooltip) {
        this.isUpdatingHighlight = false;
        return;
      }

      // 首次打开时，主动等目标元素出现（只等2次，每次50ms）
      let elements = this.normalizedTargets.map(sel => document.querySelector(sel));
      let tries = 0;
      while (elements.some(el => !el) && tries < 2) {
        await new Promise(r => setTimeout(r, 50));
        elements = this.normalizedTargets.map(sel => document.querySelector(sel));
        tries++;
      }
      if (elements.some(el => !el)) {
        return;
      }

      // 遮罩和高亮立即计算
      this.clearPreviousHighlights();
      await this.updateHighlights();

      // 弹窗动画等待（如有）
      const ifDialog = this.currentStep.dialog;
      const dialogEl = this.currentStep.dialogEl;
      if (ifDialog && dialogEl) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // Popper 定位
      await this.updatePopperPosition();

      // 显示 tooltip
      this.tooltipReady = true;
      tooltip.style.visibility = 'visible';
      tooltip.classList.remove('marketing__guide-tooltip-hidden');
      this.computeMainArrow();
      this.isUpdatingHighlight = false;
    },

    // 等待元素加载完成的工具方法
    async waitForElement(selector, maxRetries = 5, interval = 100) {
      for (let i = 0; i < maxRetries; i++) {
        const element = document.querySelector(selector);
        if (element) {
          // 检查元素是否可见且尺寸不为0
          const rect = element.getBoundingClientRect();
          const style = window.getComputedStyle(element);
          if (rect.width > 0 && rect.height > 0 && style.display !== 'none' && style.visibility !== 'hidden') {
            return element;
          }
        }
        await new Promise(resolve => setTimeout(resolve, interval));
      }
      return null;
    },

    // 等待所有目标元素加载完成
    async waitForAllTargets() {
      if (!this.currentStep || !this.normalizedTargets.length) return false;

      try {
        const targetPromises = this.normalizedTargets.map(target => 
          this.waitForElement(target)
        );
        
        const elements = await Promise.all(targetPromises);
        const allLoaded = elements.every(el => el !== null);
        
        if (!allLoaded) {
          return allLoaded;
        }
        
        return allLoaded;
      } catch (error) {
        return false;
      }
    },

    // 等待弹窗完全加载
    async waitForDialog(dialogEl) {
      if (!dialogEl) return true;
      
      try {
        const dialog = await this.waitForElement(dialogEl);
        if (!dialog) return false;

        // 减少等待时间，从500ms降到200ms
        await new Promise(resolve => setTimeout(resolve, 200));
        
        // 检查弹窗是否完全显示
        const rect = dialog.getBoundingClientRect();
        const style = window.getComputedStyle(dialog);
        
        return rect.width > 0 && rect.height > 0 && 
               style.display !== 'none' && 
               style.visibility !== 'hidden' &&
               style.opacity !== '0';
      } catch (error) {
        return false;
      }
    },

    // 等待页面稳定（防止滚动、动画等影响位置计算）
    async waitForPageStable(timeout = 500) {
      return new Promise(resolve => {
        let lastScrollTop = window.pageYOffset;
        let lastScrollLeft = window.pageXOffset;
        let stableCount = 0;
        const requiredStableCount = 2; // 减少稳定检查次数
        
        const checkStability = () => {
          const currentScrollTop = window.pageYOffset;
          const currentScrollLeft = window.pageXOffset;
          
          if (Math.abs(currentScrollTop - lastScrollTop) < 1 && 
              Math.abs(currentScrollLeft - lastScrollLeft) < 1) {
            stableCount++;
            if (stableCount >= requiredStableCount) {
              resolve();
              return;
            }
          } else {
            stableCount = 0;
          }
          
          lastScrollTop = currentScrollTop;
          lastScrollLeft = currentScrollLeft;
          
          setTimeout(checkStability, 50); // 减少检查间隔
        };
        
        checkStability();
        
        // 超时保护
        setTimeout(resolve, timeout);
      });
    },

    getScrollContainers(element) {
      const containers = [];
      let parent = element.parentElement;

      while (parent) {
        const { overflow, overflowY } = window.getComputedStyle(parent);
        if (/(auto|scroll)/.test(overflow) || /(auto|scroll)/.test(overflowY)) {
          containers.push(parent);
        }
        parent = parent.parentElement;
      }
      if (containers.length === 0) {
        containers.push(document.documentElement);
      }

      return containers;
    },

    async scrollElementIntoView(element) {
      if (!element) return;

      return new Promise(async resolve => {
        try {
          // 获取所有可滚动容器
          const scrollContainers = this.getScrollContainers(element);

          // 获取元素的位置信息
          const elementRect = element.getBoundingClientRect();

          // 对每个滚动容器进行必要的滚动调整
          for (const container of scrollContainers) {
            const containerRect = container.getBoundingClientRect();
            const isElementInView = this.isElementInViewportRelative(
              element,
              container
            );

            if (!isElementInView) {
              // 计算相对于当前容器的目标滚动位置
              const currentScrollTop = container.scrollTop;
              const relativeTop = elementRect.top - containerRect.top;
              const targetPosition = currentScrollTop + relativeTop - 50; // 50px 的上边距

              if (!isNaN(targetPosition)) {
                // 直接设置滚动位置，不使用平滑滚动
                container.scrollTop = targetPosition;

                // 使用 requestAnimationFrame 确保位置更新
                requestAnimationFrame(() => {
                  // 检查是否需要微调
                  const finalRect = element.getBoundingClientRect();
                  const finalContainerRect = container.getBoundingClientRect();
                  const finalRelativeTop =
                    finalRect.top - finalContainerRect.top;

                  // 如果位置偏差超过5px，进行微调
                  if (Math.abs(finalRelativeTop - 50) > 5) {
                    container.scrollTop =
                      targetPosition + (50 - finalRelativeTop);
                  }
                  resolve();
                });
              }
            }
          }

          resolve();
        } catch (err) {
          resolve();
        }
      });
    },

    isElementInViewportRelative(
      element,
      container,
      margin = { top: 50, bottom: 100 }
    ) {
      if (!element || !container) return false;

      // 获取元素和容器的位置信息
      const elementRect = element.getBoundingClientRect();
      const containerRect = container.getBoundingClientRect();

      // 检查容器是否在视口内
      const isContainerInViewport =
        containerRect.top >= 0 &&
        containerRect.left >= 0 &&
        containerRect.bottom <= window.innerHeight &&
        containerRect.right <= window.innerWidth;

      if (!isContainerInViewport) {
        return false;
      }

      // 计算元素相对于容器的位置
      const relativeTop = elementRect.top - containerRect.top;
      const relativeBottom = elementRect.bottom - containerRect.top;

      // 检查元素是否在容器的可视区域内
      return (
        relativeTop >= margin.top &&
        relativeBottom <= containerRect.height - margin.bottom
      );
    },

    // 获取元素的实际位置（考虑transform、fixed定位）
    async getElementPosition(element) {
      if (!element) return null;

      // 检查元素是否在弹窗内
      const ifDialog = this.currentStep.dialog;
      const dialogEl = document.querySelector(this.currentStep.dialogEl);

      // 如果在弹窗内
      if (ifDialog && dialogEl) {
        return new Promise(resolve => {
          // 等待dialog动画结束(300ms)后再获取位置
          setTimeout(async () => {
            try {
              // 获取弹窗的位置信息
              const dialogRect = dialogEl.getBoundingClientRect();
              const dialogStyle = window.getComputedStyle(dialogEl);
              
              // 获取目标元素的位置信息
              const elementRect = element.getBoundingClientRect();  
              const position = {
                top: elementRect.top,
                left: elementRect.left,
                width: elementRect.width,
                height: elementRect.height
              };

              // 获取弹窗的 transform 矩阵
              const dialogTransform = dialogStyle.transform;
              if (dialogTransform && dialogTransform !== 'none') {
                // 解析 transform 矩阵
                const matrix = new DOMMatrix(dialogTransform);
                
                // 计算弹窗的实际位置（考虑 transform）
                const dialogActualTop = dialogRect.top + matrix.m42;
                const dialogActualLeft = dialogRect.left + matrix.m41;
                
                // 调整元素位置，使其相对于弹窗的实际位置
                position.top = position.top - dialogActualTop + dialogRect.top;
                position.left = position.left - dialogActualLeft + dialogRect.left;
              }

              // 考虑弹窗的滚动位置
              const dialogScrollTop = dialogEl.scrollTop || 0;
              const dialogScrollLeft = dialogEl.scrollLeft || 0;
              
              position.top -= dialogScrollTop;
              position.left -= dialogScrollLeft;

              resolve(position);
            } catch (error) {
              // 如果计算出错，返回原始位置（增加1px边距）
              const rect = element.getBoundingClientRect();
              resolve({
                top: rect.top - 1,
                left: rect.left - 1,
                width: rect.width + 2,
                height: rect.height + 2
              });
            }
          }, 300);
        });
      }

      // 非弹窗内元素 - 直接使用 getBoundingClientRect，并增加1px边距
      const rect = element.getBoundingClientRect();

      return {
        top: rect.top - 1,
        left: rect.left - 1,
        width: rect.width + 2,
        height: rect.height + 2
      };
    },

    // 更新高亮元素位置
    async updateHighlights() {
      if (!this.currentStep || !this.normalizedTargets.length) return;

      try {
        // 先清理之前的高亮元素状态
        this.clearPreviousHighlights();

        // 等待所有目标元素的位置计算完成
        const positions = await Promise.all(
          this.normalizedTargets.map(async target => {
            const el = document.querySelector(target);
            if (!el) return null;
            const position = await this.getElementPosition(el);
            return { target, position };
          })
        );

        // 过滤掉无效的位置
        const validPositions = positions.filter(item => item && item.position);

        // 更新高亮元素
        validPositions.forEach(({ target, position }) => {
          const highlightEl = document.querySelector(`[data-highlight="${target}"]`);
          if (highlightEl && position) {
            Object.assign(highlightEl.style, {
              position: 'fixed',
              top: `${position.top}px`,
              left: `${position.left}px`,
              width: `${position.width}px`,
              height: `${position.height}px`,
              opacity: '1',
              visibility: 'visible',
              zIndex: this.getHighlightZIndex(document.querySelector(target))
            });
          }
        });
      } catch (error) {
      }
    },

    // 获取高亮元素的 z-index
    getHighlightZIndex(element) {
      if (!element) return 9999;

      // 查找元素所在的弹窗
      const dialogEl = document.querySelector(this.currentStep.dialogEl);
      if (dialogEl) {
        // 获取弹窗的 z-index
        const dialogZIndex = parseInt(window.getComputedStyle(dialogEl).zIndex, 10) || 2000;
        
        // 获取弹窗的 modal 层（如果有）
        const modal = dialogEl.previousElementSibling?.matches('.v-modal') 
          ? dialogEl.previousElementSibling 
          : null;

        // 设置遮罩层 z-index
        const container = document.querySelector('.marketing__guide-container');
        if (container) {
          // 确保遮罩层在弹窗之上，但在高亮层之下
          container.style.zIndex = dialogZIndex + 1;
          
          // 将遮罩层移动到弹窗后面
          if (dialogEl.nextSibling !== container) {
            dialogEl.parentNode.insertBefore(container, dialogEl.nextSibling);
          }
        }

        // 禁用弹窗的点击遮罩关闭
        dialogEl.setAttribute('close-on-click-modal', 'false');
        
        // 隐藏弹窗的 modal 层
        if (modal) {
          modal.style.display = 'none';
        }

        // 返回高亮层的 z-index，确保在遮罩层之上
        return dialogZIndex + 2;
      }

      // 检查元素是否有 transform
      const style = window.getComputedStyle(element);
      if (style.transform && style.transform !== 'none') {
        // 如果元素有 transform，确保高亮层在 transform 层之上
        return 9999;
      }

      // 默认返回一个较高的 z-index
      return 9999;
    },

    // 更新 Popper 位置但不显示 tooltip
    async updatePopperPosition() {
      if (!this.currentStep || !this.normalizedTargets.length || !this.$refs.tooltip) return;

      const tooltip = this.$refs.tooltip;
      // 兼容多高亮，取第二个高亮元素
      const secondTargetEl = this.normalizedTargets[1] ? document.querySelector(this.normalizedTargets[1]) : null;
      // arrowTarget 元素
      const arrowEl = this.currentStep.arrowTarget ? document.querySelector(this.currentStep.arrowTarget) : null;
      // 默认主 target
      const targetEl = document.querySelector(this.normalizedTargets[0]);
      if (!targetEl) return;

      try {
        tooltip.style.visibility = 'hidden';
        tooltip.classList.add('marketing__guide-tooltip-hidden');
        const ifDialog = this.currentStep.dialog;
        const dialogEl = document.querySelector(this.currentStep.dialogEl);
        if (ifDialog && dialogEl) {
          await new Promise(resolve => {
            setTimeout(async () => {
              await new Promise(r => requestAnimationFrame(r));
              resolve();
            }, 150);
          });
        }
        let top = 0, left = 0;
        const placement = this.currentStep.placement || 'right';
        const GAP = 12;
        const EXTRA_GAP = 16;
        if (arrowEl && secondTargetEl && placement === 'right') {
          const arrowRect = arrowEl.getBoundingClientRect();
          const secondRect = secondTargetEl.getBoundingClientRect();
          await this.$nextTick();
          const tooltipRect = tooltip.getBoundingClientRect();
          top = arrowRect.top + arrowRect.height / 2 - tooltipRect.height / 2;
          left = secondRect.left + secondRect.width + GAP;
        } else if (arrowEl && placement === 'top') {
          const arrowRect = arrowEl.getBoundingClientRect();
          await this.$nextTick();
          const tooltipRect = tooltip.getBoundingClientRect();
          top = arrowRect.top - tooltipRect.height - GAP;
          left = arrowRect.left + arrowRect.width / 2 - tooltipRect.width / 2;
        } else if (arrowEl && placement === 'bottom') {
          const arrowRect = arrowEl.getBoundingClientRect();
          await this.$nextTick();
          const tooltipRect = tooltip.getBoundingClientRect();
          top = arrowRect.top + arrowRect.height + GAP;
          left = arrowRect.left + arrowRect.width / 2 - tooltipRect.width / 2;
        } else if (arrowEl && placement === 'left') {
          const arrowRect = arrowEl.getBoundingClientRect();
          await this.$nextTick();
          const tooltipRect = tooltip.getBoundingClientRect();
          top = arrowRect.top + arrowRect.height / 2 - tooltipRect.height / 2;
          left = arrowRect.left - tooltipRect.width - GAP - EXTRA_GAP;
        } else if (secondTargetEl && placement === 'right') {
          const secondRect = secondTargetEl.getBoundingClientRect();
          await this.$nextTick();
          const tooltipRect = tooltip.getBoundingClientRect();
          top = secondRect.top + secondRect.height / 2 - tooltipRect.height / 2;
          left = secondRect.left + secondRect.width + GAP;
        } else {
          const targetRect = targetEl.getBoundingClientRect();
          await this.$nextTick();
          const tooltipRect = tooltip.getBoundingClientRect();
          if (placement === 'top') {
            top = targetRect.top - tooltipRect.height - GAP;
            left = targetRect.left + targetRect.width / 2 - tooltipRect.width / 2;
          } else if (placement === 'bottom') {
            top = targetRect.top + targetRect.height + GAP;
            left = targetRect.left + targetRect.width / 2 - tooltipRect.width / 2;
          } else if (placement === 'left') {
            top = targetRect.top + targetRect.height / 2 - tooltipRect.height / 2;
            left = targetRect.left - tooltipRect.width - GAP - EXTRA_GAP;
          } else {
            top = targetRect.top + targetRect.height / 2 - tooltipRect.height / 2;
            left = targetRect.left + targetRect.width + GAP;
          }
        }
        tooltip.style.position = 'fixed';
        tooltip.style.top = `${top}px`;
        tooltip.style.left = `${left}px`;
        tooltip.style.visibility = 'visible';
        tooltip.classList.remove('marketing__guide-tooltip-hidden');
        if (this.popperInstance) {
          this.popperInstance.destroy();
          this.popperInstance = null;
        }
      } catch (error) {
      }
    },

    // 获取高亮元素样式
    getHighlightStyle(target, index, topIndex) {
      const el = document.querySelector(target);
      if (!el) return {};

      const rect = el.getBoundingClientRect();
      const isTop = index === topIndex;
      return {
        position: "fixed",
        top: `${rect.top - 2}px`,
        left: `${rect.left - 2}px`,
        width: `${rect.width + 4}px`,
        height: `${rect.height + 4}px`,
        transition: "all 0.3s ease-in-out",
        zIndex: 9999,
        pointerEvents: "none",
        boxSizing: "border-box",
        border: isTop ? "2px dashed #409eff" : "none",
        borderRadius: "4px",
        background: isTop ? "rgba(255,255,255,0.8)" : "transparent",
        boxShadow: isTop ? "0 0 0 4px rgba(64,158,255,0.15)" : "none"
      };
    },

    // 修改 next 方法
    async next() {
      if (this.isStepTransitioning) return;
      if (this.isLastStep) {
        this.finish(true); // 正常完成引导，执行后动作
      } else {
        try {
          // 判断是否需要关闭上一步的弹窗/popover
          const currentStep = this.steps[this.currentStepIndex];
          const nextStep = this.steps[this.currentStepIndex + 1];
          if (
            (currentStep.dialogEl !== nextStep.dialogEl) ||
            (currentStep.popover !== nextStep.popover) ||
            (JSON.stringify(currentStep.target) !== JSON.stringify(nextStep.target))
          ) {
            this.closeCurrentStepDialogOrPopover();
          }
          this.isStepTransitioning = true;
          this.tooltipReady = false;
          await this.$emit("before-step", this.currentStepIndex + 1);
          this.currentStepIndex++;
          // 步骤切换时锁定popover为manual
          this.$root.$emit('set-popover-trigger', 'manual');
        } catch (error) {
        } finally {
          this.isStepTransitioning = false;
        }
      }
    },

    // 修改 prev 方法
    async prev() {
      if (this.isStepTransitioning) return;
      if (this.currentStepIndex > 0) {
        try {
          // 判断是否需要关闭上一步的弹窗/popover
          const currentStep = this.steps[this.currentStepIndex];
          const prevStep = this.steps[this.currentStepIndex - 1];
          if (
            (currentStep.dialogEl !== prevStep.dialogEl) ||
            (currentStep.popover !== prevStep.popover) ||
            (JSON.stringify(currentStep.target) !== JSON.stringify(prevStep.target))
          ) {
            this.closeCurrentStepDialogOrPopover();
          }
          this.isStepTransitioning = true;
          this.tooltipReady = false;
          await this.$emit("before-step", this.currentStepIndex - 1);
          this.currentStepIndex--;
          // 步骤切换时锁定popover为manual
          this.$root.$emit('set-popover-trigger', 'manual');
        } catch (error) {
        } finally {
          this.isStepTransitioning = false;
        }
      }
    },

    // 只关闭当前步骤的弹窗/popover
    closeCurrentStepDialogOrPopover() {
      const step = this.currentStep;
      if (step && step.dialog && step.closeBtn) {
        this.$emit("close-dialog", step.closeBtn);
      }
      if (step && step.popover) {
        this.$emit("close-popover",step.popover);
      }
    },

    // 修改 finish 方法，添加参数区分是否正常完成引导
    finish(isNormalFinish = false) {
      this.closeCurrentStepDialogOrPopover();
      this.show = false;
      this.tooltipReady = false;
      this.$emit("input", false);
      this.$emit("guide-finish", isNormalFinish); // 传递是否正常完成的标志
      this.$root.$emit('set-popover-trigger', 'click');
    },
    handleCloseDialogOrPopover() {
      // 判断是否有弹窗或者popover
      const ifDialog = this.currentStep.dialog;
      const ifPopover = this.currentStep.popover;
      if (ifDialog) {
        this.$emit("close-dialog", this.currentStep.closeBtn);
      }

      // 关闭popover
      if (ifPopover) {
        this.$emit("close-popover");
      }
    },
    async updatePopper() {
      if (!this.currentStep || !this.normalizedTargets.length || !this.$refs.tooltip) return;

      const tooltip = this.$refs.tooltip;
      
      try {
        // 如果 popper 实例不存在，先创建
        if (!this.popperInstance) {
          await this.updatePopperPosition();
        }

        // 显示 tooltip
        tooltip.style.visibility = 'visible';
        tooltip.classList.remove('marketing__guide-tooltip-hidden');

      } catch (error) {
        // 确保 tooltip 可见
        tooltip.style.visibility = 'visible';
        tooltip.classList.remove('marketing__guide-tooltip-hidden');
      }
    },
    clearPreviousHighlights() {
      try {
        // 清理所有高亮元素
        const highlights = document.querySelectorAll('.marketing__guide-highlight');
        highlights.forEach(highlight => {
          // 重置样式，避免显示之前的位置
          Object.assign(highlight.style, {
            position: 'fixed',
            top: '0px',
            left: '0px',
            width: '0px',
            height: '0px',
            opacity: '0',
            visibility: 'hidden'
          });
        });

      } catch (error) {
      }
    },
    // 计算 tooltip 到主 target 的指示条
    computeMainArrow() {
      if (!this.tooltipReady || !this.$refs.tooltip || !this.normalizedTargets.length) {
        this.mainArrow = null;
        this.mainArrowCircle = null;
        return;
      }
      const tooltip = this.$refs.tooltip;
      const targetEl = document.querySelector(this.normalizedTargets[0]);
      if (!tooltip || !targetEl) {
        this.mainArrow = null;
        this.mainArrowCircle = null;
        return;
      }
      const tooltipRect = tooltip.getBoundingClientRect();

      // 箭头长度固定 33px
      const ARROW_LENGTH = 33;
      let startX, startY;
      const placement = this.currentStep.placement || 'right';
      if (placement === 'top') {
        startX = tooltipRect.left + tooltipRect.width / 2;
        startY = tooltipRect.bottom;
      } else if (placement === 'bottom') {
        startX = tooltipRect.left + tooltipRect.width / 2;
        startY = tooltipRect.top;
      } else if (placement === 'left') {
        startX = tooltipRect.right;
        startY = tooltipRect.top + tooltipRect.height / 2;
      } else { // right or default
        startX = tooltipRect.left;
        startY = tooltipRect.top + tooltipRect.height / 2;
      }

      // 箭头终点优先用 arrowTarget
      let endX, endY;
      if (this.currentStep.arrowTarget) {
        const arrowEl = document.querySelector(this.currentStep.arrowTarget);
        if (arrowEl) {
          const arrowRect = arrowEl.getBoundingClientRect();
          endX = arrowRect.left + arrowRect.width / 2;
          endY = arrowRect.top + arrowRect.height / 2;
        }
      }
      // fallback: 高亮区域中心
      if (endX === undefined || endY === undefined) {
        const targetRect = targetEl.getBoundingClientRect();
        endX = targetRect.left + targetRect.width / 2;
        endY = targetRect.top + targetRect.height / 2;
      }

      // 算出方向
      const dx = endX - startX;
      const dy = endY - startY;
      const angle = Math.atan2(dy, dx) * 180 / Math.PI;
      this.mainArrow = {
        position: 'fixed',
        left: `${startX}px`,
        top: `${startY}px`,
        width: `${ARROW_LENGTH}px`,
        height: '4px',
        background: '#319bf5',
        borderRadius: '2px',
        zIndex: 10010,
        transform: `rotate(${angle}deg)` ,
        transformOrigin: '0 50%',
        pointerEvents: 'none',
      };
      // 靶心终点
      const endCircleX = startX + Math.cos(angle * Math.PI / 180) * ARROW_LENGTH;
      const endCircleY = startY + Math.sin(angle * Math.PI / 180) * ARROW_LENGTH;
      this.mainArrowCircle = {
        position: 'fixed',
        left: `${endCircleX - 12}px`,
        top: `${endCircleY - 12}px`,
        width: '24px',
        height: '24px',
        zIndex: 10011,
        pointerEvents: 'none',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      };
    },
    updateViewport() {
      this.viewportWidth = window.innerWidth;
      this.viewportHeight = window.innerHeight;
    },
    onMaskClick(e) {
      const x = e.clientX;
      const y = e.clientY;
      // 判断是否在任意高亮洞内
      const inAnyHighlight = this.highlightRects.some(rect =>
        x >= rect.x &&
        x <= rect.x + rect.width &&
        y >= rect.y &&
        y <= rect.y + rect.height
      );
      if (!inAnyHighlight) {
        this.finish(false); // 点击mask关闭，不执行后动作
      }
    }
  },

  mounted() {
    this.updateHighlights();
    this.updateHighlightRects();
    window.addEventListener("resize", this.updateHighlights);
    window.addEventListener('resize', this.computeMainArrow);
    window.addEventListener('resize', this.updateHighlightRects);
    window.addEventListener('resize', this.updateViewport);
  },

  beforeDestroy() {
    // 组件销毁时移除遮罩
    const mask = document.body.querySelector(".marketing__guide-mask");
    if (mask) mask.remove();
    this.$emit("guide-finish", false); // 组件销毁，不执行后动作
    if (this.popperInstance) {
      this.popperInstance.destroy();
    }
    window.removeEventListener('resize', this.computeMainArrow);
    window.removeEventListener('resize', this.updateHighlightRects);
    window.removeEventListener('resize', this.updateViewport);
  }
};
</script>

<style scoped lang="less">
.marketing__guide-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
}

.marketing__guide-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9998;
  pointer-events: auto;
}

.marketing__guide-mask-area {
  position: fixed;
  background: rgba(0, 0, 0, 0.5);
  pointer-events: auto;
}

.marketing__guide-highlight {
  display: none; // SVG遮罩后不再渲染div高亮
}

.marketing__guide-tooltip {
  background: var(--BI-Dark-Blue-03, #5498ff);
  padding: 8px;
  border-radius: 8px;
  color: #fff;
  pointer-events: auto;
  max-width: 320px;
  position: fixed;
  z-index: 10000;
  opacity: 1;
  transition: opacity 0.3s ease-out, transform 0.3s ease-out;
  will-change: transform, opacity;
  transform-origin: center;
  visibility: visible;
  transform: scale(1);

  &.marketing__guide-tooltip-hidden {
    opacity: 0;
    pointer-events: none;
    transform: scale(0.95);
    visibility: hidden;
  }

  &.marketing__guide-tooltip-flex {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}

.marketing__guide-content {
  display: flex;
  gap: 12px;
}
.marketing__guide-num {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #fff;
  width: 18px;
  height: 18px;
  color: var(--BI-Dark-Blue-01, #0c6cff);
  font-weight: 700;
  line-height: 18px;
  font-size: 14px;
  margin: 2px 0;
  flex-shrink: 0;
}
.marketing_guide-main {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.marketing__guide-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
}

.marketing__guide-desc {
  font-size: 14px;
  color: inherit;
  line-height: 1.5;
}

.marketing__guide-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 12px;
  min-height: 32px;
  width: 100%;
  position: relative;
}
.guide-step-progress {
  flex: 1;
  text-align: left;
  font-size: 14px;
  color: #fff;
  font-weight: 500;
}
.guide-step-btn-group {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  flex: 1;
}
.marketing__guide-btn {
  flex: 0 0 auto;
  color: #319bf5;
}
.guide-step-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #1e7fd3;
  opacity: 0.4;
  transition: opacity 0.2s, background 0.2s;
}
.guide-step-dot.active {
  background: #95cfff;
  opacity: 1;
}

.guide-close-btn {
  margin-left: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  .fx-icon-close {
    font-size: 16px;
    &::before {
      color: #fff;
    }
  }
}

.marketing__guide-arrow {
  position: fixed;
  background: #319bf5;
  height: 4px;
  border-radius: 2px;
  z-index: 10001;
  pointer-events: none;
}
.marketing__guide-arrow-circle {
  position: fixed;
  width: 24px;
  height: 24px;
  z-index: 10002;
  pointer-events: none;
  display: flex;
  align-items: center;
  justify-content: center;
}
.arrow-outer {
  width: 24px;
  height: 24px;
  background: rgba(49,155,245,0.2);
  border-radius: 50%;
  position: absolute;
  left: 0; top: 0;
}
.arrow-inner {
  width: 12px;
  height: 12px;
  background: #319bf5;
  border-radius: 50%;
  border: 2px solid #fff;
  position: absolute;
  left: 6px; top: 6px;
  box-sizing: border-box;
}

.guide-mask-svg {
  pointer-events: auto;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9998;
}
</style>
