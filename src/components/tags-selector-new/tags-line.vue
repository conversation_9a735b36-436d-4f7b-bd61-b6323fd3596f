<template>
  <div :class="['tags-line', singleLine && 'singleLine']">
    <template v-for="(item, index) in selectedTags">
      <fx-tag
        v-if="!singleLine || index < 3"
        :key="item.id"
        :closable="item.delAllowed === undefined || item.delAllowed === null ? true : item.delAllowed"
        size="small"
        type="info"
        class="tags__item"
        @close="handleTagsRemove(index)"
      >
        {{ item.nameid.replace(':', '：') }}
      </fx-tag>
    </template>
    <fx-tag
      v-if="singleLine && selectedTags.length > 3"
      size="small"
      type="info"
      class="tags__item"
    >
      ...
    </fx-tag>
    <div
      class="tags-line__item--add add_tag"
      @click="handleOpenSelector"
    >
      {{ addText }}
    </div>
    <TagsSelector
      v-if="isShowTagsSelector"
      :visible="isShowTagsSelector"
      :default-tags="selectedTags"
      :type="type"
      @update:visible="isShowTagsSelector = false"
      @change="handleTagsChange"
    />
  </div>
</template>

<script>
import TagsSelector from './tags-selector.vue'

export default {
  components: {
    TagsSelector,
  },
  props: {
    addText: {
      type: String,
      default: () => $t('marketing.commons.tjbq_736eaa'),
    },
    value: {
      type: Array,
      default: () => [],
    },
    singleLine: {
      type: Boolean,
      default: () => false,
    },
    // 只展示某一type的标签
    type: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      isShowTagsSelector: false,
      selectedTags: [],
    }
  },
  computed: {
    models() {
      return this.$store.state.TagsManage.models
    },
  },
  watch: {
    value() {
      // if (this.value.length > 0) {
      this.selectedTags = (this.value || []).map(item => ({
        ...item,
        nameid: item.firstTagName + (item.secondTagName ? `:${item.secondTagName}` : ''),
      }))
      // }
    },
  },
  mounted() {
    if (this.value && this.value.length > 0) {
      this.selectedTags = this.value.map(item => ({
        ...item,
        nameid: item.firstTagName + (item.secondTagName ? `:${item.secondTagName}` : ''),
      }))
    }
  },
  methods: {
    handleOpenSelector() {
      this.isShowTagsSelector = true
    },
    handleTagsChange(selectedTags) {
      this.selectedTags = selectedTags
      this.isShowTagsSelector = false
      this.$emit('input', this.selectedTags, this.value)
    },
    handleTagsRemove(targetIndex) {
      const selectTag = this.selectedTags[targetIndex]
      const models = this.models.filter(model => model.tags.some(tag => {
        if (selectTag.secondTagName) {
          return tag.subTags.some(subTag => {
            if (selectTag.firstTagName === tag.name && selectTag.secondTagName === subTag.name) return true
          })
        }
        return selectTag.firstTagName === tag.name
      }))
      if (models.some(model => model.isOnlyRead)) {
        FxUI.Message.warning($t('marketing.components.tags_selector_new.wcbqscqx_ac31b3'))
        return
      }
      this.selectedTags.splice(targetIndex, 1)
      this.$emit('input', this.selectedTags, this.value)
    },
  },
}
</script>

<style lang="less" scoped>
.tags-line {
  display: flex;
  flex-wrap: wrap;
  &.singleLine {
    flex-wrap: unset;
    .tags__item {
      margin-bottom: 0;
    }
  }
  .tags__item {
    margin: 4px 10px 4px 0;
  }

  .tags-line__item--add {
    display: flex;
    align-items: center;
    line-height: 24px;
    height: 24px;
    border-radius: 3px;
    margin: 6px 10px 4px 0;
    box-sizing: border-box;
    padding-left: 16px;
  }

  .add_tag {
    color: var(--color-info06);
    // border: 1px dashed #b2cae0;
    background: url('../../assets/images/icon/icon-btn-add-blue.png') 8px center / 14px no-repeat;
    background-position: 0;
    font-size: 12px;
    cursor: pointer;
    white-space: nowrap;
    // &:hover {
    //   border: 1px dashed #2c89de;
    //   color: #2c89de;
    // }
  }
}
</style>
