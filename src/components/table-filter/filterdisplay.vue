<template>
  <div :class="$style.table__filterdisplay">
    <div
      :class="[$style.filterwrap, showMore && $style.filterwrapSingle]"
      v-if="filterPreviewData.length || tagPreviewData.length"
    >
      <div :class="[$style.filterList, showMore && $style.filterListSingle]">
        <span v-if="tagPreviewData && tagPreviewData.length" :class="$style.filterTips">{{tagOperatorTips}}：</span>
        <span
          v-for="(item, index) in tagPreviewData"
          :key="index"
          :class="$style.filter"
        >
          {{ item.nameid }}
          <span v-if="!isPreview" :class="$style.icon" @click="handleTagsRemove(index)"></span>
        </span>
        <span
          v-for="(label, index) in filterPreviewData"
          :key="index"
          :class="$style.filter"
        >
          <span class="km-t-ellipsis1">{{ label }}</span>
          <span v-if="!isPreview" :class="$style.icon" @click="handleFilterRemove(index)"></span>
        </span>
      </div>
      <div v-if="!isPreview && isShowClearAll" :class="$style.operation">
        <!-- <span
          v-if="filterPreviewData.length"
          @click="usergroupDialogVisible = true"
          >存为目标人群</span
        > -->
        <span style="margin-left: 20px;" @click.stop="handleFilterClear"
          >{{ $t('marketing.components.table_filter.qcqbtj_7ce9bc') }}</span
        >
      </div>
      <div v-if="showMore" :class="$style.moreBtn" @click.stop="handleFilterMore">
        <span>...</span>
      </div>
    </div>
    <TargetPopulationCreate
      :ruleGroupJson="value.filters"
      :ruleGroupValue="ruleGroupValue"
      :visible.sync="usergroupDialogVisible"
    />
  </div>
</template>
<script>
import { getFilterPreviewData } from "@/utils/filter-helper";
import TargetPopulationCreate from "@/pages/TargetPopulation/TargetPopulationCreate";

export default {
  components: {
    TargetPopulationCreate
  },
  props: {
    value: {
      type: Object,
      default: () => ({
        filters: [],
        tags: [],
        tagOperator: 'LIKE',
      })
    },
    isPreview: {
      type: Boolean,
      default: false,
    },
    // 是否展示清楚全部  全员推广场景下不展示
    isShowClearAll: {
      type: Boolean,
      default: true,
    },
    showMore: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      usergroupDialogVisible: false,
      filterPreviewData: [],
      tagPreviewData: [],
    };
  },
  computed: {
    ruleGroupValue() {
      return this.filterPreviewData.join($t('marketing.commons.q_1dea3a'));
    },
    tagOperatorTips() {
      const tagOperatorObj = {
        LIKE: $t('marketing.commons.bhyxsybq_a99dd2'),
        HASANYOF: $t('marketing.commons.bhyxrybq_f86759'),
      }
      return tagOperatorObj[this.value.tagOperator] || ''
    },
  },
  watch: {
    value: {
      deep: true,
      handler() {
        this.filterParse();
      }
    }
  },
  methods: {
    queryFilterPreviewData(filters) {
      const promiseHandler = filters.map(
        rule =>
          new Promise(async (resolve, reject) => {
            const data = await getFilterPreviewData(rule);
            resolve(data);
          })
      );
      return Promise.all(promiseHandler);
    },
    filterPreviewDataParse(data = []) {
      const head = data[0] || {};
      return _.map(head.filter, item => {
        return `${head.displayApiName}.${item.join(" ")}`;
      });
    },
    async filterParse() {
      const { filters = [], tags = [] } = this.value;
      if (filters && filters.length) {
        const data = await this.queryFilterPreviewData(filters);
        this.filterPreviewData = this.filterPreviewDataParse(data);
      } else {
        this.filterPreviewData = [];
      }
      this.tagPreviewData = tags || [];
    },
    handleFilterRemove(index) {
      let { filters = [], tags = [] } = this.value || {};
      if (_.isEmpty(filters)) return;
      this.filterPreviewData.splice(index, 1);
      filters[0].query.filters.splice(index, 1);
      filters = [].concat(filters);
      if (_.isEmpty(filters[0].query.filters)) {
        filters = [];
        this.filterPreviewData = [];
      }
      this.$emit("input", {
        filters,
        tags,
        tagOperator: this.value.tagOperator,
      });
    },
    handleTagsRemove(index) {
      let { filters = [], tags = [] } = this.value || {};
      tags.splice(index, 1);
      this.$emit("input", {
        filters,
        tags,
        tagOperator: this.value.tagOperator,
      });
    },
    handleFilterClear() {
      this.filterPreviewData = [];
      this.tagPreviewData = [];
      this.$emit("input", {
        filters: [],
        tags: [],
        tagOperator: this.value.tagOperator,
      });
      this.$emit("reset");
    },
    handleFilterMore() {
      this.$emit("filterMore");
    }
  },
  created() {
    this.filterParse();
  }
};
</script>
<style lang="less" module>
@basePath: "../../assets/images/";
.table__filterdisplay {
  .filterwrap {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 4px;
    background: #ffffff;
    &Single{
      position: relative;
      padding-right: 30px;
    }
    .filterList{
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      &Single {
        max-height: 32px;
        overflow: hidden;
        position: relative;
        .filter{
          flex: 1 !important;
        }
      }
    }
    .filterTips {
      margin-left: 12px;
      font-size: 14px;
      color: #212b36;
    }
    .filter {
      flex: none;
      display: inline-flex;
      align-items: center;
      padding: 4px 10px;
      color: #212b36;
      background: rgba(225, 233, 250, 0.92);
      border-radius: @border-radius-base;
    }
    .icon {
      display: inline-block;
      height: 14px;
      width: 14px;
      vertical-align: middle;
      background: url("@{basePath}icon/close-icon.png") center / cover no-repeat;
      cursor: pointer;
    }
  }
  .operation {
    flex: none;
    margin-left: 26px;
    font-size: 13px;
    color: var(--color-info06,#407FFF);
    cursor: pointer;
  }
  .moreBtn{
    position: absolute;
    top: 50%;
    right: 0;
    width: 22px;
    height: 22px;
    margin: 0 4px;
    text-align: center;
    line-height: 22px;
    background-color: #eff3fc;
    color: #181c25;
    font-weight: 700;
    cursor: pointer;
    transform: translateY(-50%);
    z-index: 1;
  }
}
</style>
