<template>
  <Dialog
    :class="$style.select_material_dialog"
    :title="dialogTitle"
    :width="showLeftMenu ? '922px' : '760px'"
    :visible="visible"
    :z-index="curIndex"
    @onSubmit="handleSubmit"
    @onClose="handleClose"
  >
    <!-- width="748px" -->
    <div
      v-if="inputName"
      :class="$style.name"
    >
      <div :class="$style.label">
        <em>*</em>{{ $t('marketing.components.select_material_dialog.ljbt_39bb2a') }}
      </div>
      <Input
        v-model="name"
        :size="size"
        :class="$style.name_input"
        :placeholder="$t('marketing.components.select_material_dialog.qsrxzn_6794b9')"
        :has-form-item="false"
        maxlength="30"
      />
    </div>
    <div :class="$style.main">
      <div
        v-if="showLeftMenu"
        :class="$style.main__left"
      >
        <div
          v-for="menu in menuObjs"
          :key="menu.id"
          :class="[$style.menu__item, model_curMenuId === menu.id && $style.cur]"
          @click="handleMenuChange(menu.id)"
        >
          {{ menu.label }}
          <div
            v-if="menu.id === 'marketingEvent' && marketingEventName"
            :class="[$style.item__desc]"
          >
            <div
              :class="$style.marketingEventName"
              :title="$t('marketing.commons.xzschd_dd8f78')"
              @click="handleMarketingEventObjPicker"
            >
              {{ marketingEventName }}
              <div
                v-if="menu.id === 'marketingEvent' && marketingEventId && !marketingEventName"
                :class="$style.item__descLoading"
              >
                <div :class="$style.loadingSpinner">
                  <svg
                    viewBox="25 25 50 50"
                    :class="$style.circular"
                    autocomplete="off"
                  >
                    <circle
                      cx="50"
                      cy="50"
                      r="20"
                      fill="none"
                      :class="$style.path"
                      autocomplete="off"
                    />
                  </svg>
                </div>
              </div>
            </div>
            <i
              v-if="!disabledSelectEvent"
              :class="[$style.icon, 'iconfont iconarrow']"
              :title="$t('marketing.commons.xzschd_dd8f78')"
              @click="handleMarketingEventObjPicker"
            />
          </div>
        </div>
      </div>
      <div :class="$style.main__right">
        <div
          v-if="model_curMenuId === 'marketingEvent' && !marketingEventId"
          :class="$style.right__tips"
        >
          <fx-link
            type="standard"
            @click="handleMarketingEventObjPicker"
          >
            {{ $t('marketing.commons.qxxzschd_493166') }}
          </fx-link>
        </div>
        <Tabs
          v-else
          v-model="activeType"
          :class="isHideTabHeader && $style.hidetabs"
          @tab-click="handleTabChange"
        >
          <TabPane
            v-for="item in tabs"
            :key="item.type"
            :label="item.title"
            :name="item.type"
          >
            <div
              v-loading="loading"
              :class="$style.material_wrap"
            >
              <div :class="$style.search">
                <GroupDropdown
                  v-if="getGroupObjectType(item.type) && model_curMenuId !== 'marketingEvent'"
                  :active-group-id="siteGroupId"
                  :object-type="getGroupObjectType(item.type)"
                  :extra-params="groupExtraParams(item.type)"
                  size="small"
                  @change="(data) => handleGroupDropDownSelected(data, item)"
                />
                <div v-else />
                <div>
                  <el-button
                    type="text"
                    size="small"
                    :loading="loading"
                    style="margin-right: 10px"
                    @click="handleRefresh"
                  >
                    <i class="el-icon-refresh-left" />
                    <span>{{ $t('marketing.commons.sx_694fc5') }}</span>
                  </el-button>
                  <Input
                    v-model="searchKeyword"
                    size="small"
                    class="search-input"
                    type="search"
                    :placeholder="$t('marketing.commons.ssbt_b1140e')"
                    prefix-icon="el-icon-search"
                    :has-form-item="false"
                    @change="handleSearch"
                  />
                </div>
              </div>
              <div :class="$style.lists">
                <div
                  v-if="isHasAddButton && tabMaps[activeType].link && activeType != 24"
                  :class="[$style.item, $style.add_item, activeType == 24 && $style.poster_item]"
                >
                  <div
                    v-if="activeType == 1"
                    :class="[$style.add_con, $style.add_article]"
                  >
                    <div :class="$style.addbutton">
                      <!-- <span :class="$style.dropdown_link" @click="() => handleAddMaterial(activeType, '1')">
                        <i class="yxt-icon16 icon-add-b" />
                        <span>{{ $t('marketing.commons.xjwz_0af994') }}</span>
                      </span> -->
                      <Dropdown
                        trigger="click"
                        @command="(command) => handleAddMaterial(activeType, command)"
                      >
                        <span :class="$style.dropdown_link">
                          <i class="yxt-icon16 icon-add-b" />
                          <span>{{ $t('marketing.commons.xjwz_0af994') }}</span>
                        </span>
                        <DropdownMenu slot="dropdown">
                          <DropdownItem command="1">
                            {{ $t('marketing.commons.zjwz_e0e0d0') }}
                          </DropdownItem>
                          <DropdownItem command="2">
                            {{ $t('marketing.commons.tjgzhwz_b080f3') }}
                          </DropdownItem>
                        </DropdownMenu>
                      </Dropdown>
                    </div>
                  </div>
                  <div
                    v-else
                    :class="$style.add_con"
                  >
                    <div
                      :class="$style.addbutton"
                      @click="handleAddMaterial(activeType)"
                    >
                      <i class="yxt-icon16 icon-add-b" />
                      <span>{{
                        $t('marketing.commons.xj_8b0ecd', { data: { option0: tabMaps[activeType].title } })
                      }}</span>
                    </div>
                  </div>
                </div>
                <!-- activeType：24 海报 -->
                <template v-if="activeType == 24">
                  <div
                    v-for="item in lists"
                    :key="item.id"
                    :class="[
                      $style.item,
                      $style.poster_item,
                      item.id === selected && $style.selected,
                      multiSelected.findIndex((mitem) => mitem.id === item.id) > -1 && $style.selected,
                    ]"
                    @click="handleSelectMaterial(item)"
                  >
                    <v-image
                      :class="$style.image"
                      size="100%"
                      :image-url="item.image"
                      :poster="true"
                    />
                    <div :class="[$style.title, 'km-t-ellipsis1']">
                      {{ item.title }}
                    </div>
                    <span
                      v-if="item.top"
                      :class="$style.top"
                    >{{ $t('marketing.commons.zd_3d43ff') }}</span>
                  </div>
                </template>
                <template v-else>
                  <div
                    v-for="item in lists"
                    :key="item.id"
                    :class="[
                      $style.item,
                      item.id === selected && $style.selected,
                      multiSelected.findIndex((mitem) => mitem.id === item.id) > -1 && $style.selected,
                      (activeType == 25 || activeType == 3) && $style.active_item,
                    ]"
                    @click="handleSelectMaterial(item)"
                  >
                    <template v-if="activeType == 8">
                      <div
                        :class="$style.image"
                        style="display: flex; align-items: center; justify-content: center;"
                      >
                        <i
                          :class="['iconfont', item.fileIcon.iconfont]"
                          :style="{
                            color: item.fileIcon.color,
                            fontSize: '80px',
                          }"
                        />
                      </div>
                    </template>
                    <v-image
                      v-else
                      :class="$style.image"
                      size="100%"
                      :image-url="item.image"
                    />
                    <div :class="[$style.title, 'km-t-ellipsis2']">
                      {{ item.title }}
                    </div>
                    <div :class="$style.time">
                      {{ item.time }}
                    </div>
                    <span
                      v-if="item.top"
                      :class="$style.top"
                    >{{ $t('marketing.commons.zd_3d43ff') }}</span>
                  </div>
                </template>
                <div
                  v-if="!lists.length"
                  :class="$style.empty"
                >
                  <div :class="$style.empty_image" />
                  <div
                    :class="$style.tip"
                    :style="`padding-bottom: ${tabMaps[activeType].link ? 0 : 20}px`"
                  >
                    {{ $t('marketing.commons.mysj_1ac0e1') }}
                  </div>
                  <a
                    v-if="tabMaps[activeType].link && activeType != 24"
                    :class="$style.empty_newadd"
                  >
                    <Dropdown
                      v-if="activeType == 1"
                      trigger="click"
                      @command="(command) => handleAddMaterial(activeType, command)"
                    >
                      <span :class="$style.dropdown_link">
                        <span>{{ $t('marketing.commons.xjwz_0af994') }}</span>
                      </span>
                      <DropdownMenu slot="dropdown">
                        <DropdownItem command="1">{{ $t('marketing.commons.zjwz_e0e0d0') }}</DropdownItem>
                        <DropdownItem command="2">{{ $t('marketing.commons.tjgzhwz_b080f3') }}</DropdownItem>
                      </DropdownMenu>
                    </Dropdown>
                    <!-- <span
                      v-if="activeType == 1"
                      :class="$style.dropdown_link"
                      @click="() => handleAddMaterial(activeType, '1')"
                    >
                      <span>{{ $t('marketing.commons.xjwz_0af994') }}</span>
                    </span> -->
                    <span
                      v-else
                      @click="handleAddMaterial(activeType)"
                    >{{
                      $t('marketing.commons.xj_8b0ecd', { data: { option0: tabMaps[activeType].title } })
                    }}</span>
                  </a>
                </div>
              </div>
              <v-pagen
                :pagedata.sync="pageData"
                @change="handlePageChange"
              />
            </div>
          </TabPane>
        </Tabs>
      </div>
    </div>
    <div
      v-if="isMultiSelect"
      slot="footer__left"
      :class="$style.dialog__footer"
    >
      {{ $t('marketing.commons.yxg_b79bcf', { data: { option0: multiSelected.length } }) }}
    </div>
    <article-create-dialog
      v-if="showArticleCreateDialog"
      :visible.sync="showArticleCreateDialog"
      :open-in-new-window="true"
      :fromid="dialogId"
    />
    <qrposter-dialog
      v-if="showCreateQrposterDialog"
      :visible.sync="showCreateQrposterDialog"
      :marketing-event-id="marketingEventId"
      :marketing-event-name="marketingEventName"
      @created="handleQrposterCreated"
    />
  </Dialog>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import Dialog from '@/components/dialog/index.vue'
import VImage from '@/components/image/index.vue'
import VPagen from '@/components/kitty/pagen.vue'
import ArticleCreateDialog from '@/pages/article/create/pa-dialog.vue'
import QrposterDialog from '@/pages/poster-gallery/create/qrposter.vue'
import material from './material.js'
import kisvData from '@/modules/kisv-data.js'
import http from '@/services/http/index.js'
import { requireAsync } from '@/utils/index.js'
import appmarketingRPC from '@/utils/appmarketing.rpc.js'
import GroupDropdown from '@/components/group-manage-new/group-dropdown.vue'
import { getTopZIndex } from '@/utils/helper.js'
// marketingEvent：市场活动  materials：素材库
const MENUS = ['materials']
// 1: 文章   3：活动邀请  4:产品 24:海报  16:表单 3:会议 25:邀请函
const TABBAR = [1, 4]
/**
 * 获取素材详情
 * @parmas {type, objectId}
 * @event finish 快捷入口新建成功回调
 */
export const MaterialInstance = material()
const MARKETINGEVENTTABBAR = [1, 4, 24, 3]

export default {
  components: {
    Dropdown: FxUI.Dropdown,
    DropdownMenu: FxUI.DropdownMenu,
    DropdownItem: FxUI.DropdownItem,
    Dialog,
    VImage,
    VPagen,
    ArticleCreateDialog,
    QrposterDialog,
    Tabs: FxUI.Tabs,
    TabPane: FxUI.TabPane,
    Input: FxUI.Input,
    GroupDropdown,
  },
  props: {
    title: {
      type: String,
      default: '',
    },
    menus: {
      type: Array,
      default: () => MENUS, // marketingEvent：市场活动  materials：素材库
    },
    /**
     * 可选项 默认都显示
     * @params 1: 文章   3：活动邀请  4:产品
     */
    tabbar: {
      type: Array,
      default: () => TABBAR,
    },
    marketingEventTabbar: {
      type: Array,
      default: () => MARKETINGEVENTTABBAR,
    },
    visible: {
      type: Boolean,
      default: false,
    },
    inputName: {
      type: Boolean,
      default: false,
    },
    selectId: {
      type: String,
      default: '',
    },
    // 营销活动ID
    marketingEventId: {
      type: String,
      default: '',
    },
    includeHexagonForm: {
      type: Boolean,
      default: false,
    },
    // 是否多选
    isMultiSelect: {
      type: Boolean,
      default: false,
    },
    initMultiSelected: {
      type: Array,
      default: () => [],
    },
    // 是否是选择活动中心下的内容
    isActivity: {
      type: Boolean,
      default: false,
    },
    isSubmitHide: {
      type: Boolean,
      default: true,
    },
    disabledSelectEvent: {
      type: Boolean,
      default: false,
    },
    ispartner: {
      type: Boolean,
      default: false,
    },
    //微页面过滤不关联表单的内容，只对内容中心下内容生效
    siteFilterNoRelationFormId: {
      type: Boolean,
      default: false,
    },
    // 输入框尺寸
    size: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      // 弹窗ID
      dialogId: +new Date().getTime(),
      loading: false,
      showArticleCreateDialog: false,
      vDatas: kisvData.datas,
      menuMaps: {},
      menuObjs: [],
      model_curMenuId: '',
      tabMaps: {},
      tabs: [],
      activeType: '1',
      lists: [],
      pageData: {
        layout: 'prev, pager, next, jumper',
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      selected: this.selectId,
      multiSelected: this.initMultiSelected,
      name: '',
      marketingEventName: '',
      loading_marketingEventName: false,
      showCreateQrposterDialog: false,
      searchKeyword: '',
      siteGroups: [],
      siteGroupId: '',
      realMarketingEventTabbar: [],
    }
  },
  computed: {
    // 是否显示新建入口
    ...mapGetters('MarketingEventSet', ['marketSetFilters']),
    isHasAddButton() {
      return this.pageData.pageNum == 1 && this.activeType != 3 && this.lists.length
    },
    isHideTabHeader() {
      return this.tabs.length <= 1
    },
    stateType() {
      return this.$store.state.type
    },
    showLeftMenu() {
      // 选择市场活动时候不显示左侧菜单
      return (this.menuObjs.length > 1 && !this.isActivity)
    },
    // 选择器标题
    dialogTitle() {
      // 有传入标题时，则以传入的为准
      if (this.title) {
        return this.title
      }
      // 只有一个tab时标题默认为当前tab名
      if (this.tabs.length === 1) {
        let title = $t('marketing.commons.xznr_bcbe25')
        switch (this.activeType - 0) {
          case 1:
            title = $t('marketing.commons.xzwz_b72fad')
            break
          case 3:
            title = $t('marketing.commons.xzhy_a2a320')
            break
          case 4:
            title = $t('marketing.commons.xzcp_cbf1cf')
            break
          case 10:
            title = $t('marketing.components.select_material_dialog.xzwym_aaf6ce')
            break
          case 16:
            title = $t('marketing.commons.xzbd_13a278')
            break
          case 24:
            title = $t('marketing.commons.xzhb_01f39a')
            break
          case 25:
            title = $t('marketing.commons.xzyqh_f879c7')
            break
          case 8:
            title = $t('marketing.commons.xzwj_fd7e0c')
            break
          default:
            break
        }
        return title
      }
      // 选择市场活动
      if (this.isActivity) {
        return $t('marketing.commons.xzhd_a05f05')
      }
      // 默认标题
      return $t('marketing.commons.xznr_bcbe25')
    },
    curIndex() {
      if (this.visible) {
        return getTopZIndex()
      }
      return 500
    },
    ...mapState('MarketingEventSet', ['marketingEventAudit']),
  },
  watch: {
    model_curMenuId() {
      this.initTabs()
    },
    selectId() {
      this.selected = this.selectId
    },
  },
  created() {
    this.material = material()
    this.initMenus()
  },
  mounted() {
    // this.removeVisibilityChange = this.bindVisibilityChange();
    this.queryMarketingEventDetail()
  },
  beforeDestroy() {
    if (this.removeStorageEventHandler) {
      this.removeStorageEventHandler()
    }
    // this.removeVisibilityChange();
  },
  methods: {
    getGroupObjectType(tabType) {
      const _map = {
        1: 6,
        4: 4,
        10: 26,
        24: 24,
        2: 7,
      }
      return _map[tabType] || null
    },
    groupExtraParams(tabType) {
      return tabType === '2' ? { source: 2 } : {}
    },
    handleGroupDropDownSelected(data, item) {
      if (item.type !== this.activeType) return
      this.siteGroupId = data.groupId || '-1'
      this.pageData.pageNum = 1
      this.queryLists()
    },
    handleMarketingEventObjPicker() {
      if (this.disabledSelectEvent) return
      requireAsync('crm-modules/components/pickselfobject/pickselfobject', PickObject => {
        const picker = new PickObject()
        picker.on('select', value => {
          this.marketingEventId = value._id
          this.marketingEventName = value.name
          picker.destroy()
          this.handleRefresh()
        })

        picker.render({
          layout_type: 'list',
          include_layout: false,
          apiname: 'MarketingEventObj',
          dataId: this.marketingEventId,
          zIndex: 3000,
          filters: [...this.marketSetFilters],
        })
      })
    },
    initMenus() {
      // 排除不支持在市场活动下的物料类型
      this.realMarketingEventTabbar = this.marketingEventTabbar.filter(type => this.material.suportMarketingEventTabTypes.indexOf(type) !== -1)
      // 市场活动下物料tab为空时，不显示市场活动菜单
      const menus = this.realMarketingEventTabbar.length ? this.menus : this.menus.filter(type => type !== 'marketingEvent')

      // 如果没有市场活动id，把市场活动菜单放置到最后
      if (!this.marketingEventId && menus[0] === 'marketingEvent') {
        const first = menus.shift()
        menus.push(first)
      }
      // 初始化menus相关内容
      this.menuMaps = this.material.menuMaps
      // 多会场活动不展示左侧的菜单
      this.menuObjs = this.material.getMenuObjs(menus, this.vDatas.status.crmark, this.isActivity) // crmark：是否不为kis企业 true：不为kis企业 false：为kis企业   kis企业没有市场活动
      this.model_curMenuId = this.menuObjs[0].id
    },
    async initTabs() {
      this.loading = true
      this.lists = []
      const tabbar = this.model_curMenuId === 'marketingEvent' ? this.realMarketingEventTabbar : this.tabbar
      // 初始化tabs相关内容
      this.tabMaps = this.material.apiMaps
      // tab为空或者不传取默认显示规则
      const tabs = tabbar && tabbar.length ? tabbar : []
      this.tabs = this.material.getTabs(tabs)
      this.activeType = String(tabs[0])
      if (this.model_curMenuId === 'marketingEvent' && !this.marketingEventId) {
        return
      }
      if (this.model_curMenuId === 'marketingEvent' || !this.getGroupObjectType(this.activeType)) {
        this.queryLists()
      }
    },
    bindVisibilityChange() {
      const _this = this
      const _hander = function () {
        if (document.visibilityState === 'visible') {
          _this.queryLists(true)
        }
      }
      document.addEventListener('visibilitychange', _hander)
      return function () {
        document.removeEventListener('visibilitychange', _hander)
      }
    },
    bindStorageEvent(callback) {
      const _hander = function (ev) {
        callback.call(this, ev)
      }
      window.addEventListener('storage', _hander)
      return function () {
        window.removeEventListener('storage', _hander)
      }
    },
    handleMenuChange(menuId) {
      this.pageData.pageNum = 1
      this.pageData.totalCount = 0
      this.searchKeyword = ''
      this.model_curMenuId = menuId
    },
    async handleTabChange() {
      this.pageData.pageNum = 1
      this.pageData.totalCount = 0
      this.searchKeyword = ''
      this.lists = []
      this.siteGroupId = this.activeType === '2' ? '-2' : '-1'
      this.queryLists()
    },
    async queryMarketingEventDetail() {
      if (!this.marketingEventId || (this.marketingEventId && this.marketingEventName)) return
      this.loading_marketingEventName = true
      FS.util.FHHApi({
        url: '/EM1HNCRM/API/v1/object/MarketingEventObj/controller/SimpleDetail',
        data: {
          isFromRecycleBin: false,
          objectDataId: this.marketingEventId,
          objectDescribeApiName: 'MarketingEventObj',
        },
        success: res => {
          if (!res.Value || res.Value.errCode) return
          this.loading_marketingEventName = false
          const obj = res.Value.data || {}
          this.marketingEventName = obj.name
        },
      })
    },
    handleSiteGroupChange() {
      this.pageData.pageNum = 1
      this.queryLists()
    },
    async queryLists(flag) {
      !flag && (this.loading = true)
      const searchKeyword = this.searchKeyword || undefined
      const params = {
        id: this.model_curMenuId === 'marketingEvent' ? this.marketingEventId : '',
        type: this.activeType == 24 ? 1 : this.stateType,
        pageNum: this.pageData.pageNum,
        pageNo: this.pageData.pageNum,
        pageSize: this.pageData.pageSize,
        searchFitter: searchKeyword,
        name: searchKeyword,
        title: searchKeyword,
        searchKey: searchKeyword,
        keyword: searchKeyword,
        // 表单是否挂微页面表单
        ...((this.activeType == 16 && {
          includeHexagonForm: this.includeHexagonForm,
        })
          || {}),
      }
      if (['1', '4', '10', '24', '2'].includes(this.activeType) && this.siteGroupId) {
        params.groupId = this.siteGroupId
      } else {
        params.groupId = null
      }

      // 根据设置，过滤审核中的会议，如果开启了审核
      if (this.marketingEventAudit && ['3', '30'].includes(this.activeType)) {
        params.filterData = {
          objectAPIName: 'MarketingActivityObj',
          query: {
            filters: [
              {
                fieldName: 'life_status',
                fieldValues: ['normal'],
                operator: 'EQ',
              },
            ],
            orders: [
              {
                fieldName: 'last_modified_time',
                isAsc: false,
              },
            ],
          },
        }
      }

      if (this.isActivity) {
        params.isShowSpread = true
      }

      //微页面列表是否要过滤不关联表单的数据
      if(this.siteFilterNoRelationFormId && this.activeType == 10){
        params.hasFormId = true;
      }

      const results = await this.material.fetch(this.activeType, params).then(({ success, total, lists }) => {
        if (success) {
          this.pageData.totalCount = total
          if (this.model_curMenuId === 'marketingEvent') {
            return lists.map(item => ({
              ...item,
              marketingEventId: this.marketingEventId,
            }))
          }

          return lists
        }
        return []
      })
      this.loading = false

      // 伙伴营销无聊选择器过滤掉了企微活码海报
      if (this.ispartner && this.activeType === '24') {
        this.lists = results.filter(item => item.qrPostForwardType !== 13)
        return
      }
      this.lists = results
    },
    handleSearch() {
      this.pageData.pageNum = 1
      this.pageData.totalCount = 0
      this.queryLists()
    },
    handleRefresh() {
      this.pageData.pageNum = 1
      this.pageData.totalCount = 0
      this.searchKeyword = ''
      this.queryLists()
    },
    handleSelectMaterial(row) {
      if (this.isMultiSelect) {
        this.handleMultiSelectMaterial(row)
      } else {
        this.selected = row.id
        this.name = row.title || row.name
      }
    },
    handleMultiSelectMaterial(row) {
      this.multiSelected = this.multiSelected || []
      const index = this.multiSelected.findIndex(item => item.id === row.id)
      if (index > -1) {
        this.multiSelected.splice(index, 1)
      } else {
        this.multiSelected.push(row)
      }
      console.log(this.multiSelected.map(item => item.title))
    },
    handlePageChange({ pageNum }) {
      this.pageData.pageNum = pageNum
      this.queryLists()
    },
    handleSubmit() {
      if (this.isMultiSelect) {
        this.submitMultiSelect()
      } else {
        this.submitSelected()
      }
      this.isSubmitHide && this.$emit('onClose')
    },
    submitMultiSelect() {
      if (this.pageData.totalCount > 0 || this.lists.length) {
        if (!this.multiSelected || this.multiSelected.length === 0) {
          FxUI.Message.warning($t('marketing.commons.qxzsc_b4c71c'))
          return
        }
        this.$emit(
          'onSubmit',
          this.multiSelected.map(item => ({
            ...item,
            name: item.title || item.name,
          })),
          this.activeType,
        )
      }
    },
    submitSelected() {
      if (this.pageData.totalCount > 0 || this.lists.length) {
        if (this.inputName && !this.name) {
          FxUI.Message.warning($t('marketing.commons.qsrljwb_363cec'))
          return
        }
        if (!this.selected) {
          FxUI.Message.warning($t('marketing.commons.qxzsc_b4c71c'))
          return
        }
        const result = this.lists.filter(item => item.id === this.selected)
        this.$emit(
          'onSubmit',
          result && result.length
            ? {
              type: this.activeType,
              ...result[0],
              name: this.name,
              commonMarketingEventId: this.model_curMenuId === 'marketingEvent' ? this.marketingEventId : '',
            }
            : {},
          this.activeType,
        )
      }
    },
    handleClose() {
      this.$emit('onClose')
    },
    handleQrposterCreated() {
      this.queryLists(true)
    },
    addMaterialToMarketing(data) {
      http
        .addMaterial({
          marketingEventId: this.marketingEventId,
          objectId: data.id,
          objectType: MaterialInstance.TYPE_MAPS[data.type] || data.type,
        })
        .then(({ errCode, errMsg }) => {
          if (errCode === 0) {
            this.queryLists(true)
          }
        })
    },
    handleAddMaterial(type, createType) {
      if (this.vDatas.strategy == 'dingTalk') return
      // watch 新建成功后回调
      if (this.removeStorageEventHandler) {
        this.removeStorageEventHandler()
      }
      this.removeStorageEventHandler = this.bindStorageEvent(ev => {
        if (ev.key === 'APP_MARKETING_RPC') {
          const result = appmarketingRPC.get(this.dialogId)
          if (result) {
            // 如果在市场活动下新建素材则自动关联
            if (this.model_curMenuId === 'marketingEvent') {
              this.addMaterialToMarketing(result)
            } else {
              this.queryLists(true)
            }
            this.$emit('material:add', result)
          }
        }
      })

      if (createType == 2) {
        this.showArticleCreateDialog = true
        return
      } if (type == 24) {
        this.showCreateQrposterDialog = true
        return
      }
      const { link } = this.tabMaps[type] || {}

      console.log(type, 666, this.marketingEventId)
      if (link) {
        (link.query || (link.query = {})).fromid = this.dialogId

        // 微页面创建有市场活动时属于创建专属微页面
        if (this.marketingEventId && [1, 4, 6, 10, 26].some(i => i == type)) {
          (link.query || (link.query = {})).marketingEventId = this.marketingEventId
        }
        const route = this.$router.resolve(link)
        window.open(route.href, '_blank')
      }
    },
  },
}
</script>

<style lang="less" module>

.select_material_dialog {
  .name {
    display: flex;
    align-items: center;
    margin-top: 10px;
    padding: 10px 28px 15px 20px;
    border-bottom: 1px solid @border-color-base;
    .label {
      width: 80px;
      font-size: 13px;
      color: @color-title;
      margin-right: 10px;
      em {
        color: @color-error;
      }
    }
    .name_input {
      flex: 1;
    }
  }
  .main {
    display: flex;
    .main__left {
      width: 161px;
      border-right: 1px solid #e9edf5;
      padding-top: 10px;
      flex-shrink: 0;
      .menu__item {
        height: 80px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        // align-items: center;
        color: #181c25;
        margin-bottom: 1px;
        cursor: pointer;
        padding: 0 15px;
        &.cur,
        &:hover {
          background: #f0f4fc;
        }
        .item__desc {
          color: #91959e;
          font-size: 12px;
          display: flex;
          align-items: center;
          margin-top: 3px;
          // .el-loading-mask {
          //   background: #f0f4fc;
          // }
          // .el-loading-spinner {
          //   margin-top: -6px;
          // }
          // .circular {
          //   height: 12px;
          //   width: 12px;
          // }
          .marketingEventName {
            min-width: 0;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
          }
          .icon {
            color: @color-link;
            font-size: 12px;
            margin-left: 3px;
          }
        }
        .item__descLoading {
          position: relative;
          height: 20px;
          width: 20px;
          margin-top: -5px;
          .loadingSpinner {
            top: 50%;
            margin-top: -6px;
            width: 100%;
            text-align: center;
            position: absolute;
            .circular {
              height: 12px;
              width: 12px;
              -webkit-animation: loading-rotate 2s linear infinite;
              animation: loading-rotate 2s linear infinite;
            }
            .path {
              -webkit-animation: loading-dash 1.5s ease-in-out infinite;
              animation: loading-dash 1.5s ease-in-out infinite;
              stroke-dasharray: 90, 150;
              stroke-dashoffset: 0;
              stroke-width: 2;
              stroke: #407fff;
              stroke-linecap: round;
            }
          }
        }
        @keyframes loading-rotate {
          to {
            transform: rotate(1turn);
          }
        }

        @keyframes loading-dash {
          0% {
            stroke-dasharray: 1, 200;
            stroke-dashoffset: 0;
          }

          50% {
            stroke-dasharray: 90, 150;
            stroke-dashoffset: -40px;
          }

          to {
            stroke-dasharray: 90, 150;
            stroke-dashoffset: -120px;
          }
        }
      }
    }
    .main__right {
      flex: 1;
      padding-top: 20px;
      position: relative;

      :global {
        .el-tabs__nav-wrap::after {
          z-index: 4;
        }
      }

      .group_selector {
        :global {
          .el-input__validateIcon {
            display: none;
          }
        }
      }
      // .search {
      //   position: absolute;
      //   right: 28px;
      //   top: 9px;
      //   z-index: 1;
      // }
      .search {
        display: flex;
        justify-content: space-between;
        position: sticky;
        top: 0;
        background: #fff;
        // margin: 15px 0 -5px 0;
        padding: 15px 10px;
        z-index: 1;
      }
      .right__tips {
        height: 527px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
  .material_wrap {
    padding: 0 10px;
    // height: 473px;
    height: 60vh;
    min-height: 231px;
    overflow-y: auto;
    position: relative;
  }
  .lists {
    padding-bottom: 10px;
    // width: 720px;
    font-size: 0;
    .item {
      display: inline-block;
      vertical-align: top;
      width: 220px;
      height: 224px;
      margin: 20px 10px 0;
      border-radius: 3px;
      border: 1px solid @border-color-base;
      cursor: pointer;
      box-sizing: border-box;
      overflow: hidden;
      position: relative;
      &.selected {
        .righticon(25px);
        border-color: var(--color-primary06);
        // &::before {
        //   content: '\00a0';
        //   display: inline-block;
        //   border: 2px solid #fff;
        //   border-top-width: 0;
        //   border-right-width: 0;
        //   width: 9px;
        //   height: 5px;
        //   transform: rotate(-50deg);
        //   position: absolute;
        //   top: 2px;
        //   left: 2px;
        //   z-index: 3;
        // }
        // &::after {
        //   content: '';
        //   position: absolute;
        //   top: 0;
        //   left: 0;
        //   width: 0;
        //   height: 0;
        //   border-top: 25px solid var(--color-primary06);
        //   border-right: 25px solid transparent;
        // }
      }
      .time {
        font-size: 12px;
        color: @color-subtitle;
        padding: 0 13px;
        // margin-top: 6px;
      }
      &.active_item {
        height: 236px;
        .time {
          padding: 9px 13px;
          line-height: 15px;
        }
      }
      &.poster_item {
        width: 220px;
        height: 437px;
        overflow: hidden;
        .image {
          height: 392px !important;
        }
      }
      .image {
        width: 100%;
        height: 130px !important;
      }
      .title {
        font-size: 14px;
        color: @color-title;
        margin-top: 12px;
        padding: 0 13px;
        height: 42px;
      }
    }
    .add_item {
      cursor: inherit;
      .add_con {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        &.add_article {
          flex-direction: column;
          .addbutton {
            display: block;
            span {
              display: inline;
            }
          }
        }
      }
      .addbutton {
        margin-top: 9px;
        color: #407fff;
        font-size: 14px;
        cursor: pointer;
        text-align: center;
        .dropdown_link {
          color: #407fff;
        }
      }
    }
  }
  .empty {
    text-align: center;
    align-self: center;
    width: 100%;
    margin: 145px auto;
    font-size: 13px;
    .tip {
      color: @color-subtitle;
      margin-top: 10px;
      font-size: 12px;
    }
    .empty_image {
      width: 80px;
      height: 80px;
      margin: auto;
      background: url('@/assets/images/no-data.png') no-repeat;
      background-size: cover;
    }
    .empty_newadd {
      cursor: pointer;
      line-height: 25px;
      color: #407fff;
      .dropdown_link {
        color: #407fff;
        font-size: 13px;
      }
    }
  }
  .hidetabs {
    :global {
      .el-tabs__header {
        display: none;
      }
      .el-tabs__nav-scroll {
        visibility: hidden;
        opacity: 0;
      }
    }
  }
  :global {
    .el-dialog {
      .el-tabs__header {
        margin-bottom: 0;
        .el-tabs__nav {
          margin: 0 30px;
        }
        .el-tabs__item {
          font-size: 13px;
          color: @color-subtitle;
          &.is-active {
            color: @color-title;
          }
        }
      }
      .el-dialog__body {
        padding: 0;
      }
    }
  }
  .top {
    background: #fef0f0;
    border: 1px solid rgba(255, 82, 42, 0.3);
    border-radius: 2px;
    color: #ff522a;
    padding: 1px 4px;
    font-size: 12px;
    margin-left: 5px;
    position: absolute;
    top: 10px;
    left: 10px;
  }
}
</style>
