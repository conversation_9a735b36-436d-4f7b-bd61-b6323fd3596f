import _ from 'lodash'
import http from '@/services/http/index.js'
import util from '@/services/util/index.js'
import { createMaterialDetailUrlByObjectType } from '@/utils/createMaterialDetailUrl.js'
import { getFileIconByType } from '@/utils/index.js'

const HOST = WDP_DEV_ENV === 'DEV' ? 'https://www.ceshi112.com' : window.location.origin
// const MARKETING_H5_URL = `${HOST}/ec/cml-marketing/release/web/cml-marketing.html`;
const WECHAT_AUTH_URL = `${HOST}/wechat2/authIndexNoPhoneValid?wxAppId={{wxAppId}}&businessUrl={{businessUrl}}&authType=1`
const baseAssetsUrl = 'https://www.fxiaoke.com/ec/kemai/release/static/'
const defaultVcover = `${baseAssetsUrl}default-cover-vpage.jpg`
const defaultFormImage = `${baseAssetsUrl}form-default-icon.jpg`
const defaultProductImage = `${baseAssetsUrl}default_product_icon.jpg`
// import defaultFormImage from '@/assets/images/form-default-icon.jpg';
// import defaultProductImage from '@/assets/images/default_product_icon.jpg';

// 物料类型与市场活动objectType对应关系,不存在取默认
const TYPE_MAPS = {
  1: 6, // 文章
  4: 4, // 产品
  16: 16, // 表单
  5: 24, // 海报
  25: 25, // 邀请函
  10: 26, // 微页面
  3: 13, // 会议
  9999: 9999, // 外部内容
  2: 2, // 图片
  888: 888, // 多会场活动
  8: 8, // 文件
}
export const OToCTypeMaps = {
  6: 1, // 文章
  4: 4, // 产品
  16: 16, // 表单
  24: 5, // 海报
  25: 25, // 邀请函
  26: 10, // 微页面
  13: 3, // 会议
  9999: 9999, // 外部内容
  2: 2, // 图片
  888: 888, // 多会场活动
  8: 8, // 文件
}
const material = function () {
  // function createMaterialUrl(name, item) {
  //   // const employee = FS.contacts.getCurrentEmployee();
  //   //自定义表单使用formId字段
  //   const idFieldName = name === "custom-form-page" ? "formId" : "id";
  //   return `${MARKETING_H5_URL}?byshare=1&_hash=/cml/h5/${name}&${idFieldName}=${item.id}&marketingActivityId=!!marketingActivityId!!&wxAppId=!!wxAppId!!`;
  // }
  // 生成小程序素材url
  function createMpMaterialUrl(item, objectType) {
    const employee = FS.contacts.getCurrentEmployee() || {}
    return `/pages/share/share?ea=${employee.enterpriseAccount || ''}&objectType=${objectType}&marketingActivityId=!!marketingActivityId!!&objectId=${item.id}`
  }
  function createMaterialWechatAuthUrl(url) {
    return WECHAT_AUTH_URL.replace(/{{businessUrl}}/g, encodeURIComponent(url))
  }

  // 菜单对象列表
  const menuMaps = {
    marketingEvent: {
      id: 'marketingEvent',
      name: $t('marketing.commons.schd_833ba0'),
      label: $t('marketing.components.select_material_dialog.cschdxz_490165'),
      marketingEventName: $t('marketing.components.select_material_dialog.zsdqschdo_58ca9a'),
    },
    materials: {
      id: 'materials',
      name: $t('marketing.components.select_material_dialog.sck_5e0fcc'),
      label: $t('marketing.commons.cnrzxxz_1c0d28'),
    },
  }
  // 海报
  const qrPosterHandler = {
    title: $t('marketing.commons.hb_ff361e'),
    apiName: 'queryListByEa',
    type: '24',
    link: {
      name: 'poster-gallery',
    },
    options: {
      pageNo: 1,
      pageNum: 1,
      pageSize: 20,
      status: 1,
      type: 1,
    },
    format: item => ({
      id: item.qrPosterId || item.id,
      title: item.title,
      summary: item.forwardContent
        ? `${item.forwardName}-${item.forwardContent}`
        : item.forwardName,
      url: item.qrPosterUrl || item.bgThumbnailUrl,
      authUrl: '',
      image: item.qrPosterUrl || item.bgThumbnailUrl,
      time: util.formatDateTime(item.createTime, 'YYYY-MM-DD hh:mm'),
      aPath: item.qrPosterApath,
      type: 24,
      contentType: 24,
      objectType: 24,
      qrPostForwardType: item.qrPostForwardType,
      top: item.top,
      size: item.coverSize || item.photoSize || 0,
    }),
    detail: {
      apiName: 'queryPosterDetail',
      options: {
        qrPosterId: '',
      },
    },
  }
  // 微页面
  const siteHandler = {
    title: $t('marketing.commons.wym_5fd4fb'),
    apiName: 'listHexagonByGroup',
    type: '10',
    link: {
      name: 'site-create',
      query: {
        from: 'dialog',
      },
    },
    options: {
      pageNo: 1,
      pageNum: 1,
      status: 1,
      pageSize: 20,
    },
    detail: {
      apiName: 'getSiteById',
      options: {
        id: '',
      },
    },
    format: item => {
      // const employee = FS.contacts.getCurrentEmployee();
      // const SITE_URL = `${HOST}/ec/h5-landing/release/index.html?id=${item.id}&marketingActivityId=!!marketingActivityId!!&wxAppId=!!wxAppId!!&type=1`;
      const SITE_URL = createMaterialDetailUrlByObjectType(26, {
        id: item.id,
        marketingActivityId: '!!marketingActivityId!!',
        wxAppId: '!!wxAppId!!',
        qrCodeId: '!!qrCodeId!!',
        qrCodeCategory: 'official_account',
      }).url
      return {
        id: item.id,
        formId: item.formId || "",
        title: item.name,
        shareTitle: item.shareTitle,
        shareDesc: item.shareDesc,
        shareImgUrl: item.shareImgUrl,
        shareImgAPath: item.shareImgAPath,
        summary: '',
        url: SITE_URL,
        mpUrl: createMpMaterialUrl(item, 26),
        authUrl: '',
        image: item.coverUrl || defaultVcover,
        time: util.formatDateTime(item.createTime, 'YYYY-MM-DD hh:mm'),
        aPath: item.coverAPath || item.coverApath,
        sharePicH5CutUrl: item.sharePicH5CutUrl,
        sharePicMiniAppCutUrl: item.sharePicMiniAppCutUrl,
        type: 10,
        contentType: 10,
        objectType: 26,
        top: item.top,
        size: item.coverSize || item.photoSize || 0,
      }
    },
  }
  // 直播
  const liveHandler = {
    title: $t('marketing.commons.zb_7bbe8e'),
    apiName: 'queryLive',
    type: '30',
    link: {
      name: 'live-marketing',
    },
    options: {
      pageNum: 1,
      pageSize: 20,
      // spreadSearch: true
    },
    // 获取详情api信息
    detail: {
      apiName: 'queryLiveDetail',
      options: {
        id: '',
      },
    },
    format: item => {
      // const employee = FS.contacts.getCurrentEmployee();
      const { id } = item
      return {
        ...item,
        id,
        title: item.title,
        summary: item.description,
        // url: `${MARKETING_H5_URL}?id=${id}&marketingActivityId=!!marketingActivityId!!&spreadFsUid=${employee.id}&fsUserId=${employee.id}&wxAppId=!!wxAppId!!&byshare=1#/cml/h5/conference_detail`,
        image: item.cover,
        time: `${$t('marketing.commons.zbsj_519953')}${util.formatDateTime(
          item.startTime,
          'YYYY-MM-DD hh:mm',
        )}${$t('marketing.commons.z_981cbe')}${util.formatDateTime(item.endTime, 'YYYY-MM-DD hh:mm')}`,
        type: 30,
        contentType: 30,
        objectType: 30,
        size: item.coverSize || item.photoSize || 0,
      }
    },
  }
  // 会议
  const meetingHandler = {
    title: $t('marketing.commons.hy_ebcb81'),
    apiName: 'queryMeetingList',
    type: '3',
    link: {
      name: 'meeting-marketing-init',
    },
    options: {
      pageNo: 1,
      pageNum: 1,
      pageSize: 20,
      status: 0,
      // spreadSearch: true
    },
    // 获取详情api信息
    detail: {
      apiName: 'queryConferenceDetail',
      options: {
        id: '',
      },
    },
    format: item => {
      // const employee = FS.contacts.getCurrentEmployee();
      const id = item.conferenceId || item.id
      const { url } = createMaterialDetailUrlByObjectType(13, {
        id,
        marketingActivityId: '!!marketingActivityId!!',
        wxAppId: '!!wxAppId!!',
        qrCodeId: '!!qrCodeId!!',
        qrCodeCategory: 'official_account',
        byshare: 1,
      })
      return {
        ...item,
        id,
        title: item.title,
        summary: `${$t('marketing.commons.hydd_efb7f9')}${item.location || '--'}`,
        url,
        mpUrl: createMpMaterialUrl(
          {
            ...item,
            id,
          },
          13,
        ), // 小程序素材地址
        // url: `${MARKETING_H5_URL}?id=${id}&marketingActivityId=!!marketingActivityId!!&spreadFsUid=${employee.id}&fsUserId=${employee.id}&wxAppId=!!wxAppId!!&byshare=1#/cml/h5/conference_detail`,
        authUrl: '',
        image: item.thumbnailUrl || item.coverImageThumbUrl,
        time: `${$t('marketing.commons.hysj_6721f1')}${util.formatDateTime(
          item.startTime,
          'YYYY-MM-DD hh:mm',
        )}${$t('marketing.commons.z_981cbe')}${util.formatDateTime(item.endTime, 'YYYY-MM-DD hh:mm')}`,
        aPath: item.coverImagePath || item.thumbnailAPath || item.coverImageThumbApath,
        type: 3,
        contentType: 3,
        objectType: 13,
        marketingEventId: item.marketingEventId,
        formId: item.formId,
        activityDetailSiteId: item.activityDetailSiteId,
        size: item.coverSize || item.photoSize || 0,
      }
    },
  }
  const formHandler = {
    title: $t('marketing.commons.bd_eee1e2'),
    apiName: 'queryFormData',
    type: '16',
    link: {
      name: 'form-edit',
      query: {
        from: 'dialog',
      },
    },
    options: {
      pageNo: 1,
      pageNum: 1,
      pageSize: 20,
      status: 0,
    },
    detail: {
      apiName: 'getFormDataById',
      options: {
        id: '',
      },
    },
    format: item => {
      const { formHeadSetting } = item
      // const url = `${HOST}/ec/h5-landing/release/form.html?formId=${item.id}&marketingActivityId=!!marketingActivityId!!&wxAppId=!!wxAppId!!`;
      const { url } = createMaterialDetailUrlByObjectType(16, {
        id: item.id,
        marketingActivityId: '!!marketingActivityId!!',
        wxAppId: '!!wxAppId!!',
      })
      return {
        id: item.id,
        formId: item.formId || "",
        title: formHeadSetting
          ? formHeadSetting.name || formHeadSetting.title
          : item.title,
        summary: (formHeadSetting && formHeadSetting.shareDsc) || '',
        url,
        mpUrl: createMpMaterialUrl(item, 16), // 小程序素材地址
        authUrl: '',
        image: (formHeadSetting && formHeadSetting.sharePicUrl) || defaultFormImage,
        time: util.formatDateTime(item.createTime, 'YYYY-MM-DD hh:mm'),
        aPath: formHeadSetting
          ? (formHeadSetting.headPhotoPath
            && formHeadSetting.headPhotoPath[0])
          || ''
          : '',
        type: 16,
        contentType: 16,
        objectType: 16,
        coverSize: item.coverSize,
      }
    },
  }
  const invitationHandler = {
    title: $t('marketing.commons.yqh_85c9ea'),
    apiName: 'queryInvitationList',
    type: '25',
    options: {
      pageNum: 1,
      pageSize: 20,
      status: 0,
    },
    format: item => ({
      id: item.id,
      title: item.invitationName,
      image: item.coverImageThumbUrl,
      time: util.formatDateTime(item.createTime, 'YYYY-MM-DD hh:mm'),
      type: 25,
      contentType: 25,
      objectType: 25,
    }),
  }
  const pictureHandler = {
    title: $t('marketing.commons.tp_20def7'),
    apiName: 'listPhotoByGroup',
    type: '2',
    options: {
      pageNum: 1,
      pageSize: 10,
      source: 2,
    },
    format: item => ({
      id: item.id,
      title: item.photoName,
      image: item.thumbnailUrl,
      time: util.formatDateTime(item.createTime, 'YYYY-MM-DD hh:mm'),
      contentType: 2,
      aPath: item.photoPath,
      size: item.photoSize,
    }),
  }
  // 外部内容
  const externalContentHandler = {
    title: $t('marketing.commons.wbnr_6ecce8'),
    apiName: 'queryListOutLink',
    type: '9999',
    options: {
      pageNum: 1,
      pageSize: 20,
      status: 0,
    },
    detail: {
      apiName: 'getOutLinkById',
      options: {
        id: '',
      },
    },
    format: item => {
      // const url = `${HOST}/ec/h5-landing/release/external-content.html?id=${item.id}&marketingActivityId=!!marketingActivityId!!&wxAppId=!!wxAppId!!`;
      const { url } = createMaterialDetailUrlByObjectType(9999, {
        id: item.id,
        marketingActivityId: '!!marketingActivityId!!',
        wxAppId: '!!wxAppId!!',
      })
      return {
        id: item.id,
        title: item.name,
        image: item.cover || 'https://a9.fspage.com/FSR/weex/avatar/marketing_app/images/external-default-cover.jpg',
        url,
        mpUrl: createMpMaterialUrl(item, 9999),
        summary: item.describe || '',
        time: util.formatDateTime(item.createTime, 'YYYY-MM-DD hh:mm'),
        type: 9999,
        contentType: 9999,
        objectType: 9999,
        size: item.coverSize || item.photoSize || 0,
      }
    },
  }

  // 文件
  const fileHandle = {
    title: $t('marketing.commons.wj_2a0c47'),
    apiName: 'listFileByGroup',
    type: '8',
    options: {
      pageNum: 1,
      pageSize: 10,
    },
    format: item => {
      const {
        id, createTime, fileName, fileSize, filePath, ext,
      } = item

      const fileIcon = getFileIconByType(ext)

      return {
        id,
        title: fileName,
        time: util.formatDateTime(createTime, 'YYYY-MM-DD hh:mm'),
        contentType: 8,
        aPath: filePath,
        size: fileSize,
        fileIcon,
        ...item,
      }
    },
  }

  // 多会场活动
  const multiActivityHandler = {
    title: $t('marketing.commons.dhc_f461d6'),
    apiName: 'listMultiVenueMarketingEvent',
    type: '888',
    link: {
      name: 'content-marketing',
      query: {
        formType: 6,
      },
    },
    options: {
      pageNum: 1,
      pageSize: 20,
      // spreadSearch: true
    },
    // 获取详情api信息
    detail: {
      apiName: 'getMarketingEventsDetail',
      options: {
        id: '',
      },
    },
    format: item =>
      // const employee = FS.contacts.getCurrentEmployee();
      ({
        ...item,
        id: item.marketingEventId,
        title: item.title,
        summary: item.description,
        // url: `${MARKETING_H5_URL}?id=${id}&marketingActivityId=!!marketingActivityId!!&spreadFsUid=${employee.id}&fsUserId=${employee.id}&wxAppId=!!wxAppId!!&byshare=1#/cml/h5/conference_detail`,
        image: item.coverUrl,
        time: (item.startTime && item.endTime) ? `${$t('marketing.commons.hdsj_9e62e9')}${util.formatDateTime(
          item.startTime,
          'YYYY-MM-DD hh:mm',
        )}${$t('marketing.commons.z_981cbe')}${util.formatDateTime(item.endTime, 'YYYY-MM-DD hh:mm')}` : `${$t('marketing.commons.hdsj_9e62e9')}--`,
        type: 888,
        contentType: 888,
        objectType: 888,
        size: (item.cover && item.cover[0] && item.cover[0].size) || 0,
      }),

  }
  // 默认不传入市场活动apiMaps
  const apiMaps = {
    1: {
      title: $t('marketing.commons.wz_c75625'),
      apiName: 'queryArticleList',
      type: '1',
      link: {
        name: 'article-create',
        query: {
          from: 'dialog',
        },
      },
      options: {
        pageNo: 1,
        pageNum: 1,
        pageSize: 20,
        type: 2,
        status: 1,
      },
      detail: {
        apiName: 'queryArticleDetail',
        options: {
          articleId: '',
        },
      },
      format: item => {
        const { url } = createMaterialDetailUrlByObjectType(6, {
          id: item.id,
          marketingActivityId: '!!marketingActivityId!!',
          wxAppId: '!!wxAppId!!',
          qrCodeId: '!!qrCodeId!!',
          qrCodeCategory: 'official_account',
          byshare: 1,
        })
        return {
          id: item.id,
          title: item.title,
          summary: item.summary,
          url,
          mpUrl: createMpMaterialUrl(item, 6), // 小程序素材地址
          authUrl: createMaterialWechatAuthUrl(url),
          image: item.photoThumbnailUrl || item.photoUrl || defaultVcover,
          time: util.formatDateTime(item.createTime, 'YYYY-MM-DD hh:mm'),
          aPath: item.photoThumbnailAPath || item.photoThumbnailApath,
          sharePicH5CutUrl: item.sharePicH5CutUrl,
          sharePicMiniAppCutUrl: item.sharePicMiniAppCutUrl,
          type: 1,
          contentType: 1,
          objectType: 6,
          top: item.top,
          size: item.coverSize || item.photoSize || 0,
        }
      },
    },
    3: meetingHandler,
    // 3: {
    //   title: "活动",
    //   apiName: "invitationQueryListMulti",
    //   type: "3",
    //   link: {
    //     name: "invitation-create"
    //   },
    //   options: {
    //     pageNum: 1,
    //     pageSize: 20,
    //     state: [2, 3], // 1 全部 2 即将开始 3 进行中 4 已结束
    //     status: [1] // 1 启用 2 停用
    //   },
    //   format: item => {
    //     // const url = createMaterialUrl('activity_detail', item);
    //     return {
    //       id: item.id,
    //       title: item.invitationName,
    //       // summary: `活动地点：${item.location || ''}`,
    //       // url,
    //       // authUrl: createMaterialWechatAuthUrl(url),
    //       image: item.coverImageThumbUrl,
    //       // aPath: item.coverImageSmallAPath,
    //       time: util.formatDateTime(item.createTime, "YYYY-MM-DD hh:mm"),
    //       // time: `活动时间：${util.formatDateTime(item.startTime, 'YYYY-MM-DD hh:mm')}至${util.formatDateTime(
    //       //   item.endTime,
    //       //   'YYYY-MM-DD hh:mm',
    //       // )}`,
    //       type: 3
    //     };
    //   }
    // },
    4: {
      title: $t('marketing.commons.cp_a01543'),
      apiName: 'queryProductList',
      type: '4',
      link: {
        name: 'product-create',
        params: {
          origin: 'company',
        },
        query: {
          from: 'dialog',
        },
      },
      options: {
        pageNo: 1,
        pageNum: 1,
        pageSize: 20,
        status: 1, // null 全部 1 已启用 2 已停用 3 未启用
        type: 2,
      },
      detail: {
        apiName: 'queryProductDetail',
        options: {
          id: '',
        },
      },
      format: item => {
        const { url } = createMaterialDetailUrlByObjectType(4, {
          id: item.id,
          marketingActivityId: '!!marketingActivityId!!',
          wxAppId: '!!wxAppId!!',
          qrCodeId: '!!qrCodeId!!',
          qrCodeCategory: 'official_account',
          byshare: 1,
        })
        return {
          id: item.id,
          title: item.name,
          summary: item.summary,
          url,
          mpUrl: createMpMaterialUrl(item, 4), // 小程序素材地址
          authUrl: createMaterialWechatAuthUrl(url),
          image: item.sharePicOrdinaryCutUrl || (item.headPicsThumbs || [])[0] || defaultProductImage,
          aPath: item.headPicThumbAPath,
          time: util.formatDateTime(item.createTime, 'YYYY-MM-DD hh:mm'),
          type: 4,
          contentType: 4,
          objectType: 4,
          sharePicH5CutUrl: item.sharePicH5CutUrl,
          sharePicMiniAppCutUrl: item.sharePicMiniAppCutUrl,
          top: item.top,
          size: item.coverSize || item.photoSize || 0,
        }
      },
    },
    16: formHandler,
    24: qrPosterHandler,
    25: invitationHandler,
    10: siteHandler,
    30: liveHandler,
    9999: externalContentHandler,
    2: pictureHandler,
    5: qrPosterHandler,
    888: multiActivityHandler,
    8: fileHandle,
  }
  // 根据市场活动ID取数据
  const marketingEventApiHandler = {
    apiName: 'queryMarketingEventListMaterials',
    options: {
      id: '',
      objectType: 6, // 产品 4 文章 6 海报 24
      pageNum: 1,
      pageSize: 20,
    },
  }
  // 传入市场活动ID
  const apiMapsWithMarketingEventId = {
    1: marketingEventApiHandler,
    3: marketingEventApiHandler,
    4: marketingEventApiHandler,
    16: marketingEventApiHandler,
    // 24: qrPosterHandler,
    25: invitationHandler,
    24: marketingEventApiHandler,
    10: marketingEventApiHandler,
    9999: marketingEventApiHandler,
  }
  // 类型type
  let activeType = ''

  //支持市场活动下tab的物料类型
  const suportMarketingEventTabTypes = (() => {
    return Object.keys(apiMapsWithMarketingEventId).map(key => Number(key))
  })()

  return {
    TYPE_MAPS,
    OToCTypeMaps,
    menuMaps,
    suportMarketingEventTabTypes,
    getMenuObjs(menus, hasMarketingEvent,isActivity) {
      const MenuObjs = []
      menus.forEach(item => {
        // hasMarketingEvent：kis企业没有marketingEvent，所以在这里要屏蔽掉
        // 市场活动下新建的微页面也不展示marketingEvent
        if ((!hasMarketingEvent || isActivity) && item === 'marketingEvent') {
          return
        }
        MenuObjs.push(menuMaps[item])
      })
      return MenuObjs
    },
    apiMaps,
    getTabs(tab) {
      return tab.reduce((arr, key) => {
        if (apiMaps[key]) {
          arr.push(apiMaps[key])
        }
        return arr
      }, [])
    },
    _createParams(type, params = {}) {
      activeType = type
      // 是否带入marketingEventId,如果带入则通过对应接口取数据
      let isMarketingEventMaterial = false
      const isParamsEmpty = _.isEmpty(params)
      if (!isParamsEmpty && params.id) {
        isMarketingEventMaterial = true
      }
      if (isMarketingEventMaterial) {
        const { options } = apiMapsWithMarketingEventId[type]
        if (isParamsEmpty) {
          return apiMapsWithMarketingEventId[type]
        }
        apiMapsWithMarketingEventId[type].options = _.extend(
          {
            ...options,
            marketingEventId: params.id,
            objectType: Number(TYPE_MAPS[type] ? TYPE_MAPS[type] : type),
          },
          params,
        )
        return apiMapsWithMarketingEventId[type]
      }

      const { options } = apiMaps[type]
      return isParamsEmpty
        ? apiMaps[type]
        : {
          ...apiMaps[type],
          options: _.extend(options, params),
        }
    },
    setParam(type, params = {}) {
      if (!type) {
        return
      }
      this._createParams(type, params)
      return this
    },
    async fetch(type, params = {}) {
      type = type || activeType
      const { apiName, options } = this._createParams(type, params)
      // const { apiName, options } = apiMaps[type];
      const formatFunc = (apiMaps[type] && apiMaps[type].format) || (res => res)
      const results = await http[apiName](
        _.isEmpty(params) ? options : _.extend(options, params),
      ).then(res => this.formatResultData(res, formatFunc))
      return results
    },
    // 获取素材详情信息
    getMaterialDetailByContentType(type, id) {
      if (_.isEmpty(id)) return
      if (_.isEmpty(apiMaps[type])) {
        console.warn('getMaterialDetailByContentType:type不存在')
        return
      }
      const {
        detail: { apiName, options },
      } = apiMaps[type]
      if (type == 1) {
        options.articleId = id
      } else if (type == 24 || type == 5) {
        options.qrPosterId = id
      } else {
        options.id = id
      }
      return http[apiName](options)
    },
    getMaterialDetailByObjectType(objectType, id) {
      const type = OToCTypeMaps[objectType]
      if (!type) {
        console.warn('formatDataByObjectType: type未找到')
        return
      }
      return this.getMaterialDetailByContentType(type, id)
    },
    formatResultData({ errCode, data = {}, errMsg }, format) {
      if (errCode === 0) {
        const {
          result,
          productDetailResultList,
          articleDetailResults,
          recordSize,
          totalCount,
        } = data || {}
        return {
          success: true,
          total: totalCount || recordSize || 0,
          lists: (
            result
            || productDetailResultList
            || articleDetailResults
          ).map(item => format(item)),
        }
      }
      return {
        success: false,
        msg: errMsg,
      }
    },
    formatDataByContentType(type, data) {
      if (_.isEmpty(apiMaps[type])) {
        console.warn('formatDataByContentType: type不存在')
        return
      }
      if (_.isEmpty(data)) {
        console.warn('formatDataByContentType: data为空')
        return
      }
      if (apiMaps[type].format instanceof Function) {
        return apiMaps[type].format(data)
      }
    },
    formatDataByObjectType(objectType, data) {
      const type = OToCTypeMaps[objectType]
      if (!type) {
        console.warn('formatDataByObjectType: type未找到')
        return
      }
      return this.formatDataByContentType(type, data)
    },
  }
}

export default material
