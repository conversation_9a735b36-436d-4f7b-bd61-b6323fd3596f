<template>
    <div class="employee__material__slogan-wrapper" :style="{marginBottom: showSaveBtn ? '30px' : '0'}">
      <fx-input
        v-model="slogan"
        type="textarea"
        maxlength="255"
        :rows="4"
        autosize
        show-word-limit
        :disabled="!editState"
        size="mini"
        class="slogan-input"
        :placeholder="$t('marketing.components.slogan_adddialog.bjmrtgyygh_62ef6e')"
      />
      <div class="save-btn">
        <fx-button
          size="mini"
          type="text"
          icon="fx-icon-edit"
          v-if="!editState"
          @click.stop="handleEditSlogan"
        >{{ $t('marketing.commons.bj_95b351') }}</fx-button>
        <div style="display: flex;gap: 6px;" v-else>
          <fx-button
            size="mini"
            type="text"
            @click.stop="handleSubmitSlogan"
          >
            {{ $t('marketing.commons.bc_be5fbb') }}
          </fx-button>
          <fx-button
            size="mini"
            type="text"
            @click.stop="handleCancel"
          >
            {{ $t('marketing.commons.qx_625fb2') }}
          </fx-button>
        </div>
      </div>
    </div>
</template>

<script>

import http from '@/services/http/index.js'

export default {
  props: {
    objectType: {
      type: Number,
      default: 4,
    },
    objectId: {
      type: String,
      default: '',
    },
    sloganTips: {
      type: String,
      default: $t('marketing.commons.yghbhhyznr_8173dd'),
    },
    showSaveBtn: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      slogan: '',
      editState: false,
      oldSlogan: '',
    }
  },
  watch: {
    objectId: {
      handler(val) {
        if (val) {
          this.getObjectSlogan()
        }
      },
      immediate: true,
    },
  },

  mounted() {
  },

  methods: {
    handleEditSlogan() {
      this.editState = true
      this.oldSlogan = this.slogan
    },
    async handleSubmitSlogan() {
      if (!this.objectId) return
      const params = {
        objectId: this.objectId,
        objectType: this.objectType,
        slogan: this.slogan,
      }
      const res = await http.setObjectSlogan(params)
      if (res && res.errCode === 0) {
        FxUI.Message.success($t('marketing.commons.szcg_f6088e'))
        this.editState = false
      } else {
        FxUI.Message.success($t('marketing.commons.szsb_9f9603'))
        this.slogan = this.oldSlogan
      }
    },
    async getObjectSlogan() {
      const params = {
        objectId: this.objectId,
        objectType: this.objectType,
      }
      const res = await http.getObjectSlogan(params)
      if (res && res.errCode === 0 && res.data) {
        this.slogan = res.data.slogan || ''
        this.oldSlogan = this.slogan
      }
    },
    handleCancel() {
      this.editState = false
      this.slogan = this.oldSlogan
    },
  },
}
</script>

<style lang="less" scoped>
.employee__material__slogan-wrapper{
  position: relative;
  .slogan-tips{
    color: #91959E;
    font-size: 12px;
    margin-bottom: 10px;
  }
  .slogan-input{
    min-height: 100px;
    /deep/ .el-textarea__inner{
       min-height: 100px !important;
    }
  }
  .save-btn{
    position: absolute;
    right: 0;
    top: -5px;
    transform: translateY(-100%);
    color: var(--color-info06,#0c6cff);
    /deep/ .el-button.el-button--text{
      color: var(--color-info06,#0c6cff);
    }
    /deep/ .fx-icon-edit{
      color: var(--color-info06,#0c6cff);
      &::before{
        color: var(--color-info06,#0c6cff);
      }
    }
  }
}
</style>
