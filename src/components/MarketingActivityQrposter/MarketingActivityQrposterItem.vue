<template>
  <div
    :class="['MarketingActivityQrposterItem', size]"
    v-loading="flag_imgLoading"
    element-loading-text=""
    element-loading-spinner="el-icon-loading"
    element-loading-background="#f4f4f4"
  >
    <template v-if="size === 'small'">
      <div
        :class="['item__cover', flag_isLongImage && 'long-img']"
        :style="{ 'background-image': `url(${qrposter.qrPosterThumbnailUrl})` }"
      >
        <img
          v-if="showMobileDisplayTagCopy"
          class="icon-display"
          :src="require('@/assets/images/icons/enable-mobile-display.png')"
        />
        <!-- <img
          v-show="flag_isLongImage"
          :class="['icon_longimg']"
          :src="icon_longimg"
        /> -->
        <span :class="'longposter-icon'" v-show="flag_isLongImage">{{ $t('marketing.commons.ct_ce9ecb') }}</span>
        <!-- <img :src="index%2 === 0 ? img : img2"> -->
      </div>
      <div class="cover__mask">
        <div class="mask__more">
          <ElDropdown size="small" @command="handleMore">
            <i class="el-icon-more"></i>
            <ElDropdownMenu slot="dropdown">
              <!-- <ElDropdownItem command="detail">查看详情</ElDropdownItem> -->
              <ElDropdownItem command="delete">{{ $t('marketing.commons.schb_947d50') }}</ElDropdownItem>
            </ElDropdownMenu>
          </ElDropdown>
        </div>
        <div class="mask__buttons">
          <span v-if="disabledSpreadBtn" class="disabled-tips">{{ $t('marketing.commons.shtghcntg_b33c1c') }}</span>
          <span v-else class="mask__button" @click="handleSpread">
            <i class="iconfont">&#xe625;</i>
            {{ $t('marketing.commons.tg_9a8e91') }}
          </span>
        </div>
      </div>
      <Popover :content="qrposter.title" trigger="hover" :open-delay="100">
        <div class="poster__name" slot="reference">
          {{ qrposter.title }}
        </div>
      </Popover>
    </template>
    <template v-if="size === 'big'">
      <div
        :class="['item__cover', flag_isLongImage && 'long-img']"
        :style="{
          'background-image': `url(${qrposter.qrPosterThumbnailUrl})`
        }"
      >
        <!-- <img
          v-show="flag_isLongImage"
          :class="['icon_longimg']"
          :src="icon_longimg"
        /> -->
        <span :class="'longposter-icon'" v-show="flag_isLongImage">{{ $t('marketing.commons.ct_ce9ecb') }}</span>
        <span class="top" v-if="qrposter.top">{{ $t('marketing.commons.zd_3d43ff') }}</span>
        <!-- <img :src="index%2 === 0 ? img : img2"> -->
      </div>
      <div class="cover__mask">
        <div class="mask__more">
          <ElDropdown size="small" @command="handleMore">
            <i class="el-icon-more"></i>
            <ElDropdownMenu slot="dropdown">
              <!-- <ElDropdownItem command="detail">查看详情</ElDropdownItem> -->
              <ElDropdownItem command="setGroup">{{ $t('marketing.commons.szfz_194cfa') }}</ElDropdownItem>
              <ElDropdownItem command="top" v-if="!qrposter.top"
                >{{ $t('marketing.commons.zd_3d43ff') }}</ElDropdownItem
              >
              <ElDropdownItem command="cancleTop" v-else
                >{{ $t('marketing.commons.qxzd_84e4fa') }}</ElDropdownItem
              >
              <ElDropdownItem command="delete">{{ $t('marketing.commons.schb_947d50') }}</ElDropdownItem>
            </ElDropdownMenu>
          </ElDropdown>
        </div>
        <div class="mask__buttons" v-if="scene !== 'parent-target'">
          <span v-if="disabledSpreadBtn">{{ $t('marketing.commons.shtghcntg_b33c1c') }}</span>
          <span v-else class="mask__button" @click="handleSpread">
            <i class="iconfont">&#xe625;</i>
            {{ $t('marketing.commons.tg_9a8e91') }}
          </span>
        </div>
      </div>
      <Checkbox class="qrpost-check" :label="qrposter"></Checkbox>
      <Popover :content="qrposter.title" trigger="hover" :open-delay="100">
        <div class="poster__name" slot="reference">
          {{ qrposter.title }}
        </div>
      </Popover>
    </template>

    <MarketingActivityQrposterItemPreview
      v-if="isShowPreviewDialog"
      :visible.sync="isShowPreviewDialog"
      :previewPoster="qrposter"
    ></MarketingActivityQrposterItemPreview>
    <MarketingActivityQrposterSpreadDialog
      v-if="isShowSpreadDialog"
      :visible.sync="isShowSpreadDialog"
      :marketingEventId="marketingEventId"
      :qrposter="qrposter"
      @isMobileDisplayChange="isMobileDisplayChange"
      :materialLabel="materialLabel"
    ></MarketingActivityQrposterSpreadDialog>
  </div>
</template>

<script>
import util from "@/services/util";
import { confirm, alert } from "@/utils/globals";
import MarketingActivityQrposterItemPreview from "./MarketingActivityQrposterItemPreview";
import MarketingActivityQrposterSpreadDialog from "./MarketingActivityQrposterSpreadDialog";
import icon_longimg from "@/assets/images/qrposter/icon_longimg.png";
import icon_longimg_en from '@/assets/images/qrposter/icon_longimg-en.png'
import http from "@/services/http/index";
import { CampaignReviewStatus } from "@/utils/statusEnum";
import { mapState } from 'vuex';

import { getImageByLang } from '@/utils/i18n.js'

export default {
  components: {
    ElDropdown: FxUI.Dropdown,
    ElDropdownMenu: FxUI.DropdownMenu,
    ElDropdownItem: FxUI.DropdownItem,
    MarketingActivityQrposterItemPreview,
    MarketingActivityQrposterSpreadDialog,
    Popover: FxUI.Popover,
    Checkbox: FxUI.Checkbox
  },
  props: {
    marketingEventId: {
      type: String,
      default: ""
    },
    qrposter: {
      type: Object,
      default() {
        return {};
      }
    },
    size: {
      type: String,
      default: ""
    },
    showMobileDisplayTag: {
      type: Boolean,
      default: false
    },
    materialLabel: {
      type: String,
      default: '',
    },
    lifeStatus: {
      type: String,
      default: '',
    },
    scene:{
      type:String,
      default:""
    },
  },
  data() {
    return {
      util,
      showMobileDisplayTagCopy: false,
      icon_longimg: getImageByLang([icon_longimg, icon_longimg_en]),
      isShowPreviewDialog: false,
      isShowSpreadDialog: false,
      flag_imgLoading: true,
      flag_isLongImage: false
    };
  },
  watch: {
    showMobileDisplayTag(val) {
      this.showMobileDisplayTagCopy = val;
    },
  },
  computed: {
    ...mapState('MarketingEventSet', ['marketingEventAudit']),
    isFromMarketingActivity(){
      const activityMap = [
        'content',
        'live',
        'meeting'
      ]
      return activityMap.includes(this.scene)
    },
    disabledSpreadBtn() {
      return !!this.marketingEventAudit && this.lifeStatus !== CampaignReviewStatus.normal && this.isFromMarketingActivity;
    },
  },
  methods: {
    handleSpread() {
      this.isShowSpreadDialog = true;
    },
    isMobileDisplayChange(val) {
      this.showMobileDisplayTagCopy = val;
    },
    handleMore(action) {
      switch (action) {
        case "detail":
          this.handleDetail();
          break;
        case "delete":
          this.handleDelete();
          break;
        case "top":
          this.topQRPoster(this.qrposter);
          break;
        case "cancleTop":
          this.cancelTopQRPoster(this.qrposter);
          break;
        case "setGroup":
          this.$emit("setGroup", this.qrposter);
          break;
      }
    },
    handleDetail(data) {
      this.isShowPreviewDialog = true;
    },

    handleDelete(id) {
      confirm($t('marketing.commons.qqrsfyscch_4d0b78'), $t('marketing.commons.ts_02d981'), {
        callback: action => {
          if (action === "confirm")
            YXT_ALIAS.http
              .deleteQRPoster({ qrPosterId: this.qrposter.qrPosterId })
              .then(res => {
                if (res && res.errCode === 0) {
                  FxUI.Message.success($t('marketing.commons.sccg_43593d'));
                  this.$emit("refresh");
                }
              });
        }
      });
    },
    presetQrposter() {
      if (!this.qrposter || !this.qrposter.qrPosterThumbnailUrl) {
        return;
      }
      const QrPoster = new Image();
      QrPoster.src = this.qrposter.qrPosterThumbnailUrl;
      QrPoster.onload = () => {
        this.flag_imgLoading = false;
        if (QrPoster.height > 1334) {
          this.flag_isLongImage = true;
        }
      };
      QrPoster.onerror = () => {
        this.flag_imgLoading = false;
      };
    },
    async topQRPoster(item) {
      let res = await http.topQRPoster({
        objectId: item.qrPosterId
      });
      if (res && res.errCode === 0) {
        FxUI.Message.success($t('marketing.commons.zdcg_9008e2'));
        this.$emit("refresh");
      }
    },
    async cancelTopQRPoster(item) {
      let res = await http.cancelTopQRPoster({
        objectId: item.qrPosterId
      });
      if (res && res.errCode === 0) {
        FxUI.Message.success($t('marketing.commons.qxzdcg_1e9cd7'));
        this.$emit("refresh");
      }
    }
  },
  mounted() {
    this.presetQrposter();
  }
};
</script>

<style lang="less" scoped>
.MarketingActivityQrposterItem {
  margin: 10px;
  position: relative;
  width: 190px;
  height: 334px;
  &.small {
    width: 110px;
    height: 195px;
    margin: 5px;
  }
  &:hover {
    box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.2);
  }
  .item__cover {
    position: relative;
    width: 100%;
    height: calc(100% - 32px);
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f4f4f4;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    overflow: hidden;
    .icon_longimg {
      position: absolute;
      top: 0;
      left: 0;
      width: 40px;
    }
    &.long-img {
      background-size: cover;
      background-position: top;
      // &::before {
      //   position: absolute;
      //   top: -12px;
      //   left: -33px;
      //   content: '\957F\56FE';
      //   font-size: 12px;
      //   color: #fff;
      //   background: #94CE55;
      //   width: 80px;
      //   height: 40px;
      //   padding-top: 18px;
      //   display: flex;
      //   box-sizing: border-box;
      //   justify-content: center;
      //   transform: rotate(-45deg);
      // }
    }
    img {
      max-width: 100%;
      max-height: 100%;
    }
    .icon-display {
      width: 20px;
      height: 24px;
      position: absolute;
      top: 0;
      right: 6px;
    }
  }
  &.small {
    .item__cover {
      .icon_longimg {
        width: 30px;
      }
      &.long-img {
        // &::before {
        //   content: '\957F';
        //   top: -20px;
        //   left: -40px;
        //   padding-top: 23px;
        // }
      }
    }
  }
  &.small {
    .cover__mask {
      .mask__more {
        .el-icon-more {
          font-size: 15px;
        }
      }
      .mask__buttons {
        font-size: 13px;
        .mask__button {
          .iconfont {
            font-size: 16px;
            margin-right: 0px;
          }
        }
      }
    }
  }
  .cover__mask {
    flex-direction: column;
    display: flex;
    opacity: 0;
    position: absolute;
    top: 0;
    width: 100%;
    height: calc(100% - 32px);
    overflow: hidden;
    background: rgba(0, 0, 0, 0.5);
    transition: all 0.3s;
    &:hover {
      opacity: 1;
      .mask__panel {
        bottom: 0;
        opacity: 1;
      }
    }
    .mask__more {
      position: absolute;
      top: 5px;
      right: 10px;
      .el-icon-more {
        color: #fff;
        font-size: 20px;
        cursor: pointer;
      }
    }
    .mask__buttons {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #fff;
      font-size: 16px;
      font-weight: 600;
      height: 100%;
      .disabled-tips {
        text-align: center;
        padding: 0 10px;
        font-size: 16px;
      }
      .mask__button {
        margin: 10px 0;
        cursor: pointer;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        .iconfont {
          font-size: 19px;
          margin-right: 3px;
        }
      }
    }
    .mask__panel {
      position: absolute;
      width: 100%;
      height: 185px;
      background: #fff;
      bottom: -160px;
      opacity: 1;
      /* bottom: 0; */
      transition: all 0.5s;
      .panel__title {
        padding: 13px 15px 0;
        color: #353535;
        font-size: 14px;
      }
      .panel__time {
        padding: 3px 15px 10px;
        color: #91959e;
        font-size: 12px;
      }
      .panel__divide {
        height: 1px;
        background: #e9edf5;
        margin: 0 0 10px;
      }
      .panel__row {
        display: flex;
        padding: 5px 15px;
        .label {
          width: 65px;
        }
        .value {
          flex: 1;
          &.link {
            color: var(--color-primary06,#407FFF);
            cursor: pointer;
          }
        }
      }
    }
  }
  .qrpost-check {
    display: none;
    position: absolute;
    top: 10px;
    left: 10px;
  }
  .is-checked.qrpost-check {
    display: block;
  }
  &:hover {
    .cover__mask {
      display: flex;
    }
    .qrpost-check {
      display: block;
    }
  }
  .item__info {
    width: 220px;
    height: 70px;
    padding: 15px;
    box-sizing: border-box;
    border-top: 1px solid #e9edf5;
    .info__name {
      color: #353535;
      font-size: 14px;
    }
    .info__date {
      color: #91959e;
      font-size: 14px;
    }
  }
  ::v-deep .el-checkbox__label {
    display: none;
  }
}
.more__item {
  font-size: 12px;
}
.poster__name {
  width: calc(100% - 10px);
  text-align: center;
  margin-top: 12px;
  font-size: 13px;
  color: #181c25;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  padding: 0 5px;
}
.top {
  position: absolute;
  right: 10px;
  bottom: 10px;
  background: #fef0f0;
  border: 1px solid rgba(255, 82, 42, 0.3);
  border-radius: 2px;
  color: #ff522a;
  padding: 1px 4px;
  font-size: 12px;
  margin-left: 5px;
}
</style>
