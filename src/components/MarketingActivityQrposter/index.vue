<template>
    <div :class="['MarketingActivityQrposter', `poster__${size}`]">
      <div class="sort-group">
        <div v-if="size === 'big'" class="group-left">
          <GroupManageNew
            ref="groupManageRef"
            :title="$t('marketing.commons.hbfz_e0b56f')"
            :object-type="24"
            @update:active="handleUpdateGroupId"
            @permissionIntercept="permissionIntercept"
          />
        </div>
        <div class="group-right">
          <div class="header qrposter_header">
            <ContentGroupHeader
              :object-type="24"
              type="qrpost"
              :data="selections"
              :cur-group-selector-props="curGroupManageProps"
              @close="$refs.qrposterlist.clearSelection()"
              @update:list="$refs.qrposterlist.handleUpdateList()"
              @update:group="queryGroup"
            />
            <span v-show="!hideTitle" class="header__title">{{ title }}</span>
            <ElInput
              v-show="size === 'big'"
              v-model="model_search_text"
              size="small"
              class="search-input"
              style="margin-right: 18px"
              :placeholder="$t('marketing.commons.sshbmc_72a037')"
              prefix-icon="el-icon-search"
            />
            <ElButton v-if="size === 'big'" type="primary" size="small" id="guide__qrposter-create_btn" @click="handleCreate">
              <i class="yxt-icon16 icon-add" />
              <span>{{ $t('marketing.commons.xjhb_0844db') }}</span>
            </ElButton>
            <ElLink v-else-if="size === 'small'" type="standard" id="guide__qrposter-create_btn" :underline="false" @click="handleCreate">
              <i class="el-icon-plus" style="margin-right: 3px;font-weight: 700;"></i>{{ $t('marketing.commons.zzhb_80c7a4') }}
            </ElLink>
          </div>
          <MarketingActivityQrposterList
            ref="qrposterlist"
            :scene="scene"
            :size="size"
            :reset-page-size="resetPageSize"
            class="content"
            :marketing-event-id="marketingEventId"
            :search-text="model_search_text"
            :group-id="curGroupId"
            :material-label="materialLabel"
            :show-mobile-display-tag="showMobileDisplayTag"
            :life-status="lifeStatus"
            v-bind="$attrs"
            @update:group="handleUpdateGroup"
            @update:selection="setSelection"
          />
          <qrposter-create-dialog
            v-if="isShowCreateDialog"
            :visible.sync="isShowCreateDialog"
            :default-marketing-event-id="marketingEventId"
            :group-id="curGroupId"
            @update:list="handleAfterCreate"
          />
          <!-- <v-pagen @change="handlePageChange" :pagedata.sync="pageData" class="invitation-content__poster-pagen"></v-pagen> -->
        </div>
      </div>
    </div>
</template>
<script>

import MarketingActivityQrposterList from './MarketingActivityQrposterList.vue';
import QrposterCreateDialog from '@/components/qrposter-create-dialog/index.vue';
import ContentGroupHeader from '@/components/ContentGroupHeader/index.vue';

import GroupManageNew from '@/components/group-manage-new/index.vue';

export default {
  components: {
    ElInput: FxUI.Input,
    ElButton: FxUI.Button,
    ElLink: FxUI.Link,
    MarketingActivityQrposterList,
    QrposterCreateDialog,
    ContentGroupHeader,
    GroupManageNew,
  },
  props: {
    scene: {
      type: String,
      default: '',
    },
    title: {
      type: String,
      default: $t('marketing.commons.tghb_c91bd6'),
    },
    hideTitle: {
      type: Boolean,
      default: false,
    },
    size: {
      type: String,
      default: 'big',
    },
    marketingEventId: {
      type: String,
      default: '',
    },
    resetPageSize: {
      type: Number,
      default: 10,
    },
    showMobileDisplayTag: {
      type: Boolean,
      default: false,
    },
    materialLabel: {
      type: String,
      default: '',
    },
    lifeStatus: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      model_search_text: '',
      isShowCreateDialog: false,
      groups: [],
      curGroupId: '',
      isShowGroupManage: false,
      selections: [],
      permissionInterceptCallback: () => {},
    };
  },
  computed: {
    curGroupManageProps() {
      return {
        title: $t('marketing.commons.hbfz_e0b56f'),
        displayKey: 'QR_POSTER_GROUP_',
        interfaces: {
          ListGroup: 'listQRPosterGroup',
          SaveGroup: 'editQRPosterGroup',
          DeleteGroup: 'deleteQRPosterGroup',
          // setRole: "addQRPosterGroupRole",
          // getRole: "getQRPosterGroupRole",
          batchSetGroup: 'setQRPosterGroup',
          batchDelete: 'deleteQRPosterBatch',
          setRole: 'saveGroupVisible',
          getRole: 'getGroupVisible',
          objType: '24', // 二维码海报 24
        },
      };
    },
  },
  created() {},
  mounted() {
    // 监听海报创建事件
    this.$root.$on('poster-created', () => {
      this.$refs.qrposterlist.handlePageNum(1)
    })  
  },
  destroyed() {
    this.$root.$off('poster-created')
  },
  methods: {
    permissionIntercept(cb) {
      this.permissionInterceptCallback = cb;
    },

    async handleCreate() {
      const { enable, groupId } = await this.permissionInterceptCallback() || {};
      if (enable && !groupId) return;

      this.isShowCreateDialog = true;
    },
    handleAfterCreate() {
      this.$refs.qrposterlist.handlePageNum(1);
    },
    handleUpdateGroup() {
      if (this.$refs.groupManageRef && this.$refs.groupManageRef.queryGroup) {
        this.$refs.groupManageRef.queryGroup();
      }
    },
    handleUpdateGroupId(groupId) {
      this.curGroupId = groupId;
      this.$refs.qrposterlist.clearSelection();
      this.$nextTick(() => {
        this.$refs.qrposterlist.handlePageNum(1);
      });
    },
    setSelection(items) {
      console.log(items,'selectionsselections')
      this.selections = items;
    },
  },
};
</script>
<style lang="less" scoped>
.MarketingActivityQrposter {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  // padding: 20px 20px 0;
  &.poster__small {
    width: 380px;
    height: fit-content;
    // margin-top: 16px;
    border-radius: 2px;
    box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.08);
    background-color: #ffffff;
    box-sizing: border-box;
    overflow: hidden;
    padding: 0;
    flex-shrink: 0;
    .qrposter_header {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      padding: 14px 12px;
      border-bottom: 1px solid var(--color-neutrals05);
      .header__title {
        display: flex;
        align-items: center;
        color: #181c25;
        font-size: 14px;
        font-weight: 700;
        line-height: 28px;
      }
    }
  }
  &.big {
    .qrposter_header {
      padding: 14px 20px;
      padding-bottom: 5px;
    }
    .poster-gallery__list {
      padding: 0 10px;
    }
  }
  .qrposter_header {
    display: flex;
    padding-right: 21px;
    padding-top: 10px;
    padding-bottom: 10px;
    .header__title {
      color: #181c25;
      font-size: 14px;
      margin-right: auto;
    }
    .search-input {
      margin-left: auto;
    }
  }
  .content {
    // padding-left: 15px;
  }
  .invitation-content__poster-pagen {
    position: fixed;
    bottom: 0px;
  }
}
.sort-group {
  display: flex;
  height: 100%;
  .group-left {
    display: flex;
  }
  .group-right {
    flex: 1;
  }
}
</style>
