<template>
    <VDialog
      class="MarketingActivityQrposterSpreadDialog"
      :title="$t('marketing.commons.tghb_c91bd6')"
      width="1000px"
      :visible="visible"
      append-to-body
      showCancel
      :cancel-text="$t('marketing.commons.gb_b15d91')"
      @onSubmit="handleSubmit"
      @onClose="handleClose"
    >
      <div class="MarketingActivityQrposterSpreadDialog__body">
        <div class="km-g-loading-mask" v-if="dialogLoading">
          <span class="loading"></span>
        </div>
        <div class="body__left">
          <!-- <img
            v-show="flag_isLongImage"
            :class="['icon_longimg']"
            :src="icon_longimg"
          > -->
          <span :class="'longposter-icon'" v-show="flag_isLongImage">{{ $t('marketing.commons.ct_ce9ecb') }}</span>
          <div
            :class="['qrposter__cover', flag_isLongImage && 'long-img']"
          >
            <div
              v-if="!qrposter.qrPosterThumbnailUrl"
              class="cover__emptytips"
            >
              <!-- 自定义海报 -->
              <div class="emptytips__bg" />
            </div>
            <ElImage
              v-else
              :class="[
                'cover__image',
              ]"
              :src="qrposter.qrPosterThumbnailUrl"
              :preview-src-list="[qrposter.qrPosterThumbnailUrl]"
              :z-index="99999"
            />
          </div>
        </div>
        <div class="body__right">
          <div class="right__block flex-row">
            <div class="block__left">
              <div class="block__title">
                  {{ $t('marketing.components.MarketingActivityQrposter.plqf_365318') }}
                </div>
                <div class="block__tips">
              {{ $t('marketing.components.MarketingActivityQrposter.xmbkhqzplf_86bd49') }}
              </div>
            </div>
            <ElButton
                type="primary"
                size="mini"
                :class="['action-button',userLanguage === 'en' ? 'action-button-en' : '']"
                @click="handleSpread"
              >
              {{ $t('marketing.commons.qf_30269e') }}
              </ElButton>
          </div>
          <div class="right__block flex-row">
            <div class="block__left">
              <div class="block__title">
              {{ $t('marketing.components.SpreadSideslipDialog.qytg_966c63') }}
            </div>
            <div class="block__tips">
                 {{ $t('marketing.components.MarketingActivityQrposter.xyghbhyxft_870c81') }}
            </div>
            </div>
            <ElButton
                size="mini"
                plain
                type="primary"
                :class="['action-button',userLanguage === 'en' ? 'action-button-en' : '']"
                @click="handlePromotionActivityStaff"
              >
                {{ $t('marketing.components.SpreadSideslipDialog.fqqytg_b8e836') }}
              </ElButton>
          </div>
          <div class="right__block flex-row">
            <div class="block__left">
              <div class="block__title">
              {{ $t('marketing.commons.xzhb_3ab295') }}
              </div>
              <div class="block__tips">
              {{ $t('marketing.components.MarketingActivityQrposter.xzghbkzfwx_8f33a1') }}
            </div>
            </div>
             <ElButton
                size="mini"
                plain
                type="primary"
                :class="['action-button',userLanguage === 'en' ? 'action-button-en' : '']"
                :loading="isDownloading"
                @click="handleDownload"
              >
                {{ $t('marketing.commons.xzhb_3ab295') }}
              </ElButton>
          </div>
          <div class="right__block">
            <div class="block__title">
              {{ $t('marketing.commons.ygzztg_305a39') }}
              <fx-switch style="margin-left: 8px;" @change="changeCheckList" v-model="isMobileDisplayCheck" size="mini"> </fx-switch>
            </div>
             <div class="block__tips">
              {{ $t('marketing.components.MarketingActivityQrposter.xzghbkzfwx_8f33a1') }}
            </div>
            <div class="block__main" v-show="isMobileDisplayCheck">
              <div class="block_sub_title">
              {{ $t('marketing.commons.mrtgy_c36201') }}
              </div>
              <SloganAddBox :object-id="qrposter.qrPosterId" :object-type="24" :showSaveBtn="true" />
            </div>
          </div>
          <div class="right__block">
            <div class="block__title">
              {{ $t('marketing.commons.hbxx_f22f1d') }}
            </div>
            <div class="block__main info__main" style="margin-top: 20px;">
              <div
                v-for="item in data_poasterInfo"
                :key="item.label"
                class="main__row"
              >
                <div class="row__label">
                  {{ item.label }}
                </div>
                <div
                  :class="['row__value', item.clickFun && 'link']"
                  @click="handleInfoClick(item)"
                >
                  {{ item.value }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <SpreadDialog
          v-if="isShowSpreadDialog"
          :visible.sync="isShowSpreadDialog"
          scene="qrposter"
          :marketing-event-id="marketingEventId"
          :material="data_material"
          @close="isShowSpreadDialog = false"
        />
        <SpreadAllStaffDialog
          v-if="isShowSpreadAllStaffDialog"
          :visible.sync="isShowSpreadAllStaffDialog"
          :marketing-event-id="marketingEventId"
          :material="data_material"
          :poster-url="qrposter.qrPosterThumbnailUrl"
          :flag_isLongImage="flag_isLongImage"
          :show-preview-img="true"
          @close="isShowSpreadAllStaffDialog = false"
        />
        <SpreadPartnerDialog
          v-if="isShowSpreadPartnerDialog"
          :visible.sync="isShowSpreadPartnerDialog"
          :marketing-event-id="marketingEventId"
          :material="data_material"
          @close="isShowSpreadPartnerDialog = false"
        />
      </div>
    </VDialog>
</template>

<script>
import VDialog from '@/components/dialog'
import util from '@/services/util'
import { redirectToFS, downloadImage } from '@/utils'
import SpreadDialog from '@/components/SpreadDialog'
import SpreadAllStaffDialog from '@/components/SpreadAllStaffDialog'
import SpreadPartnerDialog from '@/components/SpreadPartnerDialog'
import icon_longimg from '@/assets/images/qrposter/icon_longimg.png'
import icon_longimg_en from '@/assets/images/qrposter/icon_longimg-en.png'
import QuestionTooltip from '@/components/questionTooltip/index'
import http from '@/services/http/index'
import SloganAddBox from '@/components/slogan-adddialog/index.vue'

import { getImageByLang } from '@/utils/i18n.js'

export default {
  components: {
    ElCheckboxGroup: FxUI.CheckboxGroup,
    ElCheckbox: FxUI.Checkbox,
    ElImage: FxUI.Image,
    ElButton: FxUI.Button,
    VDialog,
    SpreadDialog,
    SpreadAllStaffDialog,
    SpreadPartnerDialog,
    QuestionTooltip,
    SloganAddBox,
  },
  provide() {
    return {
      isPoster: true,
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    qrposter: {
      type: Object,
      default: ()=>{}
    },
    marketingEventId: {
      type: String,
      default: '',
    },
    materialLabel: {
      type: String,
      default: '',
    },
  },
  
  data() {
    return {
      icon_longimg: getImageByLang([icon_longimg, icon_longimg_en]),
      isShowSpreadDialog: false,
      isShowSpreadAllStaffDialog: false,
      isShowSpreadPartnerDialog: false,
      flag_imgLoading: true,
      flag_isLongImage: false,
      isMobileDisplayCheck: this.qrposter.isMobileDisplay,
      isDownloading: false,
      dialogLoading: false,
      posterDetail: {},
      userLanguage: FS.userLanguage,
    }
  },
  watch: {
    qrposter: {
      handler(newVal) {
        if(newVal) {
          this.isMobileDisplayCheck = newVal.isMobileDisplay
          this.queryPosterDetail()
        }
      },
      deep: true,
      immediate: true,
    },
  },
  computed: {
    data_poasterInfo() {
      const { qrposter } = this
      return [{
        label: $t('marketing.commons.hbmc_797511'),
        value: qrposter.title,
      }, {
        label: $t('marketing.commons.schd_a8559e'),
        value: qrposter.marketingEventTitle,
        clickFun: this.handleGoMarketingEvent,
      }, {
        label: $t('marketing.commons.smhtz_cc12c5'),
        value: this.getForwardName(),
      }, {
        label: $t('marketing.commons.cjsj_312f45'),
        value: util.formatDateTime(qrposter.createTime, 'YYYY-MM-DD hh:mm'),
      }]
    },
    data_material() {
      return {
        type: 24,
        objectType: 24,
        id: this.qrposter.qrPosterId,
      }
    },
  },
  mounted() {
    
  },
  methods: {
    changeCheckList() {
      const item = this.qrposter || {}
      const isMobileDisplay = !!this.isMobileDisplayCheck
      http
        .setContentMobileDisplay({
          marketingEventId: item.marketingEventId,
          objectId: item.qrPosterId,
          objectType: 24,
          isMobileDisplay,
        })
        .then(({ errCode }) => {
          if (errCode === 0) {
            FxUI.Message.success(
              isMobileDisplay ? $t('marketing.commons.szcg_f6088e') : $t('marketing.commons.qxszcg_beba4f'),
            )
            this.queryPosterDetail()
            this.$emit('isMobileDisplayChange', isMobileDisplay)
          }
          if (errCode !== 0) {
            FxUI.Message.error($t('marketing.commons.szsb_9f9603'))
            this.isMobileDisplayCheck = isMobileDisplay
          }
        })
    },
    getForwardName() {
      const { forwardType, forwardName, forwardContent } = this.qrposter
      if (forwardType === 10) {
        return $t('marketing.commons.ylgzh_97a07f')
      }
      if (forwardType === 3) {
        return $t('marketing.commons.tgnr_a6ec90') + (forwardContent ? ` - ${forwardContent}` : '')
      }
      if (forwardType === 13) {
        return $t('marketing.commons.qwyghm_a3fe61')
      }
      return forwardName + (forwardContent ? ` - ${forwardContent}` : '')
    },
    handleSubmit() {
      this.$emit('update:visible', false)
    },
    handleClose() {
      this.$emit('update:visible', false)
    },
    handleSpread() {
      this.isShowSpreadDialog = true
    },
    handlePromotionPartner() {
      this.isShowSpreadPartnerDialog = true
    },
    handlePromotionActivityStaff() {
      this.isShowSpreadAllStaffDialog = true
      // this.$router.push({
      //   name: "promotion-activity-staff",
      //   params: { type: "create" },
      //   query: {
      //     id: this.qrposter.marketingEventId,
      //     contentType: 24,
      //     contentId: this.qrposter.qrPosterId,
      //   },
      // })
    },
    async handleDownload() {
      const { qrPosterUrl, qrPosterApath } = this.qrposter

      if (!qrPosterApath) {
        FxUI.Message.error($t('marketing.commons.ewmwjhqsbq_4e0d4c'))
        return
      }

      // const isSpecial = /^C_/.test(qrPosterApath) // TODO 临时方案
      // if (isSpecial) {
      //   this.isDownloading = true
      //   const fileName = this.qrposter.title.replace(/[\\\/:*?"<>|]/g, '-') || qrPosterApath
      //   await downloadImage(qrPosterUrl, fileName)
      //   this.isDownloading = false

      //   return
      // }
      let ext;
      if(qrPosterApath.indexOf('.') > -1){
        ext = qrPosterApath.split('.')[1];
      } else {
        ext = 'png'
      }
      redirectToFS(
        `/FSC/EM/File/DownloadByPath?path=${
          qrPosterApath
        }&name=${this.qrposter.title.replace(/[\\\/:*?"<>|]/g, '-')}.${ext}`,
        '_self',
      )
    },

    handleInfoClick(infoItem) {
      if (infoItem.clickFun) {
        infoItem.clickFun(infoItem)
      }
    },
    handleGoMarketingEvent() {
      YXT_ALIAS.http.checkConferenceStatus({ marketingEventId: this.qrposter.marketingEventId }).then(res => {
        console.log(res)
        if (res && res.errCode === 0) {
          if (res.data.bindConference) {
            // 跳会议聚合页
            this.$router.push({
              name: 'meeting-detail',
              params: {
                id: res.data.bindConferenceId,
              },
            })
          } else {
            // 跳市场活动聚合页
            this.$router.push({
              name: 'marketing-calendar-dashboard',
              params: {
                id: this.qrposter.marketingEventId,
              },
            })
          }
        }
      })
    },
    presetQrposter() {
      if (!this.qrposter || !this.qrposter.qrPosterThumbnailUrl) {
        return
      }
      const QrPoster = new Image()
      QrPoster.src = this.qrposter.qrPosterThumbnailUrl
      QrPoster.onload = () => {
        this.flag_imgLoading = false
        if (QrPoster.height > 1334) {
          this.flag_isLongImage = true
        }
      }
      QrPoster.onerror = () => {
        this.flag_imgLoading = false
      }
    },
    async queryPosterDetail(){
      if(!this.qrposter || !this.qrposter.qrPosterId) {
        return
      }
      this.dialogLoading = true
      const res = await http.queryPosterDetail({
        qrPosterId: this.qrposter.qrPosterId
      })
      if(res.errCode === 0 && res.data){
        this.posterDetail = {
          ...this.qrposter,
          ...res.data
        }
        this.isMobileDisplayCheck = this.posterDetail.isMobileDisplay
        this.presetQrposter()
      }
      this.dialogLoading = false
    }
  },
}
</script>

<style lang="less" scoped>
@basePath: "../../";
.MarketingActivityQrposterSpreadDialog {
  /deep/ .el-dialog__body {
    padding: 0;
  }
  .MarketingActivityQrposterSpreadDialog__body {
    height: 640px;
    overflow: auto;
    display: flex;
    .body__left {
      position: relative;
      width: 300px;
      height: 100%;
      overflow: auto;
      background: #FAFAFA;
      display: flex;
      // align-items: center;
      justify-content: center;
      padding-top: 20px;
      box-sizing: border-box;
      .icon_longimg {
        position: absolute;
        top: 20px;
        left: 25px;
        width: 40px;
        z-index: 1;
      }
      .qrposter__cover {
        width: 250px;
        height: 605px;
        background: #FAFAFA;
        overflow: auto;
        box-sizing: border-box;
        background-size: cover;
        // box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.2);
        line-height: 0;
        &:not(.long-img) {
          display: flex;
          justify-content: center;
          align-items: center;
        }
        .cover__emptytips {
          color: #c1c5ce;
          font-size: 12px;
          .emptytips__bg {
            background-image: url('@{basePath}assets/images/defualt-pic.png');
            width: 70px;
            height: 57px;
            background-size: cover;
          }
        }
        .cover__image {
          width: 100%;
          box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.2);
        }
      }
    }
    .body__right {
      height: 100%;
      flex: 1;
      overflow: auto;
      .right__block {
        padding: 20px 20px 20px 0;
        border-bottom: 1px dashed var(--color-neutrals05);
        &.flex-row{
          display: flex;
          justify-content: space-between;
          align-items: center;
          gap: 20px;
        }
        .block__title {
          position: relative;
          padding-left: 11px;
          font-size: 14px;
          color: #181C25;
          font-weight: bold;
          line-height: 20px;
          &::before {
            position: absolute;
            left: 0;
            top: 3px;
            content: '';
            width: 4px;
            height: 12px;
            background: var(--color-primary06,#ff8000);
            flex-shrink: 0;
          }
        }
        .block_sub_title{
          color: var(--color-neutrals19);
          font-feature-settings: 'liga' off, 'clig' off;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 20px; /* 142.857% */
          margin-top: 10px;
          margin-bottom: 8px;
        }
        .block__tips {
          padding-left: 11px;
          color: #91959E;
          font-size: 12px;
          margin-top: 5px;
        }
        .block__main {
          padding-left: 11px;
          button {
            width: 140px;
          }
        }
        .info__main{
          display: flex;
          flex-direction: column;
          gap: 10px;
          color: #545861;
          line-height: 20px;
          .main__row{
            display: flex;
          }
          .row__label {
              flex-shrink: 0;
              min-width: 100px;
              margin-right: 10px;
            }
            .row__value {
              &.link {
                color: var(--color-info06,#407FFF);
                cursor: pointer;
              }
            }
            .question {
              margin-left: 12px;
            }
        }
        .action-button {
          min-width: 98px;
          width: fit-content;
          white-space: nowrap;
          text-align: center;
          &.action-button-en {
            min-width: 128px;
          }
        }
      }
    }
  }
}

</style>
