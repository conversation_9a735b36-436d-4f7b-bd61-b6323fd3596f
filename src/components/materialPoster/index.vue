<template>
  <fx-dialog
    :visible.sync="dialogVisible"
    :title="$t('marketing.commons.nrhb_77b1ed')"
    width="1000px"
    class="marketing-material-poster-dialog"
    zIndex="100"
    @close="handleClose"
  >
    <div class="material-poster-dialog-body">
      <div class="km-g-loading-mask" v-if="posterLoading">
        <span class="loading"></span>
      </div>
      <div class="dialog-header" v-if="posterList.length">
        <div class="header-left" v-if="!isBatchMode">
          <fx-button type="primary" @click="handleCreatePoster" size="small"
            >+ {{ $t("marketing.commons.zzhb_80c7a4") }}</fx-button
          >
          <fx-button
            size="small"
            v-if="posterList.length > 1"
            plain
            @click="toggleBatchMode"
          >
            {{ $t("marketing.components.materialPoster.plcz_7f7c62") }}
          </fx-button>
        </div>
        <div class="header-left" v-else>
          <fx-checkbox
            v-model="allChecked"
            @change="handleSelectAll"
            :size="size"
            >{{
              $t("marketing.components.materialPoster.qx_66eeac")
            }}</fx-checkbox
          >
        </div>
        <div class="header-right" v-if="isBatchMode || selectedItemList.length">
          <fx-button size="small" type="primary" @click="handleBatchDownload">{{
            $t("marketing.pages.wechat.plxz_a3f6b8")
          }}</fx-button>
          <fx-button size="small" plain @click="handleBatchDelete">{{
            $t("marketing.commons.plsc_7fb62b")
          }}</fx-button>
          <fx-button plain size="small" @click="handleCloseBatchMode">{{
            $t("marketing.commons.qx_625fb2")
          }}</fx-button>
        </div>
      </div>

      <div class="poster-grid" v-if="posterList.length">
        <div
          v-for="(item, index) in posterList"
          :key="index"
          class="poster-item"
          @mouseenter="handleMouseEnter(index)"
          @mouseleave="handleMouseLeave(index)"
        >
          <!-- 批量选择框 -->
          <fx-checkbox
            v-model="item.checked"
            @change="handleSelect(item)"
            :class="[
              'poster-checkbox',
              { 'display-inline-block': allChecked || item.checked }
            ]"
          />

          <div :class="['poster-image', item.isLongImage ? 'long-img' : '']" :style="{ 'background-image': `url(${item.qrPosterUrl})` }">
            <span class="longposter-icon" v-show="item.isLongImage">{{ $t('marketing.commons.ct_ce9ecb') }}</span>
          </div>

          <div class="poster-info">
            <div class="poster-title km-t-ellipsis2">{{ item.title }}</div>
            <div v-show="item.showPromote" class="promote-button">
              <fx-button
                type="primary"
                size="micro"
                @click="handleSpread(item)"
                >{{ $t("marketing.commons.tg_9a8e91") }}</fx-button
              >
              <fx-button plain size="micro" @click="handleDelete(item)">{{
                $t("marketing.commons.sc_2f4aad")
              }}</fx-button>
            </div>
          </div>
        </div>
      </div>
      <div class="poster-empty" v-else>
        <Empty
          class="empty-wrapper"
          type="box"
          style="width: 100%;padding: 80px 0 130px 0;"
          :title="$t('marketing.components.materialPoster.gnrzwgldhb_3d147c')"
          :button="$t('marketing.components.materialPoster.ljzznrhb_92fb7e')"
          imgWidth="375px"
          imgHeight="auto"
          @onClick="handleCreatePoster"
        />
      </div>

      <div class="dialog-footer">
        <div class="footer-left">
          <template>
            <span class="selected-count">{{
              $t("marketing.components.materialPoster.xzt_c5bc82", {
                data: { option0: selectedCount }
              })
            }}</span>
          </template>
        </div>
        <div class="footer-right">
          <fx-pagination
            @current-change="handleCurrentChange"
            layout="total2, jumper2, prev2, next2"
            :page-size="pageSize"
            :total="total"
          >
          </fx-pagination>
          <fx-button size="small" plain @click="handleClose">{{
            $t("marketing.commons.qx_625fb2")
          }}</fx-button>
        </div>
      </div>
    </div>

    <qrposter-create-dialog
      v-show="isShowCreateDialog"
      :visible.sync="isShowCreateDialog"
      :defaultMarketingEventId="marketingEventId"
      :disabled-jump-type="true"
      @complete:create="posterCreated"
      :jumpModelTypeConfig="{
        model_type: material.siteType === 'EVEN_HOMEPAGE' ? 4 : 1,
        model_marketing_materiel: {
          id:
            material.siteType === 'EVEN_HOMEPAGE' && material.targetObjectId
              ? material.targetObjectId
              : material.id,
          type: formatMaterialType,
          title: material.title
        }
      }"
      :z-index="200"
      ref="qrposterCreate"
    ></qrposter-create-dialog>
    <MarketingActivityQrposterSpreadDialog
      v-show="isShowSpreadDialog"
      :visible.sync="isShowSpreadDialog"
      :marketingEventId="marketingEventId"
      @update:visible="onSpreadDialogClose"
      :qrposter="selectedItem"
    ></MarketingActivityQrposterSpreadDialog>
  </fx-dialog>
</template>

<script>
import QrposterCreateDialog from "../qrposter-create-dialog/index.vue";
import MarketingActivityQrposterSpreadDialog from "../MarketingActivityQrposter/MarketingActivityQrposterSpreadDialog.vue";
import http from "@/services/http/index.js";
import Empty from "@/components/common/empty.vue";
import { redirectToFS } from "@/utils";
import { confirm } from "@/utils/globals";
import { formatMaterialType } from "@/constants/materialTypeMapping.js";

export default {
  name: "MaterialPosterDialog",
  components: {
    QrposterCreateDialog,
    MarketingActivityQrposterSpreadDialog,
    Empty
  },
  props: {
    marketingEventId: {
      type: String,
      default: ""
    },
    material: {
      type: Object,
      default: () => {}
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isBatchMode: false,
      pageSize: 8,
      pageNum: 1,
      total: 0,
      posterList: [],
      allChecked: false,
      isShowCreateDialog: false,
      isShowSpreadDialog: false,
      selectedItem: {},
      channels: [],
      dialogVisible: false,
      posterLoading: true,
      selectedItemList: [],
      selectedCount: 0
    };
  },
  computed: {
    // 因为在生成海报时物料选择器中，产品、文章、微页面、表单的类型不一样，所以需要转换
    formatMaterialType() {
      return formatMaterialType(this.material.objectType);
    }
  },
  watch: {
    visible: {
      immediate: true,
      handler(val) {
        this.dialogVisible = val;
        if (val) {
          // 当显示对话框时，获取数据
          this.getPosterList();
        }
      }
    },
    // 监听子弹窗状态，当子弹窗打开时隐藏当前弹窗
    isShowCreateDialog(val) {
      if (val) {
        this.$el.style.opacity = '0';
        this.$el.style.pointerEvents = 'none';
      } else {
        this.$el.style.opacity = '1';
        this.$el.style.pointerEvents = 'auto';
      }
    },
    isShowSpreadDialog(val) {
      if (val) {
        this.$el.style.opacity = '0';
        this.$el.style.pointerEvents = 'none';
      } else {
        this.$el.style.opacity = '1';
        this.$el.style.pointerEvents = 'auto';
      }
    }
  },
  methods: {
    onSpreadDialogClose() {
      this.isShowSpreadDialog = false;
      this.getPosterList();
    },
    posterCreated() {
      this.getPosterList()
      this.$root.$emit('poster-created')
    },
    handleClose() {
      this.$emit("close");
      this.resetData();
    },
    handleConfirm() {
      const selectedPosters = this.posterList.filter(item => item.checked);
      this.$emit("confirm", selectedPosters);
      this.handleClose();
    },
    toggleBatchMode() {
      this.isBatchMode = true;
      this.posterList.forEach(item => (item.checked = false));
    },
    handleMouseEnter(index) {
      this.$set(this.posterList[index], "showPromote", true);
    },
    handleMouseLeave(index) {
      this.$set(this.posterList[index], "showPromote", false);
    },
    handleSpread(item) {
      this.selectedItem = item;
      this.isShowSpreadDialog = true;
    },
    resetData() {
      this.isBatchMode = false;
      this.pageNum = 1;
      this.posterList = [];
    },
    // 会议主页使用会议id查询海报
    async getPosterList() {
      this.posterLoading = true;
      if (!this.material.id || !this.marketingEventId) {
        this.posterLoading = false;
        return;
      }
      const params = {
        targetId:
          this.material.siteType === "EVEN_HOMEPAGE" &&
          this.material.targetObjectId
            ? this.material.targetObjectId
            : this.material.id,
        pageSize: this.pageSize,
        pageNum: this.pageNum,
        marketingEventId: this.marketingEventId,
        keyword: "",
        ea: FS.contacts.getCurrentEmployee().enterpriseAccount
      };
      const res = await http.queryEvenQrPosterByTargetId(params);
      if (res && res.errCode === 0 && res.data && res.data.result) {
        this.posterList = res.data.result.map((item) => ({
          ...item,
          isLongImage: false // 先默认 false
        }));
        this.total = res.data.totalCount;

        // 检查每张图片高度
        this.posterList.forEach((item, idx) => {
          const posterImage = new window.Image();
          posterImage.src = item.qrPosterUrl;
          posterImage.onload = () => {
            if (posterImage.height > 1334) {
              this.$set(this.posterList, idx, { ...item, isLongImage: true });
            }
          };
          posterImage.onerror = () => {
            this.$set(this.posterList, idx, { ...item, isLongImage: false });
          };
        });
      }
      this.posterLoading = false;
    },
    handleCurrentChange(val) {
      this.pageNum = val;
      this.getPosterList();
    },
    async handleSelectAll() {
      if (this.allChecked) {
        this.posterList.forEach(item => (item.checked = true));
        this.selectedCount = this.posterList.length;
        this.selectedItemList = this.posterList;
      } else {
        this.posterList.forEach(item => (item.checked = false));
        this.selectedCount = 0;
        this.selectedItemList = [];
      }
    },
    handleCreatePoster() {
      this.isShowCreateDialog = true;
    },
    handleComplete({ total, successCount }) {
      if (this.generationStatus.failed.length === 0) {
        this.$message.success(
          $t("marketing.components.materialPoster.hbscwcgqdq_f840a1", {
            data: { option0: successCount }
          })
        );
      } else {
        this.$message.warning(
          $t("marketing.components.materialPoster.hbscwccgg_5a1bac", {
            data: { option0: this.generationStatus.success.length }
          }) +
            $t("marketing.components.materialPoster.sbg_76abbd", {
              data: { option0: this.generationStatus.failed.length }
            })
        );
      }
      this.pageNum = 1;
      this.getPosterList();
    },
    async handleBatchDownload() {
      if (!this.selectedItemList.length) {
        FxUI.Message.warning(this.$t("marketing.commons.qxz_7fb62b"));
        return;
      }
      FxUI.Message.info($t("marketing.commons.xzzqsh_6a2f7c"));
      for (let i = 0; i < this.selectedItemList.length; i++) {
        const item = this.selectedItemList[i];
        this.handleDownloadPoster(item);
      }
      this.resetPosterList();
    },
    handleBatchDelete() {
      const idList = this.selectedItemList.map(item => item.qrPosterId);
      confirm(
        $t("marketing.commons.qqrsfyscch_4d0b78"),
        $t("marketing.commons.ts_02d981"),
        {
          callback: action => {
            if (action === "confirm")
              http.deleteQRPosterBatch({ idList }).then(res => {
                if (res && res.errCode === 0) {
                  FxUI.Message.success($t("marketing.commons.sccg_43593d"));
                  this.getPosterList();
                }
              });
          }
        }
      );
      this.resetPosterList();
    },
    handleDelete(item) {
      confirm(
        $t("marketing.commons.qqrsfyscch_4d0b78"),
        $t("marketing.commons.ts_02d981"),
        {
          customClass: 'material-poster-delete-confirm',
          zIndex: 10000,
          callback: action => {
            if (action === "confirm")
              http.deleteQRPoster({ qrPosterId: item.qrPosterId }).then(res => {
                if (res && res.errCode === 0) {
                  FxUI.Message.success($t("marketing.commons.sccg_43593d"));
                  this.getPosterList();
                }
              });
          }
        }
      );
    },
    handleSelect(item) {
      this.selectedCount = this.posterList.filter(item => item.checked).length;
      this.selectedItemList = this.posterList.filter(item => item.checked);
    },
    async handleDownloadPoster(poster) {
      const { qrPosterApath } = poster;
      if (!qrPosterApath) {
        FxUI.Message.error(this.$t("marketing.commons.ewmwjhqsbq_4e0d4c"));
        return Promise.reject();
      }
      let ext;
      if (qrPosterApath.indexOf(".") > -1) {
        ext = qrPosterApath.split(".")[1];
      } else {
        ext = "png";
      }
      const downloadUrl = `/FSC/EM/File/DownloadByPath?path=${qrPosterApath}&name=${poster.title}.${ext}`;
      const iframe = document.createElement("iframe");
      iframe.style.display = "none";
      iframe.src = downloadUrl;
      document.body.appendChild(iframe);
      iframe.onload = () => {
        setTimeout(() => {
          document.body.removeChild(iframe);
          resolve();
        }, 500);
      };
    },
    handleCloseBatchMode() {
      this.isBatchMode = false;
      this.allChecked = false;
      this.posterList.forEach(item => (item.checked = false));
      this.selectedCount = 0;
      this.selectedItemList = [];
    },
    resetPosterList() {
      this.posterList.forEach(item => {
        item.checked = false;
      });
      this.selectedCount = 0;
      this.selectedItemList = [];
    }
  }
};
</script>

<style lang="less" scoped>
.marketing-material-poster-dialog {
  // 确保弹窗层级正确
  :deep(.qrposter-create-dialog) {
    z-index: 9999 !important;
  }
  
  // 确保删除确认弹窗层级正确
  :deep(.material-poster-delete-confirm) {
    z-index: 10000 !important;
  }

  /deep/ .el-dialog__body {
    padding: 0;
  }

  .dialog-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    border-top: 1px solid #ebeef5;

    .footer-left {
      .selected-count {
        font-size: 12px;
        color: #606266;
        padding: 0 8px;
      }
    }

    .footer-right {
      display: flex;
      align-items: center;
      gap: 24px;

      .total-count {
        font-size: 12px;
        color: #606266;
      }
      
      .simple-pagination {
        :deep(.el-pagination__jump) {
          display: none;
        }
      }

      .page-text {
        font-size: 14px;
        color: #606266;
      }

      .page-actions {
        display: flex;
        gap: 8px;

        .fx-button {
          padding: 0;
          width: 32px;
          height: 32px;
        }
      }
    }
  }
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20px;
}

.poster-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin: 0 20px 20px;
}

.poster-item {
  position: relative;
  border-radius: 4px;
  transition: all 0.3s;

  .poster-checkbox {
    position: absolute;
    top: 8px;
    left: 8px;
    z-index: 2;
    display: none;
    &.display-inline-block {
      display: inline-block;
    }
  }

  .poster-image {
    position: relative;
    height: 347px;
    border: 1px solid #EAEBEE;
    background: #f4f4f4;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    .longposter-icon {
      position: absolute;
      top: -5px;
      left: -31px;
      display: inline-block;
      width: 80px;
      color: #fff;
      font-size: 12px;
      height: 30px;
      line-height: 40px;
      background-color: #94ce55;
      text-align: center;
      -webkit-transform: rotate(-45deg);
      transform: rotate(-45deg);
      z-index: 1;
    }
    &.long-img {
      background-size: cover;
      background-position: top;
    }

    .promote-button {
      display: none;
    }
  }

  .poster-info {
    padding: 12px;
    background: rgba(0, 0, 0, 0.6);
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    gap: 8px;

    .poster-title {
      font-size: 14px;
      color: #fff;
      line-height: 1.4;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
  }
  &:hover {
    .promote-button {
      display: block;
    }
    .poster-checkbox {
      display: inline-block;
    }
  }
}
</style>
