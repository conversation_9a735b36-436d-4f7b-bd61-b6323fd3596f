<!--
 * @Author: <EMAIL>
 * @Date: 2021-05-10 10:43:54
 * @Description: 图片裁剪
-->
<template>
  <div class="BodyCutPicture">
    <div class="BodyCutPicture__preview">
      <div class="preview__title">{{ $t('marketing.components.PictureSelector.ylfm_fa6e0a') }}</div>
      <div :class="['preview__item', cropSizeIndex === index ? 'preview__item-active' : '']"
        v-for="(item, index) in cropSizeListValue" :key="index">
        <div class="item__title">{{ item.title }}<p>{{ item.width }}*{{ item.height }}{{ $t('marketing.components.PictureSelector.xs_6fa81e') }}</p>
        </div>
        <ElImage class="preview__img" :style="{
          height: (104 - (104 * 0.2) * index) + 'px',
          width: (item.value ? (item.value.width / item.value.height) : (item.width / item.height)) * (104 - (104 * 0.2) * index) + 'px'
        }" fit="cover" :src="item.value && item.value.image || ''"></ElImage>
        <div class="preview__desc">
          <div class="text">
            {{ item.desc }}
          </div>
        </div>
      </div>
    </div>
    <div class="BodyCutPicture__cutter">
      <div class="cutter__tabs">
        <template v-if="cutOption.cropSizeList.length > 1">
          <div class="tabs-pic-tips">
            <template v-if="needTempSave">
              <div v-loading="tempSaveLoading" class="tabs-pic-tips-content is-active">{{ $t('marketing.components.PictureSelector.ddscdtpcjw_919f69') }}<Button type="text" @click="handleConfirm">{{ $t('marketing.components.PictureSelector.ljbc_5e2d1e') }}</Button></div>
              <Button type="text" icon="iconfont iconshangchuan" @click="doUpload" >{{ $t('marketing.commons.zxxz_5a0aa4') }}</Button>
            </template>
            <template v-else>
              <div class="tabs-pic-tips-content">{{ $t('marketing.components.PictureSelector.rdqtpbfhfm_923a18') }}</div>
              <Button type="text" icon="iconfont iconshangchuan" @click="doUpload" >{{ $t('marketing.components.PictureSelector.ddxz_61ad61') }}</Button>
            </template>
          </div>

          <RadioGroup v-model="cropSizeIndex" class="cutter__tabs-group" :disabled="tempSaveLoading" @change="handleCutSizeChange">
            <RadioButton v-for="(item, index) in cutOption.cropSizeList" :label="index" :key="index">{{ item.title }}
            </RadioButton>
          </RadioGroup>
          <div class="cutter__tabs-group temp-save-cover" v-if="needTempSave" @click="tempSave"></div>
        </template>
        <template v-else>{{ $t('marketing.commons.cjq_aa8fbb') }}</template>
      </div>
      <div class="cutter__cropper" ref="cropper"></div>
    </div>
    <Dialog
      :visible.sync="dialogVisible"
      :title="$t('marketing.commons.ts_02d981')"
      size="small"
      custom-class="my_picture-selector-dialog"
      showFooterAction
      :confirmButtonText="$t('marketing.components.PictureSelector.ljbc_5e2d1e')"
      :cancelButtonText="$t('marketing.commons.bxy_8755a5')"
      @confirm="handleConfirm"
    >
      <div>
        {{ $t('marketing.components.PictureSelector.ddscdtpcjw_7ebd7e') }}
      </div>
      <div slot="footer" class="dialog-footer">
        <Button type="primary" @click="handleConfirm" size="small">{{$t('marketing.components.PictureSelector.ljbc_5e2d1e')}}</Button>
        <Button @click="dialogVisible = false" size="small">{{$t('marketing.commons.bxy_8755a5')}}</Button>
      </div>
    </Dialog>
  </div>
</template>

<script>

import SmartCropper from "@/modules/SmartCropper";
import PictureStandard from "@/components/PictureStandard/index";
import http from '@/services/http/index';
import { filterCrmContactByUserRoles } from "@/utils";

export default {
  components: {
    ElImage: FxUI.Image,
    RadioGroup: FxUI.RadioGroup,
    RadioButton: FxUI.RadioButton,
    Button: FxUI.Button,
    Dialog: FxUI.Dialog,
    PictureStandard
  },
  props: {},
  data() {
    return {
      curPreviewUrl: "",
      isShowImageViewer: false,
      cropSizeIndex: 0,
      cropSizeListValue: [],
      needTempSave: false,
      tempSaveLoading: false,
      isSizeChangeFlag: false,
      dialogVisible: false,
    };
  },
  computed: {

    sizePreview() {
      const { cutWidth: w, cutHeight: h } = this.cutOption;
      return {
        width: "180px",
        height: (h / w) * 180 + "px"
      };
    },
    middleSizePreview() {
      return {
        width: "100px",
        height: (336 / 420) * 100 + "px"
      };
    },
    smallSizePreview() {
      return {
        width: "60px",
        height: (300 / 300) * 60 + "px"
      };
    },
    $state() {
      return this.$store.state.PictureSelector;
    },
    selected() {
      return this.$state.selecteds[0];
    },
    cutOption() {
      return this.$state.cutOption || {};
    },
    curImageUrl() {
      let url = this.selected.url;
      if(this.cropSizeListValue[this.cropSizeIndex] && this.cropSizeListValue[this.cropSizeIndex].value) {
        url = this.cropSizeListValue[this.cropSizeIndex].value.originUrl;
      }
      if (APP_MARKETING_ENV === "DEV" && url) {
        const { protocol, host } = location;
        url = url.replace(/^(https|http):\/\/[a-z0-9]+.ceshi112.com/, `${protocol}//${host}`);
      }
      return url;
    },
    curStepId() {
      return this.$store.state.PictureSelector.curStepId;
    },

    needApath() {
      return this.$store.state.PictureSelector.needApath;
    },
    keepCutSizeRatio() {
      return this.$store.state.PictureSelector.keepCutSizeRatio;
    },
    tempSelected() {
      return this.$store.state.PictureSelector.tempSelected;
    },
    outputPathType() {
      let _return = this.$store.state.PictureSelector.outputPathType.toLocaleLowerCase();
      _return = _return || (this.needApath ? "a" : "ta"); // 支持旧版：没传 outputPathType 传了 needApath
      return _return;
    },
    paramsOfUploadInterface() {
      let _return = {};
      if (/^(ta|a)$/.test(this.outputPathType)) {
        _return = {
          options: {
            url: `${FS.BASE_PATH}/FSC/EM/File/UploadByForm?needCdn=true`,
            type: 2
          },
          onSave: {
            fields: {
              // type: 1,
              // needApath: this.outputPathType === "a",
              // needPermanent: this.outputPathType === "a"
            }
          }
        };
      } else if (/^(tn|n)$/.test(this.outputPathType)) {
        _return = {
          options: {
            url: `${location.origin}/FSC/EM/File/UploadByStream`,
            type: 3
          },
          onSave: {}
        };
      }
      return _return;
    }
  },
  watch: {
    selected: {
      deep: true,
      handler(val) {
        // 获取当前操作的尺寸索引
        const currentCropIndex = this.$store.state.PictureSelector.currentCropIndex;
        // 如果有临时保存的数据，说明是从裁剪页面返回选择新图片
        if (this.tempSelected && currentCropIndex !== undefined) {
          // 只更新当前尺寸的图片
          if (this.cropSizeListValue[currentCropIndex]) {
            this.$set(this.cropSizeListValue[currentCropIndex], 'value', {
              image: val.url,
              originUrl: val.url,
              originalPath: val.photoPath,
            });
          }
        } else {
          // 正常初始化所有尺寸
          const { cropSizeList } = this.cutOption;
          this.cropSizeListValue = JSON.parse(JSON.stringify(cropSizeList || []));
          this.cropSizeListValue.map((item, i) => {
            this.$set(item, 'value', {
              image: val.url,
              originUrl: val.url,
              originalPath: val.photoPath,
            })
          });
        }
        console.log('selectedselected',this.cropSizeListValue)
        
        this.$nextTick(() => {
          this.cutterRender();
        });
      }
    },
    curStepId(val, oldVal) {
      // 当返回上一步时
      if(val < oldVal) {
       this.clearTempData();
      }
    },
    tempSelected(val) {
      if(val) {
        this.needTempSave = true;
        
        // 如果是新的数据结构（按尺寸索引存储）
        if (typeof val === 'object' && !val.url) {
          // 恢复所有尺寸的裁剪信息
          Object.keys(val).forEach(index => {
            const cropInfo = val[index];
            if (this.cropSizeListValue[index]) {
              this.cropSizeListValue[index].value = {
                image: cropInfo.image,
                originUrl: cropInfo.originUrl,
                originalPath: cropInfo.originalPath,
                width: cropInfo.width,
                height: cropInfo.height,
                top: cropInfo.top,
                left: cropInfo.left,
              };
              if (cropInfo.cropApath) {
                this.cropSizeListValue[index].cropApath = cropInfo.cropApath;
              }
            }
          });
        } else {
          // 兼容旧版本，只恢复当前尺寸
          if(this.cropSizeListValue[this.cropSizeIndex] && this.cropSizeListValue[this.cropSizeIndex].value) {
            this.cropSizeListValue[this.cropSizeIndex].value.image = val.url;
            this.cropSizeListValue[this.cropSizeIndex].value.originUrl = val.url;
            this.cropSizeListValue[this.cropSizeIndex].value.originalPath = val.originalPath;
          }
        }
        
        this.$nextTick(() => {
          this.cutterRender();
        });
      }
    }
  },
  created() {
    console.log('this.$store.state.PictureSelector是否被重新初始化')
  },
  methods: {
    doUpload() {
      // 保存当前尺寸的裁剪信息到临时存储
      const currentIndex = this.cropSizeIndex;
      const currentItem = this.cropSizeListValue[currentIndex];
      
      if (currentItem && currentItem.value) {
        // 保存当前尺寸的裁剪信息
        const tempSelected = {
          ...this.tempSelected, // 保留其他尺寸的信息
          [currentIndex]: {
            image: currentItem.value.image,
            originUrl: currentItem.value.originUrl,
            originalPath: currentItem.value.originalPath,
            cropApath: currentItem.cropApath,
            // 保存裁剪参数
            width: currentItem.value.width,
            height: currentItem.value.height,
            top: currentItem.value.top,
            left: currentItem.value.left,
            photoTargetType: currentItem.photoTargetType
          }
        };
        this.$store.commit('PictureSelector/setTempSelected', tempSelected);
      }
      
      // 记录当前操作的尺寸索引，用于后续恢复
      this.$store.commit('PictureSelector/setState', { 
        currentCropIndex: currentIndex,
        curStepId: 1 
      });
    },
    clearTempData() {
      // 只有在没有临时保存数据时才清除
      if (!this.tempSelected) {
        this.needTempSave = false;
        this.cropSizeListValue = [];
        this.$store.commit('PictureSelector/setTempSelected', null);
        if(this.selected) {
          let tempSelected = JSON.parse(JSON.stringify(this.selected));
          this.$nextTick(()=> {
            this.$store.dispatch('PictureSelector/setSelected', tempSelected);
          })
        }
      }
    },
    async handleConfirm() {
      this.dialogVisible = false;
      console.log(this.cropSizeIndex,'cropSizeIndex')
      console.log(this.cropSizeListValue,'handleConfirmhandleConfirm')
      let targetItem = this.cropSizeListValue[this.cropSizeIndex];
          // try {
          //   delete targetItem.value.image;
          // } catch (error) {}
          let postList = [{
            ...targetItem.value,
            photoTargetType: targetItem.photoTargetType,
          }];
          this.tempSaveLoading = true;
          const { errCode, data } = await http.getCPathUrl({
            originalImageAPath: targetItem.value.originalPath || targetItem.value.path,
            cutOffsetList: postList,
            needOriginal: true
          });
          if (errCode === 0) {
            targetItem.value.image = data.urlList[0];
            targetItem.cropApath = data.apath;
            this.$store.commit('PictureSelector/setTempSelected', null);
            this.needTempSave = false;
            FxUI.Message.success($t('marketing.components.PictureSelector.bccg_c00eba'));
          }else {
            FxUI.Message.error($t('marketing.components.PictureSelector.bcsb_31ab85'));
          }
          this.tempSaveLoading = false;
    },
    tempSave() {
      this.dialogVisible = true;
    },
    handleCutSizeChange(index) {
      const item = this.cropSizeListValue[index];
      let cutInfo = {
        destWidth: item.width,
        destHeight: item.height,
      }
      if (item.value && item.value.width) {
        // 这里先不能转，需要先把新的图加载完才能转
        cutInfo = {
            ...cutInfo,
            ...item.value,
        }
      }
      this.isSizeChangeFlag = true;
      if (this.MyCropper) {
        this.tempSaveLoading = true;
        this.MyCropper.setImageWithCutBox(this.curImageUrl, cutInfo, ()=>{
          this.tempSaveLoading = false;
        });
      }
    },
    async cutterRender() {
      if (this.MyCropper) {
        this.MyCropper.destroy();
      }
      const { cropSizeList } = this.cutOption;

      const { width, height } = cropSizeList[this.cropSizeIndex] || {};

      this.MyCropper = SmartCropper(this.$refs.cropper, {
        ...this.paramsOfUploadInterface.options,
        defaultImg: this.curImageUrl,
        size: {
          width: 470,
          height: 470
        },
        cutSize: {
          width: width,
          height: height
        },
        minCutSize: {
          width: 100,
          height: 100 // 不能设为400，源码有bug：在第566行————if (h < msize.height) h = msize.height;
        },
        keepCutSizeRatio: this.keepCutSizeRatio,
        onImageLoaded: async (img) => {
          if (cropSizeList.length > 1){
            this.initImageDefaultCropParam(img)
          }
        }
      });

      this.MyCropper.onChange((res, cutInfo) => {
        let currentValue = this.cropSizeListValue[this.cropSizeIndex];
        if(this.isSizeChangeFlag) {
          this.isSizeChangeFlag = false;
        }else {
          if(currentValue.cropApath) {
            // 如果二次修改，还需要二次保存
            this.needTempSave = true;
          }
        }

        this.$set(this.cropSizeListValue, this.cropSizeIndex, {
          ...currentValue,
          value: {
            ...currentValue.value,
            image: res,
            ...cutInfo,
          }
        })
        // console.log('cutInfo:', this.cropSizeListValue, this.cropSizeIndex, cutInfo)
        // this.$store.commit('PictureSelector/setState', {  res });
      });
    },
    /**
     * 初始化默认图片裁剪参数
     * @param {Image} image
     */
   initImageDefaultCropParam(image) {
      const { cropSizeList } = this.cutOption;
        const cutOffsetList = cropSizeList.map(item => {
          const res = {}
          if (image.naturalWidth / image.naturalHeight > item.width / item.height) {
            res.width = image.naturalHeight / item.height * item.width;
            res.height = image.naturalHeight;
            res.top = 0;
            res.left = (image.naturalWidth - res.width) / 2;
          } else {
            res.height = image.naturalWidth / item.width * item.height;
            res.width = image.naturalWidth;
            res.top = (image.naturalHeight - res.height) / 2;
            res.left = 0;
          }
          Object.keys(res).forEach(key => {
            if (res[key]) {
              res[key] = Math.floor(res[key])
            }
          })
          return res
        })
        this.cropSizeListValue.map((item, i) => {
          // 当没有设置过时设置一下裁剪位置和尺寸
         if(item.value.height === undefined
         && item.value.width === undefined
         && item.value.top === undefined
         && item.value.left === undefined ) {
          this.$set(item, 'value', {
            ...item.value,
            ...cutOffsetList[i],
          })
         }
        })
    },
    getCPathUrl(cutOffsetList) {
      const item = cutOffsetList[0] || {}
      return http.getCPathUrl({
        originalImageAPath: item.originalPath ||this.selected.photoPath || this.selected.path,
        cutOffsetList,
      })
    },
    save() {
      if(this.needTempSave) {
        FxUI.Message.error($t('marketing.components.PictureSelector.xxbcddscdt_b062bd'));
        return new Promise((resolve) => {
          resolve()
        })
      }
      if (this.cropSizeListValue.length > 1) {
        let needCutPic = [];
        this.cropSizeListValue.map((item) => {
          if(!item.cropApath) {
            item.tempIndex = needCutPic.length;
            needCutPic.push(JSON.parse(JSON.stringify(item)))
          }
        });
        return new Promise(async (resolve) => {
          if(needCutPic.length) {
            const cutOffsetList = needCutPic.map(item => {
            try {
              delete item.value.image;
            } catch (error) {}
            return {
                ...item.value,
                photoTargetType: item.photoTargetType,
              }
            });
            const { errCode, data } = await this.getCPathUrl(cutOffsetList);
            if (errCode === 0) {
              let newCutOffsetList = this.cropSizeListValue.map((item) => {
                if(item.cropApath) {
                  return {
                    ...item.value,
                    photoTargetType: item.photoTargetType,
                    path: item.cropApath
                  }
                }else {
                  return {
                    ...item.value,
                    photoTargetType: item.photoTargetType,
                    image: data.urlList[item.tempIndex]
                  }
                }
              });
              // 物料创建的时候originalImageAPath都取第一张图的path
              const photoPath = newCutOffsetList[0].originalPath || newCutOffsetList[0].path || this.selected.photoPath || this.selected.path;
              resolve({
                ...this.selected,
                cropApath: photoPath,
                originalImageAPath: photoPath,
                cutOffsetList: newCutOffsetList,
                photoPath: photoPath,
                path: photoPath,
              });
            }
          }else {
            // 当三张都是被替换时
            let newCutOffsetList = this.cropSizeListValue.map((item) => {
              return {
                    ...item.value,
                    photoTargetType: item.photoTargetType,
                    path: item.cropApath
                  }
            });
            const photoPath = newCutOffsetList[0].originalPath || newCutOffsetList[0].path || this.selected.photoPath || this.selected.path;
            resolve({
              ...this.selected,
              cropApath: photoPath,
              originalImageAPath: photoPath,
              cutOffsetList: newCutOffsetList,
              photoPath: photoPath,
              path: photoPath,
            });
          }
        })
      } else {
        this.MyCropper.save(null, {
          ...this.paramsOfUploadInterface.onSave
        });
        return new Promise(resolve => {
          this.MyCropper.onSuccess(res => {
            resolve({
              ...(res.data || res),
              image: this.cropSizeListValue[this.cropSizeIndex].image
            });
          });
        });
      }

    },
  },
  beforeDestroy() {
    if (this.MyCropper) {
      this.MyCropper.destroy();
    }
  }
};
</script>

<style lang="less" scoped>
.BodyCutPicture {
  width: 100%;
  height: 100%;
  display: flex;
}

.BodyCutPicture__preview {
  width: 208px;
  height: 100%;
  overflow: hidden;
  flex-shrink: 0;
  background: #fff;
  border-right: 1px solid @border-color-base;

  .preview__title {
    color: @color-title;
    font-size: 14px;
    margin: 16px 0 13px 10px;
  }

  .preview__item {
    padding: 10px;

    &-active {
      background-color: #EEF2FB;
    }

    .item__title {
      display: flex;
      margin-bottom: 10px;
      font-size: 12px;
      color: #181C25;
    }

    .preview__img {
      width: 180px;
      height: 180px;
      overflow: hidden;
    }

    .preview__desc {
      margin-top: 4px;
      color: #545861;
      font-size: 12px;
      line-height: 18px;

      .text {
        display: flex;
        position: relative;
      }
    }
  }

  .block__line {
    height: 0;
    margin: 12px 10px;
    border-bottom: 1px dashed #dee1e8;
  }
}

.BodyCutPicture__cutter {
  flex: 1;

  .cutter__tabs {
    height: 50px;
    display: flex;
    align-items: center;
    font-size: 14px;
    color: @color-title;
    padding-left: 15px;
    position: relative;

    /deep/.el-radio-button {
      margin-right: 10px;
      .el-radio-button__inner {
        color: #181C25;
        height: 32px;
        border-radius: 4px;
        border: none;
        background: #F2F3F5;
        padding: 0 20px;
        line-height: 32px;
        font-weight: 500;
        box-shadow: none;
      }

      &.is-active {
        .el-radio-button__inner {
          color: var(--color-primary06,#ff8000);
          background: var(--color-primary01, #FFF7E6);
        }
      }
    }
    .cutter__tabs-group {
      position: absolute;
      top: -41px;
      left: 70px;
      white-space: nowrap;
      &.temp-save-cover {
        width: 540px;
        height: 33px;
        z-index: 2;
      }
    }
    .tabs-pic-tips {
      display: flex;
      width: 100%;
      align-items: center;
      padding-right: 20px;
      .tabs-pic-tips-content {
        flex: 1;
        color: #91959E;
        font-size: 14px;
        &.is-active {
          color: #181C25;
        }
      }
    }
  }

  .cutter__cropper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 514px;
    background: #eee;

    /deep/.fs-h5avatar-box {
      overflow: unset;

      .cutbox {
        overflow: unset;
      }
    }

    .cropper__img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
}

</style>

<style lang="less">
.my_picture-selector-dialog {
  .el-dialog__header, .el-dialog__footer {
    border: none;
  }
}
</style>
