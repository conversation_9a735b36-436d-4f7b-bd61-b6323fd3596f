/*
 * @Author: <EMAIL>
 * @Date: 2021-05-07 19:36:13
 * @Description:
 */

import util from "@/services/util/index";
export const moduleName = 'PictureSelector';


const state = {
  // 最大可选图片数（大于1时不可裁剪）
  maxSelectCount: 1,
  // 裁剪配置
  cutOption: {
    cutable: false,
    cutSize: { },
  },
  // 是否维持裁剪比例
  keepCutSizeRatio: true,
  // 最终输出是否需要Apath，true时，图片上传接口会做出相应变更
  needApath: false,
  // 当前步骤
  curStepId: 1,
  // 后端获取的分组列表
  groups: [],
  // 前端展示的分组列表
  displayGroups: [],
  // 当前分组
  groupId: '',
  // 图片列表
  pictureListData: [],
  // 图片列表分页信息
  pageData: {
    layout: "prev, pager, next, total, sizes",
    pageNum: 1,
    totalCount: 0,
    pageSize: 10,
    pageSizes: [10, 20, 30, 40],
    time: 0,
  },
  // 加载中
  loading_table: false,
  // 图片上传中
  uploading: false,
  // 选中
  selecteds: [],
  doUploadFlag: false, // 用于触发单文件上传事件
  tempSelected: null, // 用于保存临时上传的图片
  currentCropIndex: 0, // 用于保存临时上传的图片的索引
}

const store = {
  namespaced: true,
  state: {
    ...JSON.parse(JSON.stringify(state)),
  },
  mutations: {
    setState: Object.assign,
    setDoUploadFlag(state, doUploadFlag) {
      state.doUploadFlag = doUploadFlag
    },
    setTempSelected(state, tempSelected) {
      state.tempSelected = tempSelected
    },
  },
  actions: {
    clear({ commit }, payload) {
      commit("setState", {
        ...JSON.parse(JSON.stringify(state)),
      })
    },
    setGroupId({ commit }, payload) {
      commit("setState", {
        groupId: payload,
      })
    },
    setSelected({ commit, state }, payload) {
      let selecteds = JSON.parse(JSON.stringify(state.selecteds));

      if (state.maxSelectCount === 1) {
        // 单选
        selecteds = [payload];
      } else {
        // 多选
        const index = selecteds.findIndex(item => item.id === payload.id);
        if (index > -1) {
          console.log('index', index);
          selecteds.splice(index, 1);
        } else {
          if (selecteds.length >= state.maxSelectCount && state.maxSelectCount !== 0) {
            FxUI.Message.error($t('marketing.commons.zdxzztp_602ada', {data: ( {'option0': selecteds.length})}))
            return;
          }
          selecteds.push(payload);
        }
      }

      commit("setState", {
        selecteds,
      });
    },
    clearSelecteds({ commit, state }, payload) {
      state.selecteds = [];
    },
    async listPhotoByGroup({ dispatch, commit, state }) {
      if (!state.groupId) throw 'groupId is null';
      state.loading_table = true;
      const { pageNum, pageSize, time } = state.pageData;
      const params = {
        groupId: state.groupId,
        source: 2,
        keyword: null,
        pageNum,
        pageSize,
        time,
       }
      const res = await YXT_ALIAS.http.listPhotoByGroup(params); // source 1-图片库 2-选择器
      state.loading_table = false;
      if (!res || res.errCode !== 0) return;
      state.pageData.totalCount = res.data.totalCount;
      state.pageData.time = res.data.time || 0;
      state.pictureListData = res.data.result.map(item => ({
        ...item,
        size: util.getDisplaySizeByByte(item.photoSize, 2),
        time: util.formatDateTime(item.createTime, 'YYYY-MM-DD hh:mm'),
      }));
    },
    async listPhotoByPoster({ dispatch, commit, state }) {
      state.loading_table = true;
      const { pageNum, pageSize, time } = state.pageData;
      const params = {
        type: 1,
        pageNum,
        pageSize,
        time,
       }
      const res = await YXT_ALIAS.http.queryListByEa(params);
      state.loading_table = false;
      if (!res || res.errCode !== 0) return;
      state.pageData.totalCount = res.data.totalCount;
      state.pageData.time = res.data.time || 0;
      state.pictureListData = res.data.result.map(item => getPhotoItemByPosterItem(item));
    },
    async getPosterCount({ dispatch, commit, state }) {
      const params = {
        type: 1,
        pageNum: 1,
        pageSize: 0,
      };
      const res = await YXT_ALIAS.http.queryListByEa(params);
      if (!res || res.errCode !== 0) return;
      // 回填企业海报的数目
      dispatch('setDisplayGroups', { posterCount: res.data.totalCount });
    },
    handlePageChange({ dispatch, commit, state }, data) {
      state.pageData.pageNum = data?.pageNum || state.pageData.pageNum;
      state.pageData.pageSize = data?.pageSize || state.pageData.pageSize;
      if (state.groupId === 'poster') {
        dispatch('listPhotoByPoster');
      } else {
        dispatch('listPhotoByGroup');
      }
    },
    async queryGroup({ dispatch, commit, state }) {
      const res = await YXT_ALIAS.http.listObjectGroup({ time: 0, useType: 0, source: 2, objectType: 7 }); // source 1-图片库 2-选择器
      if (!res || res.errCode !== 0) return;
      const { data } = res;
      const groups = data.objectGroupList.reduce((a, c) => { a.push(c); return a; }, []);
      // state.groupId = state.groupId || (data.objectGroupList || [])[0].groupId || "";
      state.groups = groups || [];
      dispatch('setDisplayGroups');
      dispatch('getPosterCount');
    },
    setDisplayGroups({ dispatch, commit, state }, payload = { posterCount: '*' }) {
      const originGroups = state.groups;
      const groupMy = originGroups.find(item => item.groupId === '-2');
      const groupPoster = { groupName: $t('marketing.commons.qyhb_a11919'), groupId: 'poster', objectCount: payload.posterCount };
      const groupLib = {
        groupName: $t('marketing.commons.tpk_db7625'),
        groupId: 'lib',
        groups: originGroups.reduce((a, c) => { c.groupId !== '-2' && a.push(c); return a; }, []),
      };
      groupLib.objectCount = groupLib.groups.reduce((a, c) => { a += c.objectCount; return a; }, 0);
      const displayGroups = [
        groupMy,
        groupPoster,
        groupLib,
      ];
      console.log(displayGroups);
      state.displayGroups = displayGroups;
      state.groupId = '-2';
    },

  }
};
function getPhotoItemByPosterItem(posterItem) {
  return ({
    ...posterItem,
    id: posterItem.qrPosterId,
    ext: posterItem.qrPosterApath.split('.')[1],
    groupName: $t('marketing.commons.tghb_c91bd6'),
    photoForwardUrl: null,
    photoName: posterItem.title,
    photoPath: posterItem.qrPosterApath,
    photoSize: posterItem.photoSize || null,
    thumbnailUrl: posterItem.qrPosterThumbnailUrl,
    url: posterItem.qrPosterUrl,
    size: null,
    time: util.formatDateTime(posterItem.createTime, 'YYYY-MM-DD hh:mm'),
  });
}

export default {
  beforeCreate() {
    if (!this.$store.state[moduleName]) {
      this.$store.registerModule(moduleName, store);
    }
  },
  beforeDestroy() {
    // 这里由于新 Vue 模块的 beforeCreate 会比 老 Vue 模块的 beforeDestroy 更快执行，
    // 假如两个模块引用这个 store mixin 时，就会导致新 Vue 模块的 this.$store.PictureSelector 被销毁，从而导致页面异常
    // 因此需要注释掉
    // if (!module.hot) {
    //   this.storeUnregister();
    // }
  },
  methods: {
    storeUnregister() {
      this.$store.unregisterModule(moduleName);
    },
  },

};
