<template>
  <div :class="$style.scaffold_wrapper">
    <div :class="$style.header">
      <div :class="$style.title">{{title}}</div>
      <div>
        <fx-button
          type="primary"
          v-show="btnState[0] !== -1"
          :loading="btnState[0] === 1"
          :disabled="btnState[0] === 2"
          @click="handleSend"
        >{{  btnNames && btnNames[0] || $t('marketing.commons.fs_1535fc')}}</fx-button>

        <fx-button
          v-show="btnState[1] !== -1"
          :loading="btnState[1] === 1"
          :disabled="btnState[1] === 2"
          @click="handleCancel"
        >{{  btnNames && btnNames[1] || $t('marketing.commons.qx_625fb2')}}</fx-button>
      </div>
    </div>
    <div :class="$style.body">
      <slot></slot>
    </div>
  </div>
</template>

<script>
/*
 * @name       全屏布局框架
 * @param      title    - String   标题
 *             btnState - Array    按钮状态(-1隐藏，0正常，1加载中，2禁用)
 * @events     send     - none     点击发送按钮
 *             save     - none     点击保存按钮
 *             cancel   - none     点击取消按钮
 */


import { confirm } from '@/utils/globals';

export default {
  props: {
    title: {
      type: String,
      default: $t('marketing.commons.bt_32c65d'),
    },
    btnNames: {
      type: Array,
      defaule: [],
    },
    btnState: {
      type: Array,
      default: () => [],
    },
    needCancel: {
      type: Boolean,
      default: () => true,
    },
    needCancelConfirm: {
      type: Boolean,
      default: () => true,
    },
  },
  methods: {
    handleSend() {
      
      this.$emit('send');
    },
    handleCancel() {
      if (this.needCancelConfirm) {
        confirm($t('marketing.commons.sfqrqx_c2523b'), $t('marketing.commons.ts_02d981'), {
          callback: (action) => {
            if (action === 'confirm') this.$emit('cancel');
          },
        });
      } else {
        this.$emit('cancel');
      }
    },
  },
  components: {
ElButton: FxUI.Button
},
};
</script>

<style lang="less" module>
.scaffold_wrapper {
  display: flex;
  flex-flow: column nowrap;
  height: 100%;
  .header {
    flex: 0 0 auto;
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    justify-content: space-between;
    padding: 0 22px;
    height: 72px;
    background: #f4f6f9;
    position: sticky;
    top: 0;
    z-index: 100;
  }
  .title {
    font-size: 28px;
    color: #212b36;
  }
  .body {
    flex: auto;
  }
}
</style>
