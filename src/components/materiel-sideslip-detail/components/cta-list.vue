<template>
  <div class="objectdetail__cta-list">
    <VTable class="tablewbg" ref="table" v-loading="listLoading" :columns="columns"
      :emptyText="$t('marketing.commons.zwsj_21efd8')" :data="lists" :settable="true" tid="content_cta_list_table" @click:text="handleCTADetailOpen">
    </VTable>
    <CTADetail v-if="ctaDetailVisible" :id="targetDetailData.id" :item="targetDetailData" :visible="ctaDetailVisible" @close="handleCTADetailClose">
    </CTADetail>
  </div>
</template>

<script>
import VTable from "@/components/table-ex";
import tableTool from "./cta-list-tools";

export default {
  components: {
    VTable,
    // 使用懒加载解决循环引用
    CTADetail: () => import("@/pages/cta/detail"),
  },
  props: {
    objectId: {
      type: String,
      default: ''
    },
    objectType: {
      type: Number,
      default: 0
    },
    ctaLists: {
      type: Array,
      default: () => [],
    }
  },
  data() {
    return {
      columns: tableTool.getColumns(),
      lists: tableTool.getDatas(this.ctaLists),
      listLoading: false,
      ctaDetailVisible: false,
      targetDetailData: {},
    };
  },
  methods: {
    handleCTADetailOpen(prop,row) {
      // console.log(prop,row)
      if(prop === 'ctaName') {
        this.targetDetailData = {
          id: row.ctaId,
          ctaName: row.ctaName.text,
        };
        this.ctaDetailVisible = true;
      }
    },
    handleCTADetailClose() {
      this.ctaDetailVisible = false;
    },
  },
};
</script>

<style lang="less" scoped>
.objectdetail__cta-list {
  .ctalist__table {}
}
</style>
