<template>
  <div :class="$style.order__table" v-if="canShow">
    <div class="body__title">
      <span class="title__before"></span>
      <span class="title__text">{{ $t('marketing.commons.ddlb_07166e') }}</span>
      <div class="title__output" @click="handleOutput">
        {{ $t('marketing.commons.dc_55405e') }}
      </div>
    </div>
    <div class="body__table">
      <v-table
        class="tablewbg"
        tid="marketing-detail-table"
        :settable="true"
        :filter-option="false"
        :data="lists"
        :columns="columns"
        :row-style="{ cursor: 'pointer' }"
        @custom:cule-action="handleDetail"
        @click:row="handleRow"
      ></v-table>
      <div class="table__more" v-if="count > maxTableSize">
        <a href="javascript: void(0);" @click.stop="$emit('loadMore', 'order')"
          >{{ $t('marketing.commons.ckgd_90ef7c') }}</a
        >
      </div>
    </div>
  </div>
</template>

<script>
import VTable from "@/components/table-ex/index";
import http from "@/services/http/index";
import util from "@/services/util/index";
import table from "./table";
export default {
  components: {
    VTable
  },
  props: {
    objectId: {
      type: String,
      default: ""
    },
    marketingEventId: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      canShow: false,
      maxTableSize: 5,
      columns: [],
      lists: [],
      count: 0
    };
  },
  watch: {
    objectId() {
      this.queryOrderLists();
    }
  },
  created() {
    this.queryOrderLists();
  },
  methods: {
    getTableData(data, type) {
      const columns = table.getColumns(data);
      const lists = table.getDatas(data);
      const count = data.totalCount;
      return {
        columns,
        lists,
        count
      };
    },
    queryOrderLists(targetType) {
      if (!this.objectId) return;
      let params = {
        type: 3,
        objectId: this.objectId,
        objectType: 16,
        formUsage: 2,
        pageNum: 1,
        pageSize: this.maxTableSize
      };
      this.marketingEventId &&
        (params.marketingEventId = this.marketingEventId);

      this.marketingEventId && (params.type = 4);

      http.queryFormUserData(params).then(res => {
        if (res === false || res.errCode) {
          return;
        }
        if (res.data.result && res.data.result.length) {
          this.canShow = true;
          Object.assign(this, this.getTableData(res.data));
        } else {
          this.canShow = false;
        }
      });
    },
    handleRow(row, column, event) {
      this.handleDetail(row);
    },
    handleDetail(row) {
      if (row.saveCrmStatus === 0 || row.saveCrmStatus === 3) {
        if(!row.otherCrmObjectBind) return;
        CRM.api.show_detail({
          apiname: row.otherCrmObjectBind.apiName,
          id: row.otherCrmObjectBind.objectId
        });
      }
    },
    handleOutput() {
      this.isExporting = true;
      const params = {
        type: 3,
        objectId: this.objectId,
        objectType: 16,
        formUsage: 2
      };
      this.marketingEventId &&
        (params.marketingEventId = this.marketingEventId);
      this.marketingEventId && (params.type = 4);
      const opts = {
        action: "exportEnrollsData",
        params
      };
      util.exportoFile(opts, () => {
        this.isExporting = false;
      });
    }
  }
};
</script>

<style lang="less" module>
.order__table {
  margin-bottom: 20px;
  :global {
    .body__title {
      display: flex;
      align-items: center;
      line-height: 16px;
      margin-bottom: 16px;
      .title__before {
        width: 4px;
        height: 12px;
        background: var(--color-primary06,#ff8000);
        margin-right: 16px;
        flex-shrink: 0;
      }
      .title__text {
        font-size: 12px;
        color: #181c25;
        flex: 1;
      }
      .title__output {
        color: var(--color-primary06,#407FFF);
        font-size: 12px;
        margin-right: 20px;
        cursor: pointer;
      }
    }
    .body__table {
      .tablewbg {
        border-bottom: 0 !important;
      }
      .table__more {
        height: 40px;
        border-top: 1px solid #e9edf5 !important;
        border-left: 1px solid #e9edf5 !important;
        display: flex;
        justify-content: center;
        align-items: center;
        a {
          font-size: 12px;
          text-decoration: none;
        }
      }
    }
  }
}
</style>
