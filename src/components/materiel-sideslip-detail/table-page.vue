<template>
  <div class="table-page__wrapper">
    <div class="wrapper__header">
      <div class="header__left">
        <div class="button--back">
          <a @click.stop="handleBack">{{ `< ${$t('marketing.commons.fh_5f4112')}` }}</a>
        </div>
      </div>
      <div class="header__right">
        <div
          class="right__name"
          :title="name"
        >
          {{ name }}
        </div>
      </div>
    </div>
    <div class="wrapper__body">
      <div class="body__title">
        <div class="title__before" />
        <div class="title__text">
          {{ title }}
        </div>
        <div
          v-if="showType !='lookUpDetail' && !isDeepTable"
          class="title__output"
          @click="handleOutputByShowType"
        >
          {{ $t('marketing.commons.dc_55405e') }}
        </div>
      </div>
      <div class="body__table">
        <fx-button
          v-if="showType !='lookUpDetail' && showType!='lookup' && !isDeepTable"
          type="medium"
          size="mini"
          style="margin-bottom:20px;"
          :disabled="!selectedLeadNumber > 0"
          :loading="saveLoading"
          @click="saveToClue"
        >
          {{ $t('marketing.commons.zxcr_d56b3f') }}
        </fx-button>
        <fx-button
          v-if="showType !='lookUpDetail' && showType!='lookup' && !isDeepTable && objectType === 26"
          type="medium"
          size="mini"
          style="margin-bottom:20px;"
          :disabled="!selectedLeadNumber > 0"
          :loading="deleteLoading"
          @click="handleDeleteClues"
        >
          {{ $t('marketing.commons.sc_2f4aad') }}
        </fx-button>
        <v-table
          ref="clueTable"
          class="tablewbg"
          tid="marketing-detail-table"
          :settable="true"
          :filter-option="false"
          :data="tableDatas"
          :columns="tableColumns"
          :rowKey="getRowKeys"
          :row-style="{ cursor: 'pointer' }"
          @custom:cule-action="handleClueDetail"
          @click:row="handleClueRow"
          @refreshTable="refreshTable"
          @selection-change="selectionChange"
          @custom:count-action="handleLookupDetail"
        />
        <div class="table__footer">
          <pagination
            background
            :page-size="pageSize"
            :current-page="pageNum"
            :total="pageTotalCount"
            :page-sizes="[5, 10, 20, 30, 40]"
            layout="prev, pager, next, total, sizes, jumper"
            @size-change="handlePageSize"
            @current-change="handlePageNum"
          />
        </div>
      </div>
      <loading-mask :show="flag_showLoadingMask" />
    </div>
  </div>
</template>

<script>
import VTable from '@/components/table-ex/index.vue'
import LoadingMask from '@/components/loading-mask/index.vue'
import { confirm } from '@/utils/globals.js'
import CONST from './tools/product-tools.js'
import ORDER_TABLE from './components/order-table/table.js'
import http from '@/services/http/index.js'
import util from '@/services/util/index.js'
import LookupTableTools from './components/table-tools.js'

export default {
  components: {
Pagination: FxUI.Pagination,
VTable,
LoadingMask
},
  props: {
    objectType: {
      type: Number,
      default: () => 4, // 物料类型枚举值: 4.产品 6.文章 13.活动
    },
    showType: {
      type: String,
      default: '',
    },
    marketingEventId: {
      type: String,
      default: () => '', // 市场活动Id
    },
    objectOriginData: {
      type: Object,
      default: () => {},
    },
    tableOriginData: {
      type: Array,
      default: () => [],
    },
    formSourceType: {
      type: Number,
      default: () => 0, // 表单来源
    },
    objectId: {
      type: String,
      default: '',
    },
    userMarketingId: {
      type: String,
      default: '',
    },
    websiteId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      selectedLeadNumber: 0,
      selectedLeads: [],
      name: '',
      tableColumns: [],

      pageSize: 10,
      pageNum: 1,
      pageTotalCount: 0,

      flag_showLoadingMask: true,
      tableDatas: [],
      saveLoading: false,
      isDeepTable: false,
      deleteLoading: false,
      getRowKeys: function(row) {
        return row.id;
      },
    }
  },
  computed: {
    title() {
      if (this.showType === 'lookup' || this.showType === 'lookUpDetail') {
        return $t('marketing.commons.fwmx_f9a0fa')
      }
      return {
        4: $t('marketing.components.materiel_sideslip_detail.cpsyxs_b2984a'),
        6: $t('marketing.components.materiel_sideslip_detail.wzxs_275d96'),
        13: $t('marketing.commons.hdbmyh_2f15de'),
        26: $t('marketing.commons.tjsjmx_845319'),
      }[
        this.objectType
      ]
    },
  },
  watch: {
    objectOriginData(newVal) {
      this.loadOriginDataByShowtype()
    },
  },
  mounted() {
    this.name = this.objectOriginData.name || this.objectOriginData.title
    this.loadOriginDataByShowtype()
  },
  methods: {
    loadOriginDataByShowtype() {
      if (this.showType === 'lookup') {
        this.loadLookUpDetailData()
      } else if (this.showType === 'lookUpDetail') {
        this.loadLookupDataByUserMarketingId()
      } else {
        this.loadTableData()
      }
    },
    saveToClue() {
      confirm(
        $t('marketing.commons.jhdsxcrsbd_fedc65'),
        $t('marketing.commons.zxcrxs_f70585'),
        {},
      ).then(
        () => {
          const ids = this.getIDs()
          this.saveLoading = true
          http.reImportDataToCrm({ ids }).then(results => {
            if (results && results.errCode === 0) {
              this.saveLoading = false
              FxUI.MessageBox.alert($t('marketing.commons.zxcrknxyjz_9b0350'), $t('marketing.commons.ts_02d981'), {
                confirmButtonText: $t('marketing.commons.zdl_ce2695'),
              })
              this.clearTableSelection()
            }
          })
        },
        () => {},
      )
    },
    clearTableSelection(){
      this.selectedLeadNumber = 0
      this.selectedLeads = []
      this.$refs.clueTable.clearSelection()
    },
    handleDeleteClues(){
      const ids = this.getIDs()
      FxUI.MessageBox.confirm($t('marketing.commons.qrscdqxztj_5bc3c3'), $t('marketing.commons.ts_02d981'), {
          confirmButtonText: $t('marketing.commons.qd_38cf16'),
          cancelButtonText: $t('marketing.commons.qx_625fb2'),
          type: 'warning',
        }).then(async () => {
          this.deleteLoading = true
          const res = await http.deleteFormEnrollData({enrollUserIds: ids})
          if(res && res.errCode === 0){
            this.clearTableSelection()
            FxUI.Message.success($t('marketing.commons.sccg_0007d1'))
            this.loadTableData()
          } else {
            FxUI.Message.success($t('marketing.pages.setting.scsbqzs_89976e'))
          }
          this.deleteLoading = false
        }).catch((err)=>{
          console.log('err: ', err);
        })
    },
    getIDs() {
      const { selectedLeads } = this
      const ids = []
      selectedLeads.forEach(item => {
        ids.push(item.id)
      })
      return ids
    },
    selectionChange(val) {
      console.log('val: ', val)
      this.selectedLeadNumber = (val && val.length) || 0
      this.selectedLeads = val
    },
    refreshTable() {
      this.loadOriginDataByShowtype()
    },
    handleBack() {
      if (this.isDeepTable) {
        this.isDeepTable = false
        this.pageNum = 1
        this.loadLookUpDetailData()
        return
      }
      this.$emit('onBack', this.showType)
    },
    loadTableData() {
      if (!this.objectOriginData.id) return
      if (this.objectType === 26 && !this.objectOriginData.formId) return // 26.微页面的特殊逻辑
      const type = this.getType()
      const params = {
        type, // 1.物料 2.营销活动 3.表单 4.市场活动
        objectId:
          this.objectType === 26
            ? this.objectOriginData.formId
            : this.objectOriginData.id, // 物料Id 26.微页面（特殊逻辑：此处改传formId）
        objectType: this.objectType === 26 ? 16 : this.objectType, // 物料类型枚举值: 4.产品 6.文章 13.活动 16.表单 26.微页面（特殊逻辑：此处改传16）
        pageNum: this.pageNum,
        pageSize: this.pageSize,
      }

      if (this.formSourceType) {
        params.sourceType = this.formSourceType
      }

      if (this.marketingEventId) {
        params.marketingEventId = this.marketingEventId
      }

      if (this.websiteId) {
        params.websiteId = this.websiteId
      }

      if (this.showType === 'order') {
        params.formUsage = 2
      }
      http.queryFormUserData(params).then(res => {
        this.flag_showLoadingMask = false
        if (res === false || res.errCode) {
          FxUI.Message.error($t('marketing.commons.cpxxhqsbqz_d1c3c0'))
          return
        }
        this.pageTotalCount = res.data.totalCount
        this.getTableData(res.data)
      })
    },
    loadLookUpDetailData(param) {
      this.flag_showLoadingMask = true
      this.tableColumns = LookupTableTools.getColumns()
      const params = {
        objectId: this.objectId,
        objectType: this.objectType,
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        ...param,
      }
      LookupTableTools.getDatas(params).then(res => {
        this.flag_showLoadingMask = false
        if (res === false || res.errCode) {
          FxUI.Message.error($t('marketing.commons.hqsjsb_e05c1c'))
          return
        }
        this.pageTotalCount = res.totalCount
        this.tableDatas = res.datas
      })
    },
    loadLookupDataByUserMarketingId(param) {
      this.tableColumns = LookupTableTools.getLookupDetailColumns()
      const params = {
        userMarketingId: this.userMarketingId,
        objectId: this.objectId,
        objectType: this.objectType,
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        ...param,
      }
      LookupTableTools.getLookupDetailDatas(params).then(res => {
        this.flag_showLoadingMask = false
        if (res === false || res.errCode) {
          FxUI.Message.error($t('marketing.commons.hqsjsb_e05c1c'))
          return
        }
        this.pageTotalCount = res.totalCount
        this.tableDatas = res.datas
      })
    },
    handleLookupDetail(row) {
      this.flag_showLoadingMask = true
      this.isDeepTable = true
      this.pageNum = 1
      this.loadLookupDataByUserMarketingId({ userMarketingId: row.userMarketingId })
    },
    getType() {
      // 1.物料 2.营销活动 3.表单 4.市场活动 6报名来源
      let type
      if (this.marketingEventId) {
        type = 4
      } else if (this.formSourceType) {
        // formSourceType 按报名来源查询,必须放在/16|26/之前
        type = 6
      } else if (/16|26/.test(this.objectType)) {
        type = 3
      } else {
        type = 1
      }
      return type
    },
    getTableData(data) {
      // 订单列表
      if (this.showType === 'order') {
        this.tableColumns = ORDER_TABLE.getColumns(data)
        this.tableDatas = ORDER_TABLE.getDatas(data)
      } else {
        this.tableColumns = CONST.getColumns(data, false, this.formSourceType)
        this.tableDatas = CONST.getDatas(
          data,
          false,
          this.formSourceType,
          this,
        )
      }
    },
    handleOutput() {
      this.isExporting = true
      let params = {}
      if (this.formSourceType) {
        // 如果按照报名来源
        params = {
          type: 6,
          objectId: this.objectOriginData.id,
          sourceType: this.formSourceType,
          objectType: this.objectType,
        }
      } else if (this.objectType === 16) {
        params = {
          type: 3,
          objectId: this.objectOriginData.id,
        }
      } else if (this.objectType === 26) {
        params = {
          type: 3,
          objectId: this.objectOriginData.formId,
        }
      } else {
        params = {
          objectId: this.objectOriginData.id,
          objectType: this.objectType,
          type: 1,
        }
      }

      if (this.marketingEventId) {
        params.marketingEventId = this.marketingEventId
        params.type = 4
      }

      if (this.showType === 'order') {
        params.formUsage = 2
      }
      const opts = {
        action: 'exportEnrollsData',
        params,
      }
      util.exportoFile(opts, () => {
        this.isExporting = false
      })
    },
    handleLookupOutput() {
      this.isExporting = true
      const params = LookupTableTools.getDatasExportParams({ objectId: this.objectId, objectType: this.objectType })
      const opts = {
        action: 'exportUserMarketingLookUpStatisticByObject',
        params,
      }
      util.exportoFile(opts, () => {
        this.isExporting = false
      })
    },
    handleOutputByShowType() {
      if (this.showType === 'lookup') {
        this.handleLookupOutput()
      } else {
        this.handleOutput()
      }
    },
    handlePageSize(val) {
      this.pageSize = val
      this.handlePageNum(1)
    },
    handlePageNum(val) {
      this.pageNum = val
      this.loadOriginDataByShowtype()
    },
    handleClueDetail(row) {
      if (row.saveCrmStatus === 0 || row.saveCrmStatus === 3) {
        if(!row.otherCrmObjectBind) return;
        CRM.api.show_detail({
          apiname: row.otherCrmObjectBind.apiName,
          id: row.otherCrmObjectBind.objectId,
        })
      }
    },
    handleClueRow(row, column, event) {
      this.handleClueDetail(row)
    },
  },
}
</script>

<style lang="less">
.table-page__wrapper {
  display: flex;
  flex-direction: column;
  .wrapper__header {
    width: 100%;
    min-height: 80px;
    background: #f6f9fc;
    padding: 16px 57px 16px 20px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    flex-shrink: 0;
    .header__left {
      flex-shrink: 0;
      padding-right: 16px;
      border-right: 1px solid #e9edf5;
      a {
        text-decoration: none;
        cursor: pointer;
      }
    }
    .header__right {
      padding-left: 16px;
      .right__name {
        font-size: 14px;
        color: #181c25;
        width: 520px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
  .wrapper__body {
    position: relative;
    padding-top: 20px;
    overflow: auto;
    flex: 1;
    .body__title {
      display: flex;
      align-items: center;
      line-height: 16px;
      margin-bottom: 16px;
      .title__before {
        width: 4px;
        height: 12px;
        background: var(--color-primary06,#ff8000);
        margin-right: 16px;
        flex-shrink: 0;
      }
      .title__text {
        font-size: 12px;
        color: #181c25;
        flex: 1;
      }
      .title__output {
        color: var(--color-primary06,#407FFF);
        font-size: 12px;
        margin-right: 20px;
        cursor: pointer;
      }
    }

    .body__table {
      // width: 647px;
      overflow-x: auto;
      overflow-y: hidden;
      box-sizing: border-box;
      .tablewbg {
        width: auto;
        border-top: 1px solid #e9edf5 !important;
        border-left: 1px solid #e9edf5 !important;
        border-right: 1px solid #e9edf5 !important;
        // border-width: 1px 1px 0;
        box-sizing: border-box;
        overflow: auto;
        margin: 0;
        .el-table__header {
          tr {
            th {
              background: #fff;
            }
          }
        }
        .el-table__empty-block {
          min-height: 200px;
        }
        &.bottomborder {
          border-width: 1px;
        }
      }
      .table__more {
        height: 40px;
        border: 1px solid #e9edf5;
        border-top-width: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 20px;
        a {
          font-size: 12px;
          text-decoration: none;
        }
      }
      .table__footer {
        margin-top: 10px;
        height: 35px;
      }
    }
  }
}
</style>
