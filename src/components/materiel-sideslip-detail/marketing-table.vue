<template>
  <div class="table-page__wrapper">
    <div class="wrapper__header">
      <div class="header__left">
        <div class="button--back">
          <a @click.stop="handleBack">{{back}}</a>
        </div>
      </div>
    </div>
    <div class="wrapper__body">
      <div class="body__title">
        <div class="title__before"></div>
        <div class="title__text">{{ marktingTitle || "--" }}</div>
        <div class="title__output" @click="handleOutput">{{ $t('marketing.commons.dc_55405e') }}</div>
      </div>
      <div class="body__table">
        <fx-button
          v-if="objectType == 167"
          type="medium"
          size="mini"
          style="margin-bottom:20px;margin-left:20px"
          :disabled="!selectedLeadNumber > 0"
          @click="saveToClue"
          >{{ $t('marketing.commons.zxcrxs_f70585') }}</fx-button
        >
        <v-table
          class="tablewbg"
          tid="marketing-detail-table"
          :settable="true"
          :filter-option="false"
          :data="tableDatas"
          :columns="tableColumns"
          :row-style="{ cursor: 'pointer' }"
          @custom:cule-action="handleClueDetail"
          @click:row="handleClueRow"
          @selection-change="slectionChange"
          @refreshTable="refreshTable"
        ></v-table>
        <div class="table__footer">
          <pagination
            background
            @size-change="handlePageSize"
            @current-change="handlePageNum"
            :page-size="pageSize"
            :current-page="pageNum"
            :total="pageTotalCount"
            :page-sizes="[5, 10, 20, 30, 40]"
            layout="prev, pager, next, total, sizes, jumper"
          ></pagination>
        </div>
      </div>
      <loading-mask :show="flag_showLoadingMask"></loading-mask>
    </div>
    <el-dialog
      :title="$t('marketing.commons.zxcrxs_f70585')"
      :visible.sync="toClueVisible"
      width="547px"
      class="review-status-dialog"
      append-to-body
    >
      <span class="text">
        {{ $t('marketing.commons.jhdsxcrsbd_fedc65') }}
      </span>
      <div slot="footer" class="dialog-footer">
        <fx-button  size="small" type="primary" @click="toClueConfirm">{{ $t('marketing.commons.qd_aa7527') }}</fx-button>
        <fx-button size="small" @click="toClueVisible = false">{{ $t('marketing.commons.qx_c08ab9') }}</fx-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import VTable from "@/components/table-ex/index";
import LoadingMask from "@/components/loading-mask/index";

import marketingTools from "./tools/marketing-clue-tools";
import websiteTools from "./tools/website-clue-tools";
import http from "@/services/http/index";
import util from "@/services/util/index";


export default {
  components: {
Pagination: FxUI.Pagination,
VTable,
LoadingMask,
elDialog: FxUI.Dialog
},
  props: {
    id: {
      type: String,
      default: () => ""
    },
    objectType: {
      type: Number,
      default: () => 0
    },
    marktingTitle: {
      type: String,
      default: () => "" // 活动名称
    }
  },
  data() {
    return {
      name: "",
      tableColumns: [],

      pageSize: 10,
      pageNum: 1,
      pageTotalCount: 0,

      flag_showLoadingMask: true,
      tableDatas: [],
      ToolsMap: {
        166: marketingTools,
        167: websiteTools,
        168: websiteTools
      },
      sourceTypeMap: {
        166: 0,
        167: 2,
        168: 3
      },
      toClueVisible: false,
      selectedLeadNumber: 0,
      selectedLeads: 0,
      back: `< ${$t('marketing.commons.fh_5f4112')}`
    };
  },
  watch: {},
  computed: {},
  mounted() {
    this.loadTableData();
  },
  methods: {
    refreshTable() {
      this.loadTableData();
    },
    getIDs() {
      let selectedLeads = this.selectedLeads;
      let ids = [];
      selectedLeads.forEach(item => {
        ids.push(item.id);
      });
      return ids;
    },
    toClueConfirm() {
      let ids = this.getIDs();
      http.reImportDataToCrm({ ids: ids }).then(results => {
        if (results && results.errCode == 0) {
          FxUI.MessageBox.alert($t('marketing.commons.zxcrknxyjz_9b0350'), $t('marketing.commons.ts_02d981'), {
            confirmButtonText: $t('marketing.commons.zdl_ce2695'),
          })
        }
      });
      this.toClueVisible = false;
    },
    saveToClue() {
      this.toClueVisible = true;
    },
    slectionChange(val) {
      this.selectedLeadNumber = (val && val.length) || 0;
      this.selectedLeads = val;
    },
    handleBack() {
      this.$emit("onBack");
    },
    loadTableData() {
      let params = {
        sourceId: this.id,
        sourceType: this.sourceTypeMap[this.objectType],
        pageNum: this.pageNum,
        pageSize: this.pageSize
      };
      this.ToolsMap[this.objectType].queryMultipleFormUserData(params).then(
        data => {
          this.pageTotalCount = data.totalCount;
          this.flag_showLoadingMask = false;
          this.getTableData(data);
        },
        () => {
          this.flag_showLoadingMask = false;
        }
      );
    },
    getTableData(data) {
      this.tableColumns = this.ToolsMap[this.objectType].getColumns(data);
      this.tableDatas = this.ToolsMap[this.objectType].getDatas(data);
    },
    handleOutput() {
      this.isExporting = true;
      const params = {
        sourceId: this.id,
        sourceType: this.sourceTypeMap[this.objectType]
      };
      const opts = {
        action: "exportMultipleFormEnrollsData",
        params
      };
      util.exportoFile(opts, () => {
        this.isExporting = false;
      });
    },
    handlePageSize(val) {
      this.pageSize = val;
      this.handlePageNum(1);
    },
    handlePageNum(val) {
      this.pageNum = val;
      this.loadTableData();
    },
    handleClueDetail(row) {
      if (row.saveCrmStatus === 0 || row.saveCrmStatus === 3) {
        if(!row.otherCrmObjectBind) return;
        CRM.api.show_detail({
          apiname: row.otherCrmObjectBind.apiName,
          id: row.otherCrmObjectBind.objectId
        });
      }
    },
    handleClueRow(row, column, event) {
      console.log("click clue row", ...arguments);
      this.handleClueDetail(row);
    }
  }
};
</script>

<style lang="less">
.table-page__wrapper {
  display: flex;
  flex-direction: column;
  .wrapper__header {
    width: 100%;
    min-height: 80px;
    background: #f6f9fc;
    padding: 16px 57px 16px 20px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    flex-shrink: 0;
    .header__left {
      flex-shrink: 0;
      padding-right: 16px;
      border-right: 1px solid #e9edf5;
      a {
        text-decoration: none;
        cursor: pointer;
      }
    }
    .header__right {
      padding-left: 16px;
      .right__name {
        font-size: 14px;
        color: #181c25;
        width: 520px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
  .wrapper__body {
    position: relative;
    padding-top: 20px;
    overflow: auto;
    flex: 1;
    .body__title {
      display: flex;
      align-items: center;
      line-height: 16px;
      margin-bottom: 16px;
      .title__before {
        width: 4px;
        height: 12px;
        background: var(--color-primary06,#ff8000);
        margin-right: 16px;
        flex-shrink: 0;
      }
      .title__text {
        font-size: 12px;
        color: #181c25;
        flex: 1;
      }
      .title__output {
        color: var(--color-primary06,#407FFF);
        font-size: 12px;
        margin-right: 20px;
        cursor: pointer;
      }
    }

    .body__table {
      // width: 647px;
      overflow-x: auto;
      overflow-y: hidden;
      margin: 0 20px;
      box-sizing: border-box;
      .tablewbg {
        width: auto;
        // margin: 0 20px;
        border-top: 1px solid #e9edf5 !important;
        border-left: 1px solid #e9edf5 !important;
        // border-width: 1px 1px 0;
        box-sizing: border-box;
        overflow: auto;
        .el-table__header {
          tr {
            th {
              background: #fff;
            }
          }
        }
        .el-table__empty-block {
          min-height: 200px;
        }
        &.bottomborder {
          border-width: 1px;
        }
      }
      .table__more {
        height: 40px;
        border: 1px solid #e9edf5;
        border-top-width: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 20px;
        a {
          font-size: 12px;
          text-decoration: none;
        }
      }
      .table__footer {
        margin-top: 10px;
        height: 35px;
      }
    }
  }
}
</style>
