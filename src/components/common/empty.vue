<template>
  <div :class="$style.card_empty_wrap">
    <img
      v-if="type === 'search'"
      :src="require('@/assets/images/search-empty.jpg')"
    >
    <img v-else-if="type === 'box'" :style="{ width: imgWidth, height: imgHeight }" :src="require('@/assets/images/common-empty.png')" alt="">
    <img
      v-else
      :src="require('@/assets/images/no-data.png')"
    >
    <p>{{ title }}</p>
    <p>{{ desc }}</p>
    <div
      v-if="button"
      :class="$style.addbutton"
      @click="handleClick"
    >
      <i
        v-if="!hideAddIcon"
        class="yxt-icon16 icon-add-b"
      />
      <span>{{ button }}</span>
    </div>
    <slot />
  </div>
</template>
<script>
export default {
  props: {
    type: {
      type: String,
      default: 'default', // default|search
    },
    title: {
      type: String,
      default: '',
    },
    desc: {
      type: String,
      default: '',
    },
    button: {
      type: String,
      default: '',
    },
    hideAddIcon: {
      type: Boolean,
      default: false,
    },
    imgWidth: {
      type: String,
      default: '90px',
    },
    imgHeight: {
      type: String,
      default: '90px',
    },
  },
  methods: {
    handleClick() {
      this.$emit('onClick')
    },
  },
}
</script>
<style lang="less" module>
.card_empty_wrap {
  text-align: center;
  padding: 80px 0;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  > img {
    width: 90px;
    height: 90px;
    margin-bottom: 18px;
  }
  > p {
    font-size: 13px;
    color: @color-subtitle;
  }
  .addbutton {
    margin-top: 9px;
    color: var(--color-info06,#407FFF);
    font-size: 14px;
    cursor: pointer;
  }
}
</style>
