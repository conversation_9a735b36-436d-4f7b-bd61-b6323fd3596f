<template>
  <div class="marketing__confetti-comp">
    <canvas
      ref="confettiCanvasEffect"
      :style="{
        position: 'fixed',
        top: 0,
        left: 0,
        pointerEvents: 'none',
        zIndex: 999
      }"
    />
    <fx-dialog
      v-if="dialogVisible"
      :visible="dialogVisible"
      :showHeader="false"
      :close-on-click-modal="false"
      width="480px"
      class="marketing__confetti-comp-dialog"
      @close="handleClose"
    >
      <div class="marketing__confetti-comp-dialog-content">
        <div
          class="marketing__confetti-comp-dialog-content-title"
          v-if="confettiConfig"
        >
          <span class="fx-icon-ok"></span>
          <span>{{ confettiConfig.title }}</span>
        </div>
        <div
          v-if="confettiConfig.tips"
          class="marketing__confetti-comp-dialog-content-tips"
        >
          {{ confettiConfig.tips }}
        </div>
        <div class="marketing__confetti-comp-dialog-content-btns">
          <fx-button type="primary" size="small" @click="handleConfirm">{{
            confettiConfig.buttonText || $t('marketing.commons.lcyd_7696e8')
          }}</fx-button>
          <fx-button plain size="small" @click="handleClose">{{
           $t('marketing.commons.wzdl_fe0337')
          }}</fx-button>
        </div>
      </div>
    </fx-dialog>
  </div>
</template>

<script>
export default {
  name: "ConfettiComp",
  props: {
    duration: {
      type: Number,
      default: 6000
    },
    particleCount: {
      type: Number,
      default: 150
    },
    startOffsetTop: {
      type: Number,
      default: 100
    },
    confettiConfig: {
      type: Object,
      default: () => ({
        showTipsDialog: false,
        status: "success",
        title: $t('marketing.pages.meeting_marketing.hycjcg_66004d'),
        tips: $t('marketing.pages.meeting_marketing.gdhyszxnkg_62ece7'),
        buttonText: $t('marketing.commons.lcyd_7696e8'),
      })
    }
  },
  data() {
    return {
      canvas: null,
      ctx: null,
      particles: [],
      animationId: null,
      colors: [
        "#FC8A73",
        "#30C776",
        "#EB4F56",
        "#0C6CFF",
        "#FA7268",
        "#ff5500"
      ],
      startTime: null,
      shapes: ["circle", "rect", "triangle", "line"],
      dialogVisible: false
    };
  },
  mounted() {
    this.initCanvas();
    this.createParticles();
    this.startAnimation();
  },
  beforeDestroy() {
    this.stopAnimation();
  },
  watch: {
    "confettiConfig.showTipsDialog": {
      handler(newVal) {
        console.log("newvalvalav", newVal);
        if (newVal) {
          this.dialogVisible = newVal;
        }
      },
      immediate: true
    }
  },
  methods: {
    initCanvas() {
      this.canvas = this.$refs.confettiCanvasEffect;
      this.ctx = this.canvas.getContext("2d");
      this.resizeCanvas();
      window.addEventListener("resize", this.resizeCanvas);
    },
    resizeCanvas() {
      this.canvas.width = window.innerWidth;
      this.canvas.height = window.innerHeight;
    },
    createParticles() {
      const centerX = this.canvas.width / 2;
      const startY = this.startOffsetTop;

      for (let i = 0; i < this.particleCount; i++) {
        // 计算初始速度和角度（360度发散）
        const angle = Math.random() * Math.PI * 2;
        const speed = Math.random() * 12 + 2; // 增加速度范围

        // 随机大小（更大的范围）
        const size = Math.random() * 15 + 3; // 3-18px

        // 计算水平和垂直速度分量
        const vx = Math.cos(angle) * speed;
        const vy = Math.sin(angle) * speed;

        this.particles.push({
          x: centerX,
          y: startY,
          size,
          color: this.colors[Math.floor(Math.random() * this.colors.length)],
          vx,
          vy,
          gravity: 0.1,
          rotation: Math.random() * Math.PI * 2,
          rotationSpeed: (Math.random() - 0.5) * 0.3,
          opacity: 1,
          shape: this.shapes[Math.floor(Math.random() * this.shapes.length)],
          width: Math.random() * 8 + 3,
          height: size * (Math.random() * 2 + 1),
          friction: 0.98
        });
      }
    },
    startAnimation() {
      this.startTime = Date.now();
      this.animate();
    },
    stopAnimation() {
      if (this.animationId) {
        cancelAnimationFrame(this.animationId);
        this.animationId = null;
      }
      window.removeEventListener("resize", this.resizeCanvas);
    },
    drawParticle(particle) {
      this.ctx.save();
      this.ctx.translate(particle.x, particle.y);
      this.ctx.rotate(particle.rotation);
      this.ctx.fillStyle = particle.color;
      this.ctx.strokeStyle = particle.color;
      this.ctx.globalAlpha = particle.opacity;
      this.ctx.lineWidth = 2;

      switch (particle.shape) {
        case "circle":
          this.ctx.beginPath();
          this.ctx.arc(0, 0, particle.size / 2, 0, Math.PI * 2);
          this.ctx.fill();
          break;

        case "rect":
          this.ctx.fillRect(
            -particle.width / 2,
            -particle.height / 2,
            particle.width,
            particle.height
          );
          break;

        case "triangle":
          this.ctx.beginPath();
          this.ctx.moveTo(-particle.size / 2, particle.size / 2);
          this.ctx.lineTo(particle.size / 2, particle.size / 2);
          this.ctx.lineTo(0, -particle.size / 2);
          this.ctx.closePath();
          this.ctx.fill();
          break;

        case "line":
          this.ctx.beginPath();
          this.ctx.moveTo(-particle.width / 2, 0);
          this.ctx.lineTo(particle.width / 2, 0);
          this.ctx.stroke();
          break;
      }

      this.ctx.restore();
    },
    animate() {
      const currentTime = Date.now();
      const elapsedTime = currentTime - this.startTime;

      if (elapsedTime >= this.duration) {
        this.stopAnimation();
        this.$emit("animationComplete");
        return;
      }

      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

      const fadeOutStart = this.duration * 0.7;
      const globalOpacity =
        elapsedTime > fadeOutStart
          ? 1 - (elapsedTime - fadeOutStart) / (this.duration - fadeOutStart)
          : 1;

      this.particles.forEach(particle => {
        // 应用摩擦力
        particle.vx *= particle.friction;
        particle.vy *= particle.friction;

        // 更新位置
        particle.x += particle.vx;
        particle.y += particle.vy;
        particle.vy += particle.gravity;

        // 添加轻微的随机摆动
        particle.vx += (Math.random() - 0.5) * 0.3;
        particle.vy += (Math.random() - 0.5) * 0.3;

        // 更新旋转
        particle.rotation += particle.rotationSpeed;

        // 设置透明度
        particle.opacity = globalOpacity * (1 - elapsedTime / this.duration);

        // 绘制粒子
        this.drawParticle(particle);
      });

      this.animationId = requestAnimationFrame(this.animate);
    },
    handleClose() {
      this.dialogVisible = false;
      this.$emit("onClose");
    },
    handleConfirm() {
      this.dialogVisible = false;
      this.$emit("onConfirm");
    }
  }
};
</script>

<style lang="scss" scoped>
.marketing__confetti-comp-dialog {
  /deep/ .el-dialog{
    margin-top: 32vh !important;
  }
  .marketing__confetti-comp-dialog-content{
    display: flex;
    flex-direction: column;
    gap: 16px;
    .marketing__confetti-comp-dialog-content-title{
      display: flex;
      align-items: center;
      gap: 8px;
      .fx-icon-ok{
        font-size: 24px;
        color: #55D48C;
      }
      span{
        font-size: 18px;
        font-weight: bold;
        color: #181C25;
      }
    }
    .marketing__confetti-comp-dialog-content-tips{
      font-size: 16px;
      color: #181C25;
    }
    .marketing__confetti-comp-dialog-content-btns{  
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style>

<style lang="less">
.marketing__confetti-comp-modal{
  background-color: transparent !important;
  pointer-events: auto !important;
}
</style>
