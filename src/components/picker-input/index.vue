<template>
  <div :class="[$style.picker_input,text instanceof Array && text.length ? $style.picker_input_tags_wrap:'']" name="picker_input">
    <div :class="$style.picker_input_text" v-if="text && !(text instanceof Array)"  @click="handleClick">
      <p :title="text" style="padding: 2px 0;">{{text}}</p>
      <i :class="$style.search_icon"></i>
    </div>
    <div :class="$style.select_option"  v-else-if="text instanceof Array && text.length">
      <div v-for="(item,index) in text" :class="$style.item" :key="index">
        {{item.name || item}}
        <span @click="handleRemove(item)">&times;</span>
      </div>
      <div :class="$style.button"  @click="handleClick"><img :src="require('@/assets/images/icons/add-dark.png')">{{placeholder}}</div>
    </div>
    <!-- <div :class="[$style.picker_input_text,$style.picker_input_tags]" v-else-if="text instanceof Array && text.length">
      <p v-for="(item,index) in text" :class="$style.tag" :title="item.name" :key="index">{{item.name || item}}</p>
    </div> -->
    <!-- <div :class="$style.picker_input_inner" @click="handleClick" v-else>
      <img :src="require('@/assets/images/icons/add-dark.png')">
      {{placeholder}}
    </div> -->
    <div :class="$style.picker_input_text" @click="handleClick" v-else>
      <span style="color:#666">{{placeholder}}</span>  <i :class="$style.search_icon"></i>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    placeholder: {
      type: String,
      default: $t('marketing.commons.qxz_708c9d'),
    },
    text: '',
  },
  methods: {
    handleClick() {
      this.$emit('click');
    },
    handleRemove(item) {
      this.$emit('remove', item);
    },
  },
};
</script>
<style lang="less" module>
.picker_input {
  width: 238px;
  height: 36px;
  border-radius: 3px;
  border: 1px solid @border-color-base;
  box-sizing: border-box;
  cursor: pointer;
  background-color: #fff;
  &.picker_input_tags_wrap {
    height: auto;
  }
  .search_icon {
    width: 34px;
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    background: url('@/assets/images/icon/search-icon.png') center center no-repeat;
    background-size: 16px;
    cursor: pointer;
    border-left: 1px solid @border-color-base;
  }
  .picker_input_text {
    height: 100%;
    padding: 0 12px;
    padding-right: 40px;
    display: flex;
    align-items: center;
    color: @color-title;
    position: relative;
    p {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    &.picker_input_tags {
      display: block;
      overflow: hidden;
      padding: 3px;
      height: auto;
      max-height: 200px;
      overflow-y: auto;
      font-size: 0;
      > p {
        display: inline-block;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        width: 60px;
        height: 28px;
        line-height: 28px;
        background: #e0eeff 100%;
        color: #839ab6;
        border-radius: 3px;
        margin-right: 3px;
        text-align: center;
        padding: 0 5px;
        font-size: 12px;
        margin-bottom: 3px;
      }
    }
  }
  .picker_input_inner {
    height: 100%;
    display: flex;
    align-items: center;
    font-size: 13px;
    padding: 0 12px;
    color: @color-link;
    line-height: 20px;
    > img {
      width: 10px;
      margin-right: 5px;
    }
  }
  .select_option {
    display: flex;
    flex-flow: row wrap;
    align-items: center;
    padding: 0 4px 4px;
    width: 100%;
    min-height: 36px;
    line-height: initial;
    font-size: 12px;
    color: var(--color-primary06,#407FFF);
    box-sizing: border-box;
    .item {
      margin-right: 4px;
      margin-top: 4px;
      height: 28px;
      line-height: 28px;
      padding: 0 8px;
      font-size: 12px;
      color: #212b36;
      background: #e3eafa;
      border-radius: 2px;
      & > span {
        color: #999;
        cursor: pointer;
      }
    }
    .button {
      cursor: pointer;
      margin-top: 4px;
      display: flex;
      align-items: center;
      font-size: 13px;
      line-height: 20px;
      color: @color-link;
      > img {
        width: 10px;
        margin-right: 3px;
      }
    }
  }
}
</style>