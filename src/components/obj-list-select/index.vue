<template>
  <div
    :class="$style.sharegpt_selector"
    @click="handleShowAgentDialog"
  >
    <fx-input
      :readonly="true"
      :size="size"
      :placeholder="placeholder"
      :collapse-tags="false"
      :value="selectedAgentList"
    >
      <i
        slot="suffix"
        class="el-input__icon fx-icon-arrow-down"
        style="font-size: 12px;"
      />
    </fx-input>
  </div>
</template>

<script>
import { requireAsync } from '@/utils/index.js'

export default {
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    size: {
      type: String,
      default: 'small',
    },
    placeholder: {
      type: String,
      default: $t('marketing.commons.qxz_708c9d'),
    },
    apiName: {
      type: String,
      default: '',
    },
  },
  computed: {
    selectedAgentList() {
      return this.value ? this.value.name : ''
    },
  },
  methods: {
    handleShowAgentDialog() {
      requireAsync('crm-modules/components/pickselfobject/pickselfobject', PickObject => {
        const picker = new PickObject()
        picker.on('select', item => {
          this.$emit('input', {
            id: item._id,
            name: item.name,
          })
          picker.destroy()
        })
        picker.render({
          layout_type: 'list',
          include_layout: true,
          apiname: this.apiName,
          dataId: this.value,
          zIndex: this.zIndex,
        })
      })
    },
  },
}
</script>

<style lang="scss" module>
.sharegpt_selector {
  width: auto;
}
</style>
