define(function(require, exports, module) {
  require("crm-modules/action/myobject/myobject");
  var field = require("crm-modules/action/field/field");

  let marketingCrmFiledEmpty = false;

  function extendModel(Model) {
    return Model.extend({
      beforeSetData(data) {
        try {
          const baseInfo =
            _.findWhere(data.layout, { api_name: "base_field_section__c" }) ||
            {};
          baseInfo.label = $t('marketing.commons.schdbtxx_200056');
          const fields = [];
          baseInfo.components.forEach(field => {
            if (field.is_required) {
              fields.push(field);
            }
          });
          baseInfo.components = fields;
          // 判断并赋值
          marketingCrmFiledEmpty = !baseInfo.components || baseInfo.components.length === 0;
        } catch (error) {}
      }
    });
  }

  function liveExtendModel(Model) {
    return Model.extend({
      beforeSetData(data) {
        try {
          data.layout = data.layout.filter(layout => {
            if(layout.group_type === "area"){
              return layout.components.some(field => field.is_required === true)
            }
            return true;
          });
          const baseInfo =
            _.findWhere(data.layout, { api_name: "base_field_section__c" }) ||
            {};
          baseInfo.label = $t('marketing.commons.schdbtxx_200056');
          const fields = [];
          baseInfo.components.forEach(field => {
            if (field.is_required) {
              fields.push(field);
            }
          });
          baseInfo.components = fields;
          // 判断并赋值
          marketingCrmFiledEmpty = !baseInfo.components || baseInfo.components.length === 0;
        } catch (error) {
          console.log(error);
        }
      }
    });
  }

  var extendModel = extendModel(field.Model);
  var liveExtendModel = liveExtendModel(field.Model);

  module.exports = {
    View: field.View,
    Model: extendModel,
    LiveModel: liveExtendModel,
    marketingCrmFiledEmpty
  };
});
