<!-- 组件说明 -->
<template>
  <div class="">
    <div class="marketing-crm-marketingeventobj__wrapper"></div>
  </div>
</template>

<script>
import { requireAsync } from "@/utils";
import http from "@/services/http/index";
import field from "./field";

export default {
  components: {},
  data() {
    return {
      fieldView: null
    };
  },
  props: {
    fieldData: {
      type: Object,
      default: () => {}
    },
    hideFields: {
      type: Array,
      default: () => []
    },
    type: {
      type: String,
      default: ""
    }
  },
  watch: {
    fieldData: {
      deep: true,
      handler(val) {
        if (val && Object.keys(val).length > 0) {
          this.initCrmFiled();
        }
      },
    }
  },
  computed: {},
  methods: {
    initCrmFiled() {
      // requireAsync("crm-modules/action/field/field", field => {
      //   field.add({
      //     apiname: "MarketingEventObj",
      //     zIndex: 999
      //   })
      // })
      this.fieldView && this.fieldView.destroy();
      const Model = this.type === "live" ? field.LiveModel : field.Model;
      this.fieldView = new field.View({
        el: $(".marketing-crm-marketingeventobj__wrapper"),
        apiname: "MarketingEventObj",
        record_type: "default__c",
        title: "",
        show_type: "full",
        include_detail_describe: false,
        showNav: false,
        isWM: true,
        hideFields: this.hideFields,
        data: this.fieldData,
        Model,
        zIndex: 999
      });
      this.fieldView.render();
      this.$emit('emptyField',field.marketingCrmFiledEmpty)
    },
    submit() {
      let data = this.fieldView.collect();
      console.log(data);
      if (!this.fieldView.validate()) return false;
      return data;
    },
  },
  mounted() {
    this.initCrmFiled();
  },
  created() {},
  destroyed() {} //生命周期 - 销毁完成
};
</script>

<style lang="less">
.marketing-crm-marketingeventobj__wrapper {
  padding: 0px;
  max-width: 1000px;

  .crm-action-nfield {
    padding-left: 0;
    // position: static;
    overflow: initial;
    .view-box {
      margin-left: 12px;
      min-width: auto;
      .f-g-tit:first-child {
        margin-left: -12px;
      }
    }
    .f-required .f-g-item-label:before {
      font-size: 15px !important;
      margin: 0 4px 0 0;
    }
    .f-g-item {
      margin-top: 20px;
      color: #606266;
      // position: static;

      .f-item-wrap {
        // position: static;
        .crm-ico-error {
          &::before {
            width: 0;
            height: 0;
          }
          color: #f56c6c;
          font-size: 12px;
        }
        // .f-item-inner {
        //   position: static;
        //   .select-wrap {
        //     position: static;
        //     .crm-widget {
        //       position: static;
        //     }
        //   }
        // }
      }
    }
  }
}
</style>
