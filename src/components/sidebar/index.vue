<template>
  <div class="side-menu">
    <div 
      v-for="(item, index) in menuItems" 
      :key="index"
      class="menu-item"
      :class="{ active: activeIndex === index }"
      @click="handleClick(index)"
    >
      {{ item.title }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'Sidebar',
  props: {
    // 菜单项数组，每项包含 title 属性
    menuItems: {
      type: Array,
      required: true
    },
    defaultActiveIndex: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      activeIndex: this.defaultActiveIndex
    }
  },
  methods: {
    handleClick(index) {
      this.activeIndex = index;
      this.$emit('menu-click', this.menuItems[index]);
    }
  }
}
</script>

<style lang='less' scoped>
.side-menu {
  width: 100%;
  box-sizing: border-box;
  background: @color-primary;
  border-radius: @border-radius-base;
  padding: 12px;

  .menu-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    line-height: 20px;
    height: 36px;
    font-size: 13px;
    color: @color-title;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    transition: all 0.3s;
    cursor: pointer;
    border-radius: @border-radius-base;
    box-sizing: border-box;
    margin-bottom: 8px;

    &:hover {
      background-color: var(--color-neutrals03);
    }

    &.active {
      background-color: var(--color-primary01);
    }
  }
}

</style>
