<template>
  <div :class="$style.ChannelSelector">
    <Popover
      v-model="visible"
      :class="$style.wrap"
      :popper-class="$style.ChannelSelectorPopver"
      width="220"
      trigger="click"
      placement="bottom-start"
      @show="handleShow"
      @hide="handleChange"
    >
      <div
        slot="reference"
        :class="$style.picker"
      >
        <div
          :class="[$style.value, selectedText ? '' : $style.placeholder]"
          :title="selectedText"
        >
          {{ selectedText || placeholder }}
        </div>
        <div :class="[$style.arrow, popoverOpen ? $style.open : '']">
          <i :class="['el-icon-arrow-down']"></i>
        </div>
      </div>
      <div :class="$style.channelWrapper">
        <div :class="$style.channel">
          <fx-checkbox-group 
            :class="$style.channelCheckboxGroup" 
            v-model="checkList" 
            @change="handleOptionClick"
            :max="max"
          >
            <fx-checkbox 
              v-for="item in channels" 
              :key="item.value"
              :class="$style.channelCheckbox" 
              :disabled="item.disabled" 
              :label="item.value"
            >
              <template v-if="item.value === 'other'">
                <div :class="$style.otherWrapper">
                  <span>{{ item.label }}</span>
                  <fx-input
                    :disabled="!checkList.includes('other')"
                    v-model="otherValue"
                    :maxlength="10"
                    size="mini"
                    :class="$style.otherInput"
                      :placeholder="$t('marketing.commons.qsr_02cc4f')"
                    @input="handleOtherValueInput"
                  />
                </div>
              </template>
              <template v-else>
                {{ item.label }}
              </template>
            </fx-checkbox>
          </fx-checkbox-group>
        </div>
        
          <div v-if="multiple" :class="$style.selectedCount">{{ $t('marketing.components.ChannelSelector.yxzgqd_21ec9f', { data: ({ option0: checkList.length }) }) }}</div>
      </div>
    </Popover>
    <template v-if="checkList.includes('offiaccount') && !hideWechatSelector">
      <div :class="$style.checkbox">
        <Checkbox
          v-model="wechatChecked"
          @change="handleWechatChange"
        >
          {{ $t('marketing.components.ChannelSelector.zwxzdsbgzh_1999b5') }}
        </Checkbox>
      </div>
      <div
        v-if="wechatChecked"
        :class="$style.wechat_account"
      >
        <Select
          v-model="wxAppId"
          :class="['el-select', $style.wechat_account_selector]"
          size="small"
          :placeholder="$t('marketing.commons.xzgzh_509492')"
          @change="handleWechatChange"
        >
          <Option
            v-for="(item, index) in wxLists"
            :key="index"
            :label="item.wxAppName"
            :value="item.wxAppId"
          />
        </Select>
      </div>
    </template>
  </div>
</template>
<script>
import http from '@/services/http/index'
import wxList from '@/modules/wx-list'
import { debounce } from '@/utils'

export default {
  components: {
    Popover: FxUI.Popover,
    Checkbox: FxUI.Checkbox,
    Select: FxUI.Select.components.ElSelect,
    Option: FxUI.Select.components.ElSelect.components.ElOption,
    Tooltip: FxUI.Tooltip,
    Input: FxUI.Input
  },
  props: {
    value: {
      type: String,
      default: '',
    },
    placeholder: {
      type: String,
      default: $t('marketing.commons.qxztgqd_d5ff9d'),
    },
    hideWechatSelector: {
      type: Boolean,
      default: false,
    },
    encodeOtherValue: {
      type: Boolean,
      default: true,
    },
    disItems: {
      type: Array,
      default: () => [],
    },
    // 是否允许多选
    multiple: {
      type: Boolean,
      default: false,
    },
    max: {
      type: Number,
      default: 1,
    },
  },
  data() {
    return {
      visible: false,
      channels: [],
      channelsMap: {},
      otherValue: '',
      popoverOpen: false,
      wxAppId: '',
      wechatChecked: false,
      checkList: [],
    }
  },
  computed: {
    wxLists() {
      return wxList.datas.list || []
    },
    wxListsMap() {
      return wxList.datas.list.reduce((pre, cur) => {
        pre[cur.wxAppId] = cur.wxAppName
        return pre
      }, {})
    },
    selectedText() {
      if (!this.checkList.length) {
        return ''
      }
      
      if (this.multiple) {
        const selectedLabels = this.checkList.map(value => {
          if (value === 'other' && this.otherValue) {
            return `${this.channelsMap[value]}:${this.otherValue}`
          }
          return this.channelsMap[value]
        })
        return selectedLabels.join('、')
      } else {
        const value = this.checkList[0]
        if (value === 'other' && this.otherValue) {
          return `${this.channelsMap[value]}:${this.otherValue}`
        }
        return this.channelsMap[value] || ''
      }
    },
  },
  watch: {
    value: {
      deep: true,
      handler() {
        this.init()
      },
    },
    disItems(val) {
      this.channels.forEach(channel => {
        if (channel.value !== 'other') {
          if (val.includes(channel.value)) {
            channel.disabled = true
          } else {
            channel.disabled = false
          }
        }
      })
    },
  },
  async created() {
    // 先获取渠道列表 再初始化
    await this.queryChannelList()
    this.init()
  },
  methods: {
    async queryChannelList() {
      const res = await http.queryChannelList()
      if (res && res.errCode === 0) {
        let otherItem = {}
          const channels = (res.data || []).reduce((arr, item) => {
            this.channelsMap[item.value] = item.label
            if (item.value === 'other') {
              otherItem = item
              arr.push(otherItem)
            } else {
              if (this.disItems.includes(item.value)) {
                item.disabled = true
              } else {
                item.disabled = false
              }
              arr.push(item)
            }
            return arr
          }, [])
          this.channels = [...channels]
      }
    },
    handleOptionClick(val) {
      this.checkList = val
      this.wechatChecked = false
      this.wxAppId = ''  // 重置公众号ID

      // 单选模式下自动关闭弹窗
      if (!this.multiple && this.checkList.length) {
        this.visible = false
      }
      this.$emit('change', this.genChannelValue())
      this.handleInput()
      
      // 处理公众号选择
      if (val.includes('offiaccount')) {
        // 自动选中公众号checkbox
        this.wechatChecked = true
        // 默认选中第一个公众号
        if (this.wxLists.length > 0) {
          this.wxAppId = this.wxLists[0].wxAppId
          setTimeout(() => {
            this.handleWechatChange()
          }, 0)
        } else {
          // 如果没有可选的公众号，提示用户
          FxUI.Message.warning($t('marketing.components.ChannelSelector.zwkxdgzhqx_41e9db'))
          // 移除公众号选项
          this.checkList = this.checkList.filter(item => item !== 'offiaccount')
          this.wechatChecked = false
        }
      } else {
        // 未选中公众号时，清空相关状态
        this.wechatChecked = false
        this.wxAppId = ''
        this.handleWechatChange()
      }
    },
    init() {
      // 处理空值情况
      if (!this.value) {
        this.checkList = []
        this.otherValue = ''
        return
      }

      // 将输入值统一转换为数组处理
      const values = Array.isArray(this.value) ? this.value : [this.value]
      
      // 重置选中状态
      this.checkList = []
      this.otherValue = ''

      values.forEach(selected => {
        if (selected.indexOf('other:') !== -1) {
          // 处理包含自定义渠道的情况
          const [channel, otherValue] = selected.split(':')
          this.checkList.push(channel)
          
          // 只取第一个other的值作为otherValue
          if (channel === 'other' && !this.otherValue && otherValue) {
            const decodedValue = otherValue.replace(/\+\+/g, '%')
            this.otherValue = decodeURI(decodedValue)
          }
        } else {
          // 处理普通渠道
          this.checkList.push(selected)
        }
      })
      console.log(this.checkList,'initinitinitcheckList')
      // 单选模式下只保留第一个选中项
      if (!this.multiple && this.checkList.length > 1) {
        this.checkList = [this.checkList[0]]
      }
    },
    handleOtherValueInput(value) {
      // 替换保留关键符号:和%为空
      this.otherValue = value.replace(/:|\+/g, '')
      // this.handleInput();
    },
    genChannelValue() {
      /**
       * 由于urllink.generate生成小程序调整链接不支持中文和'%'字符
       * encodeURL后把%转成++，解密时注意先转成%再执行decodeURL方法
       * @url https://developers.weixin.qq.com/miniprogram/dev/api-backend/open-api/url-link/urllink.generate.html
       */
      if (!this.checkList.length) {
        return this.multiple ? [] : ''
      }

      const generateValue = (channelValue) => {
        const value = this.encodeOtherValue ? encodeURI(this.otherValue).replace(/%/g, '++') : this.otherValue
        return channelValue === 'other' ?   `${channelValue}:${value}` : channelValue
      }

      if (this.multiple) {
        return this.checkList.map(generateValue)
      }
      return generateValue(this.checkList[0])
    },
    handleInput() {
      // 使用debounce防抖,避免频繁触发input和change事件
      // 当用户快速输入时,只会在最后一次输入后30ms触发一次事件
      // 这样可以提高性能,减少不必要的事件触发和状态更新
      debounce(() => {
        this.$emit('input', this.genChannelValue())
        this.$emit('change', this.genChannelValue())  
      }, 30)()
    },
    handleChange() {
      this.popoverOpen = false
      this.$emit('change', this.genChannelValue())
      
      // 更新选中文本
      const getChannelText = (value) => {
        if (value.includes('other:')) {
          const [channel, otherValue] = value.split(':')
          return `${this.channelsMap[channel]}:${otherValue}`
        }
        return this.channelsMap[value]
      }
      
      const changeText = this.multiple
        ? this.checkList.map(getChannelText).join('、')
        : getChannelText(this.checkList[0] || '')
      
      this.$emit('changeText', changeText)
    },
    handleWechatChange() {
      this.$emit('wechatSelected', this.wechatChecked ? this.wxAppId : '')
      this.$emit(
        'wechatSelectedText',
        this.wechatChecked ? this.wxListsMap[this.wxAppId] : '',
      )
    },
    handleShow() {
      this.popoverOpen = true
    },
    getChannelLabel(channelValue) {
      if (channelValue.indexOf('other:') !== -1) {
        const [channel, otherValue] = channelValue.split(':')
        // 将 ++ 替换回 % 并解码
        const decodedValue = otherValue.replace(/\+\+/g, '%')
        const chineseValue = decodeURI(decodedValue)
        return $t('marketing.components.ChannelSelector.qt_92b674', { data: ({ option0: chineseValue }) })
      }
      return this.channelsMap[channelValue] || channelValue
    },
  },
}
</script>
<style lang="less" module>
.ChannelSelector {
  display: inline-block;
  width: 100%;
  transition: all 0.2s ease;
  position: relative;
  height: fit-content;
  .question {
    position: absolute;
    right: -23px;
    cursor: pointer;
    font-size: 19px;
    top: 3px;
    color: #545861;
    line-height: 1.5;
  }
  &:hover {
    border-color: #c0c4cc;
  }
  .wrap {
    width: 100%;
    height: 100%;
  }
  .picker {
    height: 100%;
    display: flex;
    // height: 32px;
    border-radius: 4px;
    border: 1px solid @border-color-base;
    cursor: pointer;
    box-sizing: border-box;
  }
  .value {
    line-height: 32px;
    flex: 1;
    display: flex;
    align-items: center;
    padding: 0 10px;
    font-size: 12px;
    color: @color-title;
    // white-space: nowrap;
    min-width: 0;
    text-overflow: ellipsis;
    overflow: hidden;
    &.placeholder {
      color: @color-subtitle;
    }
  }
  .arrow {
    width: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-left: 1px solid @border-color-base;
    i {
      color: #c0c4cc;
      font-weight: bolder;
      position: relative;
      transition: all 0.2s ease;
    }
    &.open {
      i {
        transform: rotate(-180deg);
      }
    }
  }
  .checkbox {
    margin-top: 10px;
    :global {
      .el-checkbox .el-checkbox__label {
        font-size: 12px;
        color: @color-title!important;
      }
    }
  }
  .wechat_account {
    margin-top: 10px;
  }
  .wechat_account_selector {
    width: 100%;
    :global {
      input::-webkit-input-placeholder {
        color: @color-subtitle;
      }
      input::-ms-input-placeholder {
        color: @color-subtitle;
      }
    }
  }
}

.ChannelSelectorPopver {
  padding: 0 !important;
  
  .channelWrapper {
    display: flex;
    flex-direction: column;
    height: 300px; // 设置固定高度
  }

  .channel {
    flex: 1;
    overflow-y: auto;
    &::-webkit-scrollbar {
      width: 5px;
    }
  }

  .item {
    padding: 7px 8px;
    box-sizing: border-box;
    min-height: 32px;
    display: flex;
    align-items: center;
    font-size: 12px;
    color: @color-title;
    cursor: pointer;
    &:hover {
      background-color: #f0f4fc;
    }
    &.top_line {
      border-top: 1px solid @border-color-base;
    }
    .title {
      position: relative;
      padding-left: 20px;
      &:before {
        display: inline-block;
        content: " ";
        width: 16px;
        height: 16px;
        background-repeat: no-repeat;
        background-size: 100%;
        position: absolute;
        // top: 50%;
        left: 0;
        // transform: translate(0, -50%);
      }
    }
    .custom {
      margin-top: 5px;
      :global {
        .el-input__inner {
          cursor: pointer;
        }
      }
    }
    &.active {
      .title::before {
        background-image: url("@/assets/images/icon/icon-drop-selected.png");
      }
    }
  }
  .channelCheckboxGroup{
    display: flex;
    flex-direction: column;
  }
  .channelCheckbox{
    padding: 8px;
    line-height: 26px;
    margin-right: 0;
  } 
  .channelCheckbox[aria-hidden=true] {
    display: none !important;
  }
  .otherWrapper{
    display: flex;
    flex-direction: column;
    span{
      margin-bottom: 3px;
    }
  }
  .selectedCount {
    flex: none; // 不参与弹性布局
    padding: 8px;
    font-size: 12px;
    color: #606266;
    text-align: center;
    border-top: 1px solid #EBEEF5;
    background: #fff; // 确保背景色
    position: sticky; // 使用sticky定位
    bottom: 0; // 固定在底部
  }
}
.is_disabled {
  color: #c0c4cc !important;
  cursor: not-allowed !important;
}
</style>
