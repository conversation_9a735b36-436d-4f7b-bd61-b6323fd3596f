<template>
  <div>
    <!-- 邮件推广雷达 -->
    <mail-spread-detail
      v-if="mailVisible"
      :visible="mailVisible"
      :item="mailParams"
      @close="mailVisible = false"
    />
    <sideslip-popup
      v-else
      :class="[$style.market_promotion_details, 'market_promotion_details__wrapper']"
      v-bind="$attrs"
      width="900px"
      :overflowy-hidden="detailVisible"
      v-on="$listeners"
      @close="handleClose"
    >
      <template v-if="showCrmAuthorityTips">
        <noPremissionTip @handleOperate="handleClose" />
      </template>
      <template v-else>
        <template v-if="!isShowTable">
          <div
            v-if="loading"
            class="km-g-loading-mask"
          >
            <span class="loading" />
          </div>

          <div :class="$style.header">
            <div
              v-if="detail.marketingEventId"
              :class="$style.title_con"
            >
              <div :class="$style.content_title">
                <span
                  style="cursor: pointer"
                  @click="handleOpenmMrketingDetail(detail.marketingEventId)"
                >{{
                  detail.marketingEventName
                }}</span>
                <span
                  v-if="detail.marketingEventStatus"
                  :class="$style.title_label"
                >{{
                  detail.marketingEventStatus
                }}</span>
              </div>
              <div
                v-if="detail.spreadType != 3"
                :class="$style.title_desc"
              >
                {{ $t('marketing.commons.tgbt_ce8928') }}{{ detail.name }}
              </div>
            </div>
            <div
              v-else
              :class="$style.title_con"
            >
              <div :class="$style.content_title">
                {{ $t('marketing.commons.tgbt_ce8928') }}{{ detail.name }}
              </div>
              <div
                v-if="detail.description"
                :class="$style.title_desc"
              >
                {{ detail.description }}
              </div>
            </div>
            <!-- 取消发送按钮 -->
            <div :class="$style.options">
              <el-button
                v-if="detail.sendImmediately"
                :class="$style.option_button"
                type="primary"
                size="mini"
                :loading="sending"
                plain
                @click="handleSendNow(detail)"
              >
                {{ $t('marketing.commons.ljfs_1176c5') }}
              </el-button>
              <el-button
                v-if="detail.spreadType === 1 || detail.spreadType === 7 || detail.spreadType === 13"
                :class="$style.option_button"
                type="primary"
                size="mini"
                plain
                @click="handleSendAgain(detail)"
              >
                {{ $t('marketing.commons.fz_79d3ab') }}
              </el-button>
              <el-button
                v-if="(detail.spreadType === 1 || detail.spreadType === 7 || detail.spreadType === 13) && detail.sendRevocable"
                :class="$style.option_button"
                type="primary"
                size="mini"
                plain
                @click="handleRevokeSend(detail)"
              >
                {{ $t('marketing.commons.chtg_4d5ae4') }}
              </el-button>
              <el-button
                v-if="detail.spreadType !== 1 && detail.spreadType !== 7 && detail.spreadType !== 13 && detail.sendCancelable"
                :class="$style.option_button"
                type="primary"
                size="mini"
                plain
                @click="handleCancelSend(detail)"
              >
                {{ $t('marketing.commons.qxfs_b022e6') }}
              </el-button>
            </div>
          </div>

          <div :class="$style.marketing_detail">
            <div :class="$style.gird">
              <div :class="$style.title">
                {{
                  detail.wechatMessageType == 1002
                    ? $t('marketing.components.market_promotion_details.djxqtz_eb4044')
                    : $t('marketing.commons.tgnr_a6ec90')
                }}
              </div>
              <!-- 模板消息预览 -->
              <div
                v-if="detail.spreadType == 2 && detail.wechatMessageType != 1002"
                :class="$style.tplmsg_magnifier_btn"
                @click="msgPreviewVisible = true"
              >
                {{ $t('marketing.commons.yl_645dbc') }}
              </div>
              <!-- 企业微信群发消息推广内容添加文本 -->
              <div
                v-if="detail.spreadType == 5"
                :class="$style.content"
              >
                {{ detail.qywxGroupSendMessageDetailResult.content }}
              </div>
              <pm-content
                v-if="
                  detail.wechatMessageType != 1002 || (detail.wechatMessageType == 1002 && detail.redirectType === 1)
                "
                :detail="detail"
                @linkclick="handleOpenDetail"
              />
              <div
                v-if="detail.wechatMessageType == 1002 && detail.redirectType === 3"
                :class="$style.tplmsg_content"
              >
                <p>{{ $t('marketing.commons.xcx_e599c4') }}:{{ detail.miniAppId || '--' }}</p>
                <p>
                  {{ $t('marketing.commons.xcxymdz_70c5f5') }}
                  <a>{{ detail.miniAppPagePath || '--' }}</a>
                </p>
              </div>
              <div
                v-if="detail.wechatMessageType == 1002 && (detail.redirectType === 2 || detail.redirectType === 0)"
                :class="$style.tplmsg_content"
              >
                <p>
                  {{ $t('marketing.commons.tzdz_a50f7b') }}
                  <a
                    :href="detail.redirectUrl"
                    target="_blank"
                  >
                    {{ detail.redirectUrl }}
                  </a>
                </p>
              </div>
              <div :class="$style.prof_infos">
                <table :class="$style.prof_table">
                  <tr
                    v-for="(row, index) in headerItems"
                    :key="index"
                  >
                    <template v-for="(item, idx) in row">
                      <td
                        :key="`label-${idx}`"
                        :class="$style.label"
                      >
                        {{ item.label }}
                      </td>
                      <td
                        :key="`value-${idx}`"
                        :class="$style.value"
                      >
                        <user-group-view
                          v-if="item.mktUGids"
                          :class="$style.user_group"
                          :group-ids="item.mktUGids"
                        />
                        <err-state
                          v-else-if="item.reply"
                          :data="item.reply"
                        />

                        <el-popover
                          v-else-if="item.sendRange === 5"
                          placement="bottom-start"
                          trigger="hover"
                          popper-class="pm-content-popper"
                        >
                          <span
                            v-for="(tag, index) in item.value"
                            :key="tag.combineName"
                            :class="$style.tagitem"
                          ><span v-if="index > 0">、</span>{{ tag.combineName }}</span>
                          <div slot="reference">
                            <div :class="$style.tagwrapper">
                              <span
                                v-for="(tag, index) in item.value"
                                :key="tag.combineName"
                                :class="$style.tagitem"
                              ><span v-if="index > 0">、</span>{{ tag.combineName }}</span>
                            </div>
                          </div>
                        </el-popover>
                        <el-popover
                          v-else-if="item.label === $t('marketing.commons.fsfw_dcdd59') && data.spreadType === 5"
                          placement="bottom"
                          trigger="hover"
                          popper-class="pm-content-popper"
                          :disabled="!item.value[1]"
                        >
                          <span>{{ item.value[1] }}</span>
                          <div slot="reference">
                            <span :class="[$style.range_content, 'km-t-ellipsis2']">{{
                              `${item.value[0]}${item.value[1] ? '：' + item.value[1] : ''}`
                            }}</span>
                          </div>
                        </el-popover>
                        <el-popover
                          v-else-if="item.label === $t('marketing.commons.fsfw_dcdd59') && data.spreadType === 13"
                          placement="bottom"
                          trigger="hover"
                          popper-class="filter-content-popper"
                          :disabled="item.value"
                        >
                          <FilterDisplay
                            v-model="sendRangeFilterData"
                            is-preview
                          />
                          <div slot="reference">
                            {{ item.value || '--' }}
                          </div>
                        </el-popover>
                        <el-tooltip
                          v-else-if="item.label === $t('marketing.commons.fszt_d48fc2') && data.spreadType === 2 && data.status === 4"
                          effect="light"
                          placement="bottom"
                          :open-delay="100"
                          :content="data.reason"
                        >
                          <span :class="[$style.send_fail_text]">
                            <span>{{ item.value }}</span>
                            <span :class="$style.send_fail_icon" />
                          </span>
                        </el-tooltip>
                        <span v-else>{{ item.value || '--' }}</span>
                      </td>
                    </template>
                  </tr>
                </table>
              </div>
            </div>

            <div :class="$style.statistics_wrapper">
              <div
                v-if="data.spreadType == 1 || data.spreadType == 13"
                :class="$style.statistics_funnel"
              >
                <div
                  ref="funnelChart"
                  style="width: 250px; height: 120px"
                />
              </div>
              <div :class="$style.statistics">
                <div
                  v-for="(item, index) in statistics"
                  :key="index"
                  :class="[
                    $style.item,
                    item.length && $style.item_wrapper,
                    item.enableClick && !marketingLoading ? $style.enableClick : '',
                  ]"
                  @click.stop="handleShowMoreData(item)"
                >
                  <template v-if="!item.length">
                    <span>{{ item.label }} </span>
                    <div :class="$style.count">
                      {{ item.value }}
                    </div>
                  </template>
                  <template v-else>
                    <div
                      v-for="(subItem, index) in item"
                      :key="index"
                      :class="[$style.sub_item, subItem.enableClick && !marketingLoading ? $style.enableClick : '']"
                      @click.stop="handleShowMoreData(subItem)"
                    >
                      <div :class="$style.title">
                        <span :class="$style.label">{{ subItem.label }} </span>
                        <span
                          v-show="index === 0"
                          :class="$style.line"
                        >/</span>
                      </div>
                      <div :class="$style.count">
                        {{ subItem.value }}<span
                          v-show="index == 0"
                          :class="$style.line"
                        >/</span>
                      </div>
                    </div>
                  </template>
                </div>
                <QuestionTooltip
                  v-if="detail.spreadType == 1 || detail.spreadType == 13"
                  :class="$style.export_question"
                  placement="top-start"
                  :offset="40"
                  effect="dark"
                >
                  <div
                    slot="question-content"
                    :class="$style.export_question_content"
                  >
                    {{ $t('marketing.components.market_promotion_details.rwxfygsjsd_716ab2') }}<br>
                    {{ $t('marketing.components.market_promotion_details.ytgygsggfx_afd839') }}<br>
                    {{ $t('marketing.components.market_promotion_details.wtgygswzgg_a1a04d') }}<br>
                    {{ $t('marketing.components.market_promotion_details.ygtgzcszgg_6b251c') }}<br>
                    {{ $t('marketing.components.market_promotion_details.fwrcyghkhf_074323') }}<br>
                    {{ $t('marketing.components.market_promotion_details.zfrcbhygtg_7c894d') }}
                  </div>
                </QuestionTooltip>
              </div>
            </div>

            <div :class="[$style.gird, $style.table_gird]">
              <el-tabs
                v-model="activeName"
                @tab-click="handleTabClick"
              >
                <el-tab-pane
                  v-if="detail.spreadType == 5"
                  :label="$t('marketing.commons.ygxq_b47af7')"
                  name="fifth"
                >
                  <template v-if="activeName === 'fifth'">
                    <div
                      :class="$style.filter"
                      class="filter"
                    >
                      <div>
                        <RadioGroup
                          v-if="detail.chatType !== 2"
                          v-model="employeeStatusFitler"
                          size="small"
                          @input="handlePageNumEmployee(1)"
                        >
                          <RadioButton :label="-1">
                            {{ $t('marketing.commons.qb_a8b0c2') }}
                          </RadioButton>
                          <RadioButton :label="1">
                            {{ $t('marketing.commons.yfs_93d159') }}
                          </RadioButton>
                          <RadioButton :label="0">
                            {{ $t('marketing.commons.wfs_73e127') }}
                          </RadioButton>
                        </RadioGroup>
                      </div>

                      <div style="display: flex; align-items: center">
                        <VMemberRangeSelector
                          :options="{ excludeUser: true }"
                          lock="qywx"
                          :title="$t('marketing.commons.xzbm_bb5fee')"
                          @update:memberRangeChange="handleEmployeeMemberRangeChange"
                        />
                        <el-button
                          type="text"
                          @click="handleExport('employeeDetail')"
                        >
                          {{ $t('marketing.commons.dc_55405e') }}
                        </el-button>
                      </div>
                    </div>
                    <div
                      v-if="marketingLoading"
                      class="km-g-loading-mask"
                    >
                      <span class="loading" />
                    </div>
                    <v-table
                      v-loading="showSendDetailLoading"
                      :class="[$style.tablewbg, totalCount === 0 && $style.bottomborder]"
                      :data="tableLists"
                      :columns="columnsForSMS"
                      :row-style="{ height: '50px' }"
                      @custom:user-action="handleShowDetail"
                      @custom:unSendCount-action="showUserDetailUnSendCount"
                      @custom:sentCount-action="showUserDetailSentCount"
                      @custom:outOfLimitCount-action="showUserDetailOutOfLimitCount"
                      @custom:notFriendRelationCount-action="showUserDetailNotFriendRelationCount"
                      @custom:successGroupCount-action="showUserDetailTotalGroupCount"
                    />
                    <pagination
                      :class="$style.pagination"
                      background
                      :current-page="pageNum"
                      :page-size="pageSize"
                      :pager-count="5"
                      :page-sizes="[5, 10, 20, 30, 40]"
                      layout="prev, pager, next, total, sizes, jumper"
                      :total="totalCount"
                      @size-change="handlePageSizeEmployee"
                      @current-change="handlePageNumEmployee"
                    />
                  </template>
                </el-tab-pane>
                <el-tab-pane
                  :label="
                    detail.spreadType == 1 || detail.spreadType == 7 || detail.spreadType == 13
                      ? $t('marketing.commons.tgpm_d3ff73')
                      : detail.spreadType == 5
                        ? detail.chatType === 2
                          ? $t('marketing.components.market_promotion_details.khqxq_5e030c')
                          : $t('marketing.commons.khxq_660dbe')
                        : $t('marketing.components.market_promotion_details.fsxq_f36411')
                  "
                  name="first"
                >
                  <template v-if="activeName === 'first'">
                    <div
                      v-if="/1|7|13/.test(detail.spreadType)"
                      :class="[$style.title, $style.title_promote]"
                    >
                      <div
                        v-if="detail.spreadType === 1"
                        style="height: 36px"
                      >
                      <!-- 全员营销通讯录跟随设置走 -->
                        <VMemberRangeSelector :source="detail.spreadType == 1 ? 'marketingPromotion' : ''" @update:memberRangeChange="handleMemberRangeChange" />
                      </div>
                      <div
                        v-if="detail.spreadType === 13"
                        style="height: 36px"
                      >
                        <FilterButton
                          v-model="tableFilterData"
                          :btns="['filter']"
                          :object-names="[{ name: $t('marketing.commons.hy_4d9dd5'), value: 'MemberObj' }]"
                        />
                      </div>
                      <!-- 未推广员工提醒 -->
                      <EmployeeReminder
                        :data="promoteEmployeeData"
                        :marketing-activity-id="data.id"
                        :spread-type="detail.spreadType"
                        :class="$style.reminder"
                        style="flex: 1"
                      />
                      <el-button
                        v-if="totalCount > 0"
                        :class="[
                          $style.export_option,
                          promoteEmployeeData.uncompleteTaskUserCount ? '' : $style.export_bg,
                        ]"
                        :loading="exportLoading"
                        @click="handleExportEmployeeRank(0)"
                      >
                        {{ $t('marketing.commons.dc_55405e') }}
                      </el-button>
                    </div>
                    <div
                      v-if="detail.spreadType == 3"
                      :class="$style.content_tabs__wraper"
                    >
                      <div :class="$style.content_tabs">
                        <span
                          :class="[SmsStatus == 2 ? $style.active : '']"
                          @click="handleChangeSMSStatus(2)"
                        >{{ $t('marketing.commons.fscg_9db9a7') }}({{ detail.actualSenderCount }})</span>
                        <span
                          :class="[SmsStatus == 3 ? $style.active : '']"
                          @click="handleChangeSMSStatus(3)"
                        >{{ $t('marketing.commons.fssb_9ca6a3') }}({{ detail.faillSenderCount }})</span>
                      </div>
                    </div>
                    <!-- 企微群发 -->
                    <div v-if="detail.spreadType == 5">
                      <div
                        :class="$style.filter"
                        class="filter"
                      >
                        <div>
                          <RadioGroup
                            v-if="detail.chatType !== 2"
                            v-model="statusFitler"
                            size="mini"
                            @input="handlePageNumCustomer(1)"
                          >
                            <RadioButton :label="-1">
                              {{ $t('marketing.commons.qbkh_41ae5f') }}
                            </RadioButton>
                            <RadioButton :label="1">
                              {{ $t('marketing.commons.ysd_f87f48') }}
                            </RadioButton>
                            <RadioButton :label="0">
                              {{ $t('marketing.store.wsd_e7b46a') }}
                            </RadioButton>
                            <RadioButton :label="3">
                              {{ $t('marketing.components.market_promotion_details.jsdsx_b2e73d') }}
                            </RadioButton>
                            <RadioButton :label="2">
                              {{ $t('marketing.commons.ybshy_d63dbf') }}
                            </RadioButton>
                          </RadioGroup>
                        </div>

                        <div style="display: flex; align-items: center">
                          <VMemberRangeSelector
                            :options="{ excludeUser: true }"
                            lock="qywx"
                            :title="$t('marketing.commons.xzbm_bb5fee')"
                            @update:memberRangeChange="handleMemberRangeChange"
                          />
                          <el-button
                            type="text"
                            @click="handleExport('customerDetail')"
                          >
                            {{ $t('marketing.commons.dc_55405e') }}
                          </el-button>
                        </div>
                      </div>
                    </div>

                    <FilterDisplay
                      v-if="detail.spreadType === 13"
                      v-model="tableFilterData"
                    />
                    <div :class="$style.content">
                      <div
                        v-if="marketingLoading"
                        class="km-g-loading-mask"
                      >
                        <span class="loading" />
                      </div>
                      <div
                        v-if="detail.spreadType == 2"
                        :class="$style.weChatRadioGroup"
                      >
                        <div
                          :class="[$style.weChatSendRadio, wechatSendStatus === 1 && $style.radioSelected]"
                          @click="weChatSendStatusChange(1)"
                        >
                          {{ `${$t('marketing.commons.fscg_9db9a7')}(${data.actualCompletedCount || 0})` }}
                        </div>
                        <div
                          :class="[$style.weChatSendRadio, wechatSendStatus === 2 && $style.radioSelected]"
                          @click="weChatSendStatusChange(2)"
                        >
                          {{ `${$t('marketing.commons.fssb_9ca6a3')}(${data.failSendCount || 0})` }}
                        </div>
                      </div>
                      <v-table
                        v-loading="showSendDetailLoading"
                        :class="[$style.tablewbg, totalCount === 0 && $style.bottomborder]"
                        :data="tableLists"
                        :columns="columnsForSMS"
                        :row-style="{ height: '50px' }"
                        @custom:user-action="handleShowDetail"
                        @custom:lookup-action="showUserDetailLookup"
                        @custom:forward-action="showUserDetailForward"
                      >
                        <template slot-scope="scope">
                          <template v-if="scope.col.prop === 'weChatUserName'">
                            <div :class="$style.table_image">
                              <img :src="scope.row.wechatAvatar">
                              {{ scope.row.weChatUserName }}
                            </div>
                          </template>
                        </template>
                      </v-table>
                      <div
                        v-if="(detail.spreadType === 1 || detail.spreadType === 13) && totalCount > pageSize"
                        :class="$style.showmore"
                      >
                        <a
                          href="javascript: void(0);"
                          @click.stop="handleShowmore('main')"
                        >{{
                          $t('marketing.commons.ckgd_90ef7c')
                        }}</a>
                      </div>
                      <div
                        v-if="detail.spreadType !== 1 && detail.spreadType !== 13 && totalCount > 5"
                        :class="$style.pagination"
                      >
                        <pagination
                          background
                          :current-page="pageNum"
                          :page-size="pageSize"
                          :pager-count="5"
                          :page-sizes="[5, 10, 20, 30, 40]"
                          layout="prev, pager, next, total, sizes, jumper"
                          :total="totalCount"
                          @size-change="handlePageSizeCustomer"
                          @current-change="handlePageNumCustomer"
                        />
                      </div>
                    </div>
                  </template>
                </el-tab-pane>
                <!-- 只有全员营销才显示 -->
                <el-tab-pane
                  v-if="detail.spreadType == 1 || detail.spreadType == 13"
                  :label="detail.spreadType == 1 ? $t('marketing.components.market_promotion_details.ytgyg_1c6934') : $t('marketing.commons.ytghy_cb66a9')"
                  name="second"
                  :class="$style.unpromote_employee"
                >
                  <div
                    style="display: flex; justify-content: space-between"
                    :class="[$style.title]"
                  >
                    <div v-if="detail.spreadType === 1" style="height: 36px">
                      <VMemberRangeSelector source="marketingPromotion" @update:memberRangeChange="handleMemberRangeChange" />
                    </div>
                    <div
                      v-if="detail.spreadType === 13"
                      style="height: 36px"
                    >
                      <FilterButton
                        v-model="tableFilterData"
                        :btns="['filter']"
                        :object-names="[{ name: $t('marketing.commons.hy_4d9dd5'), value: 'MemberObj' }]"
                      />
                    </div>
                    <el-button
                      v-if="totalCount !== 0"
                      :class="[$style.export_option]"
                      :loading="exportLoading"
                      @click="handleExportEmployeeRank(2)"
                    >
                      {{ $t('marketing.commons.dc_55405e') }}
                    </el-button>
                  </div>
                  <FilterDisplay
                    v-if="detail.spreadType === 13"
                    v-model="tableFilterData"
                  />
                  <div :class="$style.content">
                    <div
                      v-if="marketingLoading"
                      class="km-g-loading-mask"
                    >
                      <span class="loading" />
                    </div>
                    <v-table
                      v-loading="showSendDetailLoading"
                      :class="[$style.tablewbg, totalCount === 0 && $style.bottomborder]"
                      :data="tableLists"
                      :columns="columnsForPromoted"
                      :row-style="{ height: '50px' }"
                      @custom:user-action="handleShowDetail"
                      @custom:lookup-action="showUserDetailLookup"
                      @custom:forward-action="showUserDetailForward"
                    />
                    <div
                      v-if="totalCount > pageSize"
                      :class="$style.showmore"
                    >
                      <a
                        href="javascript: void(0);"
                        @click.stop="handleShowmore('promote')"
                      >{{
                        $t('marketing.commons.ckgd_90ef7c')
                      }}</a>
                    </div>
                  </div>
                </el-tab-pane>
                <el-tab-pane
                  v-if="(detail.spreadType == 1 || detail.spreadType == 13) && data.uuv > 0"
                  :label="detail.spreadType == 1 ? $t('marketing.commons.wtgyg_6f2e48') : $t('marketing.commons.wtghy_10572e')"
                  name="third"
                  :class="$style.unpromote_employee"
                >
                  <div :class="[$style.title, $style.title_promote]">
                    <div v-if="detail.spreadType === 1" style="height: 36px">
                      <VMemberRangeSelector source="marketingPromotion" @update:memberRangeChange="handleMemberRangeChange" />
                    </div>
                    <div
                      v-if="detail.spreadType === 13"
                      style="height: 36px"
                    >
                      <FilterButton
                        v-model="tableFilterData"
                        :btns="['filter']"
                        :object-names="[{ name: $t('marketing.commons.hy_4d9dd5'), value: 'MemberObj' }]"
                      />
                    </div>
                    <!-- 未推广员工提醒 -->
                    <EmployeeReminder
                      :data="promoteEmployeeData"
                      :marketing-activity-id="data.id"
                      :spread-type="detail.spreadType"
                      :show-task-user-name="false"
                      :class="$style.reminder"
                      style="flex: 1"
                    />
                    <el-button
                      v-if="totalCount !== 0"
                      :class="[
                        $style.export_option,
                        promoteEmployeeData.uncompleteTaskUserCount ? '' : $style.export_bg,
                      ]"
                      :loading="exportLoading"
                      @click="handleExportEmployeeRank(1)"
                    >
                      {{ $t('marketing.commons.dc_55405e') }}
                    </el-button>
                  </div>
                  <FilterDisplay
                    v-if="detail.spreadType === 13"
                    v-model="tableFilterData"
                  />
                  <div :class="$style.content">
                    <div
                      v-if="marketingLoading"
                      class="km-g-loading-mask"
                    >
                      <span class="loading" />
                    </div>
                    <v-table
                      v-loading="showSendDetailLoading"
                      :class="[$style.tablewbg, totalCount === 0 && $style.bottomborder]"
                      :data="tableLists"
                      :columns="columnsForNotPromoted"
                      :row-style="{ height: '50px' }"
                      @custom:user-action="handleShowDetail"
                    />
                    <div
                      v-if="totalCount > pageSize"
                      :class="$style.showmore"
                    >
                      <a
                        href="javascript: void(0);"
                        @click.stop="handleShowmore('unpromote')"
                      >{{
                        $t('marketing.commons.ckgd_90ef7c')
                      }}</a>
                    </div>
                  </div>
                </el-tab-pane>
                <el-tab-pane
                  v-if="/1|7|2|3|5|13/.test(detail.spreadType) && leadTotalCount !== 0"
                  :label="$t('marketing.commons.hqxsmx_9718c8')"
                  name="fourth"
                >
                  <template>
                    <div :class="$style.title">
                      <el-button
                        v-if="leadTotalCount !== 0"
                        :class="$style.export_option"
                        :loading="leadExportLoading"
                        @click="handleExportLead"
                      >
                        {{ $t('marketing.commons.dc_55405e') }}
                      </el-button>
                    </div>
                    <el-button
                      type="medium"
                      size="mini"
                      :disabled="!selectedLeadNumber > 0"
                      :loading="saveLoading"
                      @click="saveToClue"
                    >
                      {{ $t('marketing.commons.zxcrxs_f70585') }}
                    </el-button>
                    <div :class="$style.content">
                      <v-table
                        ref="table"
                        :class="[$style.tablewbg, leadTotalCount === 0 && $style.bottomborder]"
                        :data="leadTableLists || []"
                        :columns="leadColumnsLists || []"
                        :row-style="{ cursor: 'pointer' }"
                        @custom:cule-action="handleClueDetail"
                        @click:row="handleClueRow"
                        @selection-change="selectionChange"
                      >
                        <template slot-scope="scope">
                          <template v-if="scope.col.prop === 'weChatUserName'">
                            <div :class="$style.table_image">
                              <img :src="scope.row.wechatAvatar">
                              {{ scope.row.weChatUserName }}
                            </div>
                          </template>
                        </template>
                      </v-table>
                      <div
                        v-if="leadTotalCount >= leadPageSize"
                        :class="$style.showmore"
                      >
                        <a
                          href="javascript: void(0);"
                          @click.stop="handleShowmore('lead')"
                        >{{
                          $t('marketing.commons.ckgd_90ef7c')
                        }}</a>
                      </div>
                    </div>
                  </template>
                </el-tab-pane>
              </el-tabs>
            </div>
          </div>
          <div
            v-if="!loading && !detail.spreadType"
            :class="$style.error_mask"
          >
            {{ $t('marketing.components.market_promotion_details.jzcw_fd08b6') }}
          </div>
        </template>
        <template v-else>
          <AccessTable
            v-if="tableId === 'accessUsers' || tableId === 'forwardUsers'"
            :table-id="tableId"
            :marketing-activity-id="params.id"
            :employee-id="employeeId"
            :type="tableId"
            :need-back="true"
            @onBack="handleCloseTable"
          />
          <EmployeeCustomerTalbe
            v-else-if="tableId === 'customerUsers' || tableId === 'customerGroupUsers'"
            :table-id="tableId"
            :marketing-activity-id="params.id"
            :employee-id="employeeId"
            :type="tableId"
            :status="customerStatus"
            @onBack="handleCloseTable"
          />
          <index-table
            v-else
            ref="$indexTable"
            :promotion-name="detail.title"
            :table-id="tableId"
            :page-origin-data="data"
            @onBack="handleCloseTable"
            @showUserDetail="showUserDetail"
          />
        </template>
        <mate-detail :opts="detailOpts" />
        <Dialog
          :visible.sync="msgPreviewVisible"
          :title="
            detail.spreadType == 2 && detail.wechatMessageType === 1001
              ? $t('marketing.components.market_promotion_details.gjqfxxyl_34ac75')
              : $t('marketing.components.market_promotion_details.mbxxyl_b9e3d3')
          "
          :show-confirm="false"
          :show-cancel="false"
        >
          <div :class="$style.outer_tplmsg_preview">
            <div :class="$style.phone">
              <PhonePreviewBox :title="detail.appName">
                <div :class="[$style.preview_content, $style.show]">
                  <TplmsgItem
                    v-if="detail.spreadType == 2 && detail.wechatMessageType == 1002"
                    :item="wechatTplData"
                  />
                  <WechatmsgItem
                    v-if="detail.spreadType == 2 && detail.wechatMessageType === 1001"
                    :logo="detail.appLogoUrl"
                    :type="detail.msgType"
                    :html="detail.content"
                    :app-id="detail.appId"
                  />
                </div>
              </PhonePreviewBox>
            </div>
          </div>
        </Dialog>
        <!-- 营销用户详情侧滑 -->
        <user-detail
          :top="'0px'"
          :user-id="detailId"
          :visible="detailVisible"
          @close="handleCloseDetail"
        />
      </template>
      <!-- :zIndex="100000"要大于侧滑的z-index -->
      <Dialog
        :title="$t('marketing.commons.zxcrxs_f70585')"
        :visible.sync="saveLoading"
        width="547px"
        class="save-clue-dialog"
        :z-index="100000"
        :modal-append-to-body="false"
        append-to-body
        @onSubmit="toClueConfirm"
        @onClose="saveLoading = false"
      >
        <span class="text">{{ $t('marketing.commons.jhdsxcrsbd_fedc65') }}</span>
        <!-- <div
          slot="footer"
          class="dialog-footer"
        >
          <fx-button
            type="primary"
            size="small"
            @click="toClueConfirm"
          >
            {{ $t('marketing.commons.qd_aa7527') }}
          </fx-button>
          <fx-button
            size="small"
            @click="saveLoading = false"
          >
            {{ $t('marketing.commons.qx_c08ab9') }}
          </fx-button>
        </div> -->
      </Dialog>
    </sideslip-popup>
    <!-- <sideslip-popup
      width="900px"
      :visible="marketingUsersVisible"
      @close="marketingUsersVisible = false"
    >
      <div>test</div>
    </sideslip-popup> -->
  </div>
</template>

<script>
import echarts from 'echarts/lib/echarts'
import 'echarts/lib/chart/funnel'
import { mapState, mapActions, mapMutations } from 'vuex'
import { createArrayFromKeyValue, requireAsync } from '@/utils/index.js'
import { getLinkProperty } from '@/utils/helper.js'
import utils from '@/services/util/index.js'
import http from '@/services/http/index.js'
import VTable from '@/components/table-ex/index.vue'
import SideslipPopup from '../sideslip-popup/index.vue'
import QuestionTooltip from '@/components/questionTooltip/index.vue'
import errState from '@/components/table-ex/err-state.vue'
import tableColumns from './table-config.js'
import UserGroupView from './user-group-view.vue'
import IndexTable from './index_table.vue'
import pmContent from './pm-content.vue'
import mateDetail from '@/components/kitty/mate-detail.vue'
import PhonePreviewBox from '@/components/phone-preview-box/index.vue'
import Dialog from '@/components/dialog/index.vue'
import { wechatMsgTplLists } from '@/pages/wechat/components/tplmsg-dialog.vue'
import TplmsgItem from '@/pages/wechat/components/tplmsg-item.vue'
import WechatmsgItem from '@/pages/wechat/components/wechatmsg-item.vue'
import UserDetail from '@/pages/user/detail.vue'
import EmployeeReminder from './employee-reminder.vue'
import noPremissionTip from '@/components/noPremissionTip/index.vue'
import MailSpreadDetail from '@/pages/mail-marketing/mail-init/components/spread-detail.vue'
import VMemberRangeSelector from '@/pages/report/summary/components/member-range-selector.vue'
import AccessTable from './access-table.vue'
import EmployeeCustomerTalbe from './employee-customer-table.vue'
import { FilterButton, FilterDisplay } from '@/components/table-filter/index.js'

export default {
  components: {
    SideslipPopup,
    VTable,
    Pagination: FxUI.Pagination,
    QuestionTooltip,
    errState,
    UserGroupView,
    IndexTable,
    mateDetail,
    pmContent,
    Dialog,
    PhonePreviewBox,
    TplmsgItem,
    WechatmsgItem,
    UserDetail,
    EmployeeReminder,
    ElButton: FxUI.Button,
    noPremissionTip,
    MailSpreadDetail,
    ElPopover: FxUI.Popover,
    ElTabs: FxUI.Tabs,
    ElTabPane: FxUI.TabPane,
    VMemberRangeSelector,
    AccessTable,
    RadioGroup: FxUI.RadioGroup,
    RadioButton: FxUI.RadioButton,
    EmployeeCustomerTalbe,
    FilterButton,
    FilterDisplay,
  },
  props: {
    // visible: {
    //   type: Boolean,
    //   default: false,
    // },
    params: {
      type: Object,
      default: () => ({}),
    },
    wxAppId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      sending: false,
      wrapperLoading: true,
      detailId: '',
      detailVisible: false,
      exportLoading: false,
      leadExportLoading: false,
      msgPreviewVisible: false,
      headerItems: [],
      detail: {},
      statistics: [],
      tableColumns,
      columnsForSMS: [],
      isShowTable: false,
      tableId: '',
      detailOpts: {},
      selectedLeads: [],
      selectedLeadNumber: 0,
      saveLoading: false,
      showCrmAuthorityTips: false,
      mailVisible: false,
      mailParams: {},
      activeName: 'first',
      wechatSendStatus: 1, // 1: 成功, 2: 失败
      employeeRange: [],
      departmentRange: [],
      objectMarketingActivityStatistics: {},
      employeeId: '',
      statusFitler: -1,
      employeeStatusFitler: -1,
      employeeRangeEmployee: [],
      departmentRangeEmployee: [],
      customerStatus: 0,
      tableFilterData: {
        filters: [],
        tags: []
      },
      // marketingUsersVisible: false
      smsTemplateDetail: {},
    }
  },
  computed: {
    ...mapState('PromotionActivity/Detail', {
      loading: 'loading',
      data: 'data',
      tableLists: 'tableLists',
      totalCount: 'totalCount',
      pageSize: 'pageSize',
      pageNum: 'pageNum',
      SmsStatus: 'SmsStatus',
      marketingLoading: 'marketingLoading',
      leadColumnsLists: 'leadColumnsLists',
      leadTableLists: 'leadTableLists',
      leadTotalCount: 'leadTotalCount',
      leadPageSize: 'leadPageSize',
      leadPageNum: 'leadPageNum',
      qywxColumnsLists: 'qywxColumnsLists',
      qywxTableLists: 'qywxTableLists',
      qywxTotalCount: 'qywxTotalCount',
      qywxPageSize: 'qywxPageSize',
      qywxPageNum: 'qywxPageNum',
      promoteEmployeeData: 'promoteEmployeeData',
      showSendDetailLoading: 'showSendDetailLoading',
      promoteStatue: 'promoteStatue',
    }),
    wechatTplData() {
      // 根据消息模板数据填充真实数据
      const {
        spreadType, wechatMessageType, weChatOfficialTemplateId, templateMessageDatas,
      } = this.detail
      // 生成ID map
      const wechatTplmaps = JSON.parse(JSON.stringify(wechatMsgTplLists)).reduce((obj, item) => {
        obj[item.value] = item
        return obj
      }, {})
      if (spreadType === 2 && wechatMessageType === 1002 && weChatOfficialTemplateId) {
        let tplMsgDatas = {}
        // 转换数据结构
        if (templateMessageDatas && templateMessageDatas.dataList) {
          tplMsgDatas = templateMessageDatas.dataList.reduce((obj, item) => {
            obj[item.key] = {
              ...item,
              title: item.title || '--',
              type: item.key,
            }
            return obj
          }, {})
        }

        // 针对历史数据做兼容
        if (wechatTplmaps[weChatOfficialTemplateId]) {
          // 填充接口数据
          const { label, value, data } = wechatTplmaps[weChatOfficialTemplateId] || {}
          Object.keys(data).forEach(key => {
            data[key].value = (tplMsgDatas && tplMsgDatas[key] && tplMsgDatas[key].value) || ''
            data[key].type = key
          })
          return {
            destTitle: label,
            destTemplateId: value,
            data,
          }
        }
        return {
          destTitle: (templateMessageDatas && templateMessageDatas.title) || '--',
          destTemplateId: weChatOfficialTemplateId,
          data: tplMsgDatas,
        }
      }
      return {}
    },
    columnsForNotPromoted() {
      if (this.detail.contentType === 503) {
        return this.tableColumns['503_1']
      }
      return this.tableColumns[`${this.detail.spreadType}_1`]
    },
    columnsForPromoted() {
      if (this.detail.contentType === 503) {
        return this.tableColumns['503_2']
      }
      return this.tableColumns[`${this.detail.spreadType}_2`]
    },
    sendRangeFilterData() {
      const _filters = this.detail.noticeVisibilityArg.filters || []
      return {
        tags: [],
        filters: [
          {
            query: {
              filters: _filters,
            },
          },
        ],
      }
    },
  },
  watch: {
    tableLists() {
      this.$nextTick(() => {
        this.setTableColumn()
      })
    },
    data(newData) {
      const { spreadType, contentType, wechatMessageType } = newData
      if (newData.headerItems) {
        this.headerItems = []
        let cols = []
        newData.headerItems.forEach((item, index) => {
          cols.push(item)
          if (index % 3 === 2) {
            this.headerItems.push(cols)
            cols = []
          }
        })

        if (cols.length) {
          this.headerItems.push(cols)
        }
      }
      if (newData.marketingEventId) {
        this.marketingEventObjDetail(newData.marketingEventId)
      }

      if (spreadType === 1 || spreadType === 13) {
        // 全员营销
        // const { marketingActivityStatisticResult = {} } = newData; // 这种写法编译后会用 === void 0 ? 判断
        const marketingActivityStatisticResult = newData.marketingActivityStatisticResult || {}
        // this.statistics = createArrayFromKeyValue({
        //   推广员工数: marketingActivityStatisticResult.spreadUserCount || 0, // 员工推广人数 // YES 员工人数值是有的
        //   未推广员工数: newData.uuv || 0,
        //   员工推广人次: marketingActivityStatisticResult.spreadCount || 0, // 员工推广次数
        //   访问人次: marketingActivityStatisticResult.lookUpCount || 0, // 访问次数，不含员工
        //   // 访问人数: marketingActivityStatisticResult.lookUpUserCount || 0, // 访问人数，不含员工 // TODO 暂无人数值，用上面的次数充当人次
        //   转发人次: marketingActivityStatisticResult.forwardCount || 0, // 转发次数，不含员工
        //   // 转发人数: marketingActivityStatisticResult.forwardUserCount || 0, // 转发人数，不含员工 // TODO 暂无人数值，用上面的次数充当人次
        //   获取线索数: newData.leadCount || 0 // 线索累积量
        //   // 客户累积量: marketingActivityStatisticResult.customerAccumulationCount || 0, // 客户累积量
        //   // 营销活动id: marketingActivityStatisticResult.marketingActivityId || 0, // 营销活动id
        // });
        this.statistics = [
          // {
          //   label: $t('marketing.components.market_promotion_details.rwzxfygs_dbe143'),
          //   value:
          //     (marketingActivityStatisticResult.spreadUserCount || 0) +
          //     (newData.uuv || 0),
          //   enableClick:
          //     (marketingActivityStatisticResult.spreadUserCount || 0) +
          //       (newData.uuv || 0) >
          //       0 && true,
          //   clickType: "main"
          // },
          // [
          //   {
          //     label: $t('marketing.components.market_promotion_details.ytg_cccd95'),
          //     value: marketingActivityStatisticResult.spreadUserCount || 0,
          //     enableClick:
          //       marketingActivityStatisticResult.spreadUserCount > 0 && true,
          //     clickType: "promote"
          //   },
          //   {
          //     label: $t('marketing.components.market_promotion_details.wtgygs_4d4541'),
          //     value: newData.uuv || 0,
          //     enableClick: newData.uuv > 0 && true,
          //     clickType: "unpromote"
          //   }
          // ],
          {
            label: spreadType === 1 ? $t('marketing.components.market_promotion_details.ygtgzcs_9b7b32') : $t('marketing.commons.hytgzcs_4c6aee'),
            value: marketingActivityStatisticResult.spreadCount || 0,
          },
          ...(contentType === 503 ? [
            {
              label: $t('marketing.commons.fsrs_8428aa'),
              value: marketingActivityStatisticResult.sendCount || 0,
            },
            {
              label: $t('marketing.commons.sdrs_c1ec4c'),
              value: marketingActivityStatisticResult.receiveCount || 0,
            },
          ] : [
            [
              {
                label: $t('marketing.commons.fwrs_c3c959'),
                clickType: 'accessUsers',
                enableClick: this.objectMarketingActivityStatistics.lookUpUserCount > 0,
                value: this.objectMarketingActivityStatistics.lookUpUserCount || 0,
              },
              {
                label: $t('marketing.commons.fwrc_ed9a0b'),
                value: this.objectMarketingActivityStatistics.lookUpCount || 0,
              },
            ],
            [
              {
                label: $t('marketing.pages.report.zfrs_6fafea'),
                clickType: 'forwardUsers',
                enableClick: this.objectMarketingActivityStatistics.forwardUserCount > 0,
                value: this.objectMarketingActivityStatistics.forwardUserCount || 0,
              },
              {
                label: $t('marketing.commons.zfrc_f9e8f2'),
                value: this.objectMarketingActivityStatistics.forwardCount || 0,
              },
            ],
          ]),
          {
            label: $t('marketing.commons.hqxss_73f905'),
            value: newData.leadCount || 0,
            enableClick: newData.leadCount > 0 && true,
            clickType: 'lead',
          },
        ]
      } else if (spreadType === 7) {
        // 伙伴营销
        const partnerMarketingStatisticResult = newData.partnerMarketingStatisticResult || {}
        // this.statistics = createArrayFromKeyValue({
        //   推广企业数:
        //     partnerMarketingStatisticResult.spreadEnterpriseCount || 0,
        //   对接企业员工推广人次:
        //     partnerMarketingStatisticResult.downStreamEnterpriseSpreadUserCount ||
        //     0,
        //   访问人次: partnerMarketingStatisticResult.lookUpCount || 0,
        //   转发人次: partnerMarketingStatisticResult.forwardCount || 0,
        //   获取线索: newData.leadCount || 0
        // });
        this.statistics = [
          {
            label: $t('marketing.commons.tgqys_de0f80'),
            value: partnerMarketingStatisticResult.spreadEnterpriseCount || 0,
          },
          {
            label: $t('marketing.commons.djqyygtgrc_95146f'),
            value: partnerMarketingStatisticResult.downStreamEnterpriseSpreadUserCount || 0,
          },
          {
            label: $t('marketing.commons.fwrc_ed9a0b'),
            value: partnerMarketingStatisticResult.lookUpCount || 0,
            isShowTips: true,
            tips: $t('marketing.components.market_promotion_details.hbtgdldfwc_3cc946'),
          },
          {
            label: $t('marketing.commons.zfrc_f9e8f2'),
            value: partnerMarketingStatisticResult.forwardCount || 0,
            isShowTips: true,
            tips: $t('marketing.components.market_promotion_details.hbtgdldzfc_1ea59a'),
          },
          {
            label: $t('marketing.commons.hqxss_73f905'),
            value: newData.leadCount || 0,
            isShowTips: true,
            tips: $t('marketing.components.market_promotion_details.tgnrshddbd_710f63'),
          },
        ]
      } else if (spreadType === 2 && wechatMessageType === 1001) {
        // 高级群发
        this.statistics = createArrayFromKeyValue({
          [$t('marketing.commons.fsrs_8428aa')]: newData.needSendCount || 0,
          [$t('marketing.commons.fscg_9db9a7')]: newData.actualCompletedCount || 0,
          [$t('marketing.commons.fssb_9ca6a3')]: newData.failSendCount || 0,
          [$t('marketing.commons.fwcs_f0be64')]: newData.pv || 0,
          [$t('marketing.commons.fwrs_c3c959')]: newData.uv || 0,
          [$t('marketing.commons.hqxss_73f905')]: newData.leadCount || 0,
        })
      } else if (spreadType === 2 && wechatMessageType === 1002) {
        // 模板消息
        this.statistics = createArrayFromKeyValue({
          [$t('marketing.commons.fsrs_8428aa')]: newData.needSendCount || 0,
          [$t('marketing.commons.fscg_9db9a7')]: newData.actualCompletedCount || 0,
          [$t('marketing.commons.fssb_9ca6a3')]: newData.failSendCount || 0,
          [$t('marketing.components.market_promotion_details.ydrs_a6c562')]: newData.readCount || 0,
          [$t('marketing.commons.fwcs_f0be64')]: newData.pv || 0,
          [$t('marketing.commons.fwrs_c3c959')]: newData.uv || 0,
          [$t('marketing.commons.hqxss_73f905')]: newData.leadCount || 0,
        })
      } else if (spreadType === 3) {
        // 短信推广
        this.statistics = createArrayFromKeyValue({
          [$t('marketing.commons.fsrs_8428aa')]: newData.toSenderCount || 0,
          [$t('marketing.commons.fscg_9db9a7')]: newData.actualSenderCount || 0,
          [$t('marketing.commons.fssb_9ca6a3')]: newData.faillSenderCount || 0,
          [$t('marketing.commons.fwcs_f0be64')]: newData.pv || 0,
          [$t('marketing.commons.fwrs_c3c959')]: newData.uv || 0,
          [$t('marketing.commons.hqxss_73f905')]: newData.leadCount || 0,
        })
      } else if (spreadType === 5) {
        // 企业微信推广
        this.statistics = [
          {
            label: $t('marketing.components.market_promotion_details.yfsyg_e9ff5c'),
            value: newData.confirmedEmployeeCount || 0,
            enableClick: newData.chatType !== 2 && newData.confirmedEmployeeCount > 0 && true,
            clickType: 'groupMessageEmployeeSend',
          },
          {
            label: $t('marketing.components.market_promotion_details.wfsyg_0c7eb4'),
            value: newData.unconfirmedEmployeeCount || 0,
            enableClick: newData.chatType !== 2 && newData.unconfirmedEmployeeCount > 0 && true,
            clickType: 'groupMessageEmployeeUndsend',
          },
          {
            label:
              newData.chatType === 2 ? $t('marketing.commons.ysdkhq_b50cf9') : $t('marketing.commons.ysdkh_58ee0c'),
            value: newData.sendedCount || 0,
            enableClick: newData.chatType !== 2 && newData.sendedCount > 0 && true,
            clickType: 'groupMessageCustomerGet',
          },
          {
            label:
              newData.chatType === 2
                ? $t('marketing.components.market_promotion_details.dsdkhq_cbe2d8')
                : $t('marketing.components.market_promotion_details.dsdkh_52c195'),
            value: newData.unsendedCount || 0,
            enableClick: newData.chatType !== 2 && newData.unsendedCount > 0 && true,
            clickType: 'groupMessageCustomerUnget',
          },
          [
            {
              label: $t('marketing.commons.fwrs_c3c959'),
              clickType: 'accessUsers',
              enableClick: this.objectMarketingActivityStatistics.lookUpUserCount > 0,
              value: this.objectMarketingActivityStatistics.lookUpUserCount || 0,
            },
            {
              label: $t('marketing.commons.fwrc_ed9a0b'),
              value: this.objectMarketingActivityStatistics.lookUpCount || 0,
            },
          ],
          [
            {
              label: $t('marketing.pages.report.zfrs_6fafea'),
              clickType: 'forwardUsers',
              enableClick: this.objectMarketingActivityStatistics.forwardUserCount > 0,
              value: this.objectMarketingActivityStatistics.forwardUserCount || 0,
            },
            {
              label: $t('marketing.commons.zfrc_f9e8f2'),
              value: this.objectMarketingActivityStatistics.forwardCount || 0,
            },
          ],
          {
            label: $t('marketing.commons.hqxss_73f905'),
            value: newData.clueCount || 0,
          },
        ]

        // if (newData.chatType === 2) {
        //   this.statistics = createArrayFromKeyValue({
        //     已确认员工: newData.confirmedEmployeeCount || 0,
        //     未确认员工: newData.unconfirmedEmployeeCount || 0,
        //     已发送客户群: newData.sendedCount || 0,
        //     待发送客户群: newData.unsendedCount || 0,
        //     // 访问人数: newData.customerCount,
        //     获取线索数: newData.clueCount || 0
        //   });
        // } else {
        //   this.statistics = createArrayFromKeyValue({
        //     已确认员工: newData.confirmedEmployeeCount || 0,
        //     未确认员工: newData.unconfirmedEmployeeCount || 0,
        //     已发送客户: newData.sendedCount || 0,
        //     待发送客户: newData.unsendedCount || 0,
        //     获取线索数: newData.clueCount || 0
        //   });

        // }
      }
      this.detail = newData
      this.$nextTick(() => {
        this.setTableColumn()
        this.setFunnelChartOption()
      })
    },
    params(val) {
      this.initParams()
      this.queryActivityDetail(val)
      this.getObjectMarketingActivityStatistics()
    },

    wechatSendStatus(newVal) {
      this.setState({
        wechatSendStatus: newVal,
        pageNum: 1,
      })
      this.setTableColumn()
      this.queryDetailWithSpreadType()
    },
    tableFilterData(val) {
      const _filters = val.filters && val.filters.length && val.filters[0].query ? val.filters[0].query.filters : []
      this.queryDetailWithSpreadType({
        filters: _filters || [],
      })
    },
  },
  created() {
    if (this.params.id) {
      this.queryActivityDetail(this.params)
      this.getObjectMarketingActivityStatistics()
    }
  },
  beforeDestroy() {
    if (this.$detail) {
      this.$detail.destroy()
    }
    this.clearState()
    if (this.resizeEventHandler) {
      this.resizeEventHandler()
    }
    if (this.funnelChart) {
      this.funnelChart.dispose()
    }
  },
  methods: {
    ...mapActions('PromotionActivity/Detail', [
      'queryMarketingActivityDetail',
      'queryDetailWithSpreadType',
      'handleChangeSMSStatus',
      'handlePageSize',
      'handlePageNum',
      'destroyed',
      'queryFormUserData',
    ]),
    ...mapMutations('PromotionActivity/Detail', ['setState']),
    /**
     * 是否展示访问转发维度
     */
    isShowAccessForwad() {
      return (
        [1, 3].includes(getLinkProperty(this.data, 'qywxGroupSendMessageVO.qywxGroupSendMessageType'))
        && [4, 6, 13, 26].includes(getLinkProperty(this.data, 'qywxGroupSendMessageDetailResult.contentDetail.objectType'))
      )
    },
    initParams() {
      this.activeName = 'first'
      this.isShowTable = false
      this.statusFitler = -1
      this.employeeStatusFitler = -1
      this.employeeRangeEmployee = []
      this.departmentRangeEmployee = []
    },
    bindWindowResizeEvent() {
      const _handler = () => {
        this.funnelChart.resize()
      }
      window.addEventListener('resize', _handler)
      return () => {
        window.removeEventListener('resize', _handler)
      }
    },
    setFunnelChartOption() {
      if (this.funnelChart) {
        this.funnelChart.dispose()
      }

      // 全员推广才有任务统计
      if (this.data.spreadType !== 1 && this.data.spreadType !== 13) {
        return
      }
      this.$nextTick(() => {
        this.funnelChart = echarts.init(this.$refs.funnelChart, 'light')
        if (!this.resizeEventHandler) {
          this.resizeEventHandler = this.bindWindowResizeEvent()
        }
        const { uuv } = this.data || {}
        const marketingActivityStatisticResult = this.data.marketingActivityStatisticResult || {}
        const option = {
          tooltip: {
            trigger: 'item',
            appendToBody: true,
            formatter: params => `${params.name} : ${params.value - 100}`,
          },
          calculable: true,
          series: [
            {
              name: $t('marketing.components.market_promotion_details.rwtgldt_64dfb3'),
              type: 'funnel',
              width: '40%',
              height: '100%',
              left: '10%',
              top: 15,
              minSize: '3%',
              maxSize: '100%',
              label: {
                color: '#181C25',
                formatter: params => `${params.name}\n${params.value - 100}`,
              },
              // TODO i18n
              sort: 'none',
              data: [
                {
                  value: (
                    this.detail.spreadType === 1
                      ? (marketingActivityStatisticResult.spreadUserCount || 0) + (uuv || 0)
                      : marketingActivityStatisticResult.spreadMemberUserCount || 0)
                      + 100,
                  name: this.detail.spreadType === 1 ? $t('marketing.commons.rwxfzygs_f34fcf') : $t('marketing.commons.rwxfzhys_b76df5'),
                  itemStyle: {
                    color: '#63BAFF',
                  },
                },
                {
                  value: (this.objectMarketingActivityStatistics.lookUpStatusCount || 0) + 100,
                  name: this.detail.spreadType === 1 ? $t('marketing.commons.yckrwygs_274c08') : $t('marketing.commons.yckrwhys_447225'),
                  itemStyle: {
                    color: '#96D256',
                  },
                },
                {
                  value: (marketingActivityStatisticResult.spreadUserCount || 0) + 100,
                  name: this.detail.spreadType === 1 ? $t('marketing.commons.tgygs_4f8e9e') : $t('marketing.commons.tghys_f076a1'),
                  itemStyle: {
                    color: '#FED42D',
                  },
                },
                {
                  value: 0,
                  name: '',
                  itemStyle: {
                    color: '#ffffff',
                    borderWidth: 0,
                    opacity: 0,
                  },
                  label: {
                    show: true,
                    position: 'left',
                  },
                  labelLine: {
                    lineStyle: {
                      opacity: 0,
                    },
                    show: true,
                  },
                  tooltip: {
                    textStyle: {
                      color: 'rgba(0,0,0,0)',
                    },
                    backgroundColor: 'none',
                    extraCssText: 'display: none',
                  },
                },
              ],
            },
          ],
        }
        this.funnelChart.setOption(option)
      })
    },
    async getObjectMarketingActivityStatistics() {
      const res = await http.getObjectMarketingActivityStatistics({
        marketingActivityId: this.params.id,
      })
      if (res.errCode === 0) {
        this.objectMarketingActivityStatistics = res.data || {}
      }
    },
    /**
     * 携带状态过滤的新方法
     */
    handlePageNumCustomer(pageNum) {
      this.setState({
        pageNum,
      })
      this.queryDetailWithSpreadTypeCustomer()
    },
    handlePageSizeCustomer(pageSize) {
      this.setState({
        pageSize,
      })
      this.queryDetailWithSpreadTypeCustomer()
    },
    handlePageNumEmployee(pageNum) {
      this.setState({
        pageNum,
      })
      this.queryDetailWithSpreadTypeEmployee()
    },
    handlePageSizeEmployee(pageSize) {
      this.setState({
        pageSize,
      })
      this.queryDetailWithSpreadTypeEmployee()
    },
    async handleExport(type) {
      let params
      let request
      if (type === 'employeeDetail') {
        params = {
          // employeeRange: this.employeeRangeEmployee,
          departmentIdList: this.departmentRangeEmployee,
          status: this.employeeStatusFitler,
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          marketingActivityId: this.params.id,
        }
        request = 'exportEmployeeQywxGroupSendDetail'
      } else if (type === 'customerDetail') {
        params = {
          // employeeRange: this.employeeRange,
          departmentIdList: this.departmentRange,
          status: this.statusFitler,
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          marketingActivityId: this.params.id,
        }
        request = 'exportQywxMarketingActivityEmployeeRanking'
      }
      params.status = params.status === -1 ? '' : params.status
      const res = await http[request](params)
      if (res.errCode === 0) {
        FxUI.MessageBox.alert($t('marketing.commons.dcsjknxyjz_e02eef'), $t('marketing.commons.ts_02d981'), {
          confirmButtonText: $t('marketing.commons.zdl_ce2695'),
        })
      }
    },
    showUserDetailLookup(item) {
      item.tableId = 'accessUsers'
      this.showUserDetail(item)
    },
    showUserDetailForward(item) {
      item.tableId = 'forwardUsers'
      this.showUserDetail(item)
    },
    showUserDetailUnSendCount(item) {
      item.tableId = 'customerUsers'
      item.fsUserId = item.employeeId
      this.customerStatus = 0
      this.showUserDetail(item)
    },
    showUserDetailSentCount(item) {
      item.tableId = 'customerUsers'
      item.fsUserId = item.employeeId
      this.customerStatus = 1
      this.showUserDetail(item)
    },
    showUserDetailOutOfLimitCount(item) {
      item.tableId = 'customerUsers'
      item.fsUserId = item.employeeId
      this.customerStatus = 3
      this.showUserDetail(item)
    },
    showUserDetailNotFriendRelationCount(item) {
      item.tableId = 'customerUsers'
      item.fsUserId = item.employeeId
      this.customerStatus = 2
      this.showUserDetail(item)
    },
    showUserDetailTotalGroupCount(item) {
      item.tableId = 'customerGroupUsers'
      item.fsUserId = item.employeeId
      this.showUserDetail(item)
    },
    showUserDetail(item) {
      this.employeeId = item.fsUserId
      this.tableId = item.tableId
      this.isShowTable = true
    },
    selectionChange(val) {
      this.selectedLeadNumber = (val && val.length) || 0
      this.selectedLeads = val
    },
    saveToClue() {
      this.saveLoading = true
    },
    toClueConfirm() {
      const ids = this.getIDs()
      http.reImportDataToCrm({ ids }).then(results => {
        if (results && results.errCode === 0) {
          this.saveLoading = false
          FxUI.MessageBox.alert($t('marketing.commons.zxcrknxyjz_9b0350'), $t('marketing.commons.ts_02d981'), {
            confirmButtonText: $t('marketing.commons.zdl_ce2695'),
          })
          this.selectedLeadNumber = 0
          this.selectedLeads = []
          this.$refs.table.clearSelection()
        }
      })
    },
    getIDs() {
      const { selectedLeads } = this
      const ids = []
      selectedLeads.forEach(item => {
        ids.push(item.id)
      })
      return ids
    },
    handleShowDetail(row) {
      setTimeout(() => {
        this.detailId = row.userMarketingAccountId
        if (this.detailId) {
          this.detailVisible = true
        }
      }, 0)
    },
    // 关闭侧滑详情
    handleCloseDetail() {
      this.detailVisible = false
    },
    handleSendNow(item) {
      if (!item.id) return
      this.sending = true
      http
        .immediatelySend({
          id: item.id,
        })
        .then(({ errCode, errMsg }) => {
          this.sending = false
          if (errCode === 0) {
            this.queryActivityDetail(this.params)
          } else {
            FxUI.Message.error(errMsg || $t('marketing.commons.czsbqshzs_fb5ded'))
          }
        })
    },
    handleSendAgain(item) {
      const isMemberPromotion = this.detail.spreadType === 13
      this.$router.push({
        name: isMemberPromotion ? 'promotion-activity-member' : 'promotion-activity-staff',
        params: { type: 'create' },
        query: {
          marketingActivityId: item.marketingActivityId || item.id,
          promotionType: isMemberPromotion ? 'memberActivity' : 'staffActivity',
        },
      })
    },
    handleRevokeSend(item) {
      FxUI.MessageBox.confirm($t('marketing.commons.qdychtgrwm_a0e82b'), $t('marketing.commons.ts_02d981'), {
        type: 'warning',
      }).then(() => {
        http
          .fakeCancelMarketingActivityRevokeSend({
            id: item.marketingActivityId || item.id,
          })
          .then(({ errCode, errMsg }) => {
            if (errCode === 0) {
              FxUI.Message.success($t('marketing.commons.chcg_52bb3c'))
              this.queryActivityDetail(this.params)
            } else {
              FxUI.Message.error(errMsg || $t('marketing.commons.czsbqshzs_fb5ded'))
            }
          })
      })
    },
    handleCancelSend(item) {
      FxUI.MessageBox.confirm($t('marketing.commons.qrqxfsm_43c32c'), $t('marketing.commons.ts_02d981'), {
        type: 'warning',
      }).then(() => {
        http
          .fakeCancelMarketingActivitySend({
            id: item.marketingActivityId || item.id,
          })
          .then(({ errCode, errMsg }) => {
            if (errCode === 0) {
              FxUI.Message.success($t('marketing.commons.qxcg_285f58'))
              this.queryActivityDetail(this.params)
            } else {
              FxUI.Message.error(errMsg || $t('marketing.commons.czsbqshzs_fb5ded'))
            }
          })
      })
    },
    handleClueDetail(row) {
      if (row.saveCrmStatus === 0 || row.saveCrmStatus === 3) {
        if(!row.otherCrmObjectBind) return;
        CRM.api.show_detail({
          apiname: row.otherCrmObjectBind.apiName,
          id: row.otherCrmObjectBind.objectId,
        })
      }
    },
    handleClueRow(row, column, event) {
      // console.log('click clue row', ...arguments)
      this.handleClueDetail(row)
    },
    setTableColumn() {
      const { spreadType, chatType, contentType } = this.detail
      // 发送失败列表，比成功列表多了失败原因
      if (spreadType === 3) {
        const { providerId } = this.smsTemplateDetail
        const smsPlatform = providerId === 'mw' ? '1' : '2'
        this.columnsForSMS = this.tableColumns[`${spreadType}_${this.SmsStatus}_${smsPlatform}`] || []
      } else if (spreadType === 5) {
        if (this.activeName === 'fifth') {
          if (chatType === 2) {
            this.columnsForSMS = this.tableColumns['5_3'] || []
          } else {
            this.columnsForSMS = this.tableColumns['5_2'] || []
          }
        } else if (this.activeName === 'first') {
          if (chatType === 2) {
            this.columnsForSMS = this.tableColumns['5_1'] || []
          } else {
            this.columnsForSMS = this.tableColumns[spreadType] || []
          }
        }
      } else if (spreadType === 2) {
        this.columnsForSMS = this.tableColumns[`2_${this.wechatSendStatus}`]
      } else if (contentType === 503) {
        this.columnsForSMS = this.tableColumns['503'] || []
      } else {
        this.columnsForSMS = this.tableColumns[spreadType] || []
      }
    },
    marketingEventObjDetail(objectDataId) {
      // marketing-calendar/index.js
      if (objectDataId) {
        // 暂不启用此功能
        return
      }
      const MARKETING_STATUS = {
        0: $t('marketing.commons.yjh_c2bf12'),
        1: $t('marketing.commons.jhz_fb852f'),
        2: $t('marketing.commons.yjs_047fab'),
        3: $t('marketing.commons.zz_ff6c6a'),
      }
      http.marketingEventObjDetail({ objectDataId }).then(res => {
        const { data } = res.Value
        if (data._id === this.detail.marketingEventId) {
          this.$set(this.detail, 'marketingEventStatus', MARKETING_STATUS[data.status])
        }
      })
    },
    queryActivityDetail(payload) {
      this.clearState()
      this.showCrmAuthorityTips = false
      this.queryMarketingActivityDetail({
        appId: this.wxAppId,
        id: payload.id,
      }).then(res => {
        this.wrapperLoading = false
        if (res && (res.errCode === '320001401' || res.errCode === '320001400')) {
          // 无此操作的数据权限
          this.showCrmAuthorityTips = true
        }
        if (res && res.data && res.data.spreadType === 6) {
          // 显示邮件营销侧滑
          this.mailVisible = true
          this.mailParams = {
            marketingActivityId: res.data.id,
            id: res.data.mailGroupSendMessageDetailResult && res.data.mailGroupSendMessageDetailResult.taskId,
          }
          this.handleClose()
          return
        }
        this.queryDetailWithSpreadType()
        if (/1|7|2|3|5|13/.test(this.data.spreadType)) {
          this.queryFormUserData()
        }
        if (this.data.spreadType === 3) {
          const { marketingActivityGroupSenderDeatilVO = {} } = res.data
          this.querySmsTemplateDetail(marketingActivityGroupSenderDeatilVO.templateId)
        }
      })
    },
    handleClose() {
      if (this.$refs.$indexTable) {
        this.$refs.$indexTable.handleBack()
      }
      this.detailOpts = {}
      this.$emit('close')
      this.weChatSendStatusChange(1)
    },
    clearState() {
      this.headerItems = []
      this.destroyed()
    },
    // promoteStatue: 0 推广排名（全部任务员工） 1 未推广员工  2 已推广员工
    handleExportEmployeeRank(val) {
      this.exportLoading = true
      const params = {
        marketingActivityId: this.data.id,
        pageSize: 50000,
        pageNum: 1,
        promoteStatue: val,
      }
      utils.exportoFile(
        {
          action:
            this.detail.spreadType === 1 || this.detail.spreadType === 13
              ? 'exportMarketingActivityEmployeeRanking'
              : 'exportMarketingActivityPartnerRanking',
          params,
        },
        () => {
          this.exportLoading = false
        },
      )
    },
    handleExportLead() {
      this.leadExportLoading = true
      const { spreadType } = this.detail
      const isMultipleForm = spreadType === 2 || spreadType === 3
      const params = isMultipleForm
        ? {
          sourceId: this.data.marketingActivityId,
          sourceType: 1, // 查询类型： 0.市场活动 1.营销活动 2.官网来源
        }
        : {
          marketingActivityId: this.data.id,
          type: spreadType !== 5 && spreadType !== 7 ? 2 : spreadType, // 查询类型： 1.物料 2.营销活动 3.表单 4.市场活动 5.企业微信 6.报名来源 7.伙伴推广
        }
      const opts = {
        action: isMultipleForm ? 'exportMultipleFormEnrollsData' : 'exportEnrollsData',
        params,
      }
      utils.exportoFile(opts, () => {
        this.leadExportLoading = false
      })
    },
    handleOpenDetail(data) {
      this.detailOpts = { ...data }
    },
    // 打开市场活动详情
    handleOpenmMrketingDetail(id) {
      requireAsync('crm-components/showdetail/showdetail', Detail => {
        this.$detail = this.$detail || new Detail({ showMask: true })
        setTimeout(() => {
          this.$detail.setApiName('MarketingEventObj')
          this.$detail.show(id)
        }, 1)
      })
    },
    // 前往表格页面
    handleShowmore(id) {
      if (
        [
          'groupMessageEmployeeSend',
          'groupMessageEmployeeUndsend',
          'groupMessageCustomerGet',
          'groupMessageCustomerUnget',
        ].includes(id)
      ) {
        this.doSwitch(id)
        return
      }
      this.employeeId = ''
      this.isShowTable = true
      this.tableId = id
    },
    doSwitch(type) {
      if (type === 'groupMessageEmployeeSend') {
        this.switchEmployee(1)
      } else if (type === 'groupMessageEmployeeUndsend') {
        this.switchEmployee(0)
      } else if (type === 'groupMessageCustomerGet') {
        this.switchCustomer(1)
      } else if (type === 'groupMessageCustomerUnget') {
        this.switchCustomer(0)
      }
    },
    switchEmployee(status) {
      if (this.activeName === 'fifth') {
        this.employeeStatusFitler = status
        this.handlePageNumEmployee(1)
      } else {
        this.activeName = 'fifth'
        setTimeout(() => {
          this.employeeStatusFitler = status
          this.handlePageNumEmployee(1)
        }, 500)
      }
    },
    switchCustomer(status) {
      if (this.activeName === 'first') {
        this.statusFitler = status
        this.handlePageNumCustomer(1)
      } else {
        this.activeName = 'first'
        setTimeout(() => {
          this.statusFitler = status
          this.handlePageNumCustomer(1)
        }, 500)
      }
    },
    handleCloseTable(tableId) {
      if (tableId === 'main') {
        this.activeName = 'first'
      } else if (tableId === 'promote') {
        this.activeName = 'second'
      } else if (tableId === 'unpromote') {
        this.activeName = 'third'
      } else if (['accessUsers', 'forwardUsers'].includes(tableId)) {
        this.activeName = 'first'
      } else if (['customerUsers', 'customerGroupUsers'].includes(tableId)) {
        this.activeName = 'fifth'
      } else {
        this.activeName = 'fourth'
      }
      /**
       * 解决返回后表格数据不刷新问题
       */
      this.handleTabClick({ name: this.activeName })
      /**
       * 重新渲染echart
       */
      this.$nextTick(() => {
        this.setFunnelChartOption()
      })
      this.isShowTable = false
    },
    weChatSendStatusChange(status) {
      this.wechatSendStatus = status
    },
    handleTabClick(tab) {
      this.resetParams()
      if (tab.name === 'first') {
        if (this.detail.spreadType === 5) {
          this.queryDetailWithSpreadType({
            departmentRange: this.departmentRange,
            status: this.statusFitler === -1 ? '' : this.statusFitler,
          })
          return
        }
        this.setState({
          promoteStatue: 0,
        })
      } else if (tab.name === 'second') {
        this.setState({
          promoteStatue: 2,
        })
        this.queryDetailWithSpreadType({
          employeeRange: this.employeeRange,
          departmentRange: this.departmentRange,
        })
        return
      } else if (tab.name === 'third') {
        this.setState({
          promoteStatue: 1,
        })
      } else if (tab.name === 'fifth') {
        this.queryDetailWithSpreadType({
          tabName: 'fifth',
          departmentRange: this.departmentRangeEmployee,
          status: this.employeeStatusFitler === -1 ? '' : this.employeeStatusFitler,
        })
        return
      } else {
        return
      }
      this.queryDetailWithSpreadType()
    },
    resetParams() {
      this.setState({
        pageNum: 1,
      })
      this.departmentRange = []
      this.statusFitler = -1
      this.departmentRangeEmployee = []
      this.employeeStatusFitler = -1
    },
    handleMemberRangeChange(e) {
      const { colleague = [], depart = [] } = e
      const fsUserIds = this.getFsUserIds(colleague)
      this.employeeRange = fsUserIds
      this.departmentRange = depart.map(item => item.id)
      this.handlePageNumCustomer(1)
    },
    handleEmployeeMemberRangeChange(e) {
      const { colleague = [], depart = [] } = e
      const fsUserIds = this.getFsUserIds(colleague)
      this.employeeRangeEmployee = fsUserIds
      this.departmentRangeEmployee = depart.map(item => item.id)
      this.handlePageNumEmployee(1)
    },
    queryDetailWithSpreadTypeEmployee() {
      this.queryDetailWithSpreadType({
        employeeRange: this.employeeRangeEmployee,
        departmentRange: this.departmentRangeEmployee,
        status: this.employeeStatusFitler === -1 ? '' : this.employeeStatusFitler,
        tabName: this.activeName,
      })
    },
    queryDetailWithSpreadTypeCustomer() {
      this.queryDetailWithSpreadType({
        employeeRange: this.employeeRange,
        departmentRange: this.departmentRange,
        status: this.statusFitler === -1 ? '' : this.statusFitler,
      })
    },
    getFsUserIds(selectResult) {
      if (this.addressBookType !== 'ding') {
        return selectResult.map(item => item.id)
      }
      const dingIds = selectResult.map(item => item.id)
      const fsIds = selectResult.filter(item => item.fsUserId).map(item => item.fsUserId)
      // 所选的钉钉用户全部都没有fs虚拟身份
      if (fsIds.length === 0 && dingIds.length > 0) {
        FxUI.Message.error($t('marketing.commons.dqxddqbygj_41d5e1'))
        return false
      }
      // 所选的钉钉用户有部分没有fs虚拟身份
      if (fsIds.length < dingIds.length) {
        FxUI.Message.warning($t('marketing.commons.dqxdbfyghw_07ddb9'))
      }
      return fsIds
    },
    handleShowMoreData(item) {
      if (item.length || !item.enableClick) {
        /* empty */
      } else {
        this.handleShowmore(item.clickType)
      }
    },
    querySmsTemplateDetail(templateId) {
      http.sendGetSmsTemplateDetail({ templateId }).then(res => {
        this.smsTemplateDetail = res.data
      })
    },
  },
}
</script>
<style lang="less" scoped>
.pm-content-popper {
  max-width: 350px;
  span {
    color: #181c25;
    font-size: 13px;
  }
}
.filter-content-popper {
  padding: 0 !important;
}
.market_promotion_details__wrapper {
  .sideslip-content-wrapper {
    background-color: #f2f2f5;
  }
  .el-tabs__nav {
    .el-tabs__item.is-active,
    .el-tabs__item:hover {
      color: #181c25;
    }
  }
}
</style>
<style lang="less" module>
.market_promotion_details {
  .header {
    padding: 19px 12px 21px 12px;
    box-sizing: border-box;
    display: flex;
    flex: 0 0 78px;
    background-color: #ffffff;
    .title_con {
      flex: 1;
    }
    .options {
      padding-right: 30px;
      .option_button {
        width: 88px;
        height: 32px;
        background: #f2f4fb;
        border-radius: 4px;
        font-size: 14px;
        color: #181c25;
        border: 0;
        padding: 0;
      }
    }
    .content_title {
      font-size: 16px;
      line-height: 32px;
      color: #181c25;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .title_label {
      position: relative;
      top: -1px;
      left: 3px;
      padding: 1px 2px;
      font-size: 12px;
      color: #7fc25d;
      border-radius: 3px;
      border: 1px solid #a6d44c;
    }
    .title_desc {
      font-size: 12px;
      color: #91959e;
    }
  }

  .prof_infos {
    padding: 10px 0 5px 0;
  }
  .prof_table {
    width: 100%;
    font-size: 12px;
    color: #181c25;
    line-height: 34px;
    tr {
      vertical-align: top;
    }
    .label {
      width: 123px;
      color: #91959e;
    }
    .value {
      width: 275px;
    }
    .user_group {
      margin-top: 8px;
    }
    .tagwrapper {
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
    .tagitem {
      cursor: pointer;
    }
    .range_content {
      line-height: 24px;
      padding-right: 10px;
      margin-top: 6px;
      margin-left: 0;
    }
    .send_fail_text {
      cursor: pointer;
      color: #f17474;
    }
    .send_fail_icon {
      position: relative;
      top: 3px;
      display: inline-block;
      width: 16px;
      height: 16px;
      background-image: url('../../assets/images/icons/icon-error.png');
      background-size: contain;
    }
  }
  .tplmsg_magnifier_btn {
    font-size: 14px;
    color: #267cff;
    cursor: pointer;
    font-size: 14px;
    color: #267cff;
    cursor: pointer;
    position: absolute;
    right: 15px;
    top: 20px;
  }
  .tplmsg_content {
    padding: 16px;
    background-color: #f9f9f9;
    border-radius: 3px;
    border: 1px solid @border-color-base;
    p {
      font-size: 12px;
      color: @color-title;
      margin-bottom: 10px;
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  .error_mask {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    background: #fff;
  }
  .detail_content {
    padding: 5px 0;
  }
  .export_question {
    position: absolute;
    top: 33px;
    right: 10px;
  }

  .tablewbg {
    width: auto;
    border-top: 1px solid #e9edf5 !important;
    border-left: 1px solid #e9edf5 !important;
    border-width: 1px 1px 0;
    box-sizing: border-box;
    :global {
      .el-table__header {
        tr {
          th {
            background: initial;
          }
        }
      }
    }
    :global {
      .el-table__empty-block {
        min-height: 200px;
      }
    }
    &.bottomborder {
      border-width: 1px;
    }
  }
  .showmore {
    border: 1px solid #e9edf5;
    border-top-width: 0px;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 45px;
    a {
      text-decoration: none;
      cursor: pointer;
    }
  }

  .gird {
    box-shadow: 0px 2px 5px 0px rgb(0 0 0 0.05);
    background-color: #fff;
    border-radius: 2px;
    padding: 0 13px 10px 22px;
    margin: 10px;
    position: relative;
    > .title {
      position: relative;
      font-size: 14px;
      color: #333333;
      padding: 20px 0px 15px 11px;
      &::before {
        content: ' ';
        position: absolute;
        left: 0px;
        top: 23px;
        width: 4px;
        height: 16px;
        background: #ff8000;
      }
    }
    .content {
      padding-bottom: 13px;
      line-height: 16px;
    }
    .weChatRadioGroup {
      display: flex;
      justify-content: flex-end;
      margin-bottom: 12px;
      font-size: 14px;
    }
    .weChatSendRadio {
      cursor: pointer;
      padding: 6px 16px;
      color: #181c25;
      border: 1px solid #dee1e8;
    }
    .weChatSendRadio:first-child {
      border-radius: 4px 0 0 4px;
      border-right: none;
    }
    .weChatSendRadio:last-child {
      border-radius: 0 4px 4px 0;
      border-left: none;
    }
    .radioSelected {
      border: 1px solid #267cff !important;
      color: #267cff;
    }
    .text_content {
      height: 90px;
      background: #f9f9f9;
      padding: 0 16px;
      display: flex;
      align-items: center;
    }
    &.table_gird {
      > .content {
        padding: 0;
        margin-bottom: 10px;
        // border-top: 1px solid #ebeef5;
        position: relative;
      }
      :global {
        .el-table {
          &::before {
            display: none;
          }
          .cell {
            padding-left: 18px;
          }
        }
      }
      .export_option {
        font-size: 12px;
        color: #407fff;
        float: right;
        cursor: pointer;
        margin-top: 5px;
        border: 0;
        padding: 0 5px;
      }
      .content_tabs__wraper {
        display: flex;
        justify-content: end;
      }
      .content_tabs {
        width: 240px;
        display: flex;
        span {
          display: block;
          flex: 1;
          height: 28px;
          line-height: 26px;
          font-size: 12px;
          color: #313334;
          text-align: center;
          box-sizing: border-box;
          border: 1px solid #dddddd;
          cursor: pointer;
          &:first-child {
            border-top-left-radius: 3px;
            border-bottom-left-radius: 3px;
          }
          &:last-child {
            border-top-right-radius: 3px;
            border-bottom-right-radius: 3px;
          }
          &.active {
            color: var(--color-primary06);
            position: relative;
            border-color: var(--color-primary06);
          }
        }
      }
      .title_promote {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .reminder {
          flex: 1;
        }
        .export_option {
          float: none;
          height: 34px;
          background: #fff2e2;
          margin-top: 0;
          border-radius: 0;
        }
        .export_bg {
          background: #fff;
          height: 12px;
          margin-top: 5px;
        }
      }
      .filter {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;

        :global {
          .el-radio-button--mini .el-radio-button__inner {
            font-size: 12px;
          }
        }
      }
    }
  }
  .content {
    padding: 8px 0;
    > .item {
      display: -ms-flexbox;
      display: flex;
      padding: 8px 0;
      .label {
        width: 110px;
        color: #91959e;
      }
      .value {
        flex: 1;
      }
    }
  }
  .statistics_wrapper {
    height: 120px;
    border-radius: 2px;
    margin: 10px;
    display: flex;
    justify-content: space-between;
    .statistics_funnel {
      width: 250px;
      height: 120px;
      margin-right: 10px;
      background-color: #fff;
    }
    .statistics {
      display: flex;
      align-items: center;
      height: 100%;
      flex: 1;
      background-color: #fff;
      position: relative;
      .item {
        flex: 1;
        text-align: center;
        border-left: 1px solid #e9edf5;
        &:first-child {
          border-left: none;
        }
        .sub_item {
          display: flex;
          flex-direction: column;
          .title {
            line-height: 24px;
            display: flex;
            .label {
              display: flex;
              justify-content: center;
              align-items: center;
              .export_question {
                margin-left: 3px;
              }
            }
          }
          .count {
            display: flex;
            align-items: center;
          }
          .line {
            font-size: 16px;
            font-weight: 600;
            margin: 0 3px;
          }
          &:first-child {
            .count {
              justify-content: flex-end;
            }
          }
        }
      }
      .item_wrapper {
        display: flex;
        flex: 1.5;
        justify-content: center;
      }
      .enableClick {
        cursor: pointer;
        .count {
          color: #407fff;
        }
      }
      span {
        font-size: 12px;
        color: #545861;
        text-align: center;
        display: block;
      }
      .count {
        font-size: 20px;
        color: #333333;
        margin-top: 6px;
      }
    }
  }

  .table_image {
    display: flex;
    width: 100%;
    align-items: center;
    font-size: 12px;
    > img {
      width: 24px;
      height: 24px;
      margin-right: 5px;
    }
  }
}

.outer_tplmsg_preview {
  padding-top: 20px;
  padding-bottom: 20px;
  margin: 0 auto;
  width: 324px;
  height: 100%;
  flex-shrink: 0;
  .title {
    font-size: 14px;
    color: #333;
    padding: 0 36px;
    margin-bottom: 15px;
  }
  .phone {
    text-align: center;
  }
  .preview_content {
    margin: 32px 14px;
    opacity: 0;
    transform: translate(0, -10px);
    transition: all 0.3s ease-in;
    &.show {
      opacity: 1;
      transform: translate(0, 0);
    }
  }
}
.pagination {
  padding: 10px 14px !important;
}
</style>
<style lang="less" scoped>
.filter {
  ::v-deep .el-radio-button__orig-radio + .el-radio-button__inner {
    &:hover {
      color: #ff8000;
    }
  }
  ::v-deep .el-radio-button__orig-radio:checked + .el-radio-button__inner {
    background-color: #ff8000;
    border-color: #ff8000;
    box-shadow: -1px 0 0 0 #ff8000;
    &:hover {
      color: #fff;
    }
  }
}
</style>
