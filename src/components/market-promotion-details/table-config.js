import { formatStringByEmpty } from '@/utils'

const formatter = (rowData, column, cellValue) => formatStringByEmpty(cellValue)
const formaterZero = (rowData, column, cellValue) => formatStringByEmpty(cellValue, 0)

export default {
  1: [
    {
      prop: 'rank',
      label: $t('marketing.commons.pm_a4dc00'),
      width: 66,
      formatter,
    },
    // { prop: 'fsUserId', label: '员工ID' }, // 员工ID
    {
      prop: 'name',
      label: $t('marketing.commons.yg_2ed392'),
      minWidth: 95,
      formatter,
    }, // 姓名
    {
      prop: 'department',
      label: $t('marketing.commons.bm_1e1459'),
      minWidth: 125,
      formatter,
    }, // 姓名
    {
      prop: 'spreadCount',
      label: $t('marketing.commons.tgcs_6848f9'),
      minWidth: 105,
      formatter: formaterZero,
    }, // 推广次数
    {
      prop: 'lookUpUserCountClick',
      label: $t('marketing.commons.fwrs_c3c959'),
      minWidth: 105,
      formatter: formaterZero,
      exComponent: 'operation',
    },
    {
      prop: 'lookUpCount',
      label: $t('marketing.commons.fwrc_ed9a0b'),
      minWidth: 105,
      formatter: formaterZero,
    }, // 访问次数
    // { prop: 'lookUpUserCount', label: '访问人数' }, // 访问人数 // TODO 暂无人数值，用上面的次数充当人次
    // { prop: 'forwardUserCount', label: '转发人数', minWidth: 105, formatter }, // 转发人数 // TODO 暂无人数值，此列不显示
    {
      prop: 'forwardUserCountClick',
      label: $t('marketing.pages.report.zfrs_6fafea'),
      minWidth: 105,
      formatter: formaterZero,
      exComponent: 'operation',
    },
    {
      prop: 'forwardCount',
      label: $t('marketing.commons.zfcs_745ca0'),
      minWidth: 105,
      formatter: formaterZero,
    }, // 转发次数 // TODO 暂无人数值，用次数充当人次
    {
      prop: 'leadAccumulationCount',
      label: $t('marketing.commons.hqxss_73f905'),
      minWidth: 105,
      formatter: formaterZero,
    }, // 获取线索数
  ],
  13: [
    {
      prop: 'rank',
      label: $t('marketing.commons.pm_a4dc00'),
      width: 66,
      formatter,
    },
    {
      prop: 'name',
      label: $t('marketing.commons.hy_4d9dd5'),
      minWidth: 95,
      formatter,
    }, // 姓名
    {
      prop: 'mobile',
      label: $t('marketing.commons.sjh_8098e2'),
      minWidth: 125,
      formatter,
    }, // 姓名
    {
      prop: 'spreadCount',
      label: $t('marketing.commons.tgcs_6848f9'),
      minWidth: 105,
      formatter: formaterZero,
    }, // 推广次数
    {
      prop: 'lookUpUserCountClick',
      label: $t('marketing.commons.fwrs_c3c959'),
      minWidth: 105,
      formatter: formaterZero,
      exComponent: 'operation',
    },
    {
      prop: 'lookUpCount',
      label: $t('marketing.commons.fwrc_ed9a0b'),
      minWidth: 105,
      formatter: formaterZero,
    }, // 访问次数
    // { prop: 'lookUpUserCount', label: '访问人数' }, // 访问人数 // TODO 暂无人数值，用上面的次数充当人次
    // { prop: 'forwardUserCount', label: '转发人数', minWidth: 105, formatter }, // 转发人数 // TODO 暂无人数值，此列不显示
    {
      prop: 'forwardUserCountClick',
      label: $t('marketing.pages.report.zfrs_6fafea'),
      minWidth: 105,
      formatter: formaterZero,
      exComponent: 'operation',
    },
    {
      prop: 'forwardCount',
      label: $t('marketing.commons.zfcs_745ca0'),
      minWidth: 105,
      formatter: formaterZero,
    }, // 转发次数 // TODO 暂无人数值，用次数充当人次
    {
      prop: 'leadAccumulationCount',
      label: $t('marketing.commons.hqxss_73f905'),
      minWidth: 105,
      formatter: formaterZero,
    }, // 获取线索数
  ],
  // 未推广的员工
  '1_1': [
    {
      prop: 'name',
      label: $t('marketing.commons.yg_2ed392'),
      minWidth: 95,
      formatter,
    },
    {
      prop: 'department',
      label: $t('marketing.commons.bm_1e1459'),
      minWidth: 125,
      formatter,
    },
    // TODO i18n
    {
      prop: 'lookUpStatus',
      label: $t('marketing.components.market_promotion_details.sfckrw_a9ebb4'),
      minWidth: 125,
      formatter: row => (row.lookUpStatus ? $t('marketing.commons.s_0a60ac') : $t('marketing.commons.f_c9744f')),
    },
  ],
  // 未推广的会员
  '13_1': [
    {
      prop: 'name',
      label: $t('marketing.commons.yg_2ed392'),
      minWidth: 95,
      formatter,
    },
    {
      prop: 'mobile',
      label: $t('marketing.commons.sjh_8098e2'),
      minWidth: 125,
      formatter,
    },
    {
      prop: 'lookUpStatus',
      label: $t('marketing.components.market_promotion_details.sfckrw_a9ebb4'),
      minWidth: 125,
      formatter: row => (row.lookUpStatus ? $t('marketing.commons.s_0a60ac') : $t('marketing.commons.f_c9744f')),
    },
  ],
  // 已推广
  '1_2': [
    // { prop: 'fsUserId', label: '员工ID' }, // 员工ID
    {
      prop: 'name',
      label: $t('marketing.commons.yg_2ed392'),
      minWidth: 95,
      formatter,
    }, // 姓名
    {
      prop: 'department',
      label: $t('marketing.commons.bm_1e1459'),
      minWidth: 125,
      formatter,
    }, // 姓名
    {
      prop: 'spreadCount',
      label: $t('marketing.commons.tgcs_6848f9'),
      minWidth: 105,
      formatter: formaterZero,
    }, // 推广次数
    {
      prop: 'lookUpUserCountClick',
      label: $t('marketing.commons.fwrs_c3c959'),
      minWidth: 105,
      formatter: formaterZero,
      exComponent: 'operation',
    },
    {
      prop: 'lookUpCount',
      label: $t('marketing.commons.fwrc_ed9a0b'),
      minWidth: 105,
      formatter: formaterZero,
    }, // 访问次数
    // { prop: 'lookUpUserCount', label: '访问人数' }, // 访问人数 // TODO 暂无人数值，用上面的次数充当人次
    // { prop: 'forwardUserCount', label: '转发人数', minWidth: 105, formatter }, // 转发人数 // TODO 暂无人数值，此列不显示
    {
      prop: 'forwardUserCountClick',
      label: $t('marketing.pages.report.zfrs_6fafea'),
      minWidth: 105,
      formatter: formaterZero,
      exComponent: 'operation',
    },
    {
      prop: 'forwardCount',
      label: $t('marketing.commons.zfcs_745ca0'),
      minWidth: 105,
      formatter: formaterZero,
    }, // 转发次数 // TODO 暂无人数值，用次数充当人次
    {
      prop: 'leadAccumulationCount',
      label: $t('marketing.commons.hqxss_73f905'),
      minWidth: 105,
      formatter: formaterZero,
    }, // 获取线索数
  ],
  // 已推广
  '13_2': [
    // { prop: 'fsUserId', label: '员工ID' }, // 员工ID
    {
      prop: 'name',
      label: $t('marketing.commons.yg_2ed392'),
      minWidth: 95,
      formatter,
    }, // 姓名
    {
      prop: 'mobile',
      label: $t('marketing.commons.sjh_8098e2'),
      minWidth: 125,
      formatter,
    }, // 姓名
    {
      prop: 'spreadCount',
      label: $t('marketing.commons.tgcs_6848f9'),
      minWidth: 105,
      formatter: formaterZero,
    }, // 推广次数
    {
      prop: 'lookUpUserCountClick',
      label: $t('marketing.commons.fwrs_c3c959'),
      minWidth: 105,
      formatter: formaterZero,
      exComponent: 'operation',
    },
    {
      prop: 'lookUpCount',
      label: $t('marketing.commons.fwrc_ed9a0b'),
      minWidth: 105,
      formatter: formaterZero,
    }, // 访问次数
    // { prop: 'lookUpUserCount', label: '访问人数' }, // 访问人数 // TODO 暂无人数值，用上面的次数充当人次
    // { prop: 'forwardUserCount', label: '转发人数', minWidth: 105, formatter }, // 转发人数 // TODO 暂无人数值，此列不显示
    {
      prop: 'forwardUserCountClick',
      label: $t('marketing.pages.report.zfrs_6fafea'),
      minWidth: 105,
      formatter: formaterZero,
      exComponent: 'operation',
    },
    {
      prop: 'forwardCount',
      label: $t('marketing.commons.zfcs_745ca0'),
      minWidth: 105,
      formatter: formaterZero,
    }, // 转发次数 // TODO 暂无人数值，用次数充当人次
    {
      prop: 'leadAccumulationCount',
      label: $t('marketing.commons.hqxss_73f905'),
      minWidth: 105,
      formatter: formaterZero,
    }, // 获取线索数
  ],
  '2_1': [{
    prop: 'weChatUserName',
    label: $t('marketing.commons.wxyh_055389'),
    width: 200,
    exComponent: 'custom',
  },
  // {
  //   prop: "crmCustomerName",
  //   label: "关联客户",
  //   minWidth: 60,
  //   formatter
  // },
  // {
  //   prop: "crmContactName",
  //   label: "关联联系人",
  //   minWidth: 60,
  //   formatter
  // },
  {
    prop: 'sentTime',
    label: $t('marketing.commons.fssj_63b34d'),
    minWidth: 142,
    formatter,
  },
  ],
  '2_2': [{
    prop: 'weChatUserName',
    label: $t('marketing.commons.wxyh_055389'),
    width: 200,
    exComponent: 'custom',
  },
  {
    prop: 'sentTime',
    label: $t('marketing.commons.fssj_63b34d'),
    minWidth: 142,
    formatter,
  },
  {
    prop: 'status',
    label: $t('marketing.commons.sbyy_13d5f2'),
    minWidth: 200,
    formatter,
  },
  ],
  // 模板消息
  4: [
    {
      prop: 'weChatUserName',
      label: $t('marketing.commons.wxyh_055389'),
      width: 200,
      exComponent: 'custom',
    },
    {
      prop: 'crmCustomerName',
      label: $t('marketing.commons.glkh_52f15c'),
      minWidth: 60,
      formatter,
    },
    {
      prop: 'crmContactName',
      label: $t('marketing.components.market_promotion_details.gllxr_ff0488'),
      minWidth: 60,
      formatter,
    },
    {
      prop: 'sentTime',
      label: $t('marketing.commons.fssj_63b34d'),
      minWidth: 142,
      formatter,
    },
  ],
  3: [
    {
      prop: 'phone',
      label: $t('marketing.commons.sxr_6e71bd'),
      width: 165,
      formatter,
      exComponent: 'operation',
    },
    // 短信推广详情不显示 状态 列
    // {
    //   prop: 'statusText',
    //   label: '状态',
    //   minWidth: 165,
    //   exComponent: 'errState',
    // },
    {
      prop: 'spendSMSCount',
      label: $t('marketing.commons.xhdxs_443f1c'),
      minWidth: 142,
      formatter,
    },
    {
      prop: 'arriveTime',
      label: $t('marketing.commons.fssj_63b34d'),
      width: 200,
      formatter,
    },
  ],
  '3_2_1': [
    // 短信推广详情，发送成功列表
    {
      prop: 'phone',
      label: $t('marketing.commons.sxr_6e71bd'),
      width: 165,
      formatter,
      exComponent: 'operation',
    },
    {
      prop: 'spendSMSCount',
      label: $t('marketing.commons.xhdxs_443f1c'),
      minWidth: 142,
      formatter,
    },
    {
      prop: 'arriveTime',
      label: $t('marketing.commons.fssj_63b34d'),
      width: 200,
      formatter,
    },
    {
      prop: 'lookUpCount',
      label: $t('marketing.commons.fwcs_f0be64'),
      minWidth: 142,
      formatter,
    },
    {
      prop: 'clueNum',
      label: $t('marketing.commons.hqxss_73f905'),
      minWidth: 142,
      formatter,
    },
  ],
  '3_2_2': [
    // 其他短信平台  不展示消耗短信
    {
      prop: 'phone',
      label: $t('marketing.commons.sxr_6e71bd'),
      width: 165,
      formatter,
      exComponent: 'operation',
    },
    {
      prop: 'arriveTime',
      label: $t('marketing.commons.fssj_63b34d'),
      width: 200,
      formatter,
    },
    {
      prop: 'lookUpCount',
      label: $t('marketing.commons.fwcs_f0be64'),
      minWidth: 142,
      formatter,
    },
    {
      prop: 'clueNum',
      label: $t('marketing.commons.hqxss_73f905'),
      minWidth: 142,
      formatter,
    },
  ],
  '3_3_1': [
    {
      prop: 'phone',
      label: $t('marketing.commons.sxr_6e71bd'),
      width: 165,
      exComponent: 'operation',
    },
    {
      prop: 'spendSMSCount',
      label: $t('marketing.commons.xhdxs_443f1c'),
      minWidth: 142,
      formatter,
    },
    // 发送失败列表，比成功列表多了失败原因
    {
      prop: 'reply',
      label: $t('marketing.commons.sbyy_13d5f2'),
      minWidth: 142,
      formatter,
    },
    {
      prop: 'arriveTime',
      label: $t('marketing.commons.fssj_63b34d'),
      width: 200,
      formatter,
    },
  ],
  '3_3_2': [
    {
      prop: 'phone',
      label: $t('marketing.commons.sxr_6e71bd'),
      exComponent: 'operation',
    },
    {
      prop: 'reply',
      label: $t('marketing.commons.sbyy_13d5f2'),
      formatter,
    },
    {
      prop: 'arriveTime',
      label: $t('marketing.commons.fssj_63b34d'),
      formatter,
    },
  ],
  // 'leadDetail': [
  //   { prop: 'rank', label: '排名', width: 66, formatter },
  //   // { prop: 'fsUserId', label: '员工ID' }, // 员工ID
  //   { prop: 'name', label: '员工', minWidth: 95, formatter }, // 姓名
  //   { prop: 'spreadCount', label: '推广次数', minWidth: 105, formatter }, // 推广次数
  //   { prop: 'lookUpCount', label: '访问人次', minWidth: 105, formatter }, // 访问次数
  //   // { prop: 'lookUpUserCount', label: '访问人数' }, // 访问人数 // TODO 暂无人数值，用上面的次数充当人次
  //   // { prop: 'forwardUserCount', label: '转发人数', minWidth: 105, formatter }, // 转发人数 // TODO 暂无人数值，此列不显示
  //   { prop: 'forwardCount', label: '转发次数', minWidth: 105, formatter }, // 转发次数 // TODO 暂无人数值，用次数充当人次
  //   { prop: 'leadAccumulationCount', label: '获取线索数', minWidth: 105, formatter }, // 获取线索数
  // ],
  5: [
    {
      prop: 'customerName',
      label: $t('marketing.commons.wxnc_285f2c'),
      width: 165,
      formatter,
    },
    {
      prop: 'statusText',
      label: $t('marketing.commons.fszt_d48fc2'),
      minWidth: 165,
      exComponent: 'errState',
      formatter,
    },
    {
      prop: 'sendTime',
      label: $t('marketing.commons.sdsj_0fc40e'),
      minWidth: 165,
    },
    {
      prop: 'employeeName',
      label: $t('marketing.commons.fzyg_6ce54b'),
      minWidth: 142,
      formatter,
    },
    {
      prop: 'department',
      label: $t('marketing.commons.bm_1e1459'),
      minWidth: 165,
    },
  ],
  '5_1': [
    {
      prop: 'customerName',
      label: $t('marketing.commons.khq_8ba76f'),
      width: 165,
      formatter,
    },
    {
      prop: 'statusText',
      label: $t('marketing.components.market_promotion_details.sdzt_aecb9b'),
      minWidth: 165,
      exComponent: 'errState',
      formatter,
    },
    {
      prop: 'sendTime',
      label: $t('marketing.commons.sdsj_0fc40e'),
      minWidth: 165,
    },
    {
      prop: 'employeeName',
      label: $t('marketing.commons.fzyg_6ce54b'),
      minWidth: 142,
      formatter,
    },
    {
      prop: 'department',
      label: $t('marketing.commons.bm_1e1459'),
      minWidth: 165,
    },
  ],
  '5_2': [
    {
      prop: 'employeeName',
      label: $t('marketing.commons.yg_2ed392'),
      width: 120,
      formatter,
    },
    {
      prop: 'department',
      label: $t('marketing.commons.bm_1e1459'),
      minWidth: 100,
    },
    {
      prop: 'employeeStatus',
      label: $t('marketing.commons.fszt_d48fc2'),
      minWidth: 100,
      formatter,
    },
    {
      prop: 'sendTime',
      label: $t('marketing.commons.fssj_63b34d'),
      minWidth: 150,
    },
    {
      prop: 'unSendCountClick',
      label: $t('marketing.components.market_promotion_details.wsdkh_12adac'),
      minWidth: 100,
      exComponent: 'operation',
    },
    {
      prop: 'sentCountClick',
      label: $t('marketing.commons.ysdkh_58ee0c'),
      minWidth: 100,
      exComponent: 'operation',
    },
    {
      prop: 'outOfLimitCountClick',
      label: $t('marketing.components.market_promotion_details.jsdsx_b2e73d'),
      minWidth: 100,
      exComponent: 'operation',
    },
    {
      prop: 'notFriendRelationCountClick',
      label: $t('marketing.components.market_promotion_details.bshygx_2680c6'),
      minWidth: 120,
      exComponent: 'operation',
    },
  ],
  '5_3': [
    {
      prop: 'employeeName',
      label: $t('marketing.commons.yg_2ed392'),
      width: 120,
      formatter,
    },
    {
      prop: 'department',
      label: $t('marketing.commons.bm_1e1459'),
      minWidth: 100,
    },
    {
      prop: 'employeeStatus',
      label: $t('marketing.commons.fszt_d48fc2'),
      minWidth: 100,
    },
    {
      prop: 'sendTime',
      label: $t('marketing.commons.fssj_63b34d'),
      minWidth: 120,
    },
    {
      prop: 'successGroupCountClick',
      label: $t('marketing.commons.ysdkhq_b50cf9'),
      minWidth: 100,
      exComponent: 'operation',
    },
  ],
  7: [{
    prop: 'rank',
    label: $t('marketing.commons.pm_a4dc00'),
    width: 66,
    formatter,
  },
  // { prop: 'fsUserId', label: '员工ID' }, // 员工ID
  {
    prop: 'name',
    label: $t('marketing.components.market_promotion_details.hldjqy_2c7090'),
    minWidth: 95,
    formatter,
  }, // 姓名
  {
    prop: 'outTenantSpreadEmployee',
    label: $t('marketing.commons.cytgygs_db795a'),
    minWidth: 125,
    formatter,
  }, // 姓名
  {
    prop: 'spreadCount',
    label: $t('marketing.commons.tgcs_6848f9'),
    minWidth: 105,
    formatter,
  }, // 推广次数
  {
    prop: 'lookUpCount',
    label: $t('marketing.commons.fwrc_ed9a0b'),
    minWidth: 105,
    formatter,
  },
  {
    prop: 'forwardCount',
    label: $t('marketing.commons.zfcs_745ca0'),
    minWidth: 105,
    formatter,
  }, // 转发次数 // TODO 暂无人数值，用次数充当人次
  {
    prop: 'leadAccumulationCount',
    label: $t('marketing.commons.hqxss_73f905'),
    minWidth: 105,
    formatter,
  }, // 获取线索数
  ],
  503: [
    {
      prop: 'rank',
      label: $t('marketing.commons.pm_a4dc00'),
      width: 66,
      formatter,
    },
    {
      prop: 'name',
      label: $t('marketing.commons.yg_2ed392'),
      minWidth: 95,
      formatter,
    }, // 姓名
    {
      prop: 'department',
      label: $t('marketing.commons.bm_1e1459'),
      minWidth: 125,
      formatter,
    }, // 姓名
    {
      prop: 'spreadCount',
      label: $t('marketing.commons.tgcs_6848f9'),
      minWidth: 105,
      formatter: formaterZero,
    }, // 推广次数
    {
      prop: 'sendCount',
      label: $t('marketing.commons.fsrs_8428aa'),
      minWidth: 105,
      formatter: formaterZero,
    }, // 发送人数
    {
      prop: 'receiveCount',
      label: $t('marketing.commons.sdrs_c1ec4c'),
      minWidth: 105,
      formatter: formaterZero,
    }, // 送达人数
    {
      prop: 'leadAccumulationCount',
      label: $t('marketing.commons.hqxss_73f905'),
      minWidth: 105,
      formatter: formaterZero,
    }, // 获取线索数
  ],
  '503_1': [
    {
      prop: 'name',
      label: $t('marketing.commons.yg_2ed392'),
      minWidth: 95,
      formatter,
    },
    {
      prop: 'department',
      label: $t('marketing.commons.bm_1e1459'),
      minWidth: 125,
      formatter,
    },
    {
      prop: 'lookUpStatus',
      label: $t('marketing.components.market_promotion_details.sfckrw_a9ebb4'),
      minWidth: 125,
      formatter: row => (row.lookUpStatus ? $t('marketing.commons.s_0a60ac') : $t('marketing.commons.f_c9744f')),
    },
  ],
  '503_2': [
    {
      prop: 'name',
      label: $t('marketing.commons.yg_2ed392'),
      minWidth: 95,
      formatter,
    }, // 姓名
    {
      prop: 'department',
      label: $t('marketing.commons.bm_1e1459'),
      minWidth: 125,
      formatter,
    }, // 部门
    {
      prop: 'spreadCount',
      label: $t('marketing.commons.tgcs_6848f9'),
      minWidth: 105,
      formatter: formaterZero,
    }, // 推广次数
    {
      prop: 'sendCount',
      label: $t('marketing.commons.fsrs_8428aa'),
      minWidth: 105,
      formatter: formaterZero,
    }, // 发送人数
    {
      prop: 'receiveCount',
      label: $t('marketing.commons.sdrs_c1ec4c'),
      minWidth: 105,
      formatter: formaterZero,
    }, // 送达人数
    {
      prop: 'leadAccumulationCount',
      label: $t('marketing.commons.hqxss_73f905'),
      minWidth: 105,
      formatter: formaterZero,
    }, // 获取线索数
  ],
}
