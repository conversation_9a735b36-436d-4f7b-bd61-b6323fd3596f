<template>
  <div :class="$style.pmContent">
    <!-- 全员推广/伙伴营销内容 -->
    <template v-if="detail.spreadType == 1 || detail.spreadType == 7 || detail.spreadType == 13">
      <!-- 全员推广多图类型 -->
      <template v-if="(detail.spreadType == 1 || detail.spreadType == 13) && detail.materialInfoList && detail.materialInfoList.length">
        <div
          v-for="item in detail.materialInfoList"
          :key="item.objectId"
          :class="$style.image"
          :style="`background-image: url(${item.coverUrl});`"
        />
      </template>
      <template v-else>
        <div
          v-if="detail.contentDetail.image"
          :class="$style.image"
          :style="`background-image: url(${detail.contentDetail.image});`"
        />
        <div
          v-else
          :class="$style.image"
          :style="`background-image: url(${ detail.contentType === 503 ? mailCoverImg : require('@/assets/images/icons/text-icon.jpg')});`"
        />
      </template>
      <div :class="$style.descs">
        {{ detail.contentDetail.title }}
      </div>
    </template>
    <!-- 公众号推广内容 -->
    <template v-if="detail.spreadType == 2">
      <!-- 公众号任务消息支持多条推广内容 -->
      <template v-if="detail.vmsgDetail.msgType == 4">
        <div :class="$style.itemw">
          <a
            v-for="(item, index) in vmsgGraphicList"
            :key="index"
            :class="$style.item"
            :href="item.url"
            target="_blank"
          >
            <div
              v-if="item.thumbUrl"
              :class="$style.image"
              :style="`background-image: url(${item.thumbUrl};`"
            />
            <div
              v-else
              :class="$style.image"
              :style="
                `background-image: url(${require('@/assets/images/icons/text-icon.jpg')});`
              "
            />
            <div :class="$style.descs">
              <div :class="$style.text">{{ item.title }}</div>
            </div>
          </a>
        </div>
      </template>
      <template v-else>
        <div
          v-if="detail.vmsgDetail.image"
          :class="$style.image"
          :style="`background-image: url(${detail.vmsgDetail.image});`"
        />
        <div
          v-else
          :class="$style.image"
          :style="
            `background-image: url(${require('@/assets/images/icons/text-icon.jpg')});`
          "
        />
        <div
          v-if="detail.vmsgDetail.msgType === 3"
          :class="$style.descs"
        >
          {{ detail.vmsgDetail.text }}
        </div>
        <div
          v-else
          :class="$style.descs"
          @click="handleDetailClick"
          v-html="detail.vmsgDetail.text"
        />
      </template>
    </template>
    <!-- 短信推广内容 -->
    <template v-if="detail.spreadType == 3">
      <div
        :class="$style.image"
        :style="
          `background-image: url(${require('@/assets/images/icons/text-icon.jpg')});`
        "
      />
      <div
        :class="$style.descs"
        v-html="detail.htmlContent"
      />
    </template>
    <!-- 企业微信群发消息推广内容 -->
    <template v-if="detail.spreadType == 5 && detail.qywxGroupSendMessageVO">
      <PreviewDialog
        :qywxAttachmentsVO="detail.qywxGroupSendMessageVO.qywxAttachmentsVO"
        :textContent="detail.qywxGroupSendMessageVO.text ? detail.qywxGroupSendMessageVO.text.content : ''"
      />
    </template>
    <!-- 邮件推广内容 -->
    <template v-if="detail.spreadType == 6">
      <div
        :class="$style.image"
        :style="
          `background-image: url(${require('@/assets/images/icons/mail-default-icon.png')});`
        "
      />
      <div :class="$style.descs">
        <p class="km-t-ellipsis1">
          {{ $t('marketing.commons.yjbt_1f2c85') }}{{ detail.title || "--" }}
        </p>
        <p>{{ $t('marketing.commons.fjr_2df6a3') }}{{ detail.address || "--" }}</p>
      </div>
    </template>
  </div>
</template>

<script>
import http from '@/services/http/index'
import vmsgUtil from '@/utils/vmsg-util'
import PreviewDialog from "@/components/msg-contents/preview-dialog.vue";
import mailCoverImg from '@/assets/images/mail/mail-cover.png'

export default {
  components: {
    PreviewDialog,
  },
  props: {
    detail: {},
  },
  data() {
    return {
      vmsgGraphicList: [],
      showMsgContentDialog: false,
      mailCoverImg,
    }
  },
  computed: {},
  watch: {
    detail() {
      this.checkContent()
    },
  },
  created() {
    this.checkContent()
  },
  methods: {
    queryEmailDetailById() {
      const { sender, address } = this.detail || {}
      if (sender && !address) {
        http
          .getEmailSendReplyDataById({ id: sender })
          .then(({ errCode, data = {} }) => {
            if (errCode === 0) {
              // this.data.email.address = data.address;
              this.$set(this.detail, 'address', data.address)
            }
          })
      }
    },
    checkContent() {
      const { spreadType } = this.detail
      if (spreadType === 3) {
        // 短信消息
        let content = this.detail.content
          .replace(/</g, '&lt;')
          .replace(/>/g, '&gt;')
          .replace(/"/g, '&quot;')
        content = content.replace(/(https?:\/\/.+?)(\s|$)/g, (m, url) => ` <a href="${url}" target="_blank">${url}</a> `)
        this.$set(this.detail, 'htmlContent', content)
      } else if (spreadType === 6) {
        // 邮件
        this.queryEmailDetailById()
      } else if (spreadType === 2 && this.detail.vmsgDetail.msgType == 4) {
        // 公众号图文消息
        console.log(this.detail)
        const pararm = {
          mediaId: this.detail.vmsgDetail.text,
        }
        if (this.detail.wxAppId) {
          pararm.wxAppId = this.detail.wxAppId
        } else {
          pararm.appId = this.detail.appId
        }
        http.queryWxMaterialByMediaId(pararm).then(res => {
          if (res && res.errCode == 0) {
            this.vmsgGraphicList = res.data.itemDetail.content.newsItems
          }
        })
      }
    },
    handleDetailClick(evt) {
      if (evt.target.tagName === 'A') {
        const params = vmsgUtil.testHref(evt.target.href) // 判断所点的<a>是否指向推广物料H5
        if (params.id && params['#type']) {
          evt.preventDefault()
          this.$nextTick(() => {
            this.$emit('linkclick', {
              type: params['#type'],
              id: params.id,
            })
          })
        }
      }
    },
  },
}
</script>

<style lang="less" module>
.pmContent {
  display: flex;
  align-items: center;
  height: 50px;
  font-size: 13px;
  color: #181c25;
  background: #F5F7FB;
  border: 1px solid #e9edf5;
  .itemw {
    max-height: 180px;
    overflow-y: auto;
    width: 100%;
    & .item:not(:last-child) {
      border-bottom: 1px solid #e9edf5;
    }
    .item {
      height: 60px;
      display: flex;
      align-items: center;
      margin: 0 18px;
      cursor: pointer;
      color: #181c25;
      .image {
        margin-left: 0;
      }
      &:hover {
        text-decoration: none;
        color: #181c25;
        background-color: #f5f7fa;
        cursor: pointer;
      }
    }
  }
  .image {
    width: 50px;
    height: 50px;
    background: center/cover no-repeat;
    flex-shrink: 0;
    margin-right: 10px;

    &:last-of-type {
      margin-right: 0;
    }
  }
  .descs {
    padding: 10px 15px;
    word-wrap: break-word;
    word-break: break-all;
    font-size: 12px;
    overflow: hidden;
    .text {
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      /* autoprefixer: off */
      -webkit-box-orient: vertical;
      /* autoprefixer: on */
      -webkit-line-clamp: 2;
    }
  }
}
</style>
