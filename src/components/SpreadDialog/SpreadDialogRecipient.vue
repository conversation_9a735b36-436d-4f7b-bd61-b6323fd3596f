<template>
  <div class="SpreadDialogRecipient">
    <div class="SpreadDialogRecipient__body">
      <!-- 发送对象不可修改 -->
      <div 
        class="SpreadDialogRecipient__show"
        v-if="scene === 'meeting' || scene === 'live' || scene === 'activity'"
        >
        <div class="SpreadDialogRecipient__bar">
          <div 
            class="bar__item" 
            v-for="item in recipients"
            :key="item.id"
          >
            {{item.name}}  
          </div>
        </div>
        <div class="SpreadDialogRecipient__total">
          {{ $t('marketing.commons.gr_22139c', {data: ({'option0': recipients.length})}) }}
        </div>
      </div>
      <!-- 发送对象可选择 -->
      <div 
        class="SpreadDialogRecipient__selector"
        v-else
        >
        <RecipientSimpleSelector @change="handleRecipientChange" :sendRangeTypes="scene === 'qrposter' ? [2] : [2,3]"></RecipientSimpleSelector>
        <FilterNday v-model="model_filterNday"></FilterNday>
      </div>
    </div>
  </div>
</template>

<script>
import SelectCrowdLine from '@/components/select-crowd-line';
import RecipientSelector from '@/components/RecipientSelector';
import RecipientSimpleSelector from '@/components/RecipientSimpleSelector';
import FilterNday from '@/components/filter-nday';
export default {
  components: {
    RecipientSelector,
    RecipientSimpleSelector,
    FilterNday,
  },
  props: {
    scene: {
      type: String,
      default: '',
    },
    recipients: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  watch: {
    model_filterNday: {
      deep: true,
      handler() {
        this.formData.filterNDaySentUser = this.model_filterNday.checked ? this.model_filterNday.day : 0;
      },
    },
    formData: {
      deep: true,
      handler() {
        this.$emit('change', this.formData);
      },
    },
  },
  data() {
    return {
      model_filterNday: {
          checked: false,
          day: 1,
      },
      formData: {
        sendRange: 2, // 群发对象 1-手机号文件 2-目标人群
        filterNDaySentUser: 0, // 过滤天数
        taPath: null, // 手机号文件
        marketingUserGroupIds: [], // 目标人群列表
      },
    }
  },
  methods: {
    handleRecipientChange(recipient) {
      console.log('Recipient', recipient);
      this.formData.sendRange = recipient.sendRange;
      this.formData.taPath = recipient.taPath;
      this.formData.marketingUserGroupIds = recipient.marketingUserGroupIds;
      this.formData.marketingUsers = recipient.marketingUsers;
    },
  },
  mounted() {
    console.log("scene", this.scene);
  },

}
</script>

<style lang="less" scoped>
.SpreadDialogRecipient {
  .SpreadDialogRecipient__body {
    .SpreadDialogRecipient__show {
      display: flex;
    }
    .SpreadDialogRecipient__bar {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      border: 1px solid #E9EDF5;
      border-radius: 4px;
      flex: 1;
      box-sizing: border-box;
      padding: 0 12px 5px 12px;
      overflow-y: auto;
      max-height: 88px;
      .bar__item {
        line-height: 16px;
        padding: 4px;
        background-color: rgba(225,233,250,.92);
        color: #212b36;
        border-radius: 2px;
        margin-right: 5px;
        margin-top: 5px;
        cursor: pointer;
      }

    }
    .SpreadDialogRecipient__total {
      flex-shrink: 0;
      margin-left: 10px;
      color: #999999;
    }
  }
}
</style>