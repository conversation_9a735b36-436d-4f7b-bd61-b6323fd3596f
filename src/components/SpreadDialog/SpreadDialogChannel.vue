<template>
  <div class="SpreadDialogChannel">
    <div class="SpreadDialogChannel__body">
      <div class="SpreadDialogChannel__checkboxes">
        <ElCheckboxGroup
          v-model="model_channels"
          class="checkboxes__group"
          @change="handleChannelChange"
        >
          <template v-for="channel in data_channels">
            <ElCheckbox
              v-if="!channel.lock"
              :key="channel.id"
              class="checkboxes__lock"
              :label="channel.id"
              :disabled="channel.disabled"
            >
              {{ channel.label }}
              <template v-if="sendPeopleCount[channel.id].loading">
                (
                <span
                  v-loading="sendPeopleCount[channel.id].loading"
                  class="channel__loading"
                  element-loading-spinner="el-icon-loading"
                >---</span>
                )
              </template>
              <template v-else>
                {{ !sendPeopleCount[channel.id].unknow ? `（${$t('marketing.commons.r_220c4f', {data: ({'option0': sendPeopleCount[channel.id].count})})}）` : '' }}
              </template>
            </ElCheckbox>
            <ElTooltip
              v-else
              :key="channel.id"
              effect="light"
              placement="bottom"
            >
              <div
                class="checkboxes__lock"
              >
                <i class="el-icon-lock" />
                {{ channel.label }}
              </div>
              <div
                slot="content"
                class="eltooltip__content"
              >
                {{ $t('marketing.commons.wktfw_1bd3d0', {data: ({'option0': channel.label})}) }}<ElLink
                  class="eltooltip__link"
                  :href="channel.link"
                  target="_blank"
                  type="standard"
                >
                  {{ $t('marketing.commons.qwkt_5371ff') }}
                </ElLink>
              </div>
            </ElTooltip>
          </template>
        </ElCheckboxGroup>
      </div>
      <SpreadDialogChannelCards
        :scene="scene"
        :channels="model_channels"
        :send-people-count="sendPeopleCount"
        :marketing-event-id="marketingEventId"
        :material="material"
        :scene-type="sceneType"
        :show-crm-object-params="showCrmObjectParams"
        :recipient="recipient"
        @submit="handleCardsSubmit"
      />
    </div>
  </div>
</template>

<script>
import SpreadDialogChannelCards from './SpreadDialogChannelCards.vue'
import { SpreadDialogChannels } from './config.js'
import wxList from '@/modules/wx-list.js'

export default {
  components: {
ElCheckboxGroup: FxUI.CheckboxGroup,
ElCheckbox: FxUI.Checkbox,
ElTooltip: FxUI.Tooltip,
ElLink: FxUI.Link,
SpreadDialogChannelCards
},
  props: {
    // 短信模板场景 0其他营销短信 1其他通知短信  2会议报名  3会议邀约  4直播报名  5直播邀约  6：EXCEL）
    sceneType: {
      type: Number,
      default: 0,
    },
    scene: {
      type: String,
      default: '',
    },
    marketingEventId: {
      type: String,
      default: '',
    },
    material: {
      type: Object,
      default() {
        return {}
      },
    },
    recipient: {
      type: Object,
      default: () => {},
    },
    showCrmObjectParams: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      // 选中的渠道
      model_channels: [],
      model_spreadData: {},
      data_campaignIdsSendPeopleCount: null, // 通过活动成员查的发送人数
    }
  },
  computed: {
    data_channels() {
      let _result = []
      // 设置过滤项 - 会议场景下，公众号”、“企业微信”渠道隐藏
      _result = this.setChannelHide(_result)
      // 设置禁用项 - 发送对象选择“上传手机号清单”时，“公众号”、“企业微信”渠道置灰
      _result = this.setChannelDisabled(_result)
      // 设置上锁项 - 当前企业未开通该渠道时，该渠道上锁
      _result = this.setChannelLock(_result)
      return _result
    },
    isQywxOpen() {
      return this.$store.state.Global.isQywxOpen
    },
    isSmsOpen() {
      return this.$store.state.SmsMarketing.statusSMS === 0
    },
    isWechatOpen() {
      try {
        return wxList.datas.list.length > 0
      } catch (e) {
        return false
      }
    },
    isEmailOpen() {
      return this.$store.state.MailMarketing.completeAllConfig
    },
    sendRange() {
      return this.recipient.sendRange
    },
    disableds() {
      return this.recipient.sendRange === 1 ? ['wechat', 'qywx', 'email'] : []
    },
    sendPeopleCount() {
      let _return = {
        sms: { unknow: true },
        email: { unknow: true },
        qywx: { unknow: true },
        wechat: { unknow: true },
      }
      const {
        sendRange, marketingUsers,
      } = this.recipient
      if (this.scene === 'meeting' || this.scene === 'live' || this.scene === 'activity') {
        // for (const key in _return) {
        //   _return[key].unknow = false;
        //   _return[key].count = this.recipients.length;
        // }
        if (!this.data_campaignIdsSendPeopleCount) {
          _return = {
            sms: { loading: true },
            email: { loading: true },
            qywx: { loading: true },
            wechat: { loading: true },
          }
        } else {
          _return = {
            sms: {
              unknow: false,
              count: this.data_campaignIdsSendPeopleCount.sms,
            },
            email: {
              unknow: false,
              count: this.data_campaignIdsSendPeopleCount.email,
            },
            qywx: {
              unknow: false,
              count: this.data_campaignIdsSendPeopleCount.qywx,
            },
            wechat: {
              unknow: false,
              count: this.data_campaignIdsSendPeopleCount.wechat,
            },
          }
        }
      } else if (sendRange === 2 && marketingUsers.length > 0) {
        // 为目标人群时，可以计数
        _return.sms = {
          unknow: false,
          count: marketingUsers.reduce((accumulator, currentValue) => accumulator + currentValue.more.userPhoneNumber, 0),
        }
        _return.email = {
          unknow: false,
          count: marketingUsers.reduce((accumulator, currentValue) => accumulator + currentValue.more.emailUserNumber, 0),
        }
        _return.qywx = {
          unknow: false,
          count: marketingUsers.reduce((accumulator, currentValue) => accumulator + (currentValue.more.wxWorkExternalUserNumber || 0), 0),
        }
        _return.wechat = {
          unknow: false,
          count: marketingUsers.reduce((accumulator, currentValue) => accumulator + currentValue.more.wxFansNumber, 0),
        }
      }
      console.log('sendPeopleCount', _return)
      return _return
    },
  },
  watch: {
    sendPeopleCount() {
      console.log('sendPeopleCount', this.sendPeopleCount)
    },
    recipient() {
      if (this.scene === 'meeting' || this.scene === 'live' || this.scene === 'activity') {
        this.getSendPeopleCountByCampaignIds()
      }
    },
  },
  mounted() {
    if (this.scene === 'meeting' || this.scene === 'live' || this.scene === 'activity') {
      this.getSendPeopleCountByCampaignIds()
    }
    // if (this.scene === 'meeting') {
    // this.model_channels = ['sms'];
    // }
  },
  methods: {
    handleChannelChange(res) {
      // 向父组件传递更新后的值
      this.$emit('submit', this.model_spreadData, this.model_channels)
    },
    handleCardsSubmit(res) {
      this.model_spreadData = res
      this.$emit('submit', res, this.model_channels)
    },
    setChannelHide(_result) {
      let filterOutIds = []
      if (this.scene === 'meeting' || this.scene === 'live' || this.scene === 'activity') {
        filterOutIds = ['qywx']
      } else if (this.scene === 'qrposter') {
        filterOutIds = ['sms']
      }
      _result = SpreadDialogChannels.filter(item => filterOutIds.indexOf(item.id) === -1)
      return _result
    },
    setChannelDisabled(_result) {
      _result.forEach(item => {
        item.disabled = false
      })
      this.disableds.forEach(channelId => {
        const _filters = _result.filter(channel => channel.id === channelId)
        if (_filters[0]) {
          _filters[0].disabled = true
        }
      })
      return _result
    },
    setChannelLock(_result) {
      _result.forEach(item => {
        item.lock = false
      })
      const lockChannels = []
      if (!this.isSmsOpen) {
        lockChannels.push('sms')
      }
      if (!this.isQywxOpen) {
        lockChannels.push('qywx')
      }
      if (!this.isWechatOpen) {
        lockChannels.push('wechat')
      }
      if (!this.isEmailOpen) {
        lockChannels.push('email')
      }

      lockChannels.forEach(channelId => {
        const _filters = _result.filter(channel => channel.id === channelId)
        if (_filters[0]) {
          _filters[0].lock = true
          _filters[0].link = this.getChannelOpenLink(channelId)
        }
      })
      return _result
    },
    getChannelOpenLink(channelId) {
      const preurl = `${window.location.href.split('/=/')[0]}`
      let link = ''

      switch (channelId) {
        case 'sms':
          link = `${preurl}/=/sms-marketing/init/welcome`
          break
        case 'qywx':
          link = `${preurl}/=/qywx-manage/index`
          break
        case 'wechat':
          link = `${preurl}/=/wechat/home`
          break
        case 'email':
          link = `${preurl}/=/mail-marketing/init/welcome`
          break
        default: break
      }

      return link
    },
    async getSendPeopleCountByCampaignIds() {
      const typeMap = {
        2: 'wechat',
        3: 'sms',
        5: 'qywx',
        6: 'email',
      }
      // 把初始值复制给data_campaignIdsSendPeopleCount
      this.data_campaignIdsSendPeopleCount = {
        wechat: 0,
        sms: 0,
        qywx: 0,
        email: 0,
      }
      // this.recipient.campaignIds不存在或者为空，则不请求
      if (!this.recipient.campaignIds || this.recipient.campaignIds.length === 0) {
        return
      }
      const params = {
        campaignIds: this.recipient.campaignIds,
        spreadTypes: [2, 3, 5, 6], // 推广类型 2:微信公众号 3：短信 5：企业微信 6：邮件
      }
      const res = await YXT_ALIAS.http.batchGetSendNotificationStatistic(params)
      if (res && res.errCode === 0) {
        res.data.statisticData.forEach(item => {
          this.data_campaignIdsSendPeopleCount[typeMap[item.spreadType]] = item.total
        })
      }
    },
  },
}
</script>

<style lang="less">
.SpreadDialogChannel {
  .SpreadDialogChannel__body {
    .checkboxes__group {
    }
    .checkboxes__lock {
      font-size: 14px;
      display: inline-block;
      line-height: 40px;
      color: #606266;
      font-weight: 500;
      cursor: pointer;
      margin-right: 30px;
      .el-icon-lock {
        color: #B4B6C0;
        padding-right: 3px;
        font-weight: 600;
      }
    }
    .channel__loading {
      /deep/ .el-loading-spinner {
        top: 0;
        margin-top: 0;
        i {
          color: #606266;
        }
      }
    }
  }
}
.eltooltip__content {
  display: flex;
  align-items: center;
}
.eltooltip__link {
  color: #4582FF;
  font-size: 12px;
  text-decoration: none;
}
</style>
