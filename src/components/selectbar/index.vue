<template>
  <div :class="'selectbar-input' + (isShowMore ? ' has-more' : '')">
    <ul class="person-btn-wrap" ref="selectWrap">
      <li :key="item.id" v-for="item in formatSelects">
        <div class="btn-item">
          <!-- <span class="icon-people" ></span> -->
          <i class="iconfont btn-icon" v-if="item.type == 'colleague'"
            >&#xe6cb;</i
          >
          <i class="iconfont btn-icon" v-if="item.type == 'depart'">&#xe6c9;</i>
          <i class="iconfont btn-icon" v-if="item.type == 'role'">&#xe6ca;</i>
          <span class="selected-name">{{ item.name }}</span>
          <span class="btn-remove" @click="_remove(item.type, item.id)"
            ><svg
              data-v-bb38b5bc=""
              version="1.1"
              role="presentation"
              width="9.600000000000001"
              height="12.8"
              viewBox="0 0 12 16"
              class="octicon"
              style="font-size: 0.8em;"
            >
              <path
                d="M7.48 8l3.75 3.75-1.48 1.48L6 9.48l-3.75 3.75-1.48-1.48L4.52 8 .77 4.25l1.48-1.48L6 6.52l3.75-3.75 1.48 1.48z"
              ></path></svg
          ></span>
        </div>
      </li>
      <li class="selectbar-select-btn" ref="selectBtn" @click="_handleSelect">
        +{{ btnText || $t('marketing.commons.qxz_708c9d') }}
      </li>
    </ul>

    <span class="search-clear">
      <svg
        class="clear-svg"
        version="1.1"
        role="presentation"
        width="10"
        height="13"
        viewBox="0 0 12 16"
      >
        <path
          d="M7.48 8l3.75 3.75-1.48 1.48L6 9.48l-3.75 3.75-1.48-1.48L4.52 8 .77 4.25l1.48-1.48L6 6.52l3.75-3.75 1.48 1.48z"
        ></path>
      </svg>
    </span>
    <div v-if="isShowMore" class="btn-more" @click="_handleSelect">
      <span>···</span>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    selects: Object,
    btnText: String
  },
  data() {
    return {
      isShowMore: false
    };
  },
  computed: {
    formatSelects() {
      const selects = this.selects;
      const results = Object.keys(selects).reduce((a, b) => {
        let arr = [];
        if (selects[b] instanceof Array) {
          arr = selects[b].map(item => ({
            ...item,
            type: b
          }));
        }
        return [...a, ...arr];
      }, []);

      return results;
    }
  },
  methods: {
    _handleSelect() {
      this.$emit("update:select");
    },
    _remove(type, id) {
      const selects = this.selects;
      selects[type] = selects[type].filter(item => item.id != id);
      this.$emit("change", selects);
    }
  },
  updated() {
    if (this.$refs.selectBtn.offsetTop > 5) {
      this.isShowMore = true;
    } else {
      this.isShowMore = false;
    }
  },
  mounted() {
    if (this.$refs.selectBtn.offsetTop > 5) {
      this.isShowMore = true;
    } else {
      this.isShowMore = false;
    }
  }
};
</script>

<style lang="less">
.selectbar-input {
  position: relative;
  z-index: 9;
  background: #fff;
  border: 1px solid @border-color-base;
  border-radius: 4px;
  overflow: hidden;
  &.has-more {
    padding-right: 55px;
  }
  &.over-line {
    .btn-more {
      display: block;
    }
    &.focus-blur {
      .search-clear {
        display: none;
      }
    }
  }
  &.focus-blur {
    padding-right: 56px;
    height: 34px;
    overflow: hidden;
  }
  &.focus-in {
    max-height: 200px;
    .btn-more {
      display: none;
    }
  }
  .selectbar-select-btn {
    display: inline-block;
    line-height: 31px;
    font-size: 12px;
    color: var(--color-primary06, #407fff);
    background: #fff;
    border-radius: 3px;
    cursor: pointer;
    vertical-align: top;
    padding: 0 15px;
  }
  .person-btn-wrap {
    height: 30px;
    overflow: hidden;
    li {
      display: inline-block;
      white-space: nowrap;
    }
  }
  .btn-item {
    line-height: 16px;
    margin: 2px;
    border-radius: 2px;
    padding-left: 3px;
    background-color: rgba(225, 233, 250, 0.92);
    cursor: pointer;
    color: #212b36;
    white-space: nowrap;
    display: flex;
    align-items: center;
    > span {
      display: inline-block;
      vertical-align: middle;
    }
    .selected-name {
      margin: 0 1px;
      max-width: 100px;
      height: 26px;
      line-height: 26px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-size: 12px;
    }
    .btn-remove {
      height: 16px;
      width: 16px;
      color: #c3ced9;
      text-align: center;
      cursor: pointer;
      margin-top: 4px;
    }
  }
  .btn-more {
    position: absolute;
    top: 0;
    right: 0;
    width: 22px;
    height: 22px;
    margin: 4px 4px;
    text-align: center;
    line-height: 22px;
    background-color: #eff3fc;
    color: #181c25;
    font-weight: 700;
    cursor: pointer;
    border-radius: 2px;
    > span {
      font-size: 12px;
    }
  }
  .search-clear {
    display: none;
    position: absolute;
    right: 0;
    width: 16px;
    height: 16px;
    margin: 6px 2px;
    border-radius: 8px;
    background-color: #c3cbd9;
    color: #fff;
    line-height: 16px;
    text-align: center;
    cursor: pointer;
  }
  .clear-svg {
    position: absolute;
    fill: currentColor;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  .btn-icon {
    margin-right: 4px;
    display: inline-block;
    color: #737c8c;
  }
}
</style>
