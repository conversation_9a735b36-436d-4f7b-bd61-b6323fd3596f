<template>
  <div :class="$style.associate_content_campaignButton">
    <fx-button class="create-material-btn" size="small" type="text" @click="showMaterialDialog">
      <span :class="$style.title"
        ><i :class="['el-icon-plus', $style.icon]"></i>{{ $t('marketing.commons.tjnr_f75488') }}</span
      >
    </fx-button>
    <!-- 禁止删掉 -->
    <fx-button class="marketing-guide-close-material-btn" v-show="false" size="small" type="text" @click="closeMaterialDialog">
    </fx-button>
    <material-select-dialog :marketingEventId="marketingEventId" ref="materialDialog" @select="handleMaterialSelect" />
    <!-- 添加推广内容 -->
    <SelectMaterialDialog
      v-if="selectMaterialDialogVisible"
      :tabbar="tabbar"
      :visible="selectMaterialDialogVisible"
      :marketingEventId="marketingEventId"
      @onSubmit="handleMaterialSelected"
      @material:add="handleMaterialSelected"
      @onClose="selectMaterialDialogVisible = false"
    />
    <ExternalContentCreate
      v-if="externalContentVisible"
      :visible.sync="externalContentVisible"
      @onCreated="handleExternalContentCreated"
    />
    <UploadPdfToSite ref="UploadPdfToSite" :marketingEventId="marketingEventId" :systemSite="1" @completed="handleFileToHexagonCompleted"/>
  </div>
</template>

<script>
import SelectMaterialDialog from "@/components/select-material-dialog";
import ExternalContentCreate from "@/pages/external-content/components/external-content-create";
import http from "@/services/http/index";
import { confirm } from '@/utils/globals';
import FileToHexagon from '@/components/file-to-hexagon/index.vue'
import QuestionTooltip from '@/components/questionTooltip/index.vue'
import UploadPdfToSite from '@/pages/site/components/UploadPdfToSite.vue'
import materialSelectDialog from '@/components/materialSelectDialog/index.vue'

export default {
  components: {
    FileToHexagon,
    QuestionTooltip,
    Dropdown: FxUI.Dropdown,
    DropdownMenu: FxUI.DropdownMenu,
    DropdownItem: FxUI.DropdownItem,
    SelectMaterialDialog,
    ExternalContentCreate,
    UploadPdfToSite,
    materialSelectDialog
  },
  props: {
    marketingEventId: String,
    tabbar: {
      type: Array,
      default: () => [10, 1, 4, 16]
    }
  },
  data() {
    return {
      selectMaterialDialogVisible: false,
      externalContentVisible: false,
      disabledFileUpload: false,
    };
  },
  methods: {
    uploadPdfFile() {
      this.$refs.UploadPdfToSite.showDialog();
    },
    handleFileToHexagonCompleted() {
      this.closeMaterialDialog()
      this.$emit('onMaterialAdded')
    },
    handleCommand(command) {
      if (command === "content_center") {
        this.selectMaterialDialogVisible = true;
      } else if (command === "private_site") {
        //根据链接识别活动类型获取对应活动ID
        const url = window.location.href;
        const extendParams = {};
        if(url.includes('live-marketing/dashboard')) {
          //直播活动详情页面
          extendParams.liveId = this.$route.params.id;
        } else if(url.includes('/meeting-marketing/')) {
          //会议活动详情页面
          extendParams.conferenceId = this.$route.params.id;
        }
        const route = this.$router.resolve({
          name: "site-create",
          query: {
            ...extendParams,
            createType: "private",
            marketingEventId: this.marketingEventId || "",
            from: 'dialog'
          }
        });
        window.open(route.href, "_blank");
        confirm($t('marketing.components.AssociateContentToCampaignButton.sfywczswym_832358'), $t('marketing.commons.ts_02d981'), {}).then(() => {
          this.closeMaterialDialog()
          this.$emit("onMaterialAdded");
        });
      } else if (command === 'external_content') {
        this.externalContentVisible = true;
      } else if(command === 'upload_pdf') {
        this.uploadPdfFile();
      }
    },
    async handleMaterialSelected(row) {
      // 添加物料到市场活动
      const { errCode } = await http.addMaterial({
        marketingEventId: this.marketingEventId,
        objectId: row.id,
        objectType: row.objectType || (row.type == 1 ? 6 : row.type)
      });
      if (errCode === 0) {
        this.selectMaterialDialogVisible = false;
        this.closeMaterialDialog()
        this.$emit("onMaterialAdded");
      }
    },
    handleExternalContentCreated(data) {
      this.handleMaterialSelected({
        objectType: 9999,
        id: data.id
      });
    },
    showMaterialDialog() {
      this.$refs.materialDialog.show()
    },
    closeMaterialDialog() {
      this.$refs.materialDialog.handleClose()
    },
    handleMaterialSelect(type) {
      // 根据不同类型处理不同逻辑
      this.handleCommand(type)
    }
  }
};
</script>

<style lang="less" module>
.associate_content_campaignButton {
  height: 28px;
  .title {
    color: @color-link;
    .icon {
      margin-right: 3px;
      font-weight: bold;
    }
  }
}
.uploadpdf {
  display: flex;
}
</style>
