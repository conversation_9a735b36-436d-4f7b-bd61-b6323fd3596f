import { appendToElement } from '../util/dom.js'
import { onceInTime } from '../util/index.js'

export default class Component {
  constructor(attrs, props) {
    this.tagName = 'div'
    this.children = []
    this.oldProps = {}
    this.oldData = {}
    this.renderChildComp = onceInTime(this.renderChildren.bind(this))

    Object.assign(this, attrs)
    this.props = props
  }

  setData(data) {
    this.data = data
    this.renderChildComp()
  }

  mount(parent) {
    this.$parent = parent
    this.$el = this.render()
    appendToElement(parent, this.$el)
    this.mounted && this.mounted()
  }

  update(props) {
    if (this.shouldComponentUpdate && !this.shouldComponentUpdate(this.props, props)) {
      return
    }
    this.props = props
    this.renderComponent()
  }

  shouldComponentUpdate(props, nextProps) {
    return props !== nextProps
  }

  renderChildren() {
    if (this.components) {
      const { param, component } = this.components
      const data = this[param]
      Object.keys(component).forEach(key => {
        const comp = new component[key]({}, data)
        comp.mount(this.$el)
        this.children.push(comp)
      })
    }
  }

  renderComponent() {
    this.removeChildren()
    this.renderChildren()
  }

  removeChildren() {
    this.children.forEach(comp => {
      comp.destroy()
    })
    this.children = []
  }

  removeNode() {
    this.$el &&
      this.$el.parentNode &&
      this.$el.parentNode.childNodes.length &&
      this.$el.parentNode.removeChild(this.$el)
  }

  destroy() {
    this.beforeDestroy && this.beforeDestroy()
    this.removeNode()
  }
} 