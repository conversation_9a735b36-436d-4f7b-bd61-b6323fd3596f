export default class Emitter {
  static mixToClass(targetClass) {
    Object.getOwnPropertyNames(Emitter.prototype).forEach(name => {
      if (!targetClass.prototype[name]) {
        targetClass.prototype[name] = Emitter.prototype[name]
      }
    })
  }

  bind(type, handler) {
    addToHandlers({ handlers: this._handlers || (this._handlers = {}), type, handler })
    return this
  }

  unbind(type, handler) {
    if (this._handlers) {
      removeFromHandlers({ handlers: this._handlers, type, handler })
    }
    return this
  }

  trigger(type, ...args) {
    if (this._handlers && this._handlers[type]) {
      this._handlers[type].forEach(handler => {
        handler.apply(this, args)
      })
    }
    return this
  }

  hasHandlers(type) {
    return this._handlers && this._handlers[type] && this._handlers[type].length
  }
}

function addToHandlers({ handlers, type, handler }) {
  ;(handlers[type] || (handlers[type] = [])).push(handler)
}

function removeFromHandlers({ handlers, type, handler }) {
  if (handler) {
    if (handlers[type]) {
      handlers[type] = handlers[type].filter(function(func) {
        return func !== handler
      })
    }
  } else {
    delete handlers[type]
  }
} 