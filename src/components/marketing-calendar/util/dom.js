export function createElement({ tagName, attrs, content }) {
  let el = document.createElement(tagName)

  if (attrs) {
    Object.keys(attrs).forEach(key => {
      if (key === 'style') {
        applyStyle({ el, props: attrs[key] })
      } else {
        el.setAttribute(key, attrs[key])
      }
    })
  }

  if (typeof content === 'string') {
    el.innerHTML = content
  } else if (content != null) {
    appendToElement(el, content)
  }

  return el
}

export function appendToElement(el, content) {
  let childNodes = normalizeContent(content)

  for (let i = 0; i < childNodes.length; i++) {
    el.appendChild(childNodes[i])
  }
}

function normalizeContent(content) {
  let els
  if (typeof content === 'string') {
    els = htmlToElements(content)
  } else if (content instanceof Node) {
    els = [content]
  } else {
    els = Array.prototype.slice.call(content)
  }
  return els
}

export function htmlToElement(html) {
  html = html.trim()
  let container = document.createElement('div')
  container.innerHTML = html
  return container.firstChild
}

export function htmlToElements(html) {
  return Array.prototype.slice.call(htmlToNodeList(html))
}

function htmlToNodeList(html) {
  html = html.trim()
  let container = document.createElement(html)
  container.innerHTML = html
  return container.childNodes
}

const PIXEL_PROP_RE = /(top|left|right|bottom|width|height)$/i

export function applyStyle({ el, props }) {
  for (let propName in props) {
    applyStyleProp({ el, name: propName, val: props[propName] })
  }
}

export function applyStyleProp({ el, name, val }) {
  if (val == null) {
    el.style[name] = ''
  } else if (typeof val === 'number' && PIXEL_PROP_RE.test(name)) {
    el.style[name] = val + 'px'
  } else {
    el.style[name] = val
  }
} 