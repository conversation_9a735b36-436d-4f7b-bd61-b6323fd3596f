export function isTimeIntervalHaveIntersection(startDate, endDate, firstDate, lastDate) {
  return startDate <= lastDate && firstDate <= endDate
}

export function onceInTime(fn, ms = 30) {
  let called = false
  return function(...args) {
    if (called) return
    called = true
    setTimeout(() => {
      called = false
    }, ms)
    return fn.apply(this, args)
  }
}

export function debounce(fn, ms = 0) {
  let timeoutId
  return function(...args) {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => fn.apply(this, args), ms)
  }
}

export function deepClone(obj) {
  return JSON.parse(JSON.stringify(obj))
} 