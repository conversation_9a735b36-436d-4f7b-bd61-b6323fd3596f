<template>
  <div class="f--plan-view">
    <toolbar 
      :year="year"
      :month="month"
      :backButtonText="backButtonText"
      @prev="$emit('prev')"
      @next="$emit('next')"
      @today="$emit('today')"
    />
    
    <div class="f__calendar-flex">
      <div class="f__plan-table">
        <div class="f__plan-table-head">
          <div class="f__plan-table-th">{{ $t('marketing.commons.hdmc_39834b') }}</div>
          <div class="f__plan-table-th">{{ $t('marketing.commons.hdlx_13955e') }}</div>
        </div>
        <div class="f__plan-table-body">
          <div v-for="activity in monthActivities"
               :key="activity.id"
               :data-id="activity.id"
               :class="['f__plan-table-tr', {'f__planline-hover': activity.hover}]"
               @mouseover="onRowHover(activity)"
               @mouseout="onRowLeave(activity)"
               @click="onActivityClick($event, activity)">
            <div class="f__plan-table-td" :title="activity.title">{{ activity.title }}</div>
            <div class="f__plan-table-td">
              <span class="f__plan-table-dot" :style="{background: activity.hoverBackground}"></span>
              {{ activity.typeLabel }}
            </div>
          </div>
        </div>
      </div>

      <div class="f__plan-body">
        <div class="f__plan-scroll">
          <div class="f__plan-head">
            <div v-for="day in days" 
                 :key="day"
                 :class="['f__plan-th', {
                   'f__active': isToday(day),
                   'f__gray': isPastDay(day)
                 }]">
              {{ isToday(day) ? $t('marketing.commons.jt_800dfd') : day }}
            </div>
          </div>

          <div class="f__plan-content">
            <div v-for="activity in monthActivities"
                 :key="activity.id"
                 :class="['f__plan-row', {'f__planline-hover': activity.hover}]"
                 @mouseover="onRowHover(activity)"
                 @mouseout="onRowLeave(activity)">
              <div v-for="day in days" 
                   :key="day"
                   class="f__plan-col">
              </div>
              <div v-if="getActivityPosition(activity)"
                   :class="['f__plan-line', {'no__radius': activity.isNoRadius}]"
                   :style="getActivityStyle(activity)"
                   :data-id="activity.id"
                   @click="onActivityClick($event, activity)">
                <div class="f__plan-line-process" 
                     :style="{
                       background: activity.hoverBackground,
                       width: getActivityProcess(activity)
                     }">
                </div>
                <span class="f__plan-line-name">{{ activity.statusLabel }}</span>
              </div>
            </div>
          </div>

          <div v-if="todayCol !== -1"
               class="f__plan-today-col"
               :style="{left: todayCol * 50 + 'px'}">
            {{ $t('marketing.commons.jt_800dfd') }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Toolbar from './Toolbar.vue'
import { isTimeIntervalHaveIntersection } from '../util'

export default {
  name: 'PlanView',

  components: {
    Toolbar
  },

  props: {
    year: Number,
    month: Number,
    activities: Array,
    backButtonText: String
  },

  data() {
    return {
      days: Array.from({length: 31}, (_, i) => i + 1)
    }
  },

  computed: {
    monthActivities() {
      if(!this.activities?.length) return []

      const firstDate = new Date(this.year, this.month - 1, 1)
      const lastDate = new Date(this.year, this.month, 0)

      return this.activities.filter(activity => {
        const start = new Date(activity.start)
        const end = new Date(activity.end)
        return isTimeIntervalHaveIntersection(start, end, firstDate, lastDate)
      })
    },

    todayCol() {
      const now = new Date()
      if(this.year === now.getFullYear() && this.month === now.getMonth() + 1) {
        return now.getDate() - 1
      }
      return -1
    }
  },

  methods: {
    isToday(day) {
      const now = new Date()
      return this.year === now.getFullYear() && 
             this.month === now.getMonth() + 1 &&
             day === now.getDate()
    },

    isPastDay(day) {
      const now = new Date()
      const curr = new Date(this.year, this.month - 1, day)
      return curr < now
    },

    getActivityPosition(activity) {
      const start = new Date(activity.start)
      const end = new Date(activity.end)
      const firstDate = new Date(this.year, this.month - 1, 1)
      const lastDate = new Date(this.year, this.month, 0)

      if(!isTimeIntervalHaveIntersection(start, end, firstDate, lastDate)) {
        return null
      }

      const left = Math.max(0, Math.floor((start - firstDate) / (24 * 60 * 60 * 1000))) * 50
      const width = Math.min(
        lastDate.getDate() * 50 - left,
        Math.ceil((end - start) / (24 * 60 * 60 * 1000) + 1) * 50
      )

      return {
        left,
        width
      }
    },

    getActivityStyle(activity) {
      const pos = this.getActivityPosition(activity)
      if(!pos) return null

      return {
        left: pos.left + 'px',
        width: pos.width + 'px',
        borderColor: activity.hoverBackground
      }
    },

    getActivityProcess(activity) {
      const now = new Date()
      const start = new Date(activity.start)
      const firstDate = new Date(this.year, this.month - 1, 1)

      if(now <= start) return '0px'

      const days = Math.floor((now - Math.max(start, firstDate)) / (24 * 60 * 60 * 1000))
      return (days + 1) * 50 + 'px'
    },

    onRowHover(activity) {
      activity.hover = true
    },

    onRowLeave(activity) {
      activity.hover = false
    },

    onActivityClick(event, activity) {
      this.$emit('activity-click', event, activity)
    }
  }
}
</script> 