<template>
  <div class="f__calendar-wrap">
    <toolbar 
      :year="year"
      :month="month"
      :backButtonText="backButtonText"
      @prev="$emit('prev')"
      @next="$emit('next')"
      @today="$emit('today')"
    />
    
    <div class="f-calendar__header">
      <div v-for="day in weekDays" 
           :key="day" 
           class="f-week__item">
        {{ day }}
      </div>
    </div>

    <div class="f-calendar__body">
      <div v-for="(row, rowIndex) in calendarRows" 
           :key="rowIndex"
           class="f-day__row">
        <div v-for="(cell, colIndex) in row"
             :key="colIndex" 
             :class="getDayClass(cell)"
             :date="cell.date"
             @click="onDateClick($event, cell)">
          <div class="f-day">
            {{ cell.isToday ? $t('marketing.commons.jt_800dfd') : cell.day }}
            <!-- 不显示节假日 -->
            <!-- <span v-if="cell.holiday" class="f-holiday">{{ cell.holiday }}</span> -->
          </div>
          
          <div v-if="cell.activities && cell.activities.list.length" 
               :class="['f-activities', {'show': cell.showMore}]">
            <template v-for="activity in cell.activities.list">
              <div v-show="!activity.hide"
                   :key="activity.id"
                   :class="getActivityClass(activity)"
                   :data-id="activity.id"
                   :style="getActivityStyle(activity)"
                   @click="onActivityClick($event, activity)">
                <span v-if="activity.isOneDay" 
                      class="f-activities__line-dot"
                      :style="{background: activity.background}">
                </span>
                {{ activity.title }}
              </div>
            </template>

            <div v-if="cell.activities.moreCount > 0" class="f-activities__toggle">
              <span v-if="!cell.showMore"
                    class="f-activities__toggle-open"
                    @click="showMore($event, cell)">
                {{ $t('marketing.commons.hyx_26be21', {data: ({'option0': cell.activities.moreCount})}) }}
              </span>
              <span v-else
                    class="f-activities__toggle-close"
                    @click="hideMore($event, cell)">
                {{ $t('marketing.commons.sq_def9e9') }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { WEEK_DAY, HOLIDAY } from '../common/constants.js'
import { sloarToLunar } from '../util/sloarToLunar.js'
import Toolbar from './Toolbar.vue'

export default {
  name: 'CalendarView',

  components: {
    Toolbar
  },

  props: {
    year: {
      type: [String, Number],
      required: true,
      validator: value => !isNaN(Number(value))
    },
    month: {
      type: [String, Number],
      required: true,
      validator: value => !isNaN(Number(value))
    },
    selected: {
      type: [String, Date],
      default: ''
    },
    activities: {
      type: Array,
      default: () => []
    },
    backButtonText: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      weekDays: WEEK_DAY,
      currentYear: Number(this.year),
      currentMonth: Number(this.month)
    }
  },

  watch: {
    year: {
      handler(newVal) {
        console.log(newVal,'newVal')
        debugger
        this.currentYear = Number(newVal)
      },
      immediate: true
    },
    month: {
      handler(newVal) {
        debugger
        console.log(newVal,'newVal')
        this.currentMonth = Number(newVal)
      },
      immediate: true
    }
  },

  computed: {
    calendarRows() {
      const days = new Date(this.currentYear, this.currentMonth, 0).getDate()
      const firstDay = new Date(this.currentYear, this.currentMonth - 1, 1).getDay()
      const rows = []
      let day = 1
      
      for (let i = 0; i < 6; i++) {
        const row = []
        for (let j = 0; j < 7; j++) {
          if (i === 0 && j < firstDay) {
            row.push({empty: true})
          } else if (day > days) {
            break
          } else {
            const date = `${this.currentYear}-${this.currentMonth}-${day}`
            const lunar = sloarToLunar(this.currentYear, this.currentMonth, day)
            const holiday = HOLIDAY[`${this.currentMonth}-${day}`] || 
                          HOLIDAY[`${lunar.lunarMonth}月${lunar.lunarDay}`]
            
            row.push({
              day,
              date,
              holiday,
              isToday: this.isToday(day),
              isSelected: this.isSelected(day),
              activities: this.getDayActivities(day),
              showMore: false
            })
            day++
          }
        }
        if(row.length) rows.push(row)
      }
      
      console.log(rows,"rowwwww")

      return rows
    }
  },

  methods: {
    isToday(day) {
      const now = new Date()
      return day === now.getDate() && 
             this.currentMonth === now.getMonth() + 1 &&
             this.currentYear === now.getFullYear()
    },

    isSelected(day) {
      if (!this.selected) return false
      const selectedDate = typeof this.selected === 'string' ? new Date(this.selected) : this.selected
      return day === selectedDate.getDate() &&
             this.currentMonth === selectedDate.getMonth() + 1 &&
             this.currentYear === selectedDate.getFullYear()
    },

    getDayClass(cell) {
      if(cell.empty) return 'f-day__item'
      return {
        'f-day__item': true,
        'f-day__today': cell.isToday,
        'f-day__selected': cell.isSelected
      }
    },

    getDayActivities(day) {
      if(!this.activities?.length) return null
      
      const activities = this.activities.filter(activity => {
        const start = new Date(activity.start)
        const end = new Date(activity.end)
        const curr = new Date(this.currentYear, this.currentMonth - 1, day)
        
        // 将日期转换为年月日格式进行比较，忽略时分秒
        const startDate = new Date(start.getFullYear(), start.getMonth(), start.getDate())
        const endDate = new Date(end.getFullYear(), end.getMonth(), end.getDate())
        const currDate = new Date(curr.getFullYear(), curr.getMonth(), curr.getDate())
        
        console.log(startDate, endDate, currDate, 'startDate, endDate, currDate')
        return startDate <= currDate && currDate <= endDate
      })

      console.log(activities, 'activities')

      if(!activities.length) return null

      // 按开始时间排序
      activities.sort((a, b) => {
        return b?.start?.originTime - a?.start?.originTime
      })

      const activityList = activities.map((activity, index) => ({
        ...activity,
        hide: index > 3,
        isOneDay: true // 简化为所有活动显示点
      }))

      return {
        list: activityList,
        moreCount: activities.length > 4 ? activities.length - 4 : 0
      }
    },

    getActivityClass(activity) {
      return {
        'f-activities__line': true,
        'f-activities__line-hide': activity.hide,
        'f-one__day-activity': activity.isOneDay
      }
    },

    getActivityStyle(activity) {
      return {
        background: activity.isOneDay ? 'none' : activity.background,
        zIndex: activity.hide ? 0 : 1
      }
    },

    showMore(e, cell) {
      e.stopPropagation()
      this.$set(cell, 'showMore', true)
      if (cell.activities && cell.activities.list) {
        cell.activities.list.forEach(activity => {
          this.$set(activity, 'hide', false)
        })
      }
      this.$forceUpdate()
    },

    hideMore(e, cell) {
      e.stopPropagation()      
      this.$set(cell, 'showMore', false)
      if (cell.activities && cell.activities.list) {
        cell.activities.list.forEach((activity, index) => {
          this.$set(activity, 'hide', index > 3)
        })
      }
      this.$forceUpdate()
    },

    onDateClick(event, cell) {
      if(cell.empty) return
      this.$emit('date-click', event, cell.date)
    },

    onActivityClick(event, activity) {
      event.stopPropagation()
      this.$emit('activity-click', event, activity)
    },
  }
}
</script> 
<style lang="less" scoped>
.f-activities__toggle-open {
  text-align: center;
  color: #91959e;
}
</style>

