<template>
  <div class="f__toolbar">
    <div class="f__toolbar-content">
      <div class="f__toolbar-prev" @click="$emit('prev')"></div>
      <div class="f__toolbar-title">
        {{ title }}
      </div>
      <div class="f__toolbar-next" @click="$emit('next')"></div>
    </div>
    <a v-if="showBackToday" 
       class="f__toolbar-backtoday"
       @click="$emit('today')">
      {{ backButtonText }}
    </a>
  </div>
</template>

<script>
export default {
  name: 'Toolbar',

  props: {
    year: {
      type: [String, Number],
      default: ''
    },
    month: {
      type: [String, Number],
      default: ''
    },
    backButtonText: {
      type: String,
      default: ''
    }
  },

  computed: {
    showBackToday() {
      const now = new Date()
      return this.month !== now.getMonth() + 1 || 
             this.year !== now.getFullYear()
    },
    title() {
      return $t("marketing.commons.ny_e37021", {
        data: { option0: this.year, option1: this.month }
      })
    }
  },
}
</script> 