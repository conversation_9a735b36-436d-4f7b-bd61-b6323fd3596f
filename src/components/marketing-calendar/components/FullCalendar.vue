<template>
  <div class="f__fullcalendar" ref="calendar">
    <calendar-view 
      v-if="currentView === 'Calendar'"
      :year="year"
      :month="month"
      :selected="selectedDate"
      :activities="activities"
      :backButtonText="backButtonText"
      @date-click="onDateClick"
      @date-change="onDateChange"
      @activity-click="onActivityClick"
      @next="nextMonth"
      @prev="prevMonth"
      @today="goToday"
    />
    <plan-view
      v-else
      :year="year" 
      :month="month"
      :activities="activities"
      :backButtonText="backButtonText"
      @activity-click="onActivityClick"
      @next="nextMonth"
      @prev="prevMonth"
    />
  </div>
</template>

<script>
/**
 * @typedef {Object} Activity
 * @property {string|number} id
 * @property {string} title
 * @property {string|Date} start
 * @property {string|Date} end
 * @property {string} [background]
 * @property {string} [hoverBackground]
 * @property {string} [typeLabel]
 * @property {string} [statusLabel]
 * @property {boolean} [hide]
 * @property {boolean} [hover]
 * @property {boolean} [isOneDay]
 * @property {boolean} [isNoRadius]
 */

/**
 * @typedef {Object} CalendarProps
 * @property {Date} date
 * @property {Date} [selected]
 * @property {Activity[]} [activities]
 * @property {string} [backButtonText]
 * @property {'Calendar'|'Plan'} [view]
 */

import CalendarView from './CalendarView.vue'
import PlanView from './PlanView.vue'
import { debounce } from '@/utils'

export default {
  name: 'FullCalendar',
  
  components: {
    CalendarView,
    PlanView
  },

  props: {
    selected: {
      type: String,
      default: () => ''
    },
    activities: {
      type: Array,
      default: () => []
    },
    backButtonText: {
      type: String,
      default: () => window.$t('marketing.commons.jt_800dfd')
    },
    view: {
      type: String,
      default: 'Calendar'
    }
  },

  data() {
    const date = new Date()
    return {
      currentView: this.view,
      year: date.getFullYear(),
      month: date.getMonth() + 1,
      day: date.getDate(),
      selectedDate: ''
    }
  },

  watch: {
    view(newView) {
      this.currentView = newView
    },

    selected(newSelected) {
      this.selectedDate = newSelected
    }
  },

  methods: {
    showPlanView() {
      this.currentView = 'Plan'
    },

    showCalendarView() {
      this.currentView = 'Calendar' 
    },

    onDateClick(e, data, target) {
      this.$emit('date-click', e, data, target)
    },

    onDateChange(data) {
      debounce(this.$emit('date-change', data), 300)
    },

    onActivityClick(event, data, target) {
      console.log('onActivityClick', event, data, target)
      this.$emit('activity-click', event, data, target)
    },

    nextMonth() {
      console.log(this.month,'this.month')
      let month = this.month + 1
      let year = this.year
      if (month > 12) {
        year++
        month = 1
      }
      this.month = month
      this.year = year
      console.log(year,month,'yearmonth')
      this.$emit('date-change', {year, month})
    },

    prevMonth() {
      console.log(this.month,'this.month')
      let month = this.month - 1
      let year = this.year
      if (month < 1) {
        year--
        month = 12
      }
      this.month = month
      this.year = year
      console.log(year,month,'yearmonth')
      this.$emit('date-change', {year, month})
    },

    goToday() {
      const now = new Date()
      this.year = now.getFullYear()
      this.month = now.getMonth() + 1
      this.day = now.getDate()
      this.$emit('date-click',undefined,`${this.year}-${this.month}-${this.day}`)
    },

    clearSelectDate() {
      this.selectedDate = ''
    }
  },
  mounted() {
    console.log(this.activities,'activities')
  },
}
</script>

<style lang="less">
@import '../assets/style.less';
</style> 