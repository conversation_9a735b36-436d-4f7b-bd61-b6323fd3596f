@title-color: #181c25;
@primary-color: #407fff;
@border-color: #e9edf5;

.f__fullcalendar {
  font-size: 14px;
  color: @title-color;

  ::-webkit-scrollbar {
    width: 6px;
    background: @border-color;
    height: 6px;
  }

  ::-webkit-scrollbar-button {
    height: 0;
    width: 0;
  }

  ::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:window-inactive {
    background: rgba(0, 0, 0, 0.2);
  }

  ::-webkit-scrollbar-thumb:hover,
  ::-webkit-scrollbar-thumb:active {
    background-color: rgba(0, 0, 0, 0.4);
  }

  ::-webkit-scrollbar-thumb:horizontal {
    border-width: 6px 1px 1px;
    padding: 0 0 0 100px;
    box-shadow: inset 1px 1px 0 rgba(0, 0, 0, 0.1), inset -1px 0 0 rgba(0, 0, 0, 0.07);
  }

  ::-webkit-scrollbar-corner {
    background: transparent;
  }

  * {
    box-sizing: border-box;
  }

  .f__toolbar {
    height: 40px;
    text-align: center;
    position: relative;
    border-bottom: 1px solid @border-color;

    &-content {
      margin: 0 auto;
      width: 200px;
      height: 40px;
      line-height: 40px;
      display: flex;
    }

    &-title {
      font-size: 14px;
      color: @title-color;
      margin: 0 40px;
      min-width: 75px;
      text-align: center;
    }

    &-prev {
      width: 20px;
      cursor: pointer;
      color: #999;
      background: url(../assets/arrow-left.png) center center no-repeat;
      background-size: 9px;

      &:hover {
        opacity: 0.75;
      }
    }

    &-next {
      width: 20px;
      cursor: pointer;
      color: #999;
      background: url(../assets/arrow-right.png) center center no-repeat;
      background-size: 9px;

      &:hover {
        opacity: 0.75;
      }
    }

    &-backtoday {
      position: absolute;
      top: 0;
      right: 20px;
      line-height: 40px;
      color: @primary-color;
      cursor: pointer;
    }
  }

  .f-calendar__header {
    height: 40px;
    // display: -ms-flexbox;
    // display: flex;
    border-bottom: 1px solid @border-color;

    .f-week__item {
      display: inline-block;
      width: 14.285714285714286%;
      line-height: 40px;
      text-align: center;
      border-left: 1px solid @border-color;

      &:first-child {
        border-left: 0;
      }
    }
  }

  .f-calendar__body {
    .f-day__row {
      border-bottom: 1px solid @border-color;
      display: flex;
      position: relative;
      .f-activity__row {
        position: absolute;
        width: 100%;
        top: 35px;
        border-spacing: 0;
        border-collapse: collapse;
        table-layout: fixed;
        word-break: break-all;
        z-index: 1;
      }
      .f-activity__line {
        &-hide {
          display: none;
        }
        td {
          vertical-align: top;
          padding: 0;
          a {
            display: block;
            line-height: 20px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-bottom: 7px;
            font-size: 12px;
            cursor: pointer;
          }
        }
        .f-activities__toggle-open {
          text-align: center;
          color: #91959e;
        }
      }
      &:last-child {
        border-bottom: 0;

        .f-day__item {
          &:last-child {
            border-right: 1px solid @border-color;
          }
        }
      }
    }

    .f-day__item {
      display: inline-block;
      width: 14.285714285714286%;
      min-height: 127px;
      height: 167px;
      vertical-align: top;
      position: relative;
      &:hover {
        background-color: #f0f4fc;
      }
      &:before {
        content: ' ';
        display: block;
        width: 1px;
        background-color: @border-color;
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
      }

      &:first-child {
        // border-left: 0;
        &:before {
          display: none;
        }
      }

      &.f-day__selected {
        background-color: rgba(252, 177, 88, 0.11);
      }
      &.f-day__today{
        .f-day{
          color: #FF8000;
          font-weight: bold;
          font-size: 14px;
        }
      }
      .f-day {
        display: block;
        text-align: center;
        line-height: 35px;
        padding: 0 5px;
        position: relative;

        .f-holiday {
          position: absolute;
          right: 8px;
          color: #545861;
          right: 8px;
          font-size: 12px;
          max-width: 40%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }

    .f-activities__line {
      padding: 0 6px;
      height: 20px;
      line-height: 20px;
      white-space: nowrap;
      margin-bottom: 6px;
      background-color: #eee;
      color: @title-color;
      font-size: 12px;
      cursor: pointer;
      transition: background 0.15s ease;
      position: relative;
      z-index: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      .f-activities__line-dot {
        width: 8px;
        height: 8px;
        display: inline-block;
        border-radius: 100%;
        background-color: #eee;
      }

      &-hide {
        display: none;
      }

      &__space {
        height: 20px;
        margin-bottom: 6px;
      }
    }

    .f-one__day-activity {
      &:hover {
        background-color: #f0f4fc !important;
        border-radius: 0;
      }
    }

    .f-activities {
      transition: all 0.26s ease;

      .f-activities__toggle {
        position: absolute;
        bottom: 2px;
        left: 0;
        width: 100%;
        font-size: 12px;
        color: @primary-color;
        text-align: center;
        cursor: pointer;
        line-height: 26px;
        background-color: #fff;
        border-left: 1px solid @border-color;
        span {
          display: block;
        }

        .f-activities__toggle-close {
          display: none;
        }
      }

      &.show {
        background-color: #fff;
        position: relative;
        z-index: 2;
        padding-bottom: 20px;
        border-bottom: 1px solid @border-color;

        &:before {
          content: ' ';
          position: absolute;
          left: 0;
          width: 1px;
          height: 100%;
          background-color: @border-color;
        }

        .f-activities__line-hide {
          display: block;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        .f-activities__toggle {
          &-open {
            display: none;
          }

          &-close {
            display: block;
          }
        }
        .f-activities__line__space {
          &.f-activities__line-hide {
            display: none;
          }
        }
      }
    }

    .f-activities__last_date {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      border-top-right-radius: 10px;
      border-bottom-right-radius: 10px;
    }
  }

  // 计划视图
  .f--plan-view {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .f__calendar-flex {
    flex: 1;
    // display: flex;
    overflow-y: auto;
    position: relative;
  }

  .f__plan-body {
    // flex: 1;
    overflow: hidden;
    overflow-x: auto;
    position: absolute;
    left: 420px;
    top: 0;
    right: 0;
    display: flex;
    min-height: 100%;
  }

  .f__plan-content {
    transition: all 0.3s linear;
    opacity: 1;
    position: relative;

    &[blur='true'] {
      // background: hsla(0, 0%, 100%, 0.6);
      filter: blur(10px);
      opacity: 0.5;
    }
  }

  .f__plan-scroll {
    // flex: 1;
    background-image: linear-gradient(0deg, #e9edf5 1px, transparent 0),
      linear-gradient(90deg, #e9edf5 1px, transparent 0);
    background-size: 50px 41px;
    background-position: -1px 0px;
    position: relative;
  }
  .f__plan-today-col {
    position: absolute;
    width: 50px;
    height: 100%;
    bottom: 0;
    top: 0;
    background-color: rgba(252, 177, 88, 0.11);
    text-align: center;
    line-height: 40px;
    font-size: 13px;
    color: #ff8000;
    z-index: 0;
  }
  .f__plan-mask {
    position: absolute;
    width: 100%;
    top: 0;
    left: 0;
    bottom: 0;
    background-color: #fff;
    filter: blur(30px);
    transition: all 0.3s linear;
  }

  .f__plan-row {
    display: flex;
    border-bottom: 1px solid @border-color;
    position: relative;
    z-index: 1;
    &.f__planline-hover,
    &:hover {
      background-color: #f0f4fc;
    }
  }

  .f__plan-line {
    width: auto;
    position: absolute;
    top: 50%;
    transform: translate(0, -50%);
    height: 16px;
    line-height: 16px;
    text-align: right;
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;
    background-color: #e6e6e6;
    cursor: pointer;
    overflow: hidden;
    &-process {
      width: 0;
      padding: 0 3px;
      height: 16px;
      transition: all 0.3s 0.3s ease;
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }
    &-name {
      position: absolute;
      padding-right: 7px;
      right: 0;
      top: 50%;
      transform: translate(0, -50%);
      color: @title-color;
      font-size: 12px;
    }
    &.no__radius {
      border-radius: 0;
      // .f__plan-line-process{
      //   border-radius: 0;
      // }
    }
  }

  .f__plan-table {
    transition: all 0.3s ease;
    background-image: linear-gradient(0deg, #e9edf5 1px, transparent 0),
      linear-gradient(90deg, #e9edf5 1px, transparent 0);
    background-size: 250px 41px;
    background-position: -1px 0px;
    &.shadow {
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.12);
    }
  }

  .f__plan-col {
    flex: 1 0 50px;
    height: 40px;
    border-right: 1px solid @border-color;

    // &.f__active {
    //   background-color: #fff9f1;
    // }

    &:last-child {
      border-right: 0;
    }
  }

  .f__plan-head {
    display: flex;
    border-bottom: 1px solid @border-color;

    .f__plan-th {
      flex: 1 0 50px;
      width: 50px;
      height: 40px;
      line-height: 40px;
      text-align: center;
      border-right: 1px solid @border-color;
      font-size: 13px;
      color: @title-color;

      &.f__gray {
        color: #c1c5ce;
      }

      &.f__active {
        color: #ff8000;
      }

      &:last-child {
        border-right: 0;
      }
    }
  }

  .f__plan-table {
    width: 420px;
    vertical-align: top;
    border-right: 1px solid @border-color;
    background-color: #fafcff;
    min-height: 100%;
    &-dot {
      display: inline-block;
      width: 8px;
      height: 8px;
      background-color: #e8aef6;
      border-radius: 100%;
      margin-right: 5px;
    }
  }

  .f__plan-table-head {
    display: flex;
    background-color: #f0f4fc;
    align-items: center;
    border-bottom: 1px solid @border-color;
  }

  .f__plan-table-th {
    height: 40px;
    line-height: 40px;
    width: 170px;
    text-align: center;
    color: @title-color;
    font-size: 13px;
    border-left: 1px solid @border-color;

    &:first-child {
      flex: 1;
      border-left: 0;
    }
  }

  .f__plan-table-tr {
    display: flex;
    border-bottom: 1px solid @border-color;
    cursor: pointer;
    &.f__planline-hover,
    &:hover {
      background-color: #f0f4fc;
    }
  }

  .f__plan-table-td {
    color: @title-color;
    font-size: 13px;
    width: 170px;
    height: 40px;
    line-height: 40px;
    padding: 0 10px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    border-left: 1px solid @border-color;

    &:first-child {
      flex: 1;
      border-left: 0;
    }
  }
}
