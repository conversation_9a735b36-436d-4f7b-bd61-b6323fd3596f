<template>
  <Dialog
    class="dl-step-page form"
    append-to-body
    :close-on-click-modal="false"
    :width="'920px'"
    :title="title"
    :visible.sync="ShowCrmDialog2"
  >
    <div class="dl-content-wrapper">
      <crm-mapping
        ref="crmMapping"
        :activity-field="activityFieldWithDefault"
        :object-api-name="objectApiName"
        :object-type.sync="objectType2"
        :mapping-list.sync="mappingList2"
        :default-mapping="defaultMapping"
        :object-name="objectName"
        :target-object-name="targetObjectName"
        :target-object-tips="targetObjectTips"
        :target-fields="targetFields"
        :show-object-target="showObjectTarget"
        :select-member-field="selectMemberField"
        :strict-map-by-type="strictMapByType"
        :is-show-field-scene="isShowFieldScene"
        @completeMemberFieldLoading="memberFieldLoading = false"
        @update:objectType="handleObjectTypeChange"
      />
    </div>
    <div
      slot="footer"
      class="dialog-footer"
    >
      <fx-button
        class="btn btn-blue"
        type="primary"
        size="small"
        :loading="selectMemberField && memberFieldLoading"
        @click="submit"
      >
        {{ $t('marketing.commons.qd_38cf16') }}
      </fx-button>
      <fx-button
        class="btn btn-white"
        size="small"
        @click="cancel"
      >
        {{ $t('marketing.commons.qx_625fb2') }}
      </fx-button>
    </div>
  </Dialog>
</template>

<script>
import CrmMapping from './crm-mapping/index'
import { filterSource } from './const'

export default {
  props: {
    title: {
      type: String,
      default: $t('marketing.commons.bdsjcr_63e6f8'),
    },
    showCrmDialog: {
      type: Boolean,
      default: () => false,
    },
    activityField: {
      type: Array,
      default: () => [],
    },
    defaultMapping: {
      type: Array,
      default: () => [],
    },
    mappingList: {
      type: Array,
      default: () => [],
    },
    objectApiName: {
      type: String,
      default: () => '',
    },
    objectType: {
      type: String,
      default: () => '',
    },
    objectName: {
      type: String,
      default: $t('marketing.commons.bd_eee1e2'),
    },
    targetObjectName: {
      type: String,
      default: $t('marketing.commons.xs_48d2da'),
    },
    targetObjectTips: {
      type: String,
      default: $t('marketing.commons.btzdxyyysz_6b8e24'),
    },
    targetFields: {
      type: Array || Boolean,
      default: false,
    },
    showObjectTarget: {
      type: Boolean,
      default: true,
    },
    selectMemberField: {
      type: Boolean,
      default: false,
    },
    crm_showTips: {
      type: Boolean,
      default: () => false,
    },
    crm_isSubmited: {
      type: Boolean,
      default: () => false,
    },
    isLine: {
      type: Boolean,
      default: false,
    },
    strictMapByType: {
      type: Boolean,
      default: false,
    },
    isShowFieldScene: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      memberFieldLoading: true,
    }
  },
  computed: {
    ShowCrmDialog2: {
      get() {
        return this.showCrmDialog
      },
      set() {
        this.$emit('update:showCrmDialog', false)
      },
    },
    mappingList2: {
      get() {
        return this.mappingList
      },
      set(newValue) {
        this.$emit('update:mappingList', newValue)
      },
    },
    objectType2: {
      get() {
        return this.objectType
      },
      set(newValue) {
        this.$emit('update:objectType', newValue)
      },
    },
    activityFieldWithDefault() {
      return this.isLine ? filterSource(this.activityField) : this.activityField
    },
  },
  watch: {
    // objectType(newVal) {
    //   this.objectType2 = newVal
    // },
    showCrmDialog() {
      // console.log('ShowCrmDialog', newVal);
    },

    objectApiName() {},
  },
  mounted() {
    // const notifyItem = this.notifyItem;
    // this.$nextTick(() => {
    //   bus.$emit('notify:chosenItem', notifyItem);
    // });
  },
  methods: {
    handleObjectTypeChange(val) {
      this.$emit('update:objectType', val)
    },
    // closeNotifyDialog() {
    //   this.$emit('update:notifyDialogVisible', false);
    // },
    submit() {
      this.$refs.crmMapping.submit(() => {
        this.$emit('update:showCrmDialog', false)
        this.$emit('update:crm_showTips', false)
        this.$emit('update:crm_isSubmited', true)
        this.$emit('submit')
      })
    },
    cancel() {
      this.$emit('update:showCrmDialog', false)
    },
  },
  components: {
    Button: FxUI.Button,
    Dialog: FxUI.Dialog,
    CrmMapping,
  },
}
</script>

<style lang="less">
.dl-step-page {
  .el-dialog__body {
    padding: 0;
    max-height: 707px;
  }
  .dl-content-wrapper {
    overflow: hidden;
  }
}
</style>
