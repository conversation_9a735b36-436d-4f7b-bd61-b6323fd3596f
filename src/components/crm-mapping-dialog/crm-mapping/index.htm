<div class="activity-crm-mapping">
  <!-- loading蒙层 -->
  <div class="km-g-loading-mask" v-if="flag_showLoadingMask">
    <span class="loading"></span>
  </div>
  <!-- 主要内容 -->
  <div class="w-crmsetting-wrapper">
    <div class="w-crmsetting-content">
      <div class="tab_wrapper tab_mankeep">
        <!-- 禁用蒙层 -->
        <div class="init-error-mask" v-if="flag_showInitIllegalMask_mankeep">
          <div class="text-area" v-html="text_initIllegalTips_mankeep"></div>
        </div>
        <div class="tab_content">
          <div class="w-crmsetting-object-map" style="margin-top: 0;">
            <div v-if="showObjectTarget" class="content-item">
              <div class="header">
                <div class="title">{{$t('marketing.commons.bdbcd_72e2a5')}}</div>
              </div>
                <div class="body">
                  <div class="crm-type">
                    <div class="label">{{$t('marketing.commons.ywlx_09ab42')}}</div>
                    <div class="selector">
                      <fx-select
                        class="el-select"
                        :placeholder="$t('marketing.commons.qxz_708c9d')"
                        size="mini"
                        :options="data_lead_object_types"
                        v-model="data_lead_object_type_name" style="width: 346px;"
                      />
                    </div>
                  </div>
                  <p v-if="data_lead_object_type_error && !data_lead_object_type_name" style="color:#f27474;font-size: 12px">{{$t('marketing.components.crm_mapping_dialog.qxzywlx_81f1a4')}}</p>
                </div>
            </div>
            <div class="content-item">
              <div class="header">
                <div class="title">
                  {{$t('marketing.commons.zdys_e5b2ad')}}
                  <PictureStandard v-if="!selectMemberField" style="display: inline-block;font-size: 12px;" :zIndex="3000" :text="$t('marketing.commons.cktj_26e86d')" :imageUrls="[crmMappingExamplePng]" />
                </div>
                <div class="tips">{{targetObjectTips}}
                  <span v-if="data_lead_notNull_crm_objects.length > 0">
                    {{$t('marketing.commons.qqbdbtxszl_99c788', {data: ({'option0': targetObjectName})})}}{{_.map(data_lead_notNull_crm_objects, function(item){return item.fieldCaption}).join('，')}}
                  </span>
                </div>
              </div>
              <div class="body">
                <div class="table-form object-map-form">
                  <div class="tr tr_1">
                    <div class="td td_2">
                      <div class="thead thead_1" :style="{width: isShowFieldScene ? '400px' : '300px'}">{{objectName}}</div>
                      <div class="thead thead_2">{{$t('marketing.commons.ysd_4fb408')}}</div>
                      <div class="thead thead_3">{{targetObjectName}}</div>
                      <div class="thead thead_4">{{$t('marketing.commons.sfbt_04d815')}}</div>
                      <div class="thead thead_5">{{$t('marketing.commons.cz_2b6bc0')}}</div>
                    </div>
                  </div>
                  <div class="tr tr_2">
                    <div class="td td_2">
                      <div class="input-box">
                        <inputItem
                          v-for="(item, index) in info_lead_formList"
                          :info.sync="item"
                          :itemIndex="index"
                          :select-data="member_info_lead_formList"
                          :is-show-field-scene="isShowFieldScene"
                          @custom-select="handleCustomSelectChange"
                          @onChange="changeSelect"
                          @deleteInputItem="event_deleteInputItem"
                        ></inputItem>
                      </div>
                      <div class="add-box">
                        <div class="km-a-btn" @click="event_addInputItem(3)">+ {{$t('marketing.commons.xzysgx_98c56d')}}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- <crmtypes-selector></crmtypes-selector> -->
</div>
