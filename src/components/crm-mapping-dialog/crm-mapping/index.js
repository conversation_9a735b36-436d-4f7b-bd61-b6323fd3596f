import './index.less'
import _ from 'lodash'
import Vue from 'vue'
import IndexTpl from './index.htm'
import baseMixin from '@/mixins/base-mixin'
import crmMappingExamplePng from '@/assets/images/crm-mapping-dialog/input-object-reference.png'
import InputItem from './component/input-item/input-item'
import { confirm, alert } from '@/utils/globals'
import http from '@/services/http/index'
import PictureStandard from '@/components/PictureStandard/index.vue'

const FIELDS_TYPE_RULE = {
  2: 'text',
  3: 'multi_text',
  4: 'number',
  5: 'number',
  6: 'number',
  7: 'date_time',
  8: 'select_one',
  9: 'select_manny',
  10: 'image',
  13: 'boolean',
  18: 'phone_number',
  19: 'email',
}

export default {
  template: IndexTpl,
  props: {
    activityField: {
      type: Array,
      default: () => [],
    },
    mappingList: {
      type: Array,
      default: () => [],
    },
    defaultMapping: {
      type: Array,
      default: () => [],
    },
    objectApiName: {
      type: String,
      default: () => '',
    },
    objectType: {
      type: String,
      default: () => '',
    },
    objectName: {
      type: String,
      default: $t('marketing.commons.bd_eee1e2'),
    },
    targetObjectName: {
      type: String,
      default: $t('marketing.commons.xs_48d2da'),
    },
    targetObjectTips: {
      type: String,
      default: $t('marketing.commons.btzdxyyysz_6b8e24'),
    },
    targetFields: {
      type: Array || Boolean,
      default: () => [],
    },
    showObjectTarget: {
      type: Boolean,
      default: true,
    },
    selectMemberField: {
      type: Boolean,
      default: false,
    },
    strictMapByType: {
      type: Boolean,
      default: false,
    },
    isShowFieldScene: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      flag_showLoadingMask: true, // 标识 - 是否显示“加载中”蒙层
      flag_showInitIllegalMask_mankeep: false, // 标识 - 是否显示“包含非法字段，无法使用”蒙层
      text_initIllegalTips_mankeep: '', // “包含非法字段，无法使用”蒙层中的提示语
      data_lead_formList: [], // 线索表单的所有行
      data_lead_map_crm: [], // 线索表单中，crm字段的映射字典
      data_lead_map_mankeep: [], // 线索表单中，客脉字段的映射字典
      data_lead_object_types: [], // 线索表单中，所有CRM业务类型的数组 (全局搜索用data_${item.name}_object_types)
      data_lead_object_type_name: [], // 线索表单中，选中的CRM业务类型的ObjectName
      data_lead_object_type_error: false, // 业务类型是否错误
      info_lead_formList: [], // 处理后的线索表单的所有行
      data_lead_notNull_crm_objects: [], // 线索表单中，crm的必填字段组成的对象数组
      is_finish_init: false, // 是否完成初始化动作
      ajaxing_updateSaveConfig: false, // 是否正在请求更新
      validate_error_objects_mankeep: [], // 表单校验错误汇总(人脉tab下)
      listener_validating_mankeep: false, // 表单校验监听器(人脉tab下) false-未启动 true-启动
      select_key_map: {}, // 记录当前下拉框选中项
      crmMappingExamplePng,
      member_info_lead_formList: [],
    }
  },
  mixins: [baseMixin],
  components: {
    Tooltip: FxUI.Tooltip,
    eSwitch: FxUI.Switch,
    InputItem,
    Select: FxUI.Select.components.ElSelect,
    Option: FxUI.Select.components.ElSelect.components.ElOption,
    PictureStandard,
  },
  computed: {
    mappingList2: {
      get() {
        return this.mappingList
      },
      set(newValue) {
        this.$emit('update:mappingList', newValue)
      },
    },
    objectType2: {
      get() {
        return this.objectType
      },
      set(newValue) {
        this.$emit('update:objectType', newValue)
      },
    },
  },
  watch: {
    objectApiName() {
      console.log('this.objectApiName', this.objectApiName)
    },
    activityField() {
      console.log(this.activityField)
      this.initMappingList()
    },
    // data_lead_object_type_name(newVal) {
    //   this.objectType2 = newVal; // 线索人表单中，选中的CRM业务类型的ObjectName
    // },
    // switch_crm_customer: {
    //   handler() {
    //     this.ajax_updateSaveConfig('customer');
    //   },
    // },
    switch_crm_mankeep: {
      handler() {
        this.ajax_updateSaveConfig('mankeep')
      },
    },
    info_lead_formList: {
      handler(val) {
        // 这里不能用oldVal,因为是对象内的深度变更，oldVal的对象引用就是当前的val
        if (!_.isEqual(val, this.old_info_lead_formList)) {
          this.old_info_lead_formList = _.cloneDeep(val)
          if (this.listener_validating_mankeep) {
            this.tool_validate(
              'mankeep',
              this.tool_getSubmitData(),
              'fromListener',
            )
          }
          this.changeCrmFieldOptions()
        }
      },
      deep: true,
    },
    targetFields(val) {
      if (val) {
        this.init()
      }
    },
  },
  methods: {
    handleCustomSelectChange(index, itemIndex) {
      this.$set(this.info_lead_formList, itemIndex, {
        ...this.member_info_lead_formList[index],
        _memberCustomSelect: true,
      })
    },
    autoMapByName(mapList, fieldList) {
      const clueFieldMap = { // 表单字段名到线索池字段名的对应
        name: 'SalesClueName',
        companyName: 'Company',
        email: 'Email',
        position: 'Position',
        phone: 'Mobile',
      }

      const memberFieldMap = { // 表单字段名到线索池字段名的对应
        name: 'Name',
        company: 'CompanyName',
        email: 'Email',
        position: 'Position',
        phone: 'Phone',
      }

      const fieldMap = this.selectMemberField ? memberFieldMap : clueFieldMap

      // console.log('automapping:', mapList, fieldList)
      _.forEach(mapList, kmap => { // 处理现有映射表，映射字段为 null 的项
        const fieldName = fieldMap[kmap.mankeepFieldName]
        // 自动映射  只包括以上两个map中的字段
        if (!kmap.crmFieldName && fieldName) { // 原本没有映射字段，且能按字段名匹配到可映射的字段
          _.forEach(fieldList, field => { // 在可映射的目标字段列表中，存在上述可映射字段
            if (fieldName.toLocaleLowerCase() === (field.fieldName && field.fieldName.toLocaleLowerCase())) {
              kmap.crmFieldName = field.fieldName
            }
            // fieldName.toLocaleLowerCase() === field.fieldName.toLocaleLowerCase() && console.log(fieldName.toLocaleLowerCase() === field.fieldName.toLocaleLowerCase(),fieldName.toLocaleLowerCase(), field.fieldName.toLocaleLowerCase(),kmap.crmFieldName, 666)
          })
        }
        // 这里不能使用else if 否则如果进入上层if这里会不执行 逻辑错误
        if (!kmap.crmFieldName && kmap.mankeepFieldType === 'text') { // text类型字段，名字相同则自动映射
          _.forEach(fieldList, field => {
            if (kmap.defaultValue === field.fieldCaption) {
              kmap.crmFieldName = field.fieldName
            }
          })
        } else if (!kmap.crmFieldName && kmap.mankeepFieldType) {
          _.forEach(fieldList, field => {
            if (kmap.defaultValue === field.fieldCaption && kmap.mankeepFieldType === field.fieldType) {
              kmap.crmFieldName = field.fieldName
            }
          })
        }
      })
      _.forEach(this.activityField, item => { // 处理表单字段，向映射表中补充缺少的项
        const fieldName = fieldMap[item.apiName || item.fieldName] // 有可映射字段名，且原有映射表中缺少此项，才做后续处理
        if (
          fieldName
          && _.find(mapList, kmap => kmap.mankeepFieldName === (item.apiName || item.fieldName)) < 0
        ) {
          _.forEach(fieldList, field => {
            if (fieldName === field.fieldName && item.label !== field.fieldCaption) {
              mapList.push({
                // TODO 这里的数据格式需要和业务需求同步修改
                mankeepFieldName: item.apiName,
                defaultValue: item.label || item.fieldCaption,
                crmFieldName: fieldName,
                valuesOptions: null,
                modifiable: true,
                mankeepFieldOptions: [],
                mankeepFieldType: item.type,
              })
            }
          })
        }
      })
    },
    initMappingList() {
      this.data_lead_formList = this.mappingList2 && this.mappingList2.length > 0 ? this.mappingList2 : this.defaultMapping
      this.data_lead_object_type_name = this.objectType // 线索人表单中，选中的CRM业务类型的ObjectName
      this.data_lead_map_mankeep = this.formatActivityField(this.activityField)
      const keysMap = {}
      // 营销通表单字段data_lead_map_mankeep  映射表data_lead_formList
      _.each(this.data_lead_map_mankeep, item1 => {
        const idx = _.findIndex(
          this.data_lead_formList,
          item2 => item2.mankeepFieldName == item1.fieldName,
        )
        keysMap[item1.fieldName] = ''
        if (idx == -1) {
          this.data_lead_formList.push({
            mankeepFieldName: item1.fieldName,
            mankeepFieldOptions: item1.fieldOptions,
            defaultValue: item1.fieldCaption,
            crmFieldName: null,
            modifiable: true,
            mankeepFieldType: item1.fieldType, // 数据类型
          })
        } else {
          this.data_lead_formList[idx] = {
            ...this.data_lead_formList[idx],
            defaultValue: item1.fieldCaption,
            mankeepFieldOptions: item1.fieldOptions,
            mankeepFieldType: item1.fieldType,
          }
        }
      })

      this.autoMapByName(this.data_lead_formList, this.data_lead_map_crm)

      this.select_key_map = keysMap
      // 清除被删除的数据
      const _wantDelIndexes = []
      _.each(this.data_lead_formList, (item2, index2) => {
        if (
          item2.mankeepFieldName
          && !_.find(
            this.data_lead_map_mankeep,
            item1 => item1.fieldName == item2.mankeepFieldName,
          )
        ) {
          _wantDelIndexes.push(index2)
        }
      })
      let deletedCount = 0
      _.each(_wantDelIndexes, item => {
        this.data_lead_formList.splice(item - deletedCount, 1)
        deletedCount++
      })
      // this.data_lead_formList = _.sortBy(
      //   this.data_lead_formList,
      //   item => item.mankeepFieldName,
      // );
      this.get_formList()
    },
    formatActivityField(activityField) {
      const _return = []
      _.each(activityField, item => {
        _return.push({
          fieldName: item.apiName || item.fieldName,
          fieldCaption: item.label || item.fieldCaption,
          fieldType: item.type || (item.fieldType ? (FIELDS_TYPE_RULE[item.fieldType] || item.fieldType) : 'text'),
          fieldOptions: item.options || (item.enumDetails && item.enumDetails.length ? item.enumDetails.map(option => ({
            label: option.itemName,
            value: option.itemCode,
          })) : []),
        })
      })
      return _return
    },
    changeCrmFieldOptions() { // 重置选项卡子菜单
      this.info_lead_formList = _.map(this.info_lead_formList, item => {
        let crmFieldOptions = []
        if (item.keyType === 'select') {
          // 重置子选项
          // if (item.valueName != this.select_key_map[item.keyName]) {
          //   this.select_key_map[item.keyName] = item.valueName;
          //   item.mankeepFieldOptions = _.map(item.mankeepFieldOptions || [], v => ({
          //     ...v,
          //     valueName: v.valueName ? v.valueName : '',
          //   }));
          // }
          _.map(item.valueOption, v => {
            if (item.valueName === v.fieldName) {
              crmFieldOptions = item.keyDataType === 'select_manny' ? [...(v.enumDetails || [])] : [{
                itemCode: '',
                itemName: $t('marketing.commons.k_b7612b'),
                enumDetailID: '',
              }, ...(v.enumDetails || [])]
            }
          })
        }
        return {
          ...item,
          crmFieldOptions,
        }
      })
    },
    changeSelect({ item, index }) {
      console.log('item: ', item)
      const { mankeepFieldOptions } = item
      if (mankeepFieldOptions && mankeepFieldOptions.length) {
        item.mankeepFieldOptions = _.map(mankeepFieldOptions || [], v => ({
          ...v,
          valueName: '',
        }))
      }
      let crmFieldOptions = []
      if (item.keyName) {
        return
      }

      _.map(item.valueOption, v => {
        // 寻找映射字段
        if (item.valueName === v.fieldName) {
          if (v.fieldType == 8 || v.fieldType == 9) {
            item.keyType = 'select'
            if (!item.mankeepFieldOptions || item.mankeepFieldOptions && item.mankeepFieldOptions.length == 0) {
              item.keyDataType = v.fieldType == 8 ? 'select_one' : 'select_manny'
            }
            crmFieldOptions = v.enumDetails || []
          } else {
            item.keyType = 'input'
            // 重置子选项
          }
        }
      })
      if (item.keyType === 'select') {
        item.keyInputDefault = item.keyDataType === 'select_manny' ? [] : crmFieldOptions[0].itemCode
      } else {
        item.keyInputDefault = ''
        item.keyInputDefaultId = ''
      }
      item.crmFieldOptions = crmFieldOptions
      this.info_lead_formList[index] = item
    },

    dealTargetFieldData(data, callback) {
      const _resData = [{
        fieldCaption: $t('marketing.commons.k_b7612b'),
        fieldName: null,
        fieldType: 0,
        isNotNull: false,
      }]
      data.forEach(item => {
        if (
          item.fieldName !== 'leads_pool_id'
          && item.fieldName !== 'marketing_event_id'
          && item.fieldName !== 'data_own_organization'
          && item.fieldName !== 'marketing_promotion_source_id'
        ) {
          _resData.push(item)
        }
      })
      this.data_lead_map_crm = _resData
      // this.autoMapByName(this.mappingList && this.mappingList.length ? this.mappingList:this.data_lead_formList, results.data);
      this.is_finish_init = true // 完成初始化
      callback && callback()
    },
    /**
     * 接口：拉取所有设置
     * 文档：http://wiki.firstshare.cn/pages/viewpage.action?pageId=76619114
     * 备注：后台为了冗余，多个字段设计成数组。前端这边先不做冗余，直接取[0]。
     */
    ajax_getCrmObjectFields(callback) {
      if (this.targetFields) {
        this.dealTargetFieldData(this.targetFields, callback)
        this.flag_showLoadingMask = false
        return
      }
      http
        .getCrmObjectFields({
          objectApiName: this.objectApiName || 'LeadsObj',
          recordType: 'default__c',
        })
        .then(
          results => {
            this.flag_showLoadingMask = false
            if (results && results.errCode === 0) {
              this.dealTargetFieldData(results.data || [], callback)
            }
          },
          () => {
            this.flag_showLoadingMask = false
            this.is_finish_init = true
            alert($t('marketing.commons.cshsbqsxym_772779'))
          },
        )
    },
    ajax_listCrmObjectRecordTypes() {
      const ObjectApiMap = [
        {
          label: $t('marketing.commons.xs_ad46a9'),
          name: 'lead',
          apiName: 'LeadsObj',
        },
      ]
      _.each(ObjectApiMap, item => {
        http.listCrmObjectRecordTypes({ objectApiName: this.objectApiName || item.apiName }).then(
          results => {
            if (results && results.errCode == 0) {
              const lists = results.data.listCrmObjectRecordTypesUnitResults || []
              this[`data_${item.name}_object_types`] = lists.map(el => ({ ...el, value: el.apiName }))
              // 只有一种业务类型时默认选中
              if (lists.length === 1 && !this.data_lead_object_type_name) {
                this.data_lead_object_type_name = lists[0].apiName
              }
              // console.log('listCrmObjectRecordTypes', this[`data_${item.name}_object_types`]);
            }
          },
          () => {
            alert($t('marketing.commons.cshsbqsxym_772779'))
          },
        )
      })
    },

    /**
     * 获取表单数据
     * 包括info_customer_formList、info_contact_formList、info_lead_formList
     */
    get_formList() {
      // this.tool_formatList(1);
      // this.tool_formatList(2);
      this.tool_formatList(3)

      // 会员映射表单相关逻辑
      if (this.selectMemberField) {
        const defaultFieldKeys = ['name', 'phone', 'company', 'email', 'position']
        const restFieldKeys = this.mappingListCopy
          .map(item => item.mankeepFieldName)
          .filter(item => !defaultFieldKeys.includes(item))
        this.member_info_lead_formList = _.cloneDeep(this.info_lead_formList)
        this.info_lead_formList = this.info_lead_formList
          .filter(item => [...defaultFieldKeys, ...restFieldKeys].includes(item.keyName))
          .map(item => (restFieldKeys.includes(item.keyName) ? { ...item, _memberCustomSelect: true } : item))
          .sort((c, d) => (c.keyName === 'name' ? -1 : 1))
        this.$emit('completeMemberFieldLoading')
      }
    },
    /**
     * 提交表单内容
     * @param {*} whichTab customer-客户 mankeep-人脉
     */
    event_submit_formList(whichTab, sucCallback) {
      const fieldMappingConfigs = this.tool_getSubmitData()
      if (!this.data_lead_object_type_name && !this.selectMemberField) {
        this.data_lead_object_type_error = true
        FxUI.Message.warning($t('marketing.components.crm_mapping_dialog.qxzywlx_81f1a4'))
      }
      if (this.tool_validate(whichTab, fieldMappingConfigs, 'fromSubmit') && (this.data_lead_object_type_name || this.selectMemberField)) {
        this.mappingList2 = fieldMappingConfigs[0].fieldMappings || []
        this.objectType2 = this.data_lead_object_type_name
        sucCallback && sucCallback()
      } else {
        // console.log('===do not submit===');
      }
    },
    submit(callback) {
      this.event_submit_formList('mankeep', () => {
        callback && callback()
      })
    },
    event_back() {
      confirm(`${$t('marketing.commons.sffhsyqqrs_a4e673')}?`, $t('marketing.commons.ts_02d981'), {
        confirmButtonText: $t('marketing.commons.qd_38cf16'),
        cancelButtonText: $t('marketing.commons.qx_625fb2'),
        type: 'warning',
      })
        .then(() => {
          this.$router.push({ name: 'home' })
        })
        .catch(() => { })
    },
    /**
     * 事件：新增表单项目
     * @param {*} whichModel 哪个模块 1-客户 2-联系人
     */
    event_addInputItem(whichModel) {
      const me = this
      const _to = [
        '',
        'info_customer_formList',
        'info_contact_formList',
        'info_lead_formList',
      ][whichModel]
      me[_to].push({
        model: whichModel,
        keyType: this.selectMemberField ? 'custom_select' : 'input',
        valueType: 'select',
        valueOption: [
          {},
          me.data_customer_map_crm,
          me.data_contact_map_crm,
          me.data_lead_map_crm,
        ][whichModel],
        valueName: null,
      })
    },
    /**
     *删除表单中某一栏
     * @param {*} info: {
     *   model: '', // 属于哪个模块 1-客户 2-联系人 3-线索
     *   index: '', // 在该模块下的序号
     * }
     */
    event_deleteInputItem(info) {
      this.info_lead_formList.splice(info.index, 1)
    },
    /**
     * 检查是否包含必填的非法字段
     * 若包含，直接报错，页面不允许使用，直到将此字段在CRM处去除
     * 非法字段(fieldType)：附件(17)
     * 在'客户'、'人脉'tab需要分别校验
     */
    tool_checkIllegalField() {
      const tabs = ['customer', 'mankeep'] // 包含'客户'、'人脉'两个tab
      const _illegalData = [{}, {}, {}] // 报错提示语集合
      // 所有非法字段类型
      const illegalFieldObject = [
        {
          fieldType: 17,
          fieldTypeLabel: $t('marketing.commons.fj_c9a6ee'),
        },
      ]
      // 需要检查的对象
      const needCheckObjects = [
        this.data_customer_map_crm,
        this.data_contact_map_crm,
        this.data_lead_map_crm,
      ]
      // do check
      _.each(illegalFieldObject, iItem => {
        _.each(needCheckObjects, (nItem, nIndex) => {
          _.each(nItem, item => {
            if (item.fieldType == iItem.fieldType && item.isNotNull) {
              _illegalData[nIndex].belongTab = [
                'customer',
                'customer',
                'mankeep',
              ][nIndex]
              // 对应的tab会展示非法字段提示蒙层，已修改过的值就不再复制
              !this[
                `flag_showInitIllegalMask_${_illegalData[nIndex].belongTab}`
              ]
                && (this[
                  `flag_showInitIllegalMask_${_illegalData[nIndex].belongTab}`
                ] = true)
              _illegalData[nIndex].belongModel = [
                $t('marketing.commons.kh_9815cb'),
                $t('marketing.commons.lxr_a7628d'),
                $t('marketing.commons.xs_48d2da'),
              ][nIndex]
              !_illegalData[nIndex].field && (_illegalData[nIndex].field = [])
              _illegalData[nIndex].field.push({
                fieldCaption: item.fieldCaption,
                fieldTypeLabel: iItem.fieldTypeLabel,
              })
            }
          })
        })
      })
      const textInitIllegalTips = ['', ''] // 输出的提示语，[0]为客户的，[1]为人脉的
      _.each(tabs, (tItem, tIndex) => {
        textInitIllegalTips[tIndex] = $t('marketing.commons.cshsbzxzdy_3e8676', {
          data: ({
            option0: tItem === 'customer' ? $t('marketing.commons.kh_ff0b20') : $t('marketing.commons.rm_46fe08'),
            option1: _.map(
              illegalFieldObject,
              'fieldTypeLabel',
            ).join('、'),
          }),
        })
        _.each(_illegalData, item => {
          const fieldTipsArray = []
          if (item.belongTab != tItem) {
            return
          }
          _.each(item.field, fItem => {
            fieldTipsArray.push(
              $t('marketing.commons.dzdlxw_c5f9e5', {
                data: {
                  option0: `[${fItem.fieldCaption}]`,
                  option1: `[${fItem.fieldTypeLabel}]`,
                },
              }),
            )
          })
          if (item.belongModel) {
            textInitIllegalTips[tIndex] += `${$t('marketing.commons.z_5a1a0e', { data: { option0: item.belongModel } })}，${fieldTipsArray.join('、')}。<br>`
          }
        })
        textInitIllegalTips[tIndex] += $t('marketing.commons.qlxggsdgly_1eadba')
        this[`text_initIllegalTips_${tItem}`] = textInitIllegalTips[tIndex]
      })
    },
    /**
     * 工具：格式化表单列表数据
     * @param {*} whichModel 哪种模块 1-客户 2-联系人 3-线索
     * _item: {
     *   model: '', // 模块 1-客户 2-联系人 3-线索
     *   keyName: '', // 客户字段的name
     *   keyType: 'text', // 客户字段的类型 text-文本展示 input-单行输入
     *   keyText: '', // 客户字段为文本展示下的内容，与keyInputDefault不同时存在
     *   keyInputDefault: '', // 客户字段为单行输入下的内容，与keyText不同时存在
     *   valueType: 'select', // CRM客户字段的类型 text-文本展示 select-下拉选择
     *   valueOption: [], // CRM客户字段下拉选择的选择项，与valueText不同时存在
     *   valueName: '',  // CRM客户字段下拉选择的选中项，与valueText不同时存在
     *   valueText: '', // CRM客户字段文本展示的内容，与valueOption不同时存在
     *   keyError: false, // 客户字段显示错误样式
     *   valueError: false, // CRM客户字段显示错误样式
     * }
     */
    tool_formatList(whichModel) {
      const me = this
      const _from = [
        '',
        'data_customer_formList',
        'data_contact_formList',
        'data_lead_formList',
      ][whichModel]
      const _to = [
        '',
        'info_customer_formList',
        'info_contact_formList',
        'info_lead_formList',
      ][whichModel]
      me[_to] = []
      _.each(me[_from], item => {
        const _item = {}
        _item.model = whichModel
        _item.keyName = item.mankeepFieldName

        const findObj = _.find(this.data_lead_map_crm, k => k.fieldName == item.crmFieldName)
        if (findObj && ((item.mankeepFieldType === 'select_one' && findObj.fieldType != 8) || (item.mankeepFieldType === 'select_manny' && findObj.fieldType != 9))) {
          item.crmFieldName = ''
        }
        _item.keyType = (item.mankeepFieldType === 'select_one' || item.mankeepFieldType === 'select_manny') ? 'select' : item.mankeepFieldName ? 'text' : 'input'
        // 保留字段的原始type  在查找关联字段中需要使用到
        _item.originCrmFieldType = item.mankeepFieldType
        _item.keyDataType = 'select'
        _item.valuesOptions = item.valuesOptions
        _item.mankeepFieldOptions = _.map(item.mankeepFieldOptions || [], v => ({
          ...v,
          // eslint-disable-next-line no-undefined
          valueName: _item.valuesOptions && _item.valuesOptions[v.value] !== undefined ? _item.valuesOptions[v.value] : '',
        }))
        if (_item.keyType == 'text') {
          const mankeepField = me.tool_getObjectByName(
            item.mankeepFieldName,
            whichModel,
            2,
          )
          if (mankeepField) {
            _item.keyText = mankeepField.fieldCaption
          } else {
            return true
          }
        } else {
          // 处理自定义映射
          _item.keyInputDefault = item.defaultValue
        }
        const valueObject = me.tool_getObjectByName(
          item.crmFieldName,
          whichModel,
          1,
        )
        _item.valueType = !item.modifiable && valueObject ? 'text' : 'select'
        if (_item.valueType == 'text') {
          _item.valueText = valueObject.fieldCaption
        }
        _item.valueOption = _.cloneDeep(
          [
            {},
            me.data_customer_map_crm,
            me.data_contact_map_crm,
            me.data_lead_map_crm,
          ][whichModel],
        )
        if (_item.keyType === 'select') {
          _item.valueOption = _item.valueOption.filter(v => ((item.mankeepFieldType === 'select_manny' && v.fieldType == 9) || (item.mankeepFieldType === 'select_one' && v.fieldType == 8) || v.fieldType === 0))
          _item.crmFieldOptions = []
        } else if (_item.keyType === 'text' && item.mankeepFieldType == 'image') {
          if (_item.keyInputDefault == undefined) {
            const _resData = [{
              fieldCaption: $t('marketing.commons.k_b7612b'),
              fieldName: null,
              fieldType: 0,
              isNotNull: false,
            }]
            _item.valueOption = [
              ..._resData,
              ...(_item.valueOption.filter(v => (v.fieldType == 10))),
            ]
          }
        } else if (_item.keyType === 'text' && item.mankeepFieldType == 'files') {
          const _resData = [{
            fieldCaption: $t('marketing.commons.k_b7612b'),
            fieldName: null,
            fieldType: 0,
            isNotNull: false,
          }]
          _item.valueOption = [
            ..._resData,
            ...(_item.valueOption.filter(v => (v.fieldType == 17))),
          ]
        } else if (_item.keyType === 'text' && this.strictMapByType) {
          const _resData = [{
            fieldCaption: $t('marketing.commons.k_b7612b'),
            fieldName: null,
            fieldType: 0,
            isNotNull: false,
          }]
          _item.valueOption = [
            ..._resData,
            ...(_item.valueOption.filter(v => (v.fieldType != 9 && v.fieldType != 8 && v.fieldType != 10 && v.fieldType != 0))),
          ]
        } else {
          // eslint-disable-next-line no-undefined
          _item.valueOption = _item.keyInputDefault == undefined && _item.valueOption ? _item.valueOption.filter(v => (v.fieldType != 9 && v.fieldType != 8 && v.fieldType != 10)) : _item.valueOption
        }

        _item.valueName = valueObject ? item.crmFieldName || null : null
        if (_item.keyType !== 'select' && _item.keyInputDefault) {
          _item.valueOption.map(v => {
            if (v.fieldName == _item.valueName) {
              _item.crmFieldOptions = v.enumDetails || []
              _item.keyDataType = v.fieldType == 8 ? 'select_one' : 'select_manny'
              // v.fieldType == 9 && (_item.keyInputDefault = JSON.parse(item.defaultValue) || [])
              // 兼容旧数据
              if (v.fieldType == 9) {
                try {
                  _item.keyInputDefault = item.defaultValue ? JSON.parse(item.defaultValue) : []
                } catch (e) {
                  _item.keyInputDefault = [item.defaultValue]
                }
              }
              if (_item.crmFieldOptions.length) {
                _item.keyType = 'select'
              }
            }
          })
        }
        _item.modifiable = item.modifiable
        _item.disabledCrmFild = item.disabledCrmFild
        _item.disabledMankeepFild = item.disabledMankeepFild
        if (this.isShowFieldScene) {
          _item.scene = item.scene
        }
        // 初始化
        if (valueObject && valueObject.fieldTypeName === 'object_reference') {
          _item.mappingObjectApiName = valueObject.targetApiName
        }
        // 关联字段时
        if (item.mappingObjectApiName && item.mappingObjectFieldName) {
          _item.mappingObjectApiName = item.mappingObjectApiName
          _item.mappingObjectFieldName = item.mappingObjectFieldName
        }
        if (item.objectReferenceName) {
          _item.objectReferenceName = item.objectReferenceName
        }
        me[_to].push(_item)
      })
      // console.log(`====${_to}====`, me[_to]);
    },
    // /**
    //  * 初始化CRM业务类型的选择器
    //  * 包括CRM客户、CRM联系人两大类
    //  * @param {*} from
    //  */
    // tool_initCrmTypeSelectors(from) {

    // },
    /**
     * 工具：通过字段名获取相应对象
     * @param {*} whichModel 哪个模块 1-客户 2-联系人 3-线索
     * @param {*} whichFrom 哪个数据源 1-crm 2-客脉
     */
    tool_getObjectByName(name, whichModel, whichFrom) {
      const _map = {
        11: 'data_customer_map_crm',
        12: 'data_customer_map_mankeep',
        21: 'data_contact_map_crm',
        22: 'data_contact_map_mankeep',
        31: 'data_lead_map_crm',
        32: 'data_lead_map_mankeep',
      }[`${whichModel}${whichFrom}`]
      return _.find(this[_map], item => item.fieldName == name)
    },
    /**
     * 工具：获取用于提交的数据
     * @param {*} whichTab customer-客户 mankeep-人脉
     * _item: {
     *   mankeepFieldName: '', // 客脉侧的fieldName，当为默认值时该值可不填.
     *   defaultValue: '', // 默认名称
     *   crmFieldName: '', // CRM侧的fieldName
     * }
     */
    tool_getSubmitData() {
      const me = this
      const fieldMappingConfigs = [
        {
          model: 'lead',
          mankeepObjectApiName: 'Mankeep', // 客户保存到CRM该值为Customer, 人脉存CRM该值为Mankeep
          crmObjectApiName: 'LeadsObj', // CRM对象apiName, 客户为AccountObj, 联系人为ContactObj。
          fieldMappings: [], // 字段映射列表。
        },
      ]

      _.each(fieldMappingConfigs, (fItem, fIndex) => {
        _.each(me[`info_${fItem.model}_formList`], (item, index) => {
          const _item = {}
          // 赋值操作
          _item._index = index
          _item.mankeepFieldName = item.keyName
          //  如果是自定义映射为查找关联时 defaultValue取值为 keyInputDefaultId
          _item.defaultValue = item.keyInputDefaultId ? item.keyInputDefaultId : item.keyInputDefault
          if (item.keyInputDefaultId) {
            _item.objectReferenceName = item.keyInputDefault
          }
          _item.crmFieldName = item.valueName
          _item.modifiable = item.modifiable || true
          if (item.mappingObjectApiName) {
            _item.mappingObjectApiName = item.mappingObjectApiName
          }
          if (item.mappingObjectFieldName) {
            _item.mappingObjectFieldName = item.mappingObjectFieldName
          }
          if (item.keyType === 'select') {
            const valuesOptions = {}
            _.each(item.mankeepFieldOptions || [], v => {
              valuesOptions[v.value] = v.valueName
            })
            _item.valuesOptions = valuesOptions
            _item.defaultValue = item.keyDataType === 'select_manny' ? JSON.stringify(item.keyInputDefault) : item.keyInputDefault
          }
          if (this.isShowFieldScene) {
            _item.scene = item.scene || 2
          }
          fieldMappingConfigs[fIndex].fieldMappings.push(_item)
        })
      })
      return fieldMappingConfigs
    },
    /**
     * 工具：获取必填的CRM对象数组
     * @param {*} whichModel 哪个模块 1-客户 2-联系人 3-线索
     */
    tool_getNotNullCRMObjects(whichModel) {
      const me = this
      const _map = [
        '',
        'data_customer_map_crm',
        'data_contact_map_crm',
        'data_lead_map_crm',
      ][whichModel]
      const _output = []
      _.each(me[_map], item => {
        item.isNotNull && _output.push(item)
      })
      return _output
    },
    /**
     * 工具：表单校验fieldType: 17,
        fieldTypeLabel: '附件',
     * 规则：1.同模块下（客户或联系人），不允许出现重复的CRM字段映射。
     *      2.所有模块都要保证CRM必填项具备映射关系
     *      3.客户字段文本输入框不能为空
     *      4.客脉预设字段的映射值不能为特殊字段类型
     *      5.客户输入字段映射至CRM特殊字段类型时
     *      6.不能选择CRM非法字段
     * @param {*} whichTab customer-客户 mankeep-人脉
     * @param {*} data
     * @param {*} from fromSubmit-来自点击提交按钮 fromListener-来自监听器
     */
    tool_validate(whichTab, data, from) {
      let _passFlag = true // 是否通过校验
      // validate_error_objects_mankeep = []  listener_validating_mankeep = true
      this[`validate_error_objects_${whichTab}`] = [] // 置空表单校验错误汇总
      this[`listener_validating_${whichTab}`] = true // 表单校验监听器置为启动
      const models = whichTab == 'customer' ? ['customer', 'contact'] : ['lead'] // 模块
      const dataStore = {} // 数据存储器
      const _specialFields = [
        // 特殊字段类型map
        {
          type: 4, label: $t('marketing.commons.zs_b5f1a7'), reg: /^\d+$/, tips: $t('marketing.commons.qsrzs_e9fb36'),
        },
        {
          type: 5, label: $t('marketing.commons.xs_a0a0a5'), reg: /^\d+(\.\d+)?$/, tips: $t('marketing.commons.qsrxs_b74c76'),
        },
        {
          type: 6,
          label: $t('marketing.commons.je_4cf24a'),
          reg: /^([1-9][\d]{0,7}|0)(\.[\d]{1,2})?$/,
          tips: $t('marketing.commons.qsrzqdjegs_02af62'),
        },
        {
          type: 7,
          label: $t('marketing.commons.rq_4ff1e7'),
          reg: /^[1-2][\d]{3}-(0?[1-9]|1[0-2])-(0?[1-9]|[1-2][0-9]|3[0-1])$/,
          tips: $t('marketing.commons.qsrxrdrq_c2d300'),
        },
        {
          type: 19,
          label: $t('marketing.commons.yx_3bc5e6'),
          reg: /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/,
          tips: $t('marketing.commons.qsrzqdyx_97e3ac'),
        },
      ]
      // const _illegalFields = [{ type: 17, label: $t('marketing.commons.fj_c9a6ee') }] // 非法字段类型map
      const _illegalFields = [] // 非法字段类型map
      const _enumerateFields = [{ type: 8, label: $t('marketing.commons.dx_9fd1b7') }] // 枚举字段类型map
      const _mankeepFieldsTypes = {
        text: false,
        email: 19,
        number: 4,
        date_time: 7,
        phone_number: false,
        multi_text: false,
        select_one: false,
        select_manny: false,
      }
      _.each(models, (item, index) => {
        dataStore[item] = {
          modelName: item, // 模块名
          submitMappings: data[index].fieldMappings, // 待提交映射数据
          notNullCrmNames: _.map(
            this[`data_${item}_notNull_crm_objects`],
            'fieldName',
          ), // 必填CRM字段名数组
          submitCrmNames: _.map(data[index].fieldMappings, 'crmFieldName'), // 待提交CRM字段名数组
          submitDefaultObjects: _.filter(data[index].fieldMappings, fItem => fItem.mankeepFieldName), // 待提交的客户预设字段数组
          submitInputObjects: _.filter(data[index].fieldMappings, fItem => !fItem.mankeepFieldName), // 待提交的客户输入字段数组
          falseNotRepeatCrmNames: [], // 违反“不可重复映射”的CRM字段名数组
          falseNotNullCrmNames: [], // 违反“必填原则”的CRM字段名数组
          falseInputNotNullIndexes: [], // 违反“输入类型不能为空”的映射序号数组
        }
      })
      // 1.同模块下，不允许出现重复的CRM字段映射
      _.each(dataStore, item => {
        _.each(item.submitCrmNames, saItem => {
          let _repeatFlag = 0
          _.each(item.submitCrmNames, sbItem => {
            sbItem == saItem && sbItem != null && _repeatFlag++
          })
          if (_repeatFlag > 1) {
            item.falseNotRepeatCrmNames.push(saItem)
          }
        })
        if (item.falseNotRepeatCrmNames.length > 0) {
          _passFlag = false // 校验结果
          item.falseNotRepeatCrmNames = _.uniq(item.falseNotRepeatCrmNames) // 去重
          // 表单校验错误汇总 - 录入重复项错误
          this[`validate_error_objects_${whichTab}`].unshift({
            type: 'repeat',
            model: item.modelName,
            crmNames: item.falseNotRepeatCrmNames,
          })
        }
      })
      // 2.所有模块都要保证CRM必填项具备映射关系
      _.each(dataStore, item => {
        _.each(item.notNullCrmNames, nItem => {
          let notNullFlag = false
          _.each(item.submitCrmNames, sItem => {
            if (sItem == nItem) {
              notNullFlag = true
              return true
            }
          })
          if (!notNullFlag) {
            item.falseNotNullCrmNames.push(nItem)
          }
        })
        if (item.falseNotNullCrmNames.length > 0) {
          _passFlag = false // 校验结果
          // 表单校验错误汇总 - 录入必填项错误
          this[`validate_error_objects_${whichTab}`].unshift({
            type: 'notNull',
            model: item.modelName,
            crmNames: item.falseNotNullCrmNames,
          })
        }
      })
      // 3.客户字段input下的不能为空
      _.each(dataStore, item => {
        // console.log('== dataStore.submitInputObjects ==', item.submitInputObjects);
        _.each(item.submitInputObjects, siItem => {
          !siItem.defaultValue
            && !(this.isShowFieldScene && siItem.scene !== 2)
            && item.falseInputNotNullIndexes.push(siItem._index)
        })
        // 过滤掉场景为AI自动识别的输入框
        item.submitInputObjects = item.submitInputObjects.filter(siItem => siItem.scene !== 1)
        if (item.falseInputNotNullIndexes.length > 0) {
          _passFlag = false // 校验结果
          // 表单校验错误汇总 - 输入项不能为空错误
          this[`validate_error_objects_${whichTab}`].unshift({
            type: 'inputNull',
            model: item.modelName,
            indexes: item.falseInputNotNullIndexes,
          })
        }
      })
      // 4.客脉预设字段的映射值不能为特殊字段类型和枚举类型
      _.each(dataStore, (item, index) => {
        _.each(item.submitDefaultObjects, sItem => {
          const _curFieldType = this.tool_getObjectByName(
            sItem.crmFieldName,
            { customer: 1, contact: 2, lead: 3 }[index],
            1,
          ).fieldType
          const _curCrmFieldCaption = this.tool_getObjectByName(
            sItem.crmFieldName,
            { customer: 1, contact: 2, lead: 3 }[index],
            1,
          ).fieldCaption
          const _mankeepFieldType = this.tool_getObjectByName(
            sItem.mankeepFieldName,
            { customer: 1, contact: 2, lead: 3 }[index],
            2,
          ).fieldType
          const _mankeepFieldMapType = _mankeepFieldsTypes[_mankeepFieldType] || -1
          const _mkpSpecialFieldsIndex = _.findIndex(
            _specialFields,
            _item => _item.type == _mankeepFieldMapType,
          )
          const _mkpTypeLabel = (_specialFields[_mkpSpecialFieldsIndex] || {}).label || $t('marketing.commons.wblx_05c444')
          const _curMankeepFieldCaption = this.tool_getObjectByName(
            sItem.mankeepFieldName,
            { customer: 1, contact: 2, lead: 3 }[index],
            2,
          ).fieldCaption
          const _hitSpecialFieldsIndex = _.findIndex(
            _specialFields,
            _item => _item.type == _curFieldType,
          )
          const _hitEnumerateFieldsIndex = _.findIndex(
            _enumerateFields,
            _item => _item.type == _curFieldType,
          )
          // 除掉邮箱对邮件类型的校验
          if (
            (_hitSpecialFieldsIndex != -1 || _hitEnumerateFieldsIndex != -1)
            && !(sItem.mankeepFieldName == 'email' && _curFieldType == 19) && _curFieldType != 8
            && (_mankeepFieldMapType >= 0 && _mankeepFieldMapType !== _curFieldType)
          ) {
            _passFlag = false // 校验结果
            // 表单校验错误汇总 - 客脉预设字段不能映射特殊字段类型及枚举类型
            const _crmTypeLabel = (_hitSpecialFieldsIndex != -1
              ? _specialFields[_hitSpecialFieldsIndex]
              : _enumerateFields[_hitEnumerateFieldsIndex]
            ).label
            this[`validate_error_objects_${whichTab}`].unshift({
              type: 'defaultMapSpecial',
              model: item.modelName,
              index: sItem._index,
              tips: $t('marketing.commons.wfyszlx_4c36a1', {
                data: ({
                  option0: _curMankeepFieldCaption, option1: _mkpTypeLabel, option2: _curCrmFieldCaption, option3: _crmTypeLabel,
                }),
              }),
            })
          }
        })
      })
      // 5.客户输入字段映射至CRM特殊字段类型时
      _.each(dataStore, (item, index) => {
        // console.log('== dataStore.submitInputObjects ==', item.submitInputObjects);
        _.each(item.submitInputObjects, sItem => {
          if (sItem.defaultValue) {
            const _curFieldType = this.tool_getObjectByName(
              sItem.crmFieldName,
              { customer: 1, contact: 2, lead: 3 }[index],
              1,
            ).fieldType
            // const _curCrmFieldCaption = this.tool_getObjectByName(sItem.crmFieldName, { 'customer': 1, 'contact': 2, 'lead': 3 }[index], 1).fieldCaption;
            // console.log('tool_getObjectByName', this.tool_getObjectByName(sItem.crmFieldName, { 'customer': 1, 'contact': 2, 'lead': 3 }[index], 1));
            const _hitFieldsIndex = _.findIndex(
              _specialFields,
              _item => _item.type == _curFieldType,
            )
            if (
              _hitFieldsIndex != -1
              && !_specialFields[_hitFieldsIndex].reg.test(sItem.defaultValue)
            ) {
              _passFlag = false // 校验结果
              // 表单校验错误汇总 - 客脉输入字段特殊字段类型格式校验
              this[`validate_error_objects_${whichTab}`].unshift({
                type: 'specialInputRegexp',
                model: item.modelName,
                index: sItem._index,
                // tips: `${_curCrmFieldCaption}为${_specialFields[_hitFieldsIndex].label}类型，无法映射`,
                tips: _specialFields[_hitFieldsIndex].tips,
              })
            }
            // console.log(sItem);
          }
        })
      })
      // 6.不能选择CRM非法字段
      _.each(dataStore, (item, index) => {
        // console.log('== dataStore.submitMappings ==', item.submitMappings);
        _.each(item.submitMappings, sItem => {
          const _curFieldType = this.tool_getObjectByName(
            sItem.crmFieldName,
            { customer: 1, contact: 2, lead: 3 }[index],
            1,
          ).fieldType
          const _curCrmFieldCaption = this.tool_getObjectByName(
            sItem.crmFieldName,
            { customer: 1, contact: 2, lead: 3 }[index],
            1,
          ).fieldCaption
          const _hitFieldsIndex = _.findIndex(
            _illegalFields,
            _item => _item.type == _curFieldType,
          )
          if (_hitFieldsIndex != -1) {
            _passFlag = false // 校验结果
            // 表单校验错误汇总 - 客脉输入字段特殊字段类型格式校验
            this[`validate_error_objects_${whichTab}`].unshift({
              type: 'mapIllegal',
              model: item.modelName,
              index: sItem._index,
              tips: $t('marketing.commons.wlxyxtbzcy_fb6a85', { data: { option0: _curCrmFieldCaption, option1: _illegalFields[_hitFieldsIndex].label } }),
            })
          }
          // console.log(sItem);
        })
      })
      // 7.客户输入字段映射至CRM枚举类型时
      _.each(dataStore, (item, index) => {
        // console.log('== dataStore.submitInputObjects ==', item.submitInputObjects);
        _.each(item.submitInputObjects, sItem => {
          if (sItem.defaultValue) {
            const _curCrmFieldObject = this.tool_getObjectByName(
              sItem.crmFieldName,
              { customer: 1, contact: 2, lead: 3 }[index],
              1,
            )
            const _curFieldType = _curCrmFieldObject.fieldType
            const _curCrmFieldCaption = _curCrmFieldObject.fieldCaption
            const _enumerateValues = _.map(
              _curCrmFieldObject.enumDetails,
              'itemCode',
            )
            const _hitFieldsIndex = _.findIndex(
              _enumerateFields,
              _item => _item.type == _curFieldType,
            )
            if (
              _hitFieldsIndex != -1
              && _.findIndex(
                _enumerateValues,
                _item => _item == sItem.defaultValue,
              ) == -1
            ) {
              _passFlag = false // 校验结果
              // 表单校验错误汇总 - 客户输入字段映射至CRM枚举类型时
              this[`validate_error_objects_${whichTab}`].unshift({
                type: 'notInEnumerate',
                model: item.modelName,
                index: sItem._index,
                tips: $t('marketing.commons.wlxqsrmjz_7a7a66', {
                  data: {
                    option0: _curCrmFieldCaption,
                    option1: _enumerateFields[_hitFieldsIndex].label,
                    option2: _.map(
                      _curCrmFieldObject.enumDetails,
                      'itemCode',
                    ).join('、'),
                  },
                }),
                // tips: _enumerateFields[_hitFieldsIndex].tips,
              });
            }
            // console.log(sItem);
          }
        })
      })
      // 8.字段为查找关联字段时候 查找关联对象字段必选
      // _.each(dataStore, (item, index) => {
      //   _.each(item.submitDefaultObjects, sItem => {
      //     if (sItem.mappingObjectApiName && !sItem.mappingObjectFieldName) {
      //       this[`validate_error_objects_${whichTab}`].unshift({
      //         type: 'inputNull',
      //         model: item.modelName,
      //         indexes: item.falseInputNotNullIndexes,
      //       })
      //     }
      //   })
      // })

      // console.log('==validate._passFlag==', _passFlag);
      // console.log(`==validate.validate_error_objects_${whichTab}==`, this[`validate_error_objects_${whichTab}`]);
      this.tool_showValidateErrorUI(whichTab, from)
      return _passFlag
    },
    /**
     * 工具：展示校验失败的UI
     * @param {*} whichTab customer-客户 mankeep-人脉
     * @param {*} from fromSubmit-来自点击提交按钮 fromListener-来自监听器
     */
    tool_showValidateErrorUI(whichTab, from) {
      const me = this
      // 1.将之前的残留于视图数据中的校验情况清除
      const _wantClearModels = whichTab == 'customer' ? ['customer', 'contact'] : ['lead']
      _.each(_wantClearModels, item => {
        _.each(this[`info_${item}_formList`], (iItem, iIndex) => {
          Vue.set(
            this[`info_${item}_formList`],
            iIndex,
            {
              ...this[`info_${item}_formList`][iIndex],
              keyError: false,
              keyErrorTips: null,
              valueError: false,
              valueErrorTips: null,
            },
          )
        })
      })
      // 2.将校验结果置入视图数据中
      _.each(this[`validate_error_objects_${whichTab}`], vItem => {
        const _curInfoFormList = Object.assign(
          [],
          this[`info_${vItem.model}_formList`],
        )
        if (vItem.type == 'repeat') {
          // 重复情况：重复的两个CRM客户字段标红
          _.each(vItem.crmNames, cItem => {
            _.each(_curInfoFormList, (_cItem, _cIndex) => {
              _cItem.valueName == cItem
                && Vue.set(
                  this[`info_${vItem.model}_formList`],
                  _cIndex,
                  {
                    ..._curInfoFormList[_cIndex],
                    valueError: true,
                    valueErrorTips: $t('marketing.commons.tyzdbkzfys_056530'),
                  },
                )
            })
          })
        } else if (vItem.type == 'notNull') {
          console.log('必填项', vItem, me[`data_${vItem.model}_map_crm`])
          // 非空情况：新增带有必填字段的一栏
          if (from == 'fromSubmit') {
            // 只有在点提交按钮时才会新增
            _.each(vItem.crmNames, cItem => {
              let selectParams = {}
              const curItem = _.find(me[`data_${vItem.model}_map_crm`], {
                fieldName: cItem,
              })
              if (curItem.fieldType == 8 || curItem.fieldType == 9) {
                selectParams = {
                  keyType: 'select',
                  keyDataType: curItem.fieldType == 8 ? 'select_one' : 'select_manny',
                  crmFieldOptions: curItem.enumDetails || [],
                }
              }
              this[`info_${vItem.model}_formList`].push({
                isAutoAdd: true, // 是否系统自动添加的
                model: vItem.model == 'customer' ? 1 : 2,
                keyType: 'input',
                keyInputDefault: '',
                valueType: 'select',
                valueOption: me[`data_${vItem.model}_map_crm`],
                valueName: cItem,
                keyError: true,
                keyErrorTips: $t('marketing.commons.btxbxjbysg_8487bb'),
                valueError: false,
                ...selectParams,
              })
            })
            // console.log('===tool_showValidateErrorUI type=notNull ==', this[`info_${vItem.model}_formList`]);
          }
        } else if (vItem.type == 'inputNull') {
          // 输入框为空情况：输入框变红、下方出现提示语句
          _.each(vItem.indexes, iItem => {
            if (_curInfoFormList[iItem].isAutoAdd) {
              Vue.set(
                this[`info_${vItem.model}_formList`],
                iItem,
                {
                  ..._curInfoFormList[iItem],
                  keyError: true,
                  keyErrorTips: $t('marketing.commons.btxyjbysgx_abbf7a'),
                },
              )
            } else {
              Vue.set(
                this[`info_${vItem.model}_formList`],
                iItem,
                {
                  ..._curInfoFormList[iItem],
                  keyError: true,
                  keyErrorTips: $t('marketing.commons.srxbnwk_e3264f'),
                },
              )
            }
          })
          // console.log('===tool_showValidateErrorUI type=inputNull ==', this[`info_${vItem.model}_formList`]);
        } else if (vItem.type == 'defaultMapSpecial') {
          // 客脉预设字段映射至特殊类型CRM字段情况：输入框变红、下方出现提示语句
          Vue.set(
            this[`info_${vItem.model}_formList`],
            vItem.index,
            {
              ..._curInfoFormList[vItem.index],
              valueError: true,
              valueErrorTips: vItem.tips,
            },
          )
        } else if (vItem.type == 'specialInputRegexp') {
          // 客脉输入字段映射至特殊类型CRM字段，没通过格式校验情况：输入框变红、下方出现提示语句
          Vue.set(
            this[`info_${vItem.model}_formList`],
            vItem.index,
            {
              ..._curInfoFormList[vItem.index],
              keyError: true,
              keyErrorTips: vItem.tips,
            },
          )
        } else if (vItem.type == 'mapIllegal') {
          // 客脉字段映射至非法类型CRM字段情况：输入框变红、下方出现提示语句
          Vue.set(
            this[`info_${vItem.model}_formList`],
            vItem.index,
            {
              ..._curInfoFormList[vItem.index],
              valueError: true,
              valueErrorTips: vItem.tips,
            },
          )
        } else if (vItem.type == 'notInEnumerate') {
          // 客户输入字段映射至CRM枚举类型时，输入项不是枚举值情况：输入框变红、下方出现提示语句
          Vue.set(
            this[`info_${vItem.model}_formList`],
            vItem.index,
            {
              ..._curInfoFormList[vItem.index],
              keyError: true,
              keyErrorTips: vItem.tips,
            },
          )
        }
      })
    },
    init() {
      this.ajax_getCrmObjectFields(() => {
        this.ajax_listCrmObjectRecordTypes() // 更改了执行逻辑，如果不延迟调用，会导致 select组件 默认值偶现的不能正确设置，element-ui 不存在此问题
        this.mappingListCopy = _.cloneDeep(this.mappingList)
        this.initMappingList()
        this.tool_checkIllegalField()
        this.get_formList()
        // this.data_customer_notNull_crm_objects = this.tool_getNotNullCRMObjects(1);
        // this.data_contact_notNull_crm_objects = this.tool_getNotNullCRMObjects(2);
        this.data_lead_notNull_crm_objects = this.tool_getNotNullCRMObjects(3)
      })
    },
  },
  created() { },
  mounted() {
    this.init()
  },
}
