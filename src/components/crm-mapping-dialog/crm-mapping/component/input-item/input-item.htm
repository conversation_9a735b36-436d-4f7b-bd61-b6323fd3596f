<div class="input-item-wrapper dialog">
  <div class="part part_1" :style="{width: isShowFieldScene ? '400px' : '300px'}">
    <Select v-if="isShowFieldScene" class="el-select" :placeholder="$t('marketing.commons.qxz_708c9d')" filterable @change="handleSceneChange" v-model="info.scene" style="flex: 1;">
      <Option v-for="(item, index) in sceneOptions" :key="item" :label="item.label" :value="item.value"></Option>
    </Select>
    <template v-if="!(isShowFieldScene && info.scene !== 2)">
      <div v-if="info.keyType == 'text'" class="field_text">
        <div class="text km-t-ellipsis1" :title="info.keyText">{{info.keyText}}</div>
      </div>
      <div v-else-if="info.keyType == 'custom_select'">
        <Select class="el-select" :placeholder="$t('marketing.commons.qxz_708c9d')" filterable style="width:100%" @change="handleCustomChange">
          <Option v-for="(item, index) in selectData" :key="item" :label="item.keyText || item.keyInputDefault"
            :value="index"
            >
          </Option>
        </Select>
      </div>
      <div v-else-if="info.keyType == 'select'" class="field_input select_wrap" :class="{'error': info.keyError}">
        <div v-if="info.keyDataType == 'select_one' || info.keyDataType == 'select_manny'">
          <Select class="el-select" :placeholder="$t('marketing.commons.qxz_708c9d')" filterable :multiple="info.keyDataType == 'select_manny'" v-model="info.keyInputDefault" style="width:100%" :disabled="info.disabledMankeepFild">
            <Option v-for="(item, index) in info.crmFieldOptions" :key="item.itemCode" :label="item.itemName"
              :value="item.itemCode"
              >
            </Option>
          </Select>
        </div>
        <div v-else>
          <div class="select_title">{{info.keyInputDefault}}</div>
          <div class="select_item_wrap">
            <div class="select_item" v-for="(item, index) in info.mankeepFieldOptions" :key="item.valueName">
              <div class="select_item-text km-t-ellipsis1">
                {{item.label}}
              </div>
            </div>
          </div>
        </div>
        <fx-tooltip class="error-tips" :effect="effectType" :content="info.keyErrorTips" placement="right">
          <span class="icon km-ico-error"></span>
        </fx-tooltip>
      </div>
      <div v-else @click="handleOpenMarketingList" class="field_input" :class="{'error': info.keyError}">
        <fx-input
          class="input"
          :placeholder="$t('marketing.commons.qsrmrz_dd97b7')"
          v-model="selfInputValue"
          @change="handleInputValueChange"
          >
          <div v-if="currentFiled.fieldTypeName === 'object_reference'" class="pick-self" slot="append">+</div>
        </fx-input>
        <fx-tooltip class="error-tips" effect="light" :content="info.keyErrorTips" placement="right">
          <span class="icon km-ico-error"></span>
        </fx-tooltip>
      </div>
    </template>
  </div>
  <div class="part part_2">
    <div class="icon-arrow"></div>
  </div>
  <div class="part part_3">
    <div v-if="info.valueType == 'text' && info.keyType == 'text'" class="field_text"
      :class="{'error': info.valueError}"
>
      <div class="text">{{info.valueText}}</div>
    </div>
    <div v-else-if="info.keyType == 'select'" class="field_select field_select_wrap"
      :class="{'error': info.valueError}"
>
      <div v-if="info.keyDataType == 'select_one' || info.keyDataType == 'select_manny'">
        <div class="field_select_title">
          <Select class="el-select" :placeholder="$t('marketing.commons.qxz_708c9d')" filterable v-model="info.valueName" @change="handleChange"
            :disabled="info.disabledCrmFild || disabled_part3"
          >
            <Option v-for="(item, index) in info.valueOption" :key="item.fieldName" :label="item.fieldCaption"
              :value="item.fieldName"
>
            </Option>
          </Select>
        </div>
      </div>
      <div v-else>
        <div class="field_select_title">
          <Select class="el-select" :placeholder="$t('marketing.commons.qxz_708c9d')" filterable v-model="info.valueName" @change="handleChange"
            :disabled="disabled_part3"
          >
            <Option v-for="(item, index) in info.valueOption" :key="item.fieldName" :label="item.fieldCaption"
              :value="item.fieldName"
>
            </Option>
          </Select>
        </div>
        <div class="field_select_item_wrap">
          <Select class="el-select field_select_item" filterable v-for="(item, index) in info.mankeepFieldOptions" :key="index"
            v-model="item.valueName" :placeholder="$t('marketing.commons.qxz_708c9d')" :disabled="disabled_part3"
>
            <Option v-for="(item, index) in info.crmFieldOptions" :key="index" :label="item.itemName"
              :value="item.itemCode"
>
            </Option>
          </Select>
        </div>
      </div>
      <fx-tooltip class="error-tips" effect="light" :content="info.valueErrorTips" placement="right">
        <span class="icon km-ico-error"></span>
      </fx-tooltip>
    </div>
    <div v-else class="field_select" :class="{'error': info.valueError}">
      <Select class="el-select" :placeholder="$t('marketing.commons.qxz_708c9d')" filterable v-model="info.valueName" @change="handleChange"
        :disabled="disabled_part3"
      >
        <Option v-for="(item, index) in info.valueOption" :key="item.fieldName" :label="item.fieldCaption"
          :value="item.fieldName"
>
        </Option>
      </Select>
      <div class="association_fileds" v-if="(currentFiled.fieldTypeName === 'object_reference' && info.keyType !== 'input')">
        <div class="association_fileds-text">
            {{ $t('marketing.components.crm_mapping_dialog.gzdczglzdk_3d0a50', {data: ({'option0': currentFiled.fieldCaption})}) }}
            <PictureStandard style="display: inline-block;font-size: 12px;" :zIndex="3000" :text="$t('marketing.commons.cktj_26e86d')" :imageUrls="[TextObjectReference]" />
        </div>
        <div class="association_fileds-options">
          <Select class="el-select" :placeholder="$t('marketing.commons.qxz_708c9d')" filterable v-model="info.mappingObjectFieldName" @change="handleChange"
            :disabled="disabled_part3"
          >
            <Option v-for="(item, index) in currentFiledOptions" :key="item.fieldName" :label="item.fieldCaption"
              :value="item.fieldName"
              >
            </Option>
          </Select>
        </div>
      </div>
      <fx-tooltip class="error-tips" effect="light" :content="info.valueErrorTips" placement="right">
        <span class="icon km-ico-error"></span>
      </fx-tooltip>
    </div>
  </div>
  <div class="part part_4">
    {{ getPart4Text() }}
  </div>
  <div class="part part_5">
    <span v-if="info.keyType == 'input' || info.keyType == 'select' || info._memberCustomSelect" class="delete km-a-btn" @click="deleteInputItem">{{$t('marketing.commons.sc_2f4aad')}}</span>
  </div>
</div>
