.input-item-wrapper.dialog {
  display: flex;
  margin: 0;
  .el-icon-close::before {
    display: block !important;
    width: auto !important;
    content: "\E6DB" !important;
    transform: translate(0, 0.5px);
    -webkit-transform: translate(0, 0.5px);
  }
  .el-tag {
    max-width: 150px;
  }
  .part{
    border-right: 1px solid #DEE1E8;
    border-bottom: 1px solid #DEE1E8;
    padding: 13px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
  }
  .part_1 {
    width: 202px;
    box-sizing: border-box;
    padding: 10px 30px;
    gap: 10px;
    .field_text {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      background: #F2F3F5;
      width: 200px;
      .text {
        width: 100%;
        padding-left: 14px;
        border: 1px solid #C1C5CE;
        border-radius: 4px;
        font-size: 13px;
        color: #C0C4CC;
        height: 40px;
        line-height: 40px;
        box-sizing: border-box;
      }
    }
    .field_input {
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      justify-content: center;
      background: #ffffff;
      position: relative;
      width: 200px;
      .input {
        width: 100%;
        border-radius: 3px;
        font-size: 14px;
        color: #333333;
        box-sizing: border-box;
        height: 40px;
        line-height: 36px;
        padding: 0;
        border: none;
        &:focus {
          border-color: var(--color-primary06,#407FFF);
        }
        .pick-self{
          font-size: 22px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          &:hover{
            color: var(--color-primary06,#407FFF);;
          }
        }
        .el-input-group__append{
          padding: 0 5px;
        }
      }
      &.error {
        .input, .el-input {
          border: 1px solid #ff7663;
          box-sizing: content-box;
          border-radius: 3px;
        }
        .el-input .el-input__inner,.el-input-group__append{
          border: none;
        }
        .error-tips {
          display: flex;
        }
      }
    }
  }
  .part_2 {
    width: 96px;
    .icon-arrow {
      display: inline-block;
      width: 50px;
      height: 12px;
      background: url("../../../../../assets/images/crmsetting-arrow.png")
        no-repeat center;
      background-size: cover;
      float: left;
      margin-right: 6px;
    }
  }
  .part_3 {
    flex: 1;
    padding: 10px 30px;
    .field_text {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      background: #f5f7fa;
      width: 100%;
      .text {
        width: 100%;
        padding-left: 14px;
        border: 1px solid #E4E7ED;
        border-radius: 4px;
        font-size: 13px;
        color: #C0C4CC;
        height: 40px;
        line-height: 40px;
        box-sizing: border-box;
      }
      &.error .text {
        border: 1px solid #ff7663;
      }
    }
    .field_select {
      width: 100%;
      position: relative;
      .fx-select {
        width: 100%;
        box-sizing: border-box;
        .el-scrollbar__wrap {
          overflow: hidden;
        }
      }
      &.error {
        margin-left: 5px;
        .text,
        .fx-select .el-input .el-input__inner {
          border: 1px solid #ff7663;
        }
        .error-tips {
          display: flex;
        }
      }
      .association_fileds{
        .association_fileds-text{
          position: relative;
          margin: 10px 0;
          .question_tooltip{
            display: inline-block;
            position: relative;
            top: 3px;
          }
        }
      }
    }
  }
  .part_4 {
    width: 50px;
  }
  .part_5 {
    width: 52px;
    display: flex;
  }
  .error-tips {
    display: none;
    color: #ff7663;
    position: absolute;
    right: -25px;
    top: 50%;
    transform: translateY(-50%);
    .icon {
      margin: 2px 5px 0 0;
      min-width: 13px;
      width: 13px;
      height: 13px;
    }
    .etips {
      color: #ff7663;
      font-size: 12px;
      line-height: 15px;
      flex: 1 1 auto;
      white-space: nowrap;
    }
  }

  .select_wrap {
    .select_title {
      background: #F2F3F5;
      height: 40px;
      line-height: 36px;
      padding-left: 14px;
      border: 1px solid #C1C5CE;
      border-radius: 3px;
      box-sizing: border-box;
    }
    .select_item_wrap {
      margin-top: 10px;
      position: relative;
      box-sizing: border-box;
    }
    .select_item {
      background: #F2F3F5;
      height: 40px;
      line-height: 36px;
      border: 1px solid #C1C5CE;
      padding-left: 14px;
      margin-left: 8px;
      margin-bottom: 10px;
      position: relative;
      box-sizing: border-box;
      border-radius: 4px;
      &::before{
        content: '';
        width: 8px;
        height: 1px;
        background-color: #C1C5CE;
        position: absolute;
        left: -8px;
        top: 50%;
        transform: translateY(-50%);
      }
      &::after{
        content: '';
        width: 1px;
        height: 52px;
        background-color: #C1C5CE;
        position: absolute;
        left: -9px;
        top: -32px;
      }
      &:first-child:after{
        height: 34px;
        top: -14px;
      }
    }
  }
  .field_select_wrap {
    .field_select_item_wrap{
      box-sizing: border-box;
      margin-top: 10px;
      position: relative;
      .field_select_item{
        border: 1px solid #C1C5CE;
        box-sizing: border-box;
        padding-left: 14px;
        margin-left: 8px;
        margin-bottom: 10px;
        width: 244px;
        height: 40px;
        border-radius: 4px;
        box-sizing: border-box;
      &::before{
        content: '';
        width: 8px;
        height: 1px;
        background-color: #C1C5CE;
        position: absolute;
        left: -8px;
        top: 50%;
        transform: translateY(-50%);
      }
      &::after{
        content: '';
        width: 1px;
        height: 52px;
        background-color: #C1C5CE;
        position: absolute;
        left: -9px;
        top: -32px;
      }
      &:first-child::after{
        height: 34px;
        top: -14px;
      }
      }
      .el-input__inner {
        border: 0;
        background: none;
      }
    }
  }
}