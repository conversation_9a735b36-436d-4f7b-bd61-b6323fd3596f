import _ from 'lodash'
import { mapGetters } from 'vuex'
import http from '@/services/http/index.js'
import { requireAsync } from '@/utils/index.js'
import './index.less'
import inputItem from './input-item.htm'
import QuestionTooltip from '@/components/questionTooltip/index.vue'
import PictureStandard from '@/components/PictureStandard/index.vue'
import InputObjectReference from '@/assets/images/crm-mapping-dialog/input-object-reference.png'
import TextObjectReference from '@/assets/images/crm-mapping-dialog/text-object-reference.png'

export default {
  template: inputItem,
  components: {
    Select: FxUI.Select.components.ElSelect,
    Option: FxUI.Select.components.ElSelect.components.ElOption,
    Dialog: FxUI.Dialog,
    QuestionTooltip,
    PictureStandard,
  },
  props: {
    info: {
      /*
       * keyType: 'text', // 客户字段的类型 text-文本展示 input-单行输入
       * keyText: '', // 客户字段的内容
       * valueType: 'select', // CRM客户字段的类型 text-文本展示 select-下拉选择
       * valueOption: [], // CRM客户字段下拉选择的选择项
       * valueText: '', // CRM客户字段文本展示的内容
       */
    },
    selectData: [],
    itemIndex: null, // 条目序号
    isShowFieldScene: false,
  },
  computed: {
    // keyError() {
    //   return this.info.keyError;
    // },
    // valueError() {
    //   return this.info.valueError;
    // },
    ...mapGetters('MarketingEventSet', ['marketSetFilters']),
    currentFiled() {
      const filed = _.find(this.info.valueOption, item => item.fieldName === this.info.valueName) || {}
      return filed
    },
    disabled_part3() {
      return this.info && this.info.keyType === 'custom_select'
    },
  },
  data() {
    return {
      currentFiledOptions: [],
      crmObjectId: '',
      selfInputValue: '',
      TextObjectReference,
      InputObjectReference,
      sceneOptions: [
        {
          label: $t('marketing.components.crm_mapping_dialog.znsbsxwbtx_a47d41'),
          value: 1,
        },
        {
          label: $t('marketing.components.crm_mapping_dialog.gdz_86e38e'),
          value: 2,
        },
      ],
    }
  },
  watch: {
    'info.keyInputDefault': {
      handler(val) {
        this.selfInputValue = val
      },
    },
  },
  methods: {
    getPart4Text() {
      if (this.info.valueName) {
        const option = _.find(this.info.valueOption, item => item.fieldName === this.info.valueName)
        if (option && option.isNotNull) {
          return $t('marketing.commons.bt_537b39')
        }

        return $t('marketing.commons.fbt_eb5bab')
      }

      return '--'
    },
    handleCustomChange(index) {
      this.$emit('custom-select', index, this.itemIndex)
    },
    handleSceneChange(val) {
      this.$set(this.info, 'scene', val)
      this.$emit('onChange', { index: this.itemIndex, item: this.info })
    },
    // 删除本栏
    deleteInputItem() {
      this.$emit('deleteInputItem', { index: this.itemIndex })
    },
    handleChange(val) {
      if (this.currentFiled.fieldTypeName === 'object_reference') {
        this.info.mappingObjectApiName = this.currentFiled.targetApiName
        this.currentFiledOptions = []
        this.getCrmObjectMultilevelFields()
      } else {
        this.info.mappingObjectApiName = ''
        this.info.mappingObjectFieldName = ''
      }
      this.$emit('onChange', { index: this.itemIndex, item: this.info })
    },
    // 查找关联字段下的字段需要过滤自身的查找关联字段  所以使用新的获取对象字段的接口
    getCrmObjectMultilevelFields(apiName) {
      const currentFieldTypeName = this.info.originCrmFieldType || ''
      http
        .getCrmObjectMultilevelFields({
          objectApiName: apiName || this.currentFiled.targetApiName || 'LeadsObj',
          recordType: 'default__c',
        }).then(res => {
          if (res.errCode === 0) {
            const realResData = []
            const resData = res.data || []
            resData.forEach(item => {
              if (item && item.fieldTypeName && item.fieldTypeName === currentFieldTypeName) {
                realResData.push(item)
              }
            })
            this.currentFiledOptions = realResData
          }
        })
    },
    handleOpenMarketingList() {
      const me = this
      if (this.currentFiled.fieldTypeName !== 'object_reference') return
      requireAsync('crm-modules/components/pickselfobject/pickselfobject', PickObject => {
        const picker = new PickObject()
        picker.on('select', value => {
          const marketingEvent = _.extend({ id: value._id }, value)
          me.info.keyInputDefault = marketingEvent.name
          me.selfInputValue = marketingEvent.name
          me.info.keyInputDefaultId = marketingEvent.id
          me.crmObjectId = marketingEvent.id
          picker.destroy()
        })
        picker.render({
          layout_type: 'list',
          include_layout: true,
          apiname: me.currentFiled.targetApiName || 'LeadsObj',
          dataId: me.crmObjectId || '',
          zIndex: 3000,
          filters: [
            ...this.marketSetFilters,
          ],
        })
      })
    },
    handleInputValueChange(val) {
      this.info.keyInputDefault = val
      this.info.keyInputDefaultId = ''
    },
  },
  beforeMount() {},
  created() {
    if (this.info.mappingObjectApiName) {
      this.getCrmObjectMultilevelFields(this.info.mappingObjectApiName)
    }
    // 自定义映射为查找关联字段
    if (this.info.objectReferenceName && this.info.keyInputDefault) {
      this.selfInputValue = this.info.objectReferenceName
      this.crmObjectId = this.info.keyInputDefault
    } else {
      this.selfInputValue = this.info.keyInputDefault
    }

    if (this.isShowFieldScene) {
      this.info.scene = this.info.scene || this.sceneOptions[1].value
    }
  },
}
