.activity-crm-mapping {
  position: relative;
  display: flex;
  flex-flow: column nowrap;
  height: 100%;
  background: #fff;
  z-index: 1000;
  .w-crmsetting-wrapper {
    // padding: 30px 10px 0px 20px;
    .w-crmsetting-tab {
      height: 50px;
      border-bottom: 1px solid #ededed;
      display: flex;
      padding-left: 33px;
      .tab_item {
        margin-top: 19px;
        margin-right: 45px;
        font-size: 16px;
        color: #666666;
        cursor: pointer;
        &.cur {
          color: #333333;
          border-bottom: 2px solid #fc923f;
        }
      }
    }
    .w-crmsetting-content {
      .tab_wrapper {
        position: relative;
        .tab_content {
          padding: 30px 10px 0px 24px;
        }
      }
    }
    .tips {
      font-size: 12px;
      color: #999999;
    }
    .title {
      font-size: 16px;
      color: #333333;
      font-weight: bold;
    }
    .w-crmsetting-switch {
      .row_1 {
        display: flex;
        align-items: center;
        .switch {
          margin-left: 20px;
          .el-switch.is-disabled .el-switch__core {
            cursor: wait;
          }
        }
      }
      .row_2 {
        display: flex;
        .know-more {
          color: var(--color-primary06,#407FFF);
          margin-top: 9px;
          cursor: pointer;
          &:hover {
            text-decoration: underline;
          }
        }
      }
    }
    .w-crmsetting-object-map {
      // margin-top: 50px;
      .header {
        position: relative;
        .save {
          position: absolute;
          right: 0;
          bottom: 0;
          margin-right: 5px;
        }
      }
      .body {
        margin-top: 14px;
        box-sizing: border-box;
        .object-map-form {
          width: 100%;
          position: relative;
          .tr_1{
            width: 100%;
            height: 50px;
            position: absolute;
            top: 0;
            z-index: 80;
          }
          .tr_1,
          .tr_3 {
            height: 50px;
          }
          .tr_2{
            width: 100%;
            border: none;
            padding-top: 50px;
            max-height: 380px;
            overflow: auto;
          }
          .tr_2,
          .tr_4 {
            position: relative;
            min-height: 158px;
          }
          .td_1 {
            width: 256px;
          }
          .td_2 {
            width: 100%;
            border: none;
          }
          .tr_2 .td_1,
          .tr_4 .td_1 {
            position: absolute;
            background: #f6f9fd;
            display: flex;
            flex-direction: column;
            justify-content: center;
            .td_1_box {
              margin: 0 0 0 20px;
              .row_1 {
                color: #999;
              }
              .row_2 {
                display: flex;
                align-items: center;
                margin-top: 7px;
                font-size: 14px;
                color: #151515;
                .label {
                  width: 75px;
                }
                .selector {
                  width: 145px;
                  margin-left: 5px;
                }
              }
              .row_3 {
                margin-top: 7px;
                color: var(--color-primary06,#407FFF);
                cursor: pointer;
              }
            }
          }
          .tr_1 .td_2,
          .tr_3 .td_2 {
            display: flex;
            align-items: center;
          }
          .tr1{
            border-left: 1px solid #dddddd;
          }
          .thead {
            color: #1C2A4B;
            font-size: 14px;
            height: 100%;
            border-right: 1px solid #DEE1E8;
            border-top: 1px solid #DEE1E8;
            border-bottom: 1px solid #DEE1E8;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #F0F2F5;
            &.thead_1 {
              border-left: 1px solid #DEE1E8;
              width: 264px;
            }
            &.thead_2 {
              width: 123px;
            }
            &.thead_3 {
              flex: 1;
            }
            &.thead_4 {
              width: 77px;
            }
            &.thead_5 {
              width: 79px;
            }
          }
          .input-box {
            margin-bottom: 10px;
            border-left: 1px solid #dddddd;
          }
          .add-box {
            width: 857px;
            margin: 16px 0;
            border: 1px dashed #DEE1E8;
            border-radius: 3px;
            padding: 6px 0;
            display: flex;
            align-items: center;
            flex-direction: column;
            .km-a-btn{
              color: var(--color-primary06,#ff8000);;
            }
          }
        }
      }
    }
  }
  .init-error-mask {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: #fff;
    opacity: 1;
    z-index: 90;
    padding-top: 180px;
    display: flex;
    // align-items: center;
    justify-content: center;
    .text-area {
      font-size: 16px;
      line-height: 28px;
    }
  }
}
.table-form {
  .tr {
    display: flex;
    border-top: none !important;
    .td {
      // width: 100%;
      height: 100%;
      &:last-child {
        border-right: 1px solid #dddddd;
      }
    }
  }
}
.content-item{
  &:first-child{
    margin-bottom: 30px;
  }
  .header{
    .title{
      margin-left: 13px;
      color: var(--text-h-1, #181C25);
      font-size: 16px;
      font-weight: 400;
      line-height: 24px;
      &::before{
        content: '';
        width: 4px;
        height: 16px;
        background-color: var(--color-primary06,#ff8000);
        position: absolute;
        left: 0px;
        top: 4px;
      }
    }
  }
  .body{
    .crm-type{
      display: flex;
      align-items: center;
      .label{
        width: 100px;
        position: relative;
        margin-left: 10px;
        &::before{
          content: '*';
          color: var(--color-primary06,#ff8000);
          font-size: 20px;
          position: absolute;
          top: 0px;
          left: -10px;
        }
      }
    }
  }
}
