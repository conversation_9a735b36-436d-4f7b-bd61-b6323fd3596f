<template>
  <VDialog
    :class="$style.keywordresdialog_wrapper"
    width="747px"
    :title="title"
    append-to-body
    :visible="visible"
    @onSubmit="handleValidateForm"
    @close="handleCloseDialog"
    @onClose="handleCloseDialog"
  >
    <!-- 表单数据 -->
    <el-form
      :class="$style.form"
      ref="form"
      label-width="100px"
      label-position="left"
      :model="formData"
      :rules="formRules"
    >
      <!-- 规则名称 -->
      <el-form-item :label="$t('marketing.pages.wechat.gzmc_42ab4a')" prop="ruleName">
        <fx-input
          v-model="formData.ruleName"
          :placeholder="$t('marketing.commons.qsr_02cc4f')"
          maxlength="60"
          show-word-limit
        ></fx-input>
      </el-form-item>
      <el-form-item :label="$t('marketing.pages.wechat.gjc_f5444d')" prop="keyWord">
        <div :class="$style.keyWord_wrapper">
          <el-select
            :class="['el-select',$style.select]"
            v-model="operator"
            :placeholder="$t('marketing.commons.qxz_708c9d')"
          >
            <el-option
              v-for="item in optioList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <fx-input
            v-model="formData.keyWord"
            :placeholder="$t('marketing.commons.qsrgjc_cd11be')"
            maxlength="100"
            show-word-limit
          ></fx-input>
        </div>
      </el-form-item>
      <el-form-item
        :label="$t('marketing.pages.wechat.hfnr_c2747a')"
        prop="responseMsg"
        v-if="formData.responseMsg.length"
        style="margin-bottom: 0"
      >
        <div
          :class="$style.reply_msg_editor"
          v-for="(editor, i) in formData.responseMsg"
          :key="i"
          v-loading="!showVmsg"
        >
          <vmsg-conts
            v-if="showVmsg"
            :ref="`editor`"
            :class="$style.content"
            :tabbar="[2, 3, 6, 4]"
            :materialTabbar="[10, 1, 4, 3]"
            :value="editor"
            @input="val => handleEditorChange(i, val)"
            :toolbar="[
              'emoji',
              'link',
              'material',
              ...(miniprogramInfo.realAppType === 3 ? ['miniprogram'] : [])
            ]"
            :wrapperSize="{
              height: '200px'
            }"
            :wxAppId="wxAppId"
            :hiddenMulti="true"
            :npath="true"
          ></vmsg-conts>
          <!-- <p :class="$style.error_tip" v-if="editor && editor.err">{{ editor.err }}</p> -->
          <div :class="$style.btndel" v-if="formData.responseMsg.length > 1" @click="handleDelReplyEditor(i)">
            {{ $t('marketing.commons.sc_2f4aad') }}
          </div>
        </div>
      </el-form-item>
      <el-form-item
        label
        v-if="formData.responseMsg.length < 3"
        style="margin-bottom: 8px"
      >
        <div :class="$style.btnadd" @click="handleAddReplyEditor">
          <i class="yxt-icon16 icon-add-b"></i>
          <span>{{ $t('marketing.commons.tjhf_032471') }}</span>
        </div>
      </el-form-item>

      <el-form-item label=" ">
        <div>{{ $t('marketing.pages.wechat.ghfgjcdwxy_959e78') }}</div>
        <selector-line
          v-model="formData.tagNameList"
          style="line-height: 26px;"
        ></selector-line>
      </el-form-item>
      <el-form-item :label="$t('marketing.pages.wechat.yxsj_a0ab6d')" prop="date" :class="$style.date_wrapper">
        <picker-time
          v-model="formData.date"
          type="datetime"
          value-format="timestamp"
          format="yyyy-MM-dd HH:mm"
        />
      </el-form-item>
    </el-form>
    <div slot="footer" :class="$style.dialog_footer">
      <div :class="$style.tips">
        {{ $t('marketing.pages.wechat.tgbjnrhzgj_09d6ea') }}
      </div>
      <fx-button size="small" type="primary" @click="handleValidateForm">{{ $t('marketing.commons.qd_aa7527') }}</fx-button>
      <fx-button size="small" @click="handleCloseDialog">{{ $t('marketing.commons.qx_c08ab9')+ '222' }}</fx-button>
    </div>
  </VDialog>
</template>

<script>
import http from "@/services/http/index";
import vmsgConts from "./vmsg-conts.vue";
import SelectorLine from "@/components/tags-selector-new/tags-line";

import VDialog from "@/components/dialog";
import { mapState, mapActions } from "vuex";
import wxList from "@/modules/wx-list";
import PickerTime from "@/pages/promotion-activity/common/picker-time";
import vmsgUtil from "@/utils/vmsg-util";
const defaultEditorValue = {
  msgType: 2,
  content: ""
};
export default {
  components: {
VDialog,
ElForm: FxUI.Form,
ElFormItem: FxUI.FormItem,
ElSelect: FxUI.Select.components.ElSelect,
ElOption: FxUI.Select.components.ElSelect.components.ElOption,
SelectorLine,
vmsgConts,
PickerTime
},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    wxAppId: {
      type: String,
      default: ""
    },
    miniprogramInfo: {
      type: Object,
      default: {}
    },
    selectId: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      title: $t('marketing.pages.wechat.tjgjchf_6ef7ce'),
      showVmsg: false,
      optioList: [
        {
          label: $t('marketing.pages.wechat.qpp_e66ced'),
          value: "EQ"
        },
        {
          label: $t('marketing.pages.wechat.bpp_26e067') + $t('marketing.pages.wechat.bhgjc_bb572e'),
          value: "EXISTS"
        },
        {
          label: $t('marketing.pages.wechat.bpp_26e067') + $t('marketing.pages.wechat.bgjcbh_9c1530'),
          value: "LIKE"
        }
      ],
      operator: "EQ",
      serviceList: wxList.datas.list,
      loading: false,
      formData: {
        ruleName: "",
        responseMsg: [defaultEditorValue],
        tagNameList: [],
        date: [],
        keyWord: ""
      },
      formRules: {
        ruleName: [
          { required: true, message: $t('marketing.pages.wechat.qsrgzmc_40f3f1'), trigger: "change" }
        ],
        keyWord: [
          { required: true, message: $t('marketing.commons.qsrgjc_cd11be'), trigger: "change" }
        ],
        responseMsg: [
          {
            required: true,
            validator: this.validateContent,
            trigger: "blur"
          }
        ],
        date: [
          {
            required: true,
            validator: (rule, value, callback) => {
              const [startTime, endTime] = value;
              if (!startTime) {
                callback(new Error($t('marketing.commons.qxzkssj_90fae3')));
              } else if (!endTime) {
                callback(new Error($t('marketing.pages.wechat.qxzjzsj_aee47a')));
              }
              callback();
            },
            trigger: "change"
          }
        ]
      }
    };
  },
  watch: {},
  computed: {},
  created() {
    if (this.selectId) {
      this.title = $t('marketing.pages.wechat.bjgjchf_6d8961');
      this.getWxAutoReplyRuleDetail();
    } else {
      this.showVmsg = true;
    }
  },
  methods: {
    getWxAutoReplyRuleDetail() {
      http.getWxAutoReplyRuleDetail({ id: this.selectId }).then(res => {
        if (res && res.errCode == 0) {
          let {
            ruleName,
            responseMsg,
            tagNameList,
            validFrom,
            validTo,
            keyWord,
            operator
          } = res.data;
          if (typeof responseMsg === "string") {
            responseMsg = JSON.parse(responseMsg) || [];
            const keyMaps = {
              text: 2,
              image: 3,
              material: 6,
              mpnews: 4
            };

            responseMsg = responseMsg.map(item => {
              if (item.type === "text") {
                return {
                  msgType: keyMaps[item.type],
                  content: item.text
                };
              } else if (item.type === "image") {
                return {
                  msgType: keyMaps[item.type],
                  content: item.nPath
                };
              } else if (item.type === "material") {
                return {
                  msgType: keyMaps[item.type],
                  content: JSON.stringify({
                    title: item.title,
                    url: item.link,
                    mImage: item.sharePicUrl,
                    time: item.time || item.title,
                    description: item.summary,
                    url: item.link,
                    materialId: item.materialId,
                    materialType: item.materialType,
                    aPath: ""
                  })
                };
              } else if (item.type === "mpnews") {
                return {
                  msgType: keyMaps[item.type],
                  content: item.text
                };
              }
            });
          }
          this.formData = {
            ruleName,
            responseMsg,
            tagNameList,
            date: [validFrom, validTo],
            keyWord
          };
          this.showVmsg = true;
          this.operator = operator;
        }
      });
    },
    genResponseMsg(data) {
      if (data.msgType === 2) {
        // 文本消息
        return {
          type: "text",
          text: data.content
        };
      } else if (data.msgType === 3) {
        // 图片消息
        return {
          type: "image",
          nPath: data.content
        };
      } else if (data.msgType === 6) {
        // 素材
        const content = JSON.parse(data.content);
        let materialType = content.materialType;
        if (materialType === 1) {
          // 文章
          materialType = 6;
        } else if (materialType === 4) {
          // 产品
          materialType = 4;
        } else if (materialType === 10) {
          // 微页面
          materialType = 26;
        } else if (materialType === 3) {
          // 会议
          materialType = 13;
        }
        return {
          type: "material",
          materialType,
          materialId: content.materialId,
          link: content.url
        };
      } else if (data.msgType === 4) {
        console.log(data);
        //公众号图文消息
        return {
          type: "mpnews",
          text: data.content
        };
      }
    },
    handleEditorChange(i, val) {
      this.$set(this.formData.responseMsg, i, val);
    },
    //添加回复
    handleAddReplyEditor() {
      this.formData.responseMsg.push(defaultEditorValue);
    },
    handleDelReplyEditor(i) {
      this.formData.responseMsg.splice(i, 1);
      this.$nextTick(() =>
        this.$refs.editor.forEach(vnode => {
          vnode.peek();
        })
      );
    },
    // 关闭弹框
    handleCloseDialog() {
      this.$emit("update:visible", false);
    },
    handleSubmitDialog() {
      const {
        ruleName,
        responseMsg,
        tagNameList,
        date,
        keyWord
      } = this.formData;
      const sendData = responseMsg.map(item => this.genResponseMsg(item));
      let responseMsgStr = (sendData.length && JSON.stringify(sendData)) || "";
      //替换模版变量为空
      if (responseMsgStr) {
        //替换小程序ID
        responseMsgStr = responseMsgStr.replace(
          /!!miniAppId!!/g,
          this.miniprogramInfo.wxAppId || ""
        );
        //在有营销活动ID的链接上追加参数
        responseMsgStr = responseMsgStr
          .replace(/!!wxAppId!!/g, this.wxAppId || "")
          .replace(/!!marketingActivityId!!/g, "")
          .replace(/!!marketingEventId!!/g, "")
          .replace(/!!spreadFsUid!!/g, "");
      }
      let params = {
        keyWord,
        operator: this.operator,
        responseMsg: responseMsgStr,
        ruleName,
        tagNameList,
        validFrom: date[0],
        validTo: date[1],
        wxAppId: this.wxAppId
      };
      console.log("submitData", params);
      let apiName;
      if (this.selectId) {
        apiName = "updateWxAutoReplyRule";
        params.id = this.selectId;
      } else {
        apiName = "addWxAutoReplyRule";
      }
      http[apiName](params).then(res => {
        this.handleCloseDialog();
        this.$emit("refresh");
        if (res && res.errCode == 0) {
          FxUI.Message.success($t('marketing.commons.czcg_33130f'));
        }
      });
    },
    handleValidateForm() {
      const form = this.$refs.form;
      form.validate().then(valid => {
        valid && this.handleSubmitDialog();
      });
    },
    validateContent(rule, value, callback) {
      let errText = $t('marketing.commons.qsrhfnr_f9d980')
      const valid = value.every((item, i) => {
        if (!item.content) {
          this.$set(value, i, {
            ...item,
            err: errText,
          })
        }
        if ((item.msgType == 3 && item.coverImageSize >= 1 * 1024 * 1024) || (item.msgType == 6 && item.size >= 1 * 1024 * 1024)) {
          errText = $t('marketing.pages.qywx_manage.tpdxcgqzxs_6d0ed8')
          this.$set(value, i, {
            ...item,
            err: errText,
          })
          return false
        }
        return !!item.content
      })
      if (!valid) {
        return callback(new Error(errText))
      }
      callback()
    },
  }
};
</script>

<style lang="less" module>
.keywordresdialog_wrapper {
  :global {
    .el-dialog__header {
      box-shadow: 0 2px 5px 0 rgba(69, 79, 91, 0.08);
    }
    .el-dialog__body {
      max-height: calc(100vh - 40vh) !important;
      padding-right: 10px !important;
      overflow-y: auto;
    }
    .el-date-editor {
      width: 235px !important;
    }
  }
  .form {
    flex: 1;
    min-width: 0;
    padding: 0px 60px 20px 17px;
    .date_wrapper {
      .el-date-editor {
        width: 216px !important;
      }
    }
    :global {
      .el-form-item {
        margin-bottom: 18px;
      }
    }
    .reply_msg_editor {
      position: relative;
      margin-bottom: 20px;
      .error_tip {
        color: #f56c6c;
        font-size: 12px;
        line-height: 1;
        padding-top: 6px;
      }
      .content {
        flex: 1 auto;
        height: 300px;
        line-height: initial;
        border: 1px solid @border-color-base;
        border-radius: 3px;
        box-sizing: border-box;
        :global {
          .wechat_editor_wrap {
            height: 230px;
          }
          .proactivity-vmsg {
            height: 200px;
          }
          .appg-imguploader-wrap .appg-imguploader-preview {
            height: 200px;
          }
        }
      }
      :global {
        .fulltext__material {
          position: relative;
        }
        .fulltext__material-time {
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
        .fulltext__material-content {
          width: 240px;
          height: 220px;
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
          border: 1px solid @border-color-base;
          .fulltext__material-image {
            height: 125px !important;
          }
          .fulltext__material-title {
            height: 20px;
            -webkit-line-clamp: 1;
          }
          .fulltext__material-del {
            line-height: 30px;
          }
        }
        .fulltext__material-btn {
          width: 240px;
          height: 224px;
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
          border: 1px dashed @border-color-base;
          display: flex;
          align-items: center;
          align-items: center;
          flex-direction: column;
          justify-content: center;
        }
      }
    }
    .btnadd {
      font-size: 13px;
      line-height: 36px;
      text-align: center;
      color: var(--color-primary06,#407FFF);
      background: #f4f6f9;
      cursor: pointer;
    }
    .btndel {
      position: absolute;
      right: -50px;
      top: 45%;
      padding: 0 10px;
      color: #c1c5ce;
      cursor: pointer;
      &:hover {
        color: var(--color-primary06,#407FFF);
      }
    }
    .keyWord_wrapper {
      display: flex;
      .select {
        margin-right: 10px;
      }
    }
  }
  .dialog_footer {
    display: flex;
    .tips {
      flex: 1;
      text-align: left;
      font-size: 14px;
      color: #545861;
      height: 37px;
      line-height: 37px;
    }
  }
}
</style>
