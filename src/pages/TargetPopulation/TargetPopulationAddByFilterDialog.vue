<template>
  <Dialog
    :title="$t('marketing.pages.TargetPopulation.adxtjxz_61b197')"
    class="TargetPopulationAddByFilterDialog"
    append-to-body
    :ok-text="$t('marketing.commons.qr_e83a25')"
    :visible="visible"
    :loading="isSubmit"
    width="700px"
    :z-index="curIndex"
    @onClose="$emit('update:visible', false)"
    @onSubmit="handleSubmit"
  >
    <div class="TargetPopulationAddByFilterDialog__body">
      <ElForm
        ref="form"
        label-width="110px"
        label-position="left"
        :model="formData"
        :rules="formRules"
      >
        <ElFormItem
          class="body__FilterRules"
          :label="$t('marketing.commons.sxfs_711eb1')"
          required
        >
          <div class="FilterRules__searchType">
            <ElRadioGroup
              v-model="formData.searchType"
              @input="handleSearchTypeChange"
            >
              <ElRadio
                v-for="searchType in formatSearchTypes"
                :key="searchType.searchType"
                :label="searchType.searchType"
              >
                {{ searchType.shortTitle }}
              </ElRadio>
            </ElRadioGroup>
          </div>
        </ElFormItem>
        <ElFormItem
          class="body__FilterRules"
          :label="$t('marketing.commons.sxgz_110167')"
          prop="ruleGroupJson"
        >
          <TargetPopulationCreateSetDetailFilter
            ref="filter"
            class="FilterRules__filter"
            :hide-label="true"
            :search-type="formData.searchType"
          />
        </ElFormItem>
        <ElFormItem
          v-if="formData.searchType === 6"
          :label="$t('marketing.commons.sjfw_90fdc8')"
          prop="dataRange"
        >
          <CheckboxGroup v-model="formData.dataRange">
            <Checkbox :label="1">
              {{ $t('marketing.commons.cxmztjdkhs_eacc49') }}
            </Checkbox>
            <Checkbox :label="2">
              {{ $t('marketing.commons.cxgldxssj_f75079') }}
            </Checkbox>
            <Checkbox :label="3">
              {{ $t('marketing.commons.cxgldlxrsj_6e77f6') }}
            </Checkbox>
            <Checkbox :label="4">
              {{ $t('marketing.commons.cxgldqywxk_881651') }}
            </Checkbox>
          </CheckboxGroup>
        </ElFormItem>
        <ElFormItem :label="$t('marketing.pages.setting.xgsm_9838b3')">
          <p style="background: #f2f2f2;padding: 15px 8px;font-size: 12px;line-height: 20px;">
            {{ formData.type === 1 ? $t('marketing.commons.dgrqzdrsxz_2ca48c') : $t('marketing.commons.dgrqzdrsxz_1074e3') }}
          </p>
        </ElFormItem>
      </ElForm>
    </div>
  </Dialog>
</template>

<script>
import { getTopZIndex } from '@/utils/helper.js'
import Dialog from '@/components/dialog/index.vue'
import { searchTypes as sTypes } from './TargetPopulationConfig.js'
import TargetPopulationCreateSetDetailFilter from './TargetPopulationCreate/TargetPopulationCreateSetDetailFilter.vue'

export default {
  components: {
    Dialog,
    ElForm: FxUI.Form,
    ElFormItem: FxUI.FormItem,
    ElRadioGroup: FxUI.RadioGroup,
    ElRadio: FxUI.Radio,
    CheckboxGroup: FxUI.CheckboxGroup,
    Checkbox: FxUI.Checkbox,
    TargetPopulationCreateSetDetailFilter,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    marketingUserGroupId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      searchTypes: sTypes,
      isSubmit: false,

      formData: {
        searchType: 2,
        ruleGroupJson: [], // 群组筛选条件 [{objectAPIName: '', query: {}}]
        ruleGroupValue: null, // 群组条件组合规则描述
        tagNames: [], // 标签名列表
        tagOperator: null, // 标签搜索符
        dataRange: [],
      },
      formRules: {
        ruleGroupJson: [
          {
            required: true,
            message: $t('marketing.commons.qszsxgz_5a0ca1'),
            trigger: 'blur',
            validator: async (rule, value, callback) => {
              const { valid } = await this.getFormData()
              if (!valid) {
                callback(new Error($t('marketing.commons.qszsxgz_5a0ca1')))
              } else {
                callback()
              }
            },
          },
        ],
        dataRange: [
          {
            required: true,
            message: $t('marketing.commons.qxzsjfw_d8de22'),
            trigger: 'blur',
            validator: (rule, value, callback) => {
              if (this.formData.searchType === 6 && (!value || !value.length)) {
                callback(new Error($t('marketing.commons.qxzsjfw_d8de22')))
              } else {
                callback()
              }
            },
          },
        ],
      },
    }
  },
  computed: {
    formatSearchTypes() {
      const searchTypes = JSON.parse(JSON.stringify(this.searchTypes))
      const { isEnterpriseLibraryOpen, isWechatOpen, isQywxOpen } = this.MARKETING_GLOBAL.OPENINFO

      return searchTypes.filter(item => {
         // 隐藏企业库筛选
        if(!isEnterpriseLibraryOpen) {
          if(item.searchType === 3) {
            return false
          }
        }
        // 隐藏公众号粉丝筛选
        if(!isWechatOpen) {
          if(item.searchType === 4) {
            return false
          }
        }
        // 隐藏企业微信客户筛选
        if(!isQywxOpen) {
          if(item.searchType === 5) {
            return false
          }
        }
        return true
      })
    },

    curIndex() {
      if (this.visible) {
        return getTopZIndex()
      }

      return 0
    },
  },
  methods: {
    handleSearchTypeChange() {
      this.$refs.form.clearValidate()
    },
    async handleSubmit() {
      this.$refs.form.validate(async valid => {
        if (valid) {
          // await this.getPopulationCount()
          this.handleAddUser()
        }
      })
    },
    async handleAddUser() {
      const options = {
        marketingUserGroupId: this.marketingUserGroupId,
        addType: this.formData.searchType, // addType: 999, // 999 根据CRM对象添加, 1 按照CRM过滤条件 2 按标签 3 按企业库添加
        ...this.formData,
      }
      this.isSubmit = true
      const res = await YXT_ALIAS.http.addMarketingUserToGroup(options)
      this.isSubmit = false
      if (res && res.errCode === 0) {
        FxUI.Message.success($t('marketing.commons.tjcg_3fdaea'))
        this.$emit('update:visible', false)
        this.$emit('submit')
      }
    },
    async getFormData() {
      let valid = false

      const {
        ruleGroupJson, ruleGroupValue, tagNames, tagOperator, excludeTags, excludeTagNames, tagDescription,
      } = await this.$refs.filter.getFilterData() // 异步获取crm-filter数据
      if (this.formData.searchType === 2) {
        this.formData.tagNames = tagNames
        this.formData.tagOperator = tagOperator
        this.formData.excludeTags = excludeTags
        this.formData.excludeTagNames = excludeTagNames
        this.formData.description = tagDescription
        valid = tagNames && tagNames.length > 0
      } else {
        this.formData.ruleGroupJson = ruleGroupJson
        this.formData.ruleGroupValue = ruleGroupValue
        this.formData.description = ruleGroupValue
        valid = ruleGroupJson && ruleGroupJson.length > 0
      }
      return { valid }
    },
    async getPopulationCount(){
      // const options = {
      //   marketingUserGroupId: this.marketingUserGroupId,
      //   addType: this.formData.searchType, // addType: 999, // 999 根据CRM对象添加, 1 按照CRM过滤条件 2 按标签 3 按企业库添加
      //   ...this.formData,
      // }
      // const res = await http.getPopulationCount(options)
      // const { errCode, data } = res
      // if(errCode === 0 && data.count > 100000){
      //   FxUI.Message.warning('当前条件下查询到数据超过10W，系统将自动新建多个目标人群，请稍后刷新列表');
      // }
    }
  },

}
</script>

<style lang="less" scoped>
.TargetPopulationAddByFilterDialog {
  .TargetPopulationAddByFilterDialog__body {
    .body__FilterRules {
      .FilterRules__searchType {
        /deep/ .el-radio {
          margin-right: 15px;
          margin-bottom: 10px;
        }
        /deep/ .el-radio__label {
          font-size: 12px;
        }
      }
    }
  }
}
</style>
