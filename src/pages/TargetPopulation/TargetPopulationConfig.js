export const channels = {
  userPhoneNumber: {
    color: '#54D1C7',
    label: $t('marketing.commons.sj_9f9d36'),
  },
  wxFansNumber: {
    color: '#FFA250',
    label: $t('marketing.commons.gzh_215fee'),
  },
  emailUserNumber: {
    color: '#FFE06C',
    label: $t('marketing.commons.yj_e9e805'),
  },
  wxWorkExternalUserNumber: {
    color: '#81B2FE',
    label: $t('marketing.commons.qywx_ff17b9'),
  },
}

export const searchTypes = [{
  hide: false,
  title: $t('marketing.commons.abqsx_6d0ce3'),
  shortTitle: $t('marketing.commons.abq_3dd408'),
  searchType: 2,
  icon: 'iconbiaoqian',
  desc: $t('marketing.pages.TargetPopulation.azdzyhsdbq_f282ea'),
}, {
  title: $t('marketing.pages.TargetPopulation.aywdxtjsx_3da146'),
  shortTitle: $t('marketing.pages.TargetPopulation.aywdx_fdbbc5'),
  searchType: 1,
  icon: 'iconduixiang',
  desc: $t('marketing.pages.TargetPopulation.azsxdycrdx_b0febc'),
}, {
  title: $t('marketing.pages.TargetPopulation.aqyksx_4f7234'),
  shortTitle: $t('marketing.pages.TargetPopulation.aqyk_55ece2'),
  searchType: 3,
  icon: 'iconqiyekehu',
  desc: $t('marketing.pages.TargetPopulation.yqydsjdmbk_eb4cf3'),
}, {
  title: $t('marketing.pages.TargetPopulation.agzhfssx_1c1e16'),
  shortTitle: $t('marketing.pages.TargetPopulation.agzhfs_8b6329'),
  searchType: 4,
  icon: 'iconweixin',
  desc: $t('marketing.pages.TargetPopulation.kdgzhfsabq_4460a7'),
}, {
  title: $t('marketing.pages.TargetPopulation.aqywxkhsx_f320a0'),
  shortTitle: $t('marketing.pages.TargetPopulation.aqywxkh_2de946'),
  searchType: 5,
  icon: 'iconqiyeweixin1',
  desc: $t('marketing.pages.TargetPopulation.kysxqbdqyw_aca32d'),
}, {
  title: $t('marketing.commons.akhdxglsx_e18379'),
  shortTitle: $t('marketing.commons.akhdxglsx_e18379'),
  searchType: 6,
  icon: 'iconduixiang',
  desc: $t('marketing.pages.TargetPopulation.jykhdxdzdt_04ec1a'),
},
{
  title: $t('marketing.pages.TargetPopulation.ayhhwsx_91c690'),
  shortTitle: $t('marketing.pages.TargetPopulation.ayhhwsx_91c690'),
  searchType: 7,
  icon: 'iconduixiang',
  desc: $t('marketing.pages.TargetPopulation.jyyhqqddhd_5ac65b'),
}]

export const logTableColumns = [{
  id: 'createTime',
  label: $t('marketing.commons.sj_19fcb9'),
  width: 145,
}, {
  id: 'operatorName',
  label: $t('marketing.commons.czr_f9ac4b'),
  width: 101,
}, {
  id: 'description',
  label: $t('marketing.commons.cz_2b6bc0'),
  width: 654,
}]
