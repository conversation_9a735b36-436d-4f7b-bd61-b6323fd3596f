<template>
  <div class="TargetPopulationCreateSetDetailFilter">
    <div class="Filter__wrapper">
      <TargetPopulationCreateSetDetailTags
        ref="TagsFilter"
        v-if="searchType === 2"
        :filterData="filterData"
        style="padding: 0 10px 0;"
      ></TargetPopulationCreateSetDetailTags>
      <MultiObjectFilter
        v-else
        ref="MultiObjectFilter"
        :class="[objectNames.length === 1 && 'noinnerborder']"
        :objectLists="objectNames"
        :filterData="filterData.ruleGroupJson"
        :initial="true"
        :noPadding="true"
      ></MultiObjectFilter>
    </div>
  </div>
</template>

<script>
import TargetPopulationCreateSetDetailTags from "./TargetPopulationCreateSetDetailTags";
import MultiObjectFilter from "@/components/multi-object-filter";
import http from "@/services/http/index";
import { mapState, mapActions } from "vuex";

export default {
  props: {
    isEdit: {
      type: Boolean,
      default: false
    },
    searchType: {
      type: Number,
      default: 1
    },
    filterData: {
      type: Object,
      default: () => ({})
    },
    hideLabel: {
      type: Boolean,
      default: false
    }
  },
  components: {
    TargetPopulationCreateSetDetailTags,
    MultiObjectFilter
  },
  data() {
    return {};
  },
  watch: {
    searchType: {
      handler(newVal) {
        if(!this.isEdit) {
          // 当用户选择按  用户行为筛选  的时候，把用户名称 不为空 这个条件默认展示出来
          if(newVal === 7 ) {
            this.filterData.ruleGroupJson = [
              {
                objectAPIName: 'UserBehaviorRecordsObj',
                query: {
                  filters: [
                    {
                      fieldName: 'user_marketing_name',
                      fieldType: '1',
                      fieldValues: [''],
                      isCascade: null,
                      operator: 'ISN',
                      pattern: null,
                      valueType: null,
                    }
                  ],
                  tagNames: null,
                  tagOperator: null,
                  wheres: null,
                }
              }
            ]
          }else {
            this.filterData.ruleGroupJson = []
          }
        }
      },
      immediate: true
    }
  },
  computed: {
    objectNames() {
      // searchType 1 按字段 2按标签 3按企业库 4 按微信用户 5 按企业微信客户
      let _objectNames = [];
      const {
        isWechatOpen,
        isQywxOpen,
        isMemberOpen,
        isEnterpriseLibraryOpen
      } = this.MARKETING_GLOBAL.OPENINFO;
      if (this.searchType === 1) {
        _objectNames = this.isEdit
          ? [
              { name: $t('marketing.commons.xsxs_d4ed8c'), value: "LeadsObj" },
              { name: $t('marketing.commons.kh_ff0b20'), value: "AccountObj" },
              { name: $t('marketing.commons.lxr_52409d'), value: "ContactObj" },
              { name: $t('marketing.commons.gzhfs_22a1e1'), value: "WechatFanObj" },
              { name: $t('marketing.commons.qywxkh_000b5c'), value: "WechatWorkExternalUserObj" },
              { name: $t('marketing.commons.hy_4d9dd5'), value: "MemberObj" }
              // { name: "企业库", value: "EnterpriseInfoObj" },
            ]
          : [
              { name: $t('marketing.commons.xsxs_d4ed8c'), value: "LeadsObj" },
              { name: $t('marketing.commons.kh_ff0b20'), value: "AccountObj" },
              { name: $t('marketing.commons.lxr_52409d'), value: "ContactObj" },
              { name: $t('marketing.commons.hy_4d9dd5'), value: "MemberObj" }
            ];
        _objectNames = [..._objectNames, ...this.customizeObjectList];
      } else if (this.searchType === 2) {
        // 按标签
      } else if (this.searchType === 3) {
        _objectNames = [{ name: $t('marketing.commons.qyk_ac3d26'), value: "EnterpriseInfoObj" }];
      } else if (this.searchType === 4) {
        _objectNames = [{ name: $t('marketing.commons.gzhfs_22a1e1'), value: "WechatFanObj" }];
      } else if (this.searchType === 5) {
        _objectNames = [
          { name: $t('marketing.commons.qywxkh_000b5c'), value: "WechatWorkExternalUserObj" }
        ];
      } else if (this.searchType === 6) {
        _objectNames = [
        { name: $t('marketing.commons.kh_ff0b20'), value: "AccountObj" }
        ];
      } else if (this.searchType === 7) {
        _objectNames = [
        { name: $t('marketing.pages.TargetPopulation.yhhwjl_ef3cd3'), value: "UserBehaviorRecordsObj" }
        ];
      }
      // 隐藏公众号粉丝选项
      const indexW = _objectNames.findIndex(
        item => item.value === "WechatFanObj"
      );
      !isWechatOpen && indexW > -1 && _objectNames.splice(indexW, 1);
      // 隐藏企业微信客户选项
      const indexQ = _objectNames.findIndex(
        item => item.value === "WechatWorkExternalUserObj"
      );
      !isQywxOpen && indexQ > -1 && _objectNames.splice(indexQ, 1);
      // 隐藏会员选项
      const indexM = _objectNames.findIndex(item => item.value === "MemberObj");
      !isMemberOpen && indexM > -1 && _objectNames.splice(indexM, 1);
      // 隐藏企业库选项
      const indexE = _objectNames.findIndex(
        item => item.value === "EnterpriseInfoObj"
      );
      !isEnterpriseLibraryOpen && indexE > -1 && _objectNames.splice(indexE, 1);
      return _objectNames;
    },
    filterTips() {
      const tipsmap = [
        "",
        $t('marketing.commons.ywdxtj_70756a'),
        $t('marketing.commons.bq_14d342'),
        $t('marketing.commons.qyk_ac3d26'),
        $t('marketing.commons.gzhfs_22a1e1'),
        $t('marketing.commons.qywxkh_000b5c'),
        $t('marketing.commons.khdxgl_d50ffb'),
      ];
      return $t('marketing.commons.asx_6d6c68', {data: ( {'option0': tipsmap[this.searchType]})});
    },
    ...mapState("TargetPopulation", ["customizeObjectList"]),
  },
  methods: {
    async getFilterData() {
      if (this.searchType === 2) {
        // 标签
        const TagsData = this.$refs.TagsFilter.getValue();
        return TagsData;
      } else {
        // 筛选
        const ruleGroupJson = this.$refs.MultiObjectFilter.getValue();
        let {
          filterPreview,
          filterPreviewStr: ruleGroupValue
        } = await this.$refs.MultiObjectFilter.getPreviewValue();
        console.log("getFilterData", filterPreview, ruleGroupValue);
        return { ruleGroupJson, ruleGroupValue };
      }
    },
  },
  mounted() {
    this.model_tagNameList = this.filterData;
  }
};
</script>

<style lang="less" scoped>
.TargetPopulationCreateSetDetailFilter {
  .Filter__label {
  }
  /deep/ .noinnerborder {
    .crm-w-table .conditon-wrap {
      border: none;
      padding: 0;
    }
  }
}
</style>
