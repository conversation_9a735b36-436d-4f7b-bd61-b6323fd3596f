<template>
  <div class="TargetPopulationCreateSetDetail">
    <ElForm
      ref="form"
      label-width="170px"
      label-position="left"
      :model="detailFormData"
      :rules="detailFormRules"
    >
      <ElFormItem
        :label="$t('marketing.commons.qzmc_acb4c1')"
        prop="name"
      >
        <ElInput
          v-model="detailFormData.name"
          :placeholder="$t('marketing.commons.qsr_02cc4f')"
          maxlength="20"
          :show-word-limit="true"
        />
      </ElFormItem>
      <ElFormItem
        :label="$t('marketing.commons.qzms_4aa992')"
        prop="description"
      >
        <ElInput
          v-model="detailFormData.description"
          type="textarea"
          :rows="4"
          resize="none"
          :placeholder="$t('marketing.commons.qsr_02cc4f')"
          maxlength="100"
          :show-word-limit="true"
        />
      </ElFormItem>
      <ElFormItem
        v-if="formData.type === 1"
        :label="$t('marketing.pages.TargetPopulation.zsfs_13af47')"
        prop="calculationPeriod"
        required
      >
        <ElRadioGroup v-model="detailFormData.calculationPeriod">
          <ElRadio :label="1">
            {{ $t('marketing.commons.sdgx_681606') }}
          </ElRadio>
          <ElRadio :label="2">
            {{ $t('marketing.commons.zdgx_6697e4') }}
          </ElRadio>
        </ElRadioGroup>
      </ElFormItem>
      <ElFormItem
        v-if="formData.type === 1"
        :label="$t('marketing.commons.sxfs_711eb1')"
        prop="calculationPeriod"
        required
      >
        <div class="Filter__label">
          {{ filterTips }}
        </div>
      </ElFormItem>
      <ElFormItem
        v-if="formData.type === 1"
        :label="$t('marketing.commons.sxgz_110167')"
        prop="filterRules"
      >
        <TargetPopulationCreateSetDetailFilter
          ref="filter"
          :is-edit="isEdit"
          :search-type="formData.searchType"
          :filter-data="detailFormData"
        />
      </ElFormItem>
      <ElFormItem
        v-if="formData.searchType === 6"
        :label="$t('marketing.commons.sjfw_90fdc8')"
        prop="dataRange"
      >
      <!-- 编辑状态下不允许编辑 -->
        <CheckboxGroup :disabled="isEdit" v-model="detailFormData.dataRange">
          <Checkbox :label="1">
            {{ $t('marketing.commons.cxmztjdkhs_eacc49') }}
          </Checkbox>
          <Checkbox :label="2">
            {{ $t('marketing.commons.cxgldxssj_f75079') }}
          </Checkbox>
          <Checkbox :label="3">
            {{ $t('marketing.commons.cxgldlxrsj_6e77f6') }}
          </Checkbox>
          <Checkbox :label="4">
              {{ $t('marketing.commons.cxgldqywxk_881651') }}
            </Checkbox>
        </CheckboxGroup>
      </ElFormItem>
      <ElFormItem
        :label="$t('marketing.pages.setting.xgsm_9838b3')"
      >
      <p style="background: #f2f2f2;padding: 15px 8px;font-size: 12px;line-height: 20px;">
        {{ formData.type === 1 ? $t('marketing.commons.dgrqzdrsxz_2ca48c') : $t('marketing.commons.dgrqzdrsxz_1074e3') }}
        <span v-if="marketingDataIsolation" class="permission-tip">{{ $t('marketing.commons.glykqlyxsj_07cc14') }}{{ dataPermissionRange || '--' }}</span>
      </p>
      </ElFormItem>
    </ElForm>
  </div>
</template>

<script>

import TargetPopulationCreateSetDetailFilter from './TargetPopulationCreateSetDetailFilter.vue'
import kisvData from '@/modules/kisv-data.js'
import http from '@/services/http/index.js'

export default {
  components: {
    ElForm: FxUI.Form,
    ElFormItem: FxUI.FormItem,
    ElInput: FxUI.Input,
    ElRadioGroup: FxUI.RadioGroup,
    ElRadio: FxUI.Radio,
    CheckboxGroup: FxUI.CheckboxGroup,
    Checkbox: FxUI.Checkbox,
    TargetPopulationCreateSetDetailFilter,
  },
  props: {
    isEdit: {
      type: Boolean,
      default: false,
    },
    formData: {
      type: Object,
      default: () => ({}),
    },
    dataPermissionRange: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      detailFormData: {
        name: null, // 群组名称
        description: null, // 群组描述
        calculationPeriod: 1, // 计算周期, 1手工计算 2 按天（自动更新）
        dataRange: [],
        ruleGroupJson: [], // 群组筛选条件 [{objectAPIName: '', query: {}}]
        ruleGroupValue: null, // 群组条件组合规则描述
        tagOperator: 'IN', // 标签搜索符: HASANYOF-包含以下任意标签 IN-包含以下所有标签
        tagNames: [], // 标签名列表
        excludeTags: false, // 是否指定排除标签
        excludeTagNames: [], // 要排除的标签名称
      },
      detailFormRules: {
        name: [
          {
            required: true,
            message: $t('marketing.pages.TargetPopulation.qsrrqmc_1b4ad5'),
            trigger: 'change',
          },
        ],
        description: [
          {
            required: true,
            message: $t('marketing.pages.TargetPopulation.qtxqzms_89a413'),
            trigger: 'change',
          },
        ],
        calculationPeriod: [
          {
            message: $t('marketing.commons.qxzzsfs_d121fb'),
            trigger: 'change',
            validator: (rule, value, callback) => {
              if (this.formData.type === 1 && (!value)) {
                callback(new Error($t('marketing.commons.qxzzsfs_d121fb')))
              } else {
                callback()
              }
            },
          },
        ],
        filterRules: [
          {
            required: true,
            trigger: 'change',
            validator: (rule, value, callback) => {
              console.log('filterRules', value)
              if (this.formData.type === 1 && (!this.filterRules || this.filterRules.length === 0)) {
                callback(new Error($t('marketing.commons.qszsxgz_5a0ca1')))
              } else {
                callback()
              }
            },
          },
        ],
        dataRange: [
          {
            required: true,
            message: $t('marketing.commons.qxzsjfw_d8de22'),
            trigger: 'change',
            validator: (rule, value, callback) => {
              if (this.formData.searchType === 6 && (!value || !value.length)) {
                callback(new Error($t('marketing.commons.qxzsjfw_d8de22')))
              } else {
                callback()
              }
            },
          },
        ],
      },
    }
  },
  computed: {
    curType() {
      return this.formData.type
    },
    defaultFilterData() {
      if (this.isEdit) {
        return this.formData.searchType === 2 ? this.formData.tagNames : this.formData.ruleGroupJson
      }
      return []
    },
    filterRules() {
      return this.formData.searchType === 2 ? this.detailFormData.tagNames : this.detailFormData.ruleGroupJson
    },
    filterTips() {
      const tipsmap = [
        '',
        $t('marketing.commons.ywdxtj_70756a'),
        $t('marketing.commons.bq_14d342'),
        $t('marketing.commons.qyk_ac3d26'),
        $t('marketing.commons.gzhfs_22a1e1'),
        $t('marketing.commons.qywxkh_000b5c'),
        $t('marketing.commons.khdxgl_d50ffb'),
        $t('marketing.pages.TargetPopulation.yhhw_77927d'),
      ]
      return $t('marketing.commons.asx_6d6c68', { data: ({ option0: tipsmap[this.formData.searchType] }) })
    },
    marketingDataIsolation() {
      return kisvData.datas.uinfo?.marketingDataIsolation
    },
  },
  watch: {
    detailFormData: {
      deep: true,
      handler() {
        this.handleSetDetail(this.detailFormData)
      },
    },
    filterRules() {
      console.log('watch filterRules', this.filterRules)
      this.detailFormData.filterRules = this.filterRules
    },
  },
  mounted() {
    if (this.isEdit) {
      this.detailFormData = this.formData
    }
  },
  methods: {
    handleSetDetail(detailFormData) {
      this.$emit('change', detailFormData)
    },
    async getFormData() {
      let valid = false
      // 动态人群
      if (this.formData.type === 1) {
        const {
          ruleGroupJson, ruleGroupValue, tagNames, tagOperator, excludeTags, excludeTagNames,
        } = await this.$refs.filter.getFilterData() // 异步获取crm-filter数据
        if (this.formData.searchType === 2) {
          this.detailFormData.tagNames = tagNames
          this.detailFormData.tagOperator = tagOperator
          this.detailFormData.excludeTags = excludeTags
          this.detailFormData.excludeTagNames = excludeTagNames
          // this.detailFormData.description = tagDescription;
        } else {
          this.detailFormData.ruleGroupJson = ruleGroupJson
          this.detailFormData.ruleGroupValue = ruleGroupValue
          // this.detailFormData.description = ruleGroupValue;
        }
      }
      this.$refs.form.validate(_valid => {
        valid = _valid // true-校验通过 false-不通过
      })
      return { valid }
    },
  },
}
</script>

<style lang="less" scoped>
.TargetPopulationCreateSetDetail {
  padding: 0 18px 0 0;
  .permission-tip{
    display: block;
  }
  /deep/ .el-form-item {
    margin-bottom: 20px;
  }
}
</style>
