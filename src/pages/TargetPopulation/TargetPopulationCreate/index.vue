<template>
  <Dialog
    :title=" $t('marketing.commons.mbrq_fec527', {data: ( {'option0': isEdit ? $t('marketing.commons.bj_95b351'): $t('marketing.commons.xj_26bb84')})})"
    class="TargetPopulationCreate"
    append-to-body
    :ok-text="okText"
    :visible="visible"
    :loading="isSubmit"
    width="730px"
    @onClose="$emit('update:visible', false)"
    @onSubmit="handleNext"
  >
    <div class="TargetPopulationCreate__body">
      <div
        v-show="!isEdit"
        class="body__steps"
      >
        <div
          v-for="(step, index) in model_steps"
          :key="step.id"
          :class="['body__step', step.cur && 'cur', index < curStepIndex && 'pointer']"
          @click="handleChangeStep(step, index)"
        >
          {{ index + 1 }}.{{ step.label }} {{ (index + 1) < model_steps.length ? '>' : '' }}
        </div>
      </div>
      <div class="body__main">
        <TargetPopulationCreateChooseType
          v-if="curStep === 'chooseType'"
          :form-data="model_formData"
          :data-permission-range="dataPermissionRange"
          :dynamic-duato="dynamicDuato"
          @change="handleSetFormData"
        />
        <TargetPopulationCreateChooseFilter
          v-if="curStep === 'chooseFilter'"
          :form-data="model_formData"
          @change="handleSetFormData"
        />
        <TargetPopulationCreateSetDetail
          v-if="curStep === 'setDetail'"
          ref="SetDetail"
          :is-edit="isEdit"
          :data-permission-range="dataPermissionRange"
          :form-data="model_formData"
          @change="handleSetFormData"
        />
      </div>
    </div>
  </Dialog>
</template>

<script>
import Dialog from '@/components/dialog/index.vue'
import TargetPopulationCreateChooseType from './TargetPopulationCreateChooseType.vue'
import TargetPopulationCreateChooseFilter from './TargetPopulationCreateChooseFilter.vue'
import TargetPopulationCreateSetDetail from './TargetPopulationCreateSetDetail.vue'
import http from '@/services/http/index.js'
import kisvData from '@/modules/kisv-data.js'

export default {
  components: {
    Dialog,
    TargetPopulationCreateChooseType,
    TargetPopulationCreateChooseFilter,
    TargetPopulationCreateSetDetail,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    editDefaultData: {
      type: Object,
      default: () => ({}),
    },
    groupId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      curStep: 'chooseType', // chooseType-选择人群类型 chooseFilter-选择筛选方式 setDetail-设置详细信息
      isSubmit: false,
      model_formData: {
        type: 2, // 人群类型, 1 动态 2 静态
        searchType: 2, // 搜索类型, 1 按字段 2按标签 3按企业库 4 按微信用户 5 按企业微信客户 6 按客户对象关联搜索 7 按用户行为记录
        name: null, // 群组名称
        description: null, // 群组描述
        calculationPeriod: null, // 计算周期, 1手工计算 2 按天（自动更新）
        ruleGroupJson: [], // 群组筛选条件 [{objectAPIName: '', query: {}}]
        ruleGroupValue: null, // 群组条件组合规则描述
        tagOperator: 'IN', // 标签搜索符: HASANYOF-包含以下任意标签 IN-包含以下所有标签
        tagNames: [], // 标签名列表
        excludeTags: false, // 是否指定排除标签
        excludeTagNames: [], // 要排除的标签名称
        dataRange: [],
      },
      dynamicDuato: {
        quota: 50,
        used: 0,
        left: 0,
      },
      dataPermissionRange: ''
    }
  },
  computed: {
    isEdit() {
      return !!this.editDefaultData.id
    },
    okText() {
      return this.model_steps[this.curStepIndex + 1] ? $t('marketing.commons.xyb_38ce27') : $t('marketing.pages.TargetPopulation.qrtj_28dda2')
    },
    model_steps() {
      const steps = [
        { id: 'chooseType', cur: false, label: $t('marketing.pages.TargetPopulation.xzrqlx_a314c5') },
        { id: 'chooseFilter', cur: false, label: $t('marketing.pages.TargetPopulation.xzsxfs_d0aa95') },
        { id: 'setDetail', cur: false, label: $t('marketing.pages.TargetPopulation.szxxxx_1dd12c') },
      ]

      if (this.model_formData.type === 2) {
        steps.splice(1, 1)
      }
      steps.filter(item => item.id === this.curStep)[0].cur = true
      return steps
    },
    curStepIndex() {
      return this.model_steps.indexOf(this.model_steps.filter(item => item.id === this.curStep)[0])
    },
    marketingDataIsolation() {
      return kisvData.datas.uinfo?.marketingDataIsolation
    },
  },
  mounted() {
    console.log('editDefaultData', this.editDefaultData)
    this.getDynamicUserMarketingGroupQuota()
    this.getDataOwnDepartment()
    if (this.isEdit) {
      this.curStep = 'setDetail'
      this.model_formData = this.editDefaultData
    }
  },
  methods: {
    handleSetFormData(formData) {
      this.model_formData = {
        ...this.model_formData,
        ...formData,
      }
    },
    handleChangeStep(step, index) {
      if (index < this.curStepIndex) {
        this.curStep = step.id
      }
    },
    async handleNext() {
      // 编辑态下无需校验配额
      if (this.model_formData.type === 1 && this.dynamicDuato.left <= 0 && !this.isEdit) {
        FxUI.Message.warning($t('marketing.pages.TargetPopulation.ndkypebzks_a9519f'))
        return
      }
      if (this.model_steps[this.curStepIndex + 1]) {
        this.curStep = this.model_steps[this.curStepIndex + 1].id
      } else {
        await this.handleSubmit()
      }
    },
    async handleSubmit() {
      this.isSubmit = true
      // 获取crm-filter数据并数据校验
      const { valid } = await this.$refs.SetDetail.getFormData()
      if (!valid) {
        this.isSubmit = false
        return
      }
      console.log('handleSubmit before nextTick', this.model_formData)
      // 等待this.$refs.SetDetail将数据写入model_formData
      this.$nextTick(async () => {
        console.log('handleSubmit after nextTick', this.model_formData)
        delete this.model_formData.filterRules
        const params = {
          ...this.model_formData,
          groupId: this.groupId,
        }

        const res = await YXT_ALIAS.http[this.isEdit ? 'updateMarketingUserGroup' : 'addMarketingUserGroup'](params)
        this.isSubmit = false
        if (res && res.errCode === 0) {
          FxUI.Message.success($t('marketing.commons.cg_bd6c8c', { data: ({ option0: this.isEdit ? $t('marketing.commons.bj_95b351') : $t('marketing.commons.xj_26bb84') }) }))
          this.$emit('update:visible', false)
          this.$emit('submited')
        }
      })
    },
    async getDynamicUserMarketingGroupQuota() {
      const { data, errCode, errMsg } = await http.getDynamicUserMarketingGroupQuota()
      if (errCode === 0) {
        this.dynamicDuato = data
        this.dynamicDuato.left = data.quota - data.used
      } else {
        FxUI.Message.error(errMsg || $t('marketing.pages.TargetPopulation.hqdtrqkype_770c52'))
      }
    },
    // 获取数据权限范围
    getDataOwnDepartment() {
      if (!this.marketingDataIsolation) {
        return
      }
      http.getDataOwnDepartment().then(({ errCode, data }) => {
        if (errCode === 0) {
          this.dataIsolationLoading = false
          this.dataPermissionRange = (data || []).join('、')
        }
      })
    },
  },
}
</script>

<style lang="less" scoped>
.TargetPopulationCreate {
  &/deep/.el-dialog__body {
    min-height: 440px;
    padding: 15px 24px 0 24px;
  }
  .TargetPopulationCreate__body {
    padding-bottom: 10px;
    .body__steps {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
      .body__step {
        font-size: 14px;
        color: #91959E;
        margin-right: 5px;
        &.cur {
          color: #181C25;
        }
        &.pointer {
          cursor: pointer;
          &:hover {
            text-decoration: underline;
          }
        }
      }
    }
  }
}
</style>
