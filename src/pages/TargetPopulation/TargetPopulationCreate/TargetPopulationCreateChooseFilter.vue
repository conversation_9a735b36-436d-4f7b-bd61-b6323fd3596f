<template>
  <div class="TargetPopulationCreateChooseFilter">
    <div
      v-for="item in formatSearchTypes"
      :key="item.type"
      :class="['ChooseFilter__item', curSearchType === item.searchType && 'selected']"
      @click="handleChooseTypes(item)"
    >
      <div class="item__title">
        <i :class="['iconfont', item.icon]" />{{ item.title }}
      </div>
      <div class="item__desc">
        {{ item.desc }}
      </div>
    </div>
  </div>
</template>

<script>
import { searchTypes } from '../TargetPopulationConfig.js'
import kisvData from '@/modules/kisv-data.js'

export default {
  props: {
    formData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      vDatas: kisvData.datas,
      // 1 按字段 2按标签 3按企业库 4 按微信用户 5 按企业微信客户
      searchTypes,
    }
  },
  computed: {
    formatSearchTypes() {
      const searchTypes = JSON.parse(JSON.stringify(this.searchTypes))
      const { isEnterpriseLibraryOpen, isWechatOpen, isQywxOpen } = this.MARKETING_GLOBAL.OPENINFO

      return searchTypes.filter(item => {
         // 隐藏企业库筛选
        if(!isEnterpriseLibraryOpen) {
          if(item.searchType === 3) {
            return false
          }
        }
        // 隐藏公众号粉丝筛选
        if(!isWechatOpen) {
          if(item.searchType === 4) {
            return false
          }
        }
        // 隐藏企业微信客户筛选
        if(!isQywxOpen) {
          if(item.searchType === 5) {
            return false
          }
        }
        return true
      })
    },
    curSearchType() {
      return this.formData.searchType
    },
  },
  methods: {
    handleChooseTypes({ searchType }) {
      this.$emit('change', { searchType })
    },
  },

}
</script>

<style lang="less" scoped>
.TargetPopulationCreateChooseFilter {
  display: flex;
  flex-wrap: wrap;
  .ChooseFilter__item {
    // 一行放三个
    width: calc(1/3 * (100% - 2 * 10px));
    margin: 0 10px 15px 0;
    padding: 15px;
    box-sizing: border-box;
    border: 1px solid #E9EDF5;
    border-radius: 4px;
    min-width: 205px;
    height: 200px;
    display: flex;
    flex-direction: column;
    cursor: pointer;
    position: relative;
    &:hover {
      border-color: var(--color-primary06,#407FFF);
    }
    &:nth-child(3n) {
      margin-right: 0;
    }
    &.selected {
        border: 1px solid var(--color-primary06,#407FFF);
        &::before {
          content: '\00a0';
          display: inline-block;
          border: 2px solid #fff;
          border-top-width: 0;
          border-right-width: 0;
          width: 9px;
          height: 5px;
          transform: rotate(-50deg);
          position: absolute;
          top: 2px;
          left: 2px;
          z-index: 3;
        }
        &::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 0;
          height: 0;
          border-top: 25px solid var(--color-primary06,#407FFF);
          border-right: 25px solid transparent;
        }
      }
    .item__title {
      display: flex;
      align-items: center;
      color: #181C25;
      font-size: 14px;
      .iconfont {
        margin-right: 5px;
        font-size: 18px;
        color: #6E7285;
      }
    }
    .item__desc {
      color: #91959E;
      font-size: 12px;
    }
  }
}

</style>
