<!-- 组件说明 -->
<template>
  <div>
    <sideslip-popup
      class="whatsapp-sideslip-detail"
      width="900px"
      :visible="visible"
      :overflowy-hidden="detailVisible"
      @close="handleClose"
    >
      <div class="header">
        <div class="content">
          <div class="main">
            <div class="p1">
              {{ whatsAppMarketingActivityDetailVO.name || '' }}
            </div>
            <div class="info">
              {{ $t('marketing.pages.whatsapp.mbxxtz_f039a3') }}
              <fx-button
                type="text"
                style="font-size: 12px"
                @click="handleShowPreview"
              >
                {{ $t('marketing.commons.djck_08d976') }}
              </fx-button>
            </div>
            <div class="info">
              <span class="info-item">{{ $t('marketing.commons.fszh_f4ff7f') }}：{{ whatsAppMarketingActivityDetailVO.businessPhone || '' }}</span>
              <span class="info-item">{{ $t('marketing.commons.fsdx_6a571c') }}
                {{ whatsAppMarketingActivityDetailVO.sendTargetDesc || '' }}</span>
            </div>
            <div class="info">
              <span class="info-item">{{ $t('marketing.commons.fssj_f6cbc9')
              }}{{ formatDateTime(whatsAppMarketingActivityDetailVO.sendTime, 'YYYY-MM-DD hh:mm') || '' }}
              </span>
              <span class="info-item">{{ $t('marketing.commons.fszt_ac4f53')
              }}{{ SEND_OPTIONS[whatsAppMarketingActivityDetailVO.sendStatus] || '' }}
              </span>
              <span class="info-item">{{ $t('marketing.commons.czr_8146bb') }}{{ detailResult.createByName || '' }}</span>
            </div>
          </div>
        </div>
        <div class="options">
          <fx-button
            type="primary"
            class="button"
            size="mini"
            plain
            @click.stop="handleSendAgain"
          >
            {{ $t('marketing.commons.fz_79d3ab') }}
          </fx-button>
        </div>
      </div>
      <div class="side-title">
        {{ $t('marketing.commons.sjtj_1b7cba') }}
      </div>
      <div class="number-wrapper">
        <div class="number">
          <div class="item">
            <div class="num">
              {{ whatsAppMarketingActivityDetailVO.totalCount || '0' }}
            </div>
            <div class="des">
              {{ $t('marketing.commons.fsrs_8428aa') }}
            </div>
          </div>
          <div class="item">
            <div class="num">
              {{ whatsAppMarketingActivityDetailVO.deliveredCount || '0' }}
            </div>
            <div class="des">
              {{ $t('marketing.commons.sdrs_c1ec4c') }}
            </div>
          </div>
          <div class="item">
            <div class="num">
              {{ whatsAppMarketingActivityDetailVO.readCount || '0' }}
            </div>
            <div class="des">
              {{ $t('marketing.commons.ydrs_a6c562') }}
            </div>
          </div>
        </div>
      </div>
      <div class="title-wrapper">
        <div class="side-title">
          {{ $t('marketing.commons.fsxq_f36411') }}
        </div>
      </div>
      <div class="table-header__wrapper">
        <div>
          <fx-input
            v-model="phoneKeyword"
            size="mini"
            class="search-input"
            :placeholder="$t('marketing.pages.whatsapp.asjhss_658590')"
            prefix-icon="el-icon-search"
            @change="search"
          />
          <fx-button
            size="mini"
            type="primary"
            @click="handleSearch"
          >
            {{ $t('marketing.commons.ss_e5f71f') }}
          </fx-button>
        </div>
        <div class="btn-group-sort">
          <fx-button-group>
            <fx-button
              v-for="(status, key) in SEND_STATUS2"
              :key="key"
              plain
              size="mini"
              :class="currBtn === key ? 'curr-btn' : ''"
              @click="choose(key)"
            >
              <div class="status-btn__wrapper">
                {{ `${status.label}（${countData[key] || 0}）` }}
                <QuestionTooltip
                  v-if="status.value === 'sent'"
                  effect="dark"
                  class="question-tooltip"
                  :offset="192"
                >
                  <div slot="question-content">
                    {{ $t('marketing.pages.whatsapp.xxycgtsdfw_fb319b') }}
                  </div>
                </QuestionTooltip>
              </div>
            </fx-button>
          </fx-button-group>
        </div>
      </div>
      <div
        v-loading="tableLoading"
        class="side-table__wrapper"
      >
        <v-table
          class="tablewbg"
          tid="whatsapp-side-send-table"
          :data="tableDatas"
          :columns="detailColumns"
          @click:row="handleShowDetail"
          @custom:user-action="handleShowDetail"
        />
      </div>
      <v-pagen
        :pagedata.sync="pageData"
        @change="handlePageChange"
      />
      <user-detail
        :top="'0px'"
        :user-id="detailId"
        :visible="detailVisible"
        @close="handleCloseDetail"
      />
      <loading-mask :show="flag_showLoadingMask" />
    </sideslip-popup>
    <preview-dialog
      v-if="previewDialogVisible"
      :visible="previewDialogVisible"
      :form-data="whatsappFormData"
      @close="previewDialogVisible = false"
    />
  </div>
</template>

<script>
import UserDetail from '@/pages/user/detail.vue'
import LoadingMask from '@/components/loading-mask/index.vue'
import VTable from '@/components/table-ex/index.vue'
import SideslipPopup from '@/components/sideslip-popup/index.vue'
import PreviewDialog from './preview-dialog.vue'
import http from '@/services/http/index.js'
import {
  SEND_TARGET, SEND_TYPE, SEND_STATUS2, detailColumns, SEND_OPTIONS,
} from './const.js'
import QuestionTooltip from '@/components/questionTooltip/index.vue'

import VPagen from '@/components/kitty/pagen.vue'

import util from '@/services/util/index.js'

export default {
  components: {
    UserDetail,
    SideslipPopup,
    VTable,
    LoadingMask,
    PreviewDialog,
    QuestionTooltip,
    VPagen,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    item: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      detailId: '',
      detailVisible: false,
      tableDatas: [],
      currentItem: {},
      showLoadingMask: true,
      currBtn: '0',
      detailResult: {},
      SEND_OPTIONS,
      SEND_TARGET,
      SEND_TYPE,
      SEND_STATUS2,
      detailColumns,
      tableLoading: true,
      flag_showLoadingMask: true,
      phoneKeyword: '',
      previewDialogVisible: false,
      whatsappFormData: {},
      pageData: {
        layout: 'prev, pager, next, total, sizes, jumper',
        pageNum: 1,
        totalCount: 0,
        pageSize: 10,
        pageSizes: [10, 20, 30, 40],
      },
    }
  },
  computed: {
    whatsAppMarketingActivityDetailVO() {
      return this.detailResult && this.detailResult.whatsAppMarketingActivityDetailVO
        ? this.detailResult.whatsAppMarketingActivityDetailVO
        : {}
    },
    countData() {
      if (this.detailResult && this.detailResult.whatsAppMarketingActivityDetailVO) {
        return {
          0: this.detailResult.whatsAppMarketingActivityDetailVO.totalCount || 0,
          1: this.detailResult.whatsAppMarketingActivityDetailVO.sendCount || 0,
          2: this.detailResult.whatsAppMarketingActivityDetailVO.deliveredCount || 0,
          3: this.detailResult.whatsAppMarketingActivityDetailVO.readCount || 0,
          4: this.detailResult.whatsAppMarketingActivityDetailVO.failedCount || 0,
        }
      }
      return {}
    },
  },
  watch: {
    currBtn(val) {
      this.whatsappSendDetail()
    },
    item: {
      deep: true,
      handler(val) {
        this.currentItem = JSON.parse(JSON.stringify(val))
        this.initData()
      },
    },
  },
  mounted() {
    this.currentItem = JSON.parse(JSON.stringify(this.item))
    this.initData()
  },
  created() {},
  destroyed() {},
  methods: {
    formatDateTime: util.formatDateTime,
    handleSearch() {
      this.pageData.pageNum = 1
      this.whatsappSendDetail()
    },
    handlePageChange(data) {
      this.pageData.pageNum = data.pageNum
      this.pageData.pageSize = data.pageSize
      this.whatsappSendDetail()
    },
    handleShowPreview() {
      this.previewDialogVisible = true
    },
    choose(key) {
      this.currBtn = key
      this.pageData.pageNum = 1
    },
    handleSendAgain() {
      this.$router.push({
        name: 'whatsapp-create',
        query: {
          type: 'copy',
          marketingActivityId: this.currentItem.marketingActivityId,
        },
      })
    },
    handleShowDetail(row) {
      setTimeout(() => {
        this.detailId = row.userMarketingId
        if (this.detailId) {
          this.detailVisible = true
        }
      }, 0)
    },
    handleCloseDetail() {
      this.detailVisible = false
    },
    handleClose() {
      this.$emit('close')
    },
    handleClueDetail(row) {
      if (row.saveCrmStatus === 0 || row.saveCrmStatus === 3) {
        if(!row.otherCrmObjectBind) return;
        CRM.api.show_detail({
          apiname: row.otherCrmObjectBind.apiName,
          id: row.otherCrmObjectBind.objectId,
        })
      }
    },
    queryMarketingActivityDetail() {
      this.flag_showLoadingMask = true
      http
        .queryMarketingActivityDetail({
          id: this.currentItem.marketingActivityId,
        })
        .then(res => {
          this.flag_showLoadingMask = false
          if (res && res.errCode === 0 && res.data) {
            this.detailResult = res.data

            const _whatsappFormData = {}

            _whatsappFormData.name = res.data.name
            const result = res.data.whatsAppMarketingActivityDetailVO || {}
            _whatsappFormData.parameterList = result.parameterList
            if (result.sendType === SEND_TYPE['1']) {
              _whatsappFormData.sendType = {
                type: 1,
                time: '',
              }
            } else if (result.sendType === SEND_TYPE['2']) {
              _whatsappFormData.sendType = {
                type: 2,
                time: result.sendTime,
              }
            }
            _whatsappFormData.templateName = result.templateName
            _whatsappFormData.templateLanguage = result.templateLanguage
            _whatsappFormData.templateBody = result.templateBody
            _whatsappFormData.componentList = result.template ? result.template.componentList || [] : []
            this.whatsappFormData = _whatsappFormData
          }
        })
    },
    whatsappSendDetail() {
      this.tableLoading = true
      const params = {
        marketingActivityId: this.currentItem.marketingActivityId,
        pageNum: this.pageData.pageNum,
        pageSize: this.pageData.pageSize,
        phone: this.phoneKeyword || null,
      }

      if (SEND_STATUS2[this.currBtn].value) {
        params.sendStatus = SEND_STATUS2[this.currBtn].value
      }
      http.whatsappSendDetail(params).then(res => {
        this.tableLoading = false
        if (res && res.errCode === 0 && res.data) {
          if (res.data.result) {
            this.tableDatas = res.data.result.map(item => ({
              ...item,
              tUserMarketingName: [
                {
                  id: 'user',
                  name: item.userMarketingName || '--',
                },
              ],
            }))
            this.pageData.totalCount = res.data.totalCount || 0
          }
        }
      })
    },
    initData() {
      this.queryMarketingActivityDetail()
      this.whatsappSendDetail()
    },
  },
}
</script>

<style lang="less" scoped>
.whatsapp-sideslip-detail {
  .header {
    display: flex;
    align-items: center;
    background-color: #f6f9fc;
    justify-content: space-between;
    padding: 16px 55px 16px 25px;
    .button {
      height: 32px;
    }
    .content {
      flex: 1;
      display: flex;
      .main {
        display: flex;
        flex-direction: column;
        font-size: 12px;
        justify-content: center;
        color: #91959e;
      }
      .p1 {
        font-size: 14px;
        color: #181c25;
        width: 500px;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        font-weight: 700;
      }
      .p2 {
        color: #545861;
      }
      .blue {
        color: var(--color-info06);
      }
      .info {
        margin-top: 10px;
        color: #545861;
        .info-item {
          margin-right: 40px;
        }
      }
    }
  }
  .title-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .export-btn {
      height: 32px;
      font-size: 12px;
      color: var(--color-primary06, #407fff);
      margin-right: 20px;
      border: 0;
      padding: 0 5px;
      background: #fff;
    }
  }
  .side-title {
    margin-top: 20px;
    font-size: 12px;
    color: #181c25;
    padding-left: 16px;
    border-left: 4px solid var(--color-primary06, #ff8000);
    margin-bottom: 20px;
  }
  .tips-wrapper {
    margin-left: 20px;
    color: #91959e;
    font-size: 12px;
    margin-bottom: 10px;
    .el-icon-warning {
      color: var(--color-primary06, #ff8000);
      font-size: 15px;
      margin-right: 10px;
    }
    span {
      color: var(--color-primary06, #407fff);
      font-size: 12px;
      cursor: pointer;
    }
  }
  .number-wrapper {
    margin: 0px 18px 0px 25px;
    width: 856px;
    border: 1px solid #e9edf5;
    height: 125px;
    display: flex;
    align-items: center;
    .num {
      font-size: 16px;
      color: #333333;
      .mini {
        display: inline-block;
        font-size: 12px;
        border-left: 1px solid #ccc;
        height: 12px;
        line-height: 12px;
        margin-left: 5px;
        padding-left: 5px;
      }
    }
    .blue {
      color: var(--color-info06);
      cursor: pointer;
    }
    .des {
      font-size: 12px;
      color: #545861;
      .mini {
        font-size: 12px;
        display: inline-block;
        border-left: 1px solid #ccc;
        height: 12px;
        line-height: 12px;
        margin-left: 5px;
        padding-left: 5px;
      }
    }
    .number {
      // width: 500px;
      width: 100%;
      display: flex;
      justify-content: space-around;
      // flex-wrap: wrap;
      .item {
        height: 82px;
        width: 123px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
      }
    }
    .line {
      width: 1px;
      height: 57px;
      background-color: #e9edf5;
    }
    .totalWrapper {
      display: flex;
      justify-content: space-around;
      flex: 1;
    }
    .total {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
    }
  }
  .side-table__wrapper {
    margin: 12px 20px 0;
    .tablewbg {
      width: auto;
      border-top: 1px solid #e9edf5 !important;
      border-left: 1px solid #e9edf5 !important;
      box-sizing: border-box;
      overflow: auto;
    }
    .table__more {
      height: 40px;
      border-top: 1px solid #e9edf5 !important;
      border-left: 1px solid #e9edf5 !important;
      border-top-width: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      a {
        font-size: 12px;
        text-decoration: none;
      }
    }
  }
  .detail-header {
    display: flex;
    height: 82px;
    background-color: #f6f9fc;
    font-size: 14px;
    color: #181c25;
    align-items: center;
    padding: 0 20px;
    .back {
      flex-shrink: 0;
      padding-right: 16px;
      cursor: pointer;
      color: var(--color-primary06, #407fff);
    }
    .arrow-img {
      width: 16px;
      transform: rotate(90deg);
    }
    .line {
      background-color: #e9edf5;
      width: 1px;
      height: 20px;
    }
    .title {
      margin-left: 16px;
      width: 550px;
    }
  }

  .table-header__wrapper {
    display: flex;
    margin: 0 20px;
    justify-content: space-between;
    align-items: center;

    .search-input {
      margin-right: 4px;
    }
  }

  .btn-group-sort {
    button {
      &.curr-btn {
        z-index: 10;
        color: var(--color-primary06, #ff8000);
        border-color: var(--color-primary06, #ff8000);
      }
    }
    .status-btn__wrapper {
      display: flex;
    }
    .question-tooltip {

      /deep/ .ico-question {
        width: 14px;
        height: 14px;
      }
    }
  }
}
</style>
