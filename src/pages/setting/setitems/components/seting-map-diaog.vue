<!-- 组件说明 -->
<template>
     <v-dialog
       :title="$t('marketing.commons.ts_02d981')"
       append-to-body
       width="767px"
       :visible="visible"
       class="setting-map-dialog__wrapper"
       @onClose="handleCloseDialog"
       @onSubmit="handleSubmitDialog"
     >
       <p class="title">
        {{ $t('marketing.pages.setting.ghdlxybqty_c26549') }}
       </p>
       <el-checkbox-group v-model="checkedList">
        <el-checkbox
          v-for="(field) in showMarketingEventFields" 
          :key="field.apiName"
          class="checkbox-wrapper"
          :label="field.apiName"
          :disabled="field.disabled || false"
           >
            {{ field.labelName }}
          </el-checkbox>
       </el-checkbox-group>
     </v-dialog>
</template>

<script>
import { mapState } from 'vuex'
import VDialog from '@/components/dialog'
import http from '@/services/http/index'
import _ from "lodash"

export default {
  components: {
    VDialog,
    ElCheckbox: FxUI.Checkbox,
    ElCheckboxGroup: FxUI.CheckboxGroup
  },
  props: {
    visible: {
      type: Boolean,
      default: () => false,
    },
    marketingEventFields: {
      type: Array,
      default: () => [],
    },
    // crm中活动营销映射的活动类型列表
    mapping: {
      type: Array,
      default: () => [],
    },
    openMergePhone: {
      type: Boolean,
      default: () => false,
    },
    activityType: {
      type: Number,
      default: () => 0,
    },
    // disabled的营销活动类型
    disabledMarketingEventFields: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      checkedList: [],
      showMarketingEventFields: [],
    }
  },
  computed: {
    ...mapState('MarketingEventSet', ['activityTypeMapping', 'openMergePhone', 'marketingActivityAudit', 'marketingEventAudit']),
  },
  mounted() {
    this.init()
  },
  created() {},
  destroyed() {},
  methods: {
    // 关闭弹框
    handleCloseDialog() {
      this.$emit('update:visible', false)
    },
    handleSubmitDialog() {
      const {
        openMergePhone, marketingActivityAudit, marketingEventAudit,
      } = this
      const submitMapping = []
      this.checkedList.forEach(item => {
        this.showMarketingEventFields.forEach(i => {
          if (i.apiName == item) {
            submitMapping.push({
              apiName: item,
              fieldName: i.labelName,
            })
          }
        })
      })
      http
        .updateMarketingEventCommonSetting({
          activityTypeMapping: [
            {
              activityType: this.activityType,
              mapping: submitMapping,
            },
          ],
          openMergePhone,
          marketingActivityAudit,
          marketingEventAudit,
        })
        .then(res => {
          if (res && res.errCode == 0) {
            FxUI.Message.success($t('marketing.commons.szcg_f6088e'))
          }
          this.$emit('submit')
        })
    },
    init() {
      const _showMarketingEventFields = []
      const _selectList = [];
      const clonedFields = _.cloneDeep(this.marketingEventFields)
      clonedFields.forEach(item => {
        // 找出已经被选中的活动类型
        this.mapping.forEach(i => {
          if (i.apiName == item.apiName) {
            _selectList.push(i.apiName)
          }
        })
        // 已被别的活动类型占用 不允许勾选
        if (this.disabledMarketingEventFields.includes(item.apiName)) {
          item.disabled = true
        }
        // 排除广告营销、直播营销、会议营销、目标人群运营
        if (
          item.apiName !== 'advertising_marketing'
          && item.apiName !== 'once'
          && item.apiName !== 'periodicity'
        ) {
          _showMarketingEventFields.push(item)
        }
      })
      this.showMarketingEventFields = _showMarketingEventFields
      this.checkedList = _selectList
    },
  }, // 生命周期 - 销毁完成
}
</script>

<style lang="less" scoped>
.setting-map-dialog__wrapper {
  font-size: 14px;
  color: #181c25;
  .title {
    margin-bottom: 22px;
    background: #EEF0F3;
    padding: 14px 16px;
    color: #181c25;
  }
  .radio-group {
    display: flex;
    flex-direction: column;
  }
  .radio-item {
    margin-bottom: 20px;
  }
  .checkbox-wrapper {
    width: 150px;
    margin-bottom: 20px;
  }
}
</style>
