<template>
  <div class="input-item">
    <fx-input
      type="text"
      v-model="localInfo.mankeepFieldName"
      :disabled="true"
    ></fx-input>
    <div class="icon-arrow"></div>
    <el-select 
      v-model="localInfo.crmFieldApiName" 
      @change="onSelectChange"
      :placeholder="$t('marketing.commons.qxz_708c9d')"
      :disabled="typeField.length === 0"
    >
      <el-option
        v-for="(field, index) in typeField"
        :key="index"
        :name="field.fieldCaption"
        :label="field.fieldCaption"
        :value="field.fieldName"
      ></el-option>
    </el-select>
  </div>
</template>
<script>
import http from "@/services/http";

export default {
  components: {
    ElInput: FxUI.Input,
    ElSelect: FxUI.Select.components.ElSelect,
    ElOption: FxUI.Select.components.ElSelect.components.ElOption
  },
  props: {
    info: {
      type: Object,
      default: () => ({})
    },
    fields: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      localInfo: { ...this.info }
    };
  },
  computed: {
    typeField() {
      return this.fields.filter(
        field => field.fieldTypeName === this.localInfo.type
      );
    },
    typeFiledMap() {
      return this.typeField.reduce((pre, cur) => {
        pre[cur.fieldName] = cur.fieldCaption;
        return pre;
      }, {});
    }
  },
  watch: {
    info: {
      handler(newVal) {
        this.localInfo = { ...newVal };
      },
      deep: true
    },
    fields: {
      handler() {
        if (this.typeField.length === 0) {
          this.localInfo.crmFieldApiName = "";
          this.localInfo.crmFieldName = "";
          return;
        }
        const valid = this.typeField.some(f => f.fieldName === this.localInfo.crmFieldApiName);
        if (!valid) {
          this.localInfo.crmFieldApiName = "";
          this.localInfo.crmFieldName = "";
        } else {
          this.setCrm(this.localInfo.crmFieldApiName);
        }
      },
      immediate: true
    },
    "localInfo.crmFieldApiName": function(val) {
      this.setCrm(val);
    }
  },
  methods: {
    setCrm(crmApiName) {
      let crmFieldName = this.typeFiledMap[crmApiName];
      if (crmFieldName) {
        this.localInfo["crmFieldName"] = crmFieldName;
      } else {
        this.localInfo["crmFieldApiName"] = "";
        this.localInfo["crmFieldName"] = "";
      }
    },
    onSelectChange() {
      this.$emit('update', { ...this.localInfo });
    }
  },
};
</script>
<style lang="less" scoped>
.input-item {
  display: flex;
  justify-content: space-around;
  align-items: center;
  width: 600px;
  margin-bottom: 16px;
  ::v-deep .el-input {
    width: 220px;
  }
  ::v-deep .fx-select {
    width: 220px;
  }
}
.icon-arrow {
  display: inline-block;
  width: 50px;
  height: 12px;
  background: url("~@/assets/images/crmsetting-arrow.png") no-repeat center;
  background-size: cover;
  margin-right: 6px;
}
</style>
