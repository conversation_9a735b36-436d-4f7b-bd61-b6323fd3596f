<template>
  <div class="marketing-step-setting">
    <div class="step-setting__card step-setting__card-row">
      <div class="step-setting__header">
        <div class="step-setting__header-left">
          <img
            :src="icon"
            alt="icon"
            class="step-setting__icon"
          >
          <div class="step-setting__header-left-content">
            <div class="step-setting__title">
              {{ title }}
            </div>
            <div class="step-setting__content">
              {{ description }}
            </div>
          </div>
        </div>
        <fx-link
          type="text"
          class="step-setting__link"
          :href="href"
          target="_blank"
        >
          {{ $t('marketing.commons.bzsc_a3fcec') }}
        </fx-link>
      </div>
    </div>
    <div class="step-setting__progress-container">
      <div class="step-setting__progress-title">
        {{ $t('marketing.pages.setting.pzjd_802f7e') }}
      </div>
      <div class="step-setting__progress-wrapper">
        <fx-progress
          class="step-setting__progress"
          :stroke-width="12"
          :percentage="progress"
          color="#30c776"
          :show-text="false"
        />
        <div class="step-setting__progress-text">
          {{ `${selectedSteps.length}/${steps.length}` }}
        </div>
      </div>
      <div
        v-for="item, index in steps"
        :key="item.id"
        class="step-setting__card step-setting__card-col border-1px"
      >
        <div class="step-setting__left">
          <div class="step-setting__title">
            {{ item.title }}
          </div>
          <div class="step-setting__content">
            <span>{{ item.description }}</span>
            <fx-button
              v-if="item.showSetting"
              type="text"
              style="padding: 0;"
              @click="handleSetting(item)"
            >
              {{ $t('marketing.commons.qpz_a532be') }}
            </fx-button>
          </div>
          <slot :name="item.slot" />
        </div>
        <fx-button
          size="small"
          plain
          @click="handleItemSelected(item, index)"
        >
          <i
            v-if="item.selected"
            class="el-icon-check step-setting__completed-icon"
          />{{ item.selected ? $t('marketing.commons.ywc_fad522') : $t('marketing.pages.setting.bjwwc_792180') }}</fx-button>
      </div>
    </div>
  </div>
</template>

<script>
import { redirectToFS } from '@/utils/index.js'
import successSelected from '@/assets/images/setting/success-selected.png'
import successUnselected from '@/assets/images/setting/success-unselected.png'

export default {
  props: {
    title: {
      type: String,
      default: '',
    },
    description: {
      type: String,
      default: '',
    },
    steps: {
      type: Array,
      default: () => ([]),
    },
    handleItemSelected: {
      type: Function,
      default: () => {},
    },
    icon: {
      type: String,
      default: '',
    },
    href: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      successSelected,
      successUnselected,
    }
  },
  computed: {
    selectedSteps() {
      return this.steps.filter(item => item.selected)
    },
    progress() {
      return (this.selectedSteps.length / this.steps.length) * 100
    },
  },
  watch: {},
  mounted() {},
  methods: {
    handleSetting(item) {
      if (item.showSetting && typeof item.handleSetting === 'function') {
        item.handleSetting()
      } else if (item.objApiName) {
        redirectToFS(`#paasapp/list/=/appId_CRM/${item.objApiName}`)
      } else if (item.showSetting) {
        this.$router.push(item.routeTo)
      }
    },
  },
}
</script>

<style lang="less" scoped>
.marketing-step-setting {
  .step-setting__card-col {
    display: flex;
    justify-content: space-between;
    align-items: center;
    }

  .step-setting__card-row {
    display: flex;
    flex-direction: column;
    border-bottom: 1px solid #e5e5e5;
  }

  .step-setting__main {
    padding: 0 12px;
  }

  .border-1px {
    border: 1px solid #e5e5e5;
  }

  .step-setting__completed-icon {
    color: #30C776;
    font-size: 14px;
  }

  .step-setting__card {
    margin-top: 12px;
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;

    .step-setting__header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .step-setting__header-left {
      display: flex;

      .step-setting__header-left-content {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }

      img {
        width: 48px;
        height: 48px;
        margin-right: 8px;
      }
    }

    .step-setting__title {
      font-size: 16px;
      font-weight: 700;
      line-height: 24px;
    }

    .step-setting__content {
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      color: var(--color-neutrals15);
    }

    .step-setting__left {
      display: flex;
      flex-direction: column;
    }
  }

  .step-setting__progress-container {
    border-radius: 8px;
    padding: 20px;
    background-color: #fff;
    margin-top: 12px;

    .step-setting__progress-title {
      font-size: 15px;
      font-weight: 700;
      line-height: 24px;
    }

    .step-setting__progress-wrapper {
      margin: 12px 0 30px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 14px;
    }

    .step-setting__progress {
      flex: 1;
      margin-right: 20px;
    }
  }
}
</style>
