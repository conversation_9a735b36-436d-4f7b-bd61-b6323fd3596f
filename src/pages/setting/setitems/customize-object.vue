<template>
  <div class="customize">
    <div class="setitem">
      <div class="setitle">
        {{ $t('marketing.pages.setting.yxyhsz_ba9dd1') }}
      </div>
      <div class="set-sub-item" style="margin-top: 0px;">
        <div class="sub-item-title">
          <span>{{ $t('marketing.pages.setting.zdyyxyhdx_befec0') }}</span>
          <QuestionTooltip :offset="192" class="question_tooltip">
            <div slot="question-content">
              <div class="question_content" style="line-height: 18px;">
                {{ $t('marketing.pages.setting.zdyyxdxyxt_196036') }}
              </div>
            </div>
          </QuestionTooltip>
        </div>
        <div class="object-list">
          <div
            v-for="(item, index) in marketingUserObjectLists"
            :key="item.id"
            class="object-card"
          >
            <div class="object-card__header">
              <div class="object-card__title">
                {{ item.objectName }}
              </div>
              <div>
                <Button
                  type="text"
                  @click="handleCommand({ type: 'edit', index })"
                >
                  {{ $t("marketing.commons.bj_95b351") }}
                </Button>
                <Button
                  type="text"
                  @click="handleCommand({ type: 'del', index })"
                >
                  {{ $t("marketing.commons.sc_2f4aad") }}
                </Button>
              </div>
            </div>
            <div class="object-card__con">
              <div class="object-card__fieldmap-title">
                {{ $t("marketing.commons.zdys_e5b2ad") }}
              </div>
              <div class="object-card__fieldmap">
                <div
                  v-for="iitem in item.customizeObjectMappings"
                  :key="iitem.crmFieldName"
                  class="object-card__fieldmap-item"
                >
                  <div class="object-card__fieldname">
                    {{ iitem.mankeepFieldName || "--" }}
                  </div>
                  <div class="object-card__arrow" />
                  <div class="object-card__target-fieldname">
                    {{ iitem.crmFieldName || "--" }}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="object-card" @click="addCustomize">
            <div class="object-add-btn">
              <div>
                <em>+</em>
                <p>{{ $t("marketing.commons.xzzdyyxdx_636dbd") }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="set-sub-item">
        <div class="sub-item-title">
          <span>{{ $t('marketing.pages.setting.yxyhglkhdx_f7e707') }}</span>
          <QuestionTooltip :offset="192" class="question_tooltip">
            <div slot="question-content">
              <div class="question_content" style="line-height: 18px;padding: 10px;">
                {{ $t('marketing.pages.setting.yxyhzwygzr_45a2d2') }}
                <a
                  href="https://help.fxiaoke.com/93d5/c8de/87ee/d985"
                  target="_blank"
                >{{ $t('marketing.pages.setting.sfhbgz_ab8912') }}</a>
                {{ $t('marketing.pages.setting.zddtgyxdxx_912a9e') }}
              </div>
            </div>
          </QuestionTooltip>
          <fx-switch
            v-model="marketingUserStatus"
            size="small"
            style="margin-left: 25px;"
            :active-value="0"
            :inactive-value="1"
            @change="handleMarketingUserExcludeApinameChange"
          >
          </fx-switch>
        </div>
      </div>
      <div class="set-sub-item">
        <div class="sub-item-title">
          <span>{{ $t('marketing.pages.setting.yxyhsfglsl_402469') }}</span>
          <QuestionTooltip :offset="192" class="question_tooltip">
            <div slot="question-content">
              <div class="question_content" style="line-height: 18px;padding: 10px;">
                {{ $t('marketing.pages.setting.kqckgh_019278') }}<a href="https://help.fxiaoke.com/93d5/c8de/87ee/d985" target="_blank" >{{ $t('marketing.pages.setting.dqdyhsfhb_6d7024') }}</a>{{ $t('marketing.pages.setting.shjlssygls_40b9b4') }}
              </div>
            </div>
          </QuestionTooltip>
          <fx-switch
            v-model="syncObjectTagStatus"
            size="small"
            style="margin-left: 25px;"
            :active-value="1"
            :inactive-value="0"
            @change="handleMarketingUserAssociatedTagChange"
          >
          </fx-switch>
        </div>
      </div>
    </div>

    <VDialog
      :title="$t('marketing.commons.xzzdyyxdx_636dbd')"
      :visible.sync="customerVisible"
      @onSubmit="doSubmit"
      @onClose="doCancel"
    >
      <div class="tip">
        {{ $t("marketing.pages.setting.qxzxyxzdzd_3b32bb") }}
      </div>
      <div>
        <el-select
          v-model="selectObj"
          class="select"
          filterable
          :placeholder="$t('marketing.commons.qxz_708c9d')"
        >
          <el-option
            v-for="(o, index) in customerObj"
            :key="index"
            :name="o.display_name"
            :label="o.display_name"
            :value="o.api_name"
          />
        </el-select>
      </div>
      <div class="tip">
        {{ $t("marketing.pages.setting.pzzdyssjwb_1e8dae") }}
      </div>
      <input-item
        v-for="(mapping, idx) in curObjectMapping"
        :key="mapping.crmFieldName + idx"
        :info="mapping"
        :fields="crmFileds"
        @update="val => curObjectMapping.splice(idx, 1, val)"
      />
    </VDialog>
  </div>
</template>
<script>
import http from "@/services/http";
import VDialog from "@/components/dialog";
import InputItem from "./components/input-item.vue";
import preCrmCustomer from "./const/pre-crm-customer";
import QuestionTooltip from "@/components/questionTooltip/index";
import { alert, confirm } from "@/utils/globals";

const preCrmCustomerTypeMap = preCrmCustomer.reduce((pre, cur) => {
  pre[cur.apiName] = cur.type;
  return pre;
}, {});

export default {
  components: {
    Dropdown: FxUI.Dropdown,
    DropdownMenu: FxUI.DropdownMenu,
    DropdownItem: FxUI.DropdownItem,
    VDialog,
    ElSelect: FxUI.Select.components.ElSelect,
    ElOption: FxUI.Select.components.ElSelect.components.ElOption,
    InputItem,
    Button: FxUI.Button,
    QuestionTooltip
  },
  data() {
    return {
      marketingUserObjectLists: [],
      customerObj: [],
      selectObj: "",
      customerObjMap: {},
      customerVisible: false,
      customizeObjectMappings: [],
      crmFileds: [],
      crmFiledMapName: {},
      curObjectMapping: [],
      // 默认营销用户都是关联客户对象的
      marketingUserStatus: 0,
      //营销用户关联历史标签同步到关联营销用户对象开关状态
      syncObjectTagStatus: false
    };
  },
  watch: {
    async selectObj() {
      await this.getCrmObjectFields();
      this.curObjectMapping = this.getCurObjectMappingBySelectObj();
    }
  },
  created() {
    this.getMarketingUserGroupCustomizeObjectMapping();
    this.queryField();
    this.getMappingField();
    this.getMarketingUserExcludeApiname()
    this.queryMarketingUserSyncObjectTagsStatus();
  },
  methods: {
    getMarketingUserGroupCustomizeObjectMapping() {
      http
        .getMarketingUserGroupCustomizeObjectMapping()
        .then(({ errCode, data }) => {
          if (errCode === 0) {
            this.marketingUserObjectLists = data || [];
          }
        });
    },
    async queryField() {
      const res = await http.findDescribField();
      if (res && res.errCode === 0) {
        this.customerObj = res.data.resultList;
        this.customerObjMap = res.data.resultList.reduce((pre, cur) => {
          pre[cur.api_name] = cur.display_name;
          return pre;
        }, {});
      }
    },
    async doSubmit() {
      if (!this.selectObj) {
        alert($t("marketing.pages.setting.wxzzdydx_ca1463"));
        return;
      }
      const customizeObjectMappings = this.curObjectMapping.map(map => ({
        crmFieldApiName: map.crmFieldApiName,
        crmFieldName: this.crmFiledMapName[map.crmFieldApiName],
        mankeepFieldApiName: map.mankeepFieldApiName,
        mankeepFieldName: map.mankeepFieldName
      }));
      const params = {
        objectApiName: this.selectObj,
        objectName: this.customerObjMap[this.selectObj],
        customizeObjectMappings
      };
      const res = await http.setMarketingUserGroupCustomizeObjectMapping(
        params
      );
      if (res && res.errCode === 0) {
        FxUI.Message.success($t("marketing.commons.bccg_fbd249"));
        this.customerVisible = false;
        this.getMarketingUserGroupCustomizeObjectMapping();
      }
    },
    doCancel() {
      this.customerVisible = false;
    },
    async getMappingField() {
      const res = await http.getCustomerServiceClueMapping();
      if (res && res.errCode === 0) {
        const map = (res.data.crmFormFieldMap || []).reduce((pre, cur) => {
          pre[cur.mankeepFieldName] = cur.crmFieldName;
          return pre;
        }, {});
        this.customizeObjectMappings = preCrmCustomer.map(c => ({
          crmFieldApiName: map[c.apiName],
          mankeepFieldApiName: c.apiName,
          mankeepFieldName: c.label,
          type: c.type
        }));
      }
    },
    async getCrmObjectFields() {
      if (!this.selectObj) return;
      const res = await http.getCrmObjectFields({
        objectApiName: this.selectObj,
        recordType: "default__c"
      });
      if (res && res.errCode === 0) {
        const map = res.data.reduce((pre, cur) => {
          pre[cur.fieldName] = cur.fieldCaption;
          return pre;
        }, {});
        this.crmFiledMapName = map;
        this.crmFileds = res.data;
      }
    },
    async addCustomize() {
      this.selectObj = "";
      this.crmFiledMapName = {};
      this.crmFileds = [];
      await this.getCrmObjectFields();
      this.curObjectMapping = this.getCurObjectMappingBySelectObj();
      this.customerVisible = true;
    },
    handleCommand({ type, index }) {
      if (type === "edit") {
        this.doEdit(index);
      } else if (type === "del") {
        this.doDel(index);
      }
    },
    async doEdit(index) {
      this.selectObj = this.marketingUserObjectLists[index].objectApiName;
      await this.getCrmObjectFields();
      this.curObjectMapping = this.getCurObjectMappingBySelectObj();
      this.customerVisible = true;
    },
    async doDel(index) {
      const isVal = await confirm(
        $t("marketing.pages.setting.sccpzhxjmb_f239f9")
      );
      if (!isVal) return;
      const res = await http.deleteMarketingUserGroupCustomizeObjectMapping({
        id: this.marketingUserObjectLists[index].id
      });
      if (res && res.errCode === 0) {
        FxUI.Message.success($t("marketing.commons.sccg_43593d"));
        this.getMarketingUserGroupCustomizeObjectMapping();
      }
    },
    async handleMarketingUserExcludeApinameChange(value) {
      const res = await http.setMarketingUserExcludeApiname({
        status: value,
        objectApiName: 'AccountObj',
        objectName: $t('marketing.commons.kh_ff0b20'),
      })
      if(res && res.errCode == 0){
        FxUI.Message.success($t('marketing.commons.szcg_f6088e'))
      } else {
        FxUI.Message.error(res.errMsg || $t('marketing.commons.szsb_9f9603'))
      }
    },
    async getMarketingUserExcludeApiname(){
      // 获取营销用户是否开启关联对象列表（data返回的是排除列表，默认都是关联的）
      const res = await http.getMarketingUserExcludeApiname()
      if(res && res.errCode === 0){
        (res.data || []).forEach(item=>{
          if(item && item.objectApiName === 'AccountObj'){
            this.marketingUserStatus = 1
            return
          }
        })
      }
    },
    //营销用户关联对象标签开关
    async handleMarketingUserAssociatedTagChange(value){
      const res = await http.setMarketingUserSyncObjectTagsStatus({
        syncObjectTagStatus: value
      })
      if(res && res.errCode == 0){
        FxUI.Message.success($t('marketing.commons.szcg_f6088e'))
      } else {
        FxUI.Message.error(res.errMsg || $t('marketing.commons.szsb_9f9603'))
      }
    },
    async queryMarketingUserSyncObjectTagsStatus(){
      const res = await http.queryMarketingUserSyncObjectTagsStatus()
      if(res && res.errCode === 0){
        this.syncObjectTagStatus = res.data.syncObjectTagStatus
      }
    },
    getCurObjectMappingBySelectObj() {
      const found = this.marketingUserObjectLists.find(
        item => item.objectApiName === this.selectObj
      );
      if (found) {
        return JSON.parse(JSON.stringify(
          (found.customizeObjectMappings || []).map(o => ({
            ...o,
            type: preCrmCustomerTypeMap[o.mankeepFieldApiName]
          }))
        ));
      }
      // fallback: use template
      return JSON.parse(JSON.stringify(this.customizeObjectMappings));
    }
  }
};
</script>
<style lang="less" scoped>
.customize {
  .setitem {
    position: relative;
    padding: 20px 0;
    margin: 0 20px 0 20px;
    border-bottom: 1px dashed #e9edf5;
  }
  .setitle {
    font-size: 16px;
    margin-bottom: 8px;
    color: #181c25;
    display: flex;
    align-items: center;
    font-weight: bold;
  }
  .setdesc {
    color: #91959e;
    padding-right: 120px;
    font-size: 13px;
  }
  .setstat {
    position: absolute;
    top: 50px;
    right: 8px;
  }

  .set-sub-item {
    margin-top: 20px;
    .sub-item-title {
      display: flex;
      span{
        font-size: 14px;
      }
      .question_tooltip {
        margin-top: 2px;
        margin-left: 10px;
      }
    }
  }

  // .object-list{
  //   margin-top: 10px;
  // }
  .object-card {
    display: inline-block;
    width: 320px;
    height: 162px;
    background-color: #fff;
    margin-right: 16px;
    margin-top: 10px;
    border-radius: 3px;
    border: 1px solid @border-color-base;
    vertical-align: top;
    .object-add-btn {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      > div {
        text-align: center;
      }
      em {
        font-style: normal;
        font-size: 25px;
        color: @color-link;
      }
      p {
        color: @color-subtitle;
        font-size: 12px;
      }
    }
    &:hover {
      box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.05);
    }
    &__header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 38px;
      padding: 0 14px;
      border-bottom: 1px solid @border-color-base;
      background: #f2f3f5;
    }
    &__title {
      font-size: 14px;
      color: @color-title;
    }
    &__opts {
      color: #b4b6c0;
      font-size: 16px;
      cursor: pointer;
    }
    &__con {
      padding: 15px 14px;
    }

    &__fieldmap-title {
      font-size: 12px;
      color: @color-subtitle;
    }

    // &__fieldmap{
    //   margin-top: 10px;
    // }
    &__fieldmap-item {
      display: flex;
      align-items: center;
      margin-top: 6px;
    }

    &__fieldname,
    &__target-fieldname {
      font-size: 12px;
      color: @color-title;
    }
    &__arrow {
      width: 36px;
      height: 12px;
      background-image: url(../../../assets/images/crmsetting-arrow.png);
      background-repeat: no-repeat;
      background-size: contain;
      margin-right: 30px;
      margin-left: 30px;
    }
  }
}
.tip {
  color: #181c25;
  font-size: 14px;
  margin-bottom: 13px;
}
.select {
  width: 100%;
  margin-bottom: 13px;
}
</style>
