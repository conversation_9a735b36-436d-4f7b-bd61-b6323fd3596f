<!-- 组件说明 -->
<template>
  <div class="mail-user-wrapper">
    <div
      v-for="(item, index) in List"
      :key="index"
      class="item-wrapper list-wrapper"
    >
      <div class="info">
        <div class="info-item">
          <!-- <div v-if="item.defaultValue" class="default"></div>
          <div v-if="item.defaultValue" class="default-text">默认</div> -->
          <div v-if="item.defaultValue" class="default">
            <img
              :src="userMailImg"
              alt=""
            />
          </div>
          <img
            class="img"
            :src="require('@/assets/images/mail/user-send.png')"
          />
          <div class="name">{{ item.name }}</div>
        </div>
        <div class="line" style="height:10px;"></div>
        <div class="info-item">
          <img
            class="img"
            :src="require('@/assets/images/mail/user-mail.png')"
          />
          <div class="name">{{ item.address }}</div>
        </div>
      </div>
      <fx-popover
        placement="bottom-start"
        v-model="item.popVisible"
        popper-class="mail-setting-popover-wrapper"
      >
        <div class="popover-operate">
          <div class="operate-list" @click.stop="updateMailUser(item)">
            {{ $t('marketing.commons.bj_95b351') }}
          </div>
          <div class="operate-list" @click.stop="deleteMailUser(item)">
            {{ $t('marketing.commons.sc_2f4aad') }}
          </div>
          <div class="operate-list" @click.stop="setDefaultUser(item)">
            {{ $t('marketing.pages.setting.swmr_1af3ec') }}
          </div>
        </div>
        <div class="image-wrapper" slot="reference">
          <img
            :src="require('@/assets/images/icons/ellipsis.png')"
            class="image"
          />
        </div>
      </fx-popover>
    </div>
    <div class="item-wrapper add-wrapper" @click="addUser">
      <p class="icon-add">+</p>
      <p>{{ $t('marketing.commons.tj_5617ed', {data: ({'option0': userName})}) }}</p>
    </div>
    <el-dialog
      :title="formText.title"
      width="480px"
      :visible="mailSetDialogVisible"
      v-if="mailSetDialogVisible"
      @close="mailSetDailog"
      append-to-body
      class="update-setting-dialog"
    >
      <el-form
        :model="formInfo"
        ref="mailUserForm"
        :rules="rules"
        class="form-wrapper"
        label-width="90px"
        label-position="left"
      >
        <el-form-item :label="formText.lable1" prop="data1">
          <fx-input v-model="formInfo.data1" :placeholder="$t('marketing.commons.qsrmc_06e2f8')" />
        </el-form-item>
        <el-form-item :label="formText.lable2" prop="data2">
          <fx-input
            v-model="formInfo.data2"
            :class="{ 'email-doamin-input': type == '0' }"
            :placeholder="$t('marketing.commons.qsrdz_6107b3')"
          />
          <div class="domail-wrapper " v-if="type == '0'">@{{ domain }}</div>
        </el-form-item>
      </el-form>
      <div class="create-reply" v-if="showCreateReply">
        <el-checkbox v-model="replyChecked">{{ $t('marketing.pages.setting.sftscjwhfr_fec98e') }}</el-checkbox>
      </div>
      <div slot="footer" class="dialog-footer">
        <fx-button size="small" type="primary" @click="saveForm">{{ $t('marketing.commons.bc_be5fbb') }}</fx-button>
        <fx-button size="small" @click="cancleForm">{{ $t('marketing.commons.qx_625fb2') }}</fx-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { confirm } from "@/utils/globals";
import http from "@/services/http/index";

import userMailImg from '@/assets/images/mail/user-mail-default1.png'
import userMailImgEn from '@/assets/images/mail/user-mail-default1-en.png'

import { getImageByLang } from '@/utils/i18n.js'

import _ from "lodash";
export default {
  components: {
ElPopover: FxUI.Popover,
ElInput: FxUI.Input,
ElForm: FxUI.Form,
ElFormItem: FxUI.FormItem,
ElDialog: FxUI.Dialog,
ElCheckBox: FxUI.Checkbox
},
  data() {
    let nameValidator = (rule, value, callback) => {
      if (_.trim(value) === "") {
        callback(new Error($t('marketing.commons.qsrmc_06e2f8')));
      } else {
        callback();
      }
    };
    let addressValidator = (rule, value, callback) => {
      let _value = _.trim(value)
      if (_value === "") {
        callback(new Error($t('marketing.commons.qsrdz_6107b3')));
      } else {
        if (Number(this.type) == 1) {
          if (
            /^(?=.{6,128}$)([A-Za-z0-9](?:[A-Za-z0-9._%+-]*[A-Za-z0-9])?)@([A-Za-z0-9](?:[A-Za-z0-9-]{0,61}[A-Za-z0-9])?(?:\.[A-Za-z0-9](?:[A-Za-z0-9-]{0,61}[A-Za-z0-9])?)*\.[A-Za-z]{2,10})$/.test(_value)
          ) {
            callback();
          } else {
            callback(new Error($t('marketing.commons.qsrzqdyx_97e3ac')));
          }
        } else {
          if (/^[A-Za-z0-9](?:[A-Za-z0-9._%+-]*[A-Za-z0-9])?$/.test(_value)) {
            callback();
          } else {
            callback(new Error($t('marketing.commons.qsrzqdyx_97e3ac')));
          }
        }
      }
    };
    return {
      userMailImg: getImageByLang([userMailImg, userMailImgEn]),
      rules: {
        data1: [{ required: true, validator: nameValidator, trigger: "blur" }],
        data2: [
          { required: true, validator: addressValidator, trigger: "blur" }
        ]
      },
      formInfo: { data1: "", data2: "" },
      mailSetDialogVisible: false,
      List: [],
      userName: $t('marketing.commons.fjr_88c952'),
      formText: {
        title: $t('marketing.pages.setting.tjfjr_4c629d'),
        lable1: $t('marketing.pages.setting.fjrmc_b85b21'),
        lable2: $t('marketing.pages.setting.fjrdz_e18051')
      },
      actionType: 0, //1 添加  2 修改,
      updateItem: {}, //当前编辑的item
      showCreateReply: false,
      replyChecked: false
    };
  },
  props: {
    domain: {
      type: String,
      default: () => ""
    },
    type: {
      type: String,
      default: () => "0" //发件人：0，回复人：1
    }
  },
  watch: {},
  computed: {},
  methods: {
    updateInfo() {
      let _address =_.trim(this.formInfo.data2);
      if (Number(this.type) == 0) {
        //带上发件人域名
        _address = this.formInfo.data2 + "@" + this.domain;
      }
      if (this.actionType == 1) {
        http
          .addSendReplyData({
            type: Number(this.type),
            name: this.formInfo.data1,
            address: _address
          })
          .then(res => {
            if (res && res.errCode == 0) {
              this.mailSetDialogVisible = false;
              this.init();
              if (this.replyChecked && Number(this.type) == 0) {
                this.replyChecked = false;
                http.addSendReplyData({
                  type: 1,
                  name: this.formInfo.data1,
                  address: _address
                });
              }
            }
          });
      } else if (this.actionType == 2) {
        http
          .updateSendReplyData({
            id: this.updateItem.id,
            name: this.formInfo.data1,
            address: _address
          })
          .then(res => {
            if (res && res.errCode == 0) {
              this.mailSetDialogVisible = false;
              this.init();
            }
          });
      }
    },
    saveForm() {
      this.$refs["mailUserForm"].validate(valid => {
        if (valid) {
          this.updateInfo();
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    addUser() {
      if (Number(this.type) == 0) {
        //发件人
        this.showCreateReply = true;
      }
      this.actionType = 1;
      this.formText.title = $t('marketing.commons.tj_5617ed', {data: ( {'option0': this.userName})});
      this.mailSetDialogVisible = true;
      this.formInfo.data1 = "";
      this.formInfo.data2 = "";
    },
    cancleForm() {
      this.mailSetDialogVisible = false;
    },
    updateMailUser(item) {
      this.formText.title = $t('marketing.commons.bj_5f4197', {data: ( {'option0': this.userName})});
      let _address = item.address;
      if (Number(this.type) == 0) {
        let index = item.address.indexOf("@" + this.domain);
        _address = _address.substring(0, index);
      }
      item.popVisible = false;
      this.mailSetDialogVisible = true;
      this.formInfo.data1 = item.name;
      this.formInfo.data2 = _address;
      this.actionType = 2;
      this.updateItem = item;
    },
    mailSetDailog() {
      this.mailSetDialogVisible = false;
    },
    setDefaultUser(item) {
      item.popVisible = false;
      http
        .updateSendReplyData({
          id: item.id,
          defaultValue: true
        })
        .then(res => {
          this.init();
        });
    },

    async deleteMailUser(item) {
      item.popVisible = false;
      await confirm($t('marketing.commons.qrscg_0bfe6c', {data: ( {'option0': this.userName})}), $t('marketing.commons.ts_02d981'), {
        confirmButtonText: $t('marketing.commons.qrsc_631cd2'),
        cancelButtonText: $t('marketing.commons.qx_625fb2'),
        type: "warning"
      });
      const { errCode, data } = await http.updateSendReplyData({
        id: item.id,
        status: 99
      });
      if (errCode === 0) {
        this.init();
      }
    },
    init() {
      http
        .querySendReplyData({
          pageNum: 1,
          pageSize: 999,
          type: Number(this.type)
        })
        .then(res => {
          if (res && res.errCode == 0) {
            let data = res && res.data && res.data.result;
            this.List = data;
          }
        });
    }
  },
  mounted() {
    this.init();
  },
  created() {
    if (Number(this.type) == 0) {
      this.userName = $t('marketing.commons.fjr_88c952');
    } else if (Number(this.type) == 1) {
      this.userName = $t('marketing.commons.hfr_730cc4');
      this.formText = {
        title: $t('marketing.pages.setting.tjhfr_148179'),
        lable1: $t('marketing.pages.setting.hfrmc_2eacb0'),
        lable2: $t('marketing.pages.setting.hfrdz_ea0164')
      };
    }
  },
  destroyed() {} //生命周期 - 销毁完成
};
</script>

<style lang="less">
.mail-setting-popover-wrapper {
  min-width: 0px !important;
  width: 91px;
  padding: 8px 0px;
  border-radius: 2px !important;
  box-shadow: -1px 0px 7px 0px rgba(0, 0, 0, 0.16);
  margin-top: -8px !important;
  margin-left: -37px;
  .popover-operate {
    .operate-list {
      height: 30px;
      line-height: 30px;
      width: 100%;
      text-align: center;
      color: #181c25;
      font-size: 12px;
      &:hover {
        cursor: pointer;
        background-color: #f0f4fc;
      }
    }
  }
}
.mail-user-wrapper {
  display: flex;
  flex-wrap: wrap;
  .item-wrapper {
    margin-left: 14px;
    margin-top: 20px;
    width: 330px;
    height: 90px;
    border-radius: 3px;
    border: 1px solid rgba(233, 237, 245, 1);
  }
  .add-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .icon-add {
      color: var(--color-primary06,#407FFF);
      font-weight: bold;
      font-size: 20px;
    }
  }
  .list-wrapper {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    overflow: hidden;
    .default {
      position: absolute;
      left: 0;
      top: 0;
      img {
        width: 28px;
      }
    }
    .info {
      display: flex;
      flex-direction: column;
      .info-item {
        display: flex;
        align-items: center;
        .line {
          width: 100%;
          height: 7px;
        }
        .img {
          width: 15px;
          height: 15px;
          margin: 0 12px 0px 20px;
        }
        .name {
          font-size: 14px;
          color: #545861;
          width: 250px;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
    .image-wrapper {
      width: 16px;
      height: 16px;
      transform: rotate(90deg);
      position: absolute;
      right: 10px;
      top: 34px;
      .image {
        width: 16px;
        height: 16px;
      }
    }
  }
}

.update-setting-dialog {
  .el-form-item__content {
    display: flex;
    .email-doamin-input {
      flex: 1;
    }
    .domail-wrapper {
      height: 32px;
      line-height: 32px;
    }
    .el-form-item__error {
      margin-left: 10px;
      line-height: 32px;
    }
  }

  .el-dialog__body {
    padding: 30px 26px 30px 36px;
  }
}
</style>
