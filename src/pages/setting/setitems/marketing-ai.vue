<template>
  <div :class="$style.setting__marketingAi">
    <div
      slot="header"
      :class="$style.header"
    >
      <content-header
        :title="crumbs"
        :border="true"
      />
    </div>
    <div :class="$style.setting__container">
      <step-setting
        :icon="aiIcon"
        :title="$t('marketing.pages.setting.cjpzjz_fbd9e3')"
        :description="$t('marketing.pages.setting.wcbzhxsddg_41077d')"
        :steps="steps"
        :handle-item-selected="handleItemSelected"
      />
    </div>
  </div>
</template>

<script>

import ContentHeader from '@/components/content-header/index.vue'
import successSelected from '@/assets/images/setting/success-selected.png'
import successUnselected from '@/assets/images/setting/success-unselected.png'
import StepSetting from './components/step-setting.vue'

import aiIcon from '@/assets/images/setting/ai-icon.png'

import { aiSteps } from './sdr-obj-const.js'

import http from '@/services/http/index.js'

export default {
  components: {
    ContentHeader,
    StepSetting,
  },
  data() {
    return {
      aiIcon,
      successSelected,
      successUnselected,
      crumbs: [
        {
          text: $t('marketing.commons.sz_e366cc'),
          to: { name: 'setting-setitems', query: { type: 'v-base-set' } },
        },
        {
          text: $t('marketing.commons.yxcj_b131f3'),
          to: { name: 'setting-setitems', query: { type: 'v-marketing-plugin' } },
        },
        {
          text: $t('marketing.pages.setting.cjpz_85872e'),
          to: false,
        },
      ],
      steps: aiSteps,
    }
  },
  computed: {

  },
  watch: {},
  mounted() {},
  methods: {
    handleItemSelected(item, index) {
      this.steps[index].loading = true
      if (item.objApiName) {
        http.marketingPluginUpdateSetting({
          pluginType: 66,
          setting: {
            [item.objApiName]: !item.selected,
          },
        }).then(res => {
          if (res.errCode === 0) {
            this.steps[index].loading = false
            this.steps[index].selected = !this.steps[index].selected
          }
        })
      }
    },
  },
}
</script>

<style lang="less" module>
.setting__marketingAi {

  .setting__container {
  }

}
</style>
