<template>
  <div :class="$style.setting__marketingSdr">
    <div
      slot="header"
      :class="$style.header"
    >
      <content-header
        :title="crumbs"
        :border="true"
      />
    </div>
    <div :class="$style.setting__container">
      <step-setting
        :title="$t('marketing.pages.setting.zngztpz_5861ef')"
        :description="$t('marketing.commons.yqdyzsglkh_5eefa2')"
        :steps="steps"
        :handle-item-selected="handleItemSelected"
        :icon="sdrIcon"
        href="https://help.fxiaoke.com/93d5/c8de/6031/5550"
      >
        <template #step-6>
          <div :class="$style.step6__button">
            <fx-button
              v-if="!generationLoading"
              size="small"
              :class="$style.setting__buttonAi"
              @click="handleGenerateConversation"
            >
              <template v-if="generationLoading">
                <i :class="['fx-icon-AIgongju', $style.text]" />
                <span :class="$style.text">{{ $t('marketing.pages.setting.hsscz_f7b21b') }}...</span>
              </template>
              <template v-else>
                <i :class="['fx-icon-AIgongju', $style.text]" />
                <span :class="$style.text">{{ $t('marketing.pages.setting.scgths_37e44f') }}</span>
                <i :class="['fx-icon-AIgongju', $style.text]" />
              </template>
            </fx-button>
            <fx-button
              size="small"
              @click="handleViewConversation"
            >
              {{ $t('marketing.pages.setting.ckhs_fd52fc') }}
            </fx-button>
          </div>
        </template>
      </step-setting>
    </div>
    <fx-dialog
      v-if="generationDialogVisible"
      :visible.sync="generationDialogVisible"
      width="752px"
      show-footer-action
      :title="$t('marketing.pages.setting.scgthsal_d1a3e7')"
      size="small"
    >
      <div
        class="setting__marketingSdr-step4Dialog"
      >
        <div class="setting__marketingSdr-generationTips">
          {{ $t('marketing.pages.setting.cczhjytxdx_7ecc15') }}
        </div>
        <fx-form
          ref="generationForm"
          :model="generationFormData"
          label-position="top"
          :rules="generationFormRules"
        >
          <fx-form-item
            :label="$t('marketing.pages.setting.cphb_9e432a')"
            prop="productCanvas"
          >
            <ObjListSelect
              v-model="generationFormData.productCanvas"
              api-name="ProductCanvasObj"
              size="small"
            />
          </fx-form-item>
          <fx-form-item
            :label="$t('marketing.pages.setting.gtzt_e2708c')"
            prop="talkTopic"
          >
            <fx-input
              v-model="generationFormData.talkTopic"
              size="small"
              type="text"
              clearable
              :maxlength="50"
              :placeholder="$t('marketing.pages.setting.qsrgtzt_2b6ae4')"
            />
          </fx-form-item>
          <fx-form-item
            :label="$t('marketing.pages.setting.zdscalsl_bde974')"
            prop="caseNumber"
          >
            <fx-input-number
              v-model="generationFormData.caseNumber"
              size="small"
              :min="1"
              :max="10"
            />
          </fx-form-item>
        </fx-form>
      </div>
      <div slot="footer">
        <fx-button
          type="primary"
          size="small"
          @click="handleGenerationConfirm"
        >
          {{ $t('marketing.commons.qd_38cf16') }}
        </fx-button>
        <fx-button
          size="small"
          @click="generationDialogVisible = false"
        >
          {{ $t('marketing.commons.qx_625fb2') }}
        </fx-button>
      </div>
    </fx-dialog>
    <fx-dialog
      v-if="step4DialogVisible"
      :visible.sync="step4DialogVisible"
      width="752px"
      show-footer-action
      :title="$t('marketing.pages.setting.ywgzpz_cbfabb')"
      size="small"
    >
      <div
        v-loading="step4DialogLoading"
        class="setting__marketingSdr-step4Dialog"
      >
        <fx-form
          ref="form"
          :model="formData"
          label-position="top"
          :rules="formRules"
        >
          <fx-form-item
            :label="$t('marketing.commons.gzmc_870802')"
            prop="ruleName"
          >
            <fx-input
              v-model="formData.ruleName"
              size="small"
              type="text"
              clearable
              :placeholder="$t('marketing.commons.qsrmc_06e2f8')"
            />
          </fx-form-item>
          <fx-form-item
            :label="$t('marketing.pages.setting.hfyy_bdd6f5')"
            width="100%"
            prop="replyLanguage"
          >
            <fx-select
              v-model="formData.replyLanguage"
              size="small"
              :options="replyLanguageOptions"
              clearable
            />
          </fx-form-item>
          <fx-form-item
            :label="$t('marketing.pages.setting.hfyq_1571ef')"
            prop="toneStyle"
          >
            <fx-select
              v-model="formData.toneStyle"
              size="small"
              multiple
              :options="replyStyleOptions"
              clearable
            />
          </fx-form-item>
          <fx-form-item
            :label="$t('marketing.pages.setting.zxjqrmb_073f91')"
            prop="consultationRobotGoal"
          >
            <fx-select
              v-model="formData.consultationRobotGoal"
              size="small"
              :options="robotTargetOptions"
              clearable
            />
          </fx-form-item>
          <fx-form-item
            :label="$t('marketing.pages.setting.lzzd_29c0eb')"
            prop="consultationInfoFields"
          >
            <fx-select
              v-model="formData.consultationInfoFields"
              size="small"
              multiple
              :options="leadFieldOptions"
              clearable
            />
            <div class="form-field_mapping">
              <span>{{ $t('marketing.commons.zdys_e5b2ad') }}</span>
              <div
                v-if="fieldMappings && fieldMappings.length > 0"
                class="form-field_mapping-item"
              >
                <i class="el-icon-circle-check form-field_mapping-icon icon-success" />
                <span>{{ $t('marketing.commons.ysz_44e607') }}</span>
              </div>
              <div
                v-else
                class="form-field_mapping-item"
              >
                <i class="el-icon-warning-outline form-field_mapping-icon icon-warning" />
                <span>{{ $t('marketing.commons.wsz_fe2d26') }}</span>
              </div>
              <fx-button
                size="small"
                type="text"
                class="form-field_mapping-setting"
                @click="handleMappingSetting"
              >
                {{ $t('marketing.commons.qsz_241141') }}
              </fx-button>
            </div>
          </fx-form-item>
          <fx-form-item
            :label="$t('marketing.pages.setting.zhmx_d89ece')"
            prop="conversionModel"
          >
            <ObjListSelect
              v-model="formData.conversionModel"
              api-name="SDRScoringModelObj"
              size="small"
            />
          </fx-form-item>
          <fx-form-item
            :label="$t('marketing.pages.setting.xsgjgz_c6061a')"
            prop="rules"
          >
            <div class="leads-follow-rule">
              <div
                v-for="(item, index) in formData.rules"
                :key="index"
                class="leads-follow-rule-item"
              >
                <span>{{ item.fieldName }}</span>
                <fx-input
                  v-model="item.value"
                  size="small"
                  type="number"
                  clearable
                  :placeholder="$t('marketing.commons.qsr_02cc4f')"
                >
                  <span
                    v-if="item.operator !== 'EQ'"
                    slot="prepend"
                  >{{ operators[item.operator] }}</span>
                  <span slot="append">{{ item.unit }}</span>
                </fx-input>
              </div>
            </div>
          </fx-form-item>
          <fx-form-item
            :label="$t('marketing.pages.setting.xzyhhxfxzd_001e31')"
            prop="objectFields"
          >
            <div class="leads-follow-rule">
              <div
                v-for="(item, index) in formData.objectFields"
                :key="index"
                class="leads-follow-rule-item"
              >
                <span>{{ item.label }}</span>
                <fx-select
                  v-model="item.fields"
                  style="flex:1;"
                  size="small"
                  multiple
                  :options="objectFieldsOptions[item.apiName]"
                  clearable
                />
              </div>
            </div>
          </fx-form-item>
          <fx-form-item
            :label="$t('marketing.pages.setting.zskj_7da531')"
            prop="knowledgeSpace"
          >
            <fx-select
              v-model="formData.knowledgeSpace"
              size="small"
              :options="knowledgeListOptions"
              clearable
            />
          </fx-form-item>
        </fx-form>
      </div>
      <div slot="footer">
        <fx-button
          type="primary"
          size="small"
          @click="handleConfirm"
        >
          {{ $t('marketing.commons.qd_38cf16') }}
        </fx-button>
        <fx-button
          size="small"
          @click="handleCancel"
        >
          {{ $t('marketing.commons.qx_625fb2') }}
        </fx-button>
      </div>
    </fx-dialog>
    <CrmMappingDialog
      v-if="mappingVisible"
      :title="$t('marketing.pages.setting.szgztdxsdy_f1a1d2')"
      :object-name="objectName"
      :target-object-name="targetObjectName"
      :show-crm-dialog.sync="mappingVisible"
      :activity-field="fields"
      :object-type.sync="mappingRecordType"
      :mapping-list.sync="fieldMappings"
      :crm_showTips.sync="mappingShowTips"
      :crm_isSubmited.sync="mappingIsSubmitted"
      :object-api-name="mappingObjectApiName"
      :strict-map-by-type="true"
      :is-show-field-scene="true"
      @submit="handleMappingSubmit"
    />
  </div>
</template>

<script>
import ContentHeader from '@/components/content-header/index.vue'
import successSelected from '@/assets/images/setting/success-selected.png'
import successUnselected from '@/assets/images/setting/success-unselected.png'
import StepSetting from './components/step-setting.vue'
import sdrIcon from '@/assets/images/setting/sdr-icon.png'

import { redirectToFS } from '@/utils/index.js'

import { sdrObjConst } from './sdr-obj-const.js'

import http from '@/services/http/index.js'

import ObjListSelect from '../../../components/obj-list-select/index.vue'

import CrmMappingDialog from '@/components/crm-mapping-dialog/index.vue'

const operators = {
  LT: $t('marketing.pages.setting.xy_f09dc0'),
  EQ: $t('marketing.commons.dy_4c35bf'),
  GT: $t('marketing.pages.setting.dy_2791dc'),
}

const rules = [
  {
    field: 'score',
    fieldName: $t('marketing.pages.setting.pfmxjg_8790d8'),
    unit: $t('marketing.commons.f_daf783'),
    operator: 'LT',
    value: null,
  },
  {
    field: 'times',
    fieldName: $t('marketing.pages.setting.zdgjcs_796afd'),
    unit: $t('marketing.commons.c_7229ec'),
    operator: 'EQ',
    value: null,
  },
  {
    field: 'interval',
    fieldName: $t('marketing.pages.setting.gjsjjg_14d2c6'),
    unit: $t('marketing.commons.xs_2de0d4'),
    operator: 'EQ',
    value: null,
  },
]

const objectFields = [
  {
    label: $t('marketing.commons.xsxs_d4ed8c'),
    apiName: 'LeadsObj',
    fields: [],
  },
  {
    label: $t('marketing.commons.lxr_52409d'),
    apiName: 'ContactObj',
    fields: [],
  },
  {
    label: $t('marketing.commons.kh_ff0b20'),
    apiName: 'AccountObj',
    fields: [],
  },
]
export default {
  components: {
    ContentHeader,
    StepSetting,
    ObjListSelect,
    CrmMappingDialog,
  },
  data() {
    return {
      sdrIcon,
      step4DialogVisible: false,
      generationDialogVisible: false,
      generationFormData: {
        productCanvas: {},
        talkTopic: '',
        caseNumber: '',
      },
      successSelected,
      successUnselected,
      crumbs: [
        {
          text: $t('marketing.commons.sz_e366cc'),
          to: { name: 'setting-setitems', query: { type: 'v-base-set' } },
        },
        {
          text: $t('marketing.commons.yxcj_b131f3'),
          to: { name: 'setting-setitems', query: { type: 'v-marketing-plugin' } },
        },
        {
          text: $t('marketing.pages.setting.zngztpz_5861ef'),
          to: false,
        },
      ],
      steps: [
        {
          id: 'step-1',
          title: $t('marketing.pages.setting.dblxkhhx_59604e'),
          description: $t('marketing.commons.dylxkhhxjz_d8f5d4'),
          selected: false,
          showSetting: true,
          routeTo: { name: 'setting-marketing-sdr' },
          objApiName: sdrObjConst.IDEAL_CUSTOMER_PROFILE_OBJ,
        },
        {
          id: 'step-2',
          title: $t('marketing.pages.setting.dbyxcphb_1a940d'),
          description: $t('marketing.commons.dyqycphfwx_7ad6e0'),
          selected: false,
          showSetting: true,
          routeTo: { name: 'setting-marketing-sdr' },
          objApiName: sdrObjConst.PRODUCT_CANVAS_OBJ,
        },
        {
          id: 'step-3',
          title: $t('marketing.pages.setting.dbpfmx_440390'),
          description: $t('marketing.pages.setting.dypfmxypgx_8ae8cb'),
          selected: false,
          showSetting: true,
          routeTo: { name: 'setting-marketing-sdr' },
          objApiName: sdrObjConst.SDR_SCORING_MODEL_OBJ,
        },
        {
          id: 'step-4',
          title: $t('marketing.pages.setting.dbywgz_d8fea9'),
          description: $t('marketing.pages.setting.dydhfyyyqf_b7e347'),
          selected: false,
          showSetting: true,
          routeTo: { name: 'setting-marketing-sdr' },
          objApiName: sdrObjConst.SDR_BUSINESS_RULE_OBJ,
          handleSetting: () => {
            this.step4DialogVisible = true
            this.marketingPluginGetSetting()
          },
        },
        {
          id: 'step-5',
          title: $t('marketing.pages.setting.dbhtk_033514'),
          description: $t('marketing.pages.setting.txjyyyhzdg_02e8dd'),
          selected: false,
          showSetting: true,
          routeTo: { name: 'setting-marketing-sdr' },
          objApiName: sdrObjConst.SDR_TOPIC_OBJ,
        },
        {
          id: 'step-6',
          title: $t('marketing.pages.setting.dbgthsal_8f776d'),
          description: $t('marketing.pages.setting.whyyhgtddh_421052'),
          selected: false,
          slot: 'step-6',
          objApiName: sdrObjConst.SDR_CONVERSATION_CASE_OBJ,
        },
        {
          id: 'step-7',
          title: $t('marketing.pages.setting.dbzxkfpz_68e721'),
          description: $t('marketing.pages.setting.qwzxkfwckf_9edd76'),
          selected: false,
          showSetting: true,
          objApiName: 'onlineServiceConfig',
          handleSetting: () => {
            redirectToFS('#app/onlineservice/wxworkreport', 'manage')
          },
        },
      ],
      generationFormRules: {
        productCanvas: [
          {
            required: true,
            trigger: 'blur',
            validator: (_, value, callback) => {
              if (!value.id) {
                callback(new Error($t('marketing.pages.setting.qxzcphb_413e54')))
                return
              }
              callback()
            },
          },
        ],
        talkTopic: [
          { required: true, message: $t('marketing.pages.setting.qsrgtzt_2b6ae4'), trigger: 'blur' },
        ],
        caseNumber: [
          { required: true, message: $t('marketing.pages.setting.qsrzdscals_385430'), trigger: 'blur' },
        ],
      },
      formRules: {
        ruleName: [
          { required: true, message: $t('marketing.commons.qsrmc_06e2f8'), trigger: 'blur' },
        ],
        replyLanguage: [
          { required: true, message: $t('marketing.pages.setting.qxzhfyy_c19ef9'), trigger: 'blur' },
        ],
        toneStyle: [
          { required: true, message: $t('marketing.pages.setting.qxzhfyq_034f09'), trigger: 'blur' },
        ],
        consultationRobotGoal: [
          { required: true, message: $t('marketing.pages.setting.qxzzxjqrmb_e1da37'), trigger: 'blur' },
        ],
        consultationInfoFields: [
          { required: true, message: $t('marketing.pages.setting.qxzlzzd_2f4070'), trigger: 'blur' },
        ],
        conversionModel: [
          {
            required: true,
            trigger: 'blur',
            validator: (_, value, callback) => {
              if (!value.id) {
                callback(new Error($t('marketing.pages.setting.qxzzhmx_b02c9c')))
                return
              }
              callback()
            },
          },
        ],
        conversionMetrics: [
          { required: true, message: $t('marketing.pages.setting.qsrzhzb_97838a'), trigger: 'blur' },
        ],
        rules: [
          {
            required: true,
            message: $t('marketing.pages.setting.qsrxsgjgz_09951e'),
            trigger: 'blur',
            validator: (_, value, callback) => {
              if (value && value.length > 0) {
                value.forEach(item => {
                  if (!item.value) {
                    callback(new Error($t('marketing.pages.setting.qsrxsgjgz_09951e')))
                  }
                })
              }
              callback()
            },
          },
        ],
        knowledgeSpace: [
          { required: true, message: $t('marketing.pages.setting.qxzzskj_bc8987'), trigger: 'blur' },
        ],
      },
      options: [
        {
          label: $t('marketing.pages.setting.zw_a7bac2'),
          value: 'zh',
        },
        {
          label: $t('marketing.pages.setting.yw_f9fb6a'),
          value: 'en',
        },
      ],
      formData: {
        sdrRuleId: '',
        ruleName: '',
        replyLanguage: '',
        toneStyle: [],
        consultationRobotGoal: '',
        consultationInfoFields: [],
        conversionModel: {},
        conversionMetrics: '',
        knowledgeSpace: '',
        rules,
        objectFields,
      },
      replyLanguageOptions: [],
      replyStyleOptions: [],
      robotTargetOptions: [],
      leadFieldOptions: [],
      conversionModelOptions: [],
      conversionTargetOptions: [],
      knowledgeListOptions: [],

      generationLoading: false,
      step4DialogLoading: true,
      operators,

      objectFieldsOptions: {
        LeadsObj: [],
        AccountObj: [],
        ContactObj: [],
      },

      mappingVisible: false,

      leadsFields: [],
      fields: [],
      objectName: $t('marketing.commons.gzt_003c12'),
      targetObjectName: $t('marketing.commons.xs_ad46a9'),
      mappingObjectApiName: 'LeadsObj',
      mappingShowTips: false,
      mappingIsSubmitted: true,
      fieldMappings: [],
      mappingRecordType: 'default__c',
    }
  },
  watch: {},
  mounted() {
    this.marketingPluginGetSetting()
    this.getObjectFieldsOptions()
    this.getSDRBusinessRuleObjFields()
    this.queryKnowledgeBaseList()
    document.querySelector('.g-content-wrapper').scrollTop = 0
    document.querySelector('.g-content-wrapper').style.overflow = 'auto'
  },
  methods: {
    async getCrmObjectFields(objectApiName) {
      await http
        .getCrmObjectFields({
          objectApiName,
          recordType: 'default__c',
        })
        .then(({ errCode, data }) => {
          if (errCode === 0) {
            this.leadsFields = (data || []).filter(
              d => d.fieldName !== 'data_own_organization',
            )
          }
        })
    },
    async handleMappingSetting() {
      this.mappingVisible = true
      await this.getCrmObjectFields(this.mappingObjectApiName)
      const _fields = (this.leadsFields || []).filter(el => this.formData.consultationInfoFields.includes(el.fieldName))
      this.fields = _fields
      if (this.fieldMappings && this.fieldMappings.length > 0) {
        this.fieldMappings = this.fieldMappings.map(item => ({
          ...item,
          modifiable: !this.formData.consultationInfoFields.includes(item.crmFieldName),
        }))
      } else {
        this.fieldMappings = _fields.map((item, index) => ({
          crmFieldName: null,
          mankeepFieldName: item.fieldName,
          modifiable: false,
          scene: item.scene,
          _index: index,
        }))
      }
    },
    handleGenerationConfirm() {
      this.$refs.generationForm.validate().then(valid => {
        if (valid) {
          http.sdrCreateConversationCase({
            productCanvasId: this.generationFormData.productCanvas.id,
            caseNumber: this.generationFormData.caseNumber,
            talkTopic: this.generationFormData.talkTopic,
          }).then(res => {
            if (res.errCode === 0) {
              FxUI.Message.success($t('marketing.commons.qqcg_70941a'))
              this.generationDialogVisible = false
            }
          })
        }
      })
    },
    handleViewConversation() {
      redirectToFS('#paasapp/list/=/appId_CRM/SDRConversationCaseObj')
    },
    handleGenerateConversation() {
      this.generationDialogVisible = true
    },
    handleConversionModelClick() {},
    marketingPluginGetSetting() {
      this.step4DialogLoading = true
      http.marketingPluginGetSetting({
        pluginType: 60,
      }).then(res => {
        this.step4DialogLoading = false
        if (res.errCode === 0 && res.data) {
          this.steps.forEach(item => {
            if (res.data[item.objApiName]) {
              item.selected = true
            }
          })
          const _formData = res.data.ruleData || {}
          const _rule = JSON.parse(_formData.rule || '{}') || {}
          const _rules = _rule?.rules || []
          const _objectFields = _rule?.objectFields || []
          const _fieldMappings = _rule?.fieldMappings || {}
          this.formData = {
            ruleName: _formData.ruleName,
            replyLanguage: _formData.replyLanguage,
            toneStyle: _formData.toneStyle,
            consultationRobotGoal: _formData.consultationRobotGoal,
            consultationInfoFields: _formData.consultationInfoFields,
            conversionModel: {
              id: _formData.conversionModelId,
              name: _formData.conversionModelName,
            },
            conversionMetrics: _formData.conversionMetrics,
            knowledgeSpace: _formData.knowledgeSpace,
            sdrRuleId: _formData.sdrRuleId,
            rules: rules.map((item, index) => ({
              ...item,
              value: _rules[index]?.value,
            })),
            objectFields: objectFields.map((item, index) => ({
              ...item,
              fields: _objectFields[index]?.fields,
            })),
          }
          this.fieldMappings = _fieldMappings.mappings || []
          this.mappingRecordType = _fieldMappings.recordType || 'default__c'
        }
      })
    },
    queryKnowledgeBaseList() {
      http.queryKnowledgeBaseList({
        sceneType: 1,
        status: 1,
      }).then(res => {
        if (res && res.Result && res.Result.StatusCode === 0) {
          this.knowledgeListOptions = res.Value?.data?.knowledgeList?.map(el => ({
            label: el.sceneName,
            value: el.id || el.scene,
          }))
        }
      })
    },
    async getObjectFields(objectApiName) {
      const res = await http.getCrmObjectFields({
        objectApiName,
        recordType: 'default__c',
      })
      if (res && res.errCode === 0) {
        return (res.data || []).map(el => ({
          label: el.fieldCaption,
          value: el.fieldName,
        }))
      }
      return []
    },
    async getSDRBusinessRuleObjFields() {
      const res = await http.getCrmObjectFields({
        objectApiName: 'SDRBusinessRuleObj',
        recordType: 'default__c',
      })
      if (res && res.errCode === 0 && res.data) {
        res.data.forEach(item => {
          if (item.fieldName === 'reply_language') {
            this.replyLanguageOptions = item.enumDetails?.map(el => ({
              label: el.itemName,
              value: el.itemCode,
            }))
          }
          if (item.fieldName === 'tone_style') {
            this.replyStyleOptions = item.enumDetails?.map(el => ({
              label: el.itemName,
              value: el.itemCode,
            }))
          }
          if (item.fieldName === 'consultation_robot_target') {
            this.robotTargetOptions = item.enumDetails?.map(el => ({
              label: el.itemName,
              value: el.itemCode,
            }))
          }
          if (item.fieldName === 'lead_info') {
            this.leadFieldOptions = item.enumDetails?.map(el => ({
              label: el.itemName,
              value: el.itemCode,
            }))
          }
          if (item.fieldName === 'score_module') {
            this.conversionModelOptions = item.enumDetails?.map(el => ({
              label: el.itemName,
              value: el.itemCode,
            }))
          }
        })
      }
    },
    async getObjectFieldsOptions() {
      const [leads, account, contact] = await Promise.all([
        this.getObjectFields('LeadsObj'),
        this.getObjectFields('AccountObj'),
        this.getObjectFields('ContactObj'),
      ])

      this.objectFieldsOptions.LeadsObj = leads
      this.objectFieldsOptions.AccountObj = account
      this.objectFieldsOptions.ContactObj = contact
    },
    handleItemSelected(item, index) {
      if (item.objApiName) {
        http.marketingPluginUpdateSetting({
          pluginType: 60,
          setting: {
            [item.objApiName]: !item.selected,
          },
        }).then(res => {
          if (res.errCode === 0) {
            this.steps[index].selected = !this.steps[index].selected
          }
        })
      }
    },
    handleConfirm() {
      this.$refs.form.validate().then(valid => {
        if (valid) {
          this.handleCreateSDRRule()
        }
      })
    },
    handleCancel() {
      this.step4DialogVisible = false
    },
    handleMappingSubmit() {
      this.fieldMappings = this.fieldMappings.map(item => ({
        ...item,
        modifiable: false,
      }))
    },
    handleCreateSDRRule() {
      const params = {
        ruleName: this.formData.ruleName,
        replyLanguage: this.formData.replyLanguage,
        toneStyle: this.formData.toneStyle,
        consultationRobotGoal: this.formData.consultationRobotGoal,
        consultationInfoFields: this.formData.consultationInfoFields,
        conversionModel: this.formData.conversionModel.id,
        conversionMetrics: parseInt(this.formData.conversionMetrics, 10),
        knowledgeSpace: this.formData.knowledgeSpace,
        rule: JSON.stringify({
          rules: this.formData.rules,
          objectFields: this.formData.objectFields,
          fieldMappings: {
            mappings: this.fieldMappings,
            recordType: this.mappingRecordType,
          },
        }),
      }
      if (this.formData.sdrRuleId) {
        params.sdrRuleId = this.formData.sdrRuleId
      }
      http.sdrCreateOrUpdateSDRRule(params).then(res => {
        if (res.errCode === 0) {
          FxUI.Message.success($t('marketing.commons.szcg_f6088e'))
          this.step4DialogVisible = false
        }
      })
    },
  },
}
</script>

<style lang="less">
.setting__marketingSdr-step4Dialog {

  .setting__marketingSdr-generationTips {
    background: #f2f3f5;
    padding: 12px 20px;
    border-radius: 6px;
    margin-bottom: 10px;
  }

  .leads-follow-rule {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 20px;
    border-radius: 8px;
    background-color: #FAFAFA;

    .leads-follow-rule-item {
      display: flex;
      align-items: center;

      > span {
        min-width: 120px;
        text-align: left;
        line-height: 32px;
      }
    }
  }

  .form-field_mapping {
    display: flex;
    align-items: center;
    line-height: 32px;
    gap: 10px;

    .form-field_mapping-icon {
      font-size: 16px;
      margin-right: 2px;
    }

    .icon-success {
      color: #30C776;
    }

    .icon-warning {
      color: #FF522A;
    }

    .form-field_mapping-setting {
      color: @color-link;
    }
  }

  .el-form-item__content {
    .fx-select {
      width: 100%;
    }
  }
  .el-form-item--small.el-form-item {
    margin-bottom: 0;
  }
  .fx-form {
    .fx-form-item {
      margin-bottom: 10px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style>

<style lang="less" module>
.setting__marketingSdr {

  .header {
    position: sticky;
    top: 0;
    z-index: 100;
  }

  .setting__container {
    // padding: 0 12px;
  }

  .step6__button {
    margin-top: 8px;
    display: flex;
  }

  .setting__buttonAi {
    border: 1px solid transparent;
    /* 👇 双背景实现渐变边框 + 白色内部 */
    background: linear-gradient(white, white) padding-box,  /* 内层白色 */
      radial-gradient(110% 110% at 16.75% 100%, #09F 0%, #A033FF 60%, #FF5280 90%, #FF7061 100%) border-box;  /* 外层渐变边框 */

    background-origin: padding-box, border-box;
    background-clip: padding-box, border-box;
    transition: background 0.2s ease-in;

    .text {
      transition: background 0.2s ease-in;
      background: radial-gradient(110% 110% at 16.75% 100%, #09F 0%, #A033FF 60%, #FF5280 90%, #FF7061 100%);
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    &:hover {
      background: var(--color-primary01, #fff7e6);
      .text {
        background: var(--color-primary05, #ff9b29);
        background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
  }

  .step4__dialog {

  }

}
</style>
