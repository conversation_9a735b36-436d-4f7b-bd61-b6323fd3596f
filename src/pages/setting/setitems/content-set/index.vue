<!-- 组件说明 -->
<template>
  <div class="content-set__wrapper">
    <div :class="['setitem', wxAuthSettingEdit ? 'setitem-edit' : '']">
      <div class="setitle">
        {{ $t('marketing.pages.setting.nrfwsqsz_66f734') }}
      </div>
      <div class="setdesc">
        <div class="h5content-tip">
          {{ $t('marketing.pages.setting.wymwzcpwjs_be523a') }}
          <fx-link
            type="standard"
            size="small"
            target="_blank"
            href="https://help.fxiaoke.com/93d5/5540/3dc1"
          >
            {{ $t('marketing.pages.setting.cksqsm_91ad7f') }}
          </fx-link>
        </div>
        <div class="h5content-button-item">
          <span style="margin-right: 20px">{{ $t('marketing.pages.setting.gzhsqfs_bc437e') }}</span>
          <RadioGroup
            v-model="wxAuthStatus"
            class="h5content-radio"
            :disabled="!wxAuthSettingEdit"
          >
            <Radio :label="0">
              {{ $t('marketing.pages.setting.wgzhsq_7c9460') }}
            </Radio>
            <Radio :label="1">
              {{ $t('marketing.pages.setting.gzhjmsq_36c3c1') }}
            </Radio>
            <Radio :label="2">
              {{ $t('marketing.pages.setting.gzhzdsq_8473eb') }}
            </Radio>
          </RadioGroup>
        </div>
        <div
          v-if="wxAuthStatus !== 0"
          class="h5content-button-item"
        >
          <span style="margin-right: 20px">{{ $t('marketing.pages.setting.sqgzh_d2da9f') }}</span>
          <span v-if="!wxAuthSettingEdit">{{ authWxAppName || $t('marketing.commons.w_d81bb2') }}</span>
          <Select
            v-else
            v-model="authWxAppId"
            class="h5content-select"
            size="small"
            filterable
          >
            <Option
              v-for="item in wxLists"
              :key="item.wxAppId"
              :label="item.wxAppName"
              :value="item.wxAppId"
            />
          </Select>
        </div>
        <div
          v-if="wxAuthSettingEdit"
          class="edit-button-group"
        >
          <fx-button
            type="primary"
            size="mini"
            :loading="authWxLoading"
            @click="() => toggleWxAuthSettingSave('confirm')"
          >
            {{ $t('marketing.commons.bc_be5fbb') }}
          </fx-button>
          <fx-button
            size="mini"
            @click="() => toggleWxAuthSettingSave('cancel')"
          >
            {{
              $t('marketing.commons.qx_625fb2')
            }}
          </fx-button>
        </div>
      </div>
      <div
        v-if="!wxAuthSettingEdit"
        class="btn-group setting-item-btn"
      >
        <a @click="() => (wxAuthSettingEdit = true)">{{ $t('marketing.commons.sz_e366cc') }}</a>
      </div>
    </div>
    <div :class="['setitem', miniProgramAuthSettingEdit ? 'setitem-edit' : '', hideWxAuthSetting ? 'hide-show-wx-auth-setting' : '']">
      <div class="setitle">
        {{ $t('marketing.pages.setting.xcxnrfwsqs_3242c4') }}
      </div>
      <div class="setdesc">
        <div class="h5content-tip">
          {{ $t('marketing.pages.setting.wymwzcpwjs_ab4c43') }}
          <fx-link
            type="standard"
            size="small"
            target="_blank"
            href="https://help.fxiaoke.com/93d5/5540/3dc1"
          >
            {{ $t('marketing.pages.setting.cksqsm_91ad7f') }}
          </fx-link>
        </div>
        <div class="h5content-button-item">
          <span style="margin-right: 20px">{{ $t('marketing.pages.setting.sqfs_e6f883') }}</span>
          <RadioGroup
            v-model="miniProgramAuthSetting.type"
            class="h5content-radio"
            :disabled="!miniProgramAuthSettingEdit"
            @change="handleMiniProgramAuthTypeChange"
          >
            <Radio :label="0">
              {{ $t('marketing.pages.setting.wxsq_e23fa6') }}
            </Radio>
            <Radio :label="1">
              {{ $t('marketing.pages.setting.jsqsj_e0ed22') }}
            </Radio>
            <Radio :label="2">
              {{ $t('marketing.pages.setting.jsqtxnc_c35f46') }}
            </Radio>
            <Radio :label="3">
              {{ $t('marketing.pages.setting.sqtxncsj_e1c10b') }}
            </Radio>
          </RadioGroup>
        </div>
        <div
          v-if="miniProgramAuthSetting.type === 1 || miniProgramAuthSetting.type === 3"
          class="h5content-button-item"
        >
          <span style="margin-right: 20px">{{ $t('marketing.pages.setting.xscr_cecd36') }}</span>
          <span style="margin-right: 20px">{{ miniProgramAuthMapText }}</span>
          <a
            v-if="miniProgramAuthSettingEdit"
            style="cursor: pointer"
            @click="handleMiniProgramAuthSettingDialogShow"
          >{{ $t('marketing.commons.sz_e366cc') }}</a>
        </div>
        <div
          v-if="miniProgramAuthSettingEdit"
          class="edit-button-group"
        >
          <fx-button
            type="primary"
            size="mini"
            :loading="miniProgramAuthLoading"
            @click="() => toggleMiniProgramAuthSettingSave('confirm')"
          >
            {{ $t('marketing.commons.bc_be5fbb') }}
          </fx-button>
          <fx-button
            size="mini"
            @click="() => toggleMiniProgramAuthSettingSave('cancel')"
          >
            {{
              $t('marketing.commons.qx_625fb2')
            }}
          </fx-button>
        </div>
      </div>
      <div
        v-if="!miniProgramAuthSettingEdit"
        class="btn-group setting-item-btn"
      >
        <a @click="() => (miniProgramAuthSettingEdit = true)">{{ $t('marketing.commons.sz_e366cc') }}</a>
      </div>
    </div>
    <div class="setitem">
      <div class="setitle">
        {{ $t('marketing.commons.nrbqsz_60e2c9') }}
      </div>
      <div class="setdesc">
        {{ $t('marketing.pages.setting.gjnrbqtxkj_4dda8d') }}
      </div>
      <div class="btn-group setting-item-btn">
        <a @click="handleToTagManage">{{ $t('marketing.commons.sz_e366cc') }}</a>
      </div>
    </div>
    <!-- <div
      v-loading="menuLoading"
      class="setitem"
    >
      <div class="setitle">
        {{ $t('marketing.pages.setting.yddhcbltgr_08128b') }}
      </div>
      <div class="h5content-tip">
        {{ $t('marketing.pages.setting.kyszggnrkd_f8f9c3') }}
        <a
          style="cursor: pointer"
          @click="menuDemoVisible = true"
        >{{ $t('marketing.commons.cksl_95dbbe') }}</a>
      </div>
      <v-table
        v-if="menuTableData.length"
        ref="table"
        class="menu-table"
        :columns="menuColumns"
        :empty-text="$t('marketing.commons.zwsj_21efd8')"
        :data="menuTableData"
        :row-style="{ height: '40px', cursor: 'pointer' }"
      >
        <template #showName="{ row, index }">
          <div
            v-if="isActiveEditIndex === index"
            class="show-name-row"
          >
            <fx-input
              v-model="row.showName"
              class="show-name__input"
              :placeholder="$t('marketing.commons.qsrmc_06e2f8')"
              maxlength="5"
            />
            <fx-button
              class="show-name__btn"
              type="primary"
              @click="saveShowName(row)"
            >
              {{ $t('marketing.commons.qd_38cf16') }}
            </fx-button>
          </div>
          <div
            v-else
            class="show-name-row"
          >
            <span>{{ row.showName }}</span>
            <i
              class="el-icon-edit"
              @click="isActiveEditIndex = index"
            />
          </div>
        </template>
        <template #showStatus="{ row, index }">
          <el-switch
            v-model="row.showStatus"
            size="small"
            @change="handleSwitchStatus(row, index)"
          />
        </template>
      </v-table>
    </div> -->

    <SetitemsChannelManage />

    <Dialog
      :title="$t('marketing.pages.setting.nrrkzssl_ed6214')"
      width="1000px"
      :visible="menuDemoVisible"
      :show-confirm="false"
      :cancel-text="$t('marketing.commons.gb_b15d91')"
      @onClose="menuDemoVisible = false"
    >
      <img
        :src="materialMenuTipsImg"
        width="100%"
        alt=""
      >
    </Dialog>

    <MiniProgramAuthSettingDialog
      v-if="miniProgramAuthSettingDialogVisible"
      :visible="miniProgramAuthSettingDialogVisible"
      :mini-program-auth-setting="miniProgramAuthSetting"
      @onClose="handleMiniProgramAuthSettingDialogHide"
      @onSubmit="handleMiniProgramAuthSettingDialogSubmit"
    />
  </div>
</template>

<script>
import http from '@/services/http/index.js'
import VTable from '@/components/table-ex/index.vue'
import MiniProgramAuthSettingDialog from './miniProgramAuthSettingDialog.vue'

import Dialog from '@/components/dialog/index.vue'
import wxList from '@/modules/wx-list.js'
import { formatStringByEmpty } from '@/utils/index.js'
import { defaultMenus, roleEnum, materialTypeEnum } from './const.js'
import kisvData from '@/modules/kisv-data.js'
import { messageBoxConfirm } from '@/utils/message-box.js'
import SetitemsChannelManage from '../SetitemsChannelManage.vue'

import materialMenuTipsImg from '@/assets/images/setting/material-menu-tips.png'

const formatter = (rowData, column, cellValue) => formatStringByEmpty(cellValue)

export default {
  components: {
    ElSwitch: FxUI.Switch,
    RadioGroup: FxUI.RadioGroup,
    Radio: FxUI.Radio,
    Select: FxUI.Select.components.ElSelect,
    Option: FxUI.Select.components.ElSelect.components.ElOption,
    Dialog,
    VTable,
    SetitemsChannelManage,
    MiniProgramAuthSettingDialog,
  },
  props: {
    ea: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      datas: kisvData.datas,
      wxLists: [],
      wxAuthSettingEdit: false,
      wxAuthStatus: 0,
      authWxLoading: false,
      authWxAppId: '',
      authWxAppName: '',
      menuDemoVisible: false,
      menuColumns: [
        {
          prop: 'label',
          label: $t('marketing.pages.setting.tgwllx_af6a7e'),
          minWidth: 140,
          formatter,
        },
        {
          prop: 'platform',
          label: $t('marketing.pages.setting.zsd_376157'),
          minWidth: 400,
          formatter,
        },
        {
          prop: 'showName',
          label: $t('marketing.commons.zsmc_879c24'),
          slotName: 'showName',
          minWidth: 200,
          exComponent: 'slot',
        },
        {
          prop: 'roles',
          label: $t('marketing.pages.setting.rkqx_4e10a6'),
          minWidth: 160,
          formatter,
        },
        {
          prop: 'showStatus',
          label: $t('marketing.pages.setting.zsyc_ad6b9b'),
          slotName: 'showStatus',
          minWidth: 120,
          exComponent: 'slot',
        },
      ],
      menuLoading: true,
      menuTableData: [],
      isActiveEditIndex: -1,
      materialMenuTipsImg,
      miniProgramAuthLoading: false,
      miniProgramAuthSettingEdit: false,
      miniProgramAuthSettingDialogVisible: false,
      miniProgramAuthSetting: {
        type: 0,
        crmPoolId: '',
        targetObjectRecordType: '',
        crmFormFieldMap: [],
        autoCreateMember: false,
      },
      hideWxAuthSetting: false,
    }
  },
  computed: {
    miniProgramAuthMapText() {
      const { crmPoolId, crmFormFieldMap } = this.miniProgramAuthSetting
      if (crmFormFieldMap.length === 0) {
        return $t('marketing.commons.zw_f61f4c')
      }

      if (crmPoolId) {
        return $t('marketing.commons.xsc_7b62ce')
      }

      return $t('marketing.commons.xsxs_d4ed8c')
    },
  },
  mounted() {
    this.getH5AccessPermissionsSeeting()
    // this.queryMaterialShowList()
    wxList.queryList().then(datas => {
      this.wxLists = datas.list || []
    })

    this.getMiniappAccessPermissionsSetting()
  },
  methods: {
    toggleWxAuthSettingSave(type) {
      if (type === 'confirm') {
        this.handleAuthWxSubmit()
      } else {
        this.wxAuthSettingEdit = false
        this.getH5AccessPermissionsSeeting()
      }
    },
    handleAuthWxSubmit() {
      if (this.wxAuthStatus !== 0 && !this.authWxAppId) {
        FxUI.Message.warning($t('marketing.pages.setting.qxzsqgzh_3a57e3'))
        return
      }
      const [item = {}] = this.wxLists.filter(el => el.wxAppId === this.authWxAppId)
      this.authWxLoading = true
      http
        .saveH5AccessPermissionsSeeting({
          status: this.wxAuthStatus,
          wxAppId: this.authWxAppId,
          wxAppName: item.wxAppName || '',
        })
        .then(({ errCode, errMsg }) => {
          this.authWxLoading = false
          if (errCode === 0) {
            FxUI.Message.success($t('marketing.commons.bccg_3b1083'))
            this.wxAuthSettingEdit = false
            this.getH5AccessPermissionsSeeting()
          } else {
            FxUI.Message.error(errMsg || $t('marketing.commons.bcsb_6de920'))
          }
        })
    },
    getH5AccessPermissionsSeeting() {
      http.getH5AccessPermissionsSeeting().then(({ errCode, data }) => {
        if (errCode === 0) {
          this.authWxAppId = data.wxAppId
          this.authWxAppName = data.wxAppName
          this.wxAuthStatus = data.status
        }
      })
    },
    toggleMiniProgramAuthSettingSave(type) {
      if (type === 'confirm') {
        if (this.miniProgramAuthSetting.type === 0) {
          FxUI.MessageBox.confirm($t('marketing.pages.setting.wxyygfyqby_0ad267'), $t('marketing.commons.ts_02d981'), {
            confirmButtonText: $t('marketing.commons.qd_38cf16'),
            cancelButtonText: $t('marketing.commons.qx_625fb2'),
            type: 'warning',
          }).then(() => {
            this.handleMiniProgramAuthSubmit()
          })
        } else {
          this.handleMiniProgramAuthSubmit()
        }
      } else {
        this.miniProgramAuthSettingEdit = false
        this.getMiniappAccessPermissionsSetting()
      }
    },
    getMiniappAccessPermissionsSetting() {
      http.getMiniappAccessPermissionsSetting()
        .then(({ errCode, data = {} }) => {
          if (errCode === 0) {
            this.hideWxAuthSetting = !data.type;
            this.miniProgramAuthSetting = {
              ...this.miniProgramAuthSetting,
              ...data,
            }
          }
        })
    },
    handleMiniProgramAuthSubmit() {
      this.miniProgramAuthLoading = true
      http.saveMiniappAccessPermissionsSetting(this.miniProgramAuthSetting)
        .then(({ errCode, errMsg }) => {
          this.miniProgramAuthLoading = false
          if (errCode === 0) {
            FxUI.Message.success($t('marketing.commons.bccg_3b1083'))
            this.miniProgramAuthSettingEdit = false
            this.getMiniappAccessPermissionsSetting()
          } else {
            FxUI.Message.error(errMsg || $t('marketing.commons.bcsb_6de920'))
          }
        })
        .catch(() => {
          this.miniProgramAuthLoading = false
        })
    },
    handleMiniProgramAuthSettingDialogShow() {
      this.miniProgramAuthSettingDialogVisible = true
    },
    handleMiniProgramAuthSettingDialogHide() {
      this.miniProgramAuthSettingDialogVisible = false
    },
    handleMiniProgramAuthSettingDialogSubmit(payload) {
      this.miniProgramAuthSetting = {
        ...this.miniProgramAuthSetting,
        ...payload,
      }
      this.handleMiniProgramAuthSettingDialogHide()
    },
    handleMiniProgramAuthTypeChange() {
      this.miniProgramAuthSetting.crmPoolId = ''
      this.miniProgramAuthSetting.crmFormFieldMap = []
      this.miniProgramAuthSetting.targetObjectRecordType = ''
    },
    queryMaterialShowList() {
      this.menuLoading = true
      http.queryMaterialShowList().then(({ errCode, data }) => {
        if (errCode === 0) {
          if (data && data.length) {
            data.forEach(item => {
              const _menuItem = defaultMenus[item.type]
              if (_menuItem) {
                defaultMenus[item.type] = {
                  ..._menuItem,
                  showName: item.showName || _menuItem.label,
                  showStatus: item.showStatus,
                  roles: _menuItem.roles || roleEnum.all,
                }
              }
            })
          }
        }

        Object.entries(defaultMenus).forEach(([k, v]) => {
          this.menuTableData.push({
            ...v,
            type: k,
            showName: v.showName || v.label,
            showStatus: v.showStatus !== false,
            platform: v.platforms.join('、'),
          })
        })
        this.menuLoading = false
      })
    },
    saveOrUpdateMaterialShow(params) {
      http.saveOrUpdateMaterialShow(params).then(({ errCode, errMsg }) => {
        if (errCode === 0) {
          FxUI.Message.success($t('marketing.commons.xgcg_69be67'))
        } else {
          FxUI.Message.error(errMsg || $t('marketing.pages.setting.xgsb_5badb3'))
        }
      })
    },
    saveShowName(row) {
      this.isActiveEditIndex = -1
      this.saveOrUpdateMaterialShow({
        showName: row.showName,
        showStatus: row.showStatus,
        type: Number(row.type),
      })
    },
    async handleSwitchStatus(row, index) {
      if (row.showStatus) {
        if (Number(row.type) === materialTypeEnum.picture && !this.datas.uinfo.mobilePhotoLibrary) {
          messageBoxConfirm({
            mainText: $t('marketing.pages.setting.xyxkqtpkcj_4be839'),
            boxConfig: {
              showConfirmButton: false,
              cancelButtonText: $t('marketing.commons.wzdl_fe0337'),
            },
          })
          this.$set(this.menuTableData, index, {
            ...this.menuTableData[index],
            showStatus: false,
          })
          return
        } if (Number(row.type) === materialTypeEnum.distribution && !this.datas.uinfo.socialDistributeEnabled) {
          messageBoxConfirm({
            mainText: $t('marketing.pages.setting.xyxkqshhfx_dc22da'),
            boxConfig: {
              showConfirmButton: false,
              cancelButtonText: $t('marketing.commons.wzdl_fe0337'),
            },
          })
          this.$set(this.menuTableData, index, {
            ...this.menuTableData[index],
            showStatus: false,
          })
          return
        }
      }
      const confirm = await messageBoxConfirm({
        mainText: `${row.showStatus ? $t('marketing.commons.qrkq_57901a') : $t('marketing.commons.qrgb_e8e3bf')}【${row.label}】？`,
      })
      if (confirm) {
        this.saveOrUpdateMaterialShow({
          showName: row.showName,
          showStatus: row.showStatus,
          type: Number(row.type),
        })
      } else {
        this.$set(this.menuTableData, index, {
          ...this.menuTableData[index],
          showStatus: !row.showStatus,
        })
      }
    },
    handleToTagManage() {
      this.$router.push({
        name: 'content-tags-manage',
      })
    },
  },
}
</script>

<style lang="less">
.content-set__wrapper {
  .btn-group {
    margin-top: 13px;
    margin-bottom: 20px;
    margin-left: 113px;
    button {
      padding: 8px 30px;
      margin-right: 29px;
      margin-left: 0;
    }
    a {
      cursor: pointer;
    }
  }
  .setitem {
    position: relative;
    padding: 20px;
    border-bottom: 1px dashed #e9edf5;
    &.setitem-edit {
      background-color: #e9f2ff;
    }
    .el-radio__input.is-disabled + span.el-radio__label,
    .el-checkbox__input.is-checked + .el-checkbox__label {
      color: @color-title;
    }

    &.hide-show-wx-auth-setting {
      display: none;
    }
  }
  .edit-button-group {
    margin-top: 15px;
  }
  .setting-item-btn {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translate(0, -50%);
    margin: 0;
  }
  .setitle {
    margin-bottom: 8px;
    font-size: 16px;
    color: #181c25;
    display: flex;
    align-items: center;
    font-weight: bold;
  }
  .setdesc {
    color: #91959e;
    padding-right: 120px;
    font-size: 14px;
  }
  .setstat {
    position: absolute;
    top: 50px;
    right: 8px;
  }
  .h5content-tip {
    font-size: 14px;
    color: #91959e;
    margin-bottom: 10px;
    text-align: justify;
  }
  .h5content-button {
    &-item {
      margin-top: 10px;
      color: @color-title;
      > a {
        cursor: pointer;
        color: @color-link;
      }
    }
  }
  .h5content-select {
    width: 300px !important;
  }
  .menu-table {
    max-width: 1000px;
    .el-table__body {
      border-left: 1px solid #ebeef5 !important;
    }
    .el-table__cell {
      border-right: 1px solid #ebeef5 !important;
    }

    .el-input__inner {
      height: 32px;
    }

    .show-name-row {
      display: flex;
      align-items: center;

      .show-name__input {
      }

      .show-name__btn {
        padding: 4px 8px;
        margin-left: 16px;
      }

      i {
        font-size: 16px;
        margin-left: 4px;
      }
    }
  }
}
</style>
