<!--
 * @Author: <EMAIL>
 * @Date: 2021-09-26 16:07:19
 * @Description: 营销插件
-->
<template>
  <div class="MarketingPlugin">
    <div class="MarketingPlugin__wrapper">
      <div
        v-for="(item, key) in marketingPluginMap"
        :key="key"
      >
        <div class="MarketingPlugin__category-wrapper">
          <div class="MarketingPlugin__category">
            {{ item.category }}
          </div>
          <div class="MarketingPlugin__plugins">
            <SmartLayout
              :gutter="16"
              child-type="fixed"
            >
              <div
                v-for="plugin in item.plugins"
                v-show="!plugin.hide"
                :key="plugin.id"
                class="MarketingPlugin__item"
              >
                <div class="MarketingPlugin__title">
                  <div class="title__text">
                    <i
                      v-if="plugin.iconfont"
                      :style="{color: plugin.iconColor}"
                      :class="['iconfont','icon',plugin.icon]"
                    />
                    <img
                      v-else
                      :src="plugin.icon"
                      class="icon"
                    > {{ plugin.title }}
                  </div>
                  <QuestionTooltip
                    v-show="plugin.questionTips"
                    class="title__icon"
                  >
                    <div
                      slot="question-content"
                      class="sum-question-content"
                      v-html="plugin.questionTips"
                    />
                  </QuestionTooltip>
                </div>
                <div class="MarketingPlugin__desc" :title="plugin.desc">
                  {{ plugin.desc }}
                </div>
                <div
                  v-if="plugin.onlyPro && !isPro && !plugin.licenseList"
                  class="MarketingPlugin__switch"
                  style="justify-content: flex-end;"
                >
                  <fx-button
                    class="MarketingPlugin__button"
                    @click="updateToPro"
                  >
                    {{ $t('marketing.pages.setting.sjkt_8ceecf') }}
                  </fx-button>
                </div>
                <div
                  v-else-if="plugin.needApply && !plugin.model_switch && !(plugin.id === 'coupons' && enterCouponEaList)"
                  class="MarketingPlugin__switch"
                  style="justify-content: flex-end;"
                >
                  <fx-button
                    class="MarketingPlugin__button"
                    @click="applyForBeta"
                  >
                    {{ $t('marketing.commons.sysq_09a25a') }}
                  </fx-button>
                </div>

                <div
                  v-else
                  class="MarketingPlugin__switch"
                >
                  <div>
                    <div v-if="plugin.isLead">
                      <fx-button
                        type="text"
                        @click="joinSettingVisible = true"
                      >
                        {{ $t('marketing.commons.djsz_4fa09e') }}
                      </fx-button>
                      <span class="space">|</span>
                      <fx-button
                        type="text"
                        @click="mapSettingVisible = true"
                      >
                        {{ $t('marketing.commons.xscryspz_463225') }}
                      </fx-button>
                    </div>

                    <div v-if="plugin.id === 'mudu'">
                      <el-button
                        type="text"
                        @click="handleShowMuduSettingDialog"
                      >
                        {{ $t('marketing.commons.djsz_4fa09e') }}
                      </el-button>
                    </div>

                    <div v-if="plugin.liveIsLead">
                      <fx-button
                        type="text"
                        @click="setPolyv"
                      >
                        {{ $t('marketing.commons.djsz_4fa09e') }}
                      </fx-button>
                      <!-- <span class="space">|</span>
                      <fx-button type="text" @click="toPolyv"
                        >注册保利威直播账号</fx-button
                      > -->
                    </div>

                    <div v-if="plugin.liveIsLeadXiaoe">
                      <fx-button
                        type="text"
                        @click="setXiaoe"
                      >
                        {{ $t('marketing.commons.djsz_4fa09e') }}
                      </fx-button>
                      <span class="space">|</span>
                      <fx-button
                        type="text"
                        @click="xiaoeMapSettingVisible = true"
                      >
                        {{ $t('marketing.commons.xscryspz_463225') }}
                      </fx-button>
                    </div>

                    <div v-if="plugin.liveIsLeadVideo">
                      <fx-button
                        type="text"
                        @click="setWechatVideo"
                      >
                        {{ $t('marketing.commons.djsz_4fa09e') }}
                      </fx-button>
                      <!-- <span class="space">|</span>
                      <fx-button type="text" @click="toVhall"
                        >注册微吼直播账号</fx-button
                      > -->
                    </div>

                    <div v-if="plugin.adTencent && plugin.model_switch">
                      <fx-button
                        type="text"
                        @click="toSettingTA"
                      >
                        {{ $t('marketing.commons.ggsz_290262') }}
                      </fx-button>
                    </div>
                    <div v-if="plugin.adBytedance && plugin.model_switch">
                      <fx-button
                        type="text"
                        @click="toSettingBytedance"
                      >
                        {{ $t('marketing.commons.ggsz_290262') }}
                      </fx-button>
                    </div>
                    <div v-if="plugin.adBaidu && plugin.model_switch">
                      <fx-button
                        type="text"
                        @click="toSettingBaidu"
                      >
                        {{ $t('marketing.commons.ggsz_290262') }}
                      </fx-button>
                    </div>
                    <div
                      v-if="((plugin.zhiHuClue ||
                        plugin.soGouClue ||
                        plugin.KuaiShouClue ||
                        plugin.shenMaClue ||
                        plugin.faceBookClue ||
                        plugin.linKeDinClue ||
                        plugin.bindGoogleAdEnable ||
                        plugin.xiaohongshuClue ||
                        plugin.douyingLifeClue) &&
                        plugin.model_switch)
                      "
                    >
                      <fx-button
                        type="text"
                        @click="handleClueSetting(plugin)"
                      >
                        {{ $t('marketing.commons.qwpz_c70a9b') }}
                      </fx-button>
                    </div>
                    <div v-if="plugin.DianJingClue">
                      <fx-button
                        type="text"
                        @click="handle360ClueSetting"
                      >
                        {{ $t('marketing.pages.setting.qwsc_d72800') }}
                      </fx-button>
                    </div>
                    <div v-if="plugin.id === 'knowledge-database'">
                      <fx-button
                        type="text"
                        @click="isShowKnowledgeDatabaseDialog = true"
                      >
                        {{ $t('marketing.commons.sz_e366cc') }}
                      </fx-button>
                    </div>
                    <div v-if="plugin.id === 'wallet'">
                      <fx-button
                        type="text"
                        @click="handleWalletSetting"
                      >
                        {{ $t('marketing.pages.setting.pzskzh_aa5a07') }}
                      </fx-button>
                    </div>
                    <div v-if="plugin.adDataback">
                      <fx-button
                        type="text"
                        @click="setToken"
                      >
                        {{ $t('marketing.commons.qwpz_c70a9b') }}
                      </fx-button>
                    </div>
                    <div v-if="plugin.mailchimpPluginEnable">
                      <fx-button
                        type="text"
                        @click="handleClueSetting(plugin)"
                      >
                        {{ $t('marketing.commons.qwpz_c70a9b') }}
                      </fx-button>
                    </div>
                    <div v-if="plugin.id === 'picture'">
                      <fx-button
                        type="text"
                        @click="handlePictureSetting"
                      >
                        {{ $t('marketing.commons.zssz_276ff7') }}
                      </fx-button>
                    </div>
                    <div v-if="plugin.isQywxKnowledge && plugin.model_switch">
                      <fx-button
                        type="text"
                        @click="toSettingQywxKnowledge"
                      >
                        {{ $t('marketing.commons.qwpz_c70a9b') }}
                      </fx-button>
                    </div>
                    <div v-if="plugin.id === 'external-qrcode'">
                      <fx-button
                        type="text"
                        @click="handleExternalQrcodeSetting"
                      >
                        {{ $t('marketing.pages.setting.djpz_842704') }}
                      </fx-button>
                    </div>
                    <div v-if="plugin.id === 'fw-service'">
                      <fx-button
                        type="text"
                        @click="openOnlineServiceHelp"
                      >
                        {{ $t('marketing.commons.djsz_4fa09e') }}
                      </fx-button>
                      <span class="space">|</span>
                      <fx-button
                        type="text"
                        @click="openOnlineService"
                      >
                        {{ $t('marketing.commons.qwpz_c70a9b') }}
                      </fx-button>
                    </div>
                    <div v-if="plugin.id === 'vhall'">
                      <fx-button
                        type="text"
                        @click="openVHallSetting"
                      >
                        {{ $t('marketing.commons.djsz_4fa09e') }}
                      </fx-button>
                      <span class="space">|</span>
                      <fx-button
                        type="text"
                        @click="handleCrmSavingSetting"
                      >
                        {{ $t('marketing.commons.xscryspz_463225') }}
                      </fx-button>
                    </div>
                    <div v-if="plugin.id === 'whatsapp'">
                      <fx-button
                        type="text"
                        @click="whatsAppSettingVisible = true"
                      >
                        {{ $t('marketing.commons.djsz_4fa09e') }}
                      </fx-button>
                    </div>
                    <div v-if="plugin.id === 'employeeMarketing'">
                      <fx-button
                        type="text"
                        @click="employeeAddressDialogVisible = true"
                      >
                        {{ $t('marketing.pages.setting.txlsz_69314d')}}
                      </fx-button>
                    </div>

                    <div v-if="plugin.id === 'xiaobing'">
                      <fx-button
                        type="text"
                        @click="xiaobingSettingVisible = true"
                      >
                        {{ $t('marketing.commons.djsz_4fa09e') }}
                      </fx-button>
                    </div>
                    <div v-if="plugin.id === 'marketingDataIsolation'">
                      <fx-button
                        type="text"
                        @click="openLink('https://help.fxiaoke.com/93d5/5540/f03c')"
                      >
                        {{ $t('marketing.commons.ljgd_06a3c1') }}
                      </fx-button>
                    </div>
                    <div
                      v-if="[30,60,66,201,204,205,207,208,209,210,203].includes(plugin.pluginType)"
                      style="display: flex;font-size: 14px;"
                    >
                      <div
                        v-for="(setting) in plugin.pluginSettings"
                        v-show="!setting.hide"
                        :key="setting.text"
                      >
                        <fx-button
                          type="text"
                          @click="handleRouteTo(setting.routeTo)"
                        >
                          {{ setting.text }}
                        </fx-button>
                        <span
                          v-show="setting.showSpace"
                          style="margin-right: 2px;"
                          class="space"
                        >|</span>
                      </div>
                    </div>
                     <div v-if="plugin.id === 'smsMarketing' && isUseThirdSmsPlatform">
                      <fx-button
                        type="text"
                        @click="verifySmsTemplateDialogVisible = true"
                      >
                        {{ $t('marketing.commons.sz_e366cc') }}
                      </fx-button>
                    </div>
                  </div>
                  <ElSwitch
                    v-show="!plugin.hideSwitch"
                    v-model="plugin.model_switch"
                    size="small"
                    :disabled="plugin.disabled || (plugin.lockOnEnable && plugin.model_switch)"
                    @change="handleSwitchChange(plugin)"
                  />
                </div>
              </div>
            </SmartLayout>
          </div>
        </div>
      </div>
    </div>
    <VDialog
      :visible.sync="joinSettingVisible"
      :title="$t('marketing.pages.setting.kfdjys_60ecca')"
      width="1400px"
      :show-confirm="false"
      @onClose="joinSettingVisible = false"
    >
      <div class="interface">
        <div class="header">
          {{ $t('marketing.pages.setting.zpzkftsxxq_9c98d0') }}
          <fx-button
            type="text"
            @click="mapSettingVisible = true"
          >
            {{ $t('marketing.pages.setting.ckys_402dc4') }}
          </fx-button>
        </div>
        <div class="body">
          <div class="left">
            <div class="title">
              {{ $t('marketing.commons.pzbz_1fc4c1') }}
            </div>
            <div class="steps">
              <div class="step">
                1）{{ $t('marketing.pages.setting.gwkfcjmmzz_02d06d')
                }}<fx-button
                  type="text"
                  @click="toFxiaokeHelp"
                >
                  {{ $t('marketing.pages.setting.gw_fad6d8') }}，
                </fx-button>{{ $t('marketing.pages.setting.ckkf_4f60e9')
                }}<fx-button
                  type="text"
                  style="margin-left: 0"
                  @click="toEiisys"
                >
                  {{ $t('marketing.pages.setting.mmsm_e1ed31') }}
                </fx-button>
                <span class="code">
                  {{ eiisysCode }}
                  <fx-button
                    type="text"
                    class="text"
                    @click="copy(eiisysCode)"
                  >{{ $t('marketing.commons.fz_79d3ab')
                  }}</fx-button>
                </span>
              </div>
              <div class="step">
                2）{{ $t('marketing.pages.setting.dlglhtjrkf_4bfcd2') }}
              </div>
              <div class="step">
                3）{{ $t('marketing.pages.setting.qhldtspzkq_160cf6') }}
              </div>
              <div class="step">
                4）{{ $t('marketing.pages.setting.pzfwqdz_c7bbed') }}{{ serviceUrl }}
                <fx-button
                  type="text"
                  @click="copy(serviceUrl)"
                >
                  {{ $t('marketing.commons.fz_79d3ab') }}
                </fx-button>
              </div>
              <div class="step">
                5）{{ $t('marketing.pages.setting.lp_def441') }} {{ token }}
                <fx-button
                  type="text"
                  @click="copy(token)"
                >
                  {{ $t('marketing.commons.fz_79d3ab') }}
                </fx-button>
              </div>
              <div class="step">
                6）{{ $t('marketing.pages.setting.xzltjlztts_778390') }}
              </div>
              <div class="step">
                7）{{ $t('marketing.pages.setting.zkfxzmpsck_304f51') }}
                <fx-button
                  type="text"
                  @click="openLook"
                >
                  {{ $t('marketing.pages.setting.tzck_d6ecf0') }}
                </fx-button>
              </div>
            </div>
          </div>
          <div class="right">
            <img
              src="@/assets/images/53kf.png"
              width="100%"
              alt=""
            >
          </div>
        </div>
      </div>
    </VDialog>
    <VDialog
      :visible.sync="polyvSettingVisible"
      :title="$t('marketing.pages.setting.blwzbpz_be9a3d')"
      width="1200px"
      :ok-text="$t('marketing.commons.bc_be5fbb')"
      @onClose="polyvSettingVisible = false"
      @onSubmit="setPolyvNews"
    >
      <div class="interface">
        <div class="header">
          {{ $t('marketing.pages.setting.zpzqqxqbnd_953052') }}
          <fx-button
            type="text"
            @click="toPolyv"
          >
            {{ $t('marketing.pages.setting.zcblwzb_3efca8') }}
          </fx-button>
        </div>
        <div class="body">
          <div class="polyvLeft">
            <img
              src="@/assets/images/polyvBg.png"
              width="100%"
              alt=""
            >
          </div>
          <div class="polyvRight">
            <div class="title">
              {{ $t('marketing.commons.pzbz_1fc4c1') }}
            </div>
            <div class="steps">
              <div class="step">
                {{ $t('marketing.pages.setting.dlblwzbhtf_d7d15f') }}
              </div>
            </div>
            <div class="form">
              <ElForm
                ref="polyvForm"
                label-position="left"
                label-width="180px"
                :model="ployvData"
                :rules="ployvDataRules"
              >
                <ElFormItem
                  :label="$t('marketing.pages.setting.zh_09fb52')"
                  prop="userId"
                >
                  <ElInput
                    v-model="ployvData.userId"
                    :placeholder="$t('marketing.commons.qsr_32f0a5')"
                  />
                </ElFormItem>
                <ElFormItem
                  :label="$t('marketing.pages.setting.yy_73408e')"
                  prop="appId"
                >
                  <ElInput
                    v-model="ployvData.appId"
                    :placeholder="$t('marketing.commons.qsr_e4907b')"
                  />
                </ElFormItem>
                <ElFormItem
                  :label="$t('marketing.pages.setting.yymy_cfffae')"
                  prop="appSecret"
                >
                  <ElInput
                    v-model="ployvData.appSecret"
                    :placeholder="$t('marketing.pages.setting.qsr_a9a47f')"
                  />
                </ElFormItem>
              </ElForm>
            </div>
          </div>
        </div>
      </div>
    </VDialog>
    <VDialog
      :visible.sync="xiaoeSettingVisible"
      :title="$t('marketing.pages.setting.xetzbpz_08c66f')"
      width="1000px"
      :ok-text="$t('marketing.commons.bc_be5fbb')"
      @onClose="xiaoeSettingVisible = false"
      @onSubmit="setXiaoeNews"
    >
      <div class="step">
        <div class="step-item">
          <div class="title">
            {{ $t('marketing.pages.setting.dyb_5952cd') }}
          </div>
          <div class="content">
            <fx-button
              type="text"
              @click="toXiaoe"
            >
              {{ $t('marketing.pages.setting.zcxetzb_72e2a0') }}
            </fx-button>，
            {{ $t('marketing.pages.setting.cjbsqyycjf_7a22ca')
            }}<fx-button
              type="text"
              @click="openGuide"
            >
              {{ $t('marketing.pages.setting.kfzncjbsqy_0b7a2e') }}
            </fx-button>
          </div>
        </div>
        <div class="step-item">
          <div class="title">
            {{ $t('marketing.pages.setting.deb_967e36') }}
          </div>
          <div class="content">
            <span>{{ $t('marketing.pages.setting.cjyyhdlxet_fdb63a') }}
              <fx-button
                type="text"
                @click="openConsole"
              >{{ $t('marketing.commons.yfwkzt_30155c') }}</fx-button>
              {{ $t('marketing.pages.setting.hqyyxxljxe_969f25') }}
            </span>
            <div style="display: flex; align-items: end">
              <div
                class="ip-adress"
                v-html="htmlIds"
              />
              <fx-button
                type="primary"
                style="margin-left: 20px"
                @click="copyIds"
              >
                {{ $t('marketing.pages.setting.yjfz_2a5ed5') }}
              </fx-button>
            </div>
          </div>
        </div>
        <div class="step-item">
          <div class="title">
            {{ $t('marketing.pages.setting.dsb_7ea661') }}
          </div>
          <div class="content">
            <span>{{ $t('marketing.commons.dlxet_0b0993') }}
              <fx-button
                type="text"
                @click="openConsole"
              >{{ $t('marketing.commons.yfwkzt_30155c') }}</fx-button>
              {{ $t('marketing.pages.setting.jrdyyyglym_eaffb6') }}
            </span>
            <div class="pic">
              <img
                style="cursor: pointer"
                src="@/assets/images/xiaoeThird.png"
                width="440"
                alt=""
                @click="showViewer(0)"
              >
            </div>
            <ul class="text">
              <li class="text-item">
                {{ $t('marketing.pages.setting.yhgl_7d94de') }}-{{ $t('marketing.pages.setting.bqcz_c2821e') }}
              </li>
              <li class="text-item">
                {{ $t('marketing.pages.setting.ddgl_afcd11') }}-{{ $t('marketing.pages.setting.cxddxx_3acda9') }}
              </li>
              <li class="text-item">
                {{ $t('marketing.pages.setting.zbgl_9bd3cd') }}-{{ $t('marketing.pages.setting.cxzbxx_a6ff44') }}
              </li>
              <li class="text-item">
                {{ $t('marketing.pages.setting.xxsj_ded76c') }}-{{ $t('marketing.pages.setting.cxxxjl_5a0915') }}
              </li>
              <li class="text-item">
                {{ $t('marketing.pages.setting.yhgl_7d94de') }}-{{ $t('marketing.pages.setting.cxyhbq_a271b2') }}
              </li>
              <li class="text-item">
                {{ $t('marketing.pages.setting.yhgl_7d94de') }}-{{ $t('marketing.pages.setting.cxyhxx_d0976b') }}
              </li>
              <li class="text-item">
                {{ $t('marketing.pages.setting.yhgl_7d94de') }}-{{ $t('marketing.pages.setting.yhcz_b28ac7') }}
              </li>
            </ul>
          </div>
        </div>
        <div class="step-item">
          <div class="title">
            {{ $t('marketing.pages.setting.dsb_e92b68') }}
          </div>
          <div class="content">
            <span>{{ $t('marketing.commons.dlxet_0b0993') }}
              <fx-button
                type="text"
                @click="openConsole"
              >{{ $t('marketing.commons.yfwkzt_30155c') }}</fx-button>
              {{ $t('marketing.pages.setting.xgszxxbtxy_64c63c') }}
            </span>
            <div class="body">
              <div class="left">
                <img
                  style="cursor: pointer"
                  src="@/assets/images/xiaoeFourth.jpg"
                  width="100%"
                  alt=""
                  @click="showViewer(1)"
                >
              </div>
              <div class="right">
                <div class="form">
                  <ElForm
                    ref="xiaoeForm"
                    label-position="left"
                    label-width="180px"
                    :model="xiaoeData"
                    :rules="xiaoeDataRules"
                  >
                    <ElFormItem
                      :label="$t('marketing.pages.setting.sqdp_b38245')"
                      prop="appId"
                    >
                      <ElInput
                        v-model="xiaoeData.appId"
                        :placeholder="$t('marketing.pages.setting.qsr_60dc5c')"
                      />
                    </ElFormItem>
                    <ElFormItem
                      :label="$t('marketing.pages.setting.yybs_8fb1b9')"
                      prop="app_id"
                    >
                      <ElInput
                        v-model="xiaoeData.app_id"
                        :placeholder="$t('marketing.commons.qsr_dae6dc')"
                      />
                    </ElFormItem>
                    <ElFormItem
                      :label="$t('marketing.pages.setting.yymy_a7d3d6')"
                      prop="appSecret"
                    >
                      <ElInput
                        v-model="xiaoeData.appSecret"
                        :placeholder="$t('marketing.pages.setting.qsr_737ca6')"
                      />
                    </ElFormItem>
                  </ElForm>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </VDialog>
    <v-customer-service-dialog :visible.sync="isShowCustomerServiceDialog" />
    <el-dialog
      width="1000px"
      append-to-body
      :visible.sync="isShowUpdateToProTips"
    >
      <div class="MarketingPlugin__pro-tips-dialog">
        <span class="tips-title">{{ $t('marketing.pages.setting.ndqsydsyxt_c2f011') }}</span>
        <span class="tips-desc">{{ $t('marketing.pages.setting.qlxndzskfs_fd3aea') }}400-1122-788</span>
        <fx-button
          class="tips-button"
          type="primary"
          @click="isShowUpdateToProTips = false"
        >
          {{ $t('marketing.commons.wzdl_fe0337') }}
        </fx-button>
        <el-image
          class="tips-bg"
          :src="proTipsImg"
        />
      </div>
    </el-dialog>
    <crm-mapping-kf
      v-if="mapSettingVisible"
      :title="$t('marketing.pages.setting.kfxsjryspz_59cea5')"
      :visible.sync="mapSettingVisible"
    />
    <CrmMappingXiaoe
      v-if="xiaoeMapSettingVisible"
      :title="$t('marketing.pages.setting.xetxsjrysp_df1174')"
      :visible.sync="xiaoeMapSettingVisible"
    />
    <CrmMappingVHall
      v-if="vhallMapSettingVisible"
      :title="$t('marketing.pages.setting.whxsjryspz_9bb0c1')"
      :visible.sync="vhallMapSettingVisible"
    />
    <ImageViewer
      v-show="isShowImageViewer"
      :on-close="handleViewerClose"
      :url-list="previewImageUrls"
      :z-index="9999"
    />
    <VDialog
      :visible.sync="pictureVisible"
      :title="$t('marketing.pages.setting.tpfzzssz_1134f4')"
      width="520px"
      @onClose="pictureVisible = false"
      @onSubmit="mobilePhotoGroupSetting"
    >
      <div
        class="picture-header"
        style="margin-bottom:14px"
      >
        {{ $t('marketing.pages.setting.yxxzdtpfzj_3a3f95') }}
      </div>
      <div class="picture-body">
        <GroupTree
          :object-type="7"
          show-checkbox
          check-strictly
          :hide-group-ids="['-2', '-3']"
          :extra-params="groupExtraParams"
          :active-group-id="''"
          :default-checked-keys="defaultCheckedKeys"
          @check-change="handleCheckChange"
        />
      </div>
    </VDialog>
    <VDialog
      :visible.sync="externalQrcodeVisible"
      :loading="externalQrcodeSaving"
      :title="$t('marketing.pages.setting.wbxtdjpz_e59654')"
      width="1000px"
      class="external-qrcode-setting"
      @onClose="externalQrcodeVisible = false"
      @onSubmit="handleExternalQrcodeSave"
    >
      <div class="external-qrcode-setting__tip">
        {{ $t('marketing.pages.setting.ztlchdzpzw_9051e9') }}
        > {{ $t('marketing.pages.setting.ygtgxgnr_f6dee6') }}
        > {{ $t('marketing.pages.setting.khfwwbnr_4957a8') }}
        > {{ $t('marketing.pages.setting.xthcwbtgsj_5c1f8d') }}
        > {{ $t('marketing.pages.setting.cktgzhxq_e16deb') }}
      </div>
      <div class="external-qrcode-setting__body">
        <div class="external-qrcode-setting__step">
          <div class="external-qrcode-setting__step-title">
            {{ $t('marketing.pages.setting.dybewmscjk_2ed172') }}
          </div>
          <div class="external-qrcode-setting__step-memo">
            <!-- <div class="external-qrcode-setting__step-subtitle">外部二维码</div> -->
            <div class="external-qrcode-setting__step-content">
              {{ $t('marketing.pages.setting.zzzhdhbszc_292592') }}
            </div>
            <div class="external-qrcode-setting__step-form">
              <div class="external-qrcode-setting__step-label">
                <i>*</i>{{ $t('marketing.pages.setting.ewmscdz_c5b781') }}
              </div>
              <ElInput
                v-model="externalDomainNameUrl"
                class="external-qrcode-setting__step-input"
                placeholder="http://fxiaoke.com..."
                size="small"
              />
            </div>
          </div>
        </div>
        <div class="external-qrcode-setting__step">
          <div class="external-qrcode-setting__step-title">
            {{ $t('marketing.pages.setting.debsjhcys_0708cc') }}
          </div>
          <div class="external-qrcode-setting__step-memo">
            <div class="external-qrcode-setting__step-content">
              {{ $t('marketing.pages.setting.fbxxyjqkfn_f8fc39') }}
            </div>
            <a
              class="external-qrcode-setting__step-link"
              href="https://help.fxiaoke.com/93d5/0f81/935b"
              target="_blank"
            >{{ $t('marketing.pages.setting.cksjhcjkzy_00f2b8') }}</a>
          </div>
        </div>
      </div>
      <div
        class="external-qrcode-setting__step-memo"
        style="padding: 0 30px 0 50px"
      >
        <div>{{ $t('marketing.commons.wxts_947d98') }}</div>
        <div>
          {{ $t('marketing.pages.setting.zhdztgwbnr_dfe4e3') }}
        </div>
      </div>
    </VDialog>

    <mudu-setting-dialog
      :visible="showMuduSettingDialog"
      :submit-callback="muduSettingCallback"
      @onClose="handleHideMuduSettingDialog"
    />
    <KnowledgeDatabaseDialog :visible.sync="isShowKnowledgeDatabaseDialog" />
    <VHallSettingDialog
      :vhall-data="vhallData"
      :visible.sync="vHallSettingVisible"
    />
    <WhatsappSettingDialog
      :visible="whatsAppSettingVisible"
      @onClose="whatsAppSettingVisible = false"
    />
    <!--  -->
    <employeeAddressDialog
      :visible="employeeAddressDialogVisible"
      @onClose="employeeAddressDialogVisible = false"
    />
    <VerifySmsTemplateDialog
      :visible="verifySmsTemplateDialogVisible"
      @onClose="verifySmsTemplateDialogVisible = false"
    />
    <XiaobingSettingDialog
      :visible="xiaobingSettingVisible"
      @onClose="xiaobingSettingVisible = false"
    />
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex'
import QuestionTooltip from '@/components/questionTooltip/index.vue'
import kisvData from '@/modules/kisv-data.js'
import { confirm } from '@/utils/globals.js'
import { setMenuById } from '@/modules/menu/menu.js'
import VDialog from '@/components/dialog/index.vue'
import CrmMappingKf from '../components/crm-mapping-kf.vue'
import CrmMappingXiaoe from '../components/crm-mapping-xiaoe.vue'
import KnowledgeDatabaseDialog from '../components/knowledge-database-dialog.vue'
import http from '@/services/http/index.js'
import { marketingPluginMap } from './plugin-map.js'
import { messageBoxConfirm } from '@/utils/message-box.js'
import VCustomerServiceDialog from '@/components/CustomerServiceDialog/index.vue'
import { redirectToFS } from '@/utils/index.js'
import MuduSettingDialog from './mudu-setting-dialog.vue'
import CrmMappingVHall from '../components/crm-mapping-vhall.vue'
import VHallSettingDialog from '../components/vhall-setting-dialog.vue'

import GroupTree from '@/components/group-manage-new/group-tree.vue'
import SmartLayout from '@/components/SmartLayout/index.vue'
import WhatsappSettingDialog from './whatsapp-setting-dialog.vue'
import employeeAddressDialog from './employeeAddressDialog.vue'
import XiaobingSettingDialog from './xiaobing-setting-dialog.vue'
import VerifySmsTemplateDialog from './verify-sms-template-dialog.vue'

const SERVICE_URL = `${FS.BASE_PATH}/marketing/websiteThirdCustomer/kfCallback`
const TOKEN = 'b252fb0a-8cc0-42c5-aa20-440336cde3b8'

const picShow = [
  require('@/assets/images/xiaoeThird.png'),
  require('@/assets/images/xiaoeFourth.jpg'),
]

const text = `
  **************;
  **************;
  ***************;
  *************;
  *************;
  *************;
  ***************;
  ************;
  ************;
  ***************;
  ***************;
  **************;
  ***************;
  **************;
  ***************;
  **************;
  **************;
`

export default {
  components: {
    ElSwitch: FxUI.Switch,
    ElButton: FxUI.Button,
    ElDialog: FxUI.Dialog,
    ElInput: FxUI.Input,
    ElForm: FxUI.Form,
    ElFormItem: FxUI.FormItem,
    ElImage: FxUI.Image,
    ImageViewer: FxUI.Image.extends.components.ImageViewer,
    QuestionTooltip,
    VDialog,
    CrmMappingKf,
    VCustomerServiceDialog,
    CrmMappingXiaoe,
    MuduSettingDialog,
    KnowledgeDatabaseDialog,
    GroupTree,
    VHallSettingDialog,
    CrmMappingVHall,
    SmartLayout,
    WhatsappSettingDialog,
    employeeAddressDialog,
    XiaobingSettingDialog,
    VerifySmsTemplateDialog
  },
  props: {
    ea: {
      type: String,
      default: '',
    },
  },
  data() {
    const htmlIds = text.replace(/\s/g, '').replace(/;/g, '<br />')
    return {
      vDatas: kisvData.datas,
      isPro: false,
      marketingPluginMap,
      joinSettingVisible: false,
      mapSettingVisible: false,
      token: TOKEN,
      polyvSettingVisible: false,
      isShowUpdateToProTips: false,
      isShowCustomerServiceDialog: false,
      ployvData: {
        userId: '',
        appId: '',
        appSecret: '',
      },
      ployvDataRules: {
        userId: [{ required: true, message: $t('marketing.commons.qsr_32f0a5'), trigger: 'blur' }],
        appId: [{ required: true, message: $t('marketing.commons.qsr_124f96'), trigger: 'blur' }],
        appSecret: [{ required: true, message: $t('marketing.commons.qsr_c3744f'), trigger: 'blur' }],
      },
      xiaoeSettingVisible: false,
      xiaoeMapSettingVisible: false,
      xiaoeData: {
        app_id: '',
        appId: '',
        appSecret: '',
      },
      isBindXiaoe: false,
      xiaoeDataRules: {
        app_id: [{ required: true, message: $t('marketing.commons.qsr_dae6dc'), trigger: 'blur' }],
        appId: [{ required: true, message: $t('marketing.commons.qsr_124f96'), trigger: 'blur' }],
        appSecret: [{ required: true, message: $t('marketing.commons.qsr_c3744f'), trigger: 'blur' }],
      },
      isBindPolyv: false,
      videoIsBind: false,
      hasMarketingIntegration: false,
      proTipsImg: require('@/assets/images/pro-version-tip.png'),
      isShowImageViewer: false,
      previewImageUrls: [],
      appType: '',
      htmlIds,
      pictureVisible: false,
      pictureData: [],
      pictureValue: [],
      externalQrcodeVisible: false,
      externalQrcodeSaving: false,
      externalDomainNameUrl: '',
      enterCouponEaList: false, // 优惠券插件是否在灰度企业内
      // 53code
      eiisysCode: 'window.onload = function(){ var _53code = document.createElement("script");_53code.src = "https://tb.53kf.com/code/code/当前53快服ID/1?u_cust_id=" + window.FsYxt.getYxtCookie ("fsWebsiteUid") + "&u_cust_name=访客" + window.FsYxt.getYxtCookie("fsWebsiteUid").substr(-6);var s = document.getElementsByTagName("script")[0];s.parentNode.insertBefore(_53code, s);}',
      enterCustomerServiceEaList: false, // 在线客服插件是否在灰度企业内
      showMuduSettingDialog: false,
      muduSettingCallback: null,
      isShowKnowledgeDatabaseDialog: false,
      defaultCheckedKeys: [],
      vHallSettingVisible: false,
      whatsAppSettingVisible: false,
      xiaobingSettingVisible: false,
      vhallData: {
        appId: '',
        appSecret: '',
      },
      vhallMapSettingVisible: false,
      employeeAddressDialogVisible: false,
      verifySmsTemplateDialogVisible: false
    }
  },
  computed: {
    ...mapState('AdMarketing/Tencent', ['accountData', 'hasActive']),
    ...mapState('WechatVideo', ['accountList']),
    serviceUrl() {
      return `${SERVICE_URL}?ea=${this.ea}`
    },
    groupExtraParams() {
      return {
        source: 2,
      }
    },
    // 第三方短信是否开通
    isUseThirdSmsPlatform() {
      return this.$store.state.SmsMarketing.isUseThirdSmsPlatform;
    },
  },
  watch: {
    'vDatas.uinfo.version': {
      handler(newValue) {
        /**
         * 专业和加强版，包括钉钉和营销通
         */
        if (/marketing_strategy_(pro|strengthen)_.*/.test(newValue)) {
          this.isPro = true
        } else {
          this.isPro = false
        }
      },
      immediate: true,
    },
  },
  mounted() {
    this.validationBindPolyv()
    this.validateMarketingIntegration()
    this.initStatus()
    this.autoOpenDialog()
  },
  created() {
    this.queryAccountInfo()
    this.getXiaoetongAccount()
    this.getVHallAccount()
    this.getBoundMiniappInfo()
    this.checkOutQrCodeIsSetting()
    this.queryEnterCouponEaList()
    this.queryWechatVideoAccount()
  },
  methods: {
    ...mapActions('AdMarketing/Tencent', ['queryAccountInfo']),
    ...mapActions('WechatVideo', ['queryWechatVideoAccount']),
    showSwitchTips(plugin) {
      const mainText = plugin.switchTips[plugin.model_switch ? 'turnEnable' : 'turnDisable']
        ?.main || ''
      const descText = plugin.switchTips[plugin.model_switch ? 'turnEnable' : 'turnDisable']
        ?.desc || ''
      return messageBoxConfirm({
        mainText,
        descText,
        boxConfig: {
          confirmButtonText: $t('marketing.commons.qd_38cf16'),
          cancelButtonText: $t('marketing.commons.qx_625fb2'),
        },
      })
    },
    checkVersion() {
      return new Promise((resolve, reject) => {
        if (this.vDatas.uinfo.version !== 'marketing_strategy_pro_app') {
          confirm(
            $t('marketing.pages.setting.nzzsyyxtbz_0f6f0b'),
            $t('marketing.commons.ts_02d981'),
            {
              confirmButtonText: $t('marketing.commons.qd_38cf16'),
            },
          )
            .then(() => {
              resolve(false)
            })
            .catch(() => {
              resolve(false)
            })
        } else {
          resolve(true)
        }
      })
    },
    updateToPro() {
      this.isShowUpdateToProTips = true
    },
    applyForBeta() {
      this.isShowCustomerServiceDialog = true
    },
    async checkWechatPayIsOpened() {
      const { Value } = await http.queryISVInfoListForWeb()
      if (Value.errorCode === 0) {
        return (Value.result || []).some(item => item.type === 1 && item.authStatus === 2)
      }
      return false
    },
    handleExternalQrcodeSave() {
      if (!this.externalDomainNameUrl) {
        FxUI.Message.warning($t('marketing.pages.setting.qsrewmscdz_94038f'))
        return
      }
      const reg = /^(https|http)?:\/\//i
      if (!reg.test(this.externalDomainNameUrl)) {
        FxUI.Message.warning($t('marketing.pages.setting.qsrzqddz_bb053d'))
        return
      }
      this.externalQrcodeSaving = true
      http
        .saveOrUpdateOutQrCode({
          domainNameUrl: this.externalDomainNameUrl,
        })
        .then(({ errCode, errMsg }) => {
          this.externalQrcodeSaving = false
          if (errCode === 0) {
            this.externalQrcodeVisible = false
          } else {
            FxUI.Message.error(errMsg || $t('marketing.commons.bcsb_6de920'))
          }
        })
    },
    async checkOutQrCodeIsSetting() {
      const { errCode, data } = await http.queryOutQrCodeSetting()
      if (errCode === 0 && data && data.domainNameUrl) {
        this.externalDomainNameUrl = data.domainNameUrl
        return true
      }
      return false
    },
    async checkMuduIsSetting() {
      try {
        const { errCode, data } = await http.getMuduAccount()
        if (errCode === 0 && data && data.appId) {
          return true
        }
      } catch (error) { /* empty */ }

      return false
    },

    async handleSwitchChange(plugin) {
      const { pluginType, model_switch: status } = plugin
      // 保利威、目睹、小鹅通、微吼开启先判断直播营销有没有打开
      const livePlatforms = [5, 504, 501, 502]
      const adsPlatforms = [30, 31, 32, 34, 59]
      if (livePlatforms.includes(pluginType) && status) {
        const livingMarketingStatus = this.getSinglePluginstatus('scenarioApplication', 202)
        if (!livingMarketingStatus) {
          FxUI.Message.warning($t('marketing.pages.setting.qxkqsfyxcj_d91e0f'))
          plugin.model_switch = false
          return
        }
      }
      // 百度、腾讯、头条、广告回传、Google开启先判断广告营销有没有打开
      if (adsPlatforms.includes(pluginType) && status) {
        const adsMarketingStatus = this.getSinglePluginstatus('scenarioApplication', 203)
        if (!adsMarketingStatus) {
          FxUI.Message.warning($t('marketing.pages.setting.qxkqsfyxcj_4f2d0e'))
          plugin.model_switch = false
          return
        }
      }
      // 企业钱包开启条件
      if (pluginType === 33 && plugin.model_switch) {
        const isWechatPayOpened = await this.checkWechatPayIsOpened()
        // 未配置微信支付配置则先进行配置
        if (!isWechatPayOpened) {
          FxUI.MessageBox.confirm($t('marketing.pages.setting.nhwpzskzhs_217201'), $t('marketing.commons.ts_02d981'), {
            type: 'info',
          }).then(() => {
            this.handleWalletSetting()
          })
          return
        }
      }
      // 开启外部推广码条件设置
      if (pluginType === 51 && plugin.model_switch) {
        const isOutQrCodeIsSetting = await this.checkOutQrCodeIsSetting()
        if (!isOutQrCodeIsSetting) {
          FxUI.MessageBox.confirm($t('marketing.pages.setting.nhwcdjszsf_617b67'), $t('marketing.commons.ts_02d981'), {
            type: 'info',
          }).then(() => {
            this.handleExternalQrcodeSetting()
          })
          return
        }
      }
      if (pluginType === 5 && !this.isBindPolyv) {
        FxUI.Message.warning($t('marketing.pages.setting.qxpzblwzb_9a6bfc'))
        plugin.model_switch = false
        this.polyvSettingVisible = true
        return
      }
      if (pluginType === 31) {
        if (status && this.accountData && this.accountData.length === 0) {
          const tencentConfirm = await messageBoxConfirm({
            mainText: $t('marketing.pages.setting.sfkqtxgggn_ce6972'),
          })
          if (!tencentConfirm) {
            plugin.model_switch = false
            return
          }
          this.$router.push({ name: 'ad-tencent' })
          this.syncPluginInfo(pluginType, status, plugin)
          return
        }
        if (!status && this.hasActive) {
          messageBoxConfirm({
            mainText: $t('marketing.pages.setting.dqyqyzdggz_fce18c'),
            boxConfig: {
              showConfirmButton: false,
              cancelButtonText: $t('marketing.commons.wzdl_fe0337'),
            },
          })
          plugin.model_switch = true
          return
        }
      }
      if (pluginType === 501 && !this.isBindXiaoe) {
        FxUI.Message.warning($t('marketing.pages.setting.qxpzxetzb_c65c03'))
        plugin.model_switch = false
        this.xiaoeSettingVisible = true
        return
      }
      if (pluginType === 503 && !(this.accountList && this.accountList.length)) {
        FxUI.Message.warning($t('marketing.pages.setting.qxpzwxsph_b0bc3f'))
        plugin.model_switch = false
        return
      }

      // 查询新加的插件是否可以开启（购买订单以及满足相关版本等判断后台都已经处理，所以直接调用接口就可以）
      const checkPlugins = [201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211]
      if (checkPlugins.includes(pluginType) && plugin.model_switch) {
        const res = await http.queryPluginEnableOpen({
          types: [pluginType],
        })
        let isOpen = false
        if (res.errCode === 0 && res.data) {
          const { openConfigs = [] } = res.data
          const pluginConfig = openConfigs.filter(item => item.type === pluginType).length > 0 ? openConfigs.filter(item => item.type === pluginType)[0] : {}
          isOpen = pluginConfig.isOpen
        }
        if (!isOpen) {
          FxUI.Message.warning($t('marketing.pages.setting.qlxkhjlxds_4c400c'))
          plugin.model_switch = false
          return
        }
      }

      // 目睹直播设置
      if (pluginType === 504 && status) {
        const isMuduHasSetting = await this.checkMuduIsSetting()
        if (!isMuduHasSetting) {
          plugin.model_switch = false
          this.showMuduSettingDialog = true
          this.muduSettingCallback = () => {
            plugin.model_switch = true
            this.handleSwitchChange(plugin)
          }

          return
        }
      }

      // whatsapp
      if (pluginType === 61 && status) {
        const whatsappRet = await http.checkWhatsAppPluginOpenCondition()
        if (whatsappRet && whatsappRet.errCode === 0 && whatsappRet.data) {
          if (whatsappRet.data === 1) {
            plugin.model_switch = false
            FxUI.Message.error($t('marketing.commons.qlxkhjlxdy_3a1772'))
            return
          } if (whatsappRet.data === 2) {
            plugin.model_switch = false
            FxUI.Message.error($t('marketing.commons.qxwcdjsz_dbab6b'))
            return
          }
        } else {
          plugin.model_switch = false
          return
        }
      }

      // 线索连接器开关条件：Pro或者有相关插件
      if (plugin.licenseList && plugin.onlyPro && status) {
        if (this.isPro) {
          plugin.model_switch = true
        } else if (this.vDatas.strategy === 'stan') { // 标准版下判断是否有海外营销插件license
          const res = await http.checkLicenseList({
            moduleCodes: plugin.licenseList,
          })
          const { errCode, data = {} } = res
          const statusList = []
          if (errCode === 0 && data.configs) {
            (data.configs || []).forEach(config => {
              statusList.push(config.status)
            })
          }
          if (!statusList.includes(true)) {
            FxUI.Message.warning($t('marketing.pages.setting.qlxkhjlxds_4c400c'))
            plugin.model_switch = false
            return
          }
        } else {
          FxUI.Message.warning($t('marketing.pages.setting.qlxkhjlxds_4c400c'))
          plugin.model_switch = false
          return
        }
      }

      // 营销数据隔离和AI跟版本无关  只有相关插件才能开启
      if ((plugin.pluginType === 63 || plugin.pluginType === 66) && status) {
        const res = await http.checkLicenseList({
          moduleCodes: plugin.licenseList,
        })
        const { errCode, data = {} } = res
        const statusList = []
        if (errCode === 0 && data.configs) {
          (data.configs || []).forEach(config => {
            statusList.push(config.status)
          })
        }
        if (!statusList.includes(true)) {
          FxUI.Message.warning($t('marketing.pages.setting.qlxkhjlxds_4c400c'))
          plugin.model_switch = false
          return
        }
      }

      const res = await this.showSwitchTips(plugin)
      if (res === false) {
        plugin.model_switch = !plugin.model_switch
        return
      }

      plugin.model_switch = status
      this.syncPluginInfo(pluginType, status, plugin)
    },
    async syncPluginInfo(pluginType, status, plugin) {
      await this.saveOrUpdateMarketingPlugin(
        {
          pluginType,
          status,
        },
        plugin,
      )
      // 营销权限需要刷新
      if (pluginType === 63) {
        kisvData.getCurrentEmployee()
      }
      this.updateVDatas(plugin)
    },
    async saveOrUpdateMarketingPlugin(params, plugin) {
      const res = await YXT_ALIAS.http.saveOrUpdateMarketingPlugin(params)
      if (!res || res.errCode !== 0) {
        if (res.errCode === 88070) {
          if ((plugin.pluginType === 502 && this.vhallData.appId) || plugin.pluginType !== 502) {
            FxUI.Message.warning(res.errMsg)
          }
          // 微吼直播判断有没有绑定  使用这个接口
          if (plugin.pluginType === 502 && plugin.model_switch) {
            this.vHallSettingVisible = true
          }
        }
        plugin.model_switch = !plugin.model_switch
        return
      }
      // 开启后打开伙伴营销菜单
      if (plugin.id === 'partner-marketing' && params.status) {
        setMenuById('partner-marketing', { hide: false })
        setMenuById('report-partner', { hide: false })
      }
      // 是否开启腾讯广告菜单
      if (plugin.id === 'ad-tencent') {
        setMenuById('ad-tencent', { hide: !params.status })
      }
      // 是否开启头条广告菜单
      if (plugin.id === 'ad-bytedance') {
        setMenuById('ad-bytedance', { hide: !params.status })
      }
      // 是否开启百度广告菜单
      if (plugin.id === 'ad-baidu') {
        setMenuById('ad-baidu', { hide: !params.status })
      }

      // 是否开启whatsapp菜单
      if (plugin.id === 'whatsapp') {
        setMenuById('whatsappmenu', { hide: !params.status })
      }

      // 是否开启谷歌广告
      if (plugin.id === 'ad-google') {
        setMenuById('ad-google', { hide: !params.status })
      }
      // 微信商家券
      if (plugin.id === 'wechatMerchantCoupon') {
        setMenuById('coupons-template', {
          hide: !params.status,
        })
      }

      // 纷享优惠券
      if (plugin.id === 'sourceCouponEnabled' && params.status) {
        setMenuById('fs-coupons', { hide: false })
      }

      if (plugin.id === 'marketing-sdr') {
        setMenuById('marketing-sdr', { hide: !params.status })
        setMenuById('callcenter', { hide: !params.status })
        setMenuById('clue-workbench', { hide: !params.status })
        setMenuById('sdr-kanban', { hide: !params.status })
      }

      if (plugin.id === 'member-promotion') {
        setMenuById('member-promotion', {
          hide: !params.status,
        })
        setMenuById('member-center', {
          hide: !params.status,
        })
      }

      if ([201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211].includes(plugin.pluginType)) {
        setMenuById(plugin.pluginControlMenu, {
          hide: !params.status,
        })
      }

      FxUI.Message.success(
        $t('marketing.commons.cg_bd6c8c', {
          data: { option0: params.status ? $t('marketing.commons.kq_cc42dd') : $t('marketing.commons.gb_b15d91') },
        }),
      )
    },
    initStatus() {
      Object.values(this.marketingPluginMap)
        .forEach(map => {
          map.plugins.forEach(plugin => {
            /**
             * 兼容直播开启状态放在livePluginConfigs数组中
             */
            const liveConfig = this.vDatas.uinfo.livePluginConfigs[
              plugin.pluginType
            ]
            if (liveConfig) {
              plugin.model_switch = liveConfig.status
            } else if (this.vDatas.pluginInfo && this.vDatas.pluginInfo[plugin.key]) {
              plugin.model_switch = this.vDatas.pluginInfo[plugin.key]
            } else {
              plugin.model_switch = this.vDatas.uinfo[plugin.key]
            }
          })
        })
    },
    updateVDatas(plugin) {
      /**
       * 兼容直播开启状态放在livePluginConfigs数组中
       */
      const liveConfig = this.vDatas.uinfo.livePluginConfigs[plugin.pluginType]
      const pluginConfig = this.vDatas.pluginInfo[plugin.key]
      console.log('pluginConfig: ', pluginConfig)
      if (liveConfig) {
        liveConfig.status = plugin.model_switch
      } else if (pluginConfig !== undefined) {
        this.$set(this.vDatas.pluginInfo, plugin.key, plugin.model_switch)
      } else {
        this.$set(this.vDatas.uinfo, plugin.key, plugin.model_switch)
      }
      console.log('pluginConfigthis.vDatas: ', this.vDatas)
      if ([3, 64, 206].includes(plugin.pluginType)) {
        const hide = !this.vDatas.pluginInfo.memberMarketingPluginEnable && !this.vDatas.uinfo.partnerMarketingEnabled && !this.vDatas.pluginInfo.employeeMarketing
        setMenuById('employee-promotion', { hide })
      }
    },
    copy(value) {
      navigator.clipboard
        .writeText(value)
        .then(res => {
          FxUI.Message.success($t('marketing.commons.fzcg_8a4831'))
        })
        .catch(err => {
          FxUI.Message.error($t('marketing.commons.fzsb_cd9817'))
        })
        .catch(err => {
          FxUI.Message.error($t('marketing.commons.fzsb_cd9817'))
        })
    },
    handleClueSetting(plugin) {
      // if (plugin.marketingSDR) {
      //   redirectToFS('#app/onlineservice/wxworkreport', 'manage')
      //   return
      // }
      redirectToFS('#erpdss/setting', 'manage')
    },
    handle360ClueSetting() {
      this.$router.push({ name: 'lead-workbench' })
    },
    handleWalletSetting() {
      redirectToFS('#app/entwallet/wallet')
    },
    handleExternalQrcodeSetting() {
      this.externalQrcodeVisible = true
    },
    openLook() {
      const url = this.$router.resolve({
        name: 'website-access-init',
        query: {
          tab: 'website-manage',
        },
      })
      window.open(url.href, '_blank')
    },
    setPolyv() {
      this.polyvSettingVisible = true
      this.validationBindPolyv()
    },
    toPolyv() {
      window.open('https://www.polyv.net/ ', '_blank')
    },
    toSettingTA() {
      this.$router.push({ name: 'ad-tencent-setting' })
    },
    toSettingBytedance() {
      this.$router.push({ name: 'ad-bytedance-setting' })
    },
    toSettingBaidu() {
      this.$router.push({ name: 'ad-token-plugin-setting' })
    },
    setToken() {
      this.$router.push({ name: 'ad-token-setting' })
    },
    setPolyvNews() {
      this.$refs.polyvForm.validate(valid => {
        if (valid) {
          http.polyvBindPolyvAccount(this.ployvData).then(res => {
            if (res.errCode === 0) {
              FxUI.Message.success($t('marketing.commons.bccg_3b1083'))
              this.polyvSettingVisible = false
            }
          })
        }
      })
    },
    //  验证是否绑定polyv
    validationBindPolyv() {
      http.polyvBindAccount().then(res => {
        if (res.data.isBind) {
          this.isBindPolyv = true
          this.ployvData = {
            userId: res.data.userId,
            appId: res.data.appId,
            appSecret: res.data.appSecret,
          }
        } else {
          this.isBindPolyv = false
          this.ployvData = {
            userId: '',
            appId: '',
            appSecret: '',
          }
        }
      })
    },
    setXiaoe() {
      this.xiaoeSettingVisible = true
    },
    async getXiaoetongAccount() {
      const res = await http.getXiaoetongAccount()
      if (res && res.errCode === 0) {
        this.xiaoeData = {
          app_id: res.data.clientId,
          appId: res.data.appId,
          appSecret: res.data.secretKey,
        }
        this.isBindXiaoe = res.data.isBind
      }
    },
    toXiaoe() {
      window.open('https://admin.xiaoe-tech.com/login_page#/register', '_blank')
    },
    async setXiaoeNews() {
      const isValid = await this.$refs.xiaoeForm.validate()
      if (!isValid) return
      const res = await http.bindXiaoketongAccount({
        clientId: this.xiaoeData.app_id,
        appId: this.xiaoeData.appId,
        secretKey: this.xiaoeData.appSecret,
      })
      if (res && res.errCode === 0) {
        FxUI.Message.success($t('marketing.commons.bccg_3b1083'))
        this.xiaoeSettingVisible = false
      }
    },
    openGuide() {
      window.open('https://api-doc.xiaoe-tech.com/develop_guide/application_authorize.html', '_blank')
    },
    openConsole() {
      window.open('https://admin.cloud.xiaoe-tech.com/#/account', '_blank')
    },
    copyIds() {
      const copyHtmlIds = text.replace(/\s/g, '').replace(/;/g, '\n')
      navigator.clipboard
        .writeText(copyHtmlIds)
        .then(res => {
          FxUI.Message.success($t('marketing.commons.fzcg_8a4831'))
        })
        .catch(err => {
          FxUI.Message.error($t('marketing.commons.fzsb_cd9817'))
        })
    },
    setVhall() {},
    toVhall() {
      window.open('https://e.vhall.com/v3/register', '_blank')
    },
    setWechatVideo() {
      this.$router.push({ name: 'wechat-video-setting' })
    },
    toLink() {
      window.open('https://kf.qq.com/faq/190726e6JFja190726qMJBn6.html', '_blank')
    },
    // 腾讯广告插件 --> 验证是否开通营销一体化
    // adAccount/checkMarketingIntegration
    validateMarketingIntegration() {
      http.checkMarketingIntegration().then(res => {
        if (res.errCode === 0) {
          this.hasMarketingIntegration = !!res.data.hasMarketingIntegration
        }
      })
    },
    handleViewerClose() {
      this.isShowImageViewer = false
      this.previewImageUrls = []
    },
    showViewer(index) {
      this.isShowImageViewer = true
      this.previewImageUrls = [picShow[index]]
    },
    async getBoundMiniappInfo() {
      const res = await http.getBoundMiniappInfo({
        platformId: 'YXT',
      })
      if (res && res.errCode === 0) {
        this.appType = res.data.appType
      }
    },
    handlePictureSetting() {
      this.pictureVisible = true
      this.listMobilePhotoGroupSelected()
    },
    toSettingQywxKnowledge() {
      // 112地址：#paasapp/index/=/appId_FSAID_9897de
      // 线上地址：#paasapp/index/=/appId_FSAID_989ace
      if (/localhost|ceshi112/.test(window.location.origin)) {
        redirectToFS('#paasapp/index/=/appId_FSAID_9897de')
      } else {
        redirectToFS('#paasapp/index/=/appId_FSAID_989ace')
      }
    },
    async listMobilePhotoGroupSelected() {
      const res = await http.listMobilePhotoGroup({
        mobileDisplay: 1,
        source: 1,
      })
      if (res.errCode === 0 && res.data) {
        this.pictureValue = res.data.map(item => ({ ...item, groupId: item.id }))
        this.defaultCheckedKeys = res.data.map(item => item.id)
      }
    },
    async mobilePhotoGroupSetting() {
      const res = await http.mobilePhotoGroupSetting({
        groupIds: this.pictureValue.map(item => item.groupId),
      })
      if (res.errCode === 0) {
        this.pictureVisible = false
        FxUI.Message.success($t('marketing.commons.bccg_3b1083'))
      }
    },
    handleCheckChange(checkedList) {
      this.pictureValue = checkedList
    },
    async queryEnterCouponEaList() {
      const res = await http.queryEnterCouponEaList()
      if (res.errCode === 0) {
        this.enterCouponEaList = res.data || false
      }
    },
    toFxiaokeHelp() {
      window.open('https://help.fxiaoke.com/93d5/9188/6407/3954', '_blank')
    },
    openOnlineServiceHelp() {
      window.open('https://help.fxiaoke.com/93d5/5540/d184', '_blank')
    },
    openOnlineService() {
      const isNewFx = (window.Fx && window.Fx.theme === 'new') || false
      window.open(`${window.location.origin + (isNewFx ? '/XV/UI/manage' : window.location.pathname)}#app/onlineservice/wxworkreport`)
    },
    toEiisys() {
      window.open('https://www.eiisys.com/home/<USER>', '_blank')
    },
    handleShowMuduSettingDialog() {
      this.showMuduSettingDialog = true
    },
    handleHideMuduSettingDialog() {
      this.showMuduSettingDialog = false
      this.muduSettingCallback = null
    },
    handleCrmSavingSetting() {
      this.vhallMapSettingVisible = true
    },
    openVHallSetting() {
      this.vHallSettingVisible = true
    },
    async getVHallAccount() {
      const res = await http.getThirdAccount({
        platform: 7,
      })
      if (res && res.errCode === 0) {
        this.vhallData = {
          appId: res.data.appId,
          appSecret: res.data.secretKey,
        }
      }
    },
    openLink(url, type = 'target') {
      if (url) {
        window.open(url, type)
      }
    },
    handleRouteTo(routeTo) {
      this.$router.push(routeTo)
    },
    getSinglePluginstatus(category, pluginType) {
      const plugin = this.marketingPluginMap[category].plugins.filter(plugin => plugin.pluginType === pluginType)[0] || {}
      return plugin.model_switch
    },
    autoOpenDialog(){
      const { visible } = this.$route.query
      if(!visible) return
      if(visible === 'verifySmsTemplate'){
        this.verifySmsTemplateDialogVisible = true
      }
    }
  },
}
</script>

<style lang="less" scoped>
.MarketingPlugin {
  padding: 16px 14px;

  .MarketingPlugin__wrapper {
    display: flex;
    flex-direction: column;
  }

  .MarketingPlugin__category-wrapper {
    margin-bottom: 20px;
  }

  .MarketingPlugin__category {
    margin-bottom: 10px;
    font-size: 16px;
    line-height: 23px;
    color: #181c25;
  }

  .MarketingPlugin__plugins {

    .MarketingPlugin__item {
      display: flex;
      flex-direction: column;
      padding: 14px 14px 12px;
      width: 450px;
      background: #f2f4fb;
      border-radius: 4px;
      box-sizing: border-box;

      .title__text {
        display: flex;
        align-items: center;
        font-size: 14px;
        font-weight: 600;
        line-height: 20px;
        color: #181c25;
      }

      .icon {
        width: 18px;
        height: 18px;
        font-size: 18px;
        margin-right: 8px;
        border-radius: 4px;
      }

      .MarketingPlugin__desc {
        margin: 4px 0 0 26px;
        font-size: 14px;
        height: 58px;
        line-height: 20px;
        color: #545861;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3;
      }

      .space {
        color: @color-link;
      }

      .MarketingPlugin__switch {
        display: flex;
        margin: 4px 0 4px 26px;
        justify-content: space-between;
        align-items: center;

        ::v-deep .fx-button {
          padding: 0;
          font-size: 14px;
        }
      }

      .MarketingPlugin__button {
        // margin: 8px 0 0 344px;
        width: 76px;
        height: 28px;
        border: 1px solid #dee1e6;
        color: #181c25;
        padding: 0;
        line-height: 28px;

        &:hover {
          background: #fbfbfb;
        }
      }
    }

    // .MarketingPlugin__item + .MarketingPlugin__item {
    //   margin-left: 16px;
    // }
  }
}

.interface {
  .header {
    height: 40px;
    background: #f2f3f5;
    padding: 0 20px;
  }

  .body {
    padding: 20px;
    display: flex;

    .left {
      width: 50%;
      padding-right: 20px;

      .title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 10px;
      }

      .step {
        margin-bottom: 16px;
        .code {
          background-color: #f2f4fb;
          display: inline-block;
          padding: 15px 10px;
          .text {
            padding: 0;
          }
        }
      }
    }

    .right {
      width: 50%;
    }

    .polyvLeft {
      width: 55%;
    }

    .polyvRight {
      width: 45%;
      padding-left: 20px;

      .title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 10px;
      }

      .step {
        margin-bottom: 16px;
      }
    }
  }
}

.MarketingPlugin__pro-tips-dialog {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 450px;
  justify-content: center;
  position: relative;
  padding: 0 10px;

  .tips-title {
    font-size: 20px;
    line-height: 29px;
    color: #181c25;
  }

  .tips-desc {
    margin-top: 10px;
    font-size: 16px;
    line-height: 23px;
    color: #545861;
  }

  .tips-button {
    margin-top: 20px;
    width: 110px;
    line-height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 36px;
  }

  .tips-bg {
    position: absolute;
    width: 571px;
    bottom: 70px;
    right: 30px;
  }
}

.step {
  .step-item {
    position: relative;
    padding-left: 26px;

    &::after {
      content: '';
      display: block;
      position: absolute;
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background: #b2bede;
      left: 0;
      top: 5px;
    }

    &::before {
      content: '';
      display: block;
      position: absolute;
      width: 1px;
      height: 100%;
      background: #dee1e8;
      left: 5px;
    }

    .title {
      color: #181c25;
      font-size: 16px;
      line-height: 18px;
    }

    .content {
      font-size: 14px;
      color: #181c25;
      line-height: 18px;
      padding-bottom: 40px;

      .ip-adress {
        width: 186px;
        padding-left: 21px;
        padding-top: 11px;
        padding-bottom: 11px;
        background: #f2f4fb;
        font-size: 12px;
        line-height: 18px;
        margin-top: 21px;
      }

      .pic {
        margin-top: 21px;
      }

      .text {
        list-style: disc;
        margin: 20px 0 0 20px;
        .text-item {
          line-height: 32px;
        }

        .text-item::marker {
          color: #3860f4;
        }
      }

      .body {
        display: flex;
        align-items: center;

        .left {
          img {
            width: 440px;
          }
        }

        .right {
          margin-left: 50px;
        }
      }

      .crm {
        width: 500px;
        margin-top: 10px;

        ::v-deep {
          .crm-leadPoolId {
            width: 400px;
          }
        }
      }
    }
  }
}

.video-content {
  .video-tips {
    background: #fff3eb;
    border: 1px solid #ffe3ca;
    border-radius: 3px;
    padding: 10px 55px 10px 14px;
    line-height: 24px;
    margin-bottom: 20px;

    ::v-deep .fx-button {
      padding: 0;
    }

    ul {
      margin-left: 34px;

      li {
        list-style: initial;
      }
    }
  }

  .video-input {
    float: left;

    .tip {
      margin-left: 10px;
    }
  }

  ::v-deep .fx-input {
    width: 400px;
  }
}

.picture-body {
  ::v-deep {
    .el-checkbox__label {
      font-size: 14px !important;
    }

    .el-transfer__button {
      border-radius: unset;
    }
  }
}

.external-qrcode-setting {
  /deep/.el-dialog__body {
    padding: 0;
  }

  &__tip {
    height: 40px;
    padding: 0 20px;
    display: flex;
    align-items: center;
    background: #f2f3f5;
    font-size: 12px;
    line-height: 18px;
    color: #545861;
  }

  &__body {
    margin: 30px 25px 0 25px;
    border-left: 1px solid @border-color-base;
    padding-left: 20px;
  }

  &__step {
    margin-bottom: 40px;
    &:last-child {
      margin-bottom: 0;
    }
  }

  &__step-title {
    font-weight: 700;
    font-size: 16px;
    line-height: 18px;
    color: @color-title;
    position: relative;
    margin-bottom: 20px;

    &::before {
      content: ' ';
      display: block;
      width: 9px;
      height: 9px;
      border-radius: 100%;
      background-color: #b2bede;
      position: absolute;
      left: -25px;
      top: 50%;
      transform: translate(0, -55%);
    }
  }

  &__step-memo {
    margin-bottom: 20px;
  }

  &__step-subtitle {
    font-weight: 700;
    font-size: 14px;
    line-height: 18px;
    margin-bottom: 8px;
    color: @color-title;
  }

  &__step-content {
    font-weight: 400;
    font-size: 14px;
    line-height: 18px;
    color: @color-title;
  }

  &__step-form {
    display: flex;
    align-items: center;
    margin-top: 10px;
  }

  &__step-label {
    color: #545861;

    i {
      color: #ff522a;
      margin-right: 3px;
    }
  }

  &__step-input {
    width: 584px;
  }

  &__step-link {
    display: inline-block;
    color: @color-link;
    margin-top: 8px;
  }
}
</style>
