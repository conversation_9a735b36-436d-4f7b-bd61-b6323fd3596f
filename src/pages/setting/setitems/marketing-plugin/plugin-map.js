/* eslint-disable global-require */
import kisvData from '@/modules/kisv-data.js'
import store from '@/store/index.js'
import wxList from '@/modules/wx-list.js'

const { realVersion, isPurchaseAdLicense } = kisvData.datas.uinfo
const isProOrStrengthen = /marketing_strategy_strengthen_app|marketing_strategy_pro_app|marketing_strategy_pro_dingtalk.*_app/.test(realVersion)
const adDatabackLockOnEnable = !(isPurchaseAdLicense || isProOrStrengthen)

const { isQywxOpen } = store.state.Global
const isSmsOpen = store.state.SmsMarketing.statusSMS === 0
const isWechatOpen = () => {
  try {
    return wxList.datas.list.length > 0
  } catch (e) {
    return false
  }
}
const isEmailOpen = store.state.MailMarketing.completeAllConfig
const { isWebsiteAccessOpen } = store.state.WebsiteAccess
const { isCustomMiniappOpen } = store.state.MiniappInfo
const showQywxEmployeeList = store.state.Global.addressBookTypeNum === 1

export const LIVE_PLUGIN_TYPE_ENUM = {
  VHALL: 502,
  XIAOE: 501,
  POLYV: 5,
  CHANNELS: 503,
  MUDU: 504,
}

export const marketingPluginMap = {
  scenarioApplication: {
    category: $t('marketing.commons.yxcj_2aa63c'),
    plugins: [
      {
        id: 'meetingMarketing',
        pluginType: 201,
        key: 'meetingMarketing',
        title: $t('marketing.commons.hyyx_5f60fd'),
        desc:
          $t('marketing.pages.setting.qyxxhyszhg_172ba3'),
        model_switch: false,
        disabled: false,
        lockOnEnable: false,
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.commons.qrsfkq_605e6c'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.commons.qrsfgb_543f9a'),
            desc: '',
          },
        },
        icon: 'iconhuiyiyingxiao',
        iconColor: '#0C6CFF',
        iconfont: true,
        pluginControlMenu: 'meeting-marketing-init',
        pluginSettings: [
          {
            text: $t('marketing.commons.hytxsz_bffede'),
            hide: kisvData.datas.strategy === 'dingTalk',
            routeTo: { name: 'setting-meeting-set' },
          },
        ],
      },
      {
        id: 'livingMarketing',
        pluginType: 202,
        key: 'livingMarketing',
        title: $t('marketing.commons.zbyx_a9fa5d'),
        desc:
          $t('marketing.pages.setting.djblwwhmdx_baeefd'),
        model_switch: false,
        disabled: false,
        lockOnEnable: false, // 开启后不能关闭
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.commons.qrsfkq_605e6c'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.commons.qrsfgb_543f9a'),
            desc: '',
          },
        },
        icon: 'iconzhiboyingxiao',
        iconColor: '#16B4AB',
        iconfont: true,
        pluginControlMenu: 'live-marketing',
      },
      {
        id: 'adMarketing',
        pluginType: 203,
        key: 'adMarketing',
        title: $t('marketing.commons.ggyx_0271c7'),
        desc:
          $t('marketing.pages.setting.gnwzlggptd_32cbc7'),
        model_switch: false,
        disabled: false,
        lockOnEnable: false, // 开启后不能关闭
        pluginControlMenu: 'ad-marketing',
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.commons.qrsfkq_605e6c'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.commons.qrsfgb_543f9a'),
            desc: '',
          },
        },
        icon: 'iconguanggaoyingxiao',
        iconColor: '#0C6CFF',
        iconfont: true,
        pluginSettings: [
          {
            text: $t('marketing.commons.sz_e366cc'),
            routeTo: { name: 'setting-ad-set', query: { type: 'utmSetting' } },
          },
        ],
      },
      {
        id: 'websiteMarketing',
        pluginType: 204,
        key: 'websiteMarketing',
        title: $t('marketing.pages.setting.gwyx_ceac8b'),
        desc:
          $t('marketing.pages.setting.jrqygwdjgw_b200cd'),
        model_switch: false,
        disabled: false,
        lockOnEnable: false, // 开启后不能关闭
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.commons.qrsfkq_605e6c'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.commons.qrsfgb_543f9a'),
            desc: '',
          },
        },
        icon: 'iconguanwang',
        iconColor: '#30C776',
        iconfont: true,
        pluginControlMenu: 'website',
        pluginSettings:
          isWebsiteAccessOpen ? [] : [
            {
              text: $t('marketing.commons.kt_a06d40'),
              routeTo: { name: 'website-access-init' },
            },
          ],
      },
      {
        id: 'miniappMarketing',
        pluginType: 205,
        key: 'miniappMarketing',
        title: $t('marketing.commons.szzt_b97c15'),
        desc:
          $t('marketing.pages.setting.tgqyzywxxc_4dff52'),
        model_switch: false,
        disabled: false,
        lockOnEnable: false, // 开启后不能关闭
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.commons.qrsfkq_605e6c'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.commons.qrsfgb_543f9a'),
            desc: '',
          },
        },
        icon: 'iconxiaochengxuweizhan',
        iconColor: '#87CC3B',
        iconfont: true,
        pluginControlMenu: 'vapp-setting',
        pluginSettings: [
          {
            text: isCustomMiniappOpen ? $t('marketing.commons.gl_08b55f') : $t('marketing.commons.sq_98a315'),
            // eslint-disable-next-line no-nested-ternary
            routeTo: isCustomMiniappOpen
              ? (showQywxEmployeeList ? { name: 'qywx-employee', params: { id: 2 } } : { name: 'setting-channel', params: { id: 1 } })
              : { name: 'miniapp-setting' },
            showSpace: isCustomMiniappOpen,
          },
          {
            hide: !isCustomMiniappOpen,
            text: $t('marketing.commons.zfpz_1c3835'),
            routeTo: { name: 'setting-pay-set' },
          },
        ],
      },
      {
        id: 'employeeMarketing',
        pluginType: 206,
        key: 'employeeMarketing',
        title: $t('marketing.pages.setting.ygyx_f7ac22'),
        desc:
          $t('marketing.pages.setting.scrysztytg_ecf464'),
        model_switch: false,
        disabled: false,
        lockOnEnable: false,
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.commons.qrsfkq_605e6c'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.commons.qrsfgb_543f9a'),
            desc: '',
          },
        },
        icon: 'iconyuangongyingxiao',
        iconColor: '#FF522A',
        iconfont: true,
        pluginControlMenu: 'promotion',
      },
      {
        id: 'partner-marketing',
        pluginType: 3,
        key: 'partnerMarketingEnabled',
        title: $t('marketing.commons.hbyx_bd40f9'),
        desc: $t('marketing.pages.setting.qdyxjjfaqy_d7e132'),
        model_switch: false, // 开关模型
        disabled: false, // 开关禁用
        lockOnEnable: true, // 开启后不能关闭
        onlyPro: true,
        needApply: false,
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.commons.qrsfkq_605e6c'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.commons.qrsfgb_543f9a'),
            desc: '',
          },
        },
        icon: require('@/assets/images/plugins/partner-marketing.png'),
        licenseList: ['marketing_partner_plugin_app'],
      },
      {
        id: 'member-promotion',
        pluginType: 64,
        key: 'memberMarketingPluginEnable',
        title: $t('marketing.commons.hyyx_b017e9'),
        desc: $t('marketing.pages.setting.gjjchysftx_99f981'),
        model_switch: false,
        disabled: false,
        onlyPro: false,
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.commons.qrsfkq_605e6c'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.commons.qrsfgb_543f9a'),
            desc: '',
          },
        },
        icon: require('@/assets/images/plugins/member.png'),
      },
    ],
  },
  bulkSendingApplication: {
    category: $t('marketing.pages.setting.qftd_cbd26f'),
    plugins: [
      {
        id: 'smsMarketing',
        pluginType: 207,
        key: 'smsMarketing',
        title: $t('marketing.commons.dxyx_de4a39'),
        desc:
          $t('marketing.pages.setting.scyxglrykg_f9333a'),
        model_switch: false,
        disabled: false,
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.commons.qrsfkq_605e6c'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.commons.qrsfgb_543f9a'),
            desc: '',
          },
        },
        icon: 'iconduanxin2',
        iconColor: '#FF8000',
        iconfont: true,
        pluginControlMenu: 'sms-marketing-init',
        pluginSettings:
          isSmsOpen ? [] : [
            {
              text: $t('marketing.commons.kt_a06d40'),
              routeTo: { name: 'sms-marketing-init', params: { page: 'welcome' } },
            },
          ],
      },
      {
        id: 'mailMarketing',
        key: 'mailMarketing',
        pluginType: 208,
        title: $t('marketing.commons.yjyx_194e1d'),
        desc:
          $t('marketing.pages.setting.scyxglrykg_6dd815'),
        model_switch: false,
        disabled: false,
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.commons.qrsfkq_605e6c'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.commons.qrsfgb_543f9a'),
            desc: '',
          },
        },
        icon: 'iconyoujian1',
        iconColor: '#189DFF',
        iconfont: true,
        pluginControlMenu: 'mail-welcome',
        pluginSettings: [
          {
            text: isEmailOpen ? '' : $t('marketing.commons.kt_a06d40'),
            routeTo: { name: isEmailOpen ? 'mail-home' : 'mail-welcome' },
          },
          {
            hide: !isEmailOpen,
            text: $t('marketing.commons.sz_e366cc'),
            routeTo: { name: 'setting-mail-set' },
          },
        ],
      },
      {
        id: 'qywxMarketing',
        key: 'qywxMarketing',
        pluginType: 209,
        title: $t('marketing.commons.qwyx_79ce25'),
        desc:
          $t('marketing.pages.setting.ljqywxtgyg_de3821'),
        model_switch: false,
        disabled: false,
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.commons.qrsfkq_605e6c'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.commons.qrsfgb_543f9a'),
            desc: '',
          },
        },
        icon: 'iconqiyeweixin3',
        iconColor: '#189DFF',
        iconfont: true,
        pluginControlMenu: 'qywx-manage',
        pluginSettings: [
          {
            text: isQywxOpen ? $t('marketing.commons.gl_08b55f') : $t('marketing.pages.setting.lj_30f7dd'),
            routeTo: { name: 'qywx-manage' },
            showSpace: isQywxOpen,
          },
          {
            hide: !isQywxOpen,
            text: $t('marketing.commons.sz_e366cc'),
            routeTo: { name: 'setting-qywx-set' },
          },
        ],
      },
      {
        id: 'weChatAccount',
        key: 'weChatAccount',
        pluginType: 210,
        title: $t('marketing.commons.gzhyx_8bd4fe'),
        desc:
          $t('marketing.pages.setting.scryktgyxt_0463f5'),
        model_switch: false,
        disabled: false,
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.commons.qrsfkq_605e6c'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.commons.qrsfgb_543f9a'),
            desc: '',
          },
        },
        icon: 'iconweixingongzhonghaoyingxiao',
        iconColor: '#30C776',
        iconfont: true,
        pluginControlMenu: 'wechat-home',
        pluginSettings: [
          {
            text: isWechatOpen() ? $t('marketing.commons.gl_08b55f') : $t('marketing.pages.setting.bd_f4f12c'),
            routeTo: { name: 'wechat-home' },
          },
        ],
      },

    ],
  },
  livings: {
    category: $t('marketing.pages.setting.zbpt_0d615e'),
    plugins: [
      {
        id: 'polyv',
        pluginType: LIVE_PLUGIN_TYPE_ENUM.POLYV,
        key: 'bindPolyvEnabled',
        title: $t('marketing.pages.setting.blwzbdj_1a1201'),
        desc: $t('marketing.pages.setting.wcpzhzcjzb_c2ed19'),
        model_switch: false,
        disabled: false,
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.commons.qrsfkq_605e6c'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.commons.qrsfgb_543f9a'),
            desc: '',
          },
        },
        icon: require('@/assets/images/plugins/polyv.png'),
        liveIsLead: true,
      },
      {
        id: 'mudu',
        pluginType: LIVE_PLUGIN_TYPE_ENUM.MUDU,
        title: $t('marketing.pages.setting.mdzbdj_8850e1'),
        desc: $t('marketing.pages.setting.wcpzhzcjzb_512876'),
        model_switch: false,
        disabled: false,
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.commons.qrsfkq_605e6c'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.commons.qrsfgb_543f9a'),
            desc: '',
          },
        },
        icon: require('@/assets/images/plugins/mudu.png'),
      },
      {
        id: 'xiaoe',
        pluginType: LIVE_PLUGIN_TYPE_ENUM.XIAOE,
        title: $t('marketing.pages.setting.xetzbdj_aedb3a'),
        desc: $t('marketing.pages.setting.wcpzhzcjzb_951979'),
        model_switch: false,
        disabled: false,
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.commons.qrsfkq_605e6c'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.commons.qrsfgb_543f9a'),
            desc: '',
          },
        },
        icon: require('@/assets/images/plugins/xiaoetong.png'),
        liveIsLeadXiaoe: true,
      },
      {
        id: 'vhall',
        pluginType: LIVE_PLUGIN_TYPE_ENUM.VHALL,
        title: $t('marketing.pages.setting.whzbdj_07e211'),
        desc: $t('marketing.pages.setting.wcpzhzcjzb_cf9f15'),
        model_switch: false,
        disabled: false,
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.commons.qrsfkq_605e6c'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.commons.qrsfgb_543f9a'),
            desc: '',
          },
        },
        icon: require('@/assets/images/plugins/weihong.png'),
        liveIsLeadVhall: true,
      },
      {
        id: 'wechatVideo',
        pluginType: LIVE_PLUGIN_TYPE_ENUM.CHANNELS,
        title: $t('marketing.pages.setting.wxsphdj_57fd08'),
        desc: $t('marketing.pages.setting.qybwcpzhzc_620416'),
        model_switch: false,
        disabled: false,
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.commons.qrsfkq_605e6c'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.commons.qrsfgb_543f9a'),
            desc: '',
          },
        },
        icon: require('@/assets/images/plugins/wechat-video.png'),
        liveIsLeadVideo: true,
      },
    ],
  },
  /*
   * 是否购买广告插件: kisvData.datas.uinfo.isPurchaseAdLicense
   * 广告插件打开条件: 购买了专业版以上营销通或者是购买了广告插件
  */
  ads: {
    category: $t('marketing.pages.setting.ggpt_e16c39'),
    plugins: [
      {
        id: 'ad-baidu',
        pluginType: 30,
        key: 'bindBaiduAdEnable',
        title: $t('marketing.commons.bdgg_694aaa'),
        desc: $t('marketing.pages.setting.kjrbdggzhj_2b50ac'),
        model_switch: false, // 开关模型
        disabled: false, // 开关禁用
        onlyPro: !kisvData.datas.uinfo.isPurchaseAdLicense,
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.pages.setting.sfqdkqbdgg_ee29c3'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.pages.setting.sfqdgbbdgg_cd407e'),
            desc: '',
          },
        },
        icon: require('@/assets/images/plugins/baidu.png'),
        adBaidu: true,
      },
      {
        id: 'ad-tencent',
        pluginType: 31,
        key: 'bindTencentAdEnable',
        title: $t('marketing.commons.txgg_22382a'),
        desc: $t('marketing.pages.setting.kjrtxggzhj_8ed891'),
        model_switch: false, // 开关模型
        disabled: false, // 开关禁用
        onlyPro: !kisvData.datas.uinfo.isPurchaseAdLicense,
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.pages.setting.sfqdkqtxgg_5a350b'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.pages.setting.sfqdgbtxgg_258a0a'),
            desc: '',
          },
        },
        icon: require('@/assets/images/plugins/tencent.png'),
        adTencent: true,
      },
      {
        id: 'ad-bytedance',
        pluginType: 32,
        key: 'bindHeadlinesAdEnable',
        title: $t('marketing.pages.setting.jlyq_0c5064'),
        desc: $t('marketing.pages.setting.kjrjlyqggz_661213'),
        model_switch: false, // 开关模型
        disabled: false, // 开关禁用
        onlyPro: !kisvData.datas.uinfo.isPurchaseAdLicense,
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.commons.sfqdkqttgg_79ff76'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.commons.sfqdgbttgg_819820'),
            desc: '',
          },
        },
        icon: require('@/assets/images/plugins/bytedance.png'),
        adBytedance: true,
      },
      {
        id: 'ad-databack',
        pluginType: 34,
        key: 'adDataSendBackEnable',
        title: $t('marketing.pages.setting.ggxgsjhc_c63a86'),
        desc: $t('marketing.pages.setting.tgsbkhsdzh_8cb3b6'),
        model_switch: false, // 开关模型
        disabled: false, // 开关禁用
        lockOnEnable: adDatabackLockOnEnable, // 开启后不能关闭
        onlyPro: !adDatabackLockOnEnable,
        needApply: false, // 需要人工申请开通
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.commons.sfqdkqttgg_79ff76'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.commons.sfqdgbttgg_819820'),
            desc: '',
          },
        },
        icon: require('@/assets/images/plugins/passback.png'),
        adDataback: true,
      },
      {
        id: 'clue-zhihu',
        pluginType: 11,
        key: 'zhiHuClue',
        title: $t('marketing.pages.setting.zhxsljq_a3a8cc'),
        desc: $t('marketing.pages.setting.qyhjktljqw_bf0875'),
        model_switch: false,
        disabled: false,
        lockOnEnable: true, // 开启后不能关闭
        onlyPro: true,
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.commons.qrsfkq_605e6c'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.commons.qrsfgb_543f9a'),
            desc: '',
          },
        },
        icon: require('@/assets/images/plugins/zhihu.png'),
        zhiHuClue: true,
        licenseList: ['marketing_ad_plugin_app', 'zhihu_huabao_data_sync_app'],
      },
      {
        id: 'clue-sougou',
        pluginType: 12,
        key: 'soGouClue',
        title: $t('marketing.pages.setting.sgxsljq_a43c20'),
        desc: $t('marketing.pages.setting.qyhjktljqw_75b2b5'),
        model_switch: false,
        disabled: false,
        lockOnEnable: true, // 开启后不能关闭
        onlyPro: true,
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.commons.qrsfkq_605e6c'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.commons.qrsfgb_543f9a'),
            desc: '',
          },
        },
        icon: require('@/assets/images/plugins/sougou.png'),
        soGouClue: true,
        licenseList: ['marketing_ad_plugin_app', 'sogo_xiansuocrm_data_sync_app'],
      },
      {
        id: 'clue-kuaishou',
        pluginType: 13,
        key: 'kuaiShouClue',
        title: $t('marketing.pages.setting.ksxsljq_2b2dd0'),
        desc: $t('marketing.pages.setting.qyhjktljqw_9151b9'),
        model_switch: false,
        disabled: false,
        lockOnEnable: true, // 开启后不能关闭
        onlyPro: true,
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.commons.qrsfkq_605e6c'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.commons.qrsfgb_543f9a'),
            desc: '',
          },
        },
        icon: require('@/assets/images/plugins/kuaishou.png'),
        KuaiShouClue: true,
        licenseList: ['marketing_ad_plugin_app', 'kuaishou_xiansuocrm_data_sync_app'],
      },
      {
        id: 'clue-shenma',
        pluginType: 14,
        key: 'shenMaClue',
        title: $t('marketing.pages.setting.smxsljq_1517a0'),
        desc: $t('marketing.pages.setting.qyhjktljqw_c98f7b'),
        model_switch: false,
        disabled: false,
        lockOnEnable: true, // 开启后不能关闭
        onlyPro: true,
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.commons.qrsfkq_605e6c'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.commons.qrsfgb_543f9a'),
            desc: '',
          },
        },
        icon: require('@/assets/images/plugins/shenma.png'),
        shenMaClue: true,
        licenseList: ['marketing_ad_plugin_app', 'uc_shenma_data_sync_app'],
      },
      {
        id: 'clue-360',
        pluginType: 15,
        key: 'DianJingClue',
        title: 360,
        desc: $t('marketing.pages.setting.zjqwtgxsmb_fc9877'),
        uploadBy360: true,
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.commons.qrsfkq_605e6c'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.commons.qrsfgb_543f9a'),
            desc: '',
          },
        },
        icon: require('@/assets/images/plugins/360.png'),
        DianJingClue: true,
        hideSwitch: true,
      },
      {
        id: 'clue-xiaohongshu',
        pluginType: 214,
        key: 'xiaohongshuClue',
        title: $t('marketing.pages.setting.xhsxsljq_80d0fe'),
        desc: $t('marketing.pages.setting.qyhjktljqw_8f6b26'),
        model_switch: false,
        disabled: false,
        lockOnEnable: true, // 开启后不能关闭
        onlyPro: true,
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.commons.qrsfkq_605e6c'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.commons.qrsfgb_543f9a'),
            desc: '',
          },
        },
        icon: require('@/assets/images/plugins/xiaohongshu.jpg'),
        xiaohongshuClue: true,
        licenseList: ['marketing_ad_plugin_app'],
      },
      {
        id: 'douyin-life-clue',
        pluginType: 215,
        key: 'douyingLifeClue',
        title: $t('marketing.pages.setting.dylkxsljq_1f249c'),
        desc: $t('marketing.pages.setting.qyhjktljqw_0e9f52'),
        model_switch: false,
        disabled: false,
        lockOnEnable: true, // 开启后不能关闭
        onlyPro: true,
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.commons.qrsfkq_605e6c'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.commons.qrsfgb_543f9a'),
            desc: '',
          },
        },
        icon: require('@/assets/images/plugins/douyin-life.png'),
        douyingLifeClue: true,
        licenseList: ['marketing_ad_plugin_app'],
      },
    ],
  },
  overseasMarketing: {
    category: $t('marketing.pages.setting.hwyx_175634'),
    plugins: [
      {
        id: 'ad-google',
        pluginType: 59,
        key: 'bindGoogleAdEnable',
        title: $t('marketing.pages.setting.gg_7f008d'),
        desc: $t('marketing.pages.setting.kjrggsjjkg_e8d98b'),
        model_switch: false, // 开关模型
        disabled: false, // 开关禁用
        lockOnEnable: true, // 开启后不能关闭
        onlyPro: true,
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.pages.setting.sfqdkqgggn_2f6f11'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.pages.setting.sfqdgbgggn_dda067'),
            desc: '',
          },
        },
        icon: require('@/assets/images/plugins/ad-google.png'),
        bindGoogleAdEnable: true,
        licenseList: ['marketing_overseas_plugin_app', 'google_data_sync_app'],
      },
      {
        id: 'linkedin',
        pluginType: 62,
        key: 'linkedEnable',
        title: $t('marketing.pages.setting.gg_91bc6a'),
        desc: $t('marketing.pages.setting.kjrggsjjkg_863949'),
        model_switch: false,
        disabled: false,
        lockOnEnable: true, // 开启后不能关闭
        onlyPro: true,
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.commons.qrsfkq_605e6c'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.commons.qrsfgb_543f9a'),
            desc: '',
          },
        },
        icon: require('@/assets/images/plugins/linkedin.png'),
        linKeDinClue: true,
        licenseList: ['marketing_overseas_plugin_app', 'linkedin_data_sync_app'],
      },
      {
        id: 'facebook',
        pluginType: 57,
        key: 'faceBookClueLink',
        title: $t('marketing.pages.setting.gg_ef7022'),
        desc: $t('marketing.pages.setting.kjrggsjjkg_01b1e3'),
        model_switch: false,
        disabled: false,
        lockOnEnable: true, // 开启后不能关闭
        onlyPro: true,
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.commons.qrsfkq_605e6c'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.commons.qrsfgb_543f9a'),
            desc: '',
          },
        },
        icon: require('@/assets/images/plugins/facebook.png'),
        faceBookClue: true,
        hideSwitch: false,
        licenseList: ['marketing_overseas_plugin_app', 'facebook_data_sync_app'],
      },
      // {
      //   id: 'tiktok',
      //   pluginType: 999,
      //   key: 'tiktok',
      //   title: $t('marketing.pages.setting.gg_084d74'),
      //   desc: $t('marketing.pages.setting.kjrggsjjkg_125ee1'),
      //   model_switch: false,
      //   disabled: false,
      //   lockOnEnable: true, // 开启后不能关闭
      //   onlyPro: true,
      //   switchTips: {
      //     // 点击开关后的弹窗文案
      //     turnEnable: {
      //       main: $t('marketing.commons.qrsfkq_605e6c'),
      //       desc: '',
      //     },
      //     turnDisable: {
      //       main: $t('marketing.commons.qrsfgb_543f9a'),
      //       desc: '',
      //     },
      //   },
      //   icon: require('@/assets/images/plugins/tiktok.png'),
      //   hideSwitch: false,
      // },
      {
        id: 'whatsapp',
        pluginType: 61,
        key: 'whatsAppPluginEnable',
        title: $t('marketing.commons.yx_144714'),
        desc: $t('marketing.commons.ljhlyhjykh_476c46'),
        model_switch: false,
        disabled: false,
        onlyPro: false,
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.commons.qrsfkq_605e6c'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.commons.qrsfgb_543f9a'),
            desc: '',
          },
        },
        icon: require('@/assets/images/plugins/whatsapp.png'),
      },
      {
        id: 'mailchimp',
        pluginType: 220,
        key: 'mailchimpPluginEnable',
        title: $t('marketing.pages.setting.ljq_a03495'),
        desc: $t('marketing.pages.setting.tbfxxklxrs_7e3a82'),
        model_switch: false,
        disabled: false,
        onlyPro: false,
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.commons.qrsfkq_605e6c'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.commons.qrsfgb_543f9a'),
            desc: '',
          },
        },
        mailchimpPluginEnable: true,
        icon: require('@/assets/images/plugins/mailchimp.png'),
      },
    ],
  },
  ways: {
    category: $t('marketing.pages.setting.yxfythcj_29c6cd'),
    plugins: [
      {
        id: 'fw-service',
        pluginType: 53,
        key: 'marketingCustomerIntegration',
        title: $t('marketing.pages.setting.fxzxkfdj_437547'),
        desc: $t('marketing.pages.setting.tbfxzxkfdg_aeb3dd'),
        model_switch: false,
        disabled: false,
        lockOnEnable: true, // 开启后不能关闭
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.commons.qrsfkq_605e6c'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.commons.qrsfgb_543f9a'),
            desc: '',
          },
        },
        icon: require('@/assets/images/plugins/fs-service.png'),
        isMarketingCustomerIntegration: true,
      },
      {
        id: '53-service',
        pluginType: 4,
        key: 'showCustomerServiceEnabled',
        title: $t('marketing.pages.setting.ykfxsdj_616989'),
        desc: $t('marketing.pages.setting.ktbkfdgwzx_528391'),
        model_switch: false,
        disabled: false,
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.commons.qrsfkq_605e6c'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.commons.qrsfgb_543f9a'),
            desc: '',
          },
        },
        icon: require('@/assets/images/plugins/53.png'),
        isLead: true,
      },
      {
        id: 'marketing-sdr',
        pluginType: 60,
        key: 'marketingSDREnable',
        title: $t('marketing.pages.setting.zngzt_4ec56d'),
        desc: $t('marketing.commons.yqdyzsglkh_5eefa2'),
        model_switch: false,
        disabled: false,
        lockOnEnable: false, // 开启后不能关闭
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.commons.qrsfkq_605e6c'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.commons.qrsfgb_543f9a'),
            desc: '',
          },
        },
        icon: require('@/assets/images/plugins/icon-marketing-sdr.svg'),
        marketingSDR: true,
        pluginSettings: [
          {
            text: $t('marketing.commons.sz_e366cc'),
            hide: false,
            routeTo: { name: 'setting-marketing-sdr' },
          },
        ],
      },
      {
        id: 'marketingOrderIntegration',
        pluginType: 52,
        key: 'marketingOrderIntegration',
        title: $t('marketing.pages.setting.dhtg_cd92ec'),
        desc: $t('marketing.pages.setting.dtqddhyyxy_896121'),
        model_switch: false,
        disabled: false,
        lockOnEnable: true,
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.commons.qrsfkq_605e6c'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.commons.qrsfgb_543f9a'),
            desc: '',
          },
        },
        icon: require('@/assets/images/plugins/dht-login.png'),
        isLead: false,
      },
      {
        id: 'marketingPlan',
        key: 'marketingPlan',
        pluginType: 211,
        title: $t('marketing.commons.yyjh_942f80'),
        desc: $t('marketing.pages.setting.scyxtddxtb_8b8b4b'),
        model_switch: false,
        disabled: false,
        lockOnEnable: false,
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.commons.qrsfkq_605e6c'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.commons.qrsfgb_543f9a'),
            desc: '',
          },
        },
        icon: 'iconyunyingjihua',
        iconColor: '#0C6CFF',
        iconfont: true,
        pluginControlMenu: 'kanban',
      },
      {
        id: 'qywx-knowledge',
        pluginType: 100,
        key: 'knowledgeManagement',
        title: $t('marketing.pages.setting.qwhsk_55e9e0'),
        desc: $t('marketing.pages.setting.qyhjkthskk_fc6576'),
        model_switch: false,
        disabled: false,
        lockOnEnable: true, // 开启后不能关闭
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.commons.qrsfkq_605e6c'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.commons.qrsfgb_543f9a'),
            desc: '',
          },
        },
        icon: require('@/assets/images/plugins/huashu.png'),
        isQywxKnowledge: true,
      },
      {
        id: 'knowledge-database',
        pluginType: 101,
        key: 'serviceKnowledgeManagement',
        title: $t('marketing.pages.setting.zsk_7e4333'),
        desc: $t('marketing.pages.setting.kzyxzscxzs_f86667'),
        model_switch: false,
        disabled: false,
        lockOnEnable: false, // 开启后不能关闭
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.commons.qrsfkq_605e6c'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.commons.qrsfgb_543f9a'),
            desc: '',
          },
        },
        icon: require('@/assets/images/plugins/knowledge-database.png'),
      },
      {
        id: 'wallet',
        pluginType: 33,
        key: 'bindWallet',
        title: $t('marketing.pages.setting.qyqb_4cdccd'),
        desc: $t('marketing.pages.setting.tgaqbjqyjz_444586'),
        model_switch: false, // 开关模型
        disabled: false, // 开关禁用
        lockOnEnable: false, // 开启后不能关闭
        onlyPro: true,
        needApply: false,
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.commons.qrsfkq_605e6c'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.commons.qrsfgb_543f9a'),
            desc: '',
          },
        },
        icon: require('@/assets/images/plugins/wallet-icon.png'),
      },
      {
        id: 'marketing-user',
        pluginType: 56,
        key: 'marketingUserPlugin',
        title: $t('marketing.pages.setting.qyyxyhzj_2971f9'),
        desc: $t('marketing.pages.setting.zqykkhdxxq_248b14'),
        model_switch: false,
        disabled: false,
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.pages.setting.sfqrkqqyyx_5b15a0'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.pages.setting.sfqrgbqyyx_095974'),
            desc: '',
          },
        },
        icon: require('@/assets/images/plugins/marketing-user.png'),
        isMarketingUser: true,
      },
    ],
  },
  methods: {
    category: $t('marketing.pages.setting.yxyy_2313dd'),
    plugins: [
      {
        id: 'memberLoyaltyPlanEnabled',
        pluginType: 67,
        key: 'memberLoyaltyPlanEnabled',
        title: $t('marketing.pages.setting.hyjh_fa00e8'),
        desc: $t('marketing.pages.setting.qywlglhjlg_713c43'),
        model_switch: false, // 开关模型
        disabled: false, // 开关禁用
        lockOnEnable: false, // 开启后不能关闭
        onlyPro: true,
        needApply: false,
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.commons.qrsfkq_605e6c'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.commons.qrsfgb_543f9a'),
            desc: '',
          },
        },
        icon: require('@/assets/images/plugins/memberLoyaltyPlanEnabled.svg'),
      },
      {
        id: 'coupons',
        pluginType: 1,
        key: 'sourceCouponEnabled',
        title: $t('marketing.commons.fxyhq_ee2620'),
        desc: $t('marketing.pages.Coupons.fxyhqsfxxk_8c3f5a'),
        model_switch: false, // 开关模型
        disabled: false, // 开关禁用
        lockOnEnable: true, // 开启后不能关闭
        onlyPro: true,
        needApply: false,
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.commons.qrsfkq_605e6c'),
            desc: `${$t('marketing.pages.setting.qyyhqgnhys_b5cbcc')}<br>${$t(
              'marketing.pages.setting.ydkqjwfgb_31efa9',
            )}`,
          },
          turnDisable: {
            main: '',
            desc: '',
          },
        },
        icon: require('@/assets/images/plugins/coupon.png'),
      },
      {
        id: 'wechatMerchantCoupon',
        pluginType: 55,
        key: 'wechatVoucher',
        title: $t('marketing.pages.Coupons.wxsjq_d1392d'),
        desc: $t('marketing.pages.setting.wxsjqswxzf_7b40ef'),
        model_switch: false, // 开关模型
        disabled: false, // 开关禁用
        lockOnEnable: false, // 开启后不能关闭
        onlyPro: true,
        needApply: false,
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.commons.qrsfkq_605e6c'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.commons.qrsfgb_543f9a'),
            desc: '',
          },
        },
        icon: require('@/assets/images/plugins/wechat-coupons.png'),
      },
      {
        id: 'external-qrcode',
        pluginType: 51,
        key: 'outQrCode',
        title: $t('marketing.pages.setting.wbtgmdj_f35b8f'),
        desc: $t('marketing.pages.setting.djwbxtdnrj_26ac9f'),
        model_switch: false, // 开关模型
        disabled: false, // 开关禁用
        lockOnEnable: false, // 开启后不能关闭
        onlyPro: true,
        needApply: false,
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.commons.qrsfkq_605e6c'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.commons.qrsfgb_543f9a'),
            desc: '',
          },
        },
        icon: require('@/assets/images/plugins/external-qrcode-icon.png'),
      },
      {
        id: 'picture',
        pluginType: 41,
        key: 'mobilePhotoLibrary',
        title: $t('marketing.commons.tpk_db7625'),
        desc: $t('marketing.pages.setting.qyhjyyddhq_f46c72'),
        model_switch: false, // 开关模型
        disabled: false, // 开关禁用
        lockOnEnable: false, // 开启后不能关闭
        onlyPro: false,
        needApply: false,
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.commons.qrsfkq_605e6c'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.commons.qrsfgb_543f9a'),
            desc: '',
          },
        },
        icon: require('@/assets/images/plugins/picture-icon.png'),
      },
      {
        id: 'MARKETING_AI_PLUGIN',
        pluginType: 66,
        key: 'marketingAIPluginEnable',
        title: $t('marketing.pages.setting.cj_a8b82c'),
        desc: $t('marketing.pages.setting.tgzdscgzly_1ff332'),
        model_switch: false, // 开关模型
        disabled: false, // 开关禁用
        lockOnEnable: false, // 开启后不能关闭
        onlyPro: false,
        needApply: false,
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.commons.qrsfkq_605e6c'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.commons.qrsfgb_543f9a'),
            desc: '',
          },
        },
        icon: require('@/assets/images/plugins/ai-helper-avatar3.svg'),
        licenseList: ['marketing_ai_plugin_app'],
        // pluginSettings: [
        //   {
        //     text: $t('marketing.commons.sz_e366cc'),
        //     hide: false,
        //     routeTo: { name: 'setting-marketing-ai' },
        //   },
        // ],
      },
      {
        id: 'marketingDataIsolation',
        pluginType: 63,
        key: 'marketingDataIsolation',
        title: $t('marketing.pages.setting.yxsjgl_a8e813'),
        desc: $t('marketing.pages.setting.zcdfzgsddl_b9bb45'),
        model_switch: false,
        disabled: false,
        onlyPro: false,
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.pages.setting.sfdkyxsjgl_35b968'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.pages.setting.gbhyxywsjj_69bceb'),
            desc: '',
          },
        },
        icon: require('@/assets/images/plugins/marketing-user.png'),
        licenseList: ['marketing_group_plugin_app'],
      },
      {
        id: 'xiaobing',
        pluginType: 1001,
        key: 'digitalHumans',
        title: $t('marketing.pages.setting.xbszr_c91c42'),
        desc: $t('marketing.pages.setting.wrxbzsszrd_aa780d'),
        model_switch: false, // 开关模型
        disabled: false, // 开关禁用
        lockOnEnable: false, // 开启后不能关闭
        onlyPro: false,
        needApply: false,
        switchTips: {
          // 点击开关后的弹窗文案
          turnEnable: {
            main: $t('marketing.commons.qrsfkq_605e6c'),
            desc: '',
          },
          turnDisable: {
            main: $t('marketing.commons.qrsfgb_543f9a'),
            desc: '',
          },
        },
        icon: require('@/assets/images/plugins/xiaobing.png'),
      },
    ],
  },
}
