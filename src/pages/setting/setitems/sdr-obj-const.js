export const sdrObjConst = {
  IDEAL_CUSTOMER_PROFILE_OBJ: 'IdealCustomerProfileObj', // 理想客户画像
  PRODUCT_CANVAS_OBJ: 'ProductCanvasObj', // 营销产品画布
  SDR_SCORING_MODEL_OBJ: 'SDRScoringModelObj', // SDR评分模型
  SDR_SCORING_DIMENSION_OBJ: 'SDRScoringDimensionObj', // SDR评分维度
  SDR_SCORING_OUTCOME_OBJ: 'SDRScoringOutcomeObj', // SDR评分模型结果
  SDR_Dimension_Score_Item_Obj: 'SDRDimensionScoreItemObj', // SDR评分维度分值
  MARKETING_USER_INSIGHT_OBJ: 'MarketingUserInsightObj', // 营销用户洞察
  SDR_CONVERSATION_CASE_OBJ: 'SDRConversationCaseObj', // SDR沟通话术案例
  SDR_BUSINESS_RULE_OBJ: 'SDRBusinessRuleObj', // SDR业务规则
  SDR_TOPIC_OBJ: 'SDRTopicObj', // SDR话题库
  SDR_TOPIC_RECOMMENDATION_OBJ: 'SDRTopicRecommendationObj', // SDR推荐话题
}

export const aiSteps = [
  {
    id: 'step-1',
    title: $t('marketing.commons.dbszlxkhhx_ccd0e4'),
    description: $t('marketing.commons.dylxkhhxjz_d8f5d4'),
    selected: false,
    showSetting: true,
    routeTo: { name: 'setting-marketing-ai' },
    objApiName: sdrObjConst.IDEAL_CUSTOMER_PROFILE_OBJ,
  },
  {
    id: 'step-2',
    title: $t('marketing.commons.dbszyxcphb_e2ac07'),
    description: $t('marketing.commons.dyqycphfwx_7ad6e0'),
    selected: false,
    showSetting: true,
    routeTo: { name: 'setting-marketing-ai' },
    objApiName: sdrObjConst.PRODUCT_CANVAS_OBJ,
  },
]
