<template>
    <div class="m-marketingevent-setting-wrapper">
      <div class="setitem">
        <div class="setitle">
          {{ $t('marketing.commons.shpz_58ef78') }}
        </div>
        <div class="setdesc">
          {{ $t('marketing.pages.setting.ryyxgkxqry_0ebde7') }}
          <el-link
            class="link"
            href="https://help.fxiaoke.com/9adk/bfce/1a8d/22de"
            target="_blank"
          >
            {{ $t('marketing.pages.setting.splcpzzy_e0160d') }}
          </el-link>
        </div>
        <div class="review-panel">
          <div class="review-panel__item">
            <div class="review-panel__title">
              {{ $t('marketing.pages.setting.schdsh_37e977') }}
            </div>
            <div class="review-panel__desc">
              {{ $t('marketing.pages.setting.rdschdpzls_b7575a') }}
            </div>
            <el-switch
              v-model="marketingEventAudit"
              class="review-panel__switch"
              size="small"
              @change="(val) => handleStatusUpdate(val, 'marketingEventAudit')"
            />
          </div>
          <div class="review-panel__item">
            <div class="review-panel__title">
              {{ $t('marketing.pages.setting.yxtgsh_16360f') }}
            </div>
            <div class="review-panel__desc">
              {{ $t('marketing.pages.setting.rdyxhdpzls_9e08d5') }}
            </div>
            <el-switch
              v-model="marketingActivityAudit"
              class="review-panel__switch"
              size="small"
              @change="(val) => handleStatusUpdate(val, 'marketingActivityAudit')"
            />
          </div>
        </div>
      </div>
      <div class="setitem">
        <div class="setitle">
          {{ $t('marketing.commons.yxrlsz_758e0b') }}
        </div>
        <div class="setdesc">
          {{ $t('marketing.commons.zdybtlxsch_38e232') }}
        </div>
        <div class="setstat">
          <span>
            <router-link :to="{ name: 'marketing-calendar-setting' }">{{ $t('marketing.commons.sz_e366cc') }}</router-link>
          </span>
        </div>
      </div>
      <div
        v-loading="loading"
        class="setitem"
      >
        <div class="setitle">
          {{ $t('marketing.pages.setting.hdxsyschdl_4cf5e9') }}
        </div>
        <div class="map-wrapper">
          <div
            v-for="(item, index) in crmMapMarketingList"
            :key="index"
            class="item-wrapper"
          >
            <div class="marekting-wrapper">
              <i
                :class="['icon-img','iconfont', item.icon]"
                v-if="item.isIconfont"
                :style="{ color: item.color }"
              />
              <img
                class="icon-img"
                v-else
                :src="item.icon"
                alt=""
              >
              <div class="name">
                {{ item.name }}
              </div>
            </div>
            <div class="arrow_wrapper">
              <div class="triangle" />
              <div class="rectangle" />
            </div>
            <div class="des">
              {{ $t('marketing.pages.setting.schddhdlx_3f9bca') }}
            </div>
            <Tooltip
              :content="item.crmName"
              placement="top"
            >
              <div class="crm-text">
                {{ item.crmName }}
              </div>
            </Tooltip>
            <span
              v-if="item.showMarketingEventSetting"
              class="setMap"
              @click="handleMarketingEventSet(item)"
            >{{ $t('marketing.commons.szys_67653e') }}</span>
          </div>
        </div>
      </div>
       <div
      v-loading="formLoading"
      class="setitem"
    >
      <div class="setitle">
        {{ $t('marketing.pages.setting.yxcjhdzymb_95ab79') }}
      </div>
      <div
        class="setdesc"
        style="margin: 10px 0 13px 0"
      >
        {{ $t('marketing.pages.setting.xjhyhzbhds_370e2b') }}
      </div>
      <div class="trigger-tpl">
        <template v-for="(item, index) in marketingSceneHexagonTplMaps">
          <div
            v-if="item.show"
            :key="index"
            class="trigger-tpl-item"
          >
            <div class="trigger-tpl-item-header">
              <div class="trigger-tpl-item-header-title">
                <img
                  class="icon-img"
                  :src="item.icon"
                  alt=""
                >
                <span>{{ item.name }}</span>
              </div>
              <template v-if="index === 'live'">
                <div class="block-line"></div>
                <fx-tabs v-model="item.activeTab" class="trigger-live-type-tabs">
                  <template v-for="tab in item.tabs">
                    <fx-tab-pane
                      :key="tab.key"
                      v-if="tab.show"
                      :label="tab.label"
                      :name="tab.key"
                    >
                    </fx-tab-pane>
                  </template>
                </fx-tabs>
              </template>
              <div
                class="add-trigger-btn"
                @click="handleAddHexagonTpl(item.activeTab || index)"
              >
                <i class="el-icon-plus" />{{ $t('marketing.pages.setting.tjysmb_73400a') }}
              </div>
            </div>
            <template v-if="index === 'live'">
              <div
                v-if="getCurrentTabData(item).length"
                class="trigger-tpl-content"
              >
                <div
                  v-for="tpl in getCurrentTabData(item)"
                  :key="tpl.id"
                  class="trigger-tpl-subitem"
                >
                  <div class="trigger-tpl-subitem-title">
                    {{ tpl.name }}
                    <span
                      v-if="tpl.isDefault"
                      class="trigger-tpl-subitem-title-icon"
                    >{{ $t('marketing.pages.setting.mr_18c634') }}</span>
                  </div>
                  <div class="trigger-tpl-subitem-opts">
                    <span
                      class="trigger-tpl-subitem-btn hexagon-tpl-subitem-btn"
                      @click="handleHexagonTplEdit(tpl.id, index)"
                    >{{ $t('marketing.commons.bj_95b351') }}</span>
                    <span
                      v-if="tpl.formId"
                      class="trigger-tpl-subitem-btn hexagon-tpl-subitem-btn"
                      @click="handleHexagonMapping(tpl)"
                    >{{ $t('marketing.pages.setting.szbdxsmrys_8ebd4d') }}
                      <Tooltip
                        v-if="tpl.formId && !tpl.hadCrmMapping"
                        effect="dark"
                        :content="$t('marketing.commons.hwwcxssz_2a9c1d')"
                        placement="top"
                      >
                        <i :class="['el-icon-warning-outline']" />
                      </Tooltip>
                    </span>
                    <fx-dropdown size="size" @command="(command)=>handleTplCommand(command,tpl)">
                      <span class="el-dropdown-link">
                        {{ $t('marketing.commons.gd_0ec9ea') }}<i class="fx-icon-arrow-down el-icon--right"></i>
                      </span>
                      <fx-dropdown-menu slot="dropdown">
                        <fx-dropdown-item command="default" v-if="!tpl.isDefault">{{ $t('marketing.pages.setting.swmrmb_6c889a') }}</fx-dropdown-item>
                        <fx-dropdown-item command="copy">{{ $t('marketing.commons.fz_79d3ab') }}</fx-dropdown-item>
                        <fx-dropdown-item command="delete" v-if="!tpl.isDefault">{{ $t('marketing.commons.sc_2f4aad') }}</fx-dropdown-item>
                      </fx-dropdown-menu>
                    </fx-dropdown>
                  </div>
                </div>
              </div>
            </template>
            <template v-else>
              <div
                v-if="item.data.length"
                class="trigger-tpl-content"
              >
                <div
                  v-for="iitem in item.data"
                  :key="iitem.id"
                  class="trigger-tpl-subitem"
                >
                  <div class="trigger-tpl-subitem-title">
                    {{ iitem.name }}
                    <span
                      v-if="iitem.isDefault"
                      class="trigger-tpl-subitem-title-icon"
                    >{{ $t('marketing.pages.setting.mr_18c634') }}</span>
                  </div>
                  <div class="trigger-tpl-subitem-opts">
                    <span
                      class="trigger-tpl-subitem-btn hexagon-tpl-subitem-btn"
                      @click="handleHexagonTplEdit(iitem.id, index)"
                    >{{ $t('marketing.commons.bj_95b351') }}</span>
                    <span
                      v-if="iitem.formId"
                      class="trigger-tpl-subitem-btn hexagon-tpl-subitem-btn"
                      @click="handleHexagonMapping(iitem)"
                    >{{ $t('marketing.pages.setting.szbdxsmrys_8ebd4d') }}
                      <Tooltip
                        v-if="iitem.formId && !iitem.hadCrmMapping"
                        effect="dark"
                        :content="$t('marketing.commons.hwwcxssz_2a9c1d')"
                        placement="top"
                      >
                        <i :class="['el-icon-warning-outline']" />
                      </Tooltip>
                    </span>
                    <fx-dropdown size="size" @command="(command)=>handleTplCommand(command,iitem)">
                      <span class="el-dropdown-link">
                        {{ $t('marketing.commons.gd_0ec9ea') }}<i class="fx-icon-arrow-down el-icon--right"></i>
                      </span>
                      <fx-dropdown-menu slot="dropdown">
                        <fx-dropdown-item command="default" v-if="!iitem.isDefault">{{ $t('marketing.pages.setting.swmrmb_6c889a') }}</fx-dropdown-item>
                        <fx-dropdown-item command="copy">{{ $t('marketing.commons.fz_79d3ab') }}</fx-dropdown-item>
                        <fx-dropdown-item command="delete" v-if="!iitem.isDefault">{{ $t('marketing.commons.sc_2f4aad') }}</fx-dropdown-item>
                      </fx-dropdown-menu>
                    </fx-dropdown>
                  </div>
                </div>
              </div>
            </template>
          </div>
        </template>
      </div>
    </div>
      <div
        v-loading="layoutSettingLoading"
        class="setitem"
      >
        <div class="setitle">
          {{ $t('marketing.pages.setting.schdywzjbj_26c5e3') }}
        </div>
        <div class="setdesc">
          <span>{{ $t('marketing.pages.setting.zcahdlxzdy_05045f') }}</span>
        </div>
        <div
          class="add-trigger-btn"
          style="margin-top: 10px"
          @click="handleAddLayout"
        >
          <i class="el-icon-plus" />{{ $t('marketing.pages.setting.tjhdcjbjpz_4b66aa') }}
        </div>
        <v-table
          v-if="layoutTableData.length"
          ref="table"
          class="layout-table"
          :columns="layoutColumns"
          :empty-text="$t('marketing.commons.zwsj_21efd8')"
          :data="layoutTableData"
          :row-style="{ cursor: 'pointer' }"
          @custom:delete-action="handleLayoutDelete"
          @custom:edit-action="handleLayoutEdit"
        />
      </div>
      <div
        v-loading="mappingLoading"
        class="setitem"
      >
        <div class="setitle">
          {{ $t('marketing.pages.setting.bdcrxsgjsj_ea0509') }}<el-switch
            v-model="openMergePhone"
            style="margin-left: 16px"
            size="small"
            @change="(val) => handleStatusUpdate(val, 'openMergePhone')"
          />
        </div>
        <div class="setdesc">
          <span>{{ $t('marketing.pages.setting.hdyxhyyxzb_b71a3b') }}</span>
        </div>
      </div>
      <div
        v-loading="triggerLoading"
        class="setitem"
      >
        <div class="setitle">
          {{ $t('marketing.pages.setting.yxcjmrsz_92e08d') }}
        </div>
        <div
          class="setdesc"
          style="margin: 10px 0 13px 0"
        >
          {{ $t('marketing.pages.setting.zdaccszsch_1f7ce1') }}
        </div>
        <div class="trigger-tpl">
          <div
            v-for="(item, index) in marketingSceneTriggerTplMaps"
            :key="index"
            class="trigger-tpl-item"
          >
            <div class="trigger-tpl-item-header">
              <div class="trigger-tpl-item-header-title">
                <img
                  class="icon-img"
                  :src="item.icon"
                  alt=""
                >{{ item.name }}
              </div>
              <div
                class="add-trigger-btn"
                @click="handleTriggerTplEdit(item.sceneType)"
              >
                <i class="el-icon-plus" />{{ $t('marketing.pages.setting.tjcj_338412') }}
              </div>
            </div>
            <div
              v-if="item.list.length"
              class="trigger-tpl-content"
            >
              <div
                v-for="iitem in item.list"
                :key="iitem.triggerId"
                class="trigger-tpl-subitem"
                @mouseover="showEdit(iitem)"
                @mouseout="offEdit(iitem)"
              >
                <div class="trigger-tpl-subitem-title">
                  {{ $t('marketing.pages.setting.mc_5ad10b') }}{{ iitem.name }}
                </div>
                <div
                  v-show="iitem.active"
                  class="trigger-tpl-subitem-opts"
                >
                  <span
                    class="trigger-tpl-subitem-btn"
                    @click="handleTriggerTplEdit(item.sceneType, iitem.triggerId)"
                  >{{ $t('marketing.commons.bj_95b351') }}</span>
                  <span
                    class="trigger-tpl-subitem-btn"
                    @click="handleTriggerTplDelete(iitem.triggerId)"
                  >{{ $t('marketing.commons.sc_2f4aad') }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="setitem">
        <div class="setitle">
          {{ $t('marketing.pages.setting.schddddfx_eb8f8a') }}
        </div>
        <MarketingAnalysisSetting />
      </div>
      <settingMapDialog
        v-if="isShowMapDialog"
        :visible="isShowMapDialog"
        :mapping="mapping"
        :marketing-event-fields="marketingEventFields"
        :open-merge-phone="openMergePhone"
        :activityType="currentActivityType"
        :disabled-marketing-event-fields="disabledMarketingEventFields"
        @submit="handleSubmit"
        @update:visible="isShowMapDialog = false"
      />
      <site-setting-dialog
        v-if="settingDialogVisible"
        :visible.sync="settingDialogVisible"
        :site-id="curSiteId"
        :form-id="curFormId"
        :form-usage="curFormUsage"
        :showActivityMemberSet="true"
        @update:submit="handleSettingSubmit"
      />
      <Dialog
        :title="$t('marketing.pages.setting.pzhdxqgncd_4ec410')"
        :ok-text="$t('marketing.commons.bc_be5fbb')"
        width="900px"
        :visible="layoutDialogVisible"
        @onSubmit="handleLayoutSubmit"
        @onClose="layoutDialogVisible = false"
      >
        <div class="menu-layout-dialog__wrapper">
          <div>
            <div>{{ $t('marketing.pages.setting.xzhdlx_964a87') }}</div>
            <el-checkbox-group
              v-model="layoutCheckedList"
              class="menu-checkbox"
            >
              <el-checkbox
                v-for="item in layoutCheckboxList"
                :key="item.value"
                class="menu-checkbox-item"
                :label="item.value"
                :disabled="item.disabled"
              >
                {{ item.label }}
              </el-checkbox>
            </el-checkbox-group>
          </div>
          <div class="menu-area">
            <div class="menu-tips">
              {{ $t('marketing.pages.setting.tdpzcdsfzh_fe3966') }}
            </div>
            <div class="menu-group">
              <div class="menu-title">
                {{ $t('marketing.pages.setting.zdh_2b2c9b') }}
              </div>
              <vue-draggable
                v-model="mainMenusData"
                class="draggable"
                v-bind="layoutDragOptions"
                :force-fallback="true"
                handle=".menu-item"
                @start="drag = true"
                @end="drag = false"
                @change="handleMainMenuChange"
              >
                <transition-group
                  :class="layoutDrag ? 'menu-wrapper drag' : 'menu-wrapper'"
                  tag="div"
                  type="transition"
                >
                  <div
                    v-for="item in mainMenusData"
                    :key="item.id"
                    class="menu-item"
                  >
                    {{ item.name }}
                  </div>
                </transition-group>
              </vue-draggable>
            </div>
            <div class="menu-group">
              <div class="menu-title">
                {{ $t('marketing.commons.gd_0ec9ea') }}
              </div>
              <vue-draggable
                v-model="moreMenusData"
                class="draggable"
                v-bind="layoutDragOptions"
                :force-fallback="true"
                handle=".menu-item"
                @start="drag = true"
                @end="drag = false"
                @change="handleMainMenuChange"
              >
                <transition-group
                  :class="layoutDrag ? 'menu-wrapper drag' : 'menu-wrapper'"
                  tag="div"
                  type="transition"
                >
                  <div
                    v-for="item in moreMenusData"
                    :key="item.id"
                    class="menu-item"
                  >
                    {{ item.name }}
                  </div>
                </transition-group>
              </vue-draggable>
            </div>
            <div class="menu-group">
              <div class="menu-title">
                {{ $t('marketing.commons.ygb_9c5850') }}
              </div>
              <vue-draggable
                v-model="closedMenusData"
                class="draggable"
                v-bind="layoutDragOptions"
                :force-fallback="true"
                handle=".menu-item"
                @start="drag = true"
                @end="drag = false"
                @change="handleMainMenuChange"
              >
                <transition-group
                  :class="layoutDrag ? 'menu-wrapper drag' : 'menu-wrapper'"
                  tag="div"
                  type="transition"
                >
                  <div
                    v-for="item in closedMenusData"
                    :key="item.id"
                    class="menu-item"
                  >
                    {{ item.name }}
                  </div>
                </transition-group>
              </vue-draggable>
            </div>
          </div>
        </div>
      </Dialog>
    </div>
</template>

<script>
import VueDraggable from 'vuedraggable'
import { mapState, mapActions } from 'vuex'
import http from '@/services/http/index.js'
import settingMapDialog from './components/seting-map-diaog.vue'
import { confirm } from '@/utils/globals.js'
import SiteSettingDialog from '@/pages/site/site-setting-dialog/index.vue'
import VTable from '@/components/table-ex/index.vue'
import { formatStringByEmpty } from '@/utils/index.js'
import crmLayoutMenusMixin from '@/mixins/crm-layout-menus.js'
import Dialog from '@/components/dialog/index.vue'
import { messageBoxConfirm } from '@/utils/message-box.js'
import MarketingAnalysisSetting from './components/marketing-analysis-setting.vue'

import icon1 from '@/assets/images/icon/menu-content.png'
import icon2 from '@/assets/images/icon/menu-meeting.png'
import icon3 from '@/assets/images/icon/liev-marketing-icon.png'
import icon4 from '@/assets/images/icon/ad-marketing-menu.png'
import icon5 from '@/assets/images/icon/target-marketing-icon.png'
import kisvData from '@/modules/kisv-data.js'

const formatter = (rowData, column, cellValue) => formatStringByEmpty(cellValue)

export default {
  components: {
    SiteSettingDialog,
    ElSwitch: FxUI.Switch,
    settingMapDialog,
    Tooltip: FxUI.Tooltip,
    VTable,
    Dialog,
    ElCheckboxGroup: FxUI.CheckboxGroup,
    ElCheckbox: FxUI.Checkbox,
    ElLink: FxUI.Link,
    VueDraggable,
    MarketingAnalysisSetting,
    FxTabs: FxUI.Tabs,
    FxTabPane: FxUI.TabPane,
  },
  mixins: [crmLayoutMenusMixin],
  data() {
    return {
      // kisvData: kisvData.datas,
      curSiteId: '',
      curFormId: '',
      curFormUsage: '',
      settingDialogVisible: false,
      marketingEventFields: [],
      mapping: [],
      crmMapMarketingList: [
        {
          icon: 'iconcontent_marketing',
          color: '#0C6CFF',
          isIconfont: true,
          name: $t('marketing.commons.xsyx_6c9e91'),
          crmName: '',
          showMarketingEventSetting: true,
          mapping: [],
          activityType: 0
        },
        {
          icon: 'iconmultivenue_marketing',
          color: '#189DFF',
          isIconfont: true,
          name: $t('marketing.pages.setting.dhdzh_0bf57e'),
          crmName: '',
          showMarketingEventSetting: true,
          mapping: [],
          activityType: 6
        },
        {
          icon: icon3,
          isIconfont: false,
          name: $t('marketing.commons.zbyx_a9fa5d'),
          crmName: '',
          showMarketingEventSetting: true,
          mapping: [],
          activityType: 1
        },
        {
          icon: icon2,
          isIconfont: false,
          name: $t('marketing.commons.hyyx_5f60fd'),
          crmName: '',
          showMarketingEventSetting: true,
          mapping: [],
          activityType: 2
        },
        {
          icon: 'iconperiodicity_marketing',
          color: '#30C776',
          isIconfont: true,
          name: $t('marketing.commons.zqxyyhd_ce9ecc'),
          crmName: '',
          showMarketingEventSetting: false,
          mapping: [],
          activityType: 7
        },
        {
          icon: 'icononce_marketing',
          color: '#FF8000',
          isIconfont: true,
          name: $t('marketing.commons.dcyyhd_cad983'),
          crmName: '',
          showMarketingEventSetting: false,
          mapping: [],
          activityType: 8
        },
        // 广告营销没定义activityType  为了方便管理 前端自己加的activityType定义为advertising_marketing  如果后续广告支持映射 记得修改
        {
          icon: icon4,
          isIconfont: false,
          name: $t('marketing.commons.ggyx_0271c7'),
          crmName: '',
          showMarketingEventSetting: false,
          mapping: [],
          activityType: 'advertising_marketing'
        }
      ],
      marketingSceneHexagonTplMaps: {
        live: {
          icon: icon3,
          name: $t('marketing.commons.zbyx_a9fa5d'),
          show: kisvData.datas.pluginInfo.livingMarketing,
          tabs: [
            {
              label: $t('marketing.pages.setting.sphzb_4ecb85'),
              key: 'channel_live',
              data: [],
              show: kisvData.datas.uinfo.livePluginConfigs['503'].status
            },
            {
              label: $t('marketing.pages.setting.qtzb_c10c9e'),
              key: 'live',
              data: [],
              show: true
            }
          ],
          activeTab: kisvData.datas.uinfo.livePluginConfigs['503'].status ? 'channel_live' : 'live'
        },
        conference: {
          icon: icon2,
          show: kisvData.datas.pluginInfo.meetingMarketing,
          name: $t('marketing.commons.hyyx_5f60fd'),
          data: []
        }
      },
      marketingSceneTriggerTplMaps: {
        live: {
          icon: icon3, name: $t('marketing.commons.zbyx_a9fa5d'), sceneType: 'live', list: [],
        },
        conference: {
          icon: icon2,
          name: $t('marketing.commons.hyyx_5f60fd'),
          sceneType: 'conference',
          list: [],
        },
      },
      isShowMapDialog: false,
      loading: true,
      formLoading: true,
      triggerLoading: true,
      mappingLoading: true,
      layoutColumns: [
        {
          prop: 'types',
          label: $t('marketing.pages.setting.schdlx_80dc90'),
          minWidth: 200,
          formatter,
        },
        {
          prop: 'menus',
          label: $t('marketing.pages.setting.dhcd_ff36f6'),
          minWidth: 400,
          formatter,
        },
        {
          prop: 'operation',
          label: $t('marketing.commons.cz_2b6bc0'),
          minWidth: 100,
          exComponent: 'operation',
          formatter,
        },
      ],
      layoutData: [],
      layoutId: '',
      layoutSettingLoading: true,
      layoutDialogMode: 1,
      layoutDialogVisible: false,
      layoutDefaultChecked: [],
      layoutDisabledList: [],
      layoutCheckedList: [],
      layoutDrag: false,
      layoutDialogLoading: true, // TODO siuyoon
      disabledMarketingEventFields: [],
      currentActivityType: 0
    }
  },
  computed: {
    ...mapState('MarketingEventSet', ['allActivityTypeMapping', 'openMergePhone', 'marketingActivityAudit', 'marketingEventAudit']),
    layoutTableData() {
      const data = JSON.parse(JSON.stringify(this.layoutData))
      const results = data.map(item => {
        const _pushItem = { ...item }
        const { activityType, mainNavigationBar, secondaryNavigationBar } = item.customTagSetting
        _pushItem.types = this.convertKeys(activityType, this.campaignTypes, 'value')
          .map(type => type.label)
           .join('、')
        let allMenus = []

        if (mainNavigationBar && mainNavigationBar.length) {
          allMenus = allMenus.concat(mainNavigationBar)
        }

        if (mainNavigationBar && secondaryNavigationBar.length) {
          allMenus = allMenus.concat(secondaryNavigationBar)
        }

        _pushItem.menus = this.convertKeys(allMenus, this.appDropListItems, 'id')
          .map(menu => menu.name)
           .join('、')
        _pushItem.operation = [
          { id: 'edit', name: $t('marketing.commons.bj_95b351') },
          { id: 'delete', name: $t('marketing.commons.sc_2f4aad') },
        ]
        return _pushItem
      })
      return results
    },
    layoutDragOptions() {
      return {
        animation: 200,
        group: 'menu',
        disabled: false,
      }
    },
    layoutCheckboxList() {
      const _map = new Map(this.campaignTypes.map(item => [item.value, item]))
      this.layoutDisabledList.forEach(item => {
        if (_map.has(item)) {
          _map.set(item, { ..._map.get(item), disabled: true })
        }
      })
      return Array.from(_map, ([_, v]) => v)
    },
  },
  watch: {
    layoutDefaultChecked(newVal) {
      this.layoutCheckedList = newVal || []
    },
    mainMenusData(newVal) {
      console.log('mainMenusData: ', newVal)
    },
  },
  created() {
    this.queryAllMarketingEventCommonSetting().then(() => {
      this.getMarketingEventTypeField()
    })
    this.getSceneHexagonTemplates()
    this.getSceneTriggerTemplates()
    this.getMarketingEvenCustomTagSetting()
  },
  methods: {
    ...mapActions('MarketingEventSet', ['queryAllMarketingEventCommonSetting',]),
    showEdit(i) {
      this.$set(i, 'active', true)
    },
    offEdit(i) {
      this.$set(i, 'active', false)
    },
    handleHexagonMapping(data) {
      this.settingDialogVisible = true
      this.curFormId = data.formId
      this.curFormUsage = data.formUsage
    },
    handleSettingSubmit() {
      this.getSceneHexagonTemplates()
    },
    handleHexagonTplEdit(id,sceneType) {
      this.$router.push({
        name: 'site-design',
        params: {
          siteId: 'template',
          templateSiteId: id || null,
        },
        query: {
          redirect: JSON.stringify({
            name: 'setting-setitems',
            query: {
              type: 'v-marketingevent-set',
            },
          }),
          sceneType,
          templateType: 'activityComp',
        },
      })
    },
    handleHexagonSetDefaultTpl(id) {
      confirm($t('marketing.pages.setting.qrjgbdmbsz_21716b'), $t('marketing.commons.ts_02d981'), {}).then(() => {
        http
          .setHexagonDefaultTemplate({
            id,
          })
          .then(({ errCode, errMsg }) => {
            if (errCode === 0) {
              this.getSceneHexagonTemplates()
              FxUI.Message.success($t('marketing.commons.szcg_f6088e'))
            } else {
              FxUI.Message.error(errMsg || $t('marketing.commons.szsb_9f9603'))
            }
          })
      })
    },
    handleTplCommand(command,item){
      switch(command){
        case 'default':
          this.handleHexagonSetDefaultTpl(item.id)
          break
        case 'copy':
          this.handleHexagonCopyTpl(item)
          break
        case 'delete':
          this.handleHexagonTplDelete(item.id)
          break
        default:
          break
      }
    },
    async handleHexagonCopyTpl(item){
      const params = {
        id: item.id,
        name: `${item.name}${$t('marketing.pages.setting.fb_f6ce05')}`,
        checkMarketingTemplate: true,
      }
      const { errCode, errMsg } = await http.copyTemplate(params)
      if (errCode === 0) {
        FxUI.Message.success($t('marketing.commons.fzcg_20a495'))
        this.getSceneHexagonTemplates()
      } else {
        FxUI.Message.error(errMsg || $t('marketing.commons.fzsb_5154ae'))
      }
    }, 
    async handleAddHexagonTpl(sceneType) {
      if (!sceneType) {
        FxUI.Message.warning($t('marketing.commons.zwmb_b6ae68'));
        return;
      }

      const templateList = sceneType === 'live' || sceneType === 'channel_live' 
        ? this.getCurrentTabData(this.marketingSceneHexagonTplMaps.live)
        : this.marketingSceneHexagonTplMaps[sceneType]?.data;

      if (!Array.isArray(templateList) || templateList.length === 0) {
        FxUI.Message.warning($t('marketing.commons.zwmb_b6ae68'));
        return;
      }

      try {
        await confirm($t('marketing.pages.setting.sfxyxjygys_df889a'), $t('marketing.commons.ts_02d981'), {});
        const copyParams = {
          id: templateList[0].id,
          name: `${templateList[0].name}${$t('marketing.pages.setting.fb_f6ce05')}${templateList.length}`,
          checkMarketingTemplate: true,
        }
        const { errCode, errMsg } = await http.copyTemplate(copyParams);
        
        if (errCode === 0) {
          await this.getSceneHexagonTemplates();
          FxUI.Message.success($t('marketing.commons.xjcg_e477bf'));
        } else {
          FxUI.Message.error(errMsg || $t('marketing.pages.setting.xjsb_c71e1b'));
        }

      } catch (err) {
        console.error('Add hexagon template failed:', err);
      }
    },
    handleHexagonTplDelete(id) {
      confirm($t('marketing.pages.setting.qrscgbdmb_2e12e8'), $t('marketing.commons.ts_02d981'), {}).then(() => {
        http
          .deleteTemplateSite({
            id,
          })
          .then(({ errCode, errMsg }) => {
            if (errCode === 0) {
              this.getSceneHexagonTemplates()
              FxUI.Message.success($t('marketing.commons.sccg_43593d'))
            } else {
              FxUI.Message.error(errMsg || $t('marketing.commons.scsb_acf066'))
            }
          })
      })
    },
    handleTriggerTplEdit(sceneType, triggerId) {
      this.$router.push({
        name: 'trigger-create',
        query: {
          id: triggerId || '',
          sceneType,
          template: 1,
        },
      })
    },
    handleTriggerTplDelete(triggerId) {
      confirm($t('marketing.pages.setting.qrscmb_08ba1c'), $t('marketing.commons.ts_02d981'), {}).then(() => {
        http
          .deleteTrigger({
            id: triggerId,
          })
          .then(({ errCode, errMsg }) => {
            if (errCode === 0) {
              FxUI.Message.success($t('marketing.commons.sccg_43593d'))
              this.getSceneTriggerTemplates()
            } else {
              FxUI.Message.error(errMsg || $t('marketing.commons.scsb_acf066'))
            }
          })
      })
    },
    getSceneHexagonTemplates() {
      http.getSceneHexagonTemplates().then(({ errCode, data = {} }) => {
        this.formLoading = false
        if (errCode === 0) {
          const dataMaps = this.marketingSceneHexagonTplMaps
          const resultData = data.simpleResult || {}
          // 处理直播数据
          if (resultData.live || resultData.channel_live) {
            const liveTabs = dataMaps.live.tabs
            liveTabs.forEach(tab => {
              const tabData = resultData[tab.key] || []
              // 处理默认模板排序
              tabData.forEach((item, index) => {
                if (item.isDefault) {
                  const defaultTemplate = tabData.splice(index, 1)[0]
                  tabData.unshift(defaultTemplate)
                }
              })
              tab.data = tabData
            })
          }

          // 处理会议数据
          if (resultData.conference) {
            dataMaps.conference.data = resultData.conference || []
            const conferenceData = dataMaps.conference.data
            conferenceData.forEach((item, index) => {
              if(item.isDefault) {
                const defaultTemplate = conferenceData.splice(index, 1)[0]
                conferenceData.unshift(defaultTemplate)
                }
              })
            }
          console.log(dataMaps,"dataMapsdataMapsdataMaps")
          this.marketingSceneHexagonTplMaps = dataMaps
        }
      })
    },
    getSceneTriggerTemplates() {
      http.getSceneTriggerTemplates().then(({ errCode, data = {} }) => {
        this.triggerLoading = false
        if (errCode === 0) {
          const dataMaps = this.marketingSceneTriggerTplMaps
          const resultData = data.simpleResult || {}
          Object.keys(resultData).forEach(key => {
            if (dataMaps[key]) {
              dataMaps[key].list = resultData[key].map(i => {
                i.active = false
                return i
              }) || []
            }
          })
          this.marketingSceneTriggerTplMaps = dataMaps
        }
      })
    },
    handleSubmit() {
      this.isShowMapDialog = false
      this.queryAllMarketingEventCommonSetting().then(()=>{
        this.getMarketingEventCommonSetting()
      })
    },
    getMarketingEventCommonSetting() {
      // type: 0 线上营销  1：直播  2：会议营销  6：多活动  7：周期性运营活动  8：单次运营活动
      this.mappingLoading = false
      if (this.allActivityTypeMapping && this.allActivityTypeMapping.length) {
        this.allActivityTypeMapping.forEach(item => {
          // 排除单次目标人群运营和周期性运营活动
          if(item.activityType !== 7 && item.activityType !== 8){
            this.crmMapMarketingList.find(i => {
              if (i.activityType === item.activityType) {
                i.mapping = item.mapping
                i.crmName = item.mapping.map(i => i.fieldName).join('、')
                }
              })
            }
          })
        }
    },
    getMarketingEventTypeField() {
      http.getMarketingEventTypeField().then(res => {
        this.loading = false
        this.getMarketingEventCommonSetting()
        if (res && res.errCode === 0) {
          this.marketingEventFields = res.data
          let foundCount = 0;
          this.marketingEventFields.some(item => {
            // 只有目标人群运营和周期性运营活动以及广告需要配置crmname 其余的都有默认映射（或者是配置了映射）
            if (item.apiName === 'once') {
              // 目标人群运营
              this.crmMapMarketingList.find(i => i.activityType === 8).crmName += item.labelName
              foundCount++
            } else if (item.apiName === 'periodicity') {
              // 目标人群运营
              this.crmMapMarketingList.find(i=>i.activityType === 7).crmName += item.labelName
              foundCount++
            } else if (item.apiName === 'advertising_marketing') {
              // 广告营销
              this.crmMapMarketingList.find(i=>i.activityType === 'advertising_marketing').crmName = item.labelName
              foundCount++
            }
            return foundCount === 3
          })
        }
      })
    },
    async handleStatusUpdate(val, field) {
      const {
        allActivityTypeMapping, openMergePhone, marketingActivityAudit, marketingEventAudit,
      } = this
      if (!allActivityTypeMapping) {
        this[field] = !val
        return
      }
      await http
        .updateMarketingEventCommonSetting({
          activityTypeMapping: allActivityTypeMapping,
          openMergePhone,
          marketingActivityAudit,
          marketingEventAudit,
          [field]: !!val,
        })
        .then(({ errCode, errMsg }) => {
          if (errCode === 0) {
            FxUI.Message.success($t('marketing.commons.szcg_f6088e'))
          } else {
            FxUI.Message.error(errMsg || $t('marketing.commons.szsb_9f9603'))
          }
        })
      this.queryAllMarketingEventCommonSetting()
    },
    handleAddLayout() {
      this.layoutDialogMode = 1
      this.layoutDefaultChecked = []
      this.mainMenus = []
      this.moreMenus = []
      if (this.layoutData && this.layoutData.length) {
        this.layoutDisabledList = this.layoutData.reduce(
          (pre, cur) => pre.concat(cur.customTagSetting.activityType),
          [],
        )
      }
      this.layoutDialogVisible = true
    },
    handleLayoutEdit(row, index) {
      this.layoutId = row.id
      this.layoutDialogMode = 2
      this.layoutDefaultChecked = row.customTagSetting.activityType
      this.mainMenus = row.customTagSetting.mainNavigationBar || []
      this.moreMenus = row.customTagSetting.secondaryNavigationBar || []
      if (this.layoutData && this.layoutData.length) {
        this.layoutDisabledList = this.layoutData
          .filter((_, idx) => index !== idx)
          .reduce((pre, cur) => pre.concat(cur.customTagSetting.activityType), [])
      }
      this.layoutDialogVisible = true
    },
    async handleLayoutDelete(row, index) {
      const result = await messageBoxConfirm({
        mainText: $t('marketing.commons.qdsc_7eee52'),
      })
      if (!result || !row.id) return
      http
        .deleteCustomTag({
          id: row.id,
        })
        .then(({ errCode, errMsg }) => {
          if (errCode === 0) {
            FxUI.Message.success($t('marketing.commons.sccg_43593d'))
            this.getMarketingEvenCustomTagSetting()
          } else {
            FxUI.Message.error(errMsg || $t('marketing.commons.scsb_acf066'))
          }
        })
    },
    getMarketingEvenCustomTagSetting() {
      this.layoutSettingLoading = true
      http.getMarketingEvenCustomTagSetting().then(({ errCode, data }) => {
        if (errCode === 0) {
          this.layoutData = data.marketingEvenCustomTagSettingResults
        }
        this.layoutSettingLoading = false
      })
    },
    async handleLayoutSubmit() {
      const params = {
        activityType: this.layoutCheckedList,
        mainNavigationBar: this.mainMenusData.map(item => item.id),
        secondaryNavigationBar: this.moreMenusData.map(item => item.id),
      }

      if (this.layoutDialogMode === 1) {
        this.insertCustomTag(params)
      } else {
        this.updateCustomTag(params)
      }
    },
    insertCustomTag(params) {
      const { activityType, mainNavigationBar, secondaryNavigationBar } = params
      http
        .insertCustomTag({
          activityType,
          mainNavigationBar,
          secondaryNavigationBar,
        })
        .then(({ errCode, errMsg }) => {
          if (errCode === 0) {
            FxUI.Message.success($t('marketing.commons.xzcg_a5bfd7'))
            this.layoutDialogVisible = false
            this.getMarketingEvenCustomTagSetting()
          } else {
            FxUI.Message.error(errMsg || $t('marketing.pages.setting.xzsb_bac372'))
          }
        })
    },
    updateCustomTag(params) {
      const { activityType, mainNavigationBar, secondaryNavigationBar } = params
      http
        .updateCustomTag({
          id: this.layoutId,
          activityType,
          mainNavigationBar,
          secondaryNavigationBar,
        })
        .then(({ errCode, errMsg }) => {
          if (errCode === 0) {
            FxUI.Message.success($t('marketing.commons.bjcg_3bb47b'))
            this.layoutDialogVisible = false
            this.getMarketingEvenCustomTagSetting()
          } else {
            FxUI.Message.error(errMsg || $t('marketing.pages.setting.bjsb_9304e8'))
          }
        })
    },
    handleMainMenuChange() {},
    getCurrentTabData(item) {
      if (!item.tabs) return item.data || [];
      const activeTab = item.tabs.find(tab => tab.key === item.activeTab);
      return activeTab ? activeTab.data : [];
    },
    handleMarketingEventSet(item) {
      // 每次打开映射弹窗 清空被占用的映射
      this.disabledMarketingEventFields = [];
      this.currentActivityType = item.activityType
      this.mapping = item.mapping
      this.isShowMapDialog = true
      // 被别的活动类型占用的不能再勾选
      const otherData = this.crmMapMarketingList.filter(i => i.activityType !== item.activityType);
      otherData.map(i => {
        this.disabledMarketingEventFields.push(...i.mapping.map(i => i.apiName))
      })
    },
  },
}
</script>

<style lang="less" scoped>
.m-marketingevent-setting-wrapper {
  flex: 1 1 auto;
  background: #fff;
  .setitem {
    position: relative;
    padding: 20px 0;
    margin: 0 20px 0 20px;
    border-bottom: 1px dashed #e9edf5;
    .setitle {
      margin-bottom: 8px;
      font-size: 16px;
      font-weight: bold;
      color: #181c25;
    }
    .setdesc {
      color: #91959e;
      padding-right: 120px;
      font-size: 14px;
    }
    .setstat {
      position: absolute;
      top: 50px;
      right: 8px;
    }
    .layout-table {
      margin-top: 10px;
      max-width: 1216px;
      .el-table__body {
        border-left: 1px solid #ebeef5 !important;
      }
      .el-table__cell {
        border-right: 1px solid #ebeef5 !important;
      }
    }
    .add-trigger-btn {
      font-size: 14px;
      color: @color-link;
      cursor: pointer;
      i {
        font-weight: bold;
        font-size: 14px;
        margin-right: 3px;
      }
    }
    .trigger-tpl {
      display: flex;
      &-item {
        border: 1px solid @border-color-base;
        margin-right: 16px;
        width: 600px;
        border-radius: 8px;
        &-header {
          height: 40px;
          display: flex;
          align-items: center;
          padding: 0 12px;
          background: #f2f3f5;
          &-title {
            font-size: 14px;
            color: @color-title;
            display: flex;
            align-items: center;
          }
          .icon-img {
            width: 18px;
            margin-right: 10px;
          }
        }
      }
      &-content {
        padding: 10px 0;
        border-top: 1px solid @border-color-base;
      }
      &-subitem {
        padding: 8px 12px;
        display: flex;
        align-items: center;
        &:hover {
          background-color: #f0f4fc;
        }
        &-title {
          flex: 1;
          font-size: 14px;
          color: @color-title;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          line-height: 24px;
          &-icon {
            font-size: 12px;
            background-color: var(--color-success01);
            border-radius: 4px;
            color: #fff;
            padding: 1px 4px;
            line-height: 18px;
            color: var(--color-success06);
            display: inline-block;
            margin-left: 10px;
          }
        }
        .trigger-tpl-subitem-opts{
          display: flex;
          gap: 24px;
        }
        &-btn {
          font-size: 14px;
          color: @color-link;
          cursor: pointer;
          margin-left: 20px;
        }
        .hexagon-tpl-subitem-btn {
          margin-left: 0;
        }
        .shu {
          color: var(--color-primary06,#407FFF);
          margin-left: 8px;
          margin-right: 8px;
          margin-top: -1px;
        }
        .el-icon-warning-outline {
          color: #f27474;
          position: relative;
          left: 6px;
          background: #fff;
          font-size: 14px;
          top: 2px;
        }
      }
    }
    .map-wrapper {
      .item-wrapper {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        .marekting-wrapper {
          width: 160px;
          height: 40px;
          display: flex;
          align-items: center;
          margin-right: 23px;
          border: 1px solid #e9edf5;
          padding-left: 15px;
          box-sizing: border-box;
          .name {
            margin-left: 12px;
          }
          .icon-img {
            width: 18px;
          }
        }
        .arrow_wrapper {
          margin-right: 20px;
          display: flex;
          align-items: center;
          .rectangle {
            width: 31px;
            height: 4px;
            background: #d8d8d8;
          }
          .triangle {
            width: 0;
            height: 0;
            border-right: 6px solid #d8d8d8;
            border-top: 6px solid transparent;
            border-bottom: 6px solid transparent;
          }
        }
        .des {
          font-size: 14px;
          color: #545861;
        }
        .crm-text {
          flex: 1;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .btn-opt {
          display: flex;
        }
        .edit {
          margin-left: 40px;
        }
        .setMap {
          color: var(--color-info06,#407FFF);
          cursor: pointer;
          margin-left: 5px;
        }
      }
      .item-wrapper:last-child {
        margin-bottom: 0px;
      }
    }
    .combine-phone {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 14px;

      & > span {
        margin-right: 60px;
      }
    }
    .link {
      color: var(--color-info06,#407FFF);
      text-decoration: none;
    }
    .review-panel {
      margin-top: 18px;
      display: flex;
      font-size: 14px;
      line-height: 18px;
      color: #91959E;

      &__item {
        display: flex;
        max-width: 600px;
        flex: 1;
        flex-direction: column;
        padding: 18px 14px 20px 20px;
        background: #FFFFFF;
        border: 1px solid #DEE1E6;
        border-radius: 4px;
      }

      &__item + .review-panel__item {
        margin-left: 16px;
      }

      &__title {
        font-size: 16px;
        line-height: 20px;
        color: 000;
      }

      &__desc {
        margin-top: 8px;
      }

      &__switch {
        margin-top: 8px;
        align-self: flex-end;
      }
    }
  }
}



.block-line{
  margin: 0 12px 0 20px;
  width: 1px;
  height: 12.5px;
  background-color: var(--color-neutrals05);
}

.trigger-live-type-tabs {
  margin-right: 20px;
  flex: 1;
  display: flex;
  align-items: flex-end;
  height: 100%;
  /deep/ .el-tabs__header {
    margin: 0;
  }
  /deep/ .el-tabs__nav{
    display: flex;
    gap: 8px;
  }
  /deep/ .el-tabs__nav-wrap{
    &::after{
      border-color: transparent;
    }
  }

  /deep/ .el-tabs__item {
    height: 30px;
    line-height: 20px;
    padding: 0;
    font-size: 14px;
    color: var(--color-neutrals19);
    padding: 0 8px;
    &.is-top:last-child {
      padding-right: 14px !important;
    }
    &.is-top:nth-child(2){
      padding-left: 8px !important;
    }
    &.is-active {
      color: var(--color-primary06,#ff8000);
      font-weight: 500;
    }
  }
}

.trigger-tpl-item-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #E9EDF5;
}
.menu-layout-dialog__wrapper {
  max-height: 600px;
  overflow-y: auto;
  .menu-checkbox {
    display: flex;
    flex-wrap: wrap;
    margin-top: 10px;
  }
  .menu-checkbox-item {
    margin-top: 10px;
    width: 260px;
    display: flex;
    align-items: center;

    .el-checkbox__input {
      display: flex;
    }

    .el-checkbox__label {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .menu-area {
    margin: 28px 0 24px;
    user-select: none;
    color: #181c25;
    font-size: 14px;
    line-height: 20px;
  }
  .menu-tips {
    color: #91959e;
  }
  .menu-group {
    margin-top: 20px;
  }
  .menu-title {
    border-left: 3px solid var(--color-primary06,#ff8000);
    height: 16px;
    padding: 0 6px;
  }
  .menu-wrapper {
    display: flex;
    flex-wrap: wrap;
    margin-top: 10px;
    padding: 20px 20px 10px;
    background: #f2f3f5;
    min-height: 80px;
    box-sizing: border-box;
  }
  .drag {
    cursor: move;
  }

  .draggable:empty {
    padding:1rem;
    text-align:center;
  }

  .draggable:empty:before {
      content: 'Drop files here';
  }

  .menu-item {
    padding: 10px 26px;
    margin: 0 10px 10px 0;
    background: #fff;
    cursor: move;
    box-shadow: 0px 1px 0px rgba(139, 139, 139, 0.25);
    border-radius: 3px;
  }
}
</style>
