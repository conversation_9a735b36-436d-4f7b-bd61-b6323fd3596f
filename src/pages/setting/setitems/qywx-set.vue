<template>
    <div class="qywx-infomation">
      <div
        slot="header"
        class="header"
      >
        <content-header
          :title="crumbs"
          :border="true"
        />
      </div>
      <div class="main-content__wrapper">
        <div
          v-if="flag_showLoadingMask"
          class="km-g-loading-mask"
        >
          <span class="loading" />
        </div>
        <div class="left-menu__wrapper">
          <div
            v-for="item in menuLists"
            :key="item.value"
            :class="['menu-item', (item.value === menuActive && 'active') || '']"
            @click="handleChangeActiveItem(item)"
          >
            {{ item.label }}
          </div>
        </div>
        <div
          v-show="menuActive == 0"
          class="qywx-base__setting"
        >
          <div
            v-for="item in infomationMap"
            v-show="!item.isHide"
            :key="item.title"
            class="infomation--box"
          >
            <div class="infomation__title">
              {{ item.title }}
              <el-link
                v-show="item.link && item.link.label"
                type="primary"
                class="title__link"
                target="_blank"
                :href="item.link && item.link.href"
              >
                {{ item.link && item.link.label }}
              </el-link>
            </div>
            <div
              v-for="(fitem, findex) in item.fields"
              :key="findex"
              class="infomation__field"
            >
              <div class="field__key">
                {{ fitem.key }}
              </div>
              <div class="field__value">
                {{ value[findex] }}
              </div>
            </div>
          </div>
          <div class="infomation--buttons">
            <el-button
              v-show="value.isEncrypt == 0"
              class="infomation--button"
              type="primary"
              size="small"
              @click="handleSet"
            >
              {{ $t("marketing.commons.sz_e366cc") }}
            </el-button>
            <el-button
              v-show="value.isEncrypt != 0"
              class="infomation--button"
              type="small"
              size="small"
              @click="handleSet"
            >
              {{ $t("marketing.pages.setting.qwljewm_30d3bb") }}
            </el-button>
            <el-button
              class="infomation--button"
              size="small"
              @click="handleSync"
            >
              {{ $t("marketing.pages.setting.sdtbkh_28906c") }}/{{
                $t("marketing.commons.khq_8ba76f")
              }}
            </el-button>
            <el-button
              class="infomation--button"
              size="small"
              @click="handleSyncAddressBook"
            >
              {{ $t("marketing.pages.setting.tbtxl_a52ea9") }}
            </el-button>
          </div>
        </div>
        <div
          v-show="menuActive == 1"
          class="qywx-infomation__setting"
        >
          <div :class="['setitem', noticeSendTypeEdit ? 'setitem-edit' : '']">
            <div class="setitle">
              {{ $t("marketing.pages.setting.ygyxjhdhwt_9ced01") }}
              <img
                class="seticon"
                src="@/assets/images/icon/icon-question.png"
                @click="openHelp('https://help.fxiaoke.com/93d5/9188/7bd2/a14f')"
              >
            </div>
            <div class="setdesc">
              <CheckboxGroup v-model="noticeSendTypes">
                <Checkbox
                  :label="'app'"
                  disabled
                >
                  {{ $t("marketing.pages.setting.fxxkyytz_8fdfec") }}
                </Checkbox>
                <Checkbox
                  :disabled="!noticeSendTypeEdit"
                  :label="'qywx'"
                >
                  {{ $t("marketing.pages.setting.qwxcxyytz_2a2f7b") }}
                </Checkbox>
                <Checkbox
                  :disabled="!noticeSendTypeEdit"
                  :label="'qywx_h5'"
                >
                  {{ $t("marketing.pages.setting.qwyxzsyytz_deac38") }}
                </Checkbox>
              </CheckboxGroup>
              <div
                v-if="noticeSendTypeEdit"
                class="edit-button-group"
              >
                <el-button
                  type="primary"
                  size="mini"
                  @click="() => toggleNoticeSendTypeSave('confirm')"
                >
                  {{ $t("marketing.commons.bc_be5fbb") }}
                </el-button>
                <el-button
                  size="mini"
                  @click="() => toggleNoticeSendTypeSave('cancel')"
                >
                  {{ $t("marketing.commons.qx_625fb2") }}
                </el-button>
              </div>
            </div>
            <div
              v-if="!noticeSendTypeEdit"
              class="btn-group setting-item-btn"
            >
              <a @click="() => (noticeSendTypeEdit = true)">{{
                $t("marketing.commons.sz_e366cc")
              }}</a>
            </div>
          </div>
          <div :class="['setitem', chatSendTypeEdit ? 'setitem-edit' : '']">
            <div class="setitle">
              {{ $t('marketing.pages.setting.yxzscblfsw_8dea43') }}
              <img
                class="seticon"
                src="@/assets/images/icon/icon-question.png"
                @click="openHelp('https://help.fxiaoke.com/93d5/9188/7bd2/a14f')"
              >
            </div>
            <div class="setdesc">
              <Radio
                v-model="chatToolSentType"
                :label="0"
                :disabled="!chatSendTypeEdit"
              >
                {{ $t('marketing.commons.xcx_0ed510') }}
              </Radio>
              <Radio
                v-model="chatToolSentType"
                :label="1"
                :disabled="!chatSendTypeEdit"
              >
                H5
              </Radio>
              <div
                v-if="chatSendTypeEdit"
                class="edit-button-group"
              >
                <el-button
                  type="primary"
                  size="mini"
                  @click="() => toggleChatSendTypeSave('confirm')"
                >
                  {{ $t("marketing.commons.bc_be5fbb") }}
                </el-button>
                <el-button
                  size="mini"
                  @click="() => toggleChatSendTypeSave('cancel')"
                >
                  {{ $t("marketing.commons.qx_625fb2") }}
                </el-button>
              </div>
            </div>
            <div
              v-if="!chatSendTypeEdit"
              class="btn-group setting-item-btn"
            >
              <a @click="() => (chatSendTypeEdit = true)">{{
                $t("marketing.commons.sz_e366cc")
              }}</a>
            </div>
          </div>
        </div>
      </div>
      <link-qywx-setting-dialog
        v-if="isShowDialog"
        :visible.sync="isShowDialog"
        :default-data="value"
        :is-edit="true"
        @submit="handleSetSubmit"
      />
      <ElDialog
        class="qrCodeElDialog"
        width="647px"
        :title="$t('marketing.commons.ljqywx_d9b29a')"
        append-to-body
        :visible="isShowQrCodeDialog"
        @update:visible="handleCloseDialog"
      >
        <div class="qrCodeimgWrap">
          <p class="qrCodeimgWrapTitle">
            {{ $t("marketing.pages.setting.db_7e9819") }}
          </p>
          <p class="qrCodeimgWrapDesc">
            {{ $t("marketing.pages.setting.qzqywxzygl_a8514b") }}
          </p>
          <div
            id="qrCode"
            ref="qrCode"
            class="qrcode"
          />

          <p class="qrCodeimgWrapTitle">
            {{ $t("marketing.pages.setting.db_9aaf96") }}
          </p>
          <p class="qrCodeimgWrapDesc">
            {{ $t("marketing.pages.setting.wxsmyxewmt_ea9888") }}
          </p>
          <div
            id="qrCode2"
            ref="qrCod2e"
            class="qrCode2"
          >
            <img
              class="qrCode2Img"
              :src="qrCode2"
              alt=""
            >
          </div>
        </div>

        <div slot="footer">
          <ElLink
            href="https://help.fxiaoke.com/2615/93d4/9188/7bd2/0ed1"
            target="_blank"
            type="primary"
            class="qrCodeLink"
          >
            {{ $t("marketing.commons.rhbdqywx_270dbe") }}
          </ElLink>
          <el-button @click="handleCloseDialog">
            {{ $t("marketing.commons.gb_9d2578") }}
          </el-button>
        </div>
      </ElDialog>
    </div>
</template>

<script>
import QRCode from 'qrcodejs2'
import ContentHeader from '@/components/content-header/index'
import http from '@/services/http/index'
import LinkQywxSettingDialog from '@/pages/qywx-manage/init/link-qywx-setting'
import { confirm } from '@/utils/globals'
import QuestionTooltip from '@/components/questionTooltip/index'
import kisvData from '@/modules/kisv-data'

export default {
  components: {
    ContentHeader,
    ElButton: FxUI.Button,
    LinkQywxSettingDialog,
    ElLink: FxUI.Link,
    ElDialog: FxUI.Dialog,
    Checkbox: FxUI.Checkbox,
    CheckboxGroup: FxUI.CheckboxGroup,
    Radio: FxUI.Radio,
    QuestionTooltip,
  },
  data() {
    return {
      crumbs: [
        {
          text: $t('marketing.commons.sz_e366cc'),
          to: { name: 'setting-setitems', query: { type: 'v-base-set' } },
        },
        { 
          text: $t('marketing.commons.yxcj_b131f3'), 
          to: { name: 'setting-setitems', query: {type: 'v-marketing-plugin'}}, 
        },
        {
          text: $t('marketing.pages.setting.qywxsz_fc99ea'),
          to: false,
        },
      ],
      infomationMap: [
        {
          title: $t('marketing.commons.qywxjbzl_ef124c'),
          fields: {
            // cropName: {
            //   key: '企业简介',
            // },
            qywxCorpId: {
              key: 'CropID',
            },
            // customerContactSecret: {
            //   key: '客户联系Secret',
            // },
          },
        },
        // {
        //   title: '用于连接营销通与企业微信的自建应用信息配置',
        //   // link: {
        //   //   label: "如何获取?",
        //   //   href: "https://www.baidu.com"
        //   // },
        //   fields: {
        //     appName: {
        //       key: '应用名称',
        //     },
        //     appAgentId: {
        //       key: 'AgentID',
        //     },
        //     appSecret: {
        //       key: 'Secret',
        //     },
        //   },
        // },
        // {
        //   title: '自建应用的API接收消息服务器信息配置',
        //   fields: {
        //     selfAppToken: {
        //       key: 'Token',
        //     },
        //     selfAppEncodingAesKey: {
        //       key: 'EncodingAesKey',
        //     },
        //   },
        // },
        {
          // isHide: true,
          title: $t('marketing.commons.qywxkhdywc_b7a1dc'),

          // link: {
          //   label: '如何使用?',
          //   href:
          //     'https://www.fxiaoke.com/mob/guide/fmarketing/%E7%A7%81%E5%9F%9F%E6%B5%81%E9%87%8F%E6%B1%A0/%E5%A6%82%E4%BD%95%E6%B7%BB%E5%8A%A0%E5%AE%A2%E6%88%B7%E8%90%A5%E9%94%80%E5%8A%A8%E6%80%81%E4%BE%A7%E8%BE%B9%E6%A0%8F.html',
          // },
          fields: {
            customerFeed: {
              key: $t('marketing.pages.setting.yxzs_e6976d'),
              canCopy: true,
            },
            // groupFeed: {
            //   key: '客户群动态',
            //   canCopy: true,
            // },
          },
        },
        {
          title: $t('marketing.pages.setting.yxtgldqywx_554a18'),
          fields: {
            mimiAppAgentId: {
              key: 'AgentID',
              canCopy: true,
            },
            miniAppSecret: {
              key: 'Secret',
              canCopy: true,
            },
          },
        },
      ],
      value: {
        cropName: '--',
        qywxCorpId: '--',
        customerContactSecret: '--',
        appName: '--',
        appAgentId: '--',
        appSecret: '--',
        selfAppToken: '--',
        selfAppEncodingAesKey: '--',
        customerFeed: '--',
        groupFeed: '--',
        mimiAppAgentId: '--',
        miniAppSecret: '--',
        customerAuthUrl: '',
        isEncrypt: 0,
      },
      isShowDialog: false,
      isShowQrCodeDialog: false,
      qrCode: '',
      qrCode2:
        'https://a9.fspage.com/FSR/frontend/html/app-dist/marketing-dist/assets/img/openTipsQrcode.d58c040.png',
      menuLists: [
        {
          label: $t('marketing.pages.setting.jbsz_f089a8'),
          value: 0,
        },
        {
          label: $t('marketing.pages.setting.xxsz_aab75a'),
          value: 1,
        },
      ],
      menuActive: 0,
      noticeSendTypeEdit: false,
      chatSendTypeEdit: false,
      noticeSendTypes: [],
      noticeSendTypeId: '',
      chatToolSentType: 0,
      vDatas: kisvData.datas,
      flag_showLoadingMask: true,
    }
  },
  mounted() {
    this.queryEnterpriseQywxConfig()
    this.getMarketingNoticeSetting()
    this.getQywxSidebarMaterialSendSetting()
  },
  methods: {
    queryEnterpriseQywxConfig() {
      this.flag_showLoadingMask = true
      http.queryEnterpriseQywxConfig().then(res => {
        this.flag_showLoadingMask = false
        if (res === false) {
          FxUI.Message.error($t('marketing.commons.xtcwqshzs_c236af'))
          return
        }
        if (res.errCode) {
          FxUI.Message.error(res.errMsg || $t('marketing.commons.qqsbqshzs_d5dd7d'))
          return
        }
        const { data } = res
        const { enterpriseAccount } = FS.contacts.getCurrentEmployee() || {};
        this.value = {
          cropName: '--',
          qywxCorpId: data.qywxCorpId || '--',
          customerContactSecret: data.customerContactSecret || '--',
          appName: data.appName || '--',
          appAgentId: data.appAgentId || '--',
          appSecret: data.appSecret || '--',
          selfAppToken: data.selfAppToken || '--',
          selfAppEncodingAesKey: data.selfAppEncodingAesKey || '--',
          mimiAppAgentId: data.mimiAppAgentId || '--',
          miniAppSecret: data.miniAppSecret || '--',
          customerFeed: `${
            location.origin
          }/proj/page/marketing/${enterpriseAccount}#/pages/wechatSidebar/wechatSidebar?corpId=${
            data.qywxCorpId
          }&suitId=${data.suitId || ''}&ea=${enterpriseAccount}`,
          groupFeed: '--',
          customerAuthUrl: data.customerAuthUrl || '',
          isEncrypt: data.isEncrypt,
        }
        if (data.customerAuthUrl) {
          this.loadQRCode(data.customerAuthUrl)
        }
      })
    },
    handleSet() {
      if (this.value.isEncrypt == 1) {
        this.isShowQrCodeDialog = true
        this.loadQRCode(this.value.customerAuthUrl)
      } else {
        this.isShowDialog = true
      }
    },
    handleSync() {
      confirm(
        $t('marketing.pages.setting.kszhtbqywx_d22007'),
        $t('marketing.commons.ts_02d981'),
        {
          confirmButtonText: $t('marketing.commons.qd_38cf16'),
          cancelButtonText: $t('marketing.commons.qx_625fb2'),
        },
      )
        .then(() => {
          console.log('confirm then')
          http.syncAllWxWorkExternalUserToCrm().then(res => {
            if (!res || res.errCode !== 0) {
              return
            }
            FxUI.Message.success($t('marketing.pages.setting.tbzhzqshqw_376ef5'))
          })
        })
        .catch(() => {
          console.log('confirm catch')
        })
    },
    handleSyncAddressBook() {
      confirm(
        $t('marketing.pages.setting.kszhtbqywx_c8c992'),
        $t('marketing.commons.ts_02d981'),
        {
          confirmButtonText: $t('marketing.commons.qd_38cf16'),
          cancelButtonText: $t('marketing.commons.qx_625fb2'),
        },
      )
        .then(() => {
          this.$store.dispatch('QywxManage/queryQywxStaff', {
            warmUpData: true,
          })
          FxUI.Message.success($t('marketing.pages.setting.tbzhzqshck_3ebbc4'))
        })
        .catch(() => {
          console.log('confirm catch')
        })
    },
    handleSetSubmit() {
      this.queryEnterpriseQywxConfig()
    },
    // 关闭弹框
    handleCloseDialog(isShow) {
      if (isShow === true) return
      this.isShowQrCodeDialog = false
    },
    loadQRCode(code) {
      if (this.qrCode) return
      setTimeout(() => {
        if (this.$refs.qrCode) {
          this.qrCode = new QRCode(this.$refs.qrCode, {
            text: code, // 二维码内容，可以是字符串或者网址
            width: 120, // 宽度 数字 px
            height: 120, // 高度 数字 px
            colorDark: '#000', // 二维码颜色
            colorLight: '#fff', // 二维码背景色
            correctLevel: QRCode.CorrectLevel.L, // 容错率，L/M/H
          })
        }
      }, 100)
    },
    handleChangeActiveItem(item) {
      this.menuActive = item.value
    },
    toggleNoticeSendTypeSave(type) {
      if (type === 'confirm') {
        this.updateMarketingNoticeSetting()
      } else {
        this.noticeSendTypeEdit = false
        this.getMarketingNoticeSetting()
      }
    },
    toggleChatSendTypeSave(type) {
      if (type === 'confirm') {
        this.updateQywxSidebarMaterialSendSetting()
      } else {
        this.chatSendTypeEdit = false
        this.getQywxSidebarMaterialSendSetting()
      }
    },
    updateMarketingNoticeSetting() {
      if (this.noticeSendTypes.length < 1) {
        FxUI.Message.warning($t('marketing.pages.setting.qzsxzyzqyy_7c3bb8'))
        return
      }
      http
        .updateMarketingNoticeSetting({
          id: this.noticeSendTypeId,
          noticeType: this.noticeSendTypes,
        })
        .then(({ errCode, errMsg }) => {
          if (errCode !== 0) {
            FxUI.Message.error(
              errMsg || $t('marketing.commons.bcsbqzs_0ff01a'),
            )
          } else {
            this.noticeSendTypeEdit = false
          }
        })
    },
    getMarketingNoticeSetting() {
      http.getMarketingNoticeSetting().then(({ errCode, data }) => {
        if (errCode === 0) {
          this.noticeSendTypeId = data.id
          this.noticeSendTypes = data.noticeType || []
        }
      })
    },
    getQywxSidebarMaterialSendSetting() {
      http.getQywxSidebarMaterialSendSetting().then(({ errCode, data }) => {
        if (errCode === 0) {
          // 小程序：0  H5：1
          this.chatToolSentType = data.sendType || 0
        }
      })
    },
    updateQywxSidebarMaterialSendSetting() {
      http
        .updateQywxSidebarMaterialSendSetting({
          sendType: this.chatToolSentType,
        })
        .then(({ errCode, errMsg }) => {
          if (errCode !== 0) {
            FxUI.Message.error(
              errMsg || $t('marketing.commons.bcsbqzs_0ff01a'),
            )
          } else {
            this.chatSendTypeEdit = false
          }
        })
    },
    openHelp(link) {
      window.open(link, '_blank')
    },
  },
}
</script>

<style lang="less" scoped>
.qywx-infomation {
  display: flex;
  flex-direction: column;
  flex: 1;
  // .header {
  //   flex-flow: row nowrap;
  //   align-items: center;
  //   padding-left: 22px;
  // }
  .main-content__wrapper {
    display: flex;
    height: 100%;
    position: relative;
    .left-menu__wrapper {
      width: 200px;
      border-right: 1px solid #dee1e8;
      height: 100%;
      .menu-item {
        text-align: center;
        line-height: 40px;
        cursor: pointer;
        &.active {
          background-color: #f0f4fc;
        }
      }
    }
    .qywx-base__setting {
      padding: 17px 16px;
      flex: 1;
      background: #fff;
      display: flex;
      flex-direction: column;
      .infomation--box {
        .infomation__title {
          margin: 10px 0 15px;
          color: #181c25;
          font-size: 14px;
          display: flex;
          align-items: center;
          .title__link {
            margin-left: 15px;
            font-size: 12px;
            .ico-question {
              width: 16px;
              height: 16px;
              background: url("../../../assets/images/icon/icon-question.png")
                no-repeat center;
              background-size: 100%;
            }
          }
        }
        .infomation__field {
          margin-bottom: 10px;
          display: flex;
          font-size: 13px;
          .field__key {
            color: #91959e;
            width: 110px;
          }
          .field__value {
            color: #181c25;
          }
        }
      }
      .infomation--buttons {
        .infomation--button {
          min-width: 120px;
          margin: 20px 10px 20px 0;
        }
      }
    }
    .qywx-infomation__setting {
      flex: 1;
      .setitem {
        position: relative;
        padding: 20px;
        border-bottom: 1px dashed #e9edf5;
        &.setitem-edit {
          background-color: #e9f2ff;
        }
        .el-radio__input.is-disabled + span.el-radio__label,
        .el-checkbox__input.is-checked + .el-checkbox__label {
          color: @color-title;
        }
      }
      .edit-button-group {
        margin-top: 15px;
      }
      .setting-item-btn {
        position: absolute;
        right: 20px;
        top: 50%;
        transform: translate(0, -50%);
        margin: 0;
        cursor: pointer;
      }
      .setitle {
        margin-bottom: 8px;
        font-size: 16px;
        color: #181c25;
        display: flex;
        align-items: center;
        font-weight: bold;
        .seticon{
          width: 16px;
          height: 16px;
          margin-left: 8px;
          margin-bottom: 2px;
          cursor: pointer;
        }
        }
        .setdesc {
        color: #91959e;
        padding-right: 120px;
        font-size: 14px;
        }
        .question_tooltip {
        margin-left: 4px;
      }
    }
  }
}
.qrCodeLink {
  position: relative;
  right: 390px;
}
</style>
<style lang="less">
.qrCodeElDialog {
  .el-dialog__header {
    box-shadow: 0 2px 5px 0 rgba(69, 79, 91, 0.08);
    color: #181c25;
    text-align: start;
  }
  .qrCodeimgWrap {
    height: 390px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    .qrCodeimgWrapTitle {
      font-weight: bold;
      color: #000;
    }
    .qrCodeimgWrapDesc {
      color: #000;
      margin: 7px 0;
    }
    .qrcode {
      margin-bottom: 30px;
    }
    .qrCode2 {
      width: 120px;
      height: 120px;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
