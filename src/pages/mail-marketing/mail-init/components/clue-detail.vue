<!-- 组件说明 -->
<template>
  <div class="mail-clue-detail-wrapper">
    <div class="side-wrapper">
      <div class="side-title">{{ $t('marketing.commons.xsmx_e46fbe') }}</div>
      <div class="export" @click="handleOutput">{{ $t('marketing.commons.dc_55405e') }}</div>
    </div>
    <v-table
      class="tablewbg"
      tid="mail-side-detail-table"
      :settable="true"
      :filter-option="false"
      :data="tableDatas"
      :columns="tableColumns"
      :row-style="{ cursor: 'pointer' }"
      @custom:cule-action="handleClueDetail"
      @click:row="handleClueRow"
      @refreshTable="refreshTable"
    ></v-table>
    <loading-mask :show="showClueLoading"></loading-mask>
    <v-pagen @change="handlePageChange" :pagedata.sync="pageData"></v-pagen>
  </div>
</template>

<script>
import LoadingMask from "@/components/loading-mask/index";
import VTable from "@/components/table-ex";
import http from "@/services/http/index";

import VPagen from "@/components/kitty/pagen";
import CONFIG from "./config";
import util from "@/services/util/index";
export default {
  components: {
elButton: FxUI.Button,
VTable,
VPagen,
LoadingMask
},
  data() {
    return {
      showClueLoading: true,
      pageData: {
        layout: "prev, pager, next, total, sizes, jumper",
        pageNum: 1,
        totalCount: 0,
        pageSize: 10,
        pageSizes: [10, 20, 30, 40]
      },
      tableColumns: [],
      tableDatas: [],
      isExporting: false
    };
  },
  props: {
    currentItem: {
      type: Object,
      default: {}
    }
  },
  computed: {},
  methods: {
    refreshTable() {
      this.queryMultipleFormUserData();
    },
    handlePageChange(data) {
      this.pageData.pageNum = data.pageNum;
      this.pageData.pageSize = data.pageSize;
      this.queryMultipleFormUserData();
    },
    handleSearch() {},
    handleBack() {
      this.$emit("onBack");
    },
    handleClueDetail(row) {
      if (row.saveCrmStatus === 0 || row.saveCrmStatus === 3) {
        if(!row.otherCrmObjectBind) return;
        CRM.api.show_detail({
          apiname: row.otherCrmObjectBind.apiName,
          id: row.otherCrmObjectBind.objectId
        });
      }
    },
    handleClueRow(row, column, event) {
      console.log("click clue row", ...arguments);
      this.handleClueDetail(row);
    },
    queryMultipleFormUserData() {
      let params = {
        sourceType: 1,
        sourceId: this.currentItem.marketingActivityId,
        pageNum: this.pageData.pageNum,
        pageSize: this.pageData.pageSize
      };
      http.queryMultipleFormUserData(params).then(res => {
        this.showClueLoading = false;
        if (res && res.errCode == 0) {
          this.pageData.totalCount = res.data.totalCount;
          this.tableColumns = CONFIG.getClueColumns(res.data);
          this.tableDatas = CONFIG.getClueData(res.data);
        }
      });
    },
    handleOutput() {
      if (this.isExporting) return;
      this.isExporting = true;
      const params = {
        sourceId: this.currentItem.marketingActivityId,
        sourceType: 1
      };
      const opts = {
        action: http.exportMultipleFormEnrollsData.getUrl(),
        params
      };
      util.exportoFile(opts, () => {
        this.isExporting = false;
      });
    }
  },
  mounted() {
    this.queryMultipleFormUserData();
  },
  created() {},
  destroyed() {} //生命周期 - 销毁完成
};
</script>

<style lang="less" scoped>
.mail-clue-detail-wrapper {
  .header {
    display: flex;
    height: 82px;
    background-color: #f6f9fc;
    font-size: 14px;
    color: #181c25;
    padding: 19px 100px 0 20px;
    .back {
      flex-shrink: 0;
      padding-right: 16px;
      cursor: pointer;
    }
    .arrow-img {
      width: 16px;
      transform: rotate(90deg);
    }
    .line {
      background-color: #e9edf5;
      width: 1px;
      height: 20px;
    }
    .title {
      margin-left: 16px;
      width: 550px;
    }
  }

  .side-wrapper {
    display: flex;
    justify-content: space-between;
    margin: 20px 20px 0 0;
    .side-title {
      font-size: 12px;
      color: #181c25;
      padding-left: 16px;
      border-left: 4px solid var(--color-primary06,#ff8000);
      height: 18px;
    }
    .export {
      color: var(--color-primary06,#407FFF);
      font-size: 12px;
      cursor: pointer;
    }
  }
  .tablewbg {
    width: auto;
    border-top: 1px solid #e9edf5!important;
    border-left: 1px solid #e9edf5!important;
    box-sizing: border-box;
    overflow: auto;
    margin: 6px 20px 0 20px;
  }
}
</style>
