<!-- 组件说明 -->
<template>
     <div>
       <sideslip-popup
         class="mail-spread-detaill-sideslip"
         width="900px"
         :visible="visible"
         :overflowy-hidden="detailVisible"
         @close="handleClose"
       >
         <template v-if="showCrmAuthorityTips">
           <noPremissionTip @handleOperate="handleClose" />
         </template>
         <template v-else>
           <div v-if="showPage == 'spreadDetail'" class="header">
             <div class="content">
               <div class="main">
                 <div class="p1">
                   {{ mailGroupSendResult.name }}
                 </div>
                 <div class="p2">
                   {{ $t('marketing.commons.fsdx_6a571c')
                   }}<span v-if="mailGroupSendResult.sendRange == 2">{{ mailGroupSendResult.groupText }}</span
                   ><span v-if="mailGroupSendResult.sendRange == 1">{{ $t('marketing.commons.scyjwj_7b981d') }}</span>
                   <span v-if="mailGroupSendResult.sendRange == 3">{{ $t('marketing.commons.chryyjdz_6a0c5d') }}</span>
                   <span v-if="mailGroupSendResult.sendRange == 8">{{
                     $t('marketing.pages.mail_marketing.hdryyjdz_740194')
                   }}</span>
                   <span v-if="mailGroupSendResult.sendRange == 6">{{ $t('marketing.commons.zbbmryyjdz_615df9') }}</span>
                 </div>
                 <div class="info">
                   {{ $t('marketing.commons.fjr_2df6a3') }}{{ mailGroupSendResult.senderUrl }} &nbsp;&nbsp;&nbsp;&nbsp;
                   {{ $t('marketing.commons.hfr_fcf518') }}{{ mailGroupSendResult.replyUrl }}
                 </div>
                 <div class="info">
                   <span>{{ $t('marketing.commons.fssj_f6cbc9') }}{{ mailGroupSendResult.sendTime || '' }} </span
                   ><span>{{ $t('marketing.commons.fszt_ac4f53') }}{{ mailGroupSendResult.statusText || '' }} </span
                   >{{ $t('marketing.commons.czr_8146bb') }}{{ mailGroupSendResult.createByName || '' }}
                 </div>
               </div>
             </div>
             <div class="options">
               <fx-button class="button" size="mini" @click.stop="prview">
                 {{ $t('marketing.pages.mail_marketing.ylnr_9a3770') }}
               </fx-button>
               <fx-button
                 v-if="mailGroupSendResult.sendCancelable"
                 type="primary"
                 class="button"
                 size="mini"
                 plain
                 @click.stop="handleCancelSend(mailGroupSendResult)"
               >
                 {{ $t('marketing.commons.qxfs_b022e6') }}
               </fx-button>
               <fx-button type="primary" class="button" size="mini" plain @click.stop="handleSendAgain">
                 {{ $t('marketing.commons.fz_79d3ab') }}
               </fx-button>
             </div>
           </div>
           <div v-else class="detail-header">
             <div class="back" @click="changePage('spreadDetail')">
               &nbsp;&nbsp;{{ $t('marketing.commons.fh_5f4112') }}
             </div>
             <div class="line" />
             <div>
               <div class="title km-t-ellipsis1">
                 {{ currentItem.subject || '' }}
               </div>
             </div>
           </div>
           <template v-if="showPage == 'spreadDetail'">
             <div class="side-title">
               {{ $t('marketing.commons.sjtj_1b7cba') }}
             </div>
             <div class="number-wrapper">
               <div class="number">
                 <div class="item">
                   <div class="num blue" @click="handleSearchByStatus('all')">
                     {{ mailGroupSendResult.sendCount || '0' }}
                   </div>
                   <div class="des">
                     {{ $t('marketing.commons.fsrs_8428aa') }}
                   </div>
                 </div>
                 <div class="item">
                   <div class="num blue" @click="handleSearchByStatus(0)">
                     {{ mailGroupSendResult.successCount || '0' }}
                   </div>
                   <div class="des">
                     {{ $t('marketing.commons.sdrs_c1ec4c') }}
                   </div>
                 </div>
                 <div class="item">
                   <div class="num">
                     {{ mailGroupSendResult.openUserNum || '0'
                     }}<span class="mini">{{ mailGroupSendResult.openCount }}</span>
                   </div>
                   <div class="des">
                     {{ $t('marketing.commons.dkrs_b1478d')
                     }}<span class="mini">{{ $t('marketing.commons.cs_f965fe') }}</span>
                   </div>
                 </div>
                 <div class="item" style="width: 130px">
                   <div class="num">
                     {{ mailGroupSendResult.clickUserNum || '0'
                     }}<span class="mini">{{ mailGroupSendResult.clickCount }}</span>
                   </div>
                   <div class="des">
                     {{ $t('marketing.commons.djljrs_e9c637')
                     }}<span class="mini">{{ $t('marketing.commons.cs_f965fe') }}</span>
                   </div>
                 </div>
                 <div class="item">
                   <div class="num">
                     {{ mailGroupSendResult.clueCount || '0' }}
                   </div>
                   <div class="des">
                     {{ $t('marketing.commons.hqxss_73f905') }}
                   </div>
                 </div>
               </div>
               <div class="line" />
               <div class="totalWrapper">
                 <div class="total">
                   <div
                     class="num blue"
                     @click.stop="handleSearchByStatus(3)"
                   >
                     {{ mailGroupSendResult.unsubscribeCount || '0' }}
                   </div>
                   <div class="des">
                     {{ $t('marketing.commons.qxdy_04595c') }}
                   </div>
                 </div>
                 <div class="total">
                   <div
                     class="num blue"
                     @click.stop="handleSearchByStatus(4)"
                   >
                     {{ mailGroupSendResult.spamCount || '0' }}
                   </div>
                   <div class="des">
                     {{ $t('marketing.commons.ljjb_a2604f') }}
                   </div>
                 </div>
               </div>
             </div>

              <fx-tabs style="padding: 0 18px;margin-top: 10px;" v-model="activeTabName" @tab-click="handleClick">
               <fx-tab-pane :label="$t('marketing.commons.fsqk_ecf92a')" name="sendDetail">
                   <div class="table_header-wrapper">
                     <div class="filter-wrapper">
                       <fx-select
                         ref="statusSelect"
                         size="small"
                         style="width: 150px;margin-right: 20px;"
                         :placeholder="$t('marketing.pages.mail_marketing.aztsx_7d64bb')"
                         v-model="searchParams.status"
                         :options="statusOptions"
                         @change="queryMailUserSendDetail(true)"
                       ></fx-select>
                       <fx-select
                         ref="failureSelect"
                         size="small"
                         style="width: 200px;margin-right: 20px;"
                         :placeholder="$t('marketing.pages.mail_marketing.asbyysx_006462')"
                         multiple
                         collapse-tags
                         v-model="searchParams.subStatus"
                         :options="failureOptions"
                         @change="queryMailUserSendDetail(true)"
                       ></fx-select>
                       <div class="input-wrapper">
                        <fx-input
                         :placeholder="$t('marketing.pages.mail_marketing.ayxss_fb36a6')"
                         v-model="searchParams.email"
                         size="small"
                         is-search
                         @keyup.enter.native="queryMailUserSendDetail(true)"
                         @on-search="queryMailUserSendDetail(true)"
                       ></fx-input>
                       <i v-show="searchParams.email != ''" @click="handleClearInput" class="el-icon-circle-close"></i>
                       </div>
                     </div>
                     <fx-button
                       v-if="mailGroupSendResult.sendStatus === 2 || mailGroupSendResult.sendStatus === 3"
                       type="primary"
                       class="export-btn"
                       size="mini"
                       :loading="exportLoading"
                       plain
                       @click.stop="handleExportTableDatas"
                     >
                       {{ $t('marketing.commons.dc_55405e') }}
                     </fx-button>
                   </div>
                   <div class="tips-wrapper" v-if="problemEmailNum">
                     <i class="el-icon-warning"></i>
                     {{ $t('marketing.commons.rqzhygwtyj_70e41d', { data: { option0: problemEmailNum } }) }}
                     <span @click.stop="changePage('problemEmail')">{{
                       $t('marketing.pages.mail_marketing.ckwtyx_c65c80')
                     }}</span>
                   </div>
                   <div class="side-wrapper">
                     <v-table
                       class="tablewbg"
                       tid="mail-side-send-table"
                       :data="tableDatas"
                       :columns="tableColumns"
                       @click:row="handleShowDetail"
                       @custom:user-action="handleShowDetail"
                     />
                     <v-pagen @change="handleDetailPageChange" :pagedata.sync="detailPageData"></v-pagen>
                   </div>
               </fx-tab-pane>
               <fx-tab-pane v-if="clueTableDatas.length" :label="$t('marketing.commons.hqxsmx_9718c8')" name="clueDetail">
                   <div class="side-wrapper">
                     <v-table
                       class="tablewbg"
                       tid="mail-side-send-table"
                       :data="clueTableDatas"
                       :columns="clueTableColumns"
                       @custom:cule-action="handleClueDetail"
                       @click:row="handleClueRow"
                       @refreshTable="refreshTable"
                     />
                     <div v-if="clueTableDataTotal > clueTablePageSize" class="table__more">
                       <a href="javascript: void(0);" @click.stop="changePage('clueDetail')">{{
                         $t('marketing.commons.ckgd_90ef7c')
                       }}</a>
                     </div>
                   </div>
               </fx-tab-pane>
               <loading-mask :show="showLoadingMask" />
             </fx-tabs>
           </template>
           <template v-if="showPage == 'sendDetail'">
             <send-detail
               :current-item="currentItem"
               :default-select="defaultSelect"
               @onBack="changePage('spreadDetail')"
               @handleShowDetail="handleShowDetail"
             />
           </template>
           <template v-if="showPage == 'linkDetail'">
             <link-detail :current-item="currentItem" @onBack="changePage('spreadDetail')" />
           </template>
           <template v-if="showPage == 'clueDetail'">
             <clue-detail :current-item="currentItem" @onBack="changePage('spreadDetail')" />
           </template>
           <template v-if="showPage == 'cancelReportDetail'">
             <cancel-report-detail
               :current-item="currentItem"
               :type="cancelReportType"
               @onBack="changePage('spreadDetail')"
               @handleShowDetail="handleShowDetail"
             />
           </template>
           <template v-if="showPage == 'problemEmail'">
             <problem-email
               :current-item="currentItem"
               @onBack="changePage('spreadDetail')"
               @handleShowDetail="handleShowDetail"
             />
           </template>
         </template>
         <mail-preview-dialog
           v-if="showPreviewDialog"
           :task-id="currentItem.id"
           :visible="showPreviewDialog"
           @closePreview="showPreviewDialog = false"
         />
         <user-detail :top="'0px'" :user-id="detailId" :visible="detailVisible" @close="handleCloseDetail" />
       </sideslip-popup>
     </div>
</template>

<script>
import UserDetail from '@/pages/user/detail';
import LoadingMask from '@/components/loading-mask/index';
import VTable from '@/components/table-ex';
import SideslipPopup from '@/components/sideslip-popup/index.vue';
import MailPreviewDialog from '../../components/mail-preview-dialog';
import http from '@/services/http/index';
import SendDetail from './send-detail.vue';
import CancelReportDetail from './cancel-report-detail.vue';
import LinkDetail from './link-detail.vue';
import ClueDetail from './clue-detail.vue';
import CONFIG from './config';
import utils from '@/services/util/index';
import noPremissionTip from '@/components/noPremissionTip';
import problemEmail from './problem-email.vue';
import VPagen from "@/components/kitty/pagen";
import { MAIL_STATUS } from '@/utils/constant';

export default {
  components: {
    UserDetail,
    elButton: FxUI.Button,
    SideslipPopup,
    VTable,
    SendDetail,
    LinkDetail,
    ClueDetail,
    MailPreviewDialog,
    LoadingMask,
    CancelReportDetail,
    noPremissionTip,
    problemEmail,
    VPagen,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    item: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      showCrmAuthorityTips: false,
      detailId: '',
      detailVisible: false,
      previewInfo: {},
      showPreviewDialog: false,
      tableColumns: [],
      tableDatas: [],
      tablePageSize: 4,
      clueTableColumns: [],
      clueTableDatas: [],
      clueTablePageSize: 5,
      clueTableDataTotal: 0,
      showPage: 'spreadDetail',
      mailGroupSendResult: {},
      currentItem: {},
      showLoadingMask: true,
      defaultSelect: 0,
      problemEmailNum: 0,
      cancelReportType: 'cancel',
      exportLoading: false,
      activeTabName: 'sendDetail',
      searchParams: {
        status: 'all',
        subStatus: [],
        email: ''
      },
      statusOptions: CONFIG.statusOptions,
      failureOptions: CONFIG.failureOptions,
      detailPageData: {
        pageNum: 1,
        totalCount: 0,
        pageSize: 10,
        pageSizes: [5, 10, 20, 30]
      }
    };
  },
  computed: {},
  mounted() {
    this.currentItem = JSON.parse(JSON.stringify(this.item));
    setTimeout(() => {
      // 来自雷达列表
      if (this.currentItem.formSource == 'radar') {
        this.currentItem.marketingActivityId = this.currentItem.id;
        this.currentItem.id = this.currentItem.objectId;
      } else if (this.currentItem.formSource == 'rankActivity') {
        // 来自首页活动推广排行榜数据处理
        http
          .getDetailByMarketingActivityId({
            marketingActivityId: this.currentItem.id,
          })
          .then((res) => {
            if (res && res.errCode == 0) {
              const _item = res.data;
              _item.id = _item.taskId;
              this.currentItem = _item;
            }
          });
      } else if (this.currentItem.formSource == 'homeRadar') {
        // 来自首页雷达
      }
      this.initData();
    });
  },
  created() {},
  destroyed() {},
  methods: {
    refreshTable() {
      this.queryMultipleFormUserData();
    },
    handleSendAgain() {
      this.$router.push({
        name: 'mail-group-send',
        params: { type: 'create' },
        query: {
          marketingActivityId: this.item.marketingActivityId,
          taskId: this.item.id,
        },
      });
    },
    handleCancelSend(item) {
      FxUI.MessageBox.confirm($t('marketing.commons.qrqxfsm_43c32c'), $t('marketing.commons.ts_02d981'), {
        type: 'warning',
      }).then(() => {
        http
          .fakeCancelMarketingActivitySend({
            id: item.id,
          })
          .then(({ errCode, errMsg }) => {
            if (errCode === 0) {
              FxUI.Message.success($t('marketing.commons.qxcg_285f58'));
              this.queryMarketingActivityDetail();
            } else {
              FxUI.Message.error(errMsg || $t('marketing.commons.czsbqshzs_fb5ded'));
            }
          });
      });
    },
    handleShowDetail(row) {
      setTimeout(() => {
        this.detailId = row.marketingUserId;
        if (this.detailId) {
          this.detailVisible = true;
        }
      }, 0);
    },
    handleCloseDetail() {
      this.detailVisible = false;
    },
    changePage(pageName, type) {
      if (pageName == 'cancelReportDetail') {
        this.cancelReportType = type;
      }
      this.showPage = pageName;
    },
    handleClose() {
      this.$emit('close');
    },
    prview() {
      this.showPreviewDialog = true;
    },
    handleClueDetail(row) {
      if (row.saveCrmStatus === 0 || row.saveCrmStatus === 3) {
        if(!row.otherCrmObjectBind) return;
        CRM.api.show_detail({
          apiname: row.otherCrmObjectBind.apiName,
          id: row.otherCrmObjectBind.objectId,
        });
      }
    },
    handleClueRow(row, column, event) {
      console.log('click clue row', ...arguments);
      this.handleClueDetail(row);
    },
    queryMultipleFormUserData() {
      const params = {
        sourceType: 1,
        sourceId: this.currentItem.marketingActivityId,
        pageNum: 1,
        pageSize: this.clueTablePageSize,
      };
      http.queryMultipleFormUserData(params).then((res) => {
        if (res && res.errCode == 0) {
          this.clueTableDataTotal = res.data.totalCount;
          this.clueTableColumns = CONFIG.getClueColumns(res.data);
          this.clueTableDatas = CONFIG.getClueData(res.data);
        }
      });
    },
    queryMarketingActivityDetail() {
      http
        .queryMarketingActivityDetail({
          id: this.currentItem.marketingActivityId,
        })
        .then((res) => {
          if (res && res.errCode == 0) {
            const resData = {
              ...res.data,
              ...res.data.mailGroupSendMessageDetailResult,
            };
            const mailGroupSendResult = {
              ...resData,
              sendTime: utils.formatDateTime(resData.sendTime, 'YYYY-MM-DD hh:mm'),
              statusText: MAIL_STATUS[resData.sendStatus] || '--',
            };
            if (mailGroupSendResult.sendRange == 2) {
              let userNum = 0;
              let userText = '  ';
              mailGroupSendResult.marketingGroupUser &&
                mailGroupSendResult.marketingGroupUser.length &&
                mailGroupSendResult.marketingGroupUser.forEach((e, index) => {
                  if (index > 0) {
                    userText += '| ';
                  }
                  userNum += e.marketingGroupUserCount;
                  userText += `${e.marketingGroupUserName} `;
                });
              mailGroupSendResult.groupNum = userNum;
              mailGroupSendResult.groupText = userText;
            }
            this.mailGroupSendResult = mailGroupSendResult;
            console.log('mailGroupSendResult', this.mailGroupSendResult);
          } else if (res && (res.errCode == '320001401' || res.errCode == '320001400')) {
            // 无此操作的数据权限
            this.showCrmAuthorityTips = true;
          }
        });
    },
    queryMailUserSendDetail(resetPageNum = false) {
      const data = JSON.parse(JSON.stringify(this.searchParams))
      if(this.searchParams.status === 'all'){
        delete data.status
      }
      const params = {
        pageNum: resetPageNum ? 1 : this.detailPageData.pageNum,
        pageSize:this.detailPageData.pageSize,
        taskId: this.currentItem.id,
        ...data
      };
      http.queryMailUserSendDetail(params).then((res) => {
        this.showLoadingMask = false;
        if (res && res.errCode == 0) {
          this.detailPageData.totalCount = res.data.totalCount;
          this.tableColumns = CONFIG.getSendColumns(res.data);
          this.tableDatas = CONFIG.getSendTableData(res.data);
        }
      });
    },
    handleClearInput(){
      this.searchParams.email = ''
      this.queryMailUserSendDetail(true)
    },
    initData() {
      this.queryMultipleFormUserData();
      this.queryMarketingActivityDetail();
      // 发送详情
      this.queryMailUserSendDetail();
      // 问题邮件数
      http
        .queryFilterAddress({
          taskId: this.currentItem.id,
          pageNum: 1,
          pageSize: 1,
        })
        .then((res) => {
          if (res && res.errCode == 0) {
            this.problemEmailNum = res.data.totalCount;
          }
        });
    },
    handleExportTableDatas() {
      this.exportLoading = true;
      utils.exportoFile(
        {
          action: 'exportMailUserSendDetail',
          params: { taskId: this.currentItem.id, pageSize: 50000, pageNum: 1 },
        },
        () => {
          this.exportLoading = false;
        },
      );
    },
    handleDetailPageChange(data){
      this.detailPageData.pageNum = data.pageNum;
      this.detailPageData.pageSize = data.pageSize;
      this.queryMailUserSendDetail()
    },
    handleSearchByStatus(status){
      this.searchParams.status = status
      this.queryMailUserSendDetail(true)
    }
  }, // 生命周期 - 销毁完成
};
</script>

<style lang="less" scoped>
.mail-spread-detaill-sideslip {
  .header {
    display: flex;
    align-items: center;
    height: 120px;
    background-color: #f6f9fc;
    justify-content: space-between;
    padding: 0px 55px 0 25px;
    .button {
      height: 32px;
    }
    .content {
      flex: 1;
      display: flex;
      height: 100px;
      .main {
        display: flex;
        flex-direction: column;
        font-size: 12px;
        justify-content: center;
        color: #91959e;
      }
      .p1 {
        font-size: 14px;
        color: #181c25;
        margin-bottom: 10px;
        width: 500px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        /* autoprefixer: off */
        -webkit-box-orient: vertical;
        /* autoprefixer: on */
        -webkit-line-clamp: 1;
        margin-bottom: 10px;
        font-weight: bold;
      }
      .p2 {
        color: #545861;
      }
      .blue {
        color: var(--color-info06);
      }
      .info {
        margin-top: 8px;
        color: #545861;
        span {
          margin-right: 30px;
        }
      }
    }
  }
  .title-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .export-btn {
      height: 32px;
      font-size: 12px;
      color: var(--color-primary06, #407fff);
      margin-right: 20px;
      border: 0;
      padding: 0 5px;
      background: #fff;
    }
  }
  .side-title {
    margin-top: 20px;
    font-size: 12px;
    color: #181c25;
    padding-left: 16px;
    border-left: 4px solid var(--color-primary06, #ff8000);
    margin-bottom: 20px;
  }
  .table_header-wrapper{
    display: flex;
    justify-content: space-between;
    padding-bottom: 12px;
    .filter-wrapper{
      flex: 1;
      margin-right: 140px;
      display: flex;
      .input-wrapper{
        position: relative;
        width: 200px;
        .el-icon-circle-close{
          position: absolute;
          display: none;
          right: 40px;
          top: 50%;
          transform: translateY(-50%);
          font-size: 12px;
        }
        &:hover{
          .el-icon-circle-close{
            display: inline-block;
          }
        }
      }
    }
  }
  .tips-wrapper {
    margin-left: 0;
    color: #91959e;
    font-size: 12px;
    margin-bottom: 10px;
    .el-icon-warning {
      color: var(--color-primary06, #ff8000);
      font-size: 15px;
      margin-right: 5px;
    }
    span {
      color: var(--color-primary06, #407fff);
      font-size: 12px;
      cursor: pointer;
    }
  }
  .number-wrapper {
    margin: 0px 18px 0px 18px;
    width: 856px;
    border: 1px solid #e9edf5;
    height: 125px;
    display: flex;
    align-items: center;
    .num {
      font-size: 16px;
      color: #333333;
      .mini {
        display: inline-block;
        font-size: 12px;
        border-left: 1px solid #ccc;
        height: 12px;
        line-height: 12px;
        margin-left: 5px;
        padding-left: 5px;
      }
    }
    .blue {
      color: var(--color-info06);
      cursor: pointer;
    }
    .des {
      font-size: 12px;
      color: #545861;
      .mini {
        font-size: 12px;
        display: inline-block;
        border-left: 1px solid #ccc;
        height: 12px;
        line-height: 12px;
        margin-left: 5px;
        padding-left: 5px;
      }
    }
    .number {
      width: 623px;
      display: flex;
      // flex-wrap: wrap;
      .item {
        height: 82px;
        width: 123px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
      }
    }
    .line {
      width: 1px;
      height: 57px;
      background-color: #e9edf5;
    }
    .totalWrapper {
      display: flex;
      justify-content: space-around;
      flex: 1;
    }
    .total {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
    }
  }
  .side-wrapper {
    margin: 0;
    .tablewbg {
      width: auto;
      border-top: 1px solid #e9edf5 !important;
      border-left: 1px solid #e9edf5 !important;
      box-sizing: border-box;
      overflow: auto;
    }
    .table__more {
      height: 40px;
      border-top: 1px solid #e9edf5 !important;
      border-left: 1px solid #e9edf5 !important;
      border-top-width: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      a {
        font-size: 12px;
        text-decoration: none;
      }
    }
  }
  .detail-header {
    display: flex;
    height: 82px;
    background-color: #f6f9fc;
    font-size: 14px;
    color: #181c25;
    align-items: center;
    padding: 0 20px;
    .back {
      flex-shrink: 0;
      padding-right: 16px;
      cursor: pointer;
      color: var(--color-primary06, #407fff);
    }
    .arrow-img {
      width: 16px;
      transform: rotate(90deg);
    }
    .line {
      background-color: #e9edf5;
      width: 1px;
      height: 20px;
    }
    .title {
      margin-left: 16px;
      width: 550px;
    }
  }
}
</style>
