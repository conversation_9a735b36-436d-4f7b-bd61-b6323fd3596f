<!-- 组件说明 -->
<template>
  <div>
    <el-dialog
      class="mail-template-dialog-warpper"
      width="865px"
      :title="$t('marketing.pages.mail_marketing.xzyjmb_b23268')"
      append-to-body
      :visible="visible"
      @close="onClose"
    >
      <tabs
        v-model="activeTab"
        @tab-click="handleClickTab"
      >
        <tab-pane
          :label="$t('marketing.pages.mail_marketing.zdymb_f9a365')"
          name="1"
        />
        <tab-pane
          :label="$t('marketing.commons.ysmb_b2af9c')"
          name="0"
        />
      </tabs>
      <div class="content">
        <div
          v-if="templateLoading"
          class="km-g-loading-mask"
        >
          <span class="loading" />
        </div>
        <div class="content-header">
          <div
            v-if="activeTab != 0"
            class="sub-tabs"
          >
            <fx-button
              v-for="item in subTabs"
              :key="item.value"
              :class="['tab-item',activeName == item.value ? 'active-tab' : '']"
              @click="handleClickTab({name: item.value})"
            >
              {{ item.label }}
            </fx-button>
          </div>
          <div class="search-wrapper">
            <fx-button
              type="text"
              size="small"
              :loading="loading"
              style="margin-right: 10px;height: 32px;"
              @click="handleRefresh"
            >
              <i class="el-icon-refresh-left" />
              <span>{{ $t('marketing.commons.sx_694fc5') }}</span>
            </fx-button>
            <Input
              v-model="searchText"
              size="small"
              class="search-input"
              type="search"
              :placeholder="$t('marketing.commons.ssbt_b1140e')"
              prefix-icon="el-icon-search"
              @change="handleSearch"
            />
          </div>
        </div>
        <div
          v-if="templateList.length"
          class="item-wrapper"
        >
          <template>
            <div
              v-for="(item, index) in templateList"
              :key="index"
              :class="['item', selectId == item.id && 'selected',item.templateType == 2 ? 'big-item' : '']"
              @click="handleClick(item, index)"
            >
              <div :class="['img-wrapper']">
                <img
                  :class="['img',!item.snapshot && 'default-img']"
                  :src="item.templateType == 2 ? (item.snapshot || require('@/assets/images/mail/default-shanbg.png')) : require('@/assets/images/mail/default-mail.png')"
                  alt=""
                >
              </div>
              <div class="title km-t-ellipsis1">
                {{ item.name }}
              </div>
              <div
                v-if="activeName != 0"
                class="time"
              >
                {{ $t('marketing.commons.gxsj_780fb9') }}{{ item.updateTime }}
              </div>
              <div
                v-else
                class="time"
              >
                {{ $t('marketing.commons.cjsj_312f45') }}{{ item.createTime }}
              </div>
            </div>
          </template>
        </div>
        <div
          v-else
          class="empty-wrapper"
        >
          <img :src="require('@/assets/images/tel-empty.png')">
          <p class="empty-text">
            {{ $t('marketing.commons.zwsj_21efd8') }}
          </p>
          <a @click="goInitTemplate">{{ $t('marketing.pages.mail_marketing.qxzysmb_53d8e2') }}</a>
        </div>
        <v-pagen
          class="pagen-wrapper"
          :pagedata.sync="pageData"
          @change="handlePageChange"
        />
      </div>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <fx-button
          type="primary"
          size="small"
          :loading="showTemlateLoading"
          @click="confirm"
        >
          {{ $t('marketing.commons.qd_aa7527') }}
        </fx-button>
        <fx-button size="small" @click="preview">
          {{ $t('marketing.commons.yl_645dbc') }}
        </fx-button>
        <fx-button size="small" @click="onClose">
          {{ $t('marketing.commons.qx_c08ab9') }}
        </fx-button>
      </div>
    </el-dialog>
    <mail-preview-dialog
      v-if="showPreviewDialog"
      :preview-info="previewInfo"
      :visible="showPreviewDialog"
      @closePreview="showPreviewDialog = false"
    />
  </div>
</template>

<script>
import util from '@/services/util'
import VPagen from '@/components/kitty/pagen'
import http from '@/services/http/index'
import MailPreviewDialog from './mail-preview-dialog'

export default {
  components: {
    ElDialog: FxUI.Dialog,
    VPagen,
    MailPreviewDialog,
    Tabs: FxUI.Tabs,
    TabPane: FxUI.TabPane,
    Input: FxUI.Input,
  },
  props: {
    visible: {
      type: Boolean,
      default: () => true,
    },
    showTemlateLoading: {
      type: Boolean,
      default: () => false,
    },
  },
  data() {
    return {
      previewInfo: {},
      showPreviewDialog: false,
      pageData: {
        layout: 'prev, pager, next, total, sizes, jumper',
        pageNum: 1,
        totalCount: 0,
        pageSize: 10,
        pageSizes: [10, 20, 30, 40],
      },
      templateList: [],
      selectId: '',
      activeName: '1',
      loading: false,
      searchText: '',
      subTabs: [
        {
          label: $t('marketing.pages.mail_marketing.fwbbjqmb_a5dbfa'),
          value: '1',
        },
        {
          label: $t('marketing.pages.mail_marketing.ymbjqmb_091ef9'),
          value: '2',
        },
      ],
      activeTab: '1',
      templateLoading: false,
    }
  },
  computed: {},
  mounted() {
    this.listPagerTemplate()
  },
  created() {},
  destroyed() {},
  methods: {
    goInitTemplate() {
      this.activeTab = '0'
      this.handleClickTab({
        name: '0',
      })
    },
    handleClickTab(tab) {
      console.log('tab.index: ', tab)
      this.searchText = ''
      this.pageData.pageNum = 1
      this.activeName = tab.name
      this.listPagerTemplate()
    },
    preview() {
      const templateType = Number(this.activeName)
      const apiName = templateType == 2 ? 'queryShanShanEditEmailDetail' : 'queryTemplateDetail'
      const params = {
        id: this.selectId,
      }
      if (templateType !== 2) {
        params.dataType = Number(this.activeName)
      }
      http[apiName](params)
        .then(res => {
          if (res && res.errCode == 0) {
            const creatorInfo = FS.contacts.getEmployeeById(res.data.fsUserId || res.data.creator) || {}
            this.previewInfo = {
              title: res.data.name,
              text2: $t('marketing.commons.cjsj_312f45') + util.formatDateTime(
                res.data.createTime,
                'YYYY-MM-DD hh:mm',
              ),
              content: res.data.html || res.data.content,
              text1:
                this.activeName === '0'
                  ? $t('marketing.commons.ysmb_b2af9c')
                  : $t('marketing.commons.cjz_ec37bb') + (creatorInfo.name || '--'),
            }
            this.showPreviewDialog = true
          }
        })
    },
    handleClick(item, index) {
      this.selectId = item.id
    },
    handlePageChange(data) {
      this.pageData.pageNum = data.pageNum
      this.pageData.pageSize = data.pageSize
      this.listPagerTemplate()
    },
    listPagerTemplate() {
      this.templateLoading = true
      const param = {
        pageNum: this.pageData.pageNum,
        pageSize: this.pageData.pageSize,
        dataType: Number(this.activeName),
      }
      if (this.searchText) {
        param.keyword = this.searchText
      }
      http.listPagerTemplate(param).then(res => {
        this.templateLoading = false
        if (res && res.errCode == 0) {
          this.loading && (this.loading = false)
          const _resData = res.data.result || []
          _resData.forEach(item => {
            item.createTime = util.formatDateTime(
              item.createTime,
              'YYYY-MM-DD hh:mm',
            )
            item.updateTime = util.formatDateTime(
              item.updateTime,
              'YYYY-MM-DD hh:mm',
            )
            item.id = item.emailId || item.id
          })
          this.pageData.totalCount = res.data.totalCount
          this.templateList = _resData
        }
      })
    },
    onClose() {
      this.$emit('onClose')
    },
    confirm(item) {
      this.$emit('selectTemplate', {
        id: this.selectId,
        dataType: Number(this.activeName),
      })
    },
    adminTemplate() {
      this.$router.push({ name: 'mail-template' })
    },
    handleRefresh() {
      this.loading = true
      this.pageData.pageNum = 1
      this.listPagerTemplate()
    },
    handleSearch() {
      this.pageData.pageNum = 1
      this.listPagerTemplate()
    },
  }, // 生命周期 - 销毁完成
}
</script>

<style lang="less">
.mail-template-dialog-warpper {
  .el-dialog__body {
    padding: 0;
    max-height: none;
  }
  .el-dialog__header {
    box-shadow: 0 2px 5px 0 rgba(69, 79, 91, 0.08);
  }
  .el-tabs__nav-scroll {
    padding-left: 24px;
  }
  .content {
    position: relative;
    min-height: 500px;
    padding-left: 24px;
    .content-header{
      display: flex;
      justify-content: space-between;
      .sub-tabs{
        display: flex;
        .tab-item{
          cursor: pointer;
          font-size: 14px;
          font-weight: 400;
          height: 22px;
          padding: 4px 12px;
          border: 1px solid #DEE1E6;
          &:first-child{
            border-radius: 4px 0 0 4px;
          }
          &:last-child{
            border-radius: 0 4px 4px 0;
          }
        }
        .active-tab{
          border: 1px solid #FF8000;
        }
      }
      .search-wrapper{
        flex: 1;
        display: flex;
        justify-content: flex-end;
        padding-right: 20px;
        }
    }
    .pagen-wrapper {
      position: absolute;
      bottom: 0;
      z-index: 1;
      width: 700px;
      padding-left: 0;
    }
    .admin-btn {
      font-size: 14px;
      color: var(--color-primary06,#407FFF);
      margin-top: 12px;
      padding-bottom: 19px;
      position: absolute;
      right: 26px;
      cursor: pointer;
    }
    .empty-wrapper {
      height: 500px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding-bottom: 30px;
      img {
        width: 100px;
      }
      .empty-text {
        margin-top: 10px;
        color: #909399;
      }
      a {
        cursor: pointer;
      }
    }
    .item-wrapper {
      height: 500px;
      overflow: scroll;
      padding-bottom: 46px;
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 16px;
      .item {
        min-width: auto;
        height: 200px;
        border-radius: 3px;
        background-color: rgba(255, 255, 255, 1);
        border: 1px solid rgba(221, 221, 221, 1);
        position: relative;
        .img-wrapper {
          width: 100%;
          height: 130px;
          position: relative;
          background-color: #cfe4ff;
          display: flex;
          align-items: center;
          justify-content: center;
          border-bottom: 1px solid #dddddd;
          .img {
            width: 50px;
          }
          .preview {
            position: absolute;
            bottom: 0;
            width: 100%;
            height: 29px;
            line-height: 29px;
            text-align: center;
            background-color: rgba(51, 51, 51, 0.6);
            color: #fff;
            z-index: 1;
          }
        }
        .title {
          margin: 11px 0 6px 12px;
          line-height: 20px;
          font-size: 14px;
          color: #181c25;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          /* autoprefixer: off */
          -webkit-box-orient: vertical;
          /* autoprefixer: on */
          -webkit-line-clamp: 1;
        }
        .time {
          margin-left: 12px;
          color: #91959e;
          font-size: 12px;
          margin-bottom: 15px;
        }
        &:hover {
          cursor: pointer;
        }
      }
      .big-item{
        height: 403px;
        .img-wrapper{
          background-color: #fff;
          height: 330px;
          width: 100%;
          .img{
            width: 100%;
            height: 100%;
            object-fit: cover;
            }
          .default-img{
            width: 155px;
            height: 127px;
          }
          }
      }
      .shan-item{
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        background-color: #e6e9ec;
        padding: 16px 16px 0;
        height: 350px;
        width: 100%;
        box-sizing: border-box;
        position: relative;
        .big-img-wrapper{
          height: 295px;
          width: 100%;
          background-color: #fff;
          .img{
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
        .title{
          height: 20px;
          padding-top: 10px;
          line-height: 20px;
          font-size: 14px;
          padding: 10px 0;
          font-weight: 700;
          color: #223353;
          width: 100%;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .selected {
        border: 1px solid rgba(64, 127, 255, 1);
        cursor: pointer;
      }
    }
  }
}
</style>
