<!-- 组件说明 -->
<template>
  <el-dialog class="mail-preview-warpper" append-to-body :visible="visible" :fullscreen="true" @close="onClose">
    <div class="wrapper">
      <div class="title">
        {{ showInfo.title || '--' }}
      </div>
      <div class="text1">
        {{ showInfo.text1 || '--' }}
      </div>
      <!-- 推广预约多一行发件人收件人信息 -->
      <div v-if="showInfo.text3" class="text2">
        {{ showInfo.text3 || '--' }}
      </div>
      <div class="text2">
        {{ showInfo.text2 || '--' }}
      </div>
      <div class="line" />
      <div class="content" v-html="showInfo.content" />
      <div class="line" />
      <div class="annex">
        <div class="title">
          {{ $t('marketing.commons.fjg_730756', { data: { option0: showFiles.length } }) }}
        </div>
        <div class="files">
          <div v-for="(file, index) in showFiles" :key="index" class="file">
            <!-- <span class="iconfont iconw_gif"></span> -->
            <file-icon :type="file.ext" />
            <span>{{ file.name + '.' + file.ext }}（{{ file.size / 1000 }} KB）</span>
          </div>
        </div>
      </div>
    </div>
    <loading-mask :show="showPreviewLoading" />
  </el-dialog>
</template>

<script>
import LoadingMask from '@/components/loading-mask/index';
import util from '@/services/util';
import VPagen from '@/components/kitty/pagen';
import http from '@/services/http/index';
import FileIcon from '@/components/file-icon';

export default {
  components: {
    ElDialog: FxUI.Dialog,
    VPagen,
    LoadingMask,
    FileIcon,
  },
  props: {
    previewInfo: {
      type: Object,
      default: () => {},
    },
    visible: {
      type: Boolean,
      default: () => true,
    },
    taskId: {
      // 本次推广内容ID，查看邮件发送内容
      type: String,
      default: () => '',
    },
    templateId: {
      // 邮件模板ID 查看邮件模板
      type: String,
      default: () => '',
    },
    templateType: {
      type: Number,
      default: 0,
    },
    files: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      showPreviewLoading: false,
      pageData: {
        layout: 'prev, pager, next, total, sizes, jumper',
        pageNum: 1,
        totalCount: 0,
        pageSize: 10,
        pageSizes: [10, 20, 30, 40],
      },
      templateList: [],
      selectId: '',
      showInfo: {},
      showFiles: [],
    };
  },
  computed: {},
  mounted() {},
  created() {
    if (this.taskId) {
      // 本次推广内容ID，查看邮件发送内容
      this.getTaskDetailById();
    } else if (this.templateId) {
      // 邮件模板ID 查看邮件模板
      this.queryTemplateDetail();
    } else {
      // 预览
      this.showInfo = this.previewInfo;
      this.showFiles = this.files;
    }
  },
  destroyed() {},
  methods: {
    getTaskDetailById() {
      this.showPreviewLoading = true;
      http.getTaskDetailById({ id: this.taskId }).then((res) => {
        this.showPreviewLoading = false;
        if (res && res.errCode == 0) {
          const resData = res.data;
          resData._sendTime = util.formatDateTime(resData.sendTime, 'YYYY-MM-DD hh:mm');
          if (resData.sendRange == 2) {
            let userNum = 0;
            let userText = '  ';
            resData.marketingGroupUser &&
              resData.marketingGroupUser.length &&
              resData.marketingGroupUser.forEach((e, index) => {
                if (index > 0) {
                  userText += '| ';
                }
                userNum += e.marketingGroupUserCount;
                userText += `${e.marketingGroupUserName} `;
              });
            resData.groupNum = userNum;
            resData.groupText = userText;
          }
          this.showInfo = {
            title: resData.subject,
            text1:
              resData.sendRange == 2
                ? `${$t('marketing.commons.fsdx_6a571c')} ${resData.groupText}`
                : resData.sendRange == 3
                ? $t('marketing.commons.fsdxchryyj_a0aa1c')
                : resData.sendRange == 8
                ? $t('marketing.pages.mail_marketing.fsdxhdryyj_2d5dbd')
                : resData.sendRange == 6
                ? $t('marketing.commons.fsdxzbbmry_b08eb5')
                : $t('marketing.pages.mail_marketing.fsdxscyjwj_94c7c2'),
            text2: $t('marketing.commons.fssj_f6cbc9') + resData._sendTime,
            text3: `${$t('marketing.commons.fjr_2df6a3')}${resData.senderUrl}`,
            content: res.data.html,
          };
          this.showFiles = resData.attachments.map((file) => ({
            name: file.attachmentName,
            path: file.attachmentPath,
            size: file.size,
            ext: file.ext,
          }));
        }
      });
    },
    queryTemplateDetail() {
      this.showPreviewLoading = true;
      const apiName = this.templateType === 2 ? 'queryShanShanEditEmailDetail' : 'queryTemplateDetail';
      const params = { id: this.templateId };
      if (this.templateType !== 2) {
        params.dataType = this.templateType;
      }
      http[apiName](params).then((res) => {
        this.showPreviewLoading = false;
        if (res && res.errCode === 0) {
          const resData = res.data;
          const creatorInfo = FS.contacts.getEmployeeById(resData.fsUserId || resData.creator) || {};
          this.showInfo = {
            title: resData.name,
            text1: $t('marketing.commons.cjz_ec37bb') + (creatorInfo.name || '--'),
            text2:
              $t('marketing.commons.cjsj_312f45') +
              (util.formatDateTime(resData.createTime, 'YYYY-MM-DD hh:mm') || '--'),
            content: resData.html || resData.content,
          };
        }
      });
    },
    onClose() {
      this.$emit('closePreview');
    },
  }, // 生命周期 - 销毁完成
};
</script>

<style lang="less">
.mail-preview-warpper {
  z-index: 1000;
  .el-dialog__body {
    padding: 0px 20px;
    padding: 16px 25px 14px;
    // 默认颜色
    color: #000;
  }
  .el-dialog__body {
    padding: 0px 20px;
  }
  .wrapper {
    margin: 0px 32px;
    .title {
      font-size: 16px;
      color: #181c25;
      margin-bottom: 13px;
      font-weight: bold;
    }
    .text1 .text2 {
      font-size: 13px;
      color: #666666;
      margin-bottom: 5px;
    }
    .line {
      width: 100%;
      height: 1px;
      background-color: #e9edf5;
      margin: 10px 0;
    }
    .content {
      // width: 930px; 去掉预览框宽度
      min-height: 300px;
      #yxt_tinymce_body_class{
        // 去掉ul,ol的全局margin0以及padding0样式 这样跟发出去的邮件样式一致
        ul,ol{
          display: block;
          margin-block-start: 1em;
          margin-block-end: 1em;
          padding-inline-start: 40px;
        }
      }
      ul {
        li {
          list-style: disc;
        }
      }
      ol {
        li {
          list-style: decimal;
        }
      }
    }
    .files {
      margin-bottom: 22px;
      .file {
        color: #545861;
        margin-bottom: 14px;
        .iconw_gif {
          margin-right: 7px;
        }
        .iconguanbi {
          font-size: 12px;
          cursor: pointer;
        }
      }
    }
  }
}
.hasContent table {
  border-collapse: separate;
  border: none;
}
</style>
