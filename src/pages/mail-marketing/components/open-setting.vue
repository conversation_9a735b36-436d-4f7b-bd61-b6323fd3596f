<!-- 组件说明 -->
<template>
  <div class="open-setting-wrapper">
    <div class="step">
      <i class="el-icon-success"></i>
      {{ $t('marketing.commons.fxym_dd6c67') }}{{ $t('marketing.commons.ycjcg_3ddd6a', {data: ({'option0': showMailBaseInfo.domain})}) }}
      <span
        class="link"
        @click="openDialog"
        v-if="showMailBaseInfo.defaultApiUser && !showMailBaseInfo.completeAllConfig"
        >{{ $t('marketing.commons.xg_8347a9') }}</span
      >
    </div>
    <div class="line"></div>
    <div class="step">
      <i class="el-icon-success" v-if="status1"></i>
      <i class="el-icon-error" v-else></i>
      {{ $t('marketing.pages.mail_marketing.qjfzyxxxzf_549593') }}
      <fx-button type="text" class="link" @click="check" :loading="checkLoading"
        >{{ $t('marketing.pages.mail_marketing.ksjc_28129b') }}</fx-button
      >
    </div>
    <div class="des">
      {{ $t('marketing.pages.mail_marketing.zxpzxydszz_5b1810') }}
      <a
        href="https://help.fxiaoke.com/2615/93d4/9188/9d58/9e09"
        target="_blank"
        >{{ $t('marketing.pages.mail_marketing.ljrhpz_d27f60') }} >></a
      >
    </div>
    <el-table
      class="setting-table"
      :data="tableData"
      row-class-name="setting-table-row"
      border
    >
      <el-table-column prop="data1" :label="$t('marketing.commons.mc_d7ec2d')" width="61"> </el-table-column>
      <el-table-column prop="data2" :label="$t('marketing.commons.zt_3fea7c')" width="48">
        <template slot-scope="scope">
          <i class="el-icon-success" v-if="scope.row.data2"></i>
          <i class="el-icon-error" v-else></i>
        </template>
      </el-table-column>
      <el-table-column prop="data3" :label="$t('marketing.pages.mail_marketing.zjjl_287adf')" width="151">
      </el-table-column>
      <el-table-column prop="data4" :label="$t('marketing.pages.mail_marketing.jllx_8fcadc')" width="73">
      </el-table-column>
      <el-table-column prop="data5" :label="$t('marketing.pages.mail_marketing.xpzdjlz_5aa809')"> </el-table-column>
    </el-table>
    <div class="button-wrapper">
      <fx-button
        class="copy-button"
        @click="copySetting('.copy-button')"
        size="mini"
        :data-clipboard-text="copySettingText"
        >{{ $t('marketing.pages.mail_marketing.yjfzpz_6137d5') }}</fx-button
      >
    </div>
    <el-dialog
      :title="$t('marketing.pages.mail_marketing.xgfxym_ba7d67')"
      width="620px"
      :visible="updateMailDialogVisible"
      v-if="updateMailDialogVisible"
      @close="closeUpdateSettingDailog"
      append-to-body
      class="update-setting-dialog"
    >
      <el-form
        :model="updateMailInfo"
        ref="updateMailForm"
        :rules="updateMailRules"
        class="form-wrapper"
        label-width="100px"
        label-position="left"
      >
        <el-form-item :label="$t('marketing.commons.fxym_dd6c67')" prop="Name">
          <fx-input
            v-model="updateMailInfo.Name"
            :placeholder="$t('marketing.commons.slcjhbkxgj_6619b7')"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <fx-button size="small" type="primary" @click="updateMail">{{ $t('marketing.pages.mail_marketing.qrbc_babc8f') }}</fx-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Clipboard from "clipboard";
import _ from "lodash";
import http from "@/services/http/index";
export default {
  components: {
ElTable: FxUI.Table,
ElTableColumn: FxUI.TableColumn,
ElDialog: FxUI.Dialog,
ElForm: FxUI.Form,
ElFormItem: FxUI.FormItem,
ElInput: FxUI.Input
},
  props: {
    mailBaseInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    var domainValidator = (rule, value, callback) => {
      if (_.trim(value) === "") {
        callback(new Error($t('marketing.commons.qsr_02cc4f')));
      } else {
        callback();
      }
    };
    return {
      showMailBaseInfo:this.mailBaseInfo,
      checkLoading: false,
      updateMailDialogVisible: false,
      status1: true,
      updateMailInfo: {
        Name: ""
      },
      updateMailRules: {
        Name: [{ required: true, validator: domainValidator, trigger: "blur" }]
      },
      copySettingText: "",
      tableData: []
    };
  },
  computed: {},
  methods: {
    getEnvironment() {
      http.getEnvironment().then(res => {
        if (res && res.errCode == 0) {
          this.cloudEnv = res.data.cloudEnv;
        }
      });
    },
    check() {
      this.getDomainDetail();
      this.checkLoading = true;
      http.checkDomainConfig().then(res => {
        if (res && res.errCode == 0) {
          this.checkLoading = false;
          if (res.data.isConfigSuccess) {
            this.status1 = true;
            this.$emit("checkOpen", false);
          }
        }
      });
    },
    openDialog() {
      this.updateMailDialogVisible = true;
      this.updateMailInfo.Name = this.showMailBaseInfo.domain;
    },
    closeUpdateSettingDailog() {
      this.updateMailDialogVisible = false;
    },
    copySetting(el) {
      // 解决首次点击无效的问题
      if (!this[`${el}_clipboard`]) {
        this[`${el}_clipboard`] = true;
        let clipboard = new Clipboard(el);
      }
      let clipboard = new Clipboard(el);
      clipboard.on("success", function(e) {
        FxUI.Message.success($t('marketing.commons.fzcg_20a495'));
        clipboard.destroy();
      });
      clipboard.on("error", function(e) {
        FxUI.Message.error($t('marketing.commons.fzsb_5154ae'));
        clipboard.destroy();
      });
    },
    getCompanyEmailBaseInfo() {
      http.getCompanyEmailBaseInfo().then(res => {
        if (res.errCode == 0) {
          this.showMailBaseInfo = res.data;
        }
      });
    },
    updateMail() { 
      this.$refs["updateMailForm"].validate(valid => {
        if (valid) {
          http
            .updateDomainConfig({
              newDomain: this.updateMailInfo.Name
            })
            .then(res => {
              this.updateMailDialogVisible = false;
              if (res && res.errCode == 0) {
                FxUI.Message.success($t('marketing.commons.gxcg_55aa63'));
                this.getCompanyEmailBaseInfo()
              }
            });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    getDomainDetail() {
      http.getDomainDetail().then(res => {
        if (res.errCode === 0) {
          let tableData = [];
          let resData = res.data;
          tableData.push({
            data1: "SPF",
            data2: (resData.verify & 2) == 2,
            data3: resData.spfDomain,
            data4: "TXT",
            data5: resData.spfValue
          });
          tableData.push({
            data1: "DKIM",
            data2: (resData.verify & 1) == 1,
            data3: resData.dkimDomain,
            data4: "TXT",
            data5: resData.dkimValue
          });
          tableData.push({
            data1: "DMARC",
            // 企业云环境为9 亚马逊云时，dmarc 判断条件为16，其他环境为32
            data2: this.cloudEnv == 9 ? (resData.verify & 16) == 16 : (resData.verify & 32) == 32,
            data3: resData.dmarcDomain,
            data4: "TXT",
            data5: resData.dmarcValue
          });
          tableData.push({
            data1: "MX",
            data2: (resData.verify & 4) == 4,
            data3: resData.mxDomain,
            data4: "MX",
            data5: resData.mxValue
          });
          tableData.forEach(item => {
            if (item.data2 == false) {
              this.status1 = false;
            }
          });
          this.tableData = tableData;
          this.copySettingText = `
${$t('marketing.pages.mail_marketing.fxympzxxrx_036b03')}

${$t('marketing.commons.mc_5b4786')}${tableData[0].data1}
${$t('marketing.commons.zjjl_a827da')}${tableData[0].data3}
${$t('marketing.commons.jllx_8de665')}${tableData[0].data4}
${$t('marketing.commons.xpzdjlz_57f90b')}:
${tableData[0].data5}


${$t('marketing.commons.mc_5b4786')}${tableData[1].data1}
${$t('marketing.commons.zjjl_a827da')}${tableData[1].data3}
${$t('marketing.commons.jllx_8de665')}${tableData[1].data4}
${$t('marketing.commons.xpzdjlz_57f90b')}:
${tableData[1].data5}

${$t('marketing.commons.mc_5b4786')}${tableData[2].data1}
${$t('marketing.commons.zjjl_a827da')}${tableData[2].data3}
${$t('marketing.commons.jllx_8de665')}${tableData[2].data4}
${$t('marketing.commons.xpzdjlz_57f90b')}:
${tableData[2].data5}

${$t('marketing.commons.mc_5b4786')}${tableData[3].data1}
${$t('marketing.commons.zjjl_a827da')}${tableData[3].data3}
${$t('marketing.commons.jllx_8de665')}${tableData[3].data4}
${$t('marketing.commons.xpzdjlz_57f90b')}:
${tableData[3].data5}

${$t('marketing.pages.mail_marketing.pzzn_449a25')}
${$t('marketing.pages.mail_marketing.bz_5c568f')}https://help.fxiaoke.com/2615/93d4/9188/9d58/9e09`;
        }
      });
    }
  },
  mounted() {
    this.check();
  },
  created() {
    this.getEnvironment();
    this.getDomainDetail();
  },
  destroyed() {} //生命周期 - 销毁完成
};
</script>

<style lang="less">
.open-setting-wrapper {
  position: relative;
  .setting-table-row {
    padding: 0 10px;
    .cell {
      font-size: 12px !important;
      color: #666666;
    }
  }
  .setting-table {
    margin: 13px 10px 0 27px;
    width: 542px;
  }
  .step {
    color: #181c25;
    font-size: 14px;
    .link {
      display: inline-block;
      margin-left: 22px;
      color: var(--color-primary06,#407FFF);
      font-size: 14px;
      cursor: pointer;
    }
  }
  .el-icon-success {
    color: #0aba07;
    font-size: 20px;
  }
  .el-icon-error {
    color: #f56c6c;
    font-size: 20px;
  }
  .line {
    height: 40px;
    width: 8px;
    border-right: 2px dotted #979797;
    position: relative;
    top: -2px;
  }
  .des {
    width: 550px;
    font-size: 12px;
    color: #666;
    margin-top: 13px;
    margin-left: 27px;
    span {
      color: var(--color-primary06,#407FFF);
      cursor: pointer;
    }
  }
  .button-wrapper {
    height: 47px;
    line-height: 47px;
    padding-right: 16px;
    text-align: right;
    border-right: 1px solid #e9edf5;
    border-left: 1px solid #e9edf5;
    border-bottom: 1px solid #e9edf5;
    background-color: #fafcff;
    margin-left: 27px;
    margin-right: 8px;
  }
  .el-table--border th {
    background-color: #fafcff;
    font-size: 14px;
    padding: 0px;
    color: #181c25;
  }
  .el-table th > .cell {
    padding: 0 0 0 9px !important;
  }
  .el-table td {
    padding: 0;
    background-color: #fafcff;
  }
  .el-table .cell {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    /* autoprefixer: off */
    -webkit-box-orient: vertical;
    /* autoprefixer: on */
    -webkit-line-clamp: 2;
  }
}
</style>
