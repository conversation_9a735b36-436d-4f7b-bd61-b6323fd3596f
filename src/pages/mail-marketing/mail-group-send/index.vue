<!-- 组件说明 -->
<template>
  <div class="ueditor-wrapper mail-troup-send">
    <div class="top">
      <div class="top-left">
        {{ (mailParam && mailParam.pageTitle) || title }}
      </div>
      <div class="top-right">
        <fx-button
          v-if="hiddenSomeParam"
          type="primary"
          @click="handleSave"
        >
          {{ $t('marketing.commons.bc_be5fbb') }}
        </fx-button>
        <fx-button
          v-else
          type="primary"
          :loading="btnState[0] === 1"
          @click="handleSend"
        >
          {{ $t('marketing.commons.fs_1535fc') }}
        </fx-button>
        <fx-button
          type="primary"
          @click="handlePreview"
        >
          {{ $t('marketing.commons.yl_645dbc') }}
        </fx-button>
        <fx-button @click="handleCancel">
          {{ $t('marketing.commons.qx_625fb2') }}
        </fx-button>
      </div>
    </div>
    <div
      v-if="toSettingSendReply"
      class="header"
    >
      <div class="icon icon-error" />
      {{ $t('marketing.pages.mail_marketing.hmyszfjryh_69d079') }}<a
        @click="handleSettingSendReply"
      >{{ $t('marketing.pages.mail_marketing.qwtj_60808b') }}</a>
      <span
        class="refresh"
        @click="handleRefresh"
      >{{ $t('marketing.commons.sx_694fc5') }}</span>
    </div>
    <div
      v-else
      class="header"
    >
      <div class="icon" />{{ $t('marketing.commons.jtkyedhyfq_1d1b2d', {data: ({'option0': thisDayAvaliableCount})}) }}
    </div>
    <div class="mail-template-wrapper">
      <div class="mail-send-wrapper">
        <el-form
          ref="mailForm"
          :model="mailForm"
          :rules="rules"
          label-width="130px"
          label-position="left"
          class="mailForm"
        >
          <el-form-item
            v-if="!hiddenSomeParam"
            :label="$t('marketing.commons.schd_a8559e')"
            prop="marketingObj"
          >
            <v-pick-selfobject
              v-model="mailForm.marketingObj"
              @input="changeMarketingObj"
            />
          </el-form-item>
          <div class="object-wrapper">
            <el-form-item
              v-if="!hiddenSomeParam"
              :label="$t('marketing.commons.qfdx_33ed6f')"
              class="mail-title"
              prop="sendObject"
            >
              <group-object
                v-if="!hiddenSomeParam"
                v-model="mailForm.sendObject"
                :is-custom-object="isCustomObject"
                :selected-participants="selectedParticipants"
                @change="selectChange"
              />
              <FilterNday
                v-model="mailForm.filterNDaySentUser"
              />
            </el-form-item>
          </div>
          <el-form-item
            v-if="!hiddenSomeParam"
            :label="$t('marketing.commons.fssj_f6cbc9')"
            prop="sendType"
          >
            <sendtime-pker v-model="mailForm.sendType" />

          </el-form-item>

          <el-form-item
            :label="$t('marketing.commons.fjr_2df6a3')"
            prop="sendUser"
            class="sendApplyUserWrapper"
          >
            <el-select
              v-model="mailForm.sendUser"
              class="el-select"
            >
              <el-option
                v-for="item in sendUserList"
                :key="item.id"
                :label="item.address"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            v-if="!hiddenSomeParam || (mailParam && mailParam.triggerScene === 'hexagon')"
            :label="$t('marketing.commons.hfr_fcf518')"
            prop="applyUser"
            class="sendApplyUserWrapper"
          >
            <el-select
              v-model="mailForm.applyUser"
              class="el-select"
            >
              <el-option
                v-for="item in applyUserList"
                :key="item.id"
                :label="item.address"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            :label="$t('marketing.commons.yjbt_1f2c85')"
            class="mail-title"
            prop="title"
          >
            <fx-input
              v-model="mailForm.title"
              :placeholder="$t('marketing.commons.qsr_02cc4f')"
              :maxlength="100"
              :show-word-limit="true"
            />
          </el-form-item>
          <el-form-item
            v-if="attachmentsVisible"
            :label="$t('marketing.commons.fj_c9a6ee')"
            class="ueditor-form-item"
          >
            <FileUploader2 :files="files" />
          </el-form-item>
          <el-form-item
            :label="$t('marketing.commons.yjnr_425169')"
            prop="desc"
            class="ueditor-form-item"
          >
            <!-- tinymce-editor二次打开，无法编辑，这里加个key，让编辑每次都重新初始化 -->
            <tinymce-editor
              v-if="!initTinyMceLoading"
              :key="tinymceEditorKey"
              :height="800"
              :content="mailForm.desc"
              :marketing-event-name="mailForm.marketingObj.name"
              :mail-title="mailForm.title"
              :marketing-event-id="
                $route.query.marketingEventId || mailForm.marketingObj.id
              "
              :show-marketing="
                !!$route.query.marketingEventId || !hiddenSomeParam
              "
              :source="source"
              :scene-no-need-template="sceneNoNeedTemplate"
              :show-crm-object-params="shouldShowCrmObjectParams"
              :trigger-scene="mailParam && mailParam.triggerScene"
              @setEditorContent="setEditorContent"
            />
            <div class="size-tips">
              <div>{{ $t('marketing.commons.wxts_0e9525') }}</div>
              <div>
                {{ $t('marketing.commons.dqyjzwjfjd_0b40c1') }}
                <span class="strong-font">{{ totalSize }}KB</span>，
                {{ $t('marketing.commons.mfyjyydmfl_3e06e3') }}
                <fx-link
                  type="standard"
                  class="tips-link"
                  :underline="false"
                  @click="openSendCloudSite"
                >
                  {{ $t('marketing.commons.gwzxkf_15d8e8') }}
                </fx-link>
              </div>
            </div>
          </el-form-item>
        </el-form>
        <mail-preview-dialog
          v-if="showPreviewDialog"
          :preview-info="previewInfo"
          :visible="showPreviewDialog"
          :files="files"
          @closePreview="showPreviewDialog = false"
        />
      </div>
    </div>
  </div>
</template>

<script>
import sendtimePker from '@/pages/qywx-manage/groups-messaging/create/components/sendtime-pker.vue'
import tinymceEditor from '@/components/tinymce-editor.vue'
import VScaffold from '@/components/scaffold'
import VHeader from '@/components/content-header/index'
import VPickSelfobject from '@/pages/promotion-activity/common/pick-selfobject'
import GroupObject from './group-object'
import Dialog from '@/components/dialog'
import MailPreviewDialog from '../components/mail-preview-dialog'
import FilterNday from '@/components/filter-nday'
import http from '@/services/http/index'
import utils from '@/services/util/index'
import { confirm } from '@/utils/globals'
import FileUploader2 from '@/components/file-uploader2'

export default {
  components: {
    tinymceEditor,
    VScaffold,
    VHeader,
    ElSelect: FxUI.Select.components.ElSelect,
    ElOption: FxUI.Select.components.ElSelect.components.ElOption,
    ElForm: FxUI.Form,
    ElFormItem: FxUI.FormItem,
    VPickSelfobject,
    GroupObject,
    elDatePicker: FxUI.DatePicker,
    Dialog,
    MailPreviewDialog,
    sendtimePker,
    FilterNday,
    FileUploader2,
  },
  props: {
    // 提供给自动化营销发送短信
    hiddenSomeParam: {
      type: Boolean,
      default: () => false,
    },
    mailParam: {
      type: Object,
      default: () => {},
    },
    showCrmObjectParams: {
      type: Boolean,
      default: true,
    },
    attachmentsVisible: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      tinymceEditorKey: 0,
      toSettingSendReply: false,
      title: $t('marketing.commons.xjyjqf_8c341a'),
      source: '',
      sceneNoNeedTemplate: false,
      isCustomObject: false,
      selectedParticipants: {},
      taskId: '',
      paramA: {},
      previewInfo: {},
      showPreviewDialog: false,
      thisDayAvaliableCount: 0,
      sendUserList: [],
      applyUserList: [],
      btnState: [],
      mailForm: {
        marketingObj: { id: this.$route.query.marketingEventId },
        title: '',
        sendObject: { type: 2 },
        sendType: { type: 1, time: '' },
        desc: '',
        sendUser: '',
        applyUser: '',
        filterNDaySentUser: {
          checked: false,
          day: 1,
        },
      },
      rules: {
        marketingObj: [
          {
            required: true,
            validator: this.validatorMarketingObject,
            trigger: 'blur',
          },
        ],
        title: [{ required: true, message: $t('marketing.commons.qsryjbt_b1240b') }],

        sendObject: [
          {
            required: true,
            validator: this.validateSendObject,
            trigger: 'blur',
          },
        ],
        sendType: [
          { required: true, validator: this.validateTime, trigger: 'blur' },
        ],
        sendUser: [
          { required: true, message: $t('marketing.commons.qxzfjr_b1986d'), trigger: 'blur' },
        ],
        applyUser: [
          { required: true, message: $t('marketing.commons.qxzhfr_115463'), trigger: 'blur' },
        ],
        desc: [{ required: true, message: $t('marketing.commons.qsryjnr_61a258'), trigger: 'blur' }],
      },
      sendTime: 1,
      files: [],
      initTinyMceLoading: false,
    }
  },
  computed: {
    totalSize() {
      const contentSize = new TextEncoder().encode(this.mailForm.desc || '').length
      const fileSize = this.files ? this.files.reduce((pre, cur) => pre + cur.size, 0) : 0
      return ((contentSize + fileSize) / 1000).toFixed(2)
    },
    // 计算是否显示CRM对象参数，避免频繁的属性变化
    shouldShowCrmObjectParams() {
      if (!this.showCrmObjectParams) {
        return this.showCrmObjectParams;
      }
      return this.mailForm.sendObject.type === 2;
    },
  },

  mounted() {
    this.tinymceEditorKey += 1
  },
  created() {
    this.init()
  },
  destroyed() {},
  methods: {
    openSendCloudSite() {
      window.open('https://www.sendcloud.net/prices', '_blank')
    },
    handleRefresh() {
      this.initSendApply(0)
      this.initSendApply(1)
    },
    handleSettingSendReply() {
      const route = this.$router.resolve({
        name: 'setting-setitems',
        query: { type: 'v-mail-set' },
      })
      window.open(route.href, '_blank')
    },
    getContentUrlInfo() {
      // 获取链接url及内容 待发送成功后传给后台
      let listA = document
        .getElementById('marketing-tinymce_ifr')
        .contentWindow.document.getElementsByTagName('a')
      console.log(
        document
          .getElementById('marketing-tinymce_ifr')
          .contentWindow.document.getElementsByTagName('a'),
      )
      const paramA = {}
      listA = Array.from(listA)
      listA.forEach(item => {
        if (
          !(item.dataset.source && item.dataset.source == 'marketing-source')
        ) {
          paramA[item.dataset.mceHref] = item.innerText
        }
      })
      console.log(paramA)
      this.paramA = paramA
    },
    // 如果是传入的对象，校验是否所有对象没有都没有email
    isHasEmial() {
      const participantsList = this.mailForm.sendObject.participantsList
        .colleague
      let _showTipsDialog = true
      const _enrollMailMap = {}
      participantsList.forEach(item => {
        if (item.email) {
          _enrollMailMap[item.id] = item.email
          _showTipsDialog = false
        }
      })
      this.enrollMailMap = _enrollMailMap
      if (_showTipsDialog) {
        confirm($t('marketing.commons.sxchryjwyj_5e5d0a'), $t('marketing.commons.qr_e83a25'), {}).then(() => {})
      }
      return _showTipsDialog
    },
    changeMarketingObj() {
      this.$refs.mailForm.validateField('marketingObj')
    },
    getPreviewData() {
      let _text1 = ''
      let _text2 = ''
      console.log('getPreviewData:', this.mailForm)
      if (this.mailForm.sendType.type == 2) {
        const sendTiem = utils.formatDateTime(
          this.mailForm.sendType.time,
          'YYYY-MM-DD hh:mm',
        )
        _text2 = $t('marketing.pages.mail_marketing.yyfssj_a95307') + sendTiem
      } else {
        _text2 = $t('marketing.pages.mail_marketing.fssjljfs_e8dad6')
      }
      if (this.mailForm.sendObject.type == 2) {
        _text1 = $t('marketing.commons.fsdx_6a571c')
        this.mailForm.sendObject.marketingUserGroupNames
          && this.mailForm.sendObject.marketingUserGroupNames.forEach(item => {
            _text1 += ` ${item}`
          })
      } else if (this.mailForm.sendObject.type == 3) {
        _text1 = $t('marketing.commons.fsdxchryyj_a0aa1c')
      } else if (this.mailForm.sendObject.type == 6) {
        _text1 = $t('marketing.commons.fsdxzbbmry_b08eb5')
      } else if (this.mailForm.sendObject.type == 8) {
        _text1 = $t('marketing.pages.mail_marketing.fsdxhdbmry_441f85')
      } else if (this.mailForm.sendObject.type == 1) {
        _text1 = $t('marketing.pages.mail_marketing.fsdxyjscdz_d9c7d3')
      }
      let formatContent = this.mailForm.desc
      if (formatContent.indexOf('yxt_tinymce_body_class') == -1) {
        formatContent = `<div id="yxt_tinymce_body_class" style="font-size:16px;line-height: 1.4;font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;">${this.mailForm.desc}</div>`
      }
      return {
        title: this.mailForm.title,
        text1: this.hiddenSomeParam ? '  ' : _text1,
        text2: this.hiddenSomeParam ? '  ' : _text2,
        content: formatContent,
      }
    },
    handlePreview() {
      this.previewInfo = this.getPreviewData()
      this.showPreviewDialog = true
    },
    validateTime(rule, value, callback) {
      if (value.type === 2 && !value.time) {
        return callback(new Error($t('marketing.commons.qxzfssj_cc05ec')))
      }
      callback()
    },
    validatorMarketingObject(rule, value, callback) {
      if (!value || !value.id) {
        return callback(new Error($t('marketing.commons.qxzschd_9e5f1b')))
      }
      callback()
    },
    validateSendObject(rule, value, callback) {
      console.log('value', value)
      if (
        value.type === 2
        && (!value.marketingUserGroupIds || !value.marketingUserGroupIds.length)
      ) {
        return callback(new Error($t('marketing.commons.qxzmbrq_b8cc9a')))
      } if (value.type === 1 && !value.taPath) {
        return callback(new Error($t('marketing.pages.mail_marketing.qscyjdz_bce85a')))
      } if (
        value.type === 3
        && (!value.participantsList.colleague
          || !value.participantsList.colleague.length)
      ) {
        return callback(new Error($t('marketing.commons.qxzchry_f5b0b6')))
      }
      callback()
    },
    setEditorContent(val) {
      this.mailForm.desc = val
      this.$refs.mailForm.validateField('desc')
    },
    // 自动化流程新建邮件，返回信息
    handleSave() {
      console.log(this.$refs.mailForm.validateField('desc'))
      this.$refs.mailForm.validate((valid, object) => {
        if (valid) {
          let formatContent = this.mailForm.desc
          if (formatContent.indexOf('yxt_tinymce_body_class') == -1) {
            formatContent = `<div id="yxt_tinymce_body_class" style="font-size:16px;line-height: 1.4;font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;">${this.mailForm.desc}</div>`
          }
          const param = {
            sendUser: this.mailForm.sendUser,
            title: this.mailForm.title,
            desc: formatContent,
            attachments: this.files,
            applyUser: this.mailForm.applyUser,
          }

          this.sendUserList.forEach(item => {
            if (item.id == param.sendUser) {
              param.email = item.address
            }
          })
          this.$emit('save', param)
        }
      })
    },
    handleSend() {
      this.$refs.mailForm.validate(valid => {
        if (valid) {
          this.getContentUrlInfo()
          if (this.isCustomObject && this.isHasEmial()) {
            return
          }
          this.fakeAddMarketingActivity()
          this.btnState = [1]
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    fakeAddMarketingActivity() {
      let formatContent = this.mailForm.desc
      if (formatContent.indexOf('yxt_tinymce_body_class') == -1) {
        formatContent = `<div id="yxt_tinymce_body_class" style="font-size:16px;line-height: 1.4;font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;">${this.mailForm.desc}</div>`
      }
      const { filterNDaySentUser } = this.mailForm
      const attachments = this.files.map(file => ({
        attachmentName: file.name,
        attachmentPath: file.path,
        size: file.size,
        ext: file.ext,
      }))

      const params = {
        marketingEventId: this.mailForm.marketingObj.id,
        spreadType: 6,
        marketingActivityAuditData: {
          sendGroup: this.getPreviewData().text1,
        },
        mailServiceMarketingActivityVO: {
          title: this.mailForm.title,
          type: this.mailForm.sendType.type,
          sendRange: this.mailForm.sendObject.type,
          content: formatContent,
          mailType: 1,
          sendMailIds: [this.mailForm.sendUser],
          replyToIds: [this.mailForm.applyUser],
          attachments,
        },
      }
      if (filterNDaySentUser.checked) {
        params.mailServiceMarketingActivityVO.filterNDaySentUser = filterNDaySentUser.day
      }
      if (params.mailServiceMarketingActivityVO.type == 2) {
        params.mailServiceMarketingActivityVO.fixedTime = this.mailForm.sendType.time
      }
      if (params.mailServiceMarketingActivityVO.sendRange == 1) {
        params.mailServiceMarketingActivityVO.taPath = this.mailForm.sendObject.taPath
        params.mailServiceMarketingActivityVO.fileName = this.mailForm.sendObject.fileName
      } else if (params.mailServiceMarketingActivityVO.sendRange == 2) {
        params.mailServiceMarketingActivityVO.marketingUserGroupIds = this.mailForm.sendObject.marketingUserGroupIds
      } else if (params.mailServiceMarketingActivityVO.sendRange == 3) {
        params.mailServiceMarketingActivityVO.enrollMailMap = this.enrollMailMap
      }
      console.log('params', params)
      http
        .fakeAddMarketingActivity(params)
        .then(res => {
          this.btnState = []
          if (res.errCode === 0) {
            FxUI.Message.success($t('marketing.commons.fscg_9db9a7'))
            this.taskId = res.data.associateId
            this.addMailLinkContent()
            this.$router.replace({ name: 'mail-home' })
          }
        })
        .catch(() => {})
        .then(() => {
          this.state = []
        })
    },
    addMailLinkContent() {
      if (Object.keys(this.paramA).length == 0) {
        return
      }
      http
        .addMailLinkContent({
          linkContent: this.paramA,
          taskId: this.taskId,
        })
        .then(res => {})
    },
    handleCancel() {
      // 来自自动化营销弹框
      if (this.hiddenSomeParam) {
        this.$emit('close')
      } else {
        this.$router.back()
      }
    },
    selectChange(e) {
      console.log('selectChangethis.mailForm.descthis.mailForm.desc', this.mailForm.desc)
      // 只有在真正需要重新初始化时才执行
      const needReinit = (e.type == 2 || e.type == 1) && 
                        (this.mailForm.sendObject.type !== e.type);
      
      if (needReinit) {
        // 手动加载tinymce  重新执行init
        this.initTinyMceLoading = true
        // 增加 key 值，确保组件完全重新创建
        this.tinymceEditorKey += 1
        setTimeout(() => {
          this.initTinyMceLoading = false
        }, 500)
      }
      if (e.type == 2) {
        // 选择目标对象
        const _data = e.data
        const ids = []
        const names = []
        if (_data.length) {
          _data.forEach(item => {
            ids.push(item.id)
            names.push(item.name)
          })
        }
        this.mailForm.sendObject = {
          type: 2,
          marketingUserGroupIds: ids,
          marketingUserGroupNames: names,
        }
      } else if (e.type == 1) {
        // 上传邮件文件
        this.mailForm.sendObject = { type: 1, taPath: e.data, fileName: e.fileName }
      } else if (e.type == 3) {
        // 参会人员
        this.mailForm.sendObject = { type: 3, participantsList: e.data }
      }
      this.$refs.mailForm.validateField('sendObject')
    },
    initSendApply(type) {
      http
        .querySendReplyData({
          pageNum: 1,
          pageSize: 99,
          type,
        })
        .then(res => {
          if (res && res.errCode == 0) {
            const _sendUser = []
            if (res.data && res.data.result && res.data.result.length) {
              this.toSettingSendReply = false
              const resData = res.data.result
              resData.forEach(item => {
                if (item.defaultValue) {
                  type == 1
                    ? (this.mailForm.applyUser = item.id)
                    : (this.mailForm.sendUser = item.id)
                }
                _sendUser.push({
                  address: item.address,
                  id: item.id,
                  defaultValue: item.defaultValue,
                })
              })
              if (type == 0) {
                // 自动化营销编辑内容带入发件人
                this.sendUserList = _sendUser
                if (this.hiddenSomeParam && this.mailParam.desc) {
                  this.mailForm.sendUser = this.mailParam.sendUser
                }
              } else if (type == 1) {
                this.applyUserList = _sendUser
                if (this.mailParam && this.mailParam.applyUser) {
                  this.mailForm.applyUser = this.mailParam.applyUser
                }
              }
            } else {
              this.toSettingSendReply = true
            }
          }
        })
    },
    queryMarketingActivityDetail() {
      http
        .queryMarketingActivityDetail({
          id: this.$route.query.marketingActivityId,
        })
        .then(res => {
          if (res && res.errCode == 0) {
            this.mailForm.marketingObj = { id: res.data.marketingEventId,name: res.data.marketingEventName }
            this.mailForm.title = res.data.name
            const result = res.data.mailGroupSendMessageDetailResult
            this.mailForm.sendUser = result.sendMailIds[0]
            this.mailForm.applyUser = result.replyToIds[0]
            this.files = (result.attachments || []).map(file => ({
              name: file.attachmentName,
              path: file.attachmentPath,
              size: file.size,
              ext: file.ext,
            }))
            if (result.sendRange == 2) {
              const marketingGroupUser = []
              result.marketingGroupUser
                && result.marketingGroupUser.length
                && result.marketingGroupUser.forEach(item => {
                  marketingGroupUser.push(item.marketingGroupUserId)
                })
              this.mailForm.sendObject = {
                type: 2,
                crowd: marketingGroupUser,
                refresh: true,
              }
            }
          }
        })
    },
    getTaskDetailById() {
      http.getTaskDetailById({ id: this.$route.query.taskId }).then(res => {
        if (res && res.errCode == 0) {
          this.mailForm.desc = res.data.html
          this.$refs.ma
          console.log(this.mailForm.desc)
        }
      })
    },
    async init() {
      // 参会人员发送邮件
      if (this.$route.query.source == 'participant') {
        this.isCustomObject = true
        this.source = 'participant'
        this.selectedParticipants = this.$route.params.participants || {}
        console.log('selectedParticipants', this.selectedParticipants)
      }
      http.queryMailAccountInfo().then(res => {
        if (res && res.data) {
          this.thisDayAvaliableCount = res.data.thisDayAvaliableCount
        }
      })
      this.initSendApply(0)
      this.initSendApply(1)
      // 自动化营销编辑邮件
      if (this.mailParam && this.mailParam.desc) {
        this.title = $t('marketing.pages.mail_marketing.bjyjqf_b85ee4')
        this.mailForm.title = this.mailParam.title
        this.mailForm.desc = this.mailParam.desc
        this.files = this.mailParam.attachments
      }
      // 自动化营销发送邮件支持插入参数
      if (this.mailParam && this.mailParam.triggerScene) {
        this.source = this.mailParam.triggerScene
        this.sceneNoNeedTemplate = true
      }
      // 再次发送
      if (this.$route.query.marketingActivityId && this.$route.query.taskId) {
        this.queryMarketingActivityDetail()
        this.getTaskDetailById()
      }
    },
  }, // 生命周期 - 销毁完成
}
</script>

<style lang="less" scoped>
.change-group-dialog {
  .el-dialog__body {
    width: 70%;
    margin: 0 auto;
  }
}

.ueditor-form-item {
  position: relative;
  .el-form-item__content {
    line-height: initial !important;
  }
    .size-tips {
      margin-top: 8px;
      color: #737c8c;
      font-size: 12px;
      line-height: 24px;
      .tips-link {
        font-size: 12px;
        line-height: 24px;
        margin-top: -2px;
      }
    }
    .strong-font {
      color: #181c25;
    }
}
.mail-troup-send {
  .top {
    flex: 0 0 auto;
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    justify-content: space-between;
    padding: 0 22px;
    height: 72px;
    background: #f4f6f9;
    .top-left {
      font-size: 28px;
      color: #212b36;
    }
  }

  .header {
    display: flex;
    align-items: center;
    padding: 0 20px;
    height: 40px;
    font-size: 12px;
    color: #999999;
    background: #f4f4f4;
    .icon {
      flex: 0 0 auto;
      margin-right: 10px;
      height: 20px;
      width: 20px;
      background-image: url("../../../assets/images/icons/icon_tips.png");
      background-position: center center;
      background-size: contain;
    }
    .icon-error {
      background-size: 20px 20px;
      background-image: url("../../../assets/images/icons/error-2x.png");
    }
    .refresh {
      color: var(--color-primary06,#407FFF);
      display: inline-block;
      margin-left: 15px;
      cursor: pointer;
    }
  }
  .el-form-item.is-required:not(.is-no-asterisk) > .el-form-item__label:before {
    position: relative;
    left: 0;
  }
  .mail-template-wrapper {
    width: 100%;
  }
  .mail-send-wrapper {
    margin-left: 60px;
    padding: 20px 0;
    width: 1060px;
    box-sizing: border-box;
    .mail-title {
      font-size: 14px;
      color: #151515;
    }
    .object-wrapper {
      position: relative;
    }
  }
  .sendApplyUserWrapper {
    .fx-select {
      width: 930px;
    }
  }
}
</style>
