<!-- 组件说明 -->
<template>
  <div class="mail-marketing-template-wrapper">
    <div class="top">
      <div class="top-left">
        {{ title }}
      </div>
      <div class="top-right">
        <fx-button
          type="primary"
          :loading="btnState[0] === 1"
          @click="handleSend"
        >
          {{ $t('marketing.commons.bc_be5fbb') }}
        </fx-button>
        <fx-button
          type="primary"
          @click="handlePreview"
        >
          {{ $t('marketing.commons.yl_645dbc') }}
        </fx-button>
        <fx-button @click="handleCancel">
          {{ $t('marketing.commons.qx_625fb2') }}
        </fx-button>
      </div>
    </div>
    <div class="mail-template-wrapper">
      <el-form
        ref="templateForm"
        :model="templateForm"
        :rules="rules"
        label-width="130px"
        label-position="left"
        class="templateForm"
      >
        <el-form-item
          :label="scene === 'mail-marketing' ? $t('marketing.commons.yjzt_1501d5') : $t('marketing.commons.mbmc_e1ea39')"
          class="mail-title"
          prop="name"
        >
          <fx-input
            v-model="templateForm.name"
            :placeholder="$t('marketing.commons.qsr_02cc4f')"
            :maxlength="64"
            :show-word-limit="true"
          />
        </el-form-item>
        <el-form-item
          :label="scene === 'mail-marketing' ? $t('marketing.commons.yjzw_82acd6') : $t('marketing.pages.mail_marketing.mbnr_7bcd31')"
          class="ueditor-form-item"
          prop="desc"
        >
          <tinymce-editor
            v-if="showEditor"
            :height="800"
            :content="templateForm.desc"
            :show-marketing="scene === 'mail-marketing'"
            :marketing-event-id="marketingEventId"
            :source="scene"
            @setEditorContent="setEditorContent"
          />
        </el-form-item>
      </el-form>
    </div>
    <mail-preview-dialog
      v-if="showPreviewDialog"
      :preview-info="previewInfo"
      :visible="showPreviewDialog"
      @closePreview="showPreviewDialog = false"
    />
  </div>
</template>

<script>
import MailPreviewDialog from '../../components/mail-preview-dialog'
import tinymceEditor from '@/components/tinymce-editor.vue'

import Ueditor from '@/components/ueditor'
import Dialog from '@/components/dialog'
import { requireAsync } from '@/utils'
import util from '@/services/util'
import http from '@/services/http/index'

export default {
  components: {
    ElForm: FxUI.Form,
    ElFormItem: FxUI.FormItem,
    Dialog,
    tinymceEditor,
    MailPreviewDialog,
  },
  data() {
    return {
      previewInfo: {},
      showPreviewDialog: false,
      showEditor: true,
      id: '',
      title: $t('marketing.commons.xjyjmb_1a318d'),
      editor: null,
      btnState: [],
      btnNames: [$t('marketing.commons.bc_be5fbb'), $t('marketing.commons.qx_625fb2')],
      templateForm: {
        name: '',
        desc: '',
      },
      rules: {
        name: [{ required: true, message: $t('marketing.commons.qsrmbmc_8f21b9'), trigger: 'blur' }],
        desc: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (!this.templateForm.desc || this.templateForm.desc == '<div id="yxt_tinymce_body_class" style="font-size:12px;line-height: 1.4;font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', Roboto, Oxygen, Ubuntu, Cantarell, \'Open Sans\', \'Helvetica Neue\', sans-serif;"></div>') {
                callback(new Error($t('marketing.commons.qsrmbnr_73536b')))
              } else {
                callback()
              }
            },
            trigger: 'blur',
          },
        ],
      },
      smd_menus: ['marketingEvent', 'materials'],
      smd_tabbar: [10, 1, 4, 24],
      smd_marketingEventTabbar: [10, 1, 4, 24],
      scene: 'mail-template',
      marketingEventId: '',
    }
  },
  mounted() {},
  created() {
    this.id = this.$route.query.id
    this.scene = this.$route.query.scene || 'mail-template'
    if (this.scene === 'mail-marketing') {
      this.title = $t('marketing.commons.szyjtgnr_bcc199')
    }
    this.marketingEventId = this.$route.query.marketingEventId || ''
    if (this.id) {
      this.showEditor = false
      if (this.scene === 'mail-marketing') {
        this.title = $t('marketing.commons.szyjtgnr_bcc199')
        http.getEmailMaterial({ id: this.id }).then(res => {
          this.showEditor = true
          if (res && res.errCode === 0) {
            this.templateForm.name = res.data.title
            this.templateForm.desc = res.data.content
          }
        })
      } else {
        this.title = $t('marketing.pages.mail_marketing.bjyjmb_0b324f')
        http.queryTemplateDetail({ id: this.id, dataType: 1 }).then(res => {
          this.showEditor = true
          if (res && res.errCode == 0) {
            this.templateForm.name = res.data.name
            this.templateForm.desc = res.data.html
          }
        })
      }
    }
  },
  destroyed() {},
  methods: {
    handlePreview() {
      let formatContent = this.templateForm.desc
      if (formatContent.indexOf('yxt_tinymce_body_class') == -1) {
        formatContent = `<div id="yxt_tinymce_body_class" style="font-size:16px;line-height: 1.4;font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;">${this.templateForm.desc}</div>`
      }
      this.previewInfo = {
        title: this.templateForm.name,
        text1: '   ',
        text2: '   ',
        content: formatContent,
      }
      this.showPreviewDialog = true
    },
    setEditorContent(val) {
      this.templateForm.desc = val
      this.$refs.templateForm.validateField('desc')
    },
    validEditorContent() {
      this.$refs.templateForm.validateField('desc')
    },
    handleSend() {
      this.$refs.templateForm.validate(valid => {
        if (valid) {
          let formatContent = this.templateForm.desc
          if (formatContent.indexOf('yxt_tinymce_body_class') == -1) {
            formatContent = `<div id="yxt_tinymce_body_class" style="font-size:16px;line-height: 1.4;font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;">${this.templateForm.desc}</div>`
          }
          this.btnState = [1]
          if (this.id) {
            this.updateTemplate({
              html: formatContent,
              name: this.templateForm.name,
              templateId: this.id,
            })
          } else {
            // 新建模板
            this.addTemplate({
              content: formatContent,
              templateName: this.templateForm.name,
            })
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    updateTemplate(reqParams) {
      // 更新模板
      if (this.scene === 'mail-marketing') {
        http.saveEmailMaterial({
          id: reqParams.templateId,
          title: reqParams.name,
          content: reqParams.html,
        })
          .then(res => {
            if (res && res.errCode === 0) {
              FxUI.Message.success($t('marketing.commons.gxcg_55aa63'))
              this.btnState = []
              this.$route.query.mailMarketingId = res.data.id
              this.$router.back()
            }
          })
      } else {
        http
          .updateTemplateDetail(reqParams)
          .then(res => {
            if (res && res.errCode === 0) {
              FxUI.Message.success($t('marketing.commons.gxcg_55aa63'))
              this.btnState = []
              this.$router.push({ name: 'mail-template' })
            }
          })
      }
    },
    addTemplate(reqParams) {
      if (this.scene === 'mail-marketing') {
        http.saveEmailMaterial({
          title: reqParams.templateName,
          content: reqParams.content,
        }).then(res => {
          if (res && res.errCode === 0) {
            FxUI.Message.success($t('marketing.commons.tjcg_3fdaea'))
            this.btnState = []
            this.$route.query.mailMarketingId = res.data.id
            this.$router.back()
          }
        })
      } else {
        http.addTemplate(reqParams).then(res => {
          if (res && res.errCode === 0) {
            FxUI.Message.success($t('marketing.commons.tjcg_3fdaea'))
            this.btnState = []
            this.$router.push({ name: 'mail-template' })
          }
        })
      }
    },
    handleCancel() {
      this.$router.back()
    },
  }, // 生命周期 - 销毁完成
}
</script>

<style lang="less">
.mail-marketing-template-wrapper {
  .top {
    flex: 0 0 auto;
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    justify-content: space-between;
    padding: 0 22px;
    height: 72px;
    background: #f4f6f9;
    .top-left {
      font-size: 28px;
      color: #212b36;
    }
  }
  .mail-template-wrapper {
    margin-left: 60px;
    padding: 20px 0;
    width: 1060px;
    box-sizing: border-box;
    .mail-title {
      font-size: 14px;
      color: #151515;
    }
  }
}
</style>
