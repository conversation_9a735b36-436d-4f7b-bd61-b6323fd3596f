<!-- 组件说明 -->
<template>
  <div class="mai-template-wrapper">
    <scroll-wrapper>
      <div slot="header">
        <v-header
          :title="$t('marketing.commons.yjmb_532e4a')"
          :border="true"
        >
          <div
            slot="right"
            class="content-tool"
          >
            <fx-input
              v-model="searchText"
              size="small"
              class="search-input"
              :placeholder="$t('marketing.pages.mail_marketing.ssyjmbmc_aedf89')"
              prefix-icon="el-icon-search"
              @change="searchTemplate"
            />
            <fx-button
              class="add-button"
              type="primary"
              size="small"
              @click="selectDialogVisible = true"
            >
              <i class="yxt-icon16 icon-add" />
              <span>{{ $t('marketing.commons.xjyjmb_1a318d') }}</span>
            </fx-button>
          </div>
        </v-header>
      </div>
      <div
        v-if="flag_showLoadingMask"
        class="km-g-loading-mask"
      >
        <span class="loading" />
      </div>
      <mail-template-list
        v-else-if="!flag_showLoadingMask && templateList.length"
        class="template-list-wrapper"
        :list="templateList"
        @update="listPagerTemplate"
      />
      <div
        v-else
        class="empty-wrapper"
      >
        <img :src="require('@/assets/images/tel-empty.png')">
        <p class="empty-text">
          {{ $t('marketing.commons.zwsj_21efd8') }}
        </p>
      </div>
      <div
        slot="footer"
        class="sms-template-pagination-wrapper"
      >
        <v-pagen
          :pagedata.sync="pageData"
          @change="handlePageChange"
        />
      </div>
      <selectDialog
        :visible="selectDialogVisible"
        @update:visible="handleClose"
      />
    </scroll-wrapper>
  </div>
</template>

<script>
import VHeader from '@/components/content-header/index'
import VPagen from '@/components/kitty/pagen'
import http from '@/services/http/index'
import MailTemplateList from './templateList'
import ScrollWrapper from '@/components/table-ex/scroll-wrapper'
import util from '@/services/util'
import selectDialog from './components/selectDialog.vue'

export default {
  components: {
    VHeader,
    VPagen,
    MailTemplateList,
    ScrollWrapper,
    selectDialog,
  },
  data() {
    return {
      searchText: '',
      flag_showLoadingMask: true,
      pageData: {
        layout: 'prev, pager, next, total, sizes, jumper',
        pageNum: 1,
        totalCount: 0,
        pageSize: 10,
        pageSizes: [10, 20, 30, 40],
      },
      templateList: [],
      selectDialogVisible: false,
    }
  },
  computed: {
    isEmailOpen() {
      return this.$store.state.MailMarketing.completeAllConfig
    },
  },
  mounted() {
    this.queryMailMarketingOpenStatus()
  },
  destroyed() {},
  methods: {
    handleClose(val) {
      this.selectDialogVisible = val
    },
    searchTemplate() {
      this.pageData.pageNum = 1
      this.listPagerTemplate()
    },
    handlePageChange(data) {
      this.pageData.pageNum = data.pageNum
      this.pageData.pageSize = data.pageSize
      this.listPagerTemplate()
    },
    listPagerTemplate() {
      const param = {
        pageNum: this.pageData.pageNum,
        pageSize: this.pageData.pageSize,
        dataType: 1,
      }
      if (this.searchText) {
        param.keyword = this.searchText
      }
      this.flag_showLoadingMask = true
      http.listPagerMergeTemplate(param).then(res => {
        this.flag_showLoadingMask = false
        if (res && res.errCode == 0) {
          const _resData = res.data.result
          _resData.forEach(item => {
            const creatorInfo = FS.contacts.getEmployeeById(item.fsUserId) || {}
            item.creator = creatorInfo.name || ''
            item.createTime = util.formatDateTime(
              item.createTime,
              'YYYY-MM-DD hh:mm',
            )
          })
          this.pageData.totalCount = res.data.totalCount
          this.templateList = _resData
        }
      })
    },
    async queryMailMarketingOpenStatus() {
      const res = await this.$store.dispatch('MailMarketing/queryMailMarketingOpenStatus')
      if (res && res.errCode === 0 && res.data && res.data.completeAllConfig === true) {
        this.listPagerTemplate()
      }
    },
  }, // 生命周期 - 销毁完成
}
</script>

<style lang="less" scoped>
.mai-template-wrapper {
  display: flex;
  flex-flow: column nowrap;
  height: 100%;
  .search-input {
    width: 220px;
    margin-right: 20px;
  }
  .header {
    flex: 0 0 auto;
  }
  .footer {
    flex: 0 0 auto;
  }
  .template-list-wrapper {
    flex: 1 1 auto;
  }
  .empty-wrapper {
    height: 500px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    img {
      width: 100px;
    }
    .empty-text {
      line-height: 60px;
      color: #909399;
    }
  }
}
</style>
