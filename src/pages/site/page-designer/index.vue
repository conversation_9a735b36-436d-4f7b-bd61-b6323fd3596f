<template>
  <div :class="[$style.marketing__page_designer, { [$style['marketing__page_designer--agent']]: isAgent }]">
    <Designer
      ref="designer"
      :is-agent="isAgent"
      :upload="upload"
      :member-config="memberConfig"
      :marketing-event-detail-data="marketingEventDetailData"
      @close="handlePageClose"
      @saveAndExt="handleSaveAndExt"
      @classification="getClassification"
    />
    <VideoDialog
      v-if="videoDialog"
      :visible.sync="videoDialog"
      @confirm="handleUploadConfirm"
      @close="handleUploadCancel"
    />
    <LiveDialog
      v-if="liveDialog"
      :visible.sync="liveDialog"
      @confirm="val => handleUploadConfirm(val, 'live')"
      @close="handleUploadCancel"
    />
    <!-- 暂时去掉二维码绘制  需求未确定 -->
    <QrcodeDialog
      v-if="qrcodeDialog"
      :visible.sync="qrcodeDialog"
      @confirm="handleUploadConfirm"
      @close="handleUploadCancel"
    />
    <WxWorkQrcodeDialog
      v-if="wxWorkQrcodeDialog"
      :visible.sync="wxWorkQrcodeDialog"
      @confirm="handleUploadConfirm"
      @close="handleUploadCancel"
    />
    <SelectMaterialDialog
      v-if="showMaterialChooseDialog"
      :menus="[
        ...($route.query.marketingEventId ? ['marketingEvent'] : []),
        'materials'
      ]"
      :tabbar="materialTabbar"
      :marketing-event-tabbar="materialTabbar"
      :marketing-event-id="$route.query.marketingEventId"
      :visible="showMaterialChooseDialog"
      :is-multi-select="materialMultiSelect"
      :is-activity="uploadResolveType === 'eventslist'"
      @onSubmit="handleMaterialConfirm"
      @onClose="handleUploadCancel"
    />
    <PictureSelector
      :visible.sync="showPictureSelector"
      :cut-size="pictureSize"
      :max-select-count="pictureMaxSelectCount"
      :need-apath="true"
      output-path-type="a"
      @submit="handlePictureSelectorSubmit"
      @close="handlePictureSelectorClose"
    />
    <MeetingTinymceEditorDialog
      :value="currentEditConferenceDetails"
      :visible.sync="meetingContentVisible"
      @confirm="handleMeetingContentEditorConfirm"
    />
    <CustomPosterDialog
      :visible.sync="customPosterDialogVisible"
      :default-img="sharePosterUrl"
      @update:visible="customPosterDialogVisible = false"
      @selected="handleCustomPosterSelected"
    />

    <!--    <VDialog :visible.sync="pictureVisible" title="图片分组展示设置" @onClose="pictureVisible = false"-->
    <!--             @onSubmit="mobilePhotoGroupSetting" width="630px">-->
    <!--      <div class="picture-body">-->
    <!--        <Transfer v-model="pictureValue" :data="pictureData" :titles="['图片库分组', '移动端展示分组']"></Transfer>-->
    <!--      </div>-->
    <!--    </VDialog>-->
    <SetProduct
      :visible.sync="setProductVisible"
      :object-type="objectType"
      :title="groupTitle[objectType]"
      :classification="classification"
      @close="setProductVisible = false"
      @mobilePhotoGroupSetting="mobilePhotoGroupSetting"
    />
  </div>
</template>

<script>
import Designer from './Designer.vue'
import VideoDialog from '@/pages/video/components/video-dialog.vue'
import LiveDialog from '@/pages/live-marketing/components/live-dialog.vue'
import QrcodeDialog from '@/pages/wechat/components/qrcode-dialog.vue'
import WxWorkQrcodeDialog from '@/pages/qywx-manage/promote-qrcode/qrcode-dialog.vue'
import SelectMaterialDialog from '@/components/select-material-dialog/index.vue'
import PictureSelector from '@/components/PictureSelector/index.vue'
import MeetingTinymceEditorDialog from '@/pages/meeting-marketing/MeetingCreate/MeetingTinymceEditorDialog.vue'
import CustomPosterDialog from '@/components/qrposter-create-dialog/custom-poster-dialog/index.vue'
import mixins from './mixins.js'

import SetProduct from './SetProduct.vue'

export default {
  components: {
    Designer,
    VideoDialog,
    LiveDialog,
    QrcodeDialog,
    WxWorkQrcodeDialog,
    SelectMaterialDialog,
    PictureSelector,
    MeetingTinymceEditorDialog,
    CustomPosterDialog,
    SetProduct,
    // VDialog,
    // Transfer
  },
  mixins: [mixins],
  props: {
    isAgent: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      videoDialog: false,
      liveDialog: false,
      qrcodeDialog: false,
      wxWorkQrcodeDialog: false,
      materialTabbar: [10, 1, 4, 3],
      showMaterialChooseDialog: false,
      materialMultiSelect: false,
      uploadResolveType: '',
      showPictureSelector: false,
      currentEditConferenceDetails: '',
      meetingContentVisible: false,
      pictureSize: {},
      pictureMaxSelectCount: 1,
      memberConfig: {}, // 会员配置信息
      triggerSaveButton: false, // 区分是保存还是取消
      sharePosterUrl: '',
      sharePosterAPath: '',
      customPosterDialogVisible: false,
      setProductVisible: false,
      objectType: -1,
      // pictureVisible: false,
      // pictureData: [],
      // pictureValue: [],
      classification: [],
      shareCoverCutSizes: [{
        title: $t('marketing.pages.meeting_marketing.hbfm_d8183b') + '（9:5）',
        desc: $t('marketing.components.PictureSelector.xs_6fa81e'),
        width: 900,
        height: 500,
        photoTargetType: 45,
      }, {
        title: $t('marketing.commons.xcxfm_94b4f3') + '（5:4）',
        desc: $t('marketing.components.PictureSelector.yyzfwxxcxy_d66d73'),
        width: 420,
        height: 336,
        photoTargetType: 43,
      }, {
        title: $t('marketing.commons.pyqfm_ea274c') + '（1:1）',
        desc: $t('marketing.components.PictureSelector.yyzfwxhpyq_5c11c7'),
        width: 300,
        height: 300,
        photoTargetType: 44,
      }],
      groupTitle: {
        4: $t('marketing.pages.site.cpfzzssz_f3f188'),
        6: $t('marketing.pages.site.wzfzzssz_65c939'),
      },
      marketingEventDetailData: {}
    }
  },
}
</script>

<style lang="less" module>
.marketing__page_designer {
  height: 100vh;
  overflow: hidden;
  &--agent {
    height: 100%;
  }
}
</style>
