import _ from 'lodash'
import { mapGetters, mapState } from 'vuex'
import { cropImageFileSize, requireAsync } from '@/utils/index.js'
import { confirm } from '@/utils/globals.js'
import http from '@/services/http/index.js'
import utils from '@/services/util/index.js'
import wxList from '@/modules/wx-list.js'
import { createMaterialDetailUrlByObjectType } from '@/utils/createMaterialDetailUrl.js'
import { couponModel } from '@/pages/Coupons/components/CouponsSelectorIndex.js'
import { getMarketingEventSchedule } from './utils.js'
const objectTypeByRange = {
  'custom-article-more': 6,
  'custom-product-more': 4,
  'custom-site-more': 26,
}

const signupButtonItemStyle = {
  color: '#fff',
  fontSize: 16,
  lineHeight: 45,
  textAlign: 'center',
  letterSpacing: 0,
}

export default {
  computed: {
    isBindWx() {
      return !!wxList.datas.list.length
    },
    isBindWxWork() {
      return this.$store.state.Global.isQywxOpen
    },
    ...mapGetters('MarketingEventSet', ['marketSetFilters']),
    ...mapState('MarketingEventSet', ['marketingEventAudit', 'activityTypeMapping']),
  },
  created() {
    this.queryMemberConfig()
    wxList.queryList()
  },
  methods: {
    /**
     * 获取会员配置信息
     */
    queryMemberConfig() {
      http.getMemberConfig().then(({ errCode, data }) => {
        if (errCode === 0) {
          this.memberConfig = data || {}
        }
      })
    },
    handlePageClose() {
      this.triggerSaveButton = true
      const { redirect, from } = this.$route.query
      confirm($t('marketing.commons.sfqrqx_c2523b'), $t('marketing.commons.ts_02d981'), {
        callback: action => {
          if (action === 'confirm') {
            if (from === 'dialog') {
              window.close()
            } else if (redirect) {
              const route = JSON.parse(redirect)
              const params = {
                ...route,
                query: {
                  ...route.query,
                },
                params: {
                  ...route.params,
                },
              }
              this.$router.replace(params)
            } else {
              this.$router.back()
            }
          }
        },
      })
    },
    handleSaveAndExt() {
      this.triggerSaveButton = true
    },
    handleUploadConfirmProduct(data) {
      console.log(data, 'data')
      this.classification = data
      this.uploadResolve({
        classification: data,
        currentItem: '',
      })
    },
    // 选择资源
    handleUploadConfirm(data, type) {
      this.showMaterialChooseDialog = false
      if (typeof this.uploadResolve === 'function') {
        const employee = FS.contacts.getCurrentEmployee()

        if (type === 'content') {
          const {
            range, siteGroupId, items = [], contentObjectIds = [],
          } = this.uploadResolveOriginData || {}

          const _contentObjectIds = contentObjectIds.concat(
            data.map(item => item.id),
          )
          const _items = items.concat(
            data.map(item => ({
              ...item,
              image: item.image,
              title: item.title,
              time: utils.formatDateTime(item.startTime, 'YYYY-MM-DD hh:mm'),
            })),
          )
          this.uploadResolve({
            siteGroupId: range !== 'site' ? null : siteGroupId,
            contentObjectIds: _.uniq(_contentObjectIds),
            items: _.uniqBy(_items, 'id'),
          })
          // eslint-disable-next-line brace-style
        }
        // if (type === "content") {
        //   this.uploadResolve({
        //     article: {
        //       title: data.title || data.name,
        //       desc: data.summary,
        //       time: data.time,
        //       id: data.id,
        //       image: data.image,
        //       mpUrl: data.mpUrl,
        //       url: data.url,
        //       contentType: data.contentType,
        //       objectType: data.objectType
        //     },
        //     action: {
        //       type: "content",
        //       content: {
        //         id: data.id,
        //         contentType: data.contentType,
        //         objectType: data.objectType,
        //         spreadFsUid: (employee && employee.id) || undefined
        //       },
        //       extendParams: {
        //         marketingEventId: data.marketingEventId || data.commonMarketingEventId || ''
        //       },
        //       //跳转内容时，链接上所携带参数
        //       linkParams: {
        //         marketingEventId: data.commonMarketingEventId,
        //       }
        //     }
        //   });
        // }
        else if (type === 'eventslist') {
          const { items = [], marketingEventIds = [] } = this.uploadResolveOriginData || {}

          const _marketingEventIds = marketingEventIds.concat(
            data.map(item => item.marketingEventId),
          )
          const _items = items.concat(
            data.map(item => ({
              ...item,
              image: item.image,
              title: item.title,
              type: item.objectType === 13 ? $t('marketing.commons.hy_ebcb81') : $t('marketing.commons.zb_7bbe8e'),
              time: utils.formatDateTime(item.startTime, 'YYYY-MM-DD hh:mm'),
            })),
          )
          this.uploadResolve({
            marketingEventIds: _.uniq(_marketingEventIds),
            items: _.uniqBy(_items, 'marketingEventId'),
          })
        } else if (type === 'article') {
          this.getArticleDetailContent(data.id).then(content => {
            this.uploadResolve({
              article: {
                title: data.title || data.name,
                desc: data.summary,
                time: data.time,
                id: data.id,
                image: data.image,
                mpUrl: data.mpUrl,
                url: data.url,
                contentType: data.contentType,
                objectType: data.objectType,
                content: encodeURIComponent(content),
              },
              action: {
                type: 'article',
                id: data.id,
                // 跳转内容时，链接上所携带参数
                linkParams: {
                  marketingEventId: data.commonMarketingEventId,
                },
              },
            })
          })
        } else if (type === 'live') {
          const { loginSiteId, registrationSiteId } = this.memberConfig || {}
          this.uploadResolve({
            objectId: data.id,
            objectTitle: data.title,
            status: 'before', // 直播状态
            livePlatform: data.platform,
            marketingEventId:
              data.marketingEventId || data.commonMarketingEventId || '',
            member: {
              loginSiteId,
              registrationSiteId,
              action: {
                type: 'memberPage',
                name: $t('marketing.commons.hydly_1e0dc7'),
                id: loginSiteId,
              },
            },
            schedule: {
              before: {
                yes: {
                  name: $t('marketing.commons.yyy_44209e'),
                  style: signupButtonItemStyle,
                  action: {},
                },
                no: {
                  name: $t('marketing.commons.ljyy_3ed720'),
                  style: signupButtonItemStyle,
                  action: {
                    type: 'content',
                    content: {
                      id: data.submitHexagonId,
                      title: data.submitHexagonName,
                      contentType: 10,
                      objectType: 26,
                      spreadFsUid: (employee && employee.id) || '',
                    },
                    extendParams: {
                      marketingEventId:
                        data.marketingEventId
                        || data.commonMarketingEventId
                        || '',
                    },
                  },
                },
              },
              processing: {
                yes: {
                  name: $t('marketing.commons.gkzb_4dd8d5'),
                  style: signupButtonItemStyle,
                  action: {
                    type: 'outside',
                    url: data.viewUrl,
                  },
                },
                no: {
                  name: $t('marketing.commons.bmbgk_759bbd'),
                  style: signupButtonItemStyle,
                  action: {
                    type: 'content',
                    content: {
                      id: data.submitHexagonId,
                      title: data.submitHexagonName,
                      contentType: 10,
                      objectType: 26,
                      spreadFsUid: (employee && employee.id) || '',
                    },
                    extendParams: {
                      marketingEventId:
                        data.marketingEventId
                        || data.commonMarketingEventId
                        || '',
                    },
                  },
                },
              },
              after: {
                yes: {
                  name: $t('marketing.commons.gkhf_58dee7'),
                  style: signupButtonItemStyle,
                  action: {
                    type: 'outside',
                    url: data.viewUrl,
                  },
                },
                no: {
                  name: $t('marketing.commons.gkhf_58dee7'),
                  style: signupButtonItemStyle,
                  action: {
                    type: 'content',
                    content: {
                      id: data.submitHexagonId,
                      title: data.submitHexagonName,
                      contentType: 10,
                      objectType: 26,
                      spreadFsUid: (employee && employee.id) || '',
                    },
                    extendParams: {
                      marketingEventId:
                        data.marketingEventId
                        || data.commonMarketingEventId
                        || '',
                    },
                  },
                },
              },
            },
          })
        } else if (type === 'meeting') {
          const { loginSiteId, registrationSiteId } = this.memberConfig || {}

          // const meetingEnrollPage = `${
          //   location.origin
          // }/ec/h5-landing/release/index.html?id=${
          //   data.activityDetailSiteId
          // }&marketingEventId=${data.marketingEventId ||
          //   data.commonMarketingEventId ||
          //   ""}&type=1&targetObjectType=13&targetObjectId=${data.id}`;
          const meetingEnrollPage = createMaterialDetailUrlByObjectType(26, {
            id: data.activityDetailSiteId,
            marketingEventId: data.marketingEventId,
            targetObjectType: 13,
            targetObjectId: data.id,
          }).url

          this.uploadResolve({
            objectId: data.id,
            objectTitle: data.title,
            status: 'before', // 直播状态
            marketingEventId:
              data.marketingEventId || data.commonMarketingEventId || '',
            member: {
              loginSiteId,
              registrationSiteId,
              action: {
                type: 'memberPage',
                name: $t('marketing.commons.hydly_1e0dc7'),
                id: loginSiteId,
              },
            },
            schedule: {
              before: {
                yes: {
                  name: $t('marketing.commons.ybm_4166d8'),
                  style: signupButtonItemStyle,
                  action: {},
                },
                no: {
                  name: $t('marketing.commons.ljbm_1e6c87'),
                  style: signupButtonItemStyle,
                  action: {
                    type: 'content',
                    content: {
                      id: data.activityDetailSiteId,
                      title: data.title,
                      contentType: 10,
                      objectType: 26,
                      targetObjectType: 13,
                      targetObjectId: data.conferenceId || data.id || "",
                      spreadFsUid: (employee && employee.id) || '',
                    },
                    extendParams: {
                      marketingEventId:
                        data.marketingEventId
                        || data.commonMarketingEventId
                        || '',
                    },
                  },
                },
              },
              processing: {
                yes: {
                  name: $t('marketing.commons.ybm_4166d8'),
                  style: signupButtonItemStyle,
                  action: {},
                },
                no: {
                  name: $t('marketing.commons.ljbm_1e6c87'),
                  style: signupButtonItemStyle,
                  action: {
                    type: 'content',
                    content: {
                      id: data.activityDetailSiteId,
                      title: data.title,
                      contentType: 10,
                      objectType: 26,
                      targetObjectType: 13,
                      targetObjectId: data.conferenceId || data.id || "",
                      spreadFsUid: (employee && employee.id) || '',
                    },
                    extendParams: {
                      marketingEventId:
                        data.marketingEventId
                        || data.commonMarketingEventId
                        || '',
                    },
                  },
                },
              },
              after: {
                yes: {
                  name: $t('marketing.commons.jchg_f4724e'),
                  style: signupButtonItemStyle,
                  action: {},
                },
                no: {
                  name: $t('marketing.commons.jchg_f4724e'),
                  style: signupButtonItemStyle,
                  action: {
                    type: 'content',
                    content: {
                      id: data.activityDetailSiteId,
                      title: data.title,
                      contentType: 10,
                      objectType: 26,
                      targetObjectType: 13,
                      targetObjectId: data.conferenceId || data.id || "",
                      spreadFsUid: (employee && employee.id) || '',
                    },
                    extendParams: {
                      marketingEventId:
                        data.marketingEventId
                        || data.commonMarketingEventId
                        || '',
                    },
                  },
                },
              },
            },
          })
        } else {
          this.uploadResolve(data)
        }
      }
    },
    handleUploadCancel() {
      this.showMaterialChooseDialog = false
      this.materialMultiSelect = false
      if (
        typeof this.uploadResolve === 'function'
        && this.uploadResolveType !== 'article'
      ) {
        this.uploadResolve(false)
        this.uploadResolve = null
      }
    },
    handleMaterialConfirm(data) {
      console.log(data, 'data', this.uploadResolve)

      // 选择资源回调
      if (this.uploadResolve) {
        if (this.uploadResolveType === 'article') {
          this.handleUploadConfirm(data, 'article')
        } else if (this.uploadResolveType === 'meeting') {
          this.handleUploadConfirm(data, 'meeting')
        } else if (this.uploadResolveType === 'eventslist') {
          this.handleUploadConfirm(data, 'eventslist')
        } else {
          this.handleUploadConfirm(data, 'content')
        }
      } else {
        // 选择内容后动作回调
        this.chooseMaterialCallback({
          content: {
            id: data.id,
            title: data.title || data.name,
            contentType: data.contentType,
            objectType: data.objectType,
          },
        })
      }
    },
    // 获取文章详情
    getArticleDetailContent(articleId) {
      return http
        .queryArticleDetail({ articleId })
        .then(({ errCode, data }) => {
          if (errCode === 0) {
            return data.content
          }

          return undefined
        })
    },
    renderObjectPicker(type) {
      requireAsync(
        'crm-modules/components/pickselfobject/pickselfobject',
        PickObject => {
          const picker = new PickObject()
          picker.on('select', data => {
            this.handleUploadResolveMarketingEvent(data,type)
            picker.destroy()
          })
          const defaultFilters = []
          if (this.marketingEventAudit) {
            defaultFilters.push({ field_name: 'life_status', field_values: ['normal'], operator: 'EQ' })
          }

          picker.render({
            layout_type: 'list',
            include_layout: false,
            apiname: 'MarketingEventObj',
            zIndex: 3000,
            filters: [
              ...defaultFilters,
              ...this.marketSetFilters,
            ],
          })
        },
      )
    },
    renderObjectPicker2(params, cb) {
      requireAsync('crm-modules/components/pickselfobject/pickselfobject', PickSelf => {
        const pickobject = new PickSelf()
        pickobject.on('select', obj => {
          cb && cb(obj)
          pickobject.destroy()
        })
        pickobject.render(_.extend({
          zIndex: 3000,
          isMultiple: false,
        }, params))
      })
    },
    getImageFileWidth(file) {
      return new Promise(resolve => {
        const fr = new FileReader()
        fr.onload = function () {
          const img = new Image()
          img.onload = function () {
            resolve(img.width)
          }
          img.src = fr.result
        }
        fr.readAsDataURL(file)
      })
    },
    /**
     * 上传静态资源方法
     * @param { String } type  图片image 视频video
     * @return { Object } {url: ''}
     */
    upload({
      target, type, value, name, pictureSize, max, data,
    }, file) {
      console.log(target, 'target',type, 'type')
      if (type === 'video') {
        this.videoDialog = true
        return new Promise(resolve => {
          this.uploadResolve = resolve
        })
      } if (type === 'qrcode') {
        // 微信公众号吸粉二维码
        if (name === 'wx') {
          if (!this.isBindWx) {
            FxUI.MessageBox.confirm($t('marketing.commons.nhmybdwxgz_86a005'), $t('marketing.commons.ts_02d981'), {
              confirmButtonText: $t('marketing.commons.qbd_d4594e'),
              cancelButtonText: $t('marketing.commons.qx_625fb2'),
              type: 'warning',
            }).then(() => {
              FxUI.MessageBox.confirm($t('marketing.commons.sfywcwxgzh_32fef6'), $t('marketing.commons.ts_02d981'), {
                confirmButtonText: $t('marketing.commons.ywc_fad522'),
                cancelButtonText: $t('marketing.commons.gb_b15d91'),
                type: 'warning',
              }).then(() => {
                wxList.queryList()
              })
              const route = this.$router.resolve({
                name: 'wechat-home',
              })
              window.open(route.href, '_blank')
            })
          } else {
            this.qrcodeDialog = true
          }
        } else if (!this.isBindWxWork) {
          FxUI.MessageBox.confirm($t('marketing.commons.nhmybdqywx_ed154e'), $t('marketing.commons.ts_02d981'), {
            confirmButtonText: $t('marketing.commons.qbd_d4594e'),
            cancelButtonText: $t('marketing.commons.qx_625fb2'),
            type: 'warning',
          }).then(() => {
            this.handleCreateWxwork()
          })
        } else {
          this.wxWorkQrcodeDialog = true
        }
        return new Promise(resolve => {
          this.uploadResolve = resolve
        })
      } if (type === 'content') {
        if (Object.keys(objectTypeByRange).includes(data.range)) {
          this.objectType = objectTypeByRange[data.range]
          this.setProductVisible = true
          this.classification = data.classification
          return new Promise(resolve => {
            this.uploadResolve = resolve
          })
        }

        let contentType
        if (data.range.indexOf('custom-site') !== -1) {
          contentType = 10
        } else if (data.range.indexOf('custom-article') !== -1) {
          contentType = 1
        } else if (data.range.indexOf('custom-product') !== -1) {
          contentType = 4
        }
        this.materialTabbar = [contentType]
        this.showMaterialChooseDialog = true
        this.materialMultiSelect = true
        this.uploadResolveType = 'content'
        this.uploadResolveOriginData = data
        return new Promise(resolve => {
          this.uploadResolve = resolve
        })
      } if (type === 'article') {
        this.materialTabbar = [1]
        this.showMaterialChooseDialog = true
        this.uploadResolveType = 'article'
        return new Promise(resolve => {
          this.uploadResolve = resolve
        })
      } if (type === 'conference_content') {
        // 会议详情编辑
        this.meetingContentVisible = true
        let currentEditConferenceDetails = ''
        try {
          currentEditConferenceDetails = decodeURIComponent(
            data.article.content,
          )
        } catch (error) {
          currentEditConferenceDetails = data.article.content || ''
        }
        this.currentEditConferenceDetails = currentEditConferenceDetails

        this.uploadResolveType = 'conference_content'
        return new Promise(resolve => {
          this.uploadResolve = resolve
        })
      } if (type === 'live') {
        // 选择直播
        this.liveDialog = true
        return new Promise(resolve => {
          this.uploadResolve = resolve
        })
      } if (type === 'meeting') {
        // 选择会议
        this.materialTabbar = [3]
        this.uploadResolveType = 'meeting'
        this.showMaterialChooseDialog = true
        return new Promise(resolve => {
          this.uploadResolve = resolve
        })
      } if (type === 'activity') {
        this.renderObjectPicker()
        return new Promise(resolve => {
          this.uploadResolve = resolve
        })
      } if (type === 'eventslist') {
        this.materialTabbar = [3, 30, 888]
        this.showMaterialChooseDialog = true
        this.materialMultiSelect = true
        this.uploadResolveType = 'eventslist'
        this.uploadResolveOriginData = data
        return new Promise(resolve => {
          this.uploadResolve = resolve
        })
      } if (type === 'poster') {
        console.log('customPosterDialogVisible: ', value)
        this.sharePosterUrl = value
        this.customPosterDialogVisible = true
        return new Promise(resolve => {
          this.uploadResolve = resolve
        })
      } if (type === 'image') { // 针对图片资源限制
        if (file) {
          const isLt2M = file.size / 1024 / 1024 < 2
          if (!isLt2M) {
            FxUI.Message.error($t('marketing.commons.sctpdxbncg_783da2'))
            return Promise.resolve(false)
          }
          return new Promise((resolve, reject) => {
            if (name === 'shareImage') {
              cropImageFileSize(file, 632, 498).then(file => {
                this.uploadFile(file, data => resolve(data))
              })
            } else {
              this.getImageFileWidth(file).then(witdh => {
                if (witdh > 1080) {
                  FxUI.Message.error($t('marketing.commons.sctpkdbncg_dc4866'))
                  resolve(false)
                  return
                }
                this.uploadFile(file, data => resolve(data))
              })
            }
          })
        }
        this.showPictureSelector = true

        // 如果是分享图片尺寸为300*300
        if (name === 'shareImage') {
          this.pictureSize = [...this.shareCoverCutSizes]
        } else if (name === 'carousel') {
          // 微页面轮播图不需要裁剪
          this.pictureSize = {}
        } else {
          this.pictureSize = pictureSize && pictureSize.width ? pictureSize : {}
        }
        this.pictureMaxSelectCount = max || 1
        return new Promise(resolve => {
          this.uploadResolve = resolve
        })
      } if (type === 'dht_choose_product') {
        const pickerParams = _.extend({
          isMultiple: true,
          apiname: 'ProductObj',
          relatedname: 'product_new_opportunity_lines_list',
          filters: [{
            field_name: 'product_status',
            field_values: ['1'],
            operator: 'EQ',
          }].concat(data.filters || []),
          object_data: {},
          beforeSelectFunc(list) {
            const { limit } = data
            if (list.length > limit) {
              CRM.util.alert($t('当前选择数据不能超过{{limit}}条', {
                limit,
              }))
              return true
            }
            return false
          },
        }, data.extraParms)

        this.renderObjectPicker2(pickerParams, list => {
          console.log('select product list:', list)
          this.uploadResolve(list)
        })

        return new Promise(resolve => {
          this.uploadResolve = resolve
        })
      } if (type === 'dht_choose_pricepolicy') {
        const pickerParams = _.extend({
          apiname: 'PricePolicyObj',
          isMultiple: false,
          hideAdd: true,
          tableOptions: {
            // 处理接口返回的列表数据
            formatData: res => {
              const list = res.pricePolicyList || []
              _.each(list, item => {
                _.extend(item, {
                  _id: item.id,
                  start_date: item.startDate || null,
                  end_date: item.endDate || null,
                  modify_type: item.modifyType,
                })
              })
              return {
                totalCount: list.length,
                data: list,
              }
            },
          },
          // 格式化表格配置
          formatTableOption: options => {
            _.extend(options, {
              url: '/EM1HNCRM/API/v1/object/price_policy/service/findPricePolicyByAccountId',
              searchTerm: null, // 选择场景
              showTagBtn: false, // 按标签筛选
              showMoreBtn: false, // 列设置
              showFilerBtn: false, // 筛选
              search: false, // 右边的搜索框
              showPage: false, // 分页
            })
            return options
          },
          // 列表接口请求数据的处理
          beforeRequest: rq => ({
            masterObjectApiName: 'SalesOrderObj',
            accountId: data.account_id,
          }),
          // 处理表格columns
          formatColumns: (columns, apiname) => {
            // 只展示这些字段
            const showFields = ['name', 'start_date', 'end_date', 'modify_type', 'priority', 'remark']
            columns = _.filter(columns, a => showFields.indexOf(a.api_name) !== -1 || a.id === 'operate_column')
            return columns
          },
        })

        this.renderObjectPicker2(pickerParams, obj => {
          console.log('select pricepolicy obj:', obj)
          this.uploadResolve([obj])
        })

        return new Promise(resolve => {
          this.uploadResolve = resolve
        })
      } if (type === 'dht_choose_coupon') {
        const marketingEventId = this.getMarketingEventId()

        if (marketingEventId) {
          // 从活动微页面入口进入，只能选择当前市场活动的优惠券

          couponModel.create({
            props: {
              visible: true,
              marketingEventId,
              backButton: false,
              multipleChoice: true,
            },
            confirm: selectCoupon => {
              this.uploadResolve([...selectCoupon])
            },
          })
        } else {
          // 从通用微页面入口进入，需要先选市场活动再选优惠券

          const pickerParams = _.extend({
            apiname: 'MarketingEventObj',
            filters: [{
              field_name: 'event_type',
              field_values: ['content_marketing'],
              operator: 'EQ',
            }, { // 结束日期晚于当前时间
              field_name: 'end_time',
              field_values: [(new Date()).getTime()],
              operator: 'GT',
            }],
            isMultiple: false,
          }, data.extraParms)

          const chooseMarketingEventObj = () => {
            this.renderObjectPicker2(pickerParams, obj => {
              console.log('select marketing obj:', obj)
              couponModel.create({
                props: {
                  visible: true,
                  marketingEventId: obj._id,
                  backButton: true,
                  multipleChoice: true,
                },
                confirm: selectCoupon => {
                  console.log('select coupon obj:', selectCoupon)
                  this.uploadResolve([...selectCoupon])
                },
                backHandler: () => {
                  console.log('backHandlr')
                  chooseMarketingEventObj()
                },
              })
            })
          }

          chooseMarketingEventObj()
        }

        return new Promise(resolve => {
          this.uploadResolve = resolve
        })
      } if (type === 'activity-comp') {
        this.renderObjectPicker(type)
        return new Promise(resolve => {
          this.uploadResolve = resolve
        })
      }
      return Promise.resolve(false)
    },

    handleMeetingContentEditorConfirm(content) {
      this.uploadResolve({
        article: {
          content: encodeURIComponent(content),
        },
      })
    },
    // 文件选择器返回
    async handlePictureSelectorSubmit(file) {
      console.log('handlePictureSelectorSubmit', file)

      //非cdn图片转cdn
      const apathToCpath = async (path) => {
        let url = "";
        if(path){
          const { errCode, data } = await http.getCdnUrlBypath({
            path,
          });
          if(errCode === 0){
            url = data;
          }
        }
        return url;
      }
      const getUrlByApath = (file={}) => {
        return  file.qrPosterUrl || file.photoForwardUrl || file.url
      }
      const getCdnUrl = async (file) => {
        let cdnUrl = "";
        const targetPath = file.qrPosterApath || file.photoPath || file.path || "";
        const cutCdnUrl = file.cutOffsetList && file.cutOffsetList[0].image || "";
        const isTempCutFile = !!file.cutSize;//cutSize有值为临时裁剪图片
        //有裁剪图默认先取裁剪图
        if(cutCdnUrl){
          cdnUrl = cutCdnUrl
        } else if(file.cdnUrl){
          // 有裁剪的优先选取裁剪的 裁剪的地址本身就是cdn地址
          cdnUrl = file.cdnUrl
        } else if(!file.cdnUrl && targetPath.indexOf('C_') === -1 && !isTempCutFile) {
          cdnUrl = await apathToCpath(targetPath);
        }
        return {
          ...file,
          url: cdnUrl || getUrlByApath(file),
        }
      }
      // 多选
      if (this.pictureMaxSelectCount > 1) {
        const files = file.map(getCdnUrl);
        Promise.all(files).then((res) => {
          this.uploadResolve(res)
        });
      } else {
        this.uploadResolve(getCdnUrl(file));
      }
    },
    handlePictureSelectorClose() {
      this.uploadResolve(false)
    },
    handleCustomPosterSelected(data) {
      const { path, url } = data || {}
      this.uploadResolve({
        sharePosterUrl: url || '',
        sharePosterAPath: path || '',
      })
    },
    uploadFile(f, callback) {
      const formData = new FormData()
      formData.append('file', f)
      formData.append('type', 1)
      formData.append('needApath', true)
      formData.append('needPermanent', true)
      try {
        http
          .uploadFile(formData)
          .then(({ errCode, data = {} }) => {
            if (errCode === 0) {
              callback(data)
            } else {
              FxUI.Message.error($t('marketing.commons.tpscsbqzs_468a42'))
              callback(false)
            }
          })
          .catch(err => {
            FxUI.Message.error($t('marketing.commons.tpscsbqzs_468a42'))
            callback(false)
          })
      } catch (e) {
        FxUI.Message.error($t('marketing.commons.tpscsbqzs_468a42'))
        callback(false)
      }
    },
    mobilePhotoGroupSetting(value) {
      console.log(value, 'value')
      this.handleUploadConfirmProduct(value)
    },
    getClassification(value) {
      this.classification = value
    },

    /**
     * 获取当前微页面的市场活动id
     */
    getMarketingEventId() {
      return this.$router.history.current.query.marketingEventId
    },
    async handleUploadResolveMarketingEvent(data = {},type){
      const marketingEventSetting = getMarketingEventSchedule(data, this.memberConfig)
      this.uploadResolve(marketingEventSetting)
    }
  },
  beforeRouteLeave(to, from, next) {
    // 点击保存按钮直接返回
    if (this.triggerSaveButton) {
      next()
      return
    }
    if (this.throttle) clearTimeout(this.throttle)
    this.throttle = setTimeout(() => {
      confirm($t('marketing.commons.sfqrqx_c2523b'), $t('marketing.commons.ts_02d981'), {
        callback: action => {
          if (action === 'confirm') {
            next()
          } else {
            next(false)
          }
        },
      })
    }, 30)
  },
}
