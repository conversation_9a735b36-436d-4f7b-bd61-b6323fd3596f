import { createMaterialDetailUrlByObjectType } from '@/utils/createMaterialDetailUrl.js'

const signupButtonItemStyle = {
  color: '#fff',
  fontSize: 16,
  lineHeight: 45,
  textAlign: 'center',
  letterSpacing: 0,
}
export const getMarketingEventSchedule = (data, memberConfig) => {
  const employee = FS.contacts.getCurrentEmployee()
  const { loginSiteId, registrationSiteId } = memberConfig || {}
  // objectType: 使用活动形式
  if (data.event_form === 'live_marketing') {
    return {
      objectId: data._id,
      objectType: data.event_form,
      objectTitle: data.name,
      objectName: $t('marketing_pd.commons.zb_7bbe8e'),
      status: 'before',
      marketingEventId: data._id || '',
      marketingEventTitle: data.name,
      member: {
        loginSiteId,
        registrationSiteId,
        action: {
          type: 'memberPage',
          name: $t('marketing.commons.hydly_1e0dc7'),
          id: loginSiteId,
        },
      },
      schedule: {
        before: {
          yes: {
            name: $t('marketing.commons.yyy_44209e'),
            style: signupButtonItemStyle,
            action: {},
          },
          no: {
            name: $t('marketing.commons.ljyy_3ed720'),
            style: signupButtonItemStyle,
            action: {
              type: 'inside',
              content: {
                id: '',
                title: '',
                contentType: 10,
                objectType: 26,
                spreadFsUid: (employee && employee.id) || '',
              },
              extendParams: {
                marketingEventId: data._id || '',
              },
            },
          },
        },
        processing: {
          yes: {
            name: $t('marketing.commons.gkzb_4dd8d5'),
            style: signupButtonItemStyle,
            action: {
              type: 'gotoLiveAddress',
              extendParams: {
                marketingEventId: data._id || '',
              },
            },
          },
          no: {
            name: $t('marketing.commons.bmbgk_759bbd'),
            style: signupButtonItemStyle,
            action: {
              type: 'inside',
              content: {
                id: '',
                title: '',
                contentType: 10,
                objectType: 26,
                spreadFsUid: (employee && employee.id) || '',
              },
              extendParams: {
                marketingEventId: data._id || '',
              },
            },
          },
        },
        after: {
          yes: {
            name: $t('marketing.commons.gkhf_58dee7'),
            style: signupButtonItemStyle,
            action: {
              type: 'gotoLiveAddress',
            },
            extendParams: {
              marketingEventId: data._id || '',
            },
          },
          no: {
            name: $t('marketing.commons.gkhf_58dee7'),
            style: signupButtonItemStyle,
            action: {
              type: 'inside',
              content: {
                id: '',
                title: '',
                contentType: 10,
                objectType: 26,
                spreadFsUid: (employee && employee.id) || '',
              },
              extendParams: {
                marketingEventId: data._id || '',
              },
            },
          },
        },
      },
    }
  } else if (data.event_form === 'conference_marketing') {
    const meetingEnrollPage = createMaterialDetailUrlByObjectType(26, {
      id: data.activityDetailSiteId,
      marketingEventId: data._id,
      targetObjectType: 13,
      targetObjectId: data._id,
    }).url;
    return {
      objectId: data._id,
      objectType: data.event_form,
      objectTitle: data.name,
      objectName: $t('marketing.commons.hy_ebcb81'),
      status: 'before',
      marketingEventId: data._id || '',
      marketingEventTitle: data.name,
      member: {
        loginSiteId,
        registrationSiteId,
        action: {
          type: 'memberPage',
          name: $t('marketing.commons.hydly_1e0dc7'),
          id: loginSiteId,
        },
      },
      schedule: {
        before: {
          yes: {
            name: $t('marketing.commons.ybm_4166d8'),
            style: signupButtonItemStyle,
            action: {},
          },
          no: {
            name: $t('marketing.commons.ljbm_1e6c87'),
            style: signupButtonItemStyle,
            action: {
              type: 'inside',
              content: {
                id: '',
                title: '',
                contentType: 10,
                objectType: 26,
                targetObjectType: 13,
                targetObjectId: data.conferenceId || data._id || "",
                spreadFsUid: (employee && employee.id) || '',
              },
              extendParams: {
                marketingEventId: data._id || '',
              },
            },
          },
        },
        processing: {
          yes: {
            name: $t('marketing.commons.ybm_4166d8'),
            style: signupButtonItemStyle,
            action: {},
          },
          no: {
            name: $t('marketing.commons.ljbm_1e6c87'),
            style: signupButtonItemStyle,
            action: {
              type: 'inside',
              content: {
                id: '',
                title: '',
                contentType: 10,
                objectType: 26,
                targetObjectType: 13,
                targetObjectId: data.conferenceId || data._id || "",
                spreadFsUid: (employee && employee.id) || '',
              },
              extendParams: {
                marketingEventId: data._id || '',
              },
            },
          },
        },
        after: {
          yes: {
            name: $t('marketing.pages.site.hdyjs_cdae1c'),
            style: signupButtonItemStyle,
            action: {},
          },
          no: {
            name: $t('marketing.commons.jchg_f4724e'),
            style: signupButtonItemStyle,
            action: {
            },
          },
        },
      },
    }
  } else {
    return {
      objectId: data._id,
      objectType: data.event_form,
      objectTitle: data.name,
      status: 'before',
      marketingEventId: data._id,
      objectName: $t('marketing.commons.hd_36c6f5'),
      marketingEventTitle: data.name,
      member: {
        loginSiteId,
        registrationSiteId,
        action: {
          type: 'memberPage',
          name: $t('marketing.commons.hydly_1e0dc7'),
          id: loginSiteId,
        },
      },
      schedule: {
        before: {
          yes: {
            name: $t('marketing.commons.ybm_4166d8'),
            style: signupButtonItemStyle,
            action: {},
          },
          no: {
            name: $t('marketing.commons.ljbm_1e6c87'),
            style: signupButtonItemStyle,
            action: {
              type: 'inside',
              content: {
                id: '',
                title: '',
                contentType: 10,
                objectType: 26,
                spreadFsUid: (employee && employee.id) || '',
              },
              extendParams: {
                marketingEventId: data._id || '',
              },
            },
          },
        },
      },
    }
  }
}