import http from '@/services/http/index.js'
import config from './config.js'

export default {
  //递归遍历所有组件，将组件属性中含有ctaConfig并且处于enable处于开启状态的的所有ctaId汇总成一个数组，在页面保存时做一个内容关联
  getCtaIds(content){
    const ctaIds = [];
    const formatComp = (comp) => {
        if (comp.ctaConfig && comp.ctaConfig.enable) {
          ctaIds.push(comp.ctaConfig.ctaId);
        }
        if (comp.action && comp.action.ctaConfig && comp.action.ctaConfig.enable) {
          ctaIds.push(comp.action.ctaConfig.ctaId);
        }
        //文档组件
        if (comp.type === 'document') {
          const keys = ['sendMailActionConfig', 'lookActionConfig', 'downloadActionConfig'];
          keys.forEach(key => formatComp(comp[key]));
        }
        //图片组件action 下ctaId 获取
        if (comp.type === 'image') {
          (comp.images || []).forEach(formatComp)
        } else if(comp.type === 'button'){
          (comp.buttonList || []).forEach(formatComp)
        }

        if (comp.components && comp.components.length) {
          comp.components.forEach(formatComp)
        }
        return ctaIds
    }
    return formatComp(content)
  },
  async sumitPageForm(pageFormId, content, extendFormData = {}) {
    const formdata = await this.formatFormData(content, pageFormId)
    const { formUsage, formBodySetting, formMoreSetting } = formdata || {}
    let newFormId = null; let resultCode; let
      resultData = {}
    console.log('_fakePageForm', pageFormId)
    // 没有表单组件时
    if (formBodySetting.length === 0 && formUsage !== 2) {
      newFormId = null
      resultCode = 0
    } else {
      const { errCode, data } = await this.ajaxAddOrUpdateForm(pageFormId, {
        ...formdata,
        ...extendFormData,
      })
      newFormId = errCode === 0 ? data.id : null
      resultCode = errCode
      resultData = data || {}
    }

    // 存在会议组件时，同步会议详情内容
    if (formMoreSetting.conferenceId) {
      let conferenceDetails = ''
      try {
        conferenceDetails = decodeURIComponent(formMoreSetting.conferenceDetails)
      } catch (error) {
        conferenceDetails = formMoreSetting.conferenceDetails || ''
      }
      this.updateConferenceContent({
        conferenceDetails,
        conferenceId: formMoreSetting.conferenceId,
      })
    }
    return {
      formId: newFormId,
      errCode: resultCode,
      data: resultData,
    }
  },
  // 接口 - 新增或更新表单数据
  async ajaxAddOrUpdateForm(formId, formdata) {
    const service = !formId ? 'addFormData' : 'updateFormDataDetail'
    const res = await http[service](formdata)
    return res
  },
  // 更新会议详情
  updateConferenceContent(params) {
    http.updateConferenceContent(params)
  },
  // 拉取服务端的表单数据，避免提交的时候，遗漏了一些源数据
  getFormDataById(page, formId) {
    return new Promise(resolve => {
      const defaultFormData = {
        formHeadSetting: {
          name: page.name,
          title: page.name,
        },
        formMoreSetting: {
          synchronousCRM: false,
          enrollLimit: false,
          fillInOnce: false,
        },
        formSuccessSetting: {
          afterSuccessAction: 99,
        },
        type: 2,
      }
      if (!formId) {
        resolve(defaultFormData)
      } else {
        http
          .getFormDataById({
            id: formId,
          })
          .then(({ errCode, data }) => {
            resolve(errCode === 0 ? data : defaultFormData)
          })
      }
    })
  },
  // 从微页面组件解析出表单内容
  getFormBodyFromPage(page) {
    const _formComp = [] // 收集微页面中的自定义表单组件
    let submitJumpType = ''// 表单提交按钮后动作类型
    let conferenceId = ''// 会议ID
    let conferenceDetails = ''// 会议详情富文本
    const customizeApinameMapping = {}// 自定义字段与预设字段映射关系
    // 在页面组件找画板组件
    const findFormComp = comps => {
      if (!comps || comps.length === 0) return
      comps.forEach(item => {
        if (['gridcontainer', 'container'].includes(item.type)) {
          findFormComp(item.components)
        } else if (item.isFormComp) {
          // 级联组件表单字段处理
          if (item.type === 'cascade') {
            item.optionInfo.forEach(option => {
              _formComp.push(option)
            })
          } else if (item.type === 'button') {
            submitJumpType = (item.action && item.action.type) || ''
          } else if (['button', 'product', 'paybutton'].indexOf(item.type) === -1) {
            _formComp.push(item)
          }
        }
        // 会议详情组件需要把微页面组件内容同步到会议详情内容，保持数据双向同步
        else if ((item.type === 'article' && item.conference && item.conferenceId) || (item.fieldName === 'marketingEventDetail' && item.conferenceId)) {
          conferenceId = item.conferenceId
          console.log('item>>>conferenceIdconferenceId', item)
          conferenceDetails = item.type === 'article' ? item.article.content : item.value || ''
        }
      })
    }
    findFormComp(page.components)
    // page.components &&
    //   page.components
    //     .filter(item => {
    //       return item.type === "container";
    //     })
    //     .forEach(item => {
    //       // 在画板组件找表单组件
    //       item.components
    //         .filter(item => item.isFormComp)
    //         .forEach(item => {
    //           // 把表单组件汇总起来
    //           if (
    //             ["button", "product", "paybutton"].indexOf(item.type) === -1
    //           ) {
    //             _formComp.push(item);
    //           }
    //         });
    //     });
    const _formatFormBody = _formComp.reduce((fields, item) => {
      // 过滤没有apiName的字段
      if (!item.fieldName) return fields
      const fieldItems = data => {
        // 存在自定义字段名，添加到映射map
        if (data.customFieldName && data.fieldName !== data.customFieldName) {
          customizeApinameMapping[data.fieldName] = data.customFieldName || ''
        }
        return {
          id: data.fieldName,
          apiName: data.fieldName,
          customApiName: data.customFieldName,
          label: data.name,
          type:
            config.CustomFormTypeMap[
              data.fieldName ? data.fieldName.split('_')[0] : data.fieldName
            ],
          helpText: data.placeholder,
          isRequired: data.required,
          isVerify: data.verify || false,
          options: data.options,
        }
      }
      // 拆分处理省市区组件字段
      if (item.type === 'region') {
        console.log('item>>>', item)
        fields.push(
          ...Object.keys(item.fields).reduce((regionFields, key) => {
            const {
              label, show, customFieldName, value,
            } = item.fields[key] || {}

            const name = /^region_/.test(value) ? `${item.name}_${label}` : label

            if (show) {
              regionFields.push(
                fieldItems({
                  ...item,
                  fieldName: value,
                  customFieldName,
                  name,
                }),
              )
            }
            return regionFields
          }, []),
        )
      } else if (item.type === 'file' && item.fileList && item.fileList.length) {
        item.fileList.map(item1 => {
          fields.push(fieldItems({
            ...item,
            ...item1,
          }))
        })
      } else {
        fields.push(fieldItems(item))
      }

      return fields
    }, [])
    // formData.formBodySetting = _formatFormBody;
    _formatFormBody.submitJumpType = submitJumpType
    _formatFormBody.conferenceId = conferenceId
    _formatFormBody.conferenceDetails = conferenceDetails
    _formatFormBody.customizeApinameMapping = customizeApinameMapping
    return _formatFormBody
  },
  // 判断本页面是否有支付订单
  judgeIsOrderForm(page) {
    let isOrderForm = false
    // 在页面组件找画板组件
    if (page.components) {
      page.components
        .filter(item => ['gridcontainer', 'container'].includes(item.type))
        .forEach(item => {
          // 在画板组件找表单组件
          item.components
            .filter(el => el.isFormComp)
            .forEach(el => {
              // 查看是否有订单提交按钮，有则代表是支付订单
              if (['paybutton'].indexOf(el.type) !== -1) {
                isOrderForm = true
              }
            })
        })
    }

    return isOrderForm
  },
  // 通过组件类型获取某个组件
  getTargetFormComponentByType(page, type) {
    let targetFormComponent = {}
    if (page.components) {
      page.components
        .filter(item => ['gridcontainer', 'container'].includes(item.type))
        .forEach(item => {
          // 在画板组件找表单组件
          item.components
            .filter(el => el.isFormComp)
            .forEach(el => {
              // 把表单组件汇总起来
              if (el.type === type) {
                targetFormComponent = el
              }
            })
        })
    }

    return targetFormComponent
  },
  // 组织表单数据
  async formatFormData(page, formId) {
    const oldFormData = await this.getFormDataById(page, formId)
    console.log('formatFormData', page, oldFormData, formId)
    const $c_paybtton = this.getTargetFormComponentByType(page, 'paybutton')
    const formBody = this.getFormBodyFromPage(page)
    !$c_paybtton.product && ($c_paybtton.product = {})
    const $c_paybtton_title = $c_paybtton.product.title || ''
    const $c_paybtton_price = ($c_paybtton.product.price || 0) * 100 // 转换为分

    const formData = {
      ...oldFormData,
      id: formId,
      formHeadSetting: {
        name: page.name,
        title: page.name,
      },
      formSuccessSetting: {
        ...oldFormData.formSuccessSetting,
        payDescription: $c_paybtton_title,
        totalFee: $c_paybtton_price,
      },
      formBodySetting: formBody,
      formMoreSetting: {
        ...oldFormData.formMoreSetting,
        // 表单提交按钮跳转后动作类型
        submitJumpType: formBody.submitJumpType || '',
        conferenceId: formBody.conferenceId || '',
        conferenceDetails: formBody.conferenceDetails || '',
      },
      customizeApinameMapping: formBody.customizeApinameMapping || null,
      formUsage: this.judgeIsOrderForm(page) ? 2 : 1, // 表单用途 1 收集线索 2 收集订单
      checkMapping: false, // 更新表单的时候，不做校验，到列表的时候，后台会统一做校验
    }
    return formData
  },
}
