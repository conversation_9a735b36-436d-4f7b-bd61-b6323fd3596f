<template>
  <div
    class="v-crmset-wrapper"
    :class="[crmData.crm_switch ? 'expand-leadpool' : '']"
  >
    <!-- crm设置 -->
    <div class="field-property crm-field">
      <div class="field-property__crm">
        <div class="crm__leadTyp-wrapper">
          <span style="color: var(--color-neutrals11);">{{ $t('marketing.pages.site.nkyxzjsjcr_e32064') }}</span>
          <div class="crm-leaType__list">
            <div
              v-for="item in crm_leadTypeList"
              :key="item.id"
              class="crm-leadType__item"
              @click="crmLeadType = item.id"
            >
              <img class="selected-icon" v-show="crmLeadType == item.id" src="../../../assets/images/site/check.png" alt="">
              <div :class="['leadType-item-main',crmLeadType == item.id && 'selected']">
                <div class="leadType-item-left">
                  <img class="leadType-icon" :src="item.icon" alt="">
                </div>
                <div class="leadType-item-right">
                  <div class="leadType-title">
                    {{ item.name }}
                  </div>
                  <div class="leadType-desc">
                    {{ item.desc }}
                    <span
                      class="link"
                      @click="handleLeadTypeLink(item.linkAddress)"
                    >{{ item.linkText }}</span>
                  </div>
                  <div v-if="item.id == 'leadPool'" class="crm__leadPool">
                    <div
                      v-if="!crm_hasLeadPool && crm_show_leadPool_Tips && crmLeadType == 'leadPool'"
                      class="leadPool_tips"
                    >
                      {{ $t("marketing.commons.zwxscqqrxs_96eaf4") }}
                    </div>
                    <div :hidden="!crmData.crm_switch">
                      <div class="crm-leadPoolId">
                        <Select
                          v-model="crmData.crm_leadPoolId"
                          class="el-select crm-leadPoolId__select"
                          size="small"
                          filterable
                          :placeholder="$t('marketing.commons.qxz_708c9d')"
                          @change="handleLeadPoolIdChange"
                        >
                          <Option
                            v-for="item in crm_leadPoolIdList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                          />
                        </Select>
                      </div>
                    </div>
                    <div v-if="crm_show_leadPoolId_Tips && crmLeadType == 'leadPool'" class="tips">
                      {{ $t("marketing.commons.qxzxsc_4a57ae") }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="crm-mapping">
          <div>{{ $t('marketing.pages.site.xsys_4c9794') }}</div>
          <div style="display: flex;align-items: center;gap: 4px;" v-if="crmData.crm_mappingList.length && crmData.crm_isSubmited">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="#30C776"
              style="margin-right: 0;"
            >
              <path
                d="M8 15C11.866 15 15 11.866 15 8C15 4.13401 11.866 1 8 1C4.13401 1 1 4.13401 1 8C1 11.866 4.13401 15 8 15ZM8 16C3.58172 16 0 12.4183 0 8C0 3.58172 3.58172 0 8 0C12.4183 0 16 3.58172 16 8C16 12.4183 12.4183 16 8 16Z"
                fill="#30C776"
              />
              <path
                d="M4.14645 7.69445C3.95118 7.88971 3.95118 8.20629 4.14645 8.40156L6.62132 10.8764C6.81658 11.0717 7.13317 11.0717 7.32843 10.8764L12.1013 6.10357C12.2966 5.9083 12.2966 5.59172 12.1013 5.39645C11.906 5.20119 11.5895 5.20118 11.3942 5.39644L6.97487 9.81577L4.85355 7.69445C4.65829 7.49919 4.34171 7.49919 4.14645 7.69445Z"
                fill="#30C776"
              /></svg
            >{{$t('marketing.commons.ysz_44e607')}}
          </div>
          <div style="display: flex;align-items: center;gap: 4px;" v-else>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" style="margin-right: 0;" viewBox="0 0 16 17" fill="#FF522A">
              <path d="M8 15.7461C4.13401 15.7461 1 12.6121 1 8.74609C1 4.8801 4.13401 1.74609 8 1.74609C11.866 1.74609 15 4.8801 15 8.74609C15 12.6121 11.866 15.7461 8 15.7461ZM8 16.7461C12.4183 16.7461 16 13.1644 16 8.74609C16 4.32782 12.4183 0.746094 8 0.746094C3.58172 0.746094 0 4.32782 0 8.74609C0 13.1644 3.58172 16.7461 8 16.7461Z" fill="#FF522A"/>
              <path d="M7.5 4.74609C7.5 4.46995 7.72386 4.24609 8 4.24609C8.27614 4.24609 8.5 4.46995 8.5 4.74609V9.74609C8.5 10.0222 8.27614 10.2461 8 10.2461C7.72386 10.2461 7.5 10.0222 7.5 9.74609V4.74609Z" fill="#FF522A"/>
              <path d="M8.75 12.4961C8.75 12.9103 8.41421 13.2461 8 13.2461C7.58579 13.2461 7.25 12.9103 7.25 12.4961C7.25 12.0819 7.58579 11.7461 8 11.7461C8.41421 11.7461 8.75 12.0819 8.75 12.4961Z" fill="#FF522A"/>
            </svg>
            {{ $t('marketing.commons.wsz_fe2d26') }}
          </div>
          <div
            class="km-a-btn"
            @click="handleOpenCrmDialog"
          >
            {{ $t('marketing.commons.sz_e366cc') }}
          </div>
          <div
            v-if="crmData.crm_showTips"
            class="tips"
          >
            {{ $t('marketing.commons.bdzdybgqsz_7858a3') }}
          </div>
          <div
            v-else-if="crm_showSettingTips"
            class="tips"
          >
            {{ $t('marketing.commons.swszxsys_f9975b') }}
          </div>
          <crm-dialog
            :show-crm-dialog.sync="crm_dialog"
            :activity-field="fields"
            :object-type.sync="crmData.crm_objectType"
            :mapping-list.sync="crmData.crm_mappingList"
            :crm_showTips.sync="crmData.crm_showTips"
            :crm_isSubmited.sync="crmData.crm_isSubmited"
            :strict-map-by-type="strictMapByType"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import _ from 'lodash'
import CrmDialog from '@/components/crm-mapping-dialog/index.vue'

import { alert } from '@/utils/globals.js'
import http from '@/services/http/index.js'

import CONST from './const.js'
import LeadImg from '@/assets/images/site/leads.png'
import SalesImg from '@/assets/images/site/sale-leads.png'

export default {
  components: {
    CrmDialog,
    Select: FxUI.Select.components.ElSelect,
    Option: FxUI.Select.components.ElSelect.components.ElOption,
  },
  props: {
    field: {
      type: Object,
      default: () => ({}),
    },
    fields: {
      type: Array,
      default: () => [],
    },
    strictMapByType: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      data: _.extend({}, this.field),
      crmLeadType: 'leadPool',
      crm_leadTypeList: [
        {
          name: $t('marketing.commons.xsc_7b62ce'),
          id: 'leadPool',
          desc: $t('marketing.pages.site.tyjrxscjhg_fba173'),
          link: true,
          linkText: $t('marketing.commons.xq_f26225'),
          linkAddress: 'https://help.fxiaoke.com/1969/ba74/06d2',
          icon: LeadImg,
        },
        {
          name: $t('marketing.commons.xsxs_d4ed8c'),
          id: 'salesLead',
          desc: $t('marketing.pages.site.dyytgrdxsz_c76905'),
          link: false,
          icon: SalesImg,
        },
      ],
      crm_hasLeadPool: false,
      crm_show_leadPool_Tips: false,
      crmData: {
        crm_isSubmited: false,
        crm_switch: true,
        crm_leadPoolId: null,
        crm_objectType: '',
        crm_mappingList: [],
        crm_showTips: false,
      },
      crm_dialog: false,
      crm_leadPoolIdList: [],
      crm_showSettingTips: false,
      oldFormatFields: [],
      crm_show_leadPoolId_Tips: false,
    }
  },
  computed: {
    dFrom() {
      return this.$store.state.Form
    },
  },
  watch: {
    fields(newVal) {
      console.log('fields', newVal)
      const _newVal = Object.assign([], newVal)
      const formatFields = JSON.stringify(
        _newVal.sort((item1, item2) => (item1.apiName > item2.apiName) - 0.5),
      )
      if (formatFields != this.oldFormatFields) {
        if (this.oldFormatFields.length && this.crmData.crm_mappingList.length > 0) {
          this.crmData.crm_showTips = true
        }
        this.oldFormatFields = formatFields
      }
      // if (this.crmData.crm_mappingList.length === 0) {
      //   this.crmData.crm_mappingList = CONST.getDefaultEnrollLeadsFieldMappings(
      //     this.fields.map((item) => item.apiName),
      //   );
      // }
    },
    crmLeadType() {
      if (this.crmLeadType === 'salesLead') {
        this.crmData.crm_leadPoolId = null
      }
    },
    crmData: {
      deep: true,
      handler(newVal) {
        this.$emit('set:crmData', {
          crm_showTips: newVal.crm_showTips,
          isEnrollSyncToLeads: newVal.crm_switch,
          synchronousCRM: newVal.crm_switch,
          crmLeadPoolId: newVal.crm_leadPoolId,
          enrollLeadsTargetObjectRecordType: newVal.crm_objectType,
          enrollLeadsFieldMappings: newVal.crm_mappingList,
        })
        // 默认选中（即未设置映射的时候）的是线索池 leadPool 如果有crm_leadPoolId 说明是之前设置的为线索池
        this.crmLeadType = (this.crmData.crm_leadPoolId || (!this.crmData.crm_mappingList.length && this.crmLeadType !== 'salesLead'))
          ? 'leadPool'
          : 'salesLead'
        if (newVal.crm_mappingList.length) {
          this.crm_showSettingTips = false
        }
      },
    },
    crm_dialog(newVal) {
      console.log(newVal)
    },
  },
  mounted() {
    this.ajaxCrmObjectType()
  },
  methods: {
    ajaxCrmObjectType() {
      http.listLeadPools().then(
        results => {
          if (results && results.errCode === 0) {
            if (results.data && results.data.length > 0) {
              this.crm_hasLeadPool = true
              this.crm_leadPoolIdList = results.data
            } else {
              this.crm_hasLeadPool = false
            }
          }
        },
        () => {
          alert($t('marketing.commons.xsclbhqsbq_ab1691'))
        },
      )
    },
    handleSwitchCrm() {
      if (
        this.crmData.crm_switch != this.old_crm_switch
        && !this.crm_hasLeadPool
      ) {
        this.crmData.crm_switch = false
        this.crm_show_leadPool_Tips = true
        this.ajaxCrmObjectType()
      }
      this.old_crm_switch = this.crmData.crm_switch
    },
    handleOpenCrmDialog() {
      // 如果是线索池  必须得先选线索池才能设置线索映射
      if (this.crmLeadType === 'leadPool' && !this.crmData.crm_leadPoolId) {
        this.crm_show_leadPoolId_Tips = true
        return
      }
      this.crm_dialog = true
      if (this.crmData.crm_mappingList.length === 0) {
        this.crmData.crm_mappingList = CONST.getDefaultEnrollLeadsFieldMappings(
          this.fields.map(item => item.apiName),
        )
      }
    },
    export__setCrmData(crmData) {
      this.crmData.crm_switch = true // crmData.crm_switch后台默认会给false，但这里需要重置为true，才能出发校验
      this.crmData.crm_leadPoolId = crmData.crmLeadPoolId
      this.crmData.crm_mappingList = crmData.enrollLeadsFieldMappings || []
      this.crmData.crm_objectType = crmData.enrollLeadsTargetObjectRecordType
      this.crmData.crm_isSubmited = true
    },
    export__validate() {
      if (
        (this.crmData.crm_switch
          && (!this.crmData.crm_mappingList.length
            || !this.crmData.crm_isSubmited))
        || this.crmData.crm_showTips
      ) {
        this.crm_showSettingTips = true
        return false
      }
      if (this.crmLeadType === 'leadPool' && !this.crmData.crm_leadPoolId) {
        this.crm_show_leadPoolId_Tips = true
        return false
      }
      return true
    },
    handleLeadTypeLink(link) {
      window.open(link, 'blank')
    },
    handleLeadPoolIdChange() {
      this.crm_show_leadPoolId_Tips = false
    },
  },
}
</script>

<style lang="less" scoped>
@basePath: "../../../";

.v-crmset-wrapper {
  display: flex;
  flex-flow: column nowrap;
  height: 100%;
  // &.expand-leadpool {
  //   border-bottom: 1px dashed #e9edf5;
  // }
  .title {
    flex: 0 0 auto;
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    padding: 0 14px;
    height: 50px;
    line-height: 50px;
    font-size: 16px;
    color: #212b36;
    border-right: 1px solid #ededed;
    border-bottom: 1px solid #ededed;
    .title-tab {
      width: 100%;
      text-align: center;
      cursor: pointer;
    }
    .title-tab.active {
      // border-bottom: 3px solid #fcb058;
      box-shadow: 0 3px 0 0 #fcb058;
    }
    .title-tab.disable {
      color: #999;
    }
  }
  .field-property {
    margin-bottom: 10px;
    & > .tips {
      margin-top: 4px;
      margin-left: 24px;
      color: #999;
    }
  }
  .field-property__name {
    margin-left: 10px;
    margin-bottom: 8px;
    color: #151515;
    font-size: 13px;
    & > span {
      margin-left: -8px;
      margin-right: 2px;
      color: #ff7663;
    }
  }
  .field-property__crm {
    .leadPool_tips {
      color: #ff7663;
      width: 100%;
      font-size: 12px;
      margin-left: 2px;
    }
    .crm__leadTyp-wrapper {
      display: flex;
      flex-direction: column;
      .crm-leaType__list{
        display: flex;
        margin: 10px 0;
        gap: 10px;
        .crm-leadType__item{
          flex: 1;
          position: relative;
          display: flex;
          background-color: #fff;
          .leadType-item-main{
            padding: 10px;
            border: 1px solid #e9edf5;
            border-radius: 4px;
            display: flex;
            align-items: center;
            .leadType-item-left{
              margin-right: 10px;
              width: 80px;
              height: 80px;
              img{
                width: 100%;
                height: 100%;
              }
            }
            .leadType-item-right{
              flex: 1;
              .leadType-title{
                font-size: 14px;
                font-weight: bold;
                color: #181c25;
                line-height: 20px;
                margin-bottom: 0;
              }
              .leadType-desc{
                color: var(--color-neutrals15);;
                line-height: 18px;
                font-size: 12px;
                margin-bottom: 6;
                .link{
                  color: #407FFF;
                  margin-left: 5px;
                  cursor: pointer;
                }
              }
            }
          }
          .selected{
            border: 1px solid var(--color-primary06);
            &::after{
              content: "";
              position: absolute;
              left: 0;
              top: 0;
              border-bottom: 24px solid var(--color-primary06);
              border-left: 24px solid transparent;
               transform: rotate(-180deg);
            }
          }
          .selected-icon{
            position: absolute;
            left: 2px;
            top: 1px;
            width: 12.569px;
            height: 9.444px;
            z-index: 9;
          }
        }
      }
    }
    .fx-select {
      margin: 6px 0;
    }
    .crm-leadPoolId {
      .crm-leadPoolId__select {
        width: 100%;
      }
    }
    .crm-mapping {
      display: flex;
      flex-wrap: wrap;
      font-size: 13px;
      color: #333333;
      * {
        margin-right: 20px;
      }
    }
    .tips {
        color: #ff7663;
        width: 100%;
        margin-left: 2px;
        font-size: 12px;
    }
  }
  .field-property__options {
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    margin-top: 8px;
    .option-label {
      margin-right: 16px;
      width: 180px;
    }
    .option-add,
    .option-del {
      margin: 0 4px;
      height: 24px;
      width: 24px;
      border-radius: 50%;
      border: 1px solid #eaeaea;
    }
    .option-add {
      background: url("@{basePath}assets/images/icons/add.png") center no-repeat;
      background-size: 12px;
    }
    .option-del {
      background: url("@{basePath}assets/images/close.png") center no-repeat;
      background-size: 12px;
    }
  }
}
</style>
