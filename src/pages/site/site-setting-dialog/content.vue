<template>
  <div v-loading="isLoading" class="w-other-info">
    <div v-if="vDatas.strategy == 'dingTalk'" class="w-setting-tips">
      <div class="icon" />
      {{ $t('marketing.pages.site.khtjdbdxxs_19eb39') }}
    </div>
    <template v-if="formId">
      <!-- 会议新建的时候不显示线索对象设置 -->
      <div class="w-setting" v-if="!fromCreate">
        <span class="setting-area__name" style="line-height: 32px;">{{ $t('marketing.pages.site.sjcrcj_2e9fba') }}</span>
        <div class="setting-area__info" style="width: 100%;">
          <fx-select
            v-model="formMoreSetting.saveCrmObjectType"
            class="radio-group"
            :options="crmObjectTypeOptions"
            size="small"
            @change="handleCrmObjectTypeChange"
            style="width: 100%;"
          >
          </fx-select>
        </div>
      </div>
      <div v-show="formMoreSetting.saveCrmObjectType === 1" class="w-setting">
        <span class="setting-area__name">{{ $t('marketing.pages.site.crsz_b18572') }}</span>
        <div class="setting-area__info">
          <div>{{ $t('marketing.pages.site.qxzxycrddx_f6b201') }}</div>
          <div class="crm-list">
            <Select
              v-model="customObject"
              size="small"
              class="custom-object-select"
              filterable
              :placeholder="$t('marketing.commons.qxz_708c9d')"
              @change="handleCustomObjectChange"
            >
              <Option
                v-for="item in customObjectList"
                :key="item.api_name"
                :label="item.display_name"
                :value="item.api_name"
              />
            </Select>
          </div>
          <div class="setting-area__wrapper">
            <div>{{ $t('marketing.pages.site.szsjcrys_4ea698') }}</div>
            <div>
              {{
                !!customObjectMappingData.length
                  ? $t('marketing.commons.ysz_44e607')
                  : $t('marketing.commons.wsz_fe2d26')
              }}
            </div>
            <div class="km-a-btn" @click="handleShowCrmMappingDialog">
              {{ $t('marketing.commons.sz_e366cc') }}
            </div>
          </div>
        </div>
      </div>
      <div v-show="formMoreSetting.saveCrmObjectType !== 1" class="w-setting">
        <span class="setting-area__name">{{ $t('marketing.commons.xssz_4dc706') }}</span>
        <div class="setting-area__info">
          <v-crm-set
            ref="crmSetting"
            :fields="fields"
            @set:form="handleSetForm"
            @set:crmData="handleSetCrm"
            @set:field="handleSetField"
          />
          <div>
            <el-checkbox-group
              v-model="formMoreSetting.syncToMember"
              class="checkbox-group"
              @change="handleSetLeadCharger"
            >
              <el-checkbox :label="$t('marketing.pages.site.xscrcghzdc_66878d')" />
            </el-checkbox-group>
          </div>
        </div>
      </div>
    </template>
    <div
      v-if="showActivityMemberSet && formId && formMoreSetting.saveCrmObjectType === 0"
      class="w-setting"
    >
      <span class="setting-area__name">{{ $t('marketing.commons.hdcyys_6f4690') }}</span>
      <div class="setting-area__info">
        <div style="color: var(--color-neutrals11);margin-bottom: 10px;">
          {{ $t('marketing.commons.nkyjbmbdtj_3e4cb7') }}
        </div>
        <div class="setting-area__wrapper" style="display: flex;align-items: center;gap: 4px;"  >
          <div>{{ $t('marketing.pages.meeting_marketing.hdcyys_635a3e')}}</div>
          <div v-if="cmMappingData.length">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="#30C776"
            >
              <path
                d="M8 15C11.866 15 15 11.866 15 8C15 4.13401 11.866 1 8 1C4.13401 1 1 4.13401 1 8C1 11.866 4.13401 15 8 15ZM8 16C3.58172 16 0 12.4183 0 8C0 3.58172 3.58172 0 8 0C12.4183 0 16 3.58172 16 8C16 12.4183 12.4183 16 8 16Z"
                fill="#30C776"
              />
              <path
                d="M4.14645 7.69445C3.95118 7.88971 3.95118 8.20629 4.14645 8.40156L6.62132 10.8764C6.81658 11.0717 7.13317 11.0717 7.32843 10.8764L12.1013 6.10357C12.2966 5.9083 12.2966 5.59172 12.1013 5.39645C11.906 5.20119 11.5895 5.20118 11.3942 5.39644L6.97487 9.81577L4.85355 7.69445C4.65829 7.49919 4.34171 7.49919 4.14645 7.69445Z"
                fill="#30C776"
              /></svg
            >{{$t('marketing.commons.ysz_44e607')}}
          </div>
          <div style="display: flex;align-items: center;gap: 4px;" v-else>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="#FF522A">
              <path d="M8 15.7461C4.13401 15.7461 1 12.6121 1 8.74609C1 4.8801 4.13401 1.74609 8 1.74609C11.866 1.74609 15 4.8801 15 8.74609C15 12.6121 11.866 15.7461 8 15.7461ZM8 16.7461C12.4183 16.7461 16 13.1644 16 8.74609C16 4.32782 12.4183 0.746094 8 0.746094C3.58172 0.746094 0 4.32782 0 8.74609C0 13.1644 3.58172 16.7461 8 16.7461Z" fill="#FF522A"/>
              <path d="M7.5 4.74609C7.5 4.46995 7.72386 4.24609 8 4.24609C8.27614 4.24609 8.5 4.46995 8.5 4.74609V9.74609C8.5 10.0222 8.27614 10.2461 8 10.2461C7.72386 10.2461 7.5 10.0222 7.5 9.74609V4.74609Z" fill="#FF522A"/>
              <path d="M8.75 12.4961C8.75 12.9103 8.41421 13.2461 8 13.2461C7.58579 13.2461 7.25 12.9103 7.25 12.4961C7.25 12.0819 7.58579 11.7461 8 11.7461C8.41421 11.7461 8.75 12.0819 8.75 12.4961Z" fill="#FF522A"/>
            </svg>
            {{ $t('marketing.commons.wsz_fe2d26') }}
          </div>
          <div class="km-a-btn" @click="showCmDialog = true">
            {{ $t('marketing.commons.sz_e366cc') }}
          </div>
        </div>
      </div>
    </div>
    <div v-if="formId && isOpenMember" class="w-setting">
      <span class="setting-area__name" style="line-height: 32px;">{{$t('marketing.commons.hytxbdsz_e2e591')}}</span>
      <div class="setting-area__info" :style="fromCreate ? 'width: 50%;' : 'width: 100%;'">
        <fx-select
          v-model="formMoreSetting.memberCheckType"
          class="radio-group"
          size="small"
          :options="memberCheckOptions"
          @change="handleSetMemberCheckType"
          style="width: 100%;"
        >
        </fx-select>
        <div style="margin-top: 12px;" v-if="formMoreSetting.memberCheckType === 2" class="setting-area__wrapper">
          <div>{{ $t('marketing.pages.site.hyxxhtys_7f9067') }}</div>
          <div>
            {{ !!memberToFormMapping.length ? $t('marketing.commons.ysz_44e607') : $t('marketing.commons.wsz_fe2d26') }}
          </div>
          <div class="km-a-btn" @click="memberMappingVisible = true">
            {{ $t('marketing.commons.sz_e366cc') }}
          </div>
        </div>
      </div>
    </div>
    <div class="w-setting" v-if="formId">
      <span class="setting-area__name">{{ $t('marketing.commons.qtsz_dda36e') }}</span>
      <div class="setting-area__info">
        <el-checkbox-group v-model="formMoreSetting.fillInOnce" class="checkbox-group" @change="handleSetLeadCharger">
          <el-checkbox :label="$t('marketing.commons.mrzntxyc_fc648c')" />
        </el-checkbox-group>
        <el-checkbox-group v-model="formMoreSetting.enrollLimit" class="checkbox-group" @change="handleSetLeadCharger">
          <el-checkbox>
            <div class="info__enrollLimit">
              <span>{{ $t('marketing.commons.xzbdtjsbcg_b5ddd1') }}</span>
              <fx-input class="enrollLimit__num" size="small" v-model="formMoreSetting.enrollLimitNum"></fx-input>
              <span>{{ $t('marketing.commons.gtjmests_b7240f') }}</span>
              <fx-input class="enrollLimit__text" size="small" v-model="formMoreSetting.enrollLimitText"></fx-input>
            </div>
          </el-checkbox>
        </el-checkbox-group>
      </div>
    </div>
    <div class="w-setting" v-if="(siteSettingType === 1 || siteSettingType === 2) && formId && showApplyObject">
      <span class="setting-area__name">{{ $t('marketing.pages.site.swbmnr_76370c') }}</span>
      <div class="setting-area__info">
        <fx-switch size="small" v-model="formData.isApplyObject" />
      </div>
    </div>
    <div class="w-setting" v-if="isShowTagSetting">
      <span class="setting-area__name">{{ $t('marketing.commons.tjbq_736eaa') }}</span>
      <div class="setting-area__info">
        <template v-if="formId">
          <div> {{ $t("marketing.pages.meeting_marketing.gtjbdztjbq_c8fd2e") }}</div>
          <selector-line v-model="formData.tagNameList" style="line-height: 26px; margin-bottom: 16px" />
        </template>
        <template v-if="siteId">
          <div>{{ siteSettingType === 1 ? $t("marketing.pages.meeting_marketing.gfwhybmzyz_961883") : $t('marketing.pages.site.gfwwymdyht_3815b1') }}</div>
          <selector-line v-model="siteVisitTags" style="line-height: 26px;" />
        </template>
      </div>
    </div>
    <v-campaign-member-dialog
      ref="campaignMemberSetting"
      :fields="fields"
      :show-dialog.sync="showCmDialog"
      @set:cmMapping="handleSetCmMappings"
    />
    <v-crm-mapping-dialog
      v-if="showCrmMappingDialog"
      ref="crmMappingSetting"
      :fields="fields"
      :show-dialog.sync="showCrmMappingDialog"
      :target-object-name="customObjectName"
      :object-api-name="customObject"
      @set:cmMapping="handleCustomObjectMapping"
    />
    <crm-mapping-dialog
      v-if="!memberMappingLoading && isOpenMember"
      :title="$t('marketing.commons.hysjtcbdys_a5be4b')"
      :target-object-tips="$t('marketing.commons.zyhtxbdszc_c3a4e3')"
      :object-name="$t('marketing.commons.hy_4d9dd5')"
      :target-object-name="$t('marketing.commons.bd_eee1e2')"
      object-api-name="MemberObj"
      select-member-field
      :show-crm-dialog.sync="memberMappingVisible"
      :activity-field="memberFields"
      :mapping-list.sync="memberToFormMapping"
      :target-fields="formFields"
      :show-object-target="false"
    />
  </div>
</template>

<script>
import _ from 'lodash'
import http from '@/services/http/index.js';
import VCampaignMemberDialog from './campaign-member-dialog.vue';
import VCrmSet from './crm-set.vue';
import SelectorLine from '@/components/tags-selector-new/tags-line.vue';
import kisvData from '@/modules/kisv-data.js';
import VCrmMappingDialog from './crm-mapping-dialog.vue';
import CrmMappingDialog from '@/components/crm-mapping-dialog/index.vue'
import { FIELD_TYPE_MAP } from '@/components/crm-mapping-dialog/const.js'

export default {
  components: {
    Dropdown: FxUI.Dropdown,
    DropdownMenu: FxUI.DropdownMenu,
    DropdownItem: FxUI.DropdownItem,
    elRadio: FxUI.Radio,
    VCampaignMemberDialog,
    VCrmSet,
    ElCheckbox: FxUI.Checkbox,
    ElCheckboxGroup: FxUI.CheckboxGroup,
    ElInput: FxUI.Input,
    SelectorLine,
    Select: FxUI.Select.components.ElSelect,
    Option: FxUI.Select.components.ElSelect.components.ElOption,
    VCrmMappingDialog,
    CrmMappingDialog,
  },
  props: {
    fromCreate: {
      type: Boolean,
      default: false,
    },
    formId: {
      type: String,
      default: '',
    },
    siteId: {
      type: String,
      default: '',
    },
    formUsage: {
      type: Number,
      default: 1,
    },
    showActivityMemberSet: {
      type: Boolean,
      default: false,
    },
    // 微页面用途  1、会议报名设置 2、活动推广设置 3、表单设置
    siteSettingType: {
      type: Number,
      default: 0,
    },
    isApplyObject: {
      type: Boolean,
      default: false,
    },
    isShowTagSetting: {
      type: Boolean,
      default: true,
    },
    showApplyObject: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      vDatas: kisvData.datas,
      propsCrmData: {},
      crmData: {},
      cmMappingData: [],
      leadCharger: false,
      // isAddTags: false,
      formData: {
        formMoreSetting: {
          fillInOnce: false,
          checkMember: false,
          enrollLimit: false,
          // memberCheckType: 0,
          enrollLimitNum: 100,
          enrollLimitText: $t('marketing.commons.wftjcgnfwd_e0e862'),
          synchronousCRM: true,
          syncToMember: false,
          saveCrmObjectType: 0, // 存入对象类型 0:线索对象 1:其他任意对象
        },
        // 是否设置该推广内容为报名内容
        isApplyObject: false,
      },
      originSaveCrmObjectType: 0, // 这里做一个备份，用于切换场景时候重置状态
      // 访问微页面打标签
      siteVisitTags: [],
      showCmDialog: false,
      customObject: '',
      customObjectList: [],
      showCrmMappingDialog: false,
      customObjectMappingData: [],
      customCrmRecordType: 'default__c',
      isLoading: true,
      customObjectMappingDataCache: {},
      memberMappingVisible: false,
      memberFields: [],
      memberToFormMapping: [],
      memberMappingLoading: true,
      memberCheckOptions: [
        {
          label: $t('marketing.pages.site.bzdsbsfxyz_06cc3e'),
          value: 0
        },
        {
          label: $t('marketing.pages.site.zdsbbtchyj_1b4fa3'),
          value: 2
        },
        {
          label: $t('marketing.pages.meeting_marketing.zdsbhysfyh_a10999'),
          value: 1
        }
      ],
      crmObjectTypeOptions: [
        {
          label: $t('marketing.pages.site.sjxsxxsjcr_280a0d'),
          value: 0
        },
        {
          label: $t('marketing.pages.site.sjqtxxsjcr_a04650'),
          value: 1
        }
      ]
    };
  },
  computed: {
    fields() {
      return [...(this.formData.formBodySetting || [])]
    },
    formFields() {
      if (this.formData.formBodySetting) {
        return this.formData.formBodySetting.map(item => {
          const fieldTypeName = item.type === 'select_manny' ? 'select_many' : item.type
          return {
            fieldCaption: item.label,
            fieldName: item.apiName,
            fieldType: FIELD_TYPE_MAP[fieldTypeName] || 2,
            fieldTypeName,
            isNotNull: false,
            label: null,
            labelName: null,
            targetApiName: null,
            enumDetails: item.options ? item.options.map(el => ({
              itemCode: el.value,
              itemName: el.label,
              isRequired: null,
              children: null,
            })) : [],
          }
        })
      }
      return []
    },
    formMoreSetting() {
      return this.formData.formMoreSetting;
    },
    // crmData() {
    //   return this.formData.crmData;
    // },
    addTagsFlag: {
      get() {
        return this.formData.isAddTags;
      },
      set(value) {
        this.$store.commit('setIsAddTags', value);
      },
    },
    customObjectName() {
      if (!this.customObject) {
        return '';
      }

      const crm = this.customObjectList.find((el) => el.api_name === this.customObject);
      if (crm) {
        return crm.display_name;
      }

      return '';
    },
    isOpenMember() {
      return this.$store.state.Member.isOpenMember
    },
    conferenceDetail() {
      return this.$store.state.MeetingMarketing.conferenceDetail
    },
  },
  created() {},
  mounted() {
    this.findFormDescribeField();
    this.getFormDataById();
    this.queryHexagonVisitTag();

    // this.ajax_getCrmObjectFields()
  },
  methods: {
    ajax_getCrmObjectFields() {
      http
        .getCrmObjectFields({
          objectApiName: 'MemberObj', // MemberObj
          recordType: 'default__c',
        })
        .then(
          results => {
            this.memberMappingLoading = false
            if (results && results.errCode === 0) {
              this.memberFields = (results.data || []).filter(item => ![FIELD_TYPE_MAP.image].includes(item.fieldType))
            }
          },
        )
    },
    findFormDescribeField() {
      if (!this.formId) {
        return;
      }

      http.findFormDescribeField().then((res) => {
        const { errCode, data } = res;
        if (errCode === 0) {
          this.customObjectList = data.resultList || [];
        }
      });
    },
    queryHexagonVisitTag() {
      if (!this.siteId) {
        return;
      }
      http
        .queryHexagonVisitTag({
          hexagonSiteId: this.siteId,
        })
        .then(({ errCode, data }) => {
          if (errCode === 0) {
            this.siteVisitTags = data || [];
          }
        });
    },
    getFormDataById() {
      console.log('this.formId', this.formId);
      if (!this.formId) {
        this.isLoading = false;
        return;
      }
      http.getFormDataById({ id: this.formId }).then(({ errCode, data }) => {
        if (errCode === 0) {
          console.log('getFormDataById', data);
          const formData = data;
          this.originSaveCrmObjectType = formData.formMoreSetting.saveCrmObjectType;
          this.formData = {
            ...formData,
            tagNameList: formData.tagNameList || [],
            formMoreSetting: {
              syncToMember: formData.formMoreSetting.syncToMember || false,
              synchronousCRM: true,
              fillInOnce: formData.formMoreSetting.fillInOnce || false,
              checkMember: formData.formMoreSetting.checkMember || false,
              enrollLimit: formData.formMoreSetting.enrollLimit || false,
              enrollLimitNum: formData.formMoreSetting.enrollLimitNum || 100,
              memberCheckType: formData.formMoreSetting.memberCheckType || 0,
              enrollLimitText: formData.formMoreSetting.enrollLimitText || $t('marketing.commons.wftjcgnfwd_e0e862'),
              submitJumpType: formData.formMoreSetting.submitJumpType || '',
              saveCrmObjectType: formData.formMoreSetting.saveCrmObjectType,
            },
            isApplyObject: formData.isApplyObject || false,
          };

          const { formMoreSetting, crmFormFieldMap, crmRecordType, crmPoolId, tagNameList } = formData;
          this.cmMappingData = formData.campaignMemberMap || [];
          this.memberToFormMapping = _.cloneDeep(formData.memberToFormMapping || [])
          const crmData = {
            enrollLeadsFieldMappings: crmFormFieldMap,
            enrollLeadsTargetObjectRecordType: crmRecordType,
            crmLeadPoolId: crmPoolId,
            crm_switch: formMoreSetting.synchronousCRM,
            tagNameList,
          };
          this.propsCrmData = crmData;
          if (formMoreSetting.synchronousCRM) {
            this.$refs.campaignMemberSetting.export__setCmData(this.cmMappingData);
          }

          this.handleCrmObjectTypeChange(formData.formMoreSetting.saveCrmObjectType);
          if (this.isOpenMember) {
            this.ajax_getCrmObjectFields()
          }
          if (this.siteSettingType === 1) {
            this.formData.tagNameList = this.conferenceDetail.tagNameList || [];
          }
          if (this.siteSettingType === 1 || this.siteSettingType === 2) {
            this.formData.isApplyObject = this.isApplyObject
          }
          this.isLoading = false;
        }
      });
    },
    handleCrmObjectTypeChange(type) {
      if (type === 0 && this.originSaveCrmObjectType === 0) {
        if (this.formMoreSetting.synchronousCRM) {
          this.$refs.crmSetting.export__setCrmData(this.propsCrmData);
        }
      }

      if (type === 1) {
        this.customObject = this.originSaveCrmObjectType === 1 ? this.formData.crmApiName : '';
        this.customObjectMappingData = this.originSaveCrmObjectType === 1 ? this.formData.crmFormFieldMap || [] : [];
        this.customCrmRecordType = this.originSaveCrmObjectType === 1 ? this.formData.crmRecordType : 'default__c';

        if (this.$refs.crmMappingSetting) {
          this.$refs.crmMappingSetting.export__setCmData(this.customObjectMappingData, this.customCrmRecordType);
        }
      }
    },
    handleCustomObjectChange() {
      if (this.customObject === this.formData.crmApiName) {
        this.customObjectMappingData = this.formData.crmFormFieldMap;
      } else if (this.customObjectMappingDataCache[this.customObject]) {
        this.customObjectMappingData = this.customObjectMappingDataCache[this.customObject];
      } else {
        this.customObjectMappingData = [];
      }
    },
    handleSetLeadCharger() {},
    handleSetMemberCheckType() {
      // 会议主页设置不走这里
      if (!this.formId || this.siteSettingType === 1) return
      http
        .updateFormDataMoreSetting({
          id: this.formId,
          memberCheckType: this.formMoreSetting.memberCheckType,
        })
        .then(({ errCode, data }) => {
          if (errCode === 0) {
            FxUI.Message.success($t('marketing.commons.szcg_f6088e'))
          }
        })
    },
    handleSetForm(formData) {
      // if (_.isEqual(this.formData, formData)) return;
      // this.formData = _.extend({}, formData);
    },
    handleSetField(field) {
      // if (!field.apiName) return;
      // const index = _.findIndex(this.previewList, (item) => {
      //   return item.apiName === field.apiName;
      // });
      // this.previewList.splice(index, 1, field);
    },
    handleSetCrm(crmData) {
      this.crmData = crmData;
    },
    handleSetCmMappings(mappingData) {
      this.cmMappingData = mappingData || [];
    },
    handleCustomObjectMapping({ cmMappingData, crmRecordType }) {
      this.customObjectMappingData = cmMappingData || [];
      this.customCrmRecordType = crmRecordType;

      this.customObjectMappingDataCache[this.customObject] = cmMappingData;
    },
    handleShowCrmMappingDialog() {
      if (!this.customObject) {
        FxUI.Message.warning($t('marketing.pages.site.qxzcrdx_8a8581'));
        return;
      }

      this.showCrmMappingDialog = true;
      this.$nextTick(() => {
        if (this.$refs.crmMappingSetting) {
          this.$refs.crmMappingSetting.export__setCmData(this.customObjectMappingData, this.customCrmRecordType);
        }
      });
    },
    validate() {
      if (!this.formId) return true;

      const { saveCrmObjectType } = this.formMoreSetting;
      return (
        (saveCrmObjectType === 0 && this.$refs.crmSetting.export__validate()) ||
        (saveCrmObjectType === 1 && this.customObject)
      );
    },
    getSubmitData() {
      const _cmMappingData = this.cmMappingData.filter((e) => !!e.crmFieldName);
      const submitData = {
        ...this.formData,
        campaignMemberMap: _cmMappingData,
        tagNameList: this.formData.tagNameList,
        siteVisitTags: this.siteVisitTags,
        memberToFormMapping: this.memberToFormMapping,
      };
      
      if (this.formMoreSetting.saveCrmObjectType === 0) {
        submitData.crmApiName = 'LeadsObj';
        submitData.crmFormFieldMap = this.crmData.enrollLeadsFieldMappings;
        submitData.crmPoolId = this.crmData.crmLeadPoolId;
        submitData.crmRecordType = this.crmData.enrollLeadsTargetObjectRecordType;
      }

      if (this.formMoreSetting.saveCrmObjectType === 1) {
        submitData.crmApiName = this.customObject;
        submitData.crmFormFieldMap = this.customObjectMappingData;
        submitData.crmRecordType = this.customCrmRecordType;
      }
      // 处理defaultValue  只有自定义映射才会有defaultValue
      (submitData.crmFormFieldMap || []).forEach(item=>{
        if(item.crmFieldName && item.mankeepFieldName && item.defaultValue){
          delete item.defaultValue
        }
      })

      return submitData;
    },
  },
};
</script>

<style lang="less" scoped>
.w-other-info {
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 0;
  position: relative;
  .w-setting-tips {
    display: flex;
    align-items: center;
    padding: 0 20px;
    height: 33px;
    font-size: 12px;
    color: #999999;
    background: #f4f4f4;
    margin-bottom: 10px;
    margin-top: -45px;
    .icon {
      flex: 0 0 auto;
      margin-right: 10px;
      height: 20px;
      width: 20px;
      background-image: url('../../../assets/images/icons/icon_tips.png');
      background-position: center center;
      background-size: contain;
    }
  }
  .w-setting {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    padding: 20px 0;
    border-bottom: 1px dashed #e9edf5;
    &:last-of-type {
      border-bottom: 0;
    }
    .setting-area__name {
      width: 140px;
      color: var(--Text-H2, #545861);
      flex-shrink: 0;
      line-height: 20px;
    }
    .setting-area__info {
      .w-checkbox-group__leadcharger {
        margin: 32px 0;
      }

      .open-btn {
        color: #407fff;
        cursor: pointer;
        position: absolute;
        top: -4px;
        right: 210px;
      }
      .radio-group {
        .radio {
          display: block;
          margin-bottom: 10px;
        }
      }
      .checkbox-group {
        /deep/ .el-checkbox__label {
          font-size: 13px;
        }
      }
      .radio-group {
        display: flex;
        flex-direction: column;

        /deep/ .el-radio {
          margin-bottom: 10px;
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
      .cbg-lc__tip {
        color: #91959e;
        font-size: 13px;
        margin-left: 24px;
      }
      .info__enrollLimit {
        display: flex;
        align-items: center;
        gap: 12px;
        flex-wrap: wrap;
        .enrollLimit__num {
          width: 88px;
        }
        .enrollLimit__text {
          width: 320px;
        }
      }

      .custom-object-select {
        margin: 10px 0;
      }
    }
    .setting-area__wrapper {
      display: flex;
      flex-wrap: wrap;
      font-size: 13px;
      color: #333333;
      & > div {
        margin-right: 20px;
      }
    }
  }
}
</style>
