<template>
  <el-dialog
    v-if="visible"
    class="site-setting-dialog"
    top="10vh"
    width="982px"
    :title="title"
    append-to-body
    :visible="visible"
    @update:visible="handleCloseDialog"
  >
    <div class="dialog-body-wrapper">
      <dialog-content
        ref="$content"
        :form-id="formId"
        :site-id="siteId"
        :site-setting-type="siteSettingType"
        :form-usage="formUsage"
        :is-apply-object="isApplyObject"
        :show-activity-member-set="showActivityMemberSet"
      />
    </div>
    <div slot="footer">
      <fx-button
        size="small"
        type="primary"
        @click="handleSubmit"
      >
        {{ $t('marketing.commons.qd_aa7527') }}
      </fx-button>
      <fx-button size="small" @click="handleCloseDialog">
        {{ $t('marketing.commons.qx_c08ab9') }}
      </fx-button>
    </div>
  </el-dialog>
</template>
<script>
import http from '@/services/http/index.js'
// import { getTopZIndex } from '@/utils/helper.js'

import content from './content.vue'

export default {
  components: {
    elDialog: FxUI.Dialog,
    dialogContent: content,
  },
  props: {
    title: {
      type: String,
      default: $t('marketing.pages.site.wymsz_e022c5'),
    },
    visible: {
      type: Boolean,
      default: false,
    },
    formId: {
      type: String,
      default: '',
    },
    siteId: {
      type: String,
      default: '',
    },
    formUsage: {
      type: Number,
      default: 1,
    },
    showActivityMemberSet: {
      type: Boolean,
      default: false,
    },
    // 微页面用途  1、会议报名设置 2、活动推广设置 3、表单设置
    siteSettingType: {
      type: Number,
      default: 0,
    },
    // 推广内容relationId
    relationId: {
      type: String,
      default: '',
    },
    isApplyObject: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {}
  },
  computed: {
    conferenceDetail() {
      return this.$store.state.MeetingMarketing.conferenceDetail
    },
  },
  methods: {
    handleSubmit() {
      if (!this.$refs.$content.validate()) {
        FxUI.Message.warning($t('marketing.pages.site.qjcxssz_df536d'))
        return
      }

      const promiseList = []
      const defaultPromise = Promise.resolve({ errCode: 0 })
      const submitData = this.$refs.$content.getSubmitData()
      if ((this.siteSettingType === 1 || this.siteSettingType === 2) && this.relationId) {
        const signUpContentData = {
          id: this.relationId,
          isApplyObject: submitData.isApplyObject,
        }
        const submitSignUpContentPromise = http.updateIsApplyObject(signUpContentData)
        promiseList.push(submitSignUpContentPromise)
      } else {
        promiseList.push(Promise.resolve({ errCode: 0 }))
      }
      delete submitData.isApplyObject
      if (this.formId) {
        submitData.isSetting = true
        const submitForm = http.updateFormDataDetail(submitData)

        promiseList.push(submitForm)
      } else {
        promiseList.push(defaultPromise)
      }

      // 会议主页比较特殊需要单独接口处理  不要问为什么  我也不知道
      if (this.siteId) {
        const submitSite = this.siteSettingType === 1 ? http.updateConferenceTag( {
          conferenceId: this.conferenceDetail.id,
          hexagonId: this.conferenceDetail.activityDetailSiteId,
          browseTagNameList: submitData.siteVisitTags,
          enrollTagNameList: submitData.tagNameList
        }) : http
          .addHexagonVisitTag({
            hexagonSiteId: this.siteId,
            tagNameList: submitData.siteVisitTags || [],
          })

        promiseList.push(submitSite)
      } else {
        promiseList.push(defaultPromise)
      }

      Promise.all(promiseList)
        .then(([submitSignUpContentResp,submitFormResp, submitSiteResp]) => {
          const { errCode: errCode1, errMsg: errMsg1 } = submitFormResp
          const { errCode: errCode2, errMsg: errMsg2 } = submitSiteResp
          const { errCode: errCode3, errMsg: errMsg3 } = submitSignUpContentResp

          if (errCode1 === 0 && errCode2 === 0 && errCode3 === 0) {
            FxUI.Message.success($t('marketing.commons.bccg_3b1083'))
          }

          if (errCode1 !== 0) {
            FxUI.Message.error(errMsg1)
          }

          if (errCode2 !== 0) {
            FxUI.Message.error(errMsg2)
          }

          if (errCode3 !== 0) {
            FxUI.Message.error(errMsg3)
          }

          this.$emit('update:submit')
          this.handleCloseDialog()
        })
    },
    handleCloseDialog() {
      this.$emit('update:visible', false)
    },
  },
}
</script>
<style lang="less" scoped>
.site-setting-dialog {
  /deep/.el-dialog__body {
    padding: 0;
    height: 70vh;
    overflow: hidden;
    overflow-y: auto;
  }

  .dialog-body-wrapper {
    // background: #fafafa;
    padding: 0 24px;
    margin: 0;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
