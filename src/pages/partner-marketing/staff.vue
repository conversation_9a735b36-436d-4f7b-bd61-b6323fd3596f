<template>
  <v-scaffold
    :title="$t('marketing.commons.xjhbtg_f454e7')"
    @send="handleSend"
    :btnState="btnState"
    @cancel="handleCancel"
    v-loading="loading"
  >
    <div :class="[$style.staff_activity_wrapper, $style[displayMode]]">
      <el-form
        :model="staffForm"
        :rules="rules"
        ref="staffForm"
        label-width="130px"
        label-position="left"
        :class="$style.staffForm"
      >
        <el-form-item
          :label="$t('marketing.commons.schd_a8559e')"
          prop="marketingEventId"
          v-if="vDatas.status.crmark"
        >
          <v-pick-selfobject
            :disabled="
              !!staffForm.marketingEventId.id && disabled_marketingEventId
            "
            v-model="staffForm.marketingEventId"
          ></v-pick-selfobject>
        </el-form-item>
        <el-form-item :label="$t('marketing.commons.xztgnr_57e5fd')" prop="content" ref="article">
          <div :class="$style.article">
            <v-materials
              ref="materials"
              v-model="staffForm.content"
              :displayMode="displayMode"
              :presetMaterial="material"
              :marketingEventId="staffForm.marketingEventId.id"
              :multiMaterialsData="multiMaterialsData"
              @input="materialsInputChange"
              :ispartner="true"
            />
          </div>
          <fx-checkbox v-model="staffForm.staffInfoShow">
            <span>{{ $t('marketing.pages.partner_marketing.tgtphhbzdx_2d64c4') }}</span>
            <fx-button type="text" @click="posterPreviewDialog = true">{{ $t('marketing.commons.cksl_95dbbe') }}</fx-button>
          </fx-checkbox>
        </el-form-item>
        <el-form-item :label="$t('marketing.commons.tgrwbt_971653')" prop="title">
          <fx-input
            v-model="staffForm.title"
            :placeholder="$t('marketing.commons.qsr_02cc4f')"
            :maxlength="100"
          ></fx-input>
          <span :class="$style.count" style="top:0;color:#c1c5ce;"
            >{{ staffForm.title.length }}/100</span
          >
        </el-form-item>
        <el-form-item :label="$t('marketing.commons.xcy_ff27ff')" prop="description">
          <AiSlogan
            v-model="staffForm.description"
            :placeholder="$t('marketing.pages.partner_marketing.djqyygdwtg_91dcd7')"
            :maxlength="255"
            :slogan-material="sloganMaterial"
          />
        </el-form-item>
        <el-form-item :label="$t('marketing.commons.fmt_641cfa')" prop="coverPath">
          <v-pick-cover
            v-model="staffForm.coverPath"
            :defaultImageUrl="defaultImageUrl"
            outputPathType="a"
            @update:cut="showImage"
          ></v-pick-cover>
          <div :class="$style.cover__tips">
            {{ $t('marketing.commons.fmjycc_ae76b8') }}
          </div>
        </el-form-item>
        <el-form-item :label="dateLable" prop="date">
          <picker-time v-model="staffForm.date" value-format="timestamp" />
        </el-form-item>
        <el-form-item :label="$t('marketing.commons.fsfw_c8ff46')" prop="partnerNoticeVisibilityArg">
          <div class="select-bar"></div>
        </el-form-item>
        <el-form-item :label="$t('marketing.commons.fssj_f6cbc9')" prop="sendConfig">
          <sendtime-pker v-model="staffForm.sendConfig"></sendtime-pker>
        </el-form-item>
      </el-form>
    </div>
    <Dialog
      :title="$t('marketing.commons.yl_645dbc')"
      width="420px"
      :cancel-text="$t('marketing.commons.wzdl_fe0337')"
      :show-confirm="false"
      :visible="posterPreviewDialog"
      @onClose="posterPreviewDialog = false"
    >
      <div style="display: flex; justify-content: center;">
        <img src="@/assets/images/partner-spread-preview.png" style="width: 240px" alt="" >
      </div>
    </Dialog>
  </v-scaffold>
</template>

<script>

import { mapState, mapActions } from "vuex";
import { requireAsync, watchFormValidateFieldHandler } from "@/utils/index";
import http from "@/services/http/index";
import VHeader from "@/pages/promotion-activity/common/header";
import VScaffold from "@/pages/promotion-activity/common/scaffold";
import VPickSelfobject from "@/pages/promotion-activity/common/pick-selfobject";
import sendtimePker from "@/pages/promotion-activity/common/sendtime-pker.vue";
import VMaterials from "@/pages/promotion-activity/staff/materials.vue";
import PickerTime from "@/pages/promotion-activity/common/picker-time";
// import VPickCover from '../common/pick-cover';
import VPickCover from "@/components/picture-cutter";
import kisvData from "@/modules/kisv-data";
import { addressBookResult2Str, sendLinkVariableReplace } from "@/utils/tranformUtil";
import Dialog from "@/components/dialog/index.vue"
import AiSlogan from '@/pages/promotion-activity/common/ai-slogan.vue'
import { OToCTypeMaps } from '@/components/select-material-dialog/material.js'

let _enterFrom = "";

export default {
  components: {
    VHeader,
    VScaffold,
    VPickSelfobject,
    ElSelect: FxUI.Select.components.ElSelect,
    ElOption: FxUI.Select.components.ElSelect.components.ElOption,
    sendtimePker,
    ElForm: FxUI.Form,
    ElFormItem: FxUI.FormItem,
    VMaterials,
    PickerTime,
    VPickCover,
    Dialog,
    AiSlogan,
  },
  beforeRouteEnter(to, from, next) {
    _enterFrom = from.name;
    next();
  },
  props: {
    material: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  data() {
    return {
      posterPreviewDialog: false,
      loading:false,
      displayMode: "scaffold", // 或者是dialog
      disabled_marketingEventId: false,
      notSendAgain: true,
      defaultImageUrl: "",
      vDatas: kisvData.datas,
      dateLable: $t('marketing.commons.tgsj_0845c3'),
      btnState: [],
      staffForm: {
        title: "",
        content: {},
        staffInfoShow: true,
        coverPath: "",
        description: "",
        date: [],
        marketingEventId: { id: this.$route.query.id },
        partnerNoticeVisibilityArg: {},
        executor: {},
        sendConfig: {
          type: 1
        }
      },
      spreadMode: 1, // 1: 单物料 2: 多物料
      multiMaterialsData: [],
      multiObjectId: '',
      multiObjectType: '',
      rules: {
        marketingEventId: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (kisvData.datas.status.crmark && (!value || !value.id)) {
                callback(new Error($t('marketing.commons.qxzschd_9e5f1b')));
              }
              callback();
            }
          }
        ],
        title: [
          {
            required: true,
            message: $t('marketing.commons.qsrtgrwbt_c1caba'),
            trigger: "change"
          }
        ],
        coverPath: [
          {
            required: true,
            message: $t('marketing.commons.qxzfmtp_5b90d1'),
            trigger: "change"
          }
        ],
        content: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (_.isEmpty(value)) {
                callback(new Error($t('marketing.commons.qxztgnr_4da728')));
              }
              callback();
            },
            trigger: "blur"
          }
        ],
        partnerNoticeVisibilityArg: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (
                !(
                  (value.eaList && value.eaList.length) ||
                  (value.tenantGroupIdList && value.tenantGroupIdList.length)
                )
              ) {
                callback(new Error($t('marketing.commons.qxzfsfw_6d3514')));
              }
              callback();
            },
            trigger: "blur"
          }
        ],
        description: [
          {
            required: true,
            message: $t('marketing.commons.qsrxcy_c3d431'),
            trigger: "change"
          }
        ],
        date: [
          {
            required: true,
            validator: (rule, value, callback) => {
              const [startTime, endTime] = value;
              if (!startTime) {
                callback(new Error($t('marketing.commons.qxztgkssj_547e46')));
              } else if (!endTime) {
                callback(new Error($t('marketing.commons.qxztgjssj_23ba76')));
              }
              callback();
            },
            trigger: "change"
          }
        ],
        sendConfig: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (value && value.type == 2 && !value.time) {
                callback(new Error($t('marketing.commons.qxzdsfssj_88824a')));
              }
              callback();
            },
            trigger: "change"
          }
        ]
      }
    };
  },
  computed: {
    sloganMaterial() {
      const { name = '' } = this.staffForm.marketingEventId || {}
      const { id, type } = this.staffForm.content || {}
      if (this.spreadMode === 1) {
        const entry = Object.entries(OToCTypeMaps).find(([key, value]) => value === type)
        if (entry) {
          return {
            objectId: id,
            objectType: parseInt(entry[0], 10),
            campaignName: name,
          }
        }

        return {
          objectId: id,
          objectType: type,
          campaignName: name,
        }
      }

      if (this.spreadMode === 2 && this.multiMaterialsData.length) {
        return {
          materialInfos: this.multiMaterialsData.map(item => item.coverPath),
          campaignName: name,
        }
      }

      return null
    },
  },
  watch: {
    ...watchFormValidateFieldHandler(
      [
        {
          watchField: "staffForm.content",
          validateField: "content"
        },
        {
          watchField: "staffForm.partnerNoticeVisibilityArg",
          validateField: "partnerNoticeVisibilityArg"
        }
      ],
      "staffForm"
    ),
    staffForm: {
      deep: true,
      handler(newVal, oldVal) {
        console.log(newVal);
      }
    },
    vDatas: {
      deep: true,
      handler(newVal) {}
    }
  },
  methods: {
    ...mapActions("PromotionActivity", ["fakeAddMarketingActivity"]),
    materialsInputChange(value, mode) {
      // 重置选项
      if (!value) {
        this.showContentTips = false;
        this.defaultImageUrl = '';
        this.staffForm.coverPath = '';
        this.multiObjectId = '';
        this.multiObjectType = '';
        this.multiMaterialsData = [];

        return;
      }
      
      this.spreadMode = mode || 1;
      if(mode === 1) {
        if (value.qrPostForwardType == 10) {
          this.showContentTips = true;
        } else {
          this.showContentTips = false;
        }
        if (value.data.image && value.data.aPath) {
          this.defaultImageUrl = value.data.image;
          this.staffForm.coverPath = value.data.aPath;
        }
      } else if(mode === 2) {
        if(value && value.length > 0) {
          this.multiObjectId = value[0].id || value[0].objectId;
          this.multiObjectType = value[0].contentType === 24 ? 5 : value[0].contentType;
          this.defaultImageUrl = value[0].image || value[0].coverUrl;
          this.staffForm.coverPath = value[0].image || value[0].coverPath;
          this.multiMaterialsData = value.map(item => {
            if (item.objectId) {
              return item;
            }
            
            return {
              objectId: item.id,
              contentType: item.contentType === 24 ? 5 : item.contentType,
              coverPath: item.aPath,
              coverUrl: item.image,
            };
          });
        }
        this.showContentTips = false;
      }
    },
    formatParams() {
      const {
        title,
        content,
        description,
        date,
        marketingEventId,
        partnerNoticeVisibilityArg = {},
        executor = {},
        staffInfoShow,
        sendConfig,
        coverPath
      } = this.staffForm;
      // 海报在选择素材组件的type是'24'，但全员推广是5
      content.type == 24 && (content.type = 5);
      // 微页面在选择素材组件的type是'26'，但全员推广是10
      content.type == 26 && (content.type = 10);
      const params = {
        spreadType: 7,
        ...(marketingEventId && marketingEventId.id
          ? { marketingEventId: marketingEventId.id }
          : {}),
        marketingActivityAuditData: {
          executor: addressBookResult2Str(executor),
          sendLink: sendLinkVariableReplace(content?.data?.url)
        },
        marketingActivityPartnerNoticeSendVO: {
          title,
          staffInfoShow: staffInfoShow ? 1 : 0,
          ...(this.spreadMode === 1 && {
            ...(content && content.id ? { content: content.id } : {}),
            ...(content && content.activityDetailSiteId ? { activityDetailSiteId: content.activityDetailSiteId } : {}),
            ...(content && content.type ? { contentType: content.type } : {}),
          }),
          ...(this.spreadMode === 2 && {
            content: this.multiObjectId ? this.multiObjectId : '',
            contentType: this.multiObjectType ? this.multiObjectType : '',
            materialInfoList: this.multiMaterialsData ? this.multiMaterialsData : []
          }),
          ...http.$.kk({ type: "sendType", time: "timingDate" }, sendConfig),
          ...(date ? { startTime: date[0] } : {}),
          ...(date ? { endTime: date[1] } : {}),
          ...(description ? { description } : {}),
          partnerNoticeVisibilityArg,
          coverPath
        },
        //绑定素材到市场活动
        ...(this.spreadMode === 1 && {
          materialInfos: [
            {
              objectId: content.id,
              contentType: content.type
            }
          ]
        }),
        ...(this.spreadMode === 2 && {
          materialInfos: this.multiMaterialsData ? this.multiMaterialsData.map(item => ({
            objectId: item.objectId,
            contentType: item.contentType
          })) : []
        })
      };
      //递归删除空字符串
      function eachObject(obj) {
        Object.keys(obj).forEach(key => {
          if (obj[key] instanceof Object) {
            eachObject(obj[key]);
          } else {
            if (!obj[key]) {
              delete obj[key];
            }
          }
        });
      }
      eachObject(params);
      return params;
    },
    handleSend(callback) {
      this.$refs["staffForm"].validate(valid => {
        callback && callback(valid);
        if (valid) {
          // return;
          console.log(this.staffForm);
          this.btnState = [1, 0, 0];
          console.log("this.formatParams()", this.formatParams());
          this.fakeAddMarketingActivity(this.formatParams())
            .then(({ errCode }) => {
              if (errCode == 0) {
                this.handleComplete();
              }
            })
            .catch(() => {})
            .then(() => {
              this.btnState = [];
            });
        } else {
          return false;
        }
      });
    },
    handleComplete() {
      if (this.displayMode === "dialog") {
        this.$emit("submited");
      } else {
        // 全员推广有多个来源路由，点击取消时，都是 $router.back() 直接后退到来源页面
        // 点击确定时，视不同来源，跳往不同路由
        // 首页，跳推广列表
        // 活动、产品、文章新建成功后，跳全员营销
        // 全员营销、推广列表，直接退回源页面
        if (_enterFrom === "home") {
          this.$router.replace({ name: "promotion-activity" });
        } else if (_enterFrom === "materiel-create-success") {
          this.$router.replace({ name: "promotion" });
        } else {
          this.$router.back();
        }
      }
    },
    handleCancel() {
      this.$router.back();
    },
    queryMarketingActivityDetail() {
      this.loading = true
      http
        .queryMarketingActivityDetail({
          id: this.$route.query.marketingActivityId
        })
        .then(res => {
          this.loading = false
          if (res && res.errCode == 0) {
            //再次发送除发送时间外数据回填
            this.staffForm.marketingEventId = { id: res.data.marketingEventId };
            let result = res.data.marketingActivityPartnerNoticeSendVO;
            let {title, staffInfoShow, description, partnerNoticeVisibilityArg} = result
            this.staffForm.title = title;
            this.staffForm.staffInfoShow = !!staffInfoShow
            this.staffForm.description = description;
            this.initIcselector(partnerNoticeVisibilityArg);
            this.staffForm.partnerNoticeVisibilityArg = partnerNoticeVisibilityArg;

            if(result.materialInfoList && result.materialInfoList.length > 0) {
              this.spreadMode = 2;
              this.multiObjectId = result.content;
              this.multiObjectType = result.contentType;
              this.defaultImageUrl = result.materialInfoList[0].coverUrl;
              this.staffForm.coverPath = result.materialInfoList[0].coverPath;
              this.staffForm.content = result.content;
              this.staffForm.contentType = result.contentType;
              this.staffForm.materialInfoList = result.materialInfoList;
              this.multiMaterialsData = result.materialInfoList;
            } else {
              this.spreadMode = 1;
              this.$refs.materials.item = {
                photoUrl: result.contentDetail.image,
                summaryLine1: result.title,
                id: result.content,
                type: result.contentType
              };
              this.defaultImageUrl = res.data.coverUrl;
              this.staffForm.coverPath = res.data.coverApath;
              this.staffForm.content = this.$refs.materials.item;
            }
          }
        });
    },
    presetTime() {
      this.staffForm.date = [
        +new Date(),
        +new Date() + 3 * 24 * 60 * 60 * 1000
      ];
    },
    presetContent({ conferenceId, materiel }) {
      //会议营销特殊处理
      if (conferenceId) {
        http
          .queryConferenceDetail({ id: conferenceId }) // this.$route.params.conferenceID
          .then(res => {
            if (res && res.errCode == 0) {
              let conferenceDetail = res.data;
              this.staffForm.title = $t('marketing.commons.yqkhcj_60bad6', {data: ( {'option0': conferenceDetail.title})});
              let date = new Date();
              this.staffForm.date = [
                new Date().getTime(),
                date.setDate(date.getDate() + 3)
              ];
              this.dateLable = $t('marketing.commons.rwsj_b341f9');
              if (
                conferenceDetail.flowStatus == 0 ||
                conferenceDetail.flowStatus == 1
              )
                return;
              (this.$refs.materials.priValue = {
                type: 1,
                id: conferenceDetail.marketingEventId
              }),
                (this.$refs.materials.item = {
                  photoUrl: conferenceDetail.coverImageThumbUrl,
                  summaryLine1: conferenceDetail.title,
                  summaryLine2: conferenceDetail.location,
                  id: conferenceDetail.id,
                  type: "3",
                  activityDetailSiteId: conferenceDetail.activityDetailSiteId
                });
              this.staffForm.content = this.$refs.materials.item;
            }
          });
      }
      if (materiel) {
        if (materiel.materielType == "article") {
          http
            .queryArticleDetail({ articleId: materiel.materielId })
            .then(res => {
              let data = res.data;
              this.$refs.materials.item = {
                photoUrl: data.photoUrl,
                summaryLine1: data.title,
                summaryLine2: data.summary,
                id: data.id,
                type: 1
              };
              this.staffForm.content = this.$refs.materials.item;
            });
        } else if (materiel.materielType == "product") {
          http.queryProductDetail({ id: materiel.materielId }).then(res => {
            let data = res.data || {};
            this.$refs.materials.item = {
              photoUrl: data.sharePicOrdinaryCutUrl || data.headPicsThumbs[0],
              summaryLine1: data.name,
              summaryLine2: data.summary,
              id: data.id,
              type: 4
            };
            this.staffForm.content = this.$refs.materials.item;
          });
        }
      }
    },
    showImage(params) {
      console.log(params);
      this.staffForm.coverPath = params.tapath;
      this.defaultImageUrl = params.url;
      this.$refs["staffForm"].validateField("coverPath");
    },
    initIcselector(partnerNoticeVisibilityArg) {
      //http://git.firstshare.cn/fe/icmanage/-/blob/master/icmanage/modules/icselector/demo.js
      requireAsync("icmanage-modules/icselector/icselector", mod => {
        let Icselector = Vue.extend(mod.icSelector);
        const SelectWarp = new Icselector({
          propsData: {
            propsLine: { label: $t('marketing.commons.xzfsfw_779f6a') },
            propsBox: { tabs: ["outerTenantIds", "outerTgroupIds"] },
            mode: "box",
            icsOptions: {
              sourceType: 0 // 0 获取当前企业的下游数据， 1 获取当前企业的上游数据，缺少键名则不区分上下游
            },
            selected: { 						// 已勾选项
              outerTenantIds: (partnerNoticeVisibilityArg && partnerNoticeVisibilityArg.eaList) || [],
              outerTgroupIds: (partnerNoticeVisibilityArg && partnerNoticeVisibilityArg.tenantGroupIdList) || [],
            },
            //控制全选按钮
            isAllFlags: [4],

          }
        }).$mount(".select-bar");
        console.log('SelectWarpSelectWarp',SelectWarp)

        SelectWarp.$on("change", value => {
          let {outerTenantIds=[],outerTgroupIds=[]} = value
          this.staffForm.partnerNoticeVisibilityArg = {
            eaList:outerTenantIds,
            tenantGroupIdList:outerTgroupIds
          }
          this.staffForm.executor = SelectWarp.getSelectedItems();
          console.log('partnerNoticeVisibilityArg==',value, this.staffForm.executor);
        });
      });
    }
  },
  created() {

  },
  mounted() {
    this.presetTime();
    if (this.displayMode === "scaffold") {
      this.presetContent({
        conferenceId: this.$route.params.conferenceID,
        materiel: this.$route.params.materiel
      });
    } else if (this.displayMode === "dialog") {
      this.staffForm.marketingEventId = { id: this.defaultMarketingEventId };
    }
    //再次发送
    if (this.$route.query.marketingActivityId) {
      this.queryMarketingActivityDetail();
    }else{
      this.initIcselector();
    }
  }
};
</script>

<style lang="less" module>
.staff_activity_wrapper {
  margin-left: 60px;
  padding: 20px 0;
  width: 860px;
  box-sizing: border-box;
  &.dialog {
    margin-left: 0;
    width: auto;
    padding: 20px;
  }
  .selectRange {
    border: 1px solid #e9edf5;
    height: 36px;
    line-height: 1;
    border-radius: 4px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    :global {
      .selectbar-input {
        border: 0;
      }
    }
  }
  :global {
    .el-form-item__label {
      font-size: 13px;
      color: #151515;
    }
    .el-input.el-date-editor {
      width: 346px;
    }
  }
  .article {
    // height: 90px;
    background: #fcfcfc;
    // border: 1px dashed #dddddd;
  }
  .count {
    font-size: 13px;
    color: #c1c5ce;
    position: absolute;
    right: 5px;
    top: 45px;
  }
  .tips {
    color: #c1c5ce;
    font-size: 12px;
    margin-left: 9px;
    margin-top: -7px;
    display: flex;
    height: 22px;
  }
  .cover__tips {
    font-size: 12px;
    color: #91959e;
    line-height: 18px;
    margin-top: 12px;
  }
}
</style>
