<!-- 组件说明 -->
<template>
  <div class="website-manage__wrapper">
    <!-- 统计未做，在开启插件情况下才显示-->
    <div
      v-if="vDatas.uinfo['showCustomerServiceEnabled'] || vDatas.uinfo['marketingCustomerIntegration']"
      class="website-manage_cart"
    >
      <div class="top">
        {{ $t('marketing.pages.website_access.hqzxkhzx_a7c099') }}/{{ $t('marketing.pages.website_access.lyxs_26a74b') }}
      </div>
      <div class="bottom">
        <div class="plugin">
          <div class="name">
            <span>{{ $t('marketing.pages.website_access.zxkf_7f72e8') }}</span>
            <fx-button
              type="text"
              @click="openLook"
            >
              {{ $t('marketing.pages.website_access.pz_224e2c') }}
            </fx-button>
          </div>
          <div>
            <span class="clue">{{ $t('marketing.commons.hqxs_a65930') }}</span>
            <span class="num">{{ clueNum }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="website-manage_cart">
      <div class="top">
        <div class="top-name">
          {{ $t('marketing.pages.website_access.gwhqxs_1b47b8') }}
        </div>
        <fx-button
          type="primary"
          size="small"
          @click="createadvertiseContent"
        >
          <i class="yxt-icon16 icon-add" />
          <span>{{ $t('marketing.pages.website_access.tjxsbd_ded369') }}</span>
        </fx-button>
      </div>
      <div v-show="formDataInfoList && formDataInfoList.length">
        <v-table
          :columns="formColumns"
          :empty-text="$t('marketing.commons.zwsj_21efd8')"
          :data="formDataInfoList"
          @custom:clue-action="showClue"
        >
          <template slot-scope="scope">
            <template v-if="scope.col.prop === 'customOperation'">
              <span
                class="operation-btn"
                @click="insertForm(scope.row)"
              >{{ $t('marketing.commons.qrgw_a24b37') }}</span>
              <span
                class="operation-btn"
                @click="editForm(scope.row)"
              >{{ $t('marketing.commons.bj_95b351') }}</span>
              <span
                class="operation-btn"
                style="display:inline-block;width:43px;"
                @click="siteSetting(scope.row)"
              >{{ $t('marketing.commons.sz_e366cc') }}

                <Tooltip
                  v-if="!scope.row.hadCrmMapping"
                  effect="dark"
                  :content="$t('marketing.commons.hwwcxsszzs_4424ca')"
                  placement="top"
                >
                  <i :class="['el-icon-warning-outline', 'warning']" />
                </Tooltip>
              </span>
              <span
                class="operation-btn"
                @click="deleteForm(scope.row)"
              >{{ $t('marketing.commons.sc_2f4aad') }}</span>
            </template>
          </template>
        </v-table>
        <v-pagen
          class="pagen"
          :pagedata.sync="pageData"
          @change="handlePageChange"
        />
      </div>
      <div
        v-show="!formDataInfoList || !formDataInfoList.length"
        class="empty"
      >
        <img
          class="image"
          src="@/assets/images/website-empty.png"
        >
        <p class="text">
          {{ $t('marketing.pages.website_access.mfzcsybmyy_891272') }}
          {{ $t('marketing.pages.website_access.zgwsfzxshq_971538') }}
        </p>
        <div
          class="add"
          @click="createadvertiseContent"
        >
          <i class="yxt-icon16 icon-add-b" />{{ $t('marketing.pages.website_access.mstjxshqbd_f4f130') }}
        </div>
      </div>
    </div>
    <div class="website-manage_cart">
      <div class="top">
        <div class="top-name">
          {{ $t('marketing.pages.website_access.glzj_a6d790') }}
        </div>
        <fx-button
          type="primary"
          size="small"
          @click="handleAddCta"
        >
          <i class="yxt-icon16 icon-add" />
          <span>{{ $t('marketing.pages.website_access.tjzj_e01e61') }}</span>
        </fx-button>
      </div>
      <div v-show="ctaDataList && ctaDataList.length">
        <v-table
          :columns="ctaColumns"
          :empty-text="$t('marketing.commons.zwsj_21efd8')"
          :data="ctaDataList"
          @custom:clue-action="showClue"
          @custom:cta-action="showCtaDetail"
        >
          <template slot-scope="scope">
            <template v-if="scope.col.prop === 'siteName'">
              <span v-if="scope.row.siteName">
                <span>{{ $t('marketing.commons.tjbd_4e3400') }}</span>
                <span>【{{ scope.row.siteName }}】</span>
              </span>
              <span v-else>--</span>
            </template>
            <template v-if="scope.col.prop === 'customOperation'">
              <span
                class="operation-btn"
                @click="insertCta(scope.row)"
              >{{ $t('marketing.commons.qrgw_a24b37') }}</span>
              <span
                class="operation-btn"
                @click="editCta(scope.row)"
              >{{ $t('marketing.commons.bj_95b351') }}</span>
              <span
                class="operation-btn"
                @click="deleteCta(scope.row)"
              >{{ $t('marketing.commons.sc_2f4aad') }}</span>
            </template>
          </template>
        </v-table>
        <v-pagen
          class="pagen"
          :pagedata.sync="ctaPageData"
          @change="handleCtaPageChange"
        />
      </div>
      <div
        v-show="!ctaDataList || !ctaDataList.length"
        class="empty"
      >
        <img
          class="image"
          src="@/assets/images/website-empty.png"
        >
        <p class="text">
          {{ $t('marketing.pages.website_access.mfzcsybmyy_891272') }}
          {{ $t('marketing.pages.website_access.zgwsfzxshq_971538') }}
        </p>
        <div
          class="add"
          @click="handleAddCta"
        >
          <i class="yxt-icon16 icon-add-b" />{{ $t('marketing.pages.website_access.tjzj_e01e61') }}
        </div>
      </div>
    </div>
    <div class="website-manage_cart">
      <div class="top">
        <div class="top-name">
          {{ $t('marketing.pages.website_access.gwgzym_52400e') }}
          <QuestionTooltip class="question">
            <div
              slot="question-content"
              style="max-width: 212px; color: #545861"
            >
              <div class="question-tips">
                {{ $t('marketing.pages.website_access.dqgwxkbcds_edc293') }}
              </div>
            </div>
          </QuestionTooltip>
        </div>
        <div class="top-btns">
          <Input
            v-model="websiteSearchKeyWord"
            class="search-input"
            :placeholder="$t('marketing.commons.qsrgjc_cd11be')"
            prefix-icon="el-icon-search"
            size="small"
            @change="handleWebsiteSearch"
          />
          <fx-button
            type="primary"
            size="small"
            @click="addwebsitePage"
          >
            <i class="yxt-icon16 icon-add" />
            <span>{{ $t('marketing.commons.tjgzym_8d9a37') }}</span>
          </fx-button>
          <fx-button
            type="text"
            size="small"
            @click="handleBatchImportWebsitePage"
          >
            {{ $t('marketing.pages.website_access.pldr_407f3d') }}
          </fx-button>
          <fx-button
            type="text"
            size="small"
            @click="handleExportWebsiteTrackData"
          >
            {{ $t('marketing.commons.dc_55405e') }}
          </fx-button>
        </div>
      </div>
      <div v-show="trackWebsiteList.length">
        <v-table
          :columns="columns"
          :empty-text="$t('marketing.commons.zwsj_21efd8')"
          :data="trackWebsiteList"
          class="track-website-table"
          @custom:delete-action="handleTrackDelete"
          @custom:edit-action="handleTrackEdit"
        />
        <v-pagen
          :pagedata.sync="trackPageData"
          @change="handleTrackPageChange"
        />
      </div>
      <div
        v-show="!trackWebsiteList.length"
        class="empty"
      >
        <img
          class="image"
          src="@/assets/images/website-empty.png"
        >
        <p class="text">
          {{ $t('marketing.pages.website_access.zwgwgzym_8eb5ab') }}
        </p>
        <p class="text">
          {{ $t('marketing.pages.website_access.ktjnxygzgz_6d46a2') }}
        </p>
        <div
          class="add"
          @click="addwebsitePage"
        >
          <i class="yxt-icon16 icon-add-b" />{{ $t('marketing.pages.website_access.mstjgzym_b17cce') }}
        </div>
      </div>
    </div>
    <div class="website-manage_cart">
      <event-data :website-id="websiteId" />
    </div>
    <SelectMaterialDialog
      v-if="selectMaterialDialog"
      :tabbar="[10, 16]"
      :title="$t('marketing.commons.xznr_bcbe25')"
      :visible="selectMaterialDialog"
      @onSubmit="handleMaterialSelected"
      @material:add="handleMaterialSelected"
      @onClose="selectMaterialDialog = false"
    />
    <el-dialog
      :title="trackDialogTitle"
      width="600px"
      :visible="trackDialogVisible"
      append-to-body
      class="website-track-dialog"
      @close="closeTrackPageDailog"
    >
      <el-form
        ref="trackPageForm"
        :model="trackPageInfo"
        :rules="trackPageFormRules"
        class="track-page-form-wrapper"
        label-width="100px"
        label-position="left"
      >
        <el-form-item
          :label="$t('marketing.pages.website_access.ymmc_6fb13f')"
          prop="trackPageName"
        >
          <fx-input v-model="trackPageInfo.trackPageName" />
        </el-form-item>
        <el-form-item
          :label="$t('marketing.pages.website_access.ym_26bce2')"
          prop="trackPageURL"
        >
          <fx-input v-model="trackPageInfo.trackPageURL" />
          <div class="pageurl-intro">
            {{ $t('marketing.pages.website_access.tlymtygztp_9e0931') }}
          </div>
          <QuestionTooltip
            class="question"
            :offset="135"
          >
            <div
              slot="question-content"
              style="max-width: 412px; color: #545861"
            >
              <div class="question-tips">
                {{ $t('marketing.pages.website_access.rczdgtlxym_36db1d') }}
                {{ $t('marketing.commons.szhjhdzylx_3e5ae0') }}
                <br>
                <br>{{ $t('marketing.commons.sl_614fc6') }} <br>{{ $t('marketing.commons.fxxkmkhal_d775ac') }}
                https://www.fxiaoke.com/ap/partner-3981.html <br>{{ $t('marketing.commons.tzw_dee124') }}
                <br>{{ $t('marketing.commons.khal_0f31c5') }} https://www.fxiaoke.com/ap/partner-*.html
                <br>{{ $t('marketing.pages.website_access.zhgzqtsydk_476e41') }}
              </div>
            </div>
          </QuestionTooltip>
        </el-form-item>
      </el-form>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <fx-button
          type="primary"
          size="small"
          @click="addTrackPage('trackPageForm')"
        >
          {{ $t('marketing.commons.qd_aa7527') }}
        </fx-button>
        <fx-button
          size="small"
          @click="closeTrackPageDailog"
        >
          {{ $t('marketing.commons.qx_c08ab9') }}
        </fx-button>
      </div>
    </el-dialog>
    <el-dialog
      :title="$t('marketing.commons.qrgw_a24b37')"
      :visible.sync="insertWebsiteVisible"
      width="620px"
      append-to-body
      class="insert-website-dialog"
      @close="closeCopyDialog"
    >
      <p class="title">
        {{ $t('marketing.pages.website_access.nkytgyxfsj_31e4f3') }}<br>
        {{ $t('marketing.pages.website_access.xzbtdqrcjz_ed74c6') }}
        {{ $t('marketing.pages.website_access.xxbzk_a4d3df') }}
        <a
          target="_blank"
          href="https://help.fxiaoke.com/2615/93d4/9188/6407/a4b5"
        >{{ $t('marketing.pages.website_access.ckbzwd_21746a') }}</a>
      </p>
      <div class="url-wrapper">
        <div class="url">
          <div class="options-wrapper">
            <div class="options-item">
              <p class="item-title">
                {{ $t('marketing.pages.website_access.qrys_c8d6d6') }}
              </p>
              <el-select
                v-model="formType"
                class="el-select"
                size="small"
              >
                <el-option
                  :key="0"
                  :label="$t('marketing.pages.website_access.yxtbdys_504c93')"
                  :value="0"
                />
                <el-option
                  :key="1"
                  :label="$t('marketing.pages.website_access.zdybdys_980058')"
                  :value="1"
                />
              </el-select>
            </div>
            <div class="options-item">
              <p class="item-title">
                {{ $t('marketing.pages.website_access.qrcj_966a5a') }}
              </p>
              <el-select
                v-model="sceneType"
                class="el-select"
                size="small"
              >
                <el-option
                  :key="0"
                  :label="$t('marketing.pages.website_access.zzhyqrbd_8843b4')"
                  :value="0"
                />
                <el-option
                  v-show="formType === 0"
                  :key="1"
                  :label="$t('marketing.pages.website_access.djanqrbd_cc8c87')"
                  :value="1"
                />
                <el-option
                  :key="2"
                  :label="$t('marketing.pages.website_access.zxkymqrbd_a4dbc1')"
                  :value="2"
                />
              </el-select>
            </div>
          </div>
          <p
            class="sdk-tip"
          >
            {{ $t('marketing.pages.website_access.nkyfzyxxx_jhasgkhhj') }}
          </p>
          <highlightjs
            v-if="formType === 0"
            :language="[0,2].includes(sceneType)? 'html':'javascript'"
            :code="showCodeText"
            class="iframe"
          />
          <p
            v-else
            class="iframe text-wrapper"
          >
            {{ `${$t('marketing.pages.website_access.dqbdid_ybhnadjkl')}${selectedFormItem.formId || '--'}` }}
            <br>
            {{ `${$t('marketing.pages.website_access.dqcj_3fecde')}${sceneType}` }}
          </p>
          <el-button
            size="small"
            type="primary"
            class="copyiframe"
            :data-clipboard-text="showCodeText"
            @click="copyLink('.copyiframe')"
          >
            {{ $t('marketing.commons.fz_79d3ab') }}
          </el-button>
        </div>
      </div>
    </el-dialog>
    <clue-detail
      v-if="showClueDetail"
      :id="selectedClue.formId"
      ref="clueDetail"
      :object-type="16"
      :visible="true"
      :form-source-type="7"
      :website-id="websiteId"
      @close="handleCloseDetail"
    />
    <site-setting-dialog
      v-if="settingDialogVisible"
      :visible.sync="settingDialogVisible"
      :site-id="curSiteId"
      :form-id="curFormId"
      :form-usage="1"
      @update:submit="handleSettingSubmit"
    />
    <importWebsiteTemplateDialog
      :website-id="websiteId"
      :visible="importWebsiteDialogVisible"
      @update:visible="(val)=>importWebsiteDialogVisible = val"
      @uploadSuccess="queryWebsiteTrackData"
    />
    <CtaDialog v-if="ctaDialogVisible" :visible.sync="ctaDialogVisible"
      @confirm="handleCtaDialogConfirm" />
    <SDKDialog :visible.sync="ctaSdkDialogVisible" :ctaId="targetCtaId" />
  </div>
</template>

<script>
import { mapState } from 'vuex'
import SiteSettingDialog from '@/pages/site/site-setting-dialog/index.vue'
import clueDetail from '@/components/materiel-sideslip-detail/index.vue'
import eventData from './components/event-data.vue'
import SelectMaterialDialog from '@/components/select-material-dialog/index.vue'
import VPagen from '@/components/kitty/pagen.vue'
import VTable from '@/components/table-ex/index.vue'
import QuestionTooltip from '@/components/questionTooltip/index.vue'
import { createMaterialDetailUrlByObjectType } from '@/utils/createMaterialDetailUrl.js'
import importWebsiteTemplateDialog from './components/importWebsiteTemplateDialog.vue'
import CtaDialog from '@/pages/cta/components/cta-dialog.vue';
import SDKDialog from '@/pages/cta/components/sdk-dialog.vue';
import Clipboard from 'clipboard'
import _ from 'lodash'
import http from '@/services/http/index.js'
import util from '@/services/util/index.js'
import kisvData from '@/modules/kisv-data.js'
import { confirm } from '@/utils/globals.js'
import { getTriggerNames, getGuideComponentLists } from '@/pages/cta/util';

export default {
  components: {
    SelectMaterialDialog,
    eventData,
    VTable,
    VPagen,
    ElForm: FxUI.Form,
    ElButton: FxUI.Button,
    ElFormItem: FxUI.FormItem,
    ElDialog: FxUI.Dialog,
    clueDetail,
    QuestionTooltip,
    SiteSettingDialog,
    Tooltip: FxUI.Tooltip,
    ElSelect: FxUI.Select.components.ElSelect,
    ElOption: FxUI.Select.components.ElSelect.components.ElOption,
    Input: FxUI.Input,
    importWebsiteTemplateDialog,
    CtaDialog,
    SDKDialog,
  },
  data() {
    const trackURLValidator = (rule, value, callback) => {
      if (_.trim(value) === '') {
        callback(new Error($t('marketing.pages.website_access.qsrym_303f78')))
      } else {
        callback()
      }
    }
    const trackNameValidator = (rule, value, callback) => {
      if (_.trim(value) === '') {
        callback(new Error($t('marketing.pages.website_access.qsrymmc_7d5cc3')))
      } else {
        callback()
      }
    }
    return {
      vDatas: kisvData.datas,
      sceneType: 0,
      settingDialogVisible: false,
      insertWebsiteVisible: false,
      ctaDialogVisible: false,
      formColumns: [
        {
          prop: 'objectName',
          label: $t('marketing.commons.mc_d7ec2d'),
          minWidth: '120px',
        },
        {
          prop: 'uv',
          label: $t('marketing.pages.website_access.fwrzs_22adf8'),
          minWidth: '120px',
        },
        {
          prop: 'pv',
          label: $t('marketing.pages.website_access.fwzcs_f0fd7d'),
          minWidth: '120px',
        },
        {
          prop: 'clueNum',
          label: $t('marketing.commons.xss_0ac14d'),
          minWidth: '100px',
          exComponent: 'operation',
        },
        {
          prop: 'customOperation',
          label: $t('marketing.commons.cz_2b6bc0'),
          minWidth: '150px',
          exComponent: 'custom',
        },
      ],
      ctaColumns: [
        {
          prop: 'ctaName',
          label: $t('marketing.commons.mc_d7ec2d'),
          minWidth: '120px',
        },
        {
          prop: 'triggerNames',
          label: $t('marketing.commons.cffs_159dbc'),
          minWidth: '120px',
        },
        {
          prop: 'siteName',
          label: $t('marketing.commons.lzzj_989cda'),
          minWidth: '120px',
          exComponent: 'custom',
        },
        {
          prop: 'clueNum',
          label: $t('marketing.commons.xss_0ac14d'),
          minWidth: '100px',
          exComponent: 'operation',
        },
        
        {
          prop: 'customOperation',
          label: $t('marketing.commons.cz_2b6bc0'),
          minWidth: '150px',
          exComponent: 'custom',
        },
        
      ],
      ctaDataList: [],
      trackPageFormRules: {
        trackPageURL: [
          { required: true, validator: trackURLValidator, trigger: 'blur' },
        ],
        trackPageName: [
          { required: true, validator: trackNameValidator, trigger: 'blur' },
        ],
      },
      trackDialogTitle: $t('marketing.commons.tjgzym_8d9a37'),
      formDataInfoList: [],
      trackPageInfo: {
        trackPageName: '',
        trackPageURL: '',
      },
      pageData: {
        layout: 'prev, pager, next, total, sizes, jumper',
        pageNum: 1,
        totalCount: 0,
        pageSize: 5,
        pageSizes: [5, 15, 25, 35],
      },
      ctaPageData: {
        layout: 'prev, pager, next, total, sizes, jumper',
        pageNum: 1,
        totalCount: 0,
        pageSize: 5,
        pageSizes: [5, 15, 25, 35],
      },
      trackPageData: {
        layout: 'prev, pager, next, total, sizes, jumper',
        pageNum: 1,
        totalCount: 0,
        pageSize: 5,
        pageSizes: [5, 15, 25, 35],
      },
      H5Url_form_async: '',
      H5Url_form_iframe_async: '',
      script_insert_code: '',
      // websiteId: "",
      selectMaterialDialog: false, // 选择素材弹窗
      trackDialogVisible: false,
      trackWebsiteList: [],
      showClueDetail: false, // 线索侧滑
      curFormId: '',
      curSiteId: '',
      clueNum: 0,
      websiteSearchKeyWord: '',
      exportLoading: false,
      importWebsiteDialogVisible: false,
      selectedFormItem: {},
      formType: 0,
      websiteInfo: {},
      ctaSdkDialogVisible: false,
      targetCtaId: '',
    }
  },
  computed: {
    ...mapState({
      websiteId: state => state.WebsiteAccess.websiteId,
    }),
    showCodeText() {
      if (this.formType === 1) {
        return $t('undefined', { data: ({ option0: $t('marketing.pages.website_access.dqbdid_ybhnadjkl'), option1: this.selectedFormItem.formId || '--', option2: $t('marketing.pages.website_access.dqcj_3fecde'), option3: this.sceneType }) })
      }
      if (this.sceneType === 0) {
        return this.H5Url_form_iframe_async
      }
      if (this.sceneType === 2) {
        return `<iframe frameborder="0" src="${this.H5Url_form_async}&sceneType=${this.sceneType}" class="marketingFarme" allowFullScreen="true"></iframe>`
      }
      if (this.sceneType === 1) {
        return this.script_insert_code
      }
      return ''
    },
    columns() {
      const c = [
        {
          prop: 'trackName',
          label: $t('marketing.commons.ymmc_b78454'),
          minWidth: '100px',
        },
        {
          prop: 'trackUrl',
          label: $t('marketing.commons.ym_cc581d'),
          minWidth: '120px',
        },
        {
          prop: 'lookUpUserCount',
          label: `${$t('marketing.commons.fwrs_c3c959')}(${this.websiteInfo.lookUpUserCount})`,
          minWidth: '100px',
        },
        {
          prop: 'lookUpCount',
          label: `${$t('marketing.commons.fwcs_f0be64')}(${this.websiteInfo.lookUpCount})`,
          minWidth: '100px',
        },
        {
          prop: 'operations',
          label: $t('marketing.commons.cz_2b6bc0'),
          width: 230,
          exComponent: 'operation',
        },
      ]
      return c
    },
  },
  watch: {
    websiteId() {
      if (this.websiteId) {
        this.queryWebsiteTrackData()
        this.queryWebsiteFormData()
        this.getChannelClueCount()
        this.queryWebsiteCta()
      }
    },
  },
  mounted() {
    if (this.websiteId) {
      this.getWebsiteById()
      this.queryWebsiteTrackData()
      this.queryWebsiteFormData()
      this.getChannelClueCount()
      this.queryWebsiteCta()
    }
  },
  created() {},
  destroyed() {},
  methods: {
    handleSettingSubmit() {
      this.queryWebsiteFormData()
    },
    // 微页面设置
    siteSetting(row) {
      if (!row.formId) {
        FxUI.Message.warning($t('marketing.pages.website_access.gwymqsbdwf_1461f6'))
        return
      }
      console.log('handleSetting', row)
      if (row.objectType === 26) {
        this.curSiteId = row.objectId
      }
      this.settingDialogVisible = true
      this.curFormId = row.formId
    },
    addTrackPage(formName) {
      this.$refs[formName].validate(valid => {
        if (!valid) {
          return
        }

        if (this.trackDialogTitle === $t('marketing.commons.tjgzym_8d9a37')) {
          this.addWebsiteTrackData()
        } else {
          this.updateWebsiteTrackData({ status: 0 })
        }
        this.closeTrackPageDailog()
      })
    },
    closeCopyDialog() {
      this.insertWebsiteVisible = false
    },
    async getH5ShortUrl(params) {
      // let form_params, longUrl;
      let url
      if (params.objectType === 26) {
        // form_params = `id=${params.objectId}&sourceType=7`;
        // longUrl = `${window.location.origin}/ec/h5-landing/release/index.html?`;
        url = createMaterialDetailUrlByObjectType(26, {
          id: params.objectId,
          sourceType: 7,
          objectId: this.websiteId,
          objectType: 28,
        }).url
      } else {
        // form_params = `formId=${params.formId}&objectId=${this.websiteId}&objectType=28&sourceType=7&needReport=true`;
        // longUrl = `${window.location.origin}/ec/h5-landing/release/form.html?`;
        url = createMaterialDetailUrlByObjectType(16, {
          id: params.formId,
          objectType: 28,
          objectId: this.websiteId,
          sourceType: 7,
          needReport: true,
        }).url
      }
      this.H5Url_form_async = url
      this.H5Url_form_iframe_async = `<iframe frameborder="0" src="${this.H5Url_form_async}" class="marketingFarme" allowFullScreen="true"></iframe>`
      this.script_insert_code = `
      window.FsYxt.creatIframeByScript({
        id: '${$t('marketing.pages.website_access.rqd_7c35c3')}',
        src:'${this.H5Url_form_async}',
        style:'width:300px;height:400px'
      })
      `
      // getShortUrl(longUrl + form_params).then(
      //   shortUrl => {
      //     this.H5Url_form_async = shortUrl;
      //     this.H5Url_form_iframe_async = `<iframe frameborder="0" src="${this.H5Url_form_async}" class="marketingFarme" allowFullScreen="true"></iframe>`;
      //   },
      //   () => {
      //     this.H5Url_form_async = longUrl + form_params;
      //     this.H5Url_form_iframe_async = `<iframe frameborder="0" src="${this.H5Url_form_async}" class="marketingFarme" allowFullScreen="true"></iframe>`;
      //   }
      // );
    },
    handleCloseDetail() {
      this.showClueDetail = false
    },
    showClue(data) {
      if (data.formId) {
        this.selectedClue = data
        this.showClueDetail = true
      }
    },
    insertForm(item) {
      this.selectedFormItem = item || {}
      this.getH5ShortUrl(item)
      this.insertWebsiteVisible = true
    },
    editForm(item) {
      if (item.objectType === 16) {
        this.$router.push({
          name: 'form-edit',
          params: {
            id: item.objectId,
          },
        })
      } else if (item.objectType === 26) {
        this.$router.push({
          name: 'site-design',
          params: {
            siteId: item.objectId || '0',
          },
          query: {
            from: 'website',
          },
        })
      }
    },
    deleteForm(item) {
      const msg = $t('marketing.commons.qrscxsbd_c23d19', { data: ({ option0: item.objectName }) })
      confirm(msg, $t('marketing.commons.ts_02d981'), {}).then(() => {
        const params = {
          objectId: this.websiteId,
          objectType: 28,
          targetObjectId: item.objectId,
          targetObjectType: item.objectType,
        }
        http.unBindMetarials(params).then(results => {
          if (results && results.errCode === 0) {
            FxUI.Message.success($t('marketing.commons.czcg_33130f'))
            this.queryWebsiteFormData()
          }
        })
      })
    },
    handlePageChange(data) {
      this.pageData.pageNum = data.pageNum
      this.pageData.pageSize = data.pageSize
      this.queryWebsiteFormData()
    },
    handleCtaPageChange(data) {
      this.ctaPageData.pageNum = data.pageNum
      this.ctaPageData.pageSize = data.pageSize
      this.queryWebsiteCta()
    },
    handleTrackPageChange(data) {
      this.trackPageData.pageNum = data.pageNum
      this.trackPageData.pageSize = data.pageSize
      this.queryWebsiteTrackData()
    },
    getTableData(data) {
      const tableData = []
      if (Array.isArray(data)) {
        data.forEach((item, index) => {
          item.operations = [
            { id: 'edit', name: $t('marketing.commons.bj_95b351') },
            { id: 'delete', name: $t('marketing.commons.sc_2f4aad') },
          ]
          item.lookUpUserCount = this.numberWithCommas(item.lookUpUserCount)
          item.lookUpCount = this.numberWithCommas(item.lookUpCount)
          tableData.push(item)
        })
      }

      return tableData
    },
    getTableFormData(data) {
      const tableData = []
      if (Array.isArray(data)) {
        data.forEach((item, index) => {
          item.clueNum = [{ id: 'clue', name: item.clueNum }]
          tableData.push(item)
        })
      }

      return tableData
    },
    closeTrackPageDailog() {
      this.trackDialogVisible = false
      this.$refs.trackPageForm.resetFields()
    },
    queryWebsiteTrackData() {
      const params = {
        pageNum: this.trackPageData.pageNum,
        pageSize: this.trackPageData.pageSize,
        websiteId: this.websiteId,
        keyword: this.websiteSearchKeyWord,
      }
      http.queryWebsiteTrackData(params).then(results => {
        if (results && results.errCode === 0) {
          if (results.data && results.data.result) {
            this.trackWebsiteList = this.getTableData(results.data.result)
            this.trackPageData.totalCount = results.data.totalCount
          }
        }
      })
    },
    queryWebsiteFormData() {
      const params = {
        id: this.websiteId,
        pageNum: this.pageData.pageNum,
        pageSize: this.pageData.pageSize,
      }
      http.queryWebsiteFormData(params).then(results => {
        if (results && results.errCode === 0) {
          const _data = results.data && results.data.result
          this.formDataInfoList = this.getTableFormData(_data)
          this.pageData.totalCount = results.data.totalCount
        }
      })
    },
    queryWebsiteCta() {
      http.queryWebsiteCta({
        id: this.websiteId,
        pageNum: this.ctaPageData.pageNum,
        pageSize: this.ctaPageData.pageSize,
      }).then(results => {
        if (results && results.errCode === 0) {
          this.ctaDataList = (results.data.result || []).map(item => {
            const componentLists = getGuideComponentLists(item.ctaDetail?.guideComponents)
            const triggerNames = getTriggerNames(item.ctaDetail?.triggerSettings)
            let siteName,siteId
            componentLists.forEach(component => {
              if(component.componentType === 1) {
                siteName = component.siteName
                siteId = component.siteId
              }
            })
            return {
              ...item,
              siteName,
              triggerNames,
              siteId,
              clueNum: [{
                id: 'clue',
                name: item.clueNum,
              }],
            }
          })
          this.ctaPageData.totalCount = results.data.totalCount
        }
      })
    },
    handleTrackEdit(data) {
      this.selectedTrackPageId = data.id
      this.trackPageInfo.trackPageName = data.trackName
      this.trackPageInfo.trackPageURL = data.trackUrl
      this.trackDialogTitle = $t('marketing.pages.website_access.bjgzym_92113a')
      this.trackDialogVisible = true
    },
    addWebsiteTrackData() {
      const params = {
        trackName: this.trackPageInfo.trackPageName,
        trackUrl: this.trackPageInfo.trackPageURL,
        websiteId: this.websiteId,
      }
      http.addWebsiteTrackData(params).then(results => {
        if (results && results.errCode === 0) {
          this.queryWebsiteTrackData()
        }
      })
    },
    updateWebsiteTrackData(data) {
      const params = {
        trackName: this.trackPageInfo.trackPageName,
        trackUrl: this.trackPageInfo.trackPageURL,
        id: this.selectedTrackPageId,
        status: data.status, // 0 正常 1停用 2删除
      }
      if (params.status === 2) {
        delete params.trackName
        delete params.trackUrl
      }
      http.updateWebsiteTrackData(params).then(results => {
        if (results && results.errCode === 0) {
          FxUI.Message.success($t('marketing.commons.czcg_33130f'))
          this.trackDeleteVisible = false
          this.queryWebsiteTrackData()
        }
      })
    },
    addwebsitePage() {
      this.trackDialogTitle = $t('marketing.commons.tjgzym_8d9a37')
      this.trackPageInfo.trackPageName = ''
      this.trackPageInfo.trackPageURL = ''
      this.trackDialogVisible = true
    },
    createadvertiseContent() {
      // 创建推广内容
      this.selectMaterialDialog = true
    },
    handleAddCta() {
      this.ctaDialogVisible = true
    },
    handleCtaDialogConfirm(data) {
      console.log(data)
      this.bindObject({
        id: data.id,
        objectType: 43,
      })
    },
    insertCta(data) {
      this.targetCtaId = data.ctaId
      this.ctaSdkDialogVisible = true
    },
    editCta(data) {
      this.$router.push({
        name: 'cta-create',
        query: {
          id: data.ctaId,
        },
      })
    },
    deleteCta(data) {
      const msg = $t('marketing.pages.website_access.qrscggw_980cf1')
      confirm(msg, $t('marketing.commons.ts_02d981'), {}).then(() => {
        const params = {
          objectId: this.websiteId,
          objectType: 28,
          targetObjectId: data.ctaId,
          targetObjectType: 43,
        }
        http.unBindMetarials(params).then(results => {
          if (results && results.errCode === 0) {
            FxUI.Message.success($t('marketing.commons.czcg_33130f'))
            this.queryWebsiteCta()
          }
        })
      })
    },
    handleMaterialSelected(data) {
      // 添加内容
      console.log(data)
      this.bindObject(data)
    },
    bindObject(data) {
      const params = {
        objectId: this.websiteId,
        objectType: 28,
        targetObjectId: data.id,
        targetObjectType: data.objectType || data.type,
      }

      if (data.type && !data.objectType) {
        // 新建添加的
        this.selectMaterialDialog = false
      }
      http.bindMetarials(params).then(results => {
        if (results && results.errCode === 0) {
          if(data.objectType === 43) {
            this.queryWebsiteCta()
          } else {
            this.queryWebsiteFormData()
          }
        }
      })
    },
    handleTrackDelete(data) {
      this.selectedTrackPageId = data.id
      const msg = $t('marketing.commons.qrscgzym_3eff7d', { data: ({ option0: data.trackName }) })
      confirm(msg, $t('marketing.commons.ts_02d981'), {}).then(() => {
        this.updateWebsiteTrackData({ status: 2 })
      })
    },
    copyLink(el) {
      const that = this
      if (!this[`${el}_clipboard`]) {
        this[`${el}_clipboard`] = true
      }
      const clipboard = new Clipboard(el)
      clipboard.on('success', e => {
        FxUI.Message.success($t('marketing.commons.fzcg_20a495'))
        clipboard.destroy()
        that.trackCodeVisible = false
      })
      clipboard.on('error', e => {
        FxUI.Message.error($t('marketing.commons.fzsb_5154ae'))
        clipboard.destroy()
      })
    },
    openLook() {
      const url = this.$router.resolve({
        name: 'setting-setitems',
        query: {
          type: 'v-marketing-plugin',
        },
      })
      window.open(url.href, '_blank')
    },
    async getChannelClueCount() {
      const res = await http.getChannelClueCount({
        promotionChannel: 'onlineservice',
        websiteId: this.websiteId,
      })
      if (res && res.errCode === 0) {
        this.clueNum = res.data.clueCount
      }
    },
    handleBatchImportWebsitePage() {
      this.importWebsiteDialogVisible = true
    },
    handleExportWebsiteTrackData() {
      this.exportLoading = true
      const params = {
        keyword: this.websiteSearchKeyWord,
        pageSize: 50000,
        pageNum: 1,
        websiteId: this.websiteId,
      }
      util.exportoFile(
        {
          action: 'exportWebsiteTrackData',
          params,
        },
        () => {
          this.exportLoading = false
        },
      )
    },
    handleWebsiteSearch() {
      this.trackPageData.pageNum = 1
      this.queryWebsiteTrackData()
    },
    getWebsiteById() {
      http.getWebsiteById({ id: this.websiteId }).then(results => {
        if (results && results.errCode === 0) {
          const { data } = results
          data.lookUpUserCount = this.numberWithCommas(data.lookUpUserCount || '')
          data.lookUpCount = this.numberWithCommas(data.lookUpCount || '')
          this.websiteInfo = data
        }
      })
    },
    numberWithCommas(x) {
      if (!x) return 0
      return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    },

  }, // 生命周期 - 销毁完成
}
</script>

<style lang="less" scoped>
.track-delete-dialog {
  display: flex;
  align-items: center;
  flex-direction: column;
  .warning-icon {
    color: #f27474;
    font-size: 50px;
    margin-top: 35px;
    margin-bottom: 22px;
  }
  .tips-1 {
    color: #181c25;
    font-size: 16px;
  }
  .tips-2 {
    margin-top: 14px;
    color: #545861;
    font-size: 13px;
  }
  .tips-3 {
    color: #545861;
    font-size: 13px;
    margin-bottom: 46px;
  }
}
.website-track-dialog {
  .el-dialog__body {
    padding: 30px 20px 0px 20px;
  }
  .pageurl-intro {
    display: inline-block;
    font-size: 12px;
    color: #91959e;
    margin-right: 10px;
  }
  .question {
    display: inline-block;
  }
}
.track-code-dialog {
  .el-dialog__header {
    box-shadow: 0px 2px 5px 0px rgba(69, 79, 91, 0.08);
  }
  .code {
    padding: 10px;
    margin-top: 21px;
  }
}
.insert-website-dialog {
  color: #181c25;
  font-size: 14px;
  /deep/ .el-dialog__header {
    box-shadow: 0px 2px 5px 0px rgba(69, 79, 91, 0.08);
  }
  .url-wrapper {
    display: flex;
    margin-top: 22px;
    .url {
      padding-right: 16px;
      .title {
        // margin: 25px 0 19px 0;
      }
      .h5url {
        height: 110px;
        width: 253px;
        color: var(--color-primary06,#407FFF);
        margin: 10px 0;
      }
      .sdk-tip {
        color: @color-subtitle;
        font-size: 12px;
        margin-top: 10px;
      }
      .iframe {
        width: 572px;
        margin-top: 10px;
        margin-bottom: 20px;
        overflow: auto;
        box-sizing: border-box;
      }
      .text-wrapper{
        background-color: #f6f6f6;
        padding: 10px
      }
      .copyH5url {
        width: 99px;
        height: 31px;
        margin-bottom: 22px;
      }
    }
    .options-wrapper{
      display: flex;
      justify-content: space-between;
      .options-item{
        display: flex;
        width: 250px;
        align-items: center;
        .item-title{
          margin-right: 8px;
        }
        .el-select{
          width: 180px;
        }
      }
    }
  }

  .copyiframe{
    float: right;
  }
  .line {
    width: 1px;
    height: 157px;
    margin-top: 35px;
    border-right: 1px solid #e9edf5;
  }
}
.website-manage__wrapper {
  background-color: #f2f2f5;
  padding: 10px;
  .operation-btn {
    margin-left: 5px;
    cursor: pointer;
    color: var(--color-info06,#407FFF);
  }
  .warning {
    color: #f27474;
  }
  .website-manage_cart {
    margin-bottom: 16px;
    background-color: #fff;
    &:last-child{
      margin-bottom: 0;
    }
    .top {
      display: flex;
      height: 54px;
      align-items: center;
      padding-left: 16px;
      padding-right: 20px;
      justify-content: space-between;
      .top-name{
        display: flex;
        .question{
          margin-left: 5px;
        }
      }
      .top-btns{
        .search-input{
          margin-right: 10px;
        }
      }
    }
    .empty {
      height: 246px;
      display: flex;
      flex-direction: column;
      align-items: center;
      .image {
        height: 72px;
        width: 69px;
        margin-bottom: 17px;
        margin-top: 38px;
      }
      .text {
        color: #666666;
        font-size: 14px;
      }
      .add {
        margin-top: 8px;
        color: var(--color-primary06,#407FFF);
        font-size: 14px;
        cursor: pointer;
      }
    }
    .bottom {
      padding: 0 16px 16px 16px;
      .plugin {
        padding: 12px 18px;
        border: 1px solid #e9edf5;
        .name {
          display: flex;
          justify-content: space-between;
          font-size: 14px;
        }
        .clue {
          color: #91959e;
        }
        .num {
          color: #181c25;
          font-size: 20px;
          margin-left: 18px;
        }
      }
    }
  }
}
</style>
