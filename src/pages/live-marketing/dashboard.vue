<template>
  <!-- 直播详情页面 -->
  <div
    v-loading.lock="isLoading"
    :class="$style.marketing_calebdar_dashboard"
  >
    <div slot="header">
      <content-header
        :title="crumbs"
        :border="true"
      >
        <!-- <el-button
          slot="right"
          type="primary"
          size="small"
          @click="mappingSettingsVisible = true"
          >报名设置</el-button
        > -->
      </content-header>
    </div>
    <!-- 左上侧dashboard -->
    <div :class="$style.content">
      <div :class="$style.content_header">
        <div :class="$style.con">
          <div :class="$style.cover">
            <VImage
              v-if="data.coverTaPath"
              :class="$style.image"
              fit="cover"
              :src="data.coverTaPath"
            />
            <LiveStatus
              v-if="data.status"
              :class="$style.status"
              :status="data.status"
              :life-status="marketingEvent.lifeStatus"
            />
          </div>
          <div :class="$style.memo">
            <div :class="$style.title">
              {{ data.title }}<span
                v-if="hasRecord"
                :class="$style.title__status"
              >{{ $t('marketing.commons.hf_94f38a') }}</span>
            </div>
            <div
              v-if="!isMuduSubEvent"
              :class="$style.desc"
            >
              {{ $t('marketing.commons.zbsj_519953') }}{{ data.startTime }} {{ $t('marketing.commons.z_981cbe') }} {{ data.endTime }}
            </div>
            <div :class="$style.desc">
              <span>{{ $t('marketing.commons.zblj_625d5a') }}</span>
              <el-link
                type="standard"
                :href="data.viewUrl"
                target="_blank"
              >
                {{ data.viewUrl }}
              </el-link>
              <i
                :class="['el-icon-document-copy', $style.iconCopy]"
                @click="handleCopy(data.viewUrl)"
              />
            </div>
          </div>
          <div :class="$style.options">
            <template v-if="!isMuduSubEvent">
              <el-button
                style="width: 73px"
                @click="editLive"
              >
                {{ $t('marketing.commons.bj_95b351') }}
              </el-button>
              <Dropdown
                style="margin-left: 10px"
                @command="handleMenuCommand"
              >
                <el-button
                  class="el-icon-more"
                  style="padding: 12px 17px"
                  :loading="syncing"
                />
                <DropdownMenu slot="dropdown">
                  <DropdownItem command="copy">
                    {{ $t('marketing.commons.fzzblj_cbb92a') }}
                  </DropdownItem>
                  <!-- 微吼 -->
                  <DropdownItem
                    v-if="data.livePlatform === 7 || data.livePlatform === 4"
                    command="getTcUrl"
                  >
                    {{ $t('marketing.commons.hqjslj_899a11') }}
                  </DropdownItem>
                  <DropdownItem command="sync">
                    <template v-if="isMuduMainEvent">
                      <fx-tooltip placement="left">
                        <div :class="$style['sync-wrap']">
                          {{ $t('marketing.commons.tbzbsj_735f6b') }}
                          <img :src="iconQuestion">
                        </div>
                        <div
                          slot="content"
                          class="tips"
                        >
                          <p>{{ $t('marketing.pages.live_marketing.djankljtby_ebcd06') }}</p>
                          <p>1.{{ $t('marketing.pages.live_marketing.jlqmdhdxdx_ff4e1b') }}</p>
                          <p>2.{{ $t('marketing.pages.live_marketing.tbgzjschdd_4cf503') }}</p>
                        </div>
                      </fx-tooltip>
                    </template>
                    <template v-else>
                      {{ $t('marketing.commons.tbzbsj_735f6b') }}
                    </template>
                  </DropdownItem>
                  <DropdownItem
                    command="form"
                  >
                    {{ $t('marketing.commons.bmsz_381009') }}
                  </DropdownItem>
                  <!-- 微吼不展示 -->
                  <DropdownItem command="syncRules">
                    {{ $t('marketing.commons.bmsjzdtbgz_03e67b') }}
                  </DropdownItem>
                </DropdownMenu>
              </Dropdown>
            </template>
          </div>
        </div>

        <!-- 市场活动ROI -->
        <div
          v-if="!isMuduMainEvent"
          :class="$style.roi_wrap"
        >
          <p :class="$style.title">
            {{ $t('marketing.commons.schd_928092') }}
          </p>
          <div :class="$style.roi">
            <div :class="$style.item">
              <span>{{ $t('marketing.commons.yjsr_b333b6') }}/{{ $t('marketing.commons.yqcb_7d13d6') }}</span>
              <p>
                {{ marketingEvent.expectedIncome || '--' }}/{{ marketingEvent.expectedCost || '--' }} ({{
                  marketingEvent.expectedROI ? (marketingEvent.expectedROI * 100).toFixed(4) : '--'
                }}% )
              </p>
            </div>
            <div :class="$style.item">
              <span>{{ $t('marketing.commons.sjsrsjcb_b740e8') }}</span>
              <p>
                {{ marketingEvent.actualIncome || '--' }}/{{ marketingEvent.actualCost || '--' }}
                (
                {{ marketingEvent.actualROI ? (marketingEvent.actualROI * 100).toFixed(4) : '--' }}% )
              </p>
            </div>
            <div
              v-if="marketingEventAudit"
              :class="$style.item"
            >
              <span>{{ $t('marketing.commons.shzt_b6d0e9' ) }}</span>
              <p>
                {{ CampaignReviewStatusLabel[marketingEvent.lifeStatus] || '--' }}
              </p>
            </div>
          </div>
          <a
            v-loading="detailLoading"
            @click="handleOpenMarketingDetail($route.params.id)"
          >{{ $t('marketing.commons.ckschdxq_3d9783') }}</a>
        </div>
      </div>
      <el-menu
        :default-active="currentTab"
        class="dashboard-el-menu-wrapper"
        mode="horizontal"
        @select="handleSelect"
      >
        <el-menu-item
          class="set-menu-item"
          index="live-dashboard-overview"
        >
          {{ $t('marketing.pages.live_marketing.zbgl_9b6337') }}
        </el-menu-item>
        <el-menu-item
          v-if="isMuduMainEvent"
          class="set-menu-item"
          index="live-dashboard-promotion"
        >
          {{ $t('marketing.commons.tgnr_a6ec90') }}
        </el-menu-item>
        <el-menu-item
          v-if="!isMuduSubEvent"
          class="set-menu-item"
          index="live-dashboard-participants"
        >
          {{ $t('marketing.commons.cyry_b13613') }}
        </el-menu-item>
        <el-menu-item
          v-if="!isMuduSubEvent && (vDatas.uinfo.wechatVoucher || vDatas.uinfo.sourceCouponEnabled)"
          class="set-menu-item"
          index="live-dashboard-coupons"
        >
          {{ $t('marketing.commons.yhq_2f3635') }}
        </el-menu-item>
        <el-menu-item
          v-if="!isMuduSubEvent"
          class="set-menu-item"
          index="live-dashboard-radar"
        >
          {{ $t('marketing.commons.tgld_803716') }}
        </el-menu-item>
        <el-menu-item
          v-if="!isMuduSubEvent && vDatas.pluginInfo.marketingPlan"
          class="set-menu-item"
          index="live-dashboard-kanban"
        >
          {{ $t('marketing.commons.yyjh_942f80') }}<span class="kanban-num">{{ kanbanNum }}</span>
        </el-menu-item>
        <el-menu-item
          v-if="!isMuduSubEvent"
          class="set-menu-item"
          index="live-dashboard-trigger"
        >
          SOP
        </el-menu-item>
        <el-menu-item
          v-if="!isMuduSubEvent"
          class="set-menu-item"
          index="live-dashboard-analysis"
        >
          {{ $t('marketing.commons.sjfx_6450d8') }}
        </el-menu-item>
        <el-menu-item
          v-for="menu in mainMenusData"
          :key="menu.id"
          class="set-menu-item"
          :index="`custom-component-${menu.id}`"
        >
          {{ menu.name }}
        </el-menu-item>
        <el-submenu
          v-if="moreMenusData && moreMenusData.length"
          class="set-submenu"
          index="more"
        >
          <template slot="title">
            {{ $t('marketing.commons.gd_0ec9ea') }}
          </template>
          <el-menu-item
            v-for="menu in moreMenusData"
            :key="menu.id"
            :index="`custom-component-${menu.id}`"
          >
            {{
              menu.name
            }}
          </el-menu-item>
        </el-submenu>
      </el-menu>

      <router-view />
    </div>
    <Dialog
      v-if="lectureUrlDialog"
      :title="$t('marketing.commons.hqjslj_899a11')"
      width="480px"
      :ok-text="$t('marketing.commons.fz_79d3ab')"
      :visible="lectureUrlDialog"
      @onSubmit="handleCopyLectureUrl(data.lectureUrl)"
      @onClose="lectureUrlDialog = false"
    >
      <div>
        <div style="display: flex; align-items: center">
          {{ $t('marketing.commons.jszblj_855e74') }}
          <el-input
            readonly
            style="flex: 1; margin-left: 16px"
            :value="data.lectureUrl"
          />
        </div>
        <p style="margin-top: 20px">
          {{ $t('marketing.commons.jszbmm_214ca0') }}<span style="margin-left: 16px">{{ data.lecturePassword || $t('marketing.commons.wsz_fe2d26') }}</span>
        </p>
      </div>
    </Dialog>
    <MarketingActivityMappingSettings
      v-if="mappingSettingsVisible"
      :visible.sync="mappingSettingsVisible"
      :marketing-event-id="$route.params.id"
      sence="live"
      :form-hexagon-id="data.formHexagonId"
    />
    <EnrollSyncRules
      v-if="$route.params.id"
      :marketing-event-id="$route.params.id"
      :visible="EnrollSyncRulesVisible"
      @close="EnrollSyncRulesVisible = false"
    />
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex'
import http from '@/services/http/index.js'
import ContentHeader from '@/components/content-header/index.vue'
import Dialog from '@/components/dialog/index.vue'
import { requireAsync } from '@/utils/index.js'
import LiveStatus from './components/live-status.vue'
import MarketingActivityMappingSettings from '@/components/MarketingActivityMappingSettings/index.vue'
import EnrollSyncRules from '@/components/enroll-sync-rules/index.vue'
import kisvData from '@/modules/kisv-data.js'
import crmLayoutMenusMixin from '@/mixins/crm-layout-menus.js'
import { CampaignReviewStatusLabel } from '@/utils/statusEnum.js'
import { LIVE_PLATFORM_ENUM } from './create.vue'

import iconQuestion from '@/assets/images/icon/icon-question.png'

export default { // mainMenusData, moreMenusData
  components: {
    ContentHeader,
    VImage: FxUI.Image,
    Dialog,
    LiveStatus,
    MarketingActivityMappingSettings,
    Dropdown: FxUI.Dropdown,
    DropdownItem: FxUI.DropdownItem,
    DropdownMenu: FxUI.DropdownMenu,
    ElMenu: FxUI.Menu,
    ElMenuItem: FxUI.MenuItem,
    ElSubmenu: FxUI.Submenu,
    FxTooltip: FxUI.Tooltip,
    EnrollSyncRules,
  },
  mixins: [crmLayoutMenusMixin],
  /**
   * 注册父组件刷新逻辑
   */
  provide() {
    return {
      refresh: this.fetchBriefStatistics,
    }
  },
  data() {
    return {
      vDatas: kisvData.datas,
      marketingEventId: '',
      detailLoading: false,
      crumbs: [
        { text: $t('marketing.commons.zbyx_a9fa5d'), to: { name: 'live-marketing' } },
        { text: '--', to: false },
      ],
      lectureUrlDialog: false,
      syncing: false,
      mappingSettingsVisible: false,
      EnrollSyncRulesVisible: false,
      CampaignReviewStatusLabel,
      iconQuestion,
    }
  },
  computed: {
    ...mapState('MarketingEventSet', ['marketingEventAudit']),
    ...mapState('MarketingLiveDashboard', [
      'isLoading',
      'kanbanNum',
      'sumdata',
      'marketingEvent',
      'data',
      'dataUpdateFlag',
    ]),
    currentTab() {
      const { name, params: { componentName } } = this.$route
      return componentName || name
    },
    isOver() {
      return this.data.status === 3
    },
    hasRecord() {
      return this.data.status === 5
    },
    marketingEventType() {
      return (this.marketingEvent && this.marketingEvent.eventType) || ''
    },
    isMudu() {
      return this.data.livePlatform === LIVE_PLATFORM_ENUM.MUDU
    },
    isMuduMainEvent() {
      return this.isMudu && this.data.subEvent !== 1
    },
    isMuduSubEvent() {
      return this.isMudu && this.data.subEvent === 1
    },
  },

  watch: {
    data() {
      this.crumbs[1].text = this.data.title || '--'

      // if (this.isMuduSubEvent) {
      //   this.crumbs[0] = {
      //     text: '返回',
      //     to: {
      //       name: 'live-dashboard',
      //       params: {
      //         id: this.marketingEvent.parentId,
      //       },
      //     },
      //   }
      // } else {
      //   this.crumbs[0] = { text: $t('marketing.commons.zbyx_a9fa5d'), to: { name: 'live-marketing' } }
      // }
    },
    '$route.params.id': {
      handler(id) {
        if (id !== this.marketingEventId) {
          this.init()
          this.queryDetail()
          document.querySelector('.g-content-wrapper').scrollTo(0, 0)
        }
      },
    },
    mappingSettingsVisible() {
      if (!this.mappingSettingsVisible) {
        this.syncData({
          dataUpdateFlag: this.dataUpdateFlag + 1,
        })
      }
    },
  },
  mounted() {
    this.init()
    this.queryDetail()
    document.querySelector('.g-content-wrapper').scrollTo(0, 0)
  },
  beforeDestroy() {
    this.resetData()
  },
  methods: {
    ...mapActions('MarketingCalendar/Dashboard', ['queryActivitiesDetail']),
    ...mapActions('MarketingLiveDashboard', [
      'queryBriefStatistics',
      'queryActivitiesDetailById',
      'getSOPTabInfo',
      'queryLiveDetail',
      'resetData',
      'syncData',
    ]),

    init() {
      const { id } = this.$route.params
      this.marketingEventId = id
      if (id) {
        this.queryActivitiesDetailById({
          params: { id },
        })
        this.fetchBriefStatistics()
        this.getSOPTabInfo({
          params: { objectId: id },
        })
      }
    },

    fetchBriefStatistics() {
      const { id } = this.$route.params
      this.queryBriefStatistics({
        params: {
          marketingEventId: id,
          objectTypes: [4, 6, 26],
        },
      })
    },

    queryDetail() {
      this.queryLiveDetail({
        params: { id: this.marketingEventId },
        onSuccess: () => {
          // 直播信息未完善提示
          if (!this.data.livePlatform) {
            FxUI.MessageBox.confirm($t('marketing.commons.hdxxwwsbcw_720085'), $t('marketing.commons.ts_02d981'), {
              confirmButtonText: $t('marketing.commons.msws_b03804'),
              cancelButtonText: $t('marketing.commons.fh_5f4112'),
              closeOnPressEscape: false,
              closeOnClickModal: false,
              showClose: false,
              type: 'warning',
            })
              .then(() => {
                this.editLive()
              })
              .catch(() => {
                this.$router.back()
              })
          }
        },
      })
    },
    handleSelect(index) {
      const route = {
        name: index,
      }

      if (index === 'live-dashboard-radar') {
        route.query = { source: 'liveTab' }
      }

      if (/^custom-component/.test(index)) {
        route.name = 'live-dashboard-custom'
        route.params = {
          componentName: index,
        }
      }

      this.$router.push(route)
    },
    editLive() {
      if (this.data.editable) {
        this.$router.push({
          name: 'live-create',
          query: { id: this.marketingEventId },
        })
      } else {
        FxUI.MessageBox.confirm($t('marketing.commons.dqhdsysdzt_6731fe'), $t('marketing.commons.ts_02d981'), {
          confirmButtonText: $t('marketing.commons.zdl_ce2695'),
          cancelButtonText: $t('marketing.commons.qx_625fb2'),
          type: 'warning',
        })
      }
    },
    handleMenuCommand(command) {
      switch (command) {
        case 'copy':
          this.handleCopy(this.data.viewUrl)
          break
        case 'getTcUrl':
          this.handleGetLectureUrl()
          break
        case 'sync':
          this.syncLiveStatistics()
          break
        case 'form':
          this.mappingSettingsVisible = true
          break
        case 'trigger':
          this.$router.push({
            name: 'trigger-list',
            params: {
              type: 'live',
              id: this.data.id,
            },
            query: {
              objectId: this.marketingEventId,
              marketingEventId: this.marketingEventId,
              objectName: this.data.title,
            },
          })
          break
        case 'syncRules':
          this.EnrollSyncRulesVisible = true
          break
        default:
          break
      }
    },
    syncLiveStatistics() {
      this.syncing = true
      http
        .syncLiveStatistics({
          id: this.data.id,
        })
        .then(({ errCode, errMsg, data }) => {
          this.syncing = false
          if (errCode === 0) {
            this.fetchBriefStatistics()
            FxUI.Message.success($t('marketing.commons.tbcg_52b85c'))
          } else {
            FxUI.Message.error(errMsg || $t('marketing.commons.sxsb_245c7a'))
          }
        })
    },
    handleCopyLectureUrl(url) {
      let text = `
        ${this.data.title}\r\n
        ${$t('marketing.commons.zbsj_519953')}
        ${this.data.startTime}\r\n
        ${$t('marketing.commons.jszblj_acf987')}
        ${url} \r\n
        ${$t('marketing.commons.jszbmm_214ca0')}
        ${this.data.lecturePassword}
      `
      if (this.data.livePlatform === 7) {
        text = `
        ${this.data.title}\r\n
        ${$t('marketing.commons.zbsj_519953')}
        ${this.data.startTime}\r\n
        ${$t('marketing.commons.jszblj_acf987')}
        ${url}`
      }
      this.handleCopy(text)
    },
    handleCopy(url) {
      if (url) {
        this.copyToClipboard(url)
        FxUI.Message.success($t('marketing.commons.fzcg_20a495'))
      }
      this.lectureUrlDialog = false
    },
    copyToClipboard(text) {
      const $temp = $('<textarea>')
      $('body').append($temp)
      $temp.val(text).select()
      document.execCommand('copy')
      $temp.remove()
    },
    handleGetLectureUrl() {
      this.lectureUrlDialog = true
    },
    handleOpenMarketingDetail(id) {
      // loading
      this.detailLoading = true
      requireAsync('crm-components/showdetail/showdetail', Detail => {
        this.detailLoading = false
        if (!this.$detail) {
          this.$detail = new Detail({ showMask: true })
          this.$detail.on('refresh', () => {})
        }
        this.$detail.setApiName('MarketingEventObj')
        setTimeout(() => this.$detail.show(id), 0)
      })
    },
  },
}
</script>
<style lang="less" scoped>
.dashboard-el-menu-wrapper {
  margin: 10px 10px 0 10px;
  padding-left: 22px;
  border-top: 1px solid #e6e6e6;
  margin-bottom: 10px;
  .set-menu-item {
    margin: 0 40px 0 0;
    padding: 0;
    height: 50px;
    line-height: 52px;
    .kanban-num {
      font-size: 12px;
      color: #c1c1c1;
      margin-left: 3px;
      vertical-align: top;
    }
    &.is-active {
      border-bottom-color: var(--color-primary06, #ff8000);
    }
  }
  /deep/ .el-submenu {
    .el-submenu__title {
      padding: 0;
      height: 50px;
      line-height: 52px;
    }
  }
  /deep/ .el-submenu {
    &.is-active {
      .el-submenu__title {
        border-bottom-color: var(--color-primary06, #ff8000);
      }
    }
  }
}
</style>
<style lang="less" module>
@basePath: '@/assets/images/';
.marketing_calebdar_dashboard {
  display: flex;
  flex-flow: column nowrap;
  height: 100%;
  background-color: #f2f2f5;
  min-width: 1368px;

  .content {
    // height: calc(100% - 56px);
    background-color: #f2f2f5;
  }
  .options {
    padding-right: 4px;
  }
  .content_header {
    padding: 12px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .con {
      flex: 1;
      display: flex;
      align-items: center;
      padding-right: 10px;
      min-width: 0;
      .cover {
        width: 160px;
        height: 88px;
        margin-right: 14px;
        position: relative;
        .status {
          position: absolute;
          left: 0;
          top: 3px;
        }
      }
      .image {
        width: 100%;
        height: 100%;
        border-radius: 6px;
      }
      .memo {
        flex: 1;
        min-width: 0;
      }
      .title {
        color: @color-title;
        font-size: 16px;
        margin-bottom: 6px;
        .title__status {
          display: inline-block;
          width: 40px;
          height: 20px;
          line-height: 20px;
          text-align: center;
          border: 1px solid rgba(206, 213, 222, 1);
          color: #879eb8;
          font-size: 10px;
          border-radius: 2px;
          margin-left: 10px;
          box-sizing: border-box;
        }
      }
      .desc {
        font-size: 12px;
        color: #545861;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: flex;
        align-items: center;

        .iconCopy {
          color: #407fff;
          cursor: pointer;
          margin-left: 8px;

          &:hover {
            color: #69f;
          }
        }
      }
      .button {
        margin-top: 6px;
        a {
          margin-right: 20px;
          cursor: pointer;
        }
      }
    }
    .roi_wrap {
      padding-left: 40px;
      border-left: 1px solid @border-color-base;
      width: 300px;
      .title {
        color: @color-title;
        margin-bottom: 6px;
      }
      .roi {
        display: flex;
        .item {
          flex: 1;
          margin-right: 20px;
          span {
            color: @color-subtitle;
          }
          p {
            font-size: 14px;
            color: #545861;
          }
        }
      }
      > a {
        display: inline-block;
        padding-top: 6px;
        cursor: pointer;
      }
    }
  }
}
.sync-wrap {
  display: flex;
  align-items: center;

  img {
    width: 16px;
    height: 16px;
    margin-left: 4px;
  }
}
</style>
