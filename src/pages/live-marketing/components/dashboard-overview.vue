<template>
  <MuduOverview v-if="isMudu" />
  <div v-else>
    <div
      :class="$style.graph"
    >
      <Card :class="$style.graph_item">
        <div style="padding-bottom: 15px">
          <VSumdata
            :data="sumdata"
            :live-title="data.title"
            :live-platform="data.livePlatform + ''"
          />
        </div>
      </Card>
    </div>
    <div
      :class="$style.material_promotion"
    >
      <SpreadContentList
        :id="$route.params.id"
        :marketing-event-name="data.title"
        :material-label="$t('marketing.commons.zb_7bbe8e')"
        :life-status="marketingEvent.lifeStatus"
        :showTemplate="false"
      />
      <MarketingActivityQrposter
        scene="live"
        size="small"
        :life-status="marketingEvent.lifeStatus"
        :marketing-event-id="$route.params.id"
        :reset-page-size="6"
        :title="$t('marketing.commons.qbhb_621d02')"
        :class="$style.qrposter_content"
        :show-mobile-display-tag="true"
        :material-label="$t('marketing.commons.zb_7bbe8e')"
      />
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import Card from '@/components/card/index.vue'
import SpreadContentList from '@/pages/content-marketing/content-dashboard/module/spread-content-list.vue'
import MarketingActivityQrposter from '@/components/MarketingActivityQrposter/index.vue'

import VSumdata from './sumdata.vue'
import { LIVE_PLATFORM_ENUM } from '../create.vue'
import MuduOverview from './dashboard-mudu-overview.vue'

export default {
  components: {
    Card,
    VSumdata,
    MarketingActivityQrposter,
    SpreadContentList,
    MuduOverview,
  },
  computed: {
    ...mapState('MarketingLiveDashboard', [
      'sumdata',
      'marketingEvent',
      'data',
    ]),
    isMudu() {
      return this.data.livePlatform === LIVE_PLATFORM_ENUM.MUDU
    },
    isMuduMainEvent() {
      return this.isMudu && this.data.subEvent !== 1
    },
    isMuduSubEvent() {
      return this.isMudu && this.data.subEvent === 1
    },
  },
}
</script>

<style lang="less" module>
.graph {
  padding: 0px 10px 0;
  .graph_item {
    :global {
      .card__header {
        height: auto;
        padding-top: 10px;
      }
    }
  }
}

.material_promotion {
  display: flex;
  background-color: #f2f2f5;
  padding: 10px;
  .promotion_content {
    width: 380px;
    margin-right: 10px;
  }
  .leads_table {
    min-width: 0;
    flex: 1;
    display: flex;
    flex-direction: column;
    .table_title {
      font-size: 14px;
      color: #181c25;
      padding-left: 22px;
      height: 30px;
      line-height: 30px;
      background-color: #fff;
    }
  }
  .qrposter_content {
    margin-left: 10px;
  }
}
</style>
