<template>
  <div :class="$style.live_marketing_leadtable">
    <LeadTableWrap
      v-model="tableParams"
      @input="queryLeadDetail"
      @exportLead="handleExportLead"
      @sendSmsMsg="sendSmsMsg"
      @saveToClue="saveToClue"
      @toggleAllSelection="toggleAllSelection"
      @submit="submit"
      v-bind="$attrs"
      v-on="$listeners"
    >
      <div :class="$style.live__lead" v-loading="loading">
        <div :class="$style.table_wrap">
          <v-table
            :class="[$style.table_lead, $style.bottomborder]"
            ref="leadTable"
            :data="leadDatas"
            :columns="leadColumns"
            :settable="true"
            :height="tableHeight"
            :filter-option="false"
            :row-style="{ height: '60px' }"
            @custom:clue-action="handleClueDetail"
            :tableRowStyle="{ cursor: 'pointer' }"
            @selection-change="slectionChange"
            :rowKey="getRowKeys"
          >
            <template slot-scope="scope">
              <template v-if="scope.col.exComponent === 'custom-tags'">
                <fx-popover
                  :title="$t('marketing.commons.bq_14d342')"
                  trigger="hover"
                  width="160"
                  v-if="scope.row.tagNameList && scope.row.tagNameList.length"
                >
                  <div :class="$style.qywx_customer_tag_wrapper" slot="reference">
                    <template v-for="(item, index) in scope.row.tagNameList">
                      <span
                        :class="[
                          $style.tag,
                          index + 1 === scope.row.tagNameList.length &&
                            $style.last
                        ]"
                        v-if="index <= _getNumsTag2Show(scope.row.tagNameList)"
                        :key="index"
                      >
                        {{
                        index == _getNumsTag2Show(scope.row.tagNameList) &&
                        index + 1 !== scope.row.tagNameList.length
                        ? ""
                        : (item.firstTagName + (item.secondTagName ? `：${item.secondTagName}` : ''))
                        }}
                      </span>
                    </template>
                  </div>
                  <div :class="$style.qywx_customer_tag_wrapper_pop">
                    <span
                      :class="$style.tag_pop"
                      v-for="(item, index) in scope.row.tagNameList"
                      :key="index"
                    >{{ (item.firstTagName + (item.secondTagName ? `：${item.secondTagName}` : '')) }}</span>
                  </div>
                </fx-popover>
                <span v-else>--</span>
              </template>
            </template>
          </v-table>
        </div>
        <v-pagen :pagedata.sync="pageData" @change="handleLeadPageChange"></v-pagen>
      </div>
    </LeadTableWrap>
    <el-dialog :title="$t('marketing.commons.zxcrxs_f70585')" :visible.sync="toClueVisible" width="547px" class="review-status-dialog" append-to-body>
      <span class="text">{{ $t('marketing.commons.jhdsxcrsbd_fedc65') }}</span>
      <div slot="footer" class="dialog-footer">
        <fx-button size="small" type="primary" @click="toClueConfirm">{{ $t('marketing.commons.qd_aa7527') }}</fx-button>
        <fx-button size="small" @click="toClueVisible = false">{{ $t('marketing.commons.qx_c08ab9') }}</fx-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import http from '@/services/http/index';
import util from '@/services/util/index';
import Card from '@/components/card';
import LeadTableWrap from './lead-table-wrap';
import VTable from '@/components/table-ex';
import VPagen from '@/components/kitty/pagen';
import table from './table';

export default {
  components: {
LeadTableWrap,
Card,
Select: FxUI.Select.components.ElSelect,
Option: FxUI.Select.components.ElSelect.components.ElOption,
VTable,
VPagen,
elDialog: FxUI.Dialog
},
  props: {
    marketingEventId: {
      type: String,
      default: '',
    },
    selectedRow: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      loading: false,
      tableParams: {
        keyword: '',
        viewLiveStatus: 2,
        leadOptions: [
          {
            label: $t('marketing.commons.qb_a8b0c2'),
            value: 2,
          },
          {
            label: $t('marketing.commons.wgk_72f203'),
            value: 0,
          },
          {
            label: $t('marketing.commons.ygk_b1489b'),
            value: 1,
          },
        ],
        exportLoading: false,
        selectedLeadNumber: 0,
        leadLen: 0,
        selectedAll: false,
      },
      pageData: {
        pageNum: 1,
        pageSize: 10,
        totalCount: 0,
      },
      leadOptions: [
        {
          label: $t('marketing.commons.qb_a8b0c2'),
          value: 2,
        },
        {
          label: $t('marketing.commons.wgk_72f203'),
          value: 0,
        },
        {
          label: $t('marketing.commons.ygk_b1489b'),
          value: 1,
        },
      ],
      leadColumns: [],
      leadDatas: [],
      selectedLeads: [],
      tableHeight: 'auto',
      getRowKeys: function(row) {
        return row.id;
      },
      toClueVisible: false,
    };
  },
  // watch: {
  //   selectedRow: {
  //     deep: true,
  //     handler() {
  //       this.calcTableSelect();
  //     }
  //   }
  // },
  created() {
    if (this.marketingEventId) {
      this.queryLeadDetail(true);
    }
  },
  methods: {
    handleClueDetail(row, column, event) {
      if (row.saveCrmStatus === 0) {
        if(!row.otherCrmObjectBind) return;
        CRM.api.show_detail({
          apiname: row.otherCrmObjectBind.apiName,
          id: row.otherCrmObjectBind.objectId
        });
      }
    },
    handleClueRow(row, column, event) {
      console.log('click clue row', ...arguments);
      this.handleClueDetail(row);
    },
    calcTableSelect(data) {
      const colleague = this.selectedRow || [];
      colleague &&
        colleague.length &&
        data.forEach((row) => {
          if (colleague.findIndex((item) => item.id == row.id) > -1) {
            this.toggleRowSelection(row);
          }
        });
    },
    parseLeads(leads) {
      return leads.map((item) => ({
        name: item.name,
        id: item.id,
        phone: item.phone,
      }));
    },
    sendSmsMsg() {
      const leads = this.parseLeads(this.selectedLeads);
      this.$router.push({
        name: 'promotion-activity-sms',
        params: { type: 'create', leads: { colleague: leads } },
        query: {
          id: this.marketingEventId,
          source: 'live',
        },
      });
    },
    toggleRowSelection(row) {
      this.$refs.leadTable.toggleRowSelection(row, true);
    },
    toggleAllSelection() {
      this.tableParams.selectedAll = !this.tableParams.selectedAll;
      if (this.tableParams.selectedAll) {
        const params = {
          pageNum: 1,
          pageSize: this.pageData.totalCount,
          keyword: this.tableParams.keyword,
          viewLiveStatus: this.tableParams.viewLiveStatus,
          marketingEventId: this.marketingEventId,
        };
        if (!params.keyword) {
          delete params.keyword;
        }
        http.queryLiveLeadDetail(params).then(({ errCode, data }) => {
          if (errCode === 0) {
            this.leadDatas = table.getDatas(data);
            this.pageData.totalCount = data.totalCount || 0;
            this.tableParams.leadLen = this.pageData.totalCount;
            this.leadDatas.forEach((row) => {
              this.toggleRowSelection(row);
            });
            setTimeout(() => {
              this.calcTableSelect(this.leadDatas);
            }, 100);
            this.slectionChange(this.leadDatas);
          }
        });
      } else {
        this.$refs.leadTable.clearSelection();
      }
    },
    slectionChange(val) {
      this.tableParams.selectedLeadNumber = (val && val.length) || 0;
      this.selectedLeads = val;
    },
    submit() {
      const leads = this.parseLeads(this.selectedLeads);
      this.$emit('confirm', leads);
    },
    handleExportLead() {
      this.tableParams.exportLoading = true;
      util.exportoFile(
        {
          action: "fakeExportLiveLead",
          params: {
            marketingEventId: this.marketingEventId,
            keyword: this.tableParams.keyword,
            viewLiveStatus: this.tableParams.viewLiveStatus,
            ...this.pageData,
          },
        },
        () => {
          this.tableParams.exportLoading = false;
        },
      );
    },
    queryLeadDetail(isInit) {
      const params = {
        ...this.pageData,
        keyword: this.tableParams.keyword,
        viewLiveStatus: this.tableParams.viewLiveStatus,
        marketingEventId: this.marketingEventId,
      };
      if (!params.keyword) {
        delete params.keyword;
      }
      this.loading = true;
      http.queryLiveLeadDetail(params).then(({ errCode, data }) => {
        this.loading = false;
        if (errCode === 0) {
          isInit && (this.leadColumns = table.getColumns(data));
          this.leadDatas = table.getDatas(data);
          this.pageData.totalCount = data.totalCount || 0;
          this.tableParams.leadLen = this.pageData.totalCount;
          setTimeout(() => {
            this.calcTableSelect(this.leadDatas);
          }, 100);
        }
      });
    },
    handleLeadPageChange(data) {
      Object.assign(this.pageData, data);
      this.queryLeadDetail();
    },
    /* 获取可显示标签数量 */
    _getNumsTag2Show(tags) {
      let acc = 0;
      let len = tags.length;
      _.some(tags, (tag, index) => {
        acc +=
          (tag.firstTagName + (tag.secondTagName ? `：${tag.secondTagName}` : '')).replace(/[^\x00-\xff]/g, 'xx')
            .length *
            6 +
          26;
        if (acc < 270) return false;
        len = index;
        return true;
      });
      return len;
    },
    saveToClue() {
      this.toClueVisible = true;
    },
    getIDs() {
      let selectedLeads = this.selectedLeads;
      let ids = [];
      selectedLeads.forEach((item) => {
        ids.push(item.id);
      });
      return ids;
    },
    toClueConfirm() {
      let ids = this.getIDs();
      http.reImportDataToCrm({ ids: ids }).then((results) => {
        if (results && results.errCode == 0) {
          FxUI.MessageBox.alert($t('marketing.commons.zxcrknxyjz_9b0350'), $t('marketing.commons.ts_02d981'), {
            confirmButtonText: $t('marketing.commons.zdl_ce2695'),
          })
        }
      });
      this.toClueVisible = false;
    },
  },
};
</script>

<style lang="less" module>
@basePath: '@/assets/images/';
.live_marketing_leadtable {
}
.live__lead {
  overflow-y: auto;
  // flex: 1;
  // display: flex;
  // flex-direction: column;
  .table_wrap {
    // flex: 1;
    min-height: 350px;
    display: flex;
  }
}
.qywx_customer_tag_wrapper {
  display: flex;
  align-items: center;
  overflow: hidden;
  white-space: nowrap;
  .tag {
    display: inline-block;
    padding: 0 8px;
    margin-right: 10px;
    height: 20px;
    line-height: 20px;
    color: #839ab6;
    font-size: @font-size-small;
    border-radius: 3px;
    background: #e0eeff;
    box-sizing: border-box;
  }
  .tag:last-child:not(.last) {
    width: 26px;
    background: #e0eeff url('@{basePath}/icon/ellipsis-icon.png') center/14px no-repeat;
  }
}
.qywx_customer_tag_wrapper_pop {
  max-height: 240px;
  overflow: auto;
  white-space: normal;
  .tag_pop {
    display: inline-block;
    margin-top: 8px;
    margin-right: 10px;
    padding: 0 8px;
    height: 20px;
    line-height: 20px;
    color: #839ab6;
    font-size: @font-size-small;
    border-radius: 3px;
    background: #e0eeff;
    box-sizing: border-box;
  }
}
</style>
