<template>
  <!-- 新建直播页面 -->
  <div :class="$style.create_live_marketing_wrapper">
    <page-header :title=" $route.query.id ? $t('marketing.pages.live_marketing.bjzb_8aea04') : $t('marketing.pages.live_marketing.cjzb_f6102c')">
      <div slot="right">
        <el-button
          type="primary"
          :loading="sending"
          @click="handleSend"
        >
          {{ $t('marketing.commons.bc_be5fbb') }}
        </el-button>
        <el-button
          type="default"
          @click="handleCancel"
        >
          {{ $t('marketing.commons.qx_625fb2') }}
        </el-button>
      </div>
    </page-header>
    <!-- 表单数据 -->

    <el-form
      ref="form"
      :class="$style.form"
      label-width="160px"
      label-position="left"
      :model="formData"
      :rules="formRules"
    >
      <fx-input
        v-model="formData.title"
        :placeholder="$t('marketing.commons.qsr_02cc4f')"
        :maxlength="30"
        :label="$t('marketing.pages.live_marketing.zbbt_310a1d')"
        prop="title"
        show-word-limit
      />
      <el-form-item
        :label="$t('marketing.pages.live_marketing.zbfm_8386e7')"
        prop="originalImageAPath"
      >
        <div :class="$style.create__cover">
          <div :class="$style.cover">
            <PictureCutter
              :max-size="1024 * 1024"
              :cut-size="[{
                title: $t('marketing.pages.meeting_marketing.hbfm_d8183b') + '（9:5）',
                desc: $t('marketing.components.PictureSelector.xs_6fa81e'),
                width: 900,
                height: 500,
                photoTargetType: 45,
              },{
                title: $t('marketing.commons.xcxfm_94b4f3')+ '（5:4）',
                desc: $t('marketing.components.PictureSelector.yyzfwxxcxy_d66d73'),
                width: 420,
                height: 336,
                photoTargetType: 43,
              },{
                title: $t('marketing.commons.pyqfm_ea274c')+ '（1:1）',
                desc: $t('marketing.components.PictureSelector.yyzfwxhpyq_5c11c7'),
                width: 300,
                height: 300,
                photoTargetType: 44,
              }]"
              :default-image-url="coverImage"
              :uploadtip="$t('marketing.commons.jyccxszcgs_906ed5')"
              output-path-type="a"
              show-picture-standard
              @change="handlePictureCutterChange"
            />
            <!-- <div :class="$style.addcover" @click="coverDialogVisible = true" v-if="!coverImage">
            <i class="icon km-ico-add"></i>
            <p :class="$style.addtext">添加封面</p>
          </div>
          <div :class="$style.cover_image_wrap" v-else @click="coverDialogVisible = true">
            <img :src="coverImage" :class="$style.cover_image" />
            <div :class="$style.change_text">更换</div>
          </div> -->
            <!-- <div :class="$style.tip">
            （建议尺寸900*500像素，支持png、jpeg、jpg格式，最大不能超过1M）
          </div> -->
          </div>
          <!-- <div :class="$style.cover__icon">
            <i class="iconfont">&#xe645;</i>
            <div :class="$style.text" @click="handlImageCoverDialog">
              {{ $t('marketing.commons.xzfmmb_9fd03b') }}
            </div>
            <MeetingCoverImageDialog
              :visible="meetingCoverlDialog"
              @onClose="meetingCoverlDialog = false"
              @selectMeetingCover="selectMeetingCover"
            ></MeetingCoverImageDialog>
          </div> -->
        </div>
        <div :class="$style.cover__tips">
          {{ $t('marketing.commons.jyccxszcgs_326c37') }}
          <div :class="$style.cover__standard">
            <PictureStandard />
          </div>
        </div>
      </el-form-item>
      <el-form-item
        :label="$t('marketing.commons.zbsj_519953')"
        required
      >
        <Col :span="11">
          <fx-date-picker
            v-model="formData.startTime"
            type="datetime"
            prop="startTime"
            :placeholder="$t('marketing.commons.kssj_592c59')"
            :disabled="datePickerDisabled"
            format="yyyy-MM-dd HH:mm"
            value-format="timestamp"
            default-time="09:00:00"
            style="width: 100%;"
            @change="val => handleTimeChange('start', val)"
          />
        </Col>
        <Col
          style="text-align: center"
          :span="2"
        >
          {{ $t('marketing.commons.z_981cbe') }}
        </Col>
        <Col :span="11">
          <fx-date-picker
            v-model="formData.endTime"
            type="datetime"
            prop="endTime"
            :placeholder="$t('marketing.commons.jssj_f78277')"
            format="yyyy-MM-dd HH:mm"
            value-format="timestamp"
            default-time="09:00:00"
            :picker-options="endTimePickerOptions"
            :disabled="!!liveDetail.vhallId || datePickerDisabled"
            style="width: 100%;"
            @change="val => handleTimeChange('end', val)"
          />
        </Col>
      </el-form-item>
      <fx-input
        v-model="formData.lectureUserName"
        :placeholder="$t('marketing.commons.qsr_02cc4f')"
        :maxlength="32"
        show-word-limit
        :label="$t('marketing.pages.live_marketing.zbjs_d265ca')"
        prop="lectureUserName"
      />

      <el-form-item
        :label="$t('marketing.pages.live_marketing.xzzbpt_e7d975')"
        prop="livePlatform"
      >
        <div :class="$style.wrap">
          <div
            v-if="vDatas.uinfo.livePluginConfigs[LIVE_PLUGIN_TYPE_ENUM.MUDU].status"
            :class="$style.itemWrap"
          >
            <div
              :class="$style.item"
              @click="formData.livePlatform = LIVE_PLATFORM_ENUM.MUDU"
            >
              {{ $t('marketing.pages.live_marketing.mdzbpt_f99386') }}
              <div :class="$style.tip">
                {{ $t('marketing.pages.live_marketing.glmdsyzbhd_f2ffe9') }}
              </div>
              <div
                v-show="formData.livePlatform == LIVE_PLATFORM_ENUM.MUDU"
                :class="$style.hover"
              >
                <div :class="$style.topLeft">
                  <img
                    :src="require('@/assets/images/check-true.png')"
                    alt=""
                  >
                </div>
              </div>
            </div>
            <div
              v-show="!!liveDetail.livePlatform"
              :class="$style.disable"
            />
          </div>

          <div
            v-if="vDatas.uinfo.livePluginConfigs[LIVE_PLUGIN_TYPE_ENUM.POLYV].status"
            :class="$style.itemWrap"
          >
            <div
              :class="$style.item"
              @click="formData.livePlatform = LIVE_PLATFORM_ENUM.POLYV"
            >
              {{ $t('marketing.pages.live_marketing.blwzbpt_f3d2b8') }}
              <div :class="$style.tip">
                {{ $t('marketing.pages.live_marketing.glblwsyzbh_c51185') }}
              </div>
              <!-- <a
                @click="toOpenPolyv"
                :class="$style.link"
                v-show="!this.vDatas.uinfo['bindPolyvEnabled']"
                >开通保利威直播</a
              > -->
              <div
                v-show="formData.livePlatform == LIVE_PLATFORM_ENUM.POLYV"
                :class="$style.hover"
              >
                <div :class="$style.topLeft">
                  <img
                    :src="require('@/assets/images/check-true.png')"
                    alt=""
                  >
                </div>
              </div>
            </div>
            <div
              v-show="!!liveDetail.livePlatform"
              :class="$style.disable"
            />
          </div>

          <div
            v-if="vDatas.uinfo.livePluginConfigs[LIVE_PLUGIN_TYPE_ENUM.XIAOE].status"
            :class="$style.itemWrap"
          >
            <div
              :class="$style.item"
              @click="formData.livePlatform = LIVE_PLATFORM_ENUM.XIAOE"
            >
              {{ $t('marketing.pages.live_marketing.xetzbpt_61e2a5') }}
              <div :class="$style.tip">
                {{ $t('marketing.pages.live_marketing.glxetsyzbh_1c9361') }}
              </div>
              <!-- <a
                href="https://www.xiaoe-tech.com"
                target="_blank"
                :class="$style.link"
                >开通小鹅通直播</a
              > -->
              <div
                v-show="formData.livePlatform == LIVE_PLATFORM_ENUM.XIAOE"
                :class="$style.hover"
              >
                <div :class="$style.topLeft">
                  <img
                    :src="require('@/assets/images/check-true.png')"
                    alt=""
                  >
                </div>
              </div>
            </div>
            <div
              v-show="!!liveDetail.livePlatform"
              :class="$style.disable"
            />
          </div>

          <div
            v-if="vDatas.uinfo.livePluginConfigs[LIVE_PLUGIN_TYPE_ENUM.VHALL].status"
            :class="$style.itemWrap"
          >
            <div
              :class="$style.item"
              @click="formData.livePlatform = LIVE_PLATFORM_ENUM.VHALLPLUS"
            >
              {{ $t('marketing.pages.live_marketing.whzbpt_067c71') }}
              <div :class="$style.tip">
                {{ $t('marketing.pages.live_marketing.glwhsyzbhd_334d3d') }}
              </div>
              <div
                v-show="formData.livePlatform == LIVE_PLATFORM_ENUM.VHALLPLUS"
                :class="$style.hover"
              >
                <div :class="$style.topLeft">
                  <img
                    :src="require('@/assets/images/check-true.png')"
                    alt=""
                  >
                </div>
              </div>
            </div>
            <div
              v-show="!!liveDetail.livePlatform"
              :class="$style.disable"
            />
          </div>

          <div
            v-if="vDatas.uinfo.livePluginConfigs[LIVE_PLUGIN_TYPE_ENUM.CHANNELS].status"
            :class="$style.itemWrap"
          >
            <div
              :class="$style.item"
              @click="formData.livePlatform = LIVE_PLATFORM_ENUM.CHANNELS"
            >
              {{ $t('marketing.pages.live_marketing.wxsphzb_56f2f3') }}
              <!-- <span
                style="color:#91959E; font-size: 12px"
                v-if="!$route.query.id"
              >
                (剩余流量:
                <span
                  v-loading="flowLoading"
                  element-loading-spinner="el-icon-loading"
                  >{{ leftFlow }}G</span
                >)
              </span> -->
              <div :class="$style.tip">
                {{ $t('marketing.pages.live_marketing.hqyhbmsjhh_33fd9c') }}
                <div style="margin-top:5px;">
                  <a
                    style="cursor:pointer;"
                    @click.stop="cationVsible = true"
                  >{{ $t('marketing.pages.live_marketing.zysx_1bbbb2') }}</a>
                </div>
              </div>
              <div
                v-show="formData.livePlatform == LIVE_PLATFORM_ENUM.CHANNELS"
                :class="$style.hover"
              >
                <div :class="$style.topLeft">
                  <img
                    :src="require('@/assets/images/check-true.png')"
                    alt=""
                  >
                </div>
              </div>
            </div>
            <div
              v-show="!!liveDetail.livePlatform"
              :class="$style.disable"
            />
          </div>

          <div :class="$style.itemWrap">
            <div
              :class="$style.item"
              @click="formData.livePlatform = LIVE_PLATFORM_ENUM.OTHER"
            >
              {{ $t('marketing.pages.live_marketing.qtzbpt_611f66') }}
              <div :class="$style.tip">
                {{ $t('marketing.pages.live_marketing.khqyhbmsjw_0cec73') }}
              </div>
              <div
                v-show="formData.livePlatform == LIVE_PLATFORM_ENUM.OTHER"
                :class="$style.hover"
              >
                <div :class="$style.topLeft">
                  <img
                    :src="require('@/assets/images/check-true.png')"
                    alt=""
                  >
                </div>
              </div>
            </div>
            <div
              v-show="!!liveDetail.livePlatform"
              :class="$style.disable"
            />
          </div>
        </div>
      </el-form-item>

      <!--      <el-form-item label="选择直播平台：" prop="livePlatform">-->
      <!--        <RadioGroup-->
      <!--          v-model="formData.livePlatform"-->
      <!--          :disabled="!!liveDetail.livePlatform"-->
      <!--        >-->
      <!--          <Radio :label="4" :disabled="!this.vDatas.uinfo['bindPolyvEnabled']">-->
      <!--            保利威直播平台-->
      <!--            <div :class="$style.tip">-->
      <!--              对接保利威直播，获取用户直播观看信息-->
      <!--            </div>-->
      <!--            <a-->
      <!--              @click="toOpenPolyv"-->
      <!--              :class="$style.link"-->
      <!--              v-show="!this.vDatas.uinfo['bindPolyvEnabled']"-->
      <!--            >开通保利威直播</a>-->
      <!--          </Radio>-->
      <!--          <Radio :label="3">-->
      <!--            小鹅通直播平台-->
      <!--            <div :class="$style.tip">-->
      <!--              对接小鹅通直播，获取用户直播观看信息-->
      <!--            </div>-->
      <!--            <a-->
      <!--              href="https://www.xiaoe-tech.com"-->
      <!--              target="_blank"-->
      <!--              :class="$style.link"-->
      <!--              >开通小鹅通直播</a-->
      <!--            >-->
      <!--          </Radio>-->
      <!--          <Radio :label="1">-->
      <!--            微吼直播平台-->
      <!--            <div :class="$style.tip">-->
      <!--              获得更好的直播体验与直播全链条用户行为数据-->
      <!--            </div>-->
      <!--            <div :class="$style.flow" v-if="!$route.query.id">-->
      <!--              剩余可用流量：-->
      <!--              <span-->
      <!--                v-loading="flowLoading"-->
      <!--                element-loading-spinner="el-icon-loading"-->
      <!--                >{{ leftFlow }}G</span-->
      <!--              >-->
      <!--              <a @click.stop="handleBuyFlow">立即充值</a>-->
      <!--            </div>-->
      <!--          </Radio>-->
      <!--          <Radio :label="2" style="margin-top: 40px">-->
      <!--            其他直播平台-->
      <!--            <div :class="$style.tip">-->
      <!--              获取用户报名数据与行为，无法获得直播观看数据-->
      <!--            </div>-->
      <!--            <div>&nbsp;</div>-->
      <!--          </Radio>-->
      <!--        </RadioGroup>-->
      <!--      </el-form-item>-->

      <!-- 选择小鹅通直播平台 -->
      <template v-if="formData.livePlatform === LIVE_PLATFORM_ENUM.XIAOE">
        <el-form-item
          v-if="liveDetail.livePlatform !== LIVE_PLATFORM_ENUM.XIAOE"
          :label="$t('marketing.pages.live_marketing.xetzbhd_d2be0e')"
          prop="xiaoetongLiveId"
        >
          <div :class="$style.xiaoe">
            <div
              v-if="xiaoeDetail.id"
              :class="$style.xiaoe__noempty"
            >
              <div :class="$style.xiaoe__cover">
                <ElImage :src="xiaoeDetail.imgUrl" />
              </div>
              <div :class="$style.xiaoe__info">
                <div :class="['km-t-ellipsis1', $style.xiaoe__title]">
                  {{ xiaoeDetail.title }}
                </div>
                <div :class="$style.xiaoe__desc">
                  {{ xiaoeDetail.startTime || "--" }}
                </div>
              </div>
              <div :class="$style.xiaoe__button">
                <ElButton
                  type="text"
                  @click="handleSelectedXiaoETongLive"
                >
                  {{ $t('marketing.commons.zxxz_5a0aa4') }}
                </ElButton>
              </div>
            </div>
            <div
              v-else
              :class="$style.xiaoe__empty"
            >
              <ElButton
                type="text"
                @click="handleSelectedXiaoETongLive"
              >
                <i class="icon km-ico-add" />&nbsp;&nbsp;{{ $t('marketing.commons.xzglxetzb_69cc7c') }}
              </ElButton>
            </div>
          </div>
        </el-form-item>
        <el-form-item
          v-if="liveDetail.livePlatform === LIVE_PLATFORM_ENUM.XIAOE"
          :label="$t('marketing.pages.live_marketing.xetzblj_abd785')"
        >
          <div :class="$style.xiaoe__link">
            {{ liveDetail.xiaoetongUrl }}
          </div>
        </el-form-item>
      </template>

      <!--  保利威直播   -->
      <template v-if="formData.livePlatform === LIVE_PLATFORM_ENUM.POLYV">
        <el-form-item
          v-if="liveDetail.livePlatform !== LIVE_PLATFORM_ENUM.POLYV"
          :label="$t('marketing.pages.live_marketing.blwzbhd_03c6a2')"
          prop="xiaoetongLiveId"
        >
          <div :class="$style.xiaoe">
            <div
              v-if="polyvDetail.channelId"
              :class="$style.xiaoe__noempty"
            >
              <div :class="$style.xiaoe__cover">
                <ElImage :src="polyvDetail.splashImg" />
              </div>
              <div :class="$style.xiaoe__info">
                <div :class="['km-t-ellipsis1', $style.xiaoe__title]">
                  {{ polyvDetail.name }}
                </div>
                <div :class="$style.xiaoe__desc">
                  {{ polyvDetail.startTime || "--" }}
                </div>
              </div>
              <div :class="$style.xiaoe__button">
                <ElButton
                  type="text"
                  @click="isShowBaoliSelectorDialog = true"
                >
                  {{ $t('marketing.commons.zxxz_5a0aa4') }}
                </ElButton>
              </div>
            </div>
            <div
              v-else
              :class="$style.xiaoe__empty"
            >
              <ElButton
                type="text"
                @click="isShowBaoliSelectorDialog = true"
              >
                <i class="icon km-ico-add" />&nbsp;&nbsp;{{ $t('marketing.commons.xzglblwzb_85942e') }}
              </ElButton>
            </div>
          </div>
        </el-form-item>
        <el-form-item
          v-if="liveDetail.livePlatform === LIVE_PLATFORM_ENUM.POLYV"
          :label="$t('marketing.pages.live_marketing.blwzb_252e78')"
          prop="xiaoetongLiveId"
        >
          <!--        <div :class="$style.xiaoe__link">{{ liveDetail.xiaoetongUrl }}</div>-->
          <div
            :class="$style.xiaoe"
            style="background: #F9F9FA; cursor:not-allowed"
          >
            <div :class="$style.xiaoe__noempty">
              <div :class="$style.xiaoe__cover">
                <ElImage
                  v-if="readPolyvData.splashImg"
                  :src="readPolyvData.splashImg"
                />
              </div>
              <div :class="$style.xiaoe__info">
                <div :class="['km-t-ellipsis1', $style.xiaoe__title]">
                  {{ readPolyvData.name }}
                </div>
                <div :class="$style.xiaoe__desc">
                  {{ $t('marketing.commons.zbsj_519953') }}&nbsp;{{ readPolyvData.startTime || "--" }}
                  <span :class="$style.xiaoe__state">
                    <span :class="$style.xiaoe__point" />{{ readPolyvData.status }}</span>
                </div>
              </div>
            </div>
            <div :class="$style.layer" />
          </div>
        </el-form-item>
      </template>

      <!--  目睹直播   -->
      <template v-if="formData.livePlatform === LIVE_PLATFORM_ENUM.MUDU">
        <el-form-item
          v-if="liveDetail.livePlatform !== LIVE_PLATFORM_ENUM.MUDU"
          :label="$t('marketing.pages.live_marketing.mdzbhd_234451')"
          prop="xiaoetongLiveId"
        >
          <div :class="$style.xiaoe">
            <div
              v-if="muduDetail.eventId"
              :class="$style.xiaoe__noempty"
            >
              <div :class="$style.xiaoe__cover">
                <ElImage :src="muduDetail.coverUrl" />
              </div>
              <div :class="$style.xiaoe__info">
                <div :class="['km-t-ellipsis1', $style.xiaoe__title]">
                  {{ muduDetail.eventName }}
                </div>
                <div :class="$style.xiaoe__desc">
                  {{ muduDetail.startTime || "--" }}
                </div>
              </div>
              <div :class="$style.xiaoe__button">
                <ElButton
                  type="text"
                  @click="isMuduSelectorDialogVisible = true"
                >
                  {{ $t('marketing.commons.zxxz_5a0aa4') }}
                </ElButton>
              </div>
            </div>
            <div
              v-else
              :class="$style.xiaoe__empty"
            >
              <ElButton
                type="text"
                @click="isMuduSelectorDialogVisible = true"
              >
                <i class="icon km-ico-add" />&nbsp;&nbsp;{{ $t('marketing.commons.xzglmdzb_39ad92') }}
              </ElButton>
            </div>
          </div>
        </el-form-item>
        <el-form-item
          v-if="liveDetail.livePlatform === LIVE_PLATFORM_ENUM.MUDU"
          :label="$t('marketing.pages.live_marketing.mdzbhd_234451')"
          prop="xiaoetongLiveId"
        >
          <!--        <div :class="$style.xiaoe__link">{{ liveDetail.xiaoetongUrl }}</div>-->
          <div
            :class="$style.xiaoe"
            style="background: #F9F9FA; cursor:not-allowed"
          >
            <div :class="$style.xiaoe__noempty">
              <div :class="$style.xiaoe__cover">
                <ElImage
                  :src="muduDetail.coverUrl"
                >
                  <div
                    slot="error"
                    class="image-slot"
                  >
                    <i class="el-icon-picture-outline" />
                  </div>
                </ElImage>
              </div>
              <div :class="$style.xiaoe__info">
                <div :class="['km-t-ellipsis1', $style.xiaoe__title]">
                  {{ muduDetail.eventName }}
                </div>
                <div :class="$style.xiaoe__desc">
                  {{ $t('marketing.commons.zbsj_519953') }}&nbsp;{{ muduDetail.startTime || "--" }}
                  <span :class="$style.xiaoe__state">
                    <span :class="$style.xiaoe__point" />{{ muduDetail.status }}</span>
                </div>
              </div>
            </div>
            <div :class="$style.layer" />
          </div>
        </el-form-item>
      </template>

      <!--  微吼直播   -->
      <template v-if="formData.livePlatform === LIVE_PLATFORM_ENUM.VHALLPLUS">
        <el-form-item
          v-if="liveDetail.livePlatform !== LIVE_PLATFORM_ENUM.VHALLPLUS"
          :label="$t('marketing.pages.live_marketing.whzbhd_052f0c')"
          prop="xiaoetongLiveId"
        >
          <div :class="$style.xiaoe">
            <div
              v-if="vHallDetail.eventId"
              :class="$style.xiaoe__noempty"
            >
              <div :class="$style.xiaoe__cover">
                <ElImage :src="vHallDetail.coverUrl" />
              </div>
              <div :class="$style.xiaoe__info">
                <div :class="['km-t-ellipsis1', $style.xiaoe__title]">
                  {{ vHallDetail.eventName }}
                </div>
                <div :class="$style.xiaoe__desc">
                  {{ vHallDetail.startTime || "--" }}
                </div>
              </div>
              <div :class="$style.xiaoe__button">
                <ElButton
                  type="text"
                  @click="handleSelectedVHallLive"
                >
                  {{ $t('marketing.commons.zxxz_5a0aa4') }}
                </ElButton>
              </div>
            </div>
            <div
              v-else
              :class="$style.xiaoe__empty"
            >
              <ElButton
                type="text"
                @click="handleSelectedVHallLive"
              >
                <i class="icon km-ico-add" />&nbsp;&nbsp;{{ $t('marketing.pages.live_marketing.xzglwhzb_99e80d') }}
              </ElButton>
            </div>
          </div>
        </el-form-item>
        <el-form-item
          v-if="liveDetail.livePlatform === LIVE_PLATFORM_ENUM.VHALLPLUS"
          :label="$t('marketing.pages.live_marketing.whzbhd_052f0c')"
          prop="xiaoetongLiveId"
        >
          <!--        <div :class="$style.xiaoe__link">{{ liveDetail.xiaoetongUrl }}</div>-->
          <div
            :class="$style.xiaoe"
            style="background: #F9F9FA; cursor:not-allowed"
          >
            <div :class="$style.xiaoe__noempty">
              <div :class="$style.xiaoe__cover">
                <img style="width: 100%;height: 100%;" :src="vHallDetail.coverUrl">
              </div>
              <div :class="$style.xiaoe__info">
                <div :class="['km-t-ellipsis1', $style.xiaoe__title]">
                  {{ vHallDetail.eventName }}
                </div>
                <div :class="$style.xiaoe__desc">
                  {{ $t('marketing.commons.zbsj_519953') }}&nbsp;{{ vHallDetail.startTime || "--" }}
                  <span :class="$style.xiaoe__state">
                    <span :class="$style.xiaoe__point" />{{ vHallDetail.status }}</span>
                </div>
              </div>
            </div>
            <div :class="$style.layer" />
          </div>
        </el-form-item>
      </template>
      <!-- 当是编辑状态并且视频号有值的时候才禁用 （存在创建活动失败需要补充信息的场景） -->
      <fx-select
          v-if="formData.livePlatform === LIVE_PLATFORM_ENUM.CHANNELS"
          prop="associatedAccountId"
          :label="$t('marketing.commons.sph_20f8a1')"
          :value="formData.associatedAccountId"
          :disabled="$route.query.id && formData.associatedAccountId"
          :placeholder="$t('marketing.commons.qxz_708c9d')"
          :options="channelsOptions"
          @change="associatedAccountIdChange"
        />

      <!-- 选择其他直播 -->
      <el-form-item
        v-if="
          formData.livePlatform !== LIVE_PLATFORM_ENUM.XIAOE
            && formData.livePlatform !== LIVE_PLATFORM_ENUM.POLYV
            && formData.livePlatform !== LIVE_PLATFORM_ENUM.MUDU
            && formData.livePlatform !== LIVE_PLATFORM_ENUM.VHALLPLUS
        "
        :label="$t('marketing.pages.live_marketing.zbjj_e5e9b3')"
        prop="desc"
      >
        <el-input
          v-model="formData.desc"
          type="textarea"
          :placeholder="$t('marketing.commons.qsr_02cc4f')"
          :maxlength="1024"
          :rows="3"
          show-word-limit
        />
      </el-form-item>

      <!-- 选择其他直播平台 -->
      <template v-if="formData.livePlatform == LIVE_PLATFORM_ENUM.OTHER">
        <el-form-item
          :label="$t('marketing.commons.zblj_625d5a')"
          prop="otherPlatformLiveUrl"
        >
          <el-input
            v-model="formData.otherPlatformLiveUrl"
            placeholder="https://"
          />
        </el-form-item>
      </template>

      <el-form-item
        v-if="!$route.query.id"
        prop="marketingTemplateId"
      >
        <div
          slot="label"
          style="display: inline-block;"
        >
          <div style="display: flex;align-items: center;">
            <span style="margin-right: 2px;">{{ $t('marketing.pages.live_marketing.zbzymb_3cb55b') }}</span>
            <span style="cursor: pointer;display: inline-block;fontsize: 14px;" class="fx-icon-question" @click="handleOpenHelp('https://help.fxiaoke.com/93d5/9188/78fd/8c7a','_blank')"></span>
          </div>
        </div>
        <div style="position: relative;" v-if="hexagonLoading">
          <div class="km-g-loading-mask">
            <span class="loading"></span>
          </div>
        </div>
        <el-select
          v-model="formData.marketingTemplateId"
          :options="liveFormTemplateList"
          style="margin-bottom: 0;"
          v-else
        >
          <template
            slot="options"
            slot-scope="slotProps"
          >
            <span
              v-if="slotProps.data === 'create'"
              class="hexagon__btn"
              @click="goToMarketingEventSet"
            >{{ $t('marketing.commons.xjmb_156a04') }}+</span>
            <el-button
              v-else-if="slotProps.data === 'update'"
              :loading="hexagonLoading"
              class="hexagon__btn hexagon__refresh"
              @click="getSceneHexagonTemplates"
            >
              {{ $t('marketing.commons.sxmblb_370baa') }}
            </el-button>
            <span v-else>{{ slotProps.data.label }}</span>
          </template>

          <!-- <el-option
            v-for="item in liveFormTemplateList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
          <span
            class="hexagon__btn"
            @click="goToMarketingEventSet"
          >{{ $t('marketing.commons.xjmb_156a04') }}+</span>
          <el-button
            :loading="hexagonLoading"
            class="hexagon__btn hexagon__refresh"
            @click="getSceneHexagonTemplates"
          >
            {{ $t('marketing.commons.sxmblb_370baa') }}
          </el-button> -->
        </el-select>
      </el-form-item>
      <!-- 活动形式 -->
       <el-form-item
        :label="$t('marketing.commons.hdlx_13955e')"
        class="activity__label"
      >
        <eventTypeSelector
          :type="1"
          :default-value="formData.eventType"
          v-if="!$route.query.id || formData.eventType"
          @change="handleEventTypeChange"
        />
      </el-form-item>
      <el-form-item
        :label="$t('marketing.commons.sfzs_f3387f')"
        class="activity__label"
      >
        <Checkbox
          v-model="formData.isMobileDisplay"
          v-loading="isMobileDisplayLoading"
          :checked="$route.query.id ? formData.isMobileDisplay : 'checked'"
        >
          {{ $t('marketing.commons.gxdqhdjhts_919cef') }}
        </Checkbox>
      </el-form-item>
    </el-form>
    <meeting-crm-filed
      ref="crmFiled"
      :field-data="crmFiledData"
      :hide-fields="hideFields"
      type="live"
    />
    <AddCoverDialog
      v-if="coverDialogVisible"
      :visible.sync="coverDialogVisible"
      :showing-image="coverImage"
      @showImage="showImage"
    />
    <XiaoeSelector
      :visible.sync="isShowXiaoeSelectorDialog"
      :live-type="currentLiveType"
      @submit="handleXiaoeSelectorSubmit"
    />

    <BaoliSelector
      :visible.sync="isShowBaoliSelectorDialog"
      @submit="handleBaoliSelectorSubmit"
    />

    <MuduSeletorDialog
      :visible.sync="isMuduSelectorDialogVisible"
      @submit="handleMuduSelectorSubmit"
    />

    <VDialog
      :title="$t('marketing.pages.live_marketing.cjwxsphzbz_ee26bf')"
      :visible.sync="cationVsible"
      width="500px"
      :cancel-text="$t('marketing.commons.wzdl_fe0337')"
      :show-confirm="false"
      @onClose="cationVsible = false"
    >
      <div class="video-live">
        <div class="item">
          1.{{ $t('marketing.pages.live_marketing.qzzbhdkssj_cf43cb') }}
        </div>
        <div class="item">
          2.{{ $t('marketing.pages.live_marketing.gsphzbhdks_98d9da') }}
        </div>
      </div>
    </VDialog>
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex'
import http from '@/services/http/index.js'
import PageHeader from '@/components/page-header/index.vue'
import AddCoverDialog from '@/pages/meeting-marketing/meeting-detail/add-cover.vue'
import PictureCutter from '@/components/picture-cutter/index.vue'
import TagSelectorLine from '@/components/tags-selector-new/tags-line.vue'
import MeetingCrmFiled from '@/components/meeting-crm-filed/index.vue'
import XiaoeSelector from '@/components/XiaoeSelector/index.vue'
import BaoliSelector from '@/components/BaoliSelector/index.vue'
import MuduSeletorDialog from './components/mudu-selector-dialog.vue'
import util from '@/services/util/index.js'
import kisvData from '@/modules/kisv-data.js'
import VDialog from '@/components/dialog/index.vue'
import QuestionTooltip from '@/components/questionTooltip/index.vue'
import PictureStandard from '@/components/PictureStandard/index.vue'
import { LIVE_PLUGIN_TYPE_ENUM } from '@/pages/setting/setitems/marketing-plugin/plugin-map.js'
import eventTypeSelector from '@/components/eventTypeSelector/index.vue'

export const LIVE_PLATFORM_ENUM = {
  VHALL: 1,
  OTHER: 2,
  XIAOE: 3,
  POLYV: 4,
  CHANNELS: 5,
  MUDU: 6,
  // 新版微吼
  VHALLPLUS: 7,
}

const platFormMap = new Map([
  [LIVE_PLUGIN_TYPE_ENUM.MUDU, LIVE_PLATFORM_ENUM.MUDU],
  [LIVE_PLUGIN_TYPE_ENUM.POLYV, LIVE_PLATFORM_ENUM.POLYV],
  [LIVE_PLUGIN_TYPE_ENUM.XIAOE, LIVE_PLATFORM_ENUM.XIAOE],
  [LIVE_PLUGIN_TYPE_ENUM.VHALL, LIVE_PLATFORM_ENUM.VHALLPLUS],
  [LIVE_PLUGIN_TYPE_ENUM.CHANNELS, LIVE_PLATFORM_ENUM.CHANNELS],
])
const platFormShowMap = {
  [LIVE_PLATFORM_ENUM.POLYV]: LIVE_PLUGIN_TYPE_ENUM.POLYV,
  [LIVE_PLATFORM_ENUM.XIAOE]: LIVE_PLUGIN_TYPE_ENUM.XIAOE,
  [LIVE_PLATFORM_ENUM.VHALL]: LIVE_PLUGIN_TYPE_ENUM.VHALL,
  [LIVE_PLATFORM_ENUM.CHANNELS]: LIVE_PLUGIN_TYPE_ENUM.CHANNELS,
  [LIVE_PLATFORM_ENUM.MUDU]: LIVE_PLUGIN_TYPE_ENUM.MUDU,
  [LIVE_PLUGIN_TYPE_ENUM.VHALLPLUS]: LIVE_PLATFORM_ENUM.VHALLPLUS,
}

export default {
  components: {
    PageHeader,
    ElForm: FxUI.Form,
    ElFormItem: FxUI.FormItem,
    ElInput: FxUI.Input,
    ElSelect: FxUI.Select,
    ElSwitch: FxUI.Switch,
    ElButton: FxUI.Button,
    ElImage: FxUI.Image,
    Checkbox: FxUI.Checkbox,
    ElDatePicker: FxUI.DatePicker,
    Col: FxUI.Col,
    AddCoverDialog,
    PictureCutter,
    TagSelectorLine,
    MeetingCrmFiled,
    XiaoeSelector,
    BaoliSelector,
    VDialog,
    QuestionTooltip,
    PictureStandard,
    MuduSeletorDialog,
    eventTypeSelector
  },
  beforeRouteEnter(to, from, next) {
    // _enterFrom = from.name
    next()
  },
  data() {
    function getLivePlatform() {
      const plat = [...platFormMap.keys()].find(key => kisvData.datas.uinfo.livePluginConfigs[key].status)
      return plat ? platFormMap.get(plat) : 2
    }

    const livePlatform = getLivePlatform()

    return {
      vDatas: kisvData.datas,
      LIVE_PLUGIN_TYPE_ENUM,
      LIVE_PLATFORM_ENUM,
      hideFields: ['begin_time', 'end_time', 'name', 'event_type', 'owner'],
      crmFiledData: {},
      sending: false,
      flowLoading: false,
      platFormShowMap,
      formData: {
        title: '',
        startTime: '',
        endTime: '',
        coverTaPath: '',
        ext: '',
        lectureUserName: '',
        lecturePassword: '0000',
        lecturePasswordOn: 1,
        livePlatform,
        chatOn: 0,
        liveLimit: 1,
        maxLiveCount: '',
        // password: "",
        autoRecord: 1,
        tagNames: [],
        otherPlatformLiveUrl: '',
        showActivityList: false,
        marketingTemplateId: '',
        originalImageAPath: '',
        cutOffsetList: null,
        isMobileDisplay: true,
        xiaoetongLiveId: '',
        eventType: '',
      },
      isMobileDisplayLoading: false,
      formRules: {
        title: [
          {
            required: true,
            message: $t('marketing.pages.live_marketing.qsrzbbt_c77229'),
            trigger: 'change',
            validator: (_, value, callback) => {
              if (value.trim()) {
                callback()
              }
              callback(new Error($t('marketing.commons.qsrtgbt_9d7a45')))
            },
          },
        ],
        lectureUserName: [
          { required: true, message: $t('marketing.pages.live_marketing.qtxzbjs_001f36'), trigger: 'change' },
        ],
        lecturePassword: [
          {
            required: true,
            trigger: 'change',
            validator: (rule, value, callback) => {
              if (!value) {
                callback(new Error($t('marketing.pages.live_marketing.qsrjszbmm_f9d784')))
              } else if (value.length < 4) {
                callback(new Error($t('marketing.pages.live_marketing.qsrwszdjsz_688159')))
              } else {
                callback()
              }
            },
          },
        ],
        startTime: [
          { required: true, message: $t('marketing.commons.qxzkssj_90fae3'), trigger: 'change' },
        ],
        endTime: [
          { required: true, message: $t('marketing.pages.live_marketing.qxzjssj_69b76a'), trigger: 'change' },
        ],
        originalImageAPath: [
          {
            required: true,
            trigger: 'change',
            validator: (rule, value, callback) => {
              if (value || this.formData.coverTaPath) {
                callback()
              } else {
                callback(new Error($t('marketing.pages.live_marketing.qsczbfm_2d0221')))
              }
            },
          },
        ],
        otherPlatformLiveUrl: [
          {
            required: true,
            trigger: 'change',
            validator: (rule, value, callback) => {
              if (this.formData.livePlatform === 2 && !value) {
                callback(new Error($t('marketing.pages.live_marketing.qtxzbljdz_2c3b19')))
              } else if (
                this.formData.livePlatform === 2
                && value.match(
                  /(http(s)?:\/\/.)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/g,
                ) == null
              ) {
                callback(
                  new Error(
                    `${$t('marketing.commons.qsrzqdwylj_ecfa59')}https://www.fxiaoke.com`,
                  ),
                )
              } else {
                callback()
              }
            },
          },
        ],
        xiaoetongLiveId: [
          {
            required: true,
            trigger: 'change',
            validator: (rule, value, callback) => {
              if (
                this.formData.livePlatform === LIVE_PLATFORM_ENUM.XIAOE
                && !this.formData.xiaoetongLiveId
              ) {
                callback(new Error($t('marketing.pages.live_marketing.xzxetzbhd_3427fc')))
                return
              }

              if (
                this.formData.livePlatform === LIVE_PLATFORM_ENUM.MUDU
                && !this.formData.xiaoetongLiveId
              ) {
                callback(new Error($t('marketing.pages.live_marketing.qxzmdzbhd_d679e8')))
                return
              }

              if (
                this.formData.livePlatform === LIVE_PLATFORM_ENUM.POLYV
                && !this.formData.xiaoetongLiveId
              ) {
                callback(new Error($t('marketing.pages.live_marketing.qxzblwzbhd_01b29a')))
                return
              }
              if (
                this.formData.livePlatform === LIVE_PLATFORM_ENUM.VHALLPLUS
                && !this.formData.xiaoetongLiveId
              ) {
                callback(new Error($t('marketing.pages.live_marketing.qxzwhzbhd_dce2fc')))
                return
              }

              callback()
            },
          },
        ],
        marketingTemplateId: [
          {
            required: true,
            message: $t('marketing.commons.qxzbdmb_c41142'),
            trigger: 'change',
          },
        ],
        associatedAccountId: [
          {
            required: true,
            trigger: 'change',
            validator: (rule, value, callback) => {
              if (this.formData.livePlatform === LIVE_PLATFORM_ENUM.CHANNELS && !this.formData.associatedAccountId) {
                callback(new Error($t('marketing.commons.qxzsph_6a181b')))
                return
              }
              callback()
            },
          },
        ],
      },
      coverDialogVisible: false,
      coverImage: '',
      // leftFlow: 0,
      startTimePickerOptions: {
        // disabledDate: date => {
        //   const newDate = new Date()
        //   const newStartTime = new Date(
        //     newDate.getFullYear(),
        //     newDate.getMonth(),
        //     newDate.getDate(),
        //   )
        //   if (date < newStartTime) {
        //     return true
        //   }
        //   return false
        // },
      },
      endTimePickerOptions: {
        disabledDate: date => {
          const { startTime } = this.formData
          if (startTime) {
            const newDate = new Date(startTime)
            const newStartTime = new Date(
              newDate.getFullYear(),
              newDate.getMonth(),
              newDate.getDate(),
            )
            if (date < newStartTime) {
              return true
            }
          }
          return false
        },
      },
      liveDetail: {},
      xiaoeDetail: {},
      polyvDetail: {},
      muduDetail: {},
      vHallDetail: {},
      meetingCoverlDialog: false,
      isShowXiaoeSelectorDialog: false,
      isShowBaoliSelectorDialog: false,
      isMuduSelectorDialogVisible: false,
      readPolyvData: '',
      cationVsible: false,
      allTemplateData: null,
      hexagonLoading: false,
      liveFormTemplateList: [],
      currentLiveType: 'xiaoe',
    }
  },
  computed: {
    ...mapState('WechatVideo', ['accountList', 'defaultAccountData']),
    channelsOptions() {
      return this.accountList.map(a => ({
        value: a.id,
        label: a.channelsName,
      }))
    },
    datePickerDisabled() {
      // 目睹直播时间使用目睹自身的，不可编辑
      if (this.formData.livePlatform === LIVE_PLATFORM_ENUM.MUDU && this.muduDetail && this.muduDetail.eventId) {
        return true
      }

      return false
    },
  },
  watch: {
    'formData.livePlatform': {
      handler(val) {
        // 新建状态下 可以选择主页模版
        if (!this.$route.query.id) {
          if (val === LIVE_PLATFORM_ENUM.CHANNELS) {
            this.formData.associatedAccountId = this.defaultAccountData.id
          } else {
            this.formData.associatedAccountId = ''
          }
        }
        // 如果曾经进行校验过，则切换直播平台时，要触发校验
        if (this.flag_validated) {
          this.$refs.form.validate()
        }
        // 添加模板数据处理
        this.processTemplateData()
      },
      immediate: true
    },
  },
  created() {
    // this.queryLiveLeftFlow();
    this.queryDetail()
    this.queryWechatVideoAccount()
  },
  methods: {
    ...mapActions('WechatVideo', ['queryWechatVideoAccount']),
    associatedAccountIdChange(val) {
      this.settingAssociatedAccountId(val)
    },
    showMessageWarning() {
      // 直播错误提示
      const errorNode1 = $('.el-form-item__error')
      if (errorNode1.length) {
        const message = errorNode1[0].innerHTML
        FxUI.Message.error(message)
        return
      }
      // 市场活动必填提示
      const errorNode = $('.fm-error, .ui-error')
      if (errorNode.length) {
        const message = errorNode[0].innerHTML
        FxUI.Message.error(message)
      }
    },
    handleTimeChange(type, val) {
      const { startTime, endTime } = this.formData
      const disTime = 2 * 3600000
      if (type === 'start' && !endTime) {
        this.formData.endTime = val + disTime
      } else if (type === 'end' && !startTime) {
        this.formData.startTime = val - disTime
      }
      if (this.formData.endTime <= this.formData.startTime) {
        if (type === 'start') {
          this.formData.endTime = this.formData.startTime + disTime
        } else {
          this.formData.startTime = this.formData.endTime - disTime
        }
      }
    },
    handlePasswordChange(val) {
      this.formData.lecturePassword = val.replace(/\D/g, '')
    },
    handleMaxLiveCount(val) {
      let count = val
      if (val !== '') {
        if (val < 1) {
          count = 1
        } else if (val > 5000) {
          count = 5000
        }
      }
      this.formData.maxLiveCount = count
    },
    // queryLiveLeftFlow() {
    //   this.flowLoading = true;
    //   http.queryLiveLeftFlow().then(({ errCode, data }) => {
    //     this.flowLoading = false;
    //     if (errCode === 0) {
    //       this.leftFlow = data || 0;
    //     }
    //   });
    // },
    switchStatus(status) {
      switch (status) {
        case 'live':
        case '1':
          return $t('marketing.commons.zbz_2cb2b1')
        case 'playback':
          return $t('marketing.pages.live_marketing.hfz_efbd12')
        case 'end':
        case '2':
          return $t('marketing.commons.yjs_047fab')
        case 'waiting':
        case '0':
          return $t('marketing.pages.live_marketing.ddz_65dd9e')
        default:
          return '- -'
      }
    },
    async queryDetail() {
      const { id } = this.$route.query
      this.getSceneHexagonTemplates()
      if (!id) return
      this.isMobileDisplayLoading = true
      http.queryLiveDetail({ id }).then(({ errCode, data = {} }) => {
        if (errCode === 0) {
          data = {
            livePlatform: 1,
            ...data,
          }
          this.liveDetail = JSON.parse(JSON.stringify(data))
          this.queryMarketingEventDetail(data.marketingEventId)
          this.formData = {
            ...this.formData,
            ...data,
            livePlatform: data.livePlatform || 1,
            autoRecord: data.autoRecord || !data.livePlatform ? 1 : 0,
            chatOn: data.chatOn || !data.chatOn ? 0 : 1,
            lecturePasswordOn:
              data.lecturePassword || !data.livePlatform ? 1 : 0,
            maxLiveCount: (data.maxLiveCount && data.maxLiveCount) || '',
          }
          this.settingAssociatedAccountId(data.associatedAccountId)
          this.formData.showActivityList = data.livePlatform
            ? data.showActivityList
            : true
          this.coverImage = this.formData.coverTaPath

          //  保利威直播
          if (data.livePlatform === LIVE_PLATFORM_ENUM.POLYV) {
            http
              .polyvGetLiveInfo({
                id: data.xiaoetongLiveId,
              })
              .then(res => {
                this.readPolyvData = {
                  ...res.data,
                  startTime: util.formatDateTime(
                    res.data.startTime,
                    'YYYY-MM-DD hh:mm',
                  ),
                  // 兼容返回http的情况
                  splashImg: /^(http|https):/.test(res.data.splashImg) ? res.data.splashImg : `http:${res.data.splashImg}`,
                  status: this.switchStatus(res.data.watchState),
                }
              })
          }

          if (data.livePlatform === LIVE_PLATFORM_ENUM.MUDU) {
            http.fetchMuduLiveDetail({ id: data.xiaoetongLiveId })
              .then(res => {
                if (res.errCode === 0) {
                  this.muduDetail = {
                    ...res.data,
                    status: this.switchStatus(`${res.data.eventStatus}`),
                  }
                }
              })
          }
          if (data.livePlatform === LIVE_PLATFORM_ENUM.VHALLPLUS) {
            http.getVHallLiveDetail({ id: data.xiaoetongLiveId }).then(res => {
              if (res && res.errCode === 0 && res.data) {
                this.vHallDetail = {
                  ...res.data,
                  status: ['', $t('marketing.commons.zbz_2cb2b1'), $t('marketing.commons.yg_1cbb42'), $t('marketing.commons.yjs_047fab'), $t('marketing.commons.yjs_047fab'), $t('marketing.commons.yjs_047fab')][res.data.eventStatus] || '--', // 直播状态：1、直播  2： 预告 3：已结束
                }
              }
            })
          }
        }
      })
    },
    // handleBuyFlow(e) {
    //   if (e) {
    //     e.preventDefault();
    //     e.stopPropagation();
    //   }
    //   // return Message.warning('充值暂时关闭，请联系客服')
    //   const url = redirectToFS(
    //     "#app/payment/orderindex/=/param-" +
    //       encodeURIComponent(
    //         JSON.stringify({
    //           gid: "GOODS_TRAINING_LIVE_FLOW",
    //           cburl:
    //             location.protocol +
    //             "//" +
    //             location.host +
    //             location.pathname +
    //             "#app/train/setting/setting/=/stype-5"
    //         })
    //       ),
    //     "_return"
    //   );
    //   window.open(url);
    // },
    settingAssociatedAccountId(id) {
      if (this.channelsOptions.some(item => item.value === id)) {
        this.formData.associatedAccountId = id
      } else {
        this.formData.associatedAccountId = ''
      }
    },
    showImage(params) {
      this.coverImage = params.url
      this.formData.coverTaPath = params.tapath
      this.$refs.form.validateField('coverTaPath')
    },
    handlePictureCutterChange(file) {
      this.coverImage = file.cutOffsetList[0].image
      this.formData.coverTaPath = file.cutOffsetList[0].path || file.cropApath
      this.formData.originalImageAPath = file.photoPath || file.path
      this.formData.cutOffsetList = file.cutOffsetList
      this.$refs.form.validateField('originalImageAPath')
    },
    handleXiaoeSelectorSubmit(res) {
      console.log('res: ', res)
      if (this.currentLiveType === 'vhall') {
        this.vHallDetail = res
        this.formData.xiaoetongLiveId = res.id
      } else if (this.currentLiveType === 'xiaoe') {
        this.xiaoeDetail = res
        this.formData.xiaoetongLiveId = res.id
        this.formData.xiaoetongLiveUrl = res.pageUrl
      }
      // this.formData.otherPlatformLiveUrl = res.pageUrl;
    },
    handleBaoliSelectorSubmit(res) {
      console.log('handleBaoliSelectorSubmit', res)
      this.polyvDetail = {
        ...res,
        startTime: util.formatDateTime(res.startTime, 'YYYY-MM-DD hh:mm'),
      }
      this.formData.xiaoetongLiveId = res.channelId
      this.formData.xiaoetongLiveUrl = res.watchUrl
    },
    handleMuduSelectorSubmit(res) {
      console.log('handleMuduSelectorSubmit >>>', res)
      const { startTime, endTime, eventId } = res
      this.muduDetail = res
      this.formData.xiaoetongLiveId = eventId
      this.formData.startTime = Date.parse(startTime)
      this.formData.endTime = Date.parse(endTime)
    },
    queryMarketingEventDetail(id) {
      FS.util.FHHApi(
        {
          url: '/EM1HNCRM/API/v1/object/MarketingEventObj/controller/Detail',
          data: {
            objectDescribeApiName: 'MarketingEventObj',
            objectDataId: id,
          },
          success: ({ Result: { StatusCode }, Value }) => {
            if (StatusCode === 0) {
              this.crmFiledData = Value.data
              this.formData.isMobileDisplay = Value.data && Value.data.is_mobile_display === '0'
              this.isMobileDisplayLoading = false
            }
          },
        },
        {
          errorAlertModel: 1,
        },
      )
    },
    handleSend() {
      const { form } = this.$refs
      // if (this.formData.livePlatform === 1 && this.leftFlow <= 0) {
      //   MessageBox.confirm("直播流量不足，请先购买直播流量。", "提示", {
      //     confirmButtonText: "立即购买",
      //     cancelButtonText: "取消",
      //     type: "warning"
      //   }).then(() => {
      //     this.handleBuyFlow();
      //   });
      //   return;
      // }
      // 校验市场活动必填字段
      setTimeout(() => {
        this.showMessageWarning()
      })
      const crmFiledData = this.$refs.crmFiled.submit()
      this.flag_validated = true
      form.validate().then(valid => {
        if (valid && crmFiledData) {
          // 直播的标题、时间、活动类型、负责人（当前员工）在crm表格的布局中被隐藏(hideFields中的)，提交的时候前端代码填入
          crmFiledData.begin_time = this.formData.startTime
          crmFiledData.end_time = this.formData.endTime
          crmFiledData.name = this.formData.title
          crmFiledData.event_type = this.formData.eventType // 活动类型3 会议销售
          crmFiledData.owner = [`${FS.contacts.getCurrentEmployee().id}`]
          crmFiledData.is_mobile_display = this.formData.isMobileDisplay ? '0' : '1'
          this.crmFiledData = crmFiledData
          const formData = JSON.parse(JSON.stringify(this.formData))
          if (!formData.lecturePasswordOn) {
            delete formData.lecturePassword
          }
          delete formData.lecturePasswordOn
          if (formData.livePlatform === 1 && formData.maxLiveCount !== '') {
            if (formData.maxLiveCount > 5000 || formData.maxLiveCount < 1) {
              FxUI.Message.error($t('marketing.pages.live_marketing.xzgkrsjzcf_9c182f'))
              return
            }
          } else {
            delete formData.maxLiveCount
          }
          formData.createObjectDataModel = { objectData: this.crmFiledData }
          this.sending = true
          formData.parentId = this.$route.query.parent_id || ''
          http.createOrUpdateLive(formData).then(({ errCode, data, errMsg }) => {
            this.sending = false
            if (errCode === 0) {
              // if (!id && data.id && data.marketingEventId) {
              //   this.$router.push({
              //     name: "live-dashboard",
              //     params: {
              //       id: data.marketingEventId
              //     },
              //     query: {
              //       id: data.id
              //     }
              //   });
              // } else {
              this.handleCancel()
              // }
            } else {
              // Message.error(errMsg)
            }
          })
        }
      })
    },
    handleCancel() {
      this.$router.back()
    },
    handlImageCoverDialog() {
      this.meetingCoverlDialog = true
    },
    selectMeetingCover(params) {
      console.log(params)
      this.coverImage = params.coverImage
      this.formData.coverTaPath = params.coverImageTAPath
      this.$emit('input', params)
    },

    toOpenPolyv() {
      this.$router.push({
        name: 'setting-setitems',
        query: {
          type: 'v-marketing-plugin',
        },
      })
    },

    async getSceneHexagonTemplates() {
      // // 如果已有缓存数据，直接处理
      // if (this.allTemplateData) {
      //   this.processTemplateData();
      //   return;
      // }

      this.hexagonLoading = true;
      try {
        const { errCode, errMsg, data = {} } = await http.getSceneHexagonTemplates();
        if (errCode === 0) {
          // 缓存数据
          this.allTemplateData = data.simpleResult || {};
          this.processTemplateData();
        } else {
          FxUI.Message.error(errMsg || $t('marketing.commons.hqmblbsb_463047'));
        }
      } catch (error) {
        FxUI.Message.error($t('marketing.commons.hqmblbsb_463047'));
      } finally {
        this.hexagonLoading = false;
      }
    },
    processTemplateData() {
      if (!this.allTemplateData) return;

      // 根据直播平台类型选择对应的模板数据
      const sceneType = this.formData.livePlatform === this.LIVE_PLATFORM_ENUM.CHANNELS 
        ? 'channel_live' 
        : 'live';

      let result = this.allTemplateData[sceneType] || [];

      // 处理默认模板排序
      result = result.slice(); // 创建副本以避免修改原数据
      const defaultIndex = result.findIndex(item => item.isDefault);
      if (defaultIndex > -1) {
        const defaultTemplate = result.splice(defaultIndex, 1)[0];
        result.unshift(defaultTemplate);
      }

      // 转换为下拉列表格式
      const liveFormTemplateList = result.map(el => ({
        ...el,
        label: el.name,
        value: el.id,
      }));

      // 添加创建和更新选项
      this.liveFormTemplateList = liveFormTemplateList.concat(['create', 'update']);
      
      // 设置默认选中的模板ID
      if (result.length > 0) {
        this.formData.marketingTemplateId = result[0].id || '';
      }
    },
    goToMarketingEventSet() {
      const routerData = this.$router.resolve({
        name: 'setting-setitems',
        query: {
          type: 'v-marketingevent-set',
        },
      })
      window.open(routerData.href, '_blank')
    },
    handleSelectedVHallLive() {
      this.currentLiveType = 'vhall'
      this.isShowXiaoeSelectorDialog = true
    },
    handleSelectedXiaoETongLive() {
      this.currentLiveType = 'xiaoe'
      this.isShowXiaoeSelectorDialog = true
    },
    handleOpenHelp(url,target){
      window.open(url || 'https://help.fxiaoke.com/93d5',target || '_blank')
    },
    handleEventTypeChange(value) {
      this.formData.eventType = value.apiName
    },
  },
}
</script>

<style lang="less" module>
.create_live_marketing_wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  .flow {
    color: #545861;
    font-size: 12px;
    a {
      margin-left: 10px;
    }
    :global {
      .el-loading-spinner {
        margin-top: -6px;
      }
    }
  }
  .form {
    width: 1172px;
    box-sizing: border-box;
    min-width: 0;
    padding: 20px 0;
    padding-left: 38px;
    :global {
      .el-radio {
        vertical-align: top;
      }
      .el-form-item__label {
        font-size: 14px;
      }
    }
  }
  .list {
    width: 100%;
  }
  .content {
    flex: 1 auto;
    height: 340px;
    line-height: initial;
    border: 1px solid @border-color-base;
    border-radius: 3px;
  }
  .tags {
    line-height: 0;
  }
  .count {
    font-size: 13px;
    color: #999999;
    position: absolute;
    right: 5px;
    top: 45px;
  }
  :global {
    .el-radio-group {
      margin-top: 12px;
      margin-bottom: 8px;
    }
    .el-form-item {
      margin-bottom: 18px;
    }
    .el-form-item__label {
      font-size: 13px;
      color: #151515;
    }
    .el-date-editor {
      display: block;
    }
  }

  .xiaoe {
    height: 80px;
    box-sizing: border-box;
    border-radius: 4px;
    border: 1px solid #e9edf5;
    overflow: hidden;
    line-height: 20px;
    .xiaoe__noempty {
      display: flex;
    }
    .xiaoe__empty {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      span {
        display: flex;
      }
    }
    .xiaoe__cover {
      width: 100px;
      height: 80px;
      display: flex;
      justify-content: center;
      align-items: center;

      :global {
        .el-image {
          width: 100%;
          height: 100%;

          .image-slot {
            width: 100%;
            height: 100%;
            font-size: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }
    .xiaoe__info {
      margin-left: 12px;
    }
    .xiaoe__title {
      font-size: 14px;
      color: #181c25;
      margin: 18px 0 0 0;
    }
    .xiaoe__desc {
      font-size: 12px;
      color: #545861;
      margin: 5px 0 0 0;
    }
    .xiaoe__button {
      margin: 0 16px 0 auto;
      display: flex;
      align-items: center;
    }
    .xiaoe__state {
      color: #f8ae3f;
      margin-left: 20px;
      display: inline-block;
      position: relative;
    }
    .xiaoe__point {
      display: inline-block;
      width: 4px;
      height: 4px;
      border-radius: 50%;
      background: #f8ae3f;
      position: absolute;
      top: 9px;
      left: -7px;
    }
  }
  .layer {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background: #eaebee;
    opacity: 0.3;
    cursor: not-allowed;
  }
  .xiaoe__link {
    white-space: normal;
    word-break: break-all;
  }
  .tip {
    font-size: 12px;
    color: #91959e;
    line-height: 1.5;
    padding: 4px 0;
  }
  .link {
    font-size: 12px;
    cursor: pointer;
    position: relative;
    z-index: 1001;
  }
  .cover__tips {
    display: flex;
    font-size: 12px;
    color: #91959e;
    line-height: 18px;
    margin-top: 12px;
    .cover__standard{
      margin-left: 5px;
    }
  }
  .create__cover {
    display: flex;
  }
  .cover__icon {
    display: flex;
    margin-top: 90px;
    height: 20px;
    line-height: 20px;
    margin-left: 18px;
    .text {
      margin-left: 8px;
      color: #91959e;
    }
    .text:hover {
      cursor: pointer;
    }
  }
  .cover {
    .addtext {
      color: @color-subtitle;
      font-size: 14px;
    }
    .addcover {
      position: relative;
      flex-direction: column;
      width: 200px;
      height: 120px;
      border: 1px solid @border-color-base;
      margin-bottom: 0;
      margin-right: 23px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
    }
    .cover_image_wrap {
      width: 200px;
      height: 120px;
      background-size: cover;
      position: relative;
      cursor: pointer;
      overflow: hidden;
      .cover_image {
        width: 200px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
      &:hover .change_text {
        opacity: 0.75;
      }
    }
    .change_text {
      position: absolute;
      background-color: rgba(0, 0, 0, 0.75);
      width: 200px;
      height: 120px;
      opacity: 0;
      text-align: center;
      line-height: 120px;
      color: #fff;
      font-size: 16px;
    }
  }
  .wrap {
    width: 1200px;
    .itemWrap {
      position: relative;
      width: 239px;
      height: 115px;
      margin-bottom: 10px;
      float: left;
      margin-right: 16px;
      .item {
        width: 100%;
        height: 100%;
        border: 1px solid #dee1e6;
        box-sizing: border-box;
        border-radius: 4px;
        padding: 15px;
        line-height: 20px;
        position: relative;
        .topLeft{
          display: none;
        }
        .hover {
          .righticon;
          position: absolute;
          top: 0;
          left: 0;
          border: 1px solid var(--color-primary06,#0c6cff);
          box-sizing: border-box;
          border-radius: 4px;
          width: 100%;
          height: 100%;
          pointer-events: none;
          // .topLeft {
          //   width: 24px;
          //   height: 24px;
          //   img {
          //     width: 100%;
          //     height: 100%;
          //   }
          // }
        }
      }
      .disable {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1000;
        box-sizing: border-box;
        border-radius: 4px;
        width: 100%;
        height: 100%;
        background: #eaebee;
        opacity: 0.3;
        cursor: not-allowed;
      }
    }
  }
}
</style>
<style lang="less" scoped>
.iconfont {
  color: #91959e;
  font-size: 13px;
}
.video-live {
  .item {
    margin-bottom: 20px;
  }
}
.el-select {
  width: 300px;
}
.hexagon__btn {
  font-size: 13px;
  display: inline-block;
  width: 100%;
  padding: 0;
  color: #0c6cff;
  cursor: pointer;
  line-height: 34px;
}
.hexagon__refresh{
  border: none;
  text-align: left;
}
.hexagon__refresh:hover,
.hexagon__refresh:active,
.hexagon__refresh:focus {
  color: #0c6cff;
  background-color: var(--color-special01,#f2f4fb);
}
.el-select-dropdown__item.hover .hexagon__refresh {
  background-color: var(--color-special01,#f2f4fb);
}
</style>
