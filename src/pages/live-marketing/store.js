import { mapState } from 'vuex'
import Store from '@/store/base-list-module.js'
import http from '@/services/http/index.js'
import util from '@/services/util/index.js'

const storeModule = {
  pageSize: 20,
  handlers: {
    queryLists(params = { pageSize: 20, pageNum: 1 }) {
      return http.queryLive(params)
    },
    parseLists({ errCode, data }) {
      if (errCode === 0) {
        return (data.result || []).map(item => ({
          ...item,
          startTime: util.formatDateTime(item.startTime, 'YYYY-MM-DD hh:mm'),
          endTime: util.formatDateTime(item.endTime, 'YYYY-MM-DD hh:mm'),
        }))
      }
      return []
    },
  },
}

const moduleName = 'liveLists'

export default {
  data() {
    return {
      pageNum: 1,
      pageSize: 10,
      status: 0,
      keyword: '',
      pageData: {
        pageSize: 10,
        pageNum: 1,
        totalCount: 0,
      },
    }
  },
  computed: {
    ...mapState(moduleName, {
      lists: 'lists', // 列表数据
      refreshing: 'refreshing', // 是否刷新数据
      hasMore: 'hasMore', // 是否有更多数据
      totalCount: 'totalCount', // 数据条数
      isShowHomeTip: 'isShowHomeTip',
      firstLoading: 'firstLoading',
    }),
    // pageData(){
    //   return {
    //     pageSize: this.pageSize,
    //     pageNum: this.pageNum,
    //     totalCount: this.totalCount,
    //   }
    // }
  },
  watch: {
    totalCount(newVal) {
      console.log(`newVal${newVal}`)
      this.pageData.totalCount = newVal
    },
  },
  methods: {
    initStore() {
      const store = (this.store = new Store())
      store.registerModule(this.$store, moduleName, storeModule)
    },
    queryListsInit() {
      this.queryLists()
      setTimeout(() => {
        this.pageData.totalCount = this.totalCount
      }, 3000)
    },
    queryLists() {
      const params = {
        ...this.pageData,
      }
      delete params.totalCount
      if (this.keyword) {
        params.keyword = this.keyword
      }
      if (this.status) {
        params.status = this.status
      }
      params.filterData = this.fitlers.length > 0 ? this.fitlers[0] : {}

      const materialTagFilter = {
        type: this.materialTagFilter.queryType,
        materialTagIds: this.materialTagFilter.tags.map(el => el.tagId),
      }
      params.materialTagFilter = materialTagFilter
      if(this.screenStatus){
        params.eventType = this.screenStatus
      }
      this.store.dispatch('queryLists', params)
    },
    refresh(lists = []) {
      this.store.dispatch('refresh', lists)
    },
    syncLiveStatistics(params) {
      return http.syncLiveStatistics(params)
    },
  },

  created() {
    this.initStore()
  },
  mounted() {
    this.queryListsInit()
  },
}
