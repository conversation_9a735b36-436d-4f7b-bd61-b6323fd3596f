<template>
  <div :class="$style.marketing_live">
    <div slot="header">
      <content-header
        :title="$t('marketing.commons.zbyx_a9fa5d')"
        :border="true"
      >
        <ContentGroupHeader
          :object-type="30"
          :data="selectedLives"
          type="live"
          @close="handleClearSelect"
          @update:list="handleUpdateList"
        />
        <div
          slot="right"
          :class="$style.search"
        >
          <ContentTagsQuery
            v-model="materialTagFilter"
            @change="handleTagsChange"
          />
          <AdvancedFilterButton
            style="margin-left: 20px;margin-right: -10px;"
            :object-names="objectNames"
            @confirm="handleFilterConfirm"
          />
          <eventTypeSelector
            style="margin-left: 20px;"
            :expandOptions="[
              {
                apiName: '',
                fieldName: $t('marketing.commons.qb_a8b0c2'),
              },
            ]"
            :type="1"
            @change="handleEventTypeChange"
           />
          <fx-input
            v-model="keyword"
            size="small"
            class="search-input"
            :placeholder="$t('marketing.commons.ssbt_b1140e')"
            prefix-icon="el-icon-search"
            style="margin-left: 20px"
            @change="handleChangeList"
          />
          <fx-button
            id="newMarketingEventTips"
            type="primary"
            size="small"
            style="margin-left: 20px"
            @click="$router.push({ name: 'live-create' })"
          >
            <i class="yxt-icon16 icon-add" />
            <span>{{ $t('marketing.pages.live_marketing.xjzb_c4094d') }}</span>
          </fx-button>
        </div>
      </content-header>
    </div>
    <div
      v-loading="refreshing"
      :class="$style.content"
      element-loading-background="rgba(0, 0, 0, 0)"
    >
      <div
        v-show="lists.length > 0"
        ref="liveScrollWrap"
        :class="$style.live__list"
      >
        <div
          v-for="(item) in lists"
          :key="item.id"
          :class="$style.live"
          @click="handleOpenDetail(item)"
        >
          <div
            :class="$style.live__select"
            @click.stop="handleStopDefault"
          >
            <fx-checkbox
              :value="selectedMap[item.id] > -1"
              @change="e => handleLiveCheck(e, item)"
            />
          </div>
          <div :class="$style.live__cover">
            <VImage
              fit="cover"
              :src="item.cover"
            />
            <LiveStatus
              :class="$style.status"
              :life-status="item.lifeStatus"
              :status="item.status"
            />
          </div>
          <div :class="$style.live__info">
            <div :class="$style.info__title">
              {{ item.title }}
              <div
                v-if="item.hasRecord"
                :class="$style.title__status"
              >
                {{ $t('marketing.commons.hf_94f38a') }}
              </div>
            </div>
            <div :class="$style.info__others">
              <div :class="$style.others__item">
                <div :class="$style.cell">
                  {{ $t('marketing.commons.zbsj_519953') }}{{ item.startTime }}{{ $t('marketing.commons.z_981cbe') }}{{ item.endTime }}
                </div>
                <div :class="$style.cell">
                  {{ $t('marketing.commons.zblj_625d5a') }}{{ item.viewUrl }}
                </div>
              </div>
              <div
                :class="$style.others__item"
                @click.stop
              >
                <template v-if="item.materialTags && item.materialTags.length">
                  <img
                    :src="iconTags"
                    :class="$style.icon"
                  >
                  <v-tag
                    style="flex: 1;"
                    name-key="name"
                    empty-text=""
                    placement="bottom-start"
                    :data="item.materialTags || []"
                  />
                </template>
              </div>
            </div>
          </div>
          <div :class="$style.live__statistic">
            <div :class="$style.statistic__dividing" />
            <!-- <div :class="$style.statistic__item">
                <div :class="$style.item__number">
                  {{ item.pv || 0 }}
                </div>
                <div :class="$style.item__label">访问次数</div>
              </div> -->
            <div :class="$style.statistic__item">
              <div :class="$style.item__number">
                {{ item.uv || 0 }}
              </div>
              <div :class="$style.item__label">
                {{ $t('marketing.commons.fwrs_c3c959') }}
              </div>
            </div>
            <div
              v-if="item.subEvent !== 1"
              :class="$style.statistic__item"
            >
              <div :class="$style.item__number">
                {{ item.enrollCount || 0 }}
              </div>
              <div :class="$style.item__label">
                {{ $t('marketing.commons.bmrs_a06b35') }}
              </div>
            </div>
            <div
              v-if="item.platform === 6 && item.subEvent !== 1"
              :class="$style.statistic__item"
            >
              <div :class="$style.item__number">
                {{ item.stayCount || 0 }}
              </div>
              <div :class="$style.item__label">
                {{ $t('marketing.pages.live_marketing.zlrs_c33b03') }}
              </div>
            </div>
            <div
              v-else
              :class="$style.statistic__item"
            >
              <div :class="$style.item__number">
                {{ item.totalViewUsers || 0 }}
              </div>
              <div :class="$style.item__label">
                {{ $t('marketing.commons.gkrs_bbbf76') }}
              </div>
            </div>
            <!-- <div :class="$style.statistic__item">
                <div :class="$style.item__number">
                  {{ item.chatTimes || 0 }}
                </div>
                <div :class="$style.item__label">互动总次数</div>
              </div> -->
            <!-- <div :class="$style.statistic__item">
                <div :class="$style.item__number">{{ item.leads || 0 }}</div>
                <div :class="$style.item__label">线索数</div>
              </div> -->
            <!-- <div>
                <fx-button
                  type="text"
                  :loading="item.refreshing || false"
                  @click.stop="handleRowRefresh(index, item)"
                  >刷新数据</fx-button
                >
              </div> -->
          </div>
        </div>
      </div>
      <v-pagen
        v-show="totalCount > 0"
        slot="footer"
        :class="$style.pagenav"
        :pagedata.sync="pageData"
        @change="handlePageChange"
      />
      <ContentEmpty v-if="!totalCount && isShowHomeTip" />
      <div
        v-if="!totalCount && !isShowHomeTip && !firstLoading"
        :class="$style.list__empty"
      >
        <div :class="$style.empty__icon" />
        <div :class="$style.empty__text">
          {{ $t('marketing.commons.zwsj_21efd8') }}
        </div>
      </div>
      <PermissionDialog
        :marketing-event-name="marketingEventName"
        :marketing-event-id="marketingEventId"
        :visible.sync="permissionDialogVisible"
      />
    </div>
  </div>
</template>

<script>
import http from '@/services/http/index.js'
import ContentHeader from '@/components/content-header/index.vue'
import VPagen from '@/components/kitty/pagen.vue'
import ContentEmpty from './empty.vue'
import LiveStatus from './components/live-status.vue'
import store from './store.js'
import AdvancedFilterButton from '@/components/advanced-filter-button/index.vue'
import PermissionDialog from '@/pages/live-marketing/components/permissionsDialog.vue'
import kisvData from '@/modules/kisv-data.js'
import ContentTagsQuery from '@/components/content-tags-selector/tags-query.vue'
import ContentGroupHeader from '@/components/ContentGroupHeader/index.vue'
import VTag from '@/components/table-ex/tag.vue'

import iconTags from '@/assets/images/icon-tags.svg'
import eventTypeSelector from '@/components/eventTypeSelector/index.vue'

export default {
  components: {
    ContentHeader,
    VImage: FxUI.Image,
    ContentEmpty,
    LiveStatus,
    VPagen,
    AdvancedFilterButton,
    PermissionDialog,
    ContentTagsQuery,
    ContentGroupHeader,
    VTag,
    eventTypeSelector
  },
  mixins: [store],
  data() {
    return {
      options: [
        {
          label: $t('marketing.commons.qb_a8b0c2'),
          value: 0,
        },
        {
          label: $t('marketing.commons.zbz_2cb2b1'),
          value: 1,
        },
        {
          label: $t('marketing.commons.hf_94f38a'),
          value: 5,
        },
        {
          label: $t('marketing.commons.yjs_047fab'),
          value: 3,
        },
      ],
      data: [],
      objectNames: [{ name: $t('marketing.store.schd_3634ed'), value: 'MarketingEventObj' }],
      fitlers: [],
      permissionDialogVisible: false,
      marketingEventId: '',
      marketingEventName: '',
      vDatas: kisvData.datas,
      materialTagFilter: {
        queryType: 2,
        tags: [],
      },
      selectedLives: [],
      iconTags,
      // 新加活动类型筛选
      screenStatus: ''

    }
  },
  computed: {
    selectedMap() {
      const livesMap = {}
      this.selectedLives.forEach((item, index) => {
        livesMap[item.id] = index
      })

      return livesMap
    },
  },
  methods: {
    handleFilterConfirm(data) {
      this.fitlers = data
      this.handleChangeList()
    },
    statusText(status) {
      let text
      let cls
      switch (status) {
        case 1:
          text = $t('marketing.commons.zbz_2cb2b1')
          cls = this.$style.yellow
          break
        case 2:
          text = $t('marketing.commons.yg_1cbb42')
          cls = this.$style.blue
          break
        case 3:
          text = $t('marketing.commons.yjs_047fab')
          cls = this.$style.gray
          break
        default:
          text = $t('marketing.commons.wz_1622dc')
          cls = this.$style.gray
          break
      }
      return {
        text,
        cls,
      }
    },
    // handleRowRefresh(index, row) {
    //   this.$set(this.lists, index, {
    //     ...row,
    //     refreshing: true
    //   });
    //   this.syncLiveStatistics({
    //     id: row.id
    //   }).then(({ errCode, errMsg, data }) => {
    //     this.$set(this.lists, index, {
    //       ...row,
    //       refreshing: false
    //     });
    //     if (errCode === 0) {
    //       this.$set(this.lists, index, {
    //         ...row,
    //         ...(data || {}),
    //       });
    //     } else {
    //       Message.error(errMsg || "刷新失败");
    //     }
    //   });
    // },
    resetScrollWrap() {
      this.$refs.liveScrollWrap.scrollTo(0, 0)
    },
    async handleOpenDetail(item) {
      const permission = await this.checkMarketingEventPermission(item.marketingEventId)
      if (!permission) {
        this.marketingEventId = item.marketingEventId
        this.marketingEventName = item.title
        this.permissionDialogVisible = true
      } else {
        this.$router.push({
          name: 'live-dashboard',
          params: {
            id: item.marketingEventId,
          },
        })
      }
    },
    async checkMarketingEventPermission(id) {
      // 开了多组织权限才需要判断
      let flag = true
      if (this.vDatas.uinfo.marketingDataIsolation) {
        const res = await http.getEntityOpenness({
          objectApiName: 'MarketingEventObj',
          objectIds: [id],
        })
        //  1是只读,2是读写
        if (res.errCode === 0 && res.data && res.data.openness) {
          flag = res.data.openness[id] === 2
        }
      }
      return flag
    },
    handleChangeList() {
      this.pageData.pageNum = 1
      this.queryLists()
      this.resetScrollWrap()
    },
    handlePageChange(data) {
      this.pageData.pageNum = data.pageNum
      this.pageData.pageSize = data.pageSize
      this.queryLists()
      this.resetScrollWrap()
    },
    handleTagsChange() {
      this.pageData.pageNum = 1
      this.queryLists()
      this.resetScrollWrap()
    },
    handleClearSelect() {
      this.selectedLives = []
    },
    handleUpdateList() {
      this.pageData.pageNum = 1
      this.queryLists()
      this.resetScrollWrap()
      this.handleClearSelect()
    },
    handleLiveCheck(checked, item) {
      if (checked) {
        this.selectedLives.push(item)
      } else {
        this.selectedLives = this.selectedLives.filter(el => el.id !== item.id)
      }
    },
    handleStopDefault(e) {
      e.stopPropagation()
    },
    handleEventTypeChange(value) {
      this.screenStatus = value.apiName
      this.pageData.pageNum = 1
      this.queryLists()
    },
  },
}
</script>

<style lang="less" module>
.marketing_live {
  display: flex;
  flex-flow: column nowrap;
  height: 100%;
  .content_header {
    display: flex;
    height: 50px;
    padding: 0 19px;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid @border-color-base;
    .right {
      font-size: 0;
      :global {
        .el-radio {
          margin-right: 0;
        }
      }
    }
  }
  .search {
    display: flex;
    align-items: center;
  }
  .content {
    flex: 1 1 auto;
    display: flex;
    flex-flow: column;
    overflow: hidden;
    min-width: 1200px;
  }
  .live__list {
    overflow-y: auto;
    flex: 1;
    .live {
      height: 120px;
      padding: 13px 30px 13px 18px;
      display: flex;
      align-items: center;
      box-sizing: border-box;
      border-bottom: 1px solid #e9edf5;
      cursor: pointer;
      &:hover {
        background: #f6f9fc;
      }

      .live__select {
        width: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .live__cover {
        width: 144px;
        height: 80px;
        overflow: hidden;
        border-radius: 4px;
        cursor: pointer;
        position: relative;
        .status {
          position: absolute;
          left: 0;
          top: 3px;
        }
        :global {
          .el-image {
            width: 100%;
            height: 100%;
            .image-slot {
              display: flex;
              justify-content: center;
              align-items: center;
              width: 100%;
              height: 100%;
              background: #f5f7fa;
              color: #909399;
              font-size: 20px;
            }
          }
        }
      }
      .live__info {
        display: flex;
        flex-direction: column;
        justify-content: center;
        margin-left: 16px;
        flex: 1;
        width: 0;
        .info__title {
          display: flex;
          color: #181c25;
          font-size: 16px;
          .title__status {
            display: inline-block;
            width: 40px;
            height: 20px;
            line-height: 20px;
            text-align: center;
            border: 1px solid rgba(206, 213, 222, 1);
            color: #879eb8;
            font-size: 10px;
            border-radius: 2px;
            margin-left: 10px;
            box-sizing: border-box;
          }
        }
        .info__others {
          color: #545861;
          font-size: 12px;
          .others__item {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-top: 8px;
            margin-right: 20px;
            display: flex;
            align-items: center;

            .cell {
              margin-right: 20px;
            }

            .icon {
              width: 16px;
              height: 16px;
              margin-right: 8px;
            }
          }
        }
      }
      .live__statistic {
        display: flex;
        width: 350px;
        justify-content: space-around;
        align-items: center;
        .statistic__dividing {
          width: 1px;
          height: 33px;
          background-color: #e9edf5;
        }
        .statistic__item {
          display: flex;
          align-items: center;
          flex-direction: column;
          .item__number {
            color: #181c25;
            font-size: 16px;
          }
          .item__label {
            margin-top: 5px;
            color: #91959e;
            font-size: 12px;
          }
        }
      }
    }
  }
  .list__empty {
    flex: 1;
    display: flex;
    width: 100%;
    height: 100%;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .empty__icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      min-width: 120px;
      height: 120px;
      background: url("@/assets/images/no-data.png") center no-repeat;
      background-size: 100px;
      .empty-content {
        margin-top: 130px;
        color: #999;
      }
    }
    .empty__text {
      color: #666;
    }
  }
  .pagenav {
    height: 49px;
    border-top: 1px solid #e9edf5;
  }
}
</style>
