<template>
  <div class="live-empty">
    <div class="content-summarize">
      <div
        class="content-summarize-photo"
        :style="{
          'background': `url(${bannerImg}) center / 1170px 260px no-repeat, linear-gradient(to right, #e7f4ff, #d9eeff)`,
          'background-size': 'cover'
        }"
      >
        <div class="guide__banner-h">
          {{ $t('marketing.pages.live_marketing.qlctshkxl_9eb748') }} {{ $t('marketing.pages.live_marketing.zlyxzz_4b3141') }}
        </div>
        <div class="guide__banner-con">
          <span class="guide__banner-text">{{ $t('marketing.pages.live_marketing.zbq_5c48ea') }}-{{ $t('marketing.pages.live_marketing.dqdyytcbml_d37dd9') }}</span>
          <span class="guide__banner-text">{{ $t('marketing.commons.zbz_2cb2b1') }}-{{ $t('marketing.pages.live_marketing.gzhdjldzfs_44f484') }}</span>
          <span class="guide__banner-text">{{ $t('marketing.pages.live_marketing.zbh_900aa2') }}-{{ $t('marketing.pages.live_marketing.sjzzfxkhzh_098c8a') }}</span>
        </div>
      </div>
    </div>
    <div class="content-tips"></div>
    <div class="content-guidance">
      <div class="content-item" v-for="(item, index) in list" :key="index">
        <div class="guidance-line" v-if="index < (list.length - 1)"></div>
        <div class="guidance-arrow-line" v-else>
          <div class="guidance-arrow"></div>
        </div>
        <img class="guidance-icon" :src="item.image" />
        <div class="guidance-title">{{ item.title }}</div>
        <div class="guidance-desc" v-html="item.desc"></div>
      </div>
    </div>
    <div class="content-foot">
      <fx-button class="tool-button" size="small" type="primary" @click="addLiveMarketing">{{ $t('marketing.commons.ljxjzb_0c3451') }}</fx-button>
      <!-- <fx-button class="tool-button" size="small" @click="knowMore">了解更多详情</fx-button> -->
    </div>
  </div>
</template>

<script>
import introImg from '@/assets/images/banner/live-banner.png'
import introImgEn from '@/assets/images/banner/live-banner-en.png'

import { getImageByLang } from '@/utils/i18n.js'

export default {
  data() {
    return {
      bannerImg: getImageByLang([introImg, introImgEn]),
      list: [
        {
          title: $t('marketing.commons.hdchynrzz_0a7038'),
          image: require("@/assets/images/content-marketing/guidance-2.png"),
          desc: $t('marketing.commons.hdjsyjbtgh_1ab813'),
        }, 
        {
          title: $t('marketing.commons.dqdkhyyytg_9692d3'),
          image: require("@/assets/images/content-marketing/guidance-3.png"),
          desc: $t('marketing.commons.wxqpyqgzht_9d7c7d'),
        }, 
        {
          title: $t('marketing.commons.zxbmyzbtx_2abc4e'),
          image: require("@/assets/images/content-marketing/guidance-1.png"),
          desc: $t('marketing.commons.ssfwzzkhbm_89eb84'),
        }, 
        {
          title: $t('marketing.commons.zbzgkhd_1a803d'),
          image: require("@/assets/images/content-marketing/guidance-5.png"),
          desc: $t('marketing.commons.gkyzsshdfx_fed8e3'),
        }, 
        {
          title: $t('marketing.commons.xgzz_5d9756'),
          image: require("@/assets/images/content-marketing/guidance-4.png"),
          desc: $t('marketing.commons.hdfwbmhdyh_7a2e1b'),
        }
      ]
    }
  },
  methods: {
    addLiveMarketing() {
      this.$router.push({
        name: 'live-create'
      })
    },
    knowMore() {}
  }
}
</script>

<style lang="less">
.live-empty {
  height: 100%;
  width: 100%;
  overflow-y: auto;
  padding-bottom: 50px;
  .content-summarize {
    margin: 17px;
    display: flex;
    &-photo {
      width: 100%;
      height: 300px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding-left: 120px;
    }
    .guide__banner-h {
      max-width: 640px;
      font-weight: 700;
      font-size: 20px;
      color: #49638d;
    }

    .guide__banner-con {
      max-width: 640px;
      display: flex;
      flex-direction: column;
      text-align: left;
      margin-top: 16px;

      .guide__banner-text {
        color: #6f889c;
        font-size: 16px;
      }

      .guide__banner-text + .guide__banner-text {
        margin-top: 4px;
      }
    }
  }
  .content-tips {
    color: #2A304D;
    font-size: 16px;
    text-align: center;
    margin: 45px 17px 23px 17px;
  }
  .content-guidance {
    display: flex;
    flex-flow: row nowrap;
    position: relative;
    justify-content: center;
    margin: 15px 34px 65px 34px;
    .content-item {
      display: flex;
      flex-flow: column nowrap;
      align-items: center;
      flex: 1 1 20%;
      padding-left: 5%;
      &:first-of-type {
        padding-left: 0;
      }
      .guidance-line {
        position: absolute;
        height: 1px;
        width: 20%;
        border-bottom: 1px solid #e9edf5;
        top: 20px;
        transform: translateX(50%);
        z-index: 1;
      }
      .guidance-arrow-line {
        position: absolute;
        height: 1px;
        width: 30px;
        border-bottom: 1px solid #e9edf5;
        top: 20px;
        transform: translateX(50%);
        z-index: 1;
        .guidance-arrow {
          position: absolute;
          top: -7px;
          left: 100%;
          width: 14px;
          height: 15px;
          background: url("../../assets/images/content-marketing/arrow.png") center / 14px 15px no-repeat;
        }
      }
      .guidance-icon {
        width: 40px;
        height: 40px;
        z-index: 2;
      }
      .guidance-title {
        color: #181C25;
        margin-top: 14px;
        font-size: 14px;
      }
      .guidance-desc {
        color: #91959E;
        margin-top: 12px;
        font-size: 12px;
      }
    }
  }
  .content-foot {
    text-align: center;
    .tool-button {
      margin: 0 20px;
    }
  }
}
</style>