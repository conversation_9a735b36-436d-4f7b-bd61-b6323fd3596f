<template>
  <div :class="[$style.pastimer,priValue.type === 2 && $style.pastimerSingle]">
    <div class="pastimer-select">
      <el-select class="el-select" v-model="priValue.type" :placeholder="$t('marketing.commons.qxz_708c9d')">
        <el-option v-for="item in sendTypes" :key="item.value" :label="item.label" :value="item.value"></el-option>
      </el-select>
    </div>
    <date-picker
      ref="date-picker"
      v-if="priValue.type === 2"
      class="sendtime-choose"
      v-model="priValue.time"
      value-format="timestamp"
      type="datetime"
      format="yyyy-MM-dd HH:mm"
      :hasFormItem="false"
      :placeholder="$t('marketing.commons.xzrqsj_a42ae4')"
      :picker-options="{ disabledDate: disabledDateStart }"
    ></date-picker>
  </div>
</template>

<script>
import util from '@/services/util/index';

export default {
  components: {
    elSelect: FxUI.Select.components.ElSelect,
    elOption: FxUI.Select.components.ElSelect.components.ElOption,
    DatePicker: FxUI.DatePicker,
  },
  props: {
    value: {},
  },
  watch: {
    value: {
      deep: true,
      handler() {
        this.peek();
      },
    },
    priValue: {
      deep: true,
      handler(newVal) {
        const timeMin = +new Date() + 1000 * 60 * 10;
        if (newVal.time && +newVal.time < timeMin) {
          this.priValue.time = timeMin;
          return; // priValue 被 watch 了，这里修正了它的值会再次触发 watch，所以不要 emit 修正之前的值
        }
        this.emit();
      },
    },
  },
  data() {
    return {
      priValue: {
        type: 1,
        time: '',
      },
      sendTypes: [
        { value: 1, label: $t('marketing.commons.ljfs_1176c5') },
        { value: 2, label: $t('marketing.commons.dsfs_74e5c0') },
      ],
    };
  },
  methods: {
    disabledDateStart(time) {
      return time.getTime() < util.dateAtStart(Date.now());
    },
    peek() {
      $.extend(true, this.priValue, this.value);
      this.priValue.time = this.priValue.time || '';
    },
    emit() {
      this.$emit('input', this.priValue);
      if (this.priValue.type === 1) {
        // type 为 1 时，没有原生（elementUI）blur事件，不能触发父组件的 priValue 校验
        // 这里借用 date-picker 的 el.form.blur 事件通知父组件执行值校验
        // TODO 如何才能派发 el-select 自身的 blur 事件呢
        this.$refs['date-picker'] && this.$refs['date-picker'].dispatch('ElFormItem', 'el.form.blur');
      }
    },
  },
  mounted() {
    this.peek();
  },
};
</script>

<style lang="less" module>
.pastimer {
  width: 100%;
  display: flex;
  // justify-content: center;
  align-items: center;
  .pastimer-select{
    display: flex;
    width: 100%;
  }
  &Single{
    .pastimer-select{
      flex: 3;
    }
  }
  :global {
    .el-input__inner {
      height: 36px;
      line-height: 36px;
    }
    .fx-select {
      flex: 1;
    }
    .el-date-editor {
      flex: 3;
      margin-left: 20px;
    }
  }
}
</style>
