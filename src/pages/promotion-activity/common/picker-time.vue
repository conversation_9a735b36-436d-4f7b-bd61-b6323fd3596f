<template>
  <div :class="$style.promotion__time">
    <DatePicker
      v-model="startTime"
      :type="type"
      :format="format"
      @change="handleChange"
      :placeholder="$t('marketing.commons.kssj_592c59')"
      :value-format="valueFormat"
    ></DatePicker>
    <span style="padding: 0 10px;">{{ $t('marketing.commons.z_981cbe') }}</span>
    <DatePicker
      v-model="endTime"
      :type="type"
      :format="format"
      @change="handleChange"
      :placeholder="$t('marketing.commons.jssj_f78277')"
      :value-format="valueFormat"
    ></DatePicker>
  </div>
</template>

<script>

import util from "@/services/util/index";

export default {
  components: {
DatePicker: FxUI.DatePicker
},
  props: {
    type: {
      type: String,
      default: "date"
    },
    format: {
      type: String,
      default: "yyyy-MM-dd"
    },
    valueFormat: {
      type: String,
      default: ""
    },
    value: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      startTime: "",
      endTime: ""
    };
  },
  watch: {
    value: {
      deep: true,
      handler(res) {
        if (res instanceof Array) {
          const [startTime, endTime] = res;
          this.startTime = startTime;
          this.endTime = endTime;
        }
      }
    }
  },
  methods: {
    handleChange() {
      let startTime = this.startTime;
      let endTime = this.endTime;
      if (this.format == "yyyy-MM-dd") {
        if (!startTime && !endTime) {
          return;
        } else if (!startTime) {
          startTime = endTime;
        } else if (!endTime) {
          endTime = startTime;
        }
        if (startTime > endTime) {
          startTime = [endTime, (endTime = startTime)][0];
        }
        startTime = util.dateAtStart(startTime);
        endTime = util.dateAtEnd(endTime);
      }
      this.$emit("input", [startTime, endTime]);
    }
  }
};
</script>

<style lang="less" module>
.promotion__time{
  display: flex;
  /deep/ .fx-form-item{
    flex: 1;
    .el-date-editor{
      width: 100%;
    }
  }
}
</style>
