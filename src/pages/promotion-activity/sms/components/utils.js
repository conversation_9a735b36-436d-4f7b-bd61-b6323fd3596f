import { formatStringByEmpty } from "@/utils";
import util from "@/services/util/index.js";
import _ from 'lodash'
import store from '@/store/index.js'
const formatter = (rowData, column, cellValue) =>
  formatStringByEmpty(cellValue);

const currentPlatform = store.state.SmsMarketing.currentPlatform
const templateFieldConfig = {
  'mw': {
    tplTypeMap: {
      0: 0,
      1:1
    },
    tplTypeParams: {
      '0': 0,
      '1':1
    },
    statusMap: {
      0: 0,
      1: 1,
      2: 2
    },
    // 搜索
    statusParams: {
      '0': 0,
      '1': 1,
      '2': 2
    },
  },
  'hisense-aliyun': {
    tplTypeMap: {
      'NOTIFICATION': 0,
      'PROMOTION': 1,
      'VERIFY_CODE': 2,
      'INTERNATIONAL': 3
    },
    tplTypeParams: {
      '0': 'NOTIFICATION',
      '1': 'PROMOTION',
      '2': 'VERIFY_CODE',
      '3': 'INTERNATIONAL'
    },
    // 统一状态值 ['NO_REPLY','APPROVING']: 审核中  ['REJECTED','CANCELED']: 审核失败
    statusMap: {
      'APPROVED': 0,
      'NO_REPLY': 1,
      'REJECTED': 2,
      'APPROVING': 1,
      'CANCELED': 2
    },
    // 搜索
    statusParams: {
      '0': 'APPROVED',
      '1': ['NO_REPLY','APPROVING'],
      '2': ['REJECTED','CANCELED']
    },
  },
  // 默认
  'default': {
    tplTypeMap: {
      'NOTIFICATION': 0,
      'PROMOTION': 1,
      'VERIFY_CODE': 2,
      'INTERNATIONAL': 3
    },
    tplTypeParams: {
      '0': 'NOTIFICATION',
      '1': 'PROMOTION',
      '2': 'VERIFY_CODE',
      '3': 'INTERNATIONAL'
    },
    // 统一状态值 ['NO_REPLY','APPROVING']: 审核中  ['REJECTED','CANCELED']: 审核失败
    statusMap: {
      'APPROVED': 0,
      'NO_REPLY': 1,
      'REJECTED': 2,
      'APPROVING': 1,
      'CANCELED': 2
    },
    // 搜索
    statusParams: {
      '0': 'APPROVED',
      '1': ['NO_REPLY','APPROVING'],
      '2': ['REJECTED','CANCELED']
    },
  },
}

export default {
  smsTemplateColumns: [
    {
      prop: 'selection',
      label: $t('marketing.commons.xz_153fa6'),
      exComponent: 'slot',
      slotName: 'singleSelection',
    },
    {
      prop: 'name',
      label: $t('marketing.commons.mbmc_a5d1c5'),
      minWidth: 100,
      formatter
    },
    {
      prop: 'statusText',
      label: $t('marketing.commons.shzt_b6d0e9'),
      minWidth: 100,
      exComponent: 'errState',
      formatter
    },
    {
      prop: 'tplType',
      label: $t('marketing.commons.mblx_bfd708'),
      minWidth: 100,
      exComponent: 'text',
      formatter
    },
    {
      prop: 'content',
      label: $t('marketing.commons.dxnr_4e963c'),
      minWidth: 200,
      formatter
    },
    {
      prop: 'createTime',
      label: $t('marketing.commons.cjsj_eca37c'),
      minWidth: 100,
      formatter
    },
    {
      prop: 'operations',
      label: $t('marketing.commons.cz_2b6bc0'),
      width: 100,
      exComponent: 'operation',
      unsettable: true,
    },
  ],
  formatTemplateList(res,providerId){
    const data = res.data
    const result = data.result || []
    const datas = _.map(result, item => {
      const { id, name, content, tplVersion,status,tplType,reply } = item;
      const operations = [
        { id: "copy", name: $t("marketing.commons.fz_79d3ab") }
      ];
      // 梦网的审核失败的可以删除
      if ((status === 'REJECTED' || status === 'CANCELED') && providerId === 'mw') {
        operations.push({
          id: "delete",
          name: $t("marketing.commons.sc_2f4aad")
        });
      }
      return {
        id,
        name,
        content,
        status: status,
        titleContent: {
          title: name,
          content: content.replace(/\n/g, '<br />'),
          isRichText: true,
        },
        statusText: {
          errs: status === 2,
          text:
          {
            'APPROVED': $t('marketing.commons.shtg_871a30'),
            'NO_REPLY': $t('marketing.commons.shz_b720a6'),
            'REJECTED': $t('marketing.commons.shbtg_abad33'),
            'APPROVING': $t('marketing.commons.shz_b720a6'),
            'CANCELED': $t('marketing.pages.sms_marketing.shbtg_abad33')
          }[status],
          tips: item.reply,
        },
        tplType: {
          text: {
            'NOTIFICATION': $t('marketing.pages.promotion_activity.tzdx_5bd128'),
            'PROMOTION': $t('marketing.commons.yxdx_4ca661'),
            'VERIFY_CODE':  $t('marketing.pages.form.yzm_983f59'),
            'INTERNATIONAL':  $t('marketing.pages.sms_marketing.gjgatxx_3b76bb'),
          }[tplType] || '--',
        },
        operations,
        createTime: util.formatDateTime(item.createTime, "YYYY-MM-DD hh:mm"),
        tplVersion,
        reply: reply || '--'
      };
    });
    return {
      datas,
      totalCount: data.totalCount
    };
  },
  templateFieldConfig,
  // 模版类型
  getSmsTplTypeList(data){
    const platform = data || currentPlatform
    let tplTypeList = [
      {
        typeName: 'PROMOTION',
        tplType: 'PROMOTION',
        title: $t('marketing.commons.yxmb_3c3e91'),
        des: $t('marketing.pages.sms_marketing.syyhltgcjr_bc80d9')
      },
      {
        typeName: 'NOTIFICATION',
        tplType: 'NOTIFICATION',
        title: $t('marketing.commons.tzmb_59f9d9'),
        des: $t('marketing.pages.sms_marketing.syyywtzcjr_35c2e2')
      }
    ]
    if(platform !== 'mw'){
      tplTypeList.push({
        typeName: 'international',
        tplType: 'INTERNATIONAL',
        title: $t('marketing.pages.sms_marketing.hwdx_5ad4c2'),
        des: $t('marketing.pages.sms_marketing.zcczgndxzg_843178')
      })
    }
    return tplTypeList
  },
  getSmsTplVariableTypeList(data){
    const tplType = data
    if(!tplType){
      return []
    }
    const PARAMS_OPTIONS = {
      'PROMOTION': [
        {
          label: $t('marketing.commons.yhnc_9a56bb'),
          value: 'user_nick'
        },
        {
          label: $t('marketing.pages.promotion_activity.grxm_9cd996'),
          value: 'name'
        },
        {
          label: $t('marketing.pages.promotion_activity.slje_d22d01'),
          value: 'money'
        },
        {
          label: $t('marketing.pages.promotion_activity.rqsj_35dc24'),
          value: 'time'
        },
        {
          label: $t('marketing.pages.promotion_activity.ljcs_847564'),
          value: 'link_param'
        },
      ],
      'NOTIFICATION': [
        {
          label: $t('marketing.commons.yhnc_9a56bb'),
          value: 'user_nick'
        },
        {
          label: $t('marketing.pages.promotion_activity.grxm_9cd996'),
          value: 'name'
        },
        {
          label: $t('marketing.pages.promotion_activity.qyzzmc_0239e1'),
          value: 'unit_name'
        },
        {
          label: $t('marketing.commons.dz_765048'),
          value: 'address'
        },
        {
          label: $t('marketing.pages.promotion_activity.cph_188c23'),
          value: 'license_plate_number'
        },
        {
          label: $t('marketing.pages.promotion_activity.kddh_3c016d'),
          value: 'tracking_number'
        },
        {
          label: $t('marketing.pages.promotion_activity.qjm_0461a7'),
          value: 'pick_up_code'
        },
        {
          label: $t('marketing.commons.je_4cf24a'),
          value: 'money'
        },
        {
          label: $t('marketing.pages.promotion_activity.qthm_1b07b4'),
          value: 'other_number2'
        },
        {
          label: $t('marketing.commons.sj_19fcb9'),
          value: 'time'
        },
        {
          label: $t('marketing.pages.promotion_activity.dhhm_193a8c'),
          value: 'phone_number2'
        },
        {
          label: $t('marketing.pages.promotion_activity.ljcs_847564'),
          value: 'link_param'
        },
        {
          label: $t('marketing.pages.promotion_activity.yxdz_6ab78f'),
          value: 'email_address'
        },
        {
          label: $t('marketing.pages.promotion_activity.qtrmczhdzd_1e5fff'),
          value: 'others'
        },
      ],
      'INTERNATIONAL': [
        {
          label: $t('marketing.commons.yhnc_9a56bb'),
          value: 0
        },
        {
          label: $t('marketing.pages.promotion_activity.grxm_9cd996'),
          value: 1
        },
        {
          label: $t('marketing.pages.promotion_activity.qyzzmc_0239e1'),
          value: 2
        },
      ],
      'VERIFY_CODE': [  
        {
          label: $t('marketing.pages.promotion_activity.jsz_6fee0d'),
          value: 'numberCaptcha'
        },
        {
          label: $t('marketing.pages.promotion_activity.szzmzhhjzm_cfe0af'),
          value: 'characterWithNumber2'
        },
      ]
    }
    return PARAMS_OPTIONS[tplType]
  }
};
