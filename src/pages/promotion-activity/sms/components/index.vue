<template>
  <div class="sms-create__content">
    <div class="form__left">
      <div class="preview-label">{{ $t('marketing.pages.promotion_activity.ylxg_2a2ec3') }}</div>
      <div class="wrapper--phone">
        <div class="wrapper--phone__header">
          <img :src="phoneHeader" style="width: 208px;" />
        </div>
        <div class="wrapper--phone__body">
          <div class="wrapper--msg">
            <div class="wrapper--msg__content">{{
                model_templateContent 
              }}
            </div>
            <div class="wrapper--msg__jiao">
              <img :src="phoneJiiao" />
            </div>
          </div>
        </div>
        <div style="display: none;" class="wrapper--phone__footer">
          <img :src="phoneFooter" />
        </div>
      </div>
    </div>

    <div class="form__right">
      <slot name="form_right_header"></slot>
      <el-form label-width="140px" label-position="left">
        <div class="selectbar_wrapper" v-if="isFromLive">
          <span class="select_wrapper-lable">{{ $t('marketing.commons.qfdx_33ed6f') }}</span>
          <template v-if="source == 'live'">
            <Selectbar
              :btnText="$t('marketing.commons.xzhdry_d8e99d')"
              @change="removeItem"
              @update:select="leadDialogVisible = true"
              :selects="selectedParticipants"
              class="select-range-bar"
            ></Selectbar>
            <div class="participant">
              <span
                >{{$t('marketing.commons.xzhdry_d8e99d', {
                  data : (
                    {'0': (selectedParticipants.colleague &&
                    selectedParticipants.colleague.length) || 0
                    }
                  )
                })}}</span
              >
              <a @click="clearSelected">{{ $t('marketing.commons.qkxz_16e193') }}</a>
            </div>
          </template>
          <Selectbar
            :btnText="$t('marketing.commons.xzchry_e3ca67')"
            @change="removeItem"
            @update:select="selectParticipantDialogVisible = true"
            :selects="selectedParticipants"
            class="select-range-bar"
            v-if="source == 'participant'"
          ></Selectbar>
          <div v-if="source == 'participant'" class="participant">
              <span
                >{{$t('marketing.commons.xzhdry_d8e99d', {
                  data : (
                    {'0': (selectedParticipants.colleague &&
                      selectedParticipants.colleague.length) || 0
                    }
                  )
                })}}</span
              >
            <a @click="clearSelected">{{ $t('marketing.commons.qkxz_16e193') }}</a>
          </div>
          <span class="selectbar-lable" v-if="source == 'invite'"
            >{{ $t('marketing.pages.promotion_activity.ayyryxz_1752eb') }}</span
          >
          <Selectbar
            :btnText="$t('marketing.commons.xzyyry_55b5c0')"
            @change="removeItem"
            @update:select="inviteParticipantDialogVisible = true"
            :selects="inviteParticipant"
            class="select-range-bar"
            v-if="source == 'invite'"
          ></Selectbar>
        </div>
        <div
          v-if="errorTips_tapath && isFromConference"
          class="errorTips_tapath"
        >
          {{ errorTips_tapath }}
        </div>
        <el-form-item v-if="!isFromLive" :label="$t('marketing.commons.qfdx_33ed6f')">
          <v-filters
            v-model="sendObject"
            userGroupDataType="phone"
            :list="srgTypes"
            :exlist="exSendRange"
          >
            <div :slot="3" :class="[error_tapath && 'error']">
              <div class="wrapper--upload">
                <div class="wrapper--upload__display">
                  <div class="display__bar" @click="handleChooseFile">{{  fileName || $t('marketing.commons.qxz_708c9d')}}</div>
                </div>
                <div class="wrapper--upload__button">
                  <fx-button
                    size="small"
                    style="height:36px;"
                    @click="handleChooseFile"
                    :loading="isUploading"
                    >{{  isUploading ? $t('marketing.commons.scz_fc09a7') : $t('marketing.commons.xqwj_61f3db')}}</fx-button
                  >
                  <input
                    accept=".csv, .xlsx, .xls"
                    ref="upload"
                    type="file"
                    name="file"
                    class="button__input"
                    @change="handleUploadFile"
                  />
                </div>
              </div>
              <div class="error--tips">{{ $t('marketing.commons.qscqfdxwj_991fab') }}</div>
              <div class="wrapper--tips">
                <span
                  >{{ $t('marketing.pages.promotion_activity.xznxyfsdxd_cdcdcd') }}</span
                >
                <a :href="excel" :download="$t('marketing.commons.yhsjhmb_578282')"
                  >{{ $t('marketing.commons.yhsjhmb_65d57c') }}</a
                >
              </div>
            </div>
          </v-filters>
          <div
            v-if="errorTips_tapath"
            class="errorTips_tapath"
            style="transform:none;line-height: 1.5;"
          >
            {{ errorTips_tapath }}
          </div>
          <div class="filter_err-phone" v-show="sendObject.sendRange == 3">
            <fx-switch
              v-model="ignoreErrorPhone"
              class="filter_err-phone-switch"
              size="small"
            />
            <span style="margin-left: 8px;">{{ $t('marketing.pages.promotion_activity.hlsjhqdzdc_85fc04') }}</span>
          </div>
          <div class="filter__wrapper">
            <FilterNday v-model="filterNDaySentUser" />
            <div class="filter-switch">
              <fx-switch style="margin-right: 5px;" v-model="filterReceived" size="small"> </fx-switch>
              {{ $t('marketing.components.SpreadDialog.gldqhdzysd_75f81f') }}
            </div>
          </div>
        </el-form-item>

        <el-form-item :label="$t('marketing.pages.promotion_activity.fsmb_c7ba1c')" style="margin-top: -10px;">
          <div
            :class="[error_templateList && 'error']"
          >
            <fx-input :placeholder="$t('marketing.components.SpreadSmsDialog.qxzdxmb_86a60d')" v-model="model_templateName" class="template-select__bar" @focus="handleChooseTemplate"></fx-input>
            <div class="error--tips">{{ $t('marketing.commons.qxzmb_9af823') }}</div>
          </div>
          <!-- 新营销通短信模板才有  获取详情的时候没返回tplVersion 上传Excel时不在这里使用 -->
          <div class="sms-params-wrapper" v-if="!!model_templateList && tplVersion === 1 && model_templateContent.indexOf('}') > 0 && sendObject.sendRange != 3">
            <div class="wrapper-title">
              <span style="color:#ff522a;margin-right: 5px;">*</span>{{ $t('marketing.pages.whatsapp.szbl_23f013') }}
            </div>
            <smsParams
              ref="smsParams"
              :params="model_templateContent"
              :validateParams="validateSmsParams"
              :platform-id="smsTemplateProviderId"
              :originSmsVarArgs="originSmsVarArgs"
              :marketingEventId="marketingEventId"
              @onChange="handleSmsVarArgChange"
            />
          </div>
        </el-form-item>

        <el-form-item :label="$t('marketing.commons.dxnr_957f2c')">
          <div :class="['template--content', error_templateContent && 'error']">
            <div>
              <textarea
                v-model="model_templateContent"
                :placeholder="model_templateType == 1 ? $t('marketing.commons.qxzmb_9af823') : textPlaceholder"
                :class="['textarea', 'disabled']"
                disabled
                ref="template--content"
                :maxlength="450"
              ></textarea>
            </div>
            <div class="error--tips">{{ errorTips_templateContent }}</div>
            <div
              :class="[
                'template--content__tips',
                model_templateType == 999 && 'disabled'
              ]"
              v-if="smsTemplateProviderId === 'mw'"
            >
              {{ $t('marketing.commons.zatdxjf_d7f0ef', {data: ({'option0': length_templateContent, 'option1': messageShort})}) }}
              <!-- {{ length_templateContent }}字/按
              <span class="count">{{ messageShort }}</span
              >条短信计费 -->
              <QuestionTooltip class="question" :offset="135">
                <div
                  slot="question-content"
                  style="max-width: 280px; color: #545861"
                >
                  <div class="question-tips">{{ $t('marketing.components.SpreadSmsDialog.zstjbhqmjs_3feff0', {data: ({'option0': ''})}) }}</div>
                </div>
              </QuestionTooltip>
            </div>
            <div v-if="isSmsTry" class="sms-open-tips">
              {{ $t('marketing.pages.promotion_activity.tsktdxfwkz_8a020e') }}
              <a @click="openSms" class="sms-open-link">{{ $t('marketing.commons.ljkt_0fca8c') }}</a>
            </div>
          </div>
        </el-form-item>

        <el-form-item
          v-if="model_templateType == 1"
          :label="$t('marketing.commons.fssj_f6cbc9')"
          :class="[error_sendTime && 'error']"
        >
          <sendtime-pker v-model="sendConfig"></sendtime-pker>
          <div class="error--tips">{{ $t('marketing.commons.qxzfssj_cc05ec') }}</div>
        </el-form-item>
      </el-form>
    </div>

    <sms-calc-expend-dialog
      :visible="showCalcExpendDialog"
      :param="calParam"
      @update:visible="showCalcExpendDialog = false"
      @upadete:submit="_sendGroupSMS"
    ></sms-calc-expend-dialog>
    <select-Participant-dialog
      v-if="selectParticipantDialogVisible"
      :visible="selectParticipantDialogVisible"
      @update:visible="selectParticipantDialogVisible = false"
      @upadete:submit="confirmSelectParticipant"
      :selectedRow="selectedParticipants"
      :filterPhoneUser="true"
    ></select-Participant-dialog>
    <invite-Participant-dialog
      v-if="inviteParticipantDialogVisible"
      :visible="inviteParticipantDialogVisible"
      @update:visible="inviteParticipantDialogVisible = false"
      @upadete:submit="confirmInviteParticipant"
      :selectedRow="inviteParticipant"
    ></invite-Participant-dialog>
    <LeadTableDialog
      v-if="leadDialogVisible"
      :visible.sync="leadDialogVisible"
      :marketingEventId="marketingEventId"
      :selectedRow="selectedParticipants"
      @confirm="confirmSelectLeads"
    />
    <smsTemplateDialog
      v-if="smsTemplateDialogVisible"
      :tplTypeList="['PROMOTION']"
      :show-real-content="sendObject.sendRange === 3"
      source="smsPromotion"
      :visible.sync="smsTemplateDialogVisible"
      @update:submit="confirmSmsTemplateSelected"
    />
  </div>
</template>

<script>
import { getImageByLang } from '@/utils/i18n.js'

import Selectbar from "@/components/selectbar/index";
import phoneHeader from '@/assets/images/sms/msg_preview.jpg'
import phoneHeaderEn from '@/assets/images/sms/msg_preview-en.jpg'
import phoneFooter from '@/assets/images/sms/msg_bottom.png'
const toolInterface = require("@/services/util/toolInterface").default;
import phoneFooterEn from '@/assets/images/sms/msg_bottom-en.png'
const phoneJiiao = require("@/assets/images/sms/msg_jiao.png");
import smsCalcExpendDialog from "@/pages/sms-marketing/components/sms-calc-expend-dialog/index"
import smsTemplateDialog from './sms-template-dialog.vue'
import http from "@/services/http/index";
import _ from "lodash";
import util from "@/services/util/index";
import excel from "@/assets/static/sms-phone-import-template-new.xlsx";
import VFilters from "@/pages/promotion-activity/common/filters";
import QuestionTooltip from "@/components/questionTooltip/index";
import { goCrmmanageSmsmanage } from "@/utils/index";
import selectParticipantDialog from "./select-Participant-dialog";
import inviteParticipantDialog from "./invite-participant-dialog";
import LeadTableDialog from "./lead-table-dialog";
import { MaterialInstance } from "@/components/select-material-dialog";
import sendtimePker from "../../common/sendtime-pker.vue";
import FilterNday from "@/components/filter-nday";
import { getShortUrl } from "@/utils/index";
import { sendObjectResult2Str } from "@/utils/tranformUtil";
import smsParams from './sms-params.vue'

const templatesList = [];
const hashConfig = {
  1: "article_detail",
  3: "conference_detail",
  4: "spread_product_detail"
};
const dataPickerOptions = {
  disabledDate: date => {
    return date.getTime() < util.dateAtStart(Date.now());
  }
};
$.fn.extend({
  insertAtCaret: function(myValue) {
    var $t = $(this)[0];
    if (document.selection) {
      this.focus();
      sel = document.selection.createRange();
      sel.text = myValue;
      this.focus();
    } else if ($t.selectionStart || $t.selectionStart == "0") {
      var startPos = $t.selectionStart;
      var endPos = $t.selectionEnd;
      var scrollTop = $t.scrollTop;
      $t.value =
        $t.value.substring(0, startPos) +
        myValue +
        $t.value.substring(endPos, $t.value.length);
      this.focus();
      $t.selectionStart = startPos + myValue.length;
      $t.selectionEnd = startPos + myValue.length;
      $t.scrollTop = scrollTop;
    } else {
      this.value += myValue;
      this.focus();
    }
  }
});

export default {
  components: {
smsCalcExpendDialog,
elSelect: FxUI.Select.components.ElSelect,
elOption: FxUI.Select.components.ElSelect.components.ElOption,
elRadio: FxUI.Radio,
elDatePicker: FxUI.DatePicker,
elForm: FxUI.Form,
elFormItem: FxUI.FormItem,
VFilters,
QuestionTooltip,
selectParticipantDialog,
Selectbar,
inviteParticipantDialog,
LeadTableDialog,
sendtimePker,
FilterNday,
smsTemplateDialog,
smsParams,
},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    marketingEventId: {
      type: String,
      default: ""
    },
    smsSendId: {
      type: String,
      default: ""
    }
  },
  data() {
    const { user_group } = this.$route.query;
    return {
      textPlaceholder: $t('marketing.commons.qsr_02cc4f'),
      event_type: "", //市场活动类型
      isFromLive: false,
      isConferenceMarketingEvent: false, // 当前市场活动是否会议营销市场活动
      inviteParticipantDialogVisible: false,
      selectParticipantDialogVisible: false,
      leadDialogVisible: false,
      selectedParticipants: {},
      inviteParticipant: {},
      isSmsTry: false,
      sendObject: {
        crowd: user_group ? user_group.split(",") : [],
        sendRange: 2 // 创建营销活动时给sendRange赋值： 2：目标人群 3：会议参会人（报名） 4：会议邀约人
      }, //这里1：选择标签 2：目标人群 3：上传手机号清单  4：会议参会人 5：会议邀约人
      filterNDaySentUser: {
        checked: false,
        day: 1
      },
      filterReceived: false,
      srgTypes: [{ label: $t('marketing.commons.ambrqxz_ace093'), value: 2 }],
      exSendRange: [{ label: $t('marketing.commons.scsjhqd_1011aa'), value: 3 }],
      phoneHeader: getImageByLang([phoneHeader, phoneHeaderEn]),
      phoneFooter: getImageByLang([phoneFooter, phoneFooterEn]),
      phoneJiiao,
      excel,

      showCalcExpendDialog: false,
      showCreateShortUrlDialog: false,

      signature: "--", // 短信签名
      dataPickerOptions, // 预置的时间快捷选项
      fileName: null, // 群发对象 - 文件名
      model_fileTApath: null, // 群发对象 - 文件tapath
      isUploading: false, // 群发对象 - 文件是否上传中
      model_templateType: 1, // 短信内容 - 模板类型 1-使用已有模板 2-创建新模板
      templatesList, // 短信内容 - 模板列表
      model_templateList: null, // 短信内容 - 选中的模板id
      tplVersion: 0,  // 模板版本 0旧模板 1营销通新模
      // 只有营销短信有那个拒收请回复R
      tplType: null, // 模板类型 0通知短信 1营销短信
      model_templateName: null, // 短信内容 - 模板名称
      model_templateContent: "", // 短信内容 - 模板内容
      error_tapath: false, // 群发对象 - 文件tapath
      error_templateList: false, // 短信内容 - 选中的模板id
      error_templateName: false, // 短信内容 - 模板名称
      error_templateContent: false, // 短信内容 - 模板内容
      error_sendTimeForTtype1: false, // 发送时间 - 发送类型（模板类型为1时）
      error_sendTime: false, // 发送时间 - 定时发送的发送时间

      errorTips_templateName: "",
      errorTips_templateContent: "",
      errorTips_tapath: "",

      param: {},
      calParam: {},
      messageShort: 1,
      showMaterialChooseDialog: false,
      paramDataList: [],
      isFromConference: false,
      source: "",
      smsMap: {},
      materialInfo_shortUrl_map: {}, // 短链映射物料的map，用于提交时匹配短信中所具有的物料，并提交给后台
      sendConfig: {
        type: 1
      },
      insertLinkType: "",
      configOrigin: this.$store.state.Global.configOrigin,
      ignoreErrorPhone: false,
      smsTemplateDialogVisible: false,
      smsVarArgsParams: {
        smsVarArgs: [],
        paramsValid: true
      },
      originSmsVarArgs: [],
      validateSmsParams: false,
      smsTemplateProviderId: 'mw'
    };
  },
  watch: {
    smsSendId() {
      console.log("smsSendId");
      this.loadDataOnEdit();
    },
    model_fileTApath() {
      this.handleValidate();
    },
    model_templateType(val) {
      let sendButtonText = "";
      if (val == 1) {
        sendButtonText = $t('marketing.commons.fs_1535fc');
      } else if (val == 2) {
        sendButtonText = $t('marketing.commons.sh_cf13b1');
      }
      this.$emit("setSendButtonText", {
        sendButtonText,
        model_templateType: val
      });
      this.handleValidate();
    },
    model_templateList() {
      this.handleValidate();
    },
    model_templateName() {
      this.handleValidate();
    },
    model_templateContent() {
      this.handleValidate();
    },
    sendConfig() {
      this.handleValidate();
    },
    length_templateContent() {
      this.calMessageShort();
    },
    marketingEventId() {
      console.log("marketingEventId", this.marketingEventId);
      this.checkConferenceStatus();
    },
    'sendObject.crowd':{
      handler(){
        this.handleValidate();
        this.ignoreErrorPhone = false
      }
    },
    sendObject:{
      handler(){
        this.handleValidate();
      },
      deep: true
    }
  },
  computed: {
    smd_menus() {
      return this.marketingEventId
        ? ["marketingEvent", "materials"]
        : ["materials"];
    },
    // 1:文章 3:会议 4:产品 10:落地页 24:海报 16:表单 25:邀请函
    smd_tabbar() {
      return this.isConferenceMarketingEvent ? [3, 1] : [10, 1, 4];
    },
    smd_marketingEventTabbar() {
      return this.isConferenceMarketingEvent ? [3, 1] : [10, 1, 4];
    },
    length_templateContent() {
      return (this.model_templateContent.length || 0)
    },
  },
  methods: {
    handleShowMaterialDialog(type) {
      this.insertLinkType = type; // h5链接 miniApp 小程序链接
      this.showMaterialChooseDialog = true;
    },
    // 查询当前市场活动是否会议营销市场活动
    async checkConferenceStatus() {
      if (!this.marketingEventId) return;
      const { errCode, data } = await http.checkConferenceStatus({
        marketingEventId: this.marketingEventId
      });
      if (errCode == 0) {
        this.isConferenceMarketingEvent = data.bindConference;
      } else {
        this.isConferenceMarketingEvent = false;
      }
    },
    confirmInviteParticipant(data) {
      if (data && data.length) {
        this.errorTips_tapath = "";
      }
      let selected = {
        colleague: this.inviteParticipant.colleague || []
      };
      data.forEach(item => {
        let _item = {
          name: item.invitorName,
          id: item.id,
          phone: item.phone
        };
        const idx = _.findIndex(
          selected.colleague,
          element => element.id == _item.id
        );
        if (idx == -1) {
          selected.colleague.push(_item);
        }
      });
      this.inviteParticipant = selected;
    },
    confirmSelectParticipant(data) {
      if (data && data.length) {
        this.errorTips_tapath = "";
      }
      let selected = {
        colleague: this.selectedParticipants.colleague || []
      };
      data.forEach(item => {
        let _item = {
          name: item.name,
          id: item.conferenceEnrollId,
          phone: item.phone
        };
        const idx = _.findIndex(
          selected.colleague,
          element => element.id == _item.id
        );
        if (idx == -1) {
          selected.colleague.push(_item);
        }
      });
      this.selectedParticipants = selected;
    },
    confirmSelectLeads(data) {
      if (data && data.length) {
        this.errorTips_tapath = "";
      }
      let selected = {
        colleague: this.selectedParticipants.colleague || []
      };
      data.forEach(item => {
        let _item = {
          name: item.name,
          id: item.id,
          phone: item.phone
        };
        const idx = _.findIndex(
          selected.colleague,
          element => element.id == _item.id
        );
        if (idx == -1) {
          selected.colleague.push(_item);
        }
      });
      this.selectedParticipants = selected;
    },
    removeItem() {},
    openSms() {
      goCrmmanageSmsmanage();
    },
    parseMaterialUrl(url) {
      url = url.replace(/!!marketingActivityId!!/g, "");
      url = url.replace(/!!wxAppId!!/g, "");
      return url;
    },
    async handleApply(data) {
      console.log("handleApply", data);
      this.showMaterialChooseDialog = false;
      let { id, type, objectType,title, url, mpUrl } = data;
      const href = this.parseMaterialUrl(url);
      let shortUrl = "";
      let longUrl = "";
      if (this.insertLinkType == "h5") { //H5链接
        let res = await http.getShortUrl({
          longUrl: href
        });
        let { errCode, data } = res;
        if (errCode != 0) return;
        shortUrl = data.shortUrl;
        longUrl = data.longUrl;
      } else if (this.insertLinkType == "miniApp") {  //小程序内容链接
        let origin = this.configOrigin || location.origin
        const { enterpriseAccount } = FS.contacts.getCurrentEmployee() || {};
        let params = {
          longUrl: `${origin}/proj/page/marketing-mplink?ea=${enterpriseAccount||''}&objectType=${objectType}&objectId=${id}&redirectType=wechat`
        };
        let res = await http.getShortUrl(params);
        let { errCode, data } = res;
        if (errCode != 0) return;
        shortUrl = data.shortUrl;
        longUrl = this.parseMaterialUrl(mpUrl);
      }
      let ea = FS.contacts.getCurrentEmployee().enterpriseAccount;
      this.smsMap[
        shortUrl
      ] = `${longUrl}&channelType=6&spreadChannel=sms&ea=${ea}&marketingActivityId=`;
      $(this.$refs["template--content"]).insertAtCaret($t('marketing.commons.dj_4c3a12', {data: ( {'option0': shortUrl})}));
      const newTemplateContent2 = $(this.$refs["template--content"]).val();
      this.model_templateContent = newTemplateContent2; // 触发vue watcher
      this.setDataToMaterialInfoShortUrlMap(shortUrl, data);
    },
    setDataToMaterialInfoShortUrlMap(
      shortUrl,
      materialDataFromSelectMaterialDialog = {}
    ) {
      if (!shortUrl) return;
      this.materialInfo_shortUrl_map[shortUrl] = {
        objectId: materialDataFromSelectMaterialDialog.id,
        contentType: materialDataFromSelectMaterialDialog.type
      };
    },
    loadDataOnEdit() {
      if (this.smsSendId == "") {
        return;
      }
      http
        .getSMSSendHistoryByIdForReset({ smsSendId: this.smsSendId })
        .then(res => {
          if (res && res.errCode == 0) {
            const data = res.data;
            if (data.templateId) {
              this.model_templateName = data.title;
              this.model_templateContent = data.templateContent;
            } 
          }
        });
    },
    handleChooseFile() {
      this.$refs.upload.click();
    },
    handleUploadFile(e) {
      const me = this;
      this.isUploading = true;
      console.log("handleUploadFile", e);
      const file = e.target.files[0];
      e.target.value = ""; // 清空input，避免下次选同一文件失效
      const { name, size, type } = file;
      console.log(name, size, type);
      this.fileName = name;
      toolInterface.uploadFile(file).then(res => {
        console.log(res.status, res);
        me.isUploading = false;
        if (res.status == "success") {
          FxUI.Message.success($t('marketing.commons.sccg_a7699b'));
          this.model_fileTApath = res.tapath;
        } else if (res.status == "fail") {
          FxUI.Message.error($t('marketing.commons.scsbqzs_8e2f99'));
          this.model_fileTApath = null;
          this.fileName = null;
        }
      });
    },
    submitUpload() {
      this.$refs.upload.submit();
    },
    handleRemove(file, fileList) {
      console.log(file, fileList);
    },
    handlePreview(file) {
      console.log(file);
    },
    confirmSmsTemplateSelected(template = {}) {
      const { id,tplVersion,status,name,providerId } = template
      this.model_templateList = id;
      this.tplVersion = tplVersion
      this.smsTemplateProviderId = providerId
      // 新建的时候
      this.model_templateType = status == 'APPROVED' ? 1 : 2
      this.model_templateName = name
      // 重置
      this.originSmsVarArgs = []
      this.model_templateContent = (providerId === 'mw' && this.signature) ? `【${this.signature}】` + template.content : template.content;
    },
    // 校验
    handleValidate() {
      if (!this.validateListening) {
        return;
      }
      const errorListener = {
        error_tapath: false, // 群发对象 - 文件tapath
        error_templateList: false, // 短信内容 - 选中的模板id
        error_templateName: false, // 短信内容 - 模板名称
        error_templateContent: false, // 短信内容 - 模板内容
        error_sendTimeForTtype1: false, // 发送时间 - 发送类型（模板类型为1时）
        error_sendTime: false // 发送时间 - 定时发送的发送时间
      };
      let flag = false;

      const sendObject = this.sendObject;
      if (sendObject.sendRange == 2) {
        errorListener.error_tapath =
          !sendObject.crowd || !sendObject.crowd.length;
        if (errorListener.error_tapath) {
          this.errorTips_tapath = $t('marketing.commons.qxzmbrq_b8cc9a');
        } else {
          this.errorTips_tapath = ''
        }
      } else if (sendObject.sendRange == 4) {
        errorListener.error_tapath =
          !this.selectedParticipants.colleague ||
          this.selectedParticipants.colleague.length == 0;
        if (errorListener.error_tapath) {
          this.errorTips_tapath = $t('marketing.commons.qxzchry_f5b0b6');
        }
      } else if (sendObject.sendRange == 5) {
        errorListener.error_tapath =
          !this.inviteParticipant.colleague ||
          this.inviteParticipant.colleague.length == 0;
        if (errorListener.error_tapath) {
          this.errorTips_tapath = $t('marketing.pages.promotion_activity.qxzyyry_3b8129');
        }
      } else if (sendObject.sendRange == 6) {
        errorListener.error_tapath =
          !this.selectedParticipants.colleague ||
          this.selectedParticipants.colleague.length == 0;
        if (errorListener.error_tapath) {
          this.errorTips_tapath = $t('marketing.pages.promotion_activity.qxzhdry_1165c9');
        }
      } else {
        errorListener.error_tapath = !this.model_fileTApath;
      }
      if (this.model_templateType == 1) {
        // 使用已有模板
        errorListener.error_templateList = !this.model_templateList;
        errorListener.error_sendTimeForTtype1 = !this.sendConfig.type;
        if (this.sendConfig.type == 2) {
          errorListener.error_sendTime = !this.sendConfig.time;
        }
      } else if (this.model_templateType == 2) {
        // 创建新模板
        if (!this.model_templateName || this.model_templateName.length == 0) {
          // 空值校验
          errorListener.error_templateName = true;
          this.errorTips_templateName = $t('marketing.commons.qsrmbmc_8f21b9');
        } else if (util.hasSpecailCharacters(this.model_templateName)) {
          // 正则校验
          errorListener.error_templateName = true;
          this.errorTips_templateName = $t('marketing.commons.qbysrtszf_d4a2fe');
        }
        if (
          !this.model_templateContent ||
          this.model_templateContent.length == 0
        ) {
          // 空值校验
          errorListener.error_templateContent = true;
          this.errorTips_templateContent = $t('marketing.commons.qsrmbnr_73536b');
        } else if (this.length_templateContent > 450) {
          // 超字校验
          errorListener.error_templateContent = true;
          this.errorTips_templateContent = $t('marketing.pages.promotion_activity.qbysrcggz_4427b7');
        }
      }

      console.log(`=== handleValidate >> errorListener ===`, errorListener);
      for (const key in errorListener) {
        console.log(`=== errorListener >> ${key} ===`, errorListener[key]);
        this[key] = errorListener[key];
        !flag && (flag = errorListener[key]);
      }
      // flag && FxUI.Message.error('校验失败');
      return flag;
    },
    handleSubmit(smsEventType) {
      //  1保存为草稿 2 发送
      this.validateListening = true; // 启动校验
      let smsVarArgs = [],paramsValid = true
      if(this.sendObject.sendRange !== 3 && this.tplVersion === 1){
        smsVarArgs = this.smsVarArgsParams.smsVarArgs
        paramsValid = this.smsVarArgsParams.paramsValid
        this.validateSmsParams = true
      }
      if (this.handleValidate() || !paramsValid) {
        return Promise.reject(); // 短信被包装成营销活动，子组件向外抛出异步校验信息
      }
      const param = {
        taPath: this.model_fileTApath, // 群发对象的excel文件
        eventType: smsEventType
      };
      this.smsSendId != "" && (param.smsSendId = this.smsSendId);
      // 发送类型 1-立即发送 2-定时发送
      param.type = this.sendConfig.type;
      // 是否过滤手机号清单错误号码
      param.ignoreErrorPhone = this.ignoreErrorPhone
      param.filterReceived = this.filterReceived
      if(this.sendObject.sendRange !== 3){
        param.smsVarArgs = smsVarArgs
      }
      //发送时间
      param.type == 2 &&
        (param.fixedTime = this.sendConfig.time
          ? this.sendConfig.time
          : null);
      param.templateId = this.model_templateList;
      param.templateName = this.model_templateName;
      param.templateContent = this.model_templateContent.substring(this.signature.length + 2)
      // 拒收请回复R 跟短信内容有一个空格
      this.param = param;
      this.calParam = {
        taPath: this.param.taPath,
        templateId: this.param.templateId,
        marketingEventId: this.marketingEventId,
        ignoreErrorPhone: this.param.ignoreErrorPhone,
        filterReceived: this.param.filterReceived
      }; 
      const calParam = this.calParam;

      this.param.marketingActivityAuditData = {
        sendGroup: this.sendObject.sendRange === 3 ? this.fileName:sendObjectResult2Str(this.sendObject),
      }

      if (this.sendObject.sendRange === 2) {
        calParam.userGroupIds = _.map(
          this.sendObject.crowd || [],
          item => item.id
        );
        delete calParam.taPath;
      } else if (this.sendObject.sendRange === 3) {
        this.param.taPathName = this.fileName;
      }
      // 将短信内容中，所引用的物料信息提交给全员推广接口
      if (this.param.templateContent) {
        const materialInfos = [];
        Object.keys(this.materialInfo_shortUrl_map).forEach(item => {
          this.param.templateContent.indexOf(item) !== -1 &&
            materialInfos.push(this.materialInfo_shortUrl_map[item]);
        });
        this.$set(this.param, "materialInfos", materialInfos);
      }

      //过滤N天内已接受推送用户
      if (this.filterNDaySentUser.checked) {
        this.param.filterNDaySentUser = this.filterNDaySentUser.day;
        this.calParam.filterNDaySentUser = this.filterNDaySentUser.day;
      }
      if(this.smsTemplateProviderId !== 'mw'){
        const dataParam = this.param;
        Object.assign(dataParam, this.sendObject);
        dataParam.shortUrlMap = this.smsMap;
        return Promise.resolve(dataParam)
      }
      this._calcCoast();

      return new Promise((resolve, reject) => {
        // 短信被包装成营销活动，子组件向外抛出异步校验信息
        this._spResolve = resolve; // 后面要注意解除这里的引用
        this._spReject = reject;
      });
    },
    _calcCoast() {
      this.showCalcExpendDialog = true;
    },
    _sendGroupSMS() {
      const param = this.param;
      Object.assign(param, this.sendObject);
      param.shortUrlMap = this.smsMap;
      if (param) {
        // 短信被包装成营销活动，由外层组件执行接口调用
        return this._spResolve(param); // 按需求必定命中这个 if，后续逻辑将弃用
      }
      this.isSubmiting = true;
      http.sendGroupSMS(param).then(res => {
        this.isSubmiting = false;
        if (res === false) {
          FxUI.Message.error($t('marketing.commons.xtcwqshzs_c236af'));
          return;
        }
        if (res.errCode) {
          if (res.errCode == 60017) {
            FxUI.Message.error($t('marketing.pages.promotion_activity.qzqfdxwjzj_c2efb7'));
          } else {
            FxUI.Message.error(res.errMsg || $t('marketing.commons.qqsbqshzs_d5dd7d'));
          }
          return;
        }
        FxUI.Message.success($t('marketing.commons.tjcgqnxdhs_800763'));
        this.handleCloseDialog();
        this.$emit("update:submit");
        this._goBack();
      });
    },
    _goBack() {
      this.$router.go(-1); // 短信被包装成营销活动，发送成功后页面后退而不是前进到指定页面
    },
    // 关闭弹框
    handleCloseDialog(isShow) {
      if (isShow === true) return;
      this.$emit("update:visible", false);
    },
    loadSignature() {
      http.querySignature().then(res => {
        if (
          (res && res.errCode == 60004) ||
          (res.errCode == 0 && res.data.status != 0)
        ) {
          http.queryTrial().then(res => {
            if (res.data && res.data.signature) {
              this.isSmsTry = true;
              this.signature = res.data.signature;
              this.model_templateContent = `【${this.signature}】`
            }
          });
        } else if (res && res.errCode === 0) {
          this.signature = res.data.signature;
          this.model_templateContent = `【${this.signature}】`
        } else {
          FxUI.Message.error(res.errMsg || $t('marketing.commons.qmhqsbqshz_fc313a'));
        }
      });
    },
    calMessageShort() {
      if (this.length_templateContent <= 70) {
        this.messageShort = 1;
      } else {
        this.messageShort = Math.ceil(this.length_templateContent / 67);
      }
    },
    clearSelected() {
      this.selectedParticipants = {};
    },
    async loadDataOnUrlHasContentId() {
      const { contentType, contentId } = this.$route.query;
      if (contentType && contentId) {
        const result = await MaterialInstance.getMaterialDetailByContentType(
          contentType,
          contentId
        );
        if (result && result.errCode === 0) {
          const data = MaterialInstance.formatDataByContentType(
            contentType,
            result.data
          );
          this.model_templateType = 2;

          const href = this.parseMaterialUrl(data.url);
          const shorturl = await getShortUrl(href);
          this.model_templateContent = $t('marketing.commons.dj_4c3a12', {data: ( {'option0': shorturl})});
          this.smsMap[shorturl] = `${href}&marketingActivityId=`;

          this.setDataToMaterialInfoShortUrlMap(shorturl, data);
          // //是否是海报
          // const isPoster = objectType == 24;
          // this.priValue = {
          //   ...this.priValue,
          //   ...data,
          //   msgType: isPoster ? 3 : 2,
          //   image: isPoster ? data.aPath : "",
          //   text: isPoster
          //     ? ""
          //     : `<a target="_blank" href="${data.url}">${data.title}</a>`
          // };
        }
      }
    },
    handleChooseTemplate(){
      // 创建时候打开新建
      if(!this.marketingEventId){
        FxUI.Message.warning($t('marketing.commons.qxxzschd_493166'))
        return
      }
      this.smsTemplateDialogVisible = true
    },
    queryMarketingActivityDetail() {
      http
        .queryMarketingActivityDetail({
          id: this.$route.query.marketingActivityId
        })
        .then(res => {
          if (res && res.errCode == 0) {
            //再次发送数据回填
            if (
              res.data.eventType == "3" ||
              res.data.eventType == "live_marketing"
            ) {
              this.event_type = res.data.eventType;
            }
            let result = res.data.marketingActivityGroupSenderDeatilVO;
            this.model_templateList = result.templateId;
            this.model_templateName = result.templateName;
            this.model_templateContent = result.content;
            this.originSmsVarArgs = result.smsVarArgs || []
            // 有自定义参数说明是新模板
            if (result.sendRange == 2) {
              let marketingUserGroupIds = result.marketingUserGroupIds;
              this.sendObject = {
                sendRange: 2,
                crowd: marketingUserGroupIds,
                refresh: true
              };
            } else if(result.sendRange == 1){
              this.sendObject = {
                sendRange: 3,
                refresh: true
              };
            }
            this.getSmsTemplateDetail(result.templateId)
          }
        });
    },
    getSmsTemplateDetail(id) {
      if(!id) return
      http.sendGetSmsTemplateDetail({ templateId: id }).then(res => {
        if (res && res.errCode === 0) {
          const resData = res.data
          this.tplVersion = resData.tplVersion || 0
        }
      })
    },
    handleSmsVarArgChange(data){
      this.smsVarArgsParams = data
    }
  },
  mounted() {
    this.checkConferenceStatus();
    this.loadSignature();
    this.loadDataOnEdit();
    this.loadDataOnUrlHasContentId();
  },
  created() {
    //再次发送
    if (this.$route.query.marketingActivityId) {
      this.queryMarketingActivityDetail();
    }
  }
};
</script>

<style lang="less">
.transition() {
  transition: all 500ms ease-out 0ms;
}
.sms-create__content {
  display: flex;
  .el-form-item__label {
    font-size: 13px;
    color: #151515;
  }
  .transition;
  .insert-param-select {
    color: var(--color-info06,#407FFF);
    margin-left: 5px;
  }
  .form__left {
    position: relative;
    width: 270px;
    display: flex;
    justify-content: center;
    background: #fafafa;
    border-right: 1px solid #e9edf5;
    .preview-label {
      position: absolute;
      top: 40px;
      left: 30px;
      color: @color-title;
    }
    .wrapper--phone {
      margin-top: 80px;
      height: 370px;
      border: 1px solid #ddd;
    }
    .wrapper--phone__body {
      height: 308px;
      background: #fff;
      padding-top: 20px;
      padding-left: 13px;
      overflow: auto;
    }
    .wrapper--msg {
      position: relative;
      padding: 5px 10px;
      width: 182px;
      box-sizing: border-box;
      background: #e5e5ea;
      font-size: 12px;
      line-height: 15px;
      border-radius: 8.65px;
      word-wrap: break-word;
    }
    .wrapper--msg__jiao {
      position: absolute;
      bottom: -3px;
      left: -4px;
    }
  }
  .form__right {
    padding: 20px 10px 0 40px;
    box-sizing: border-box;
    .selectbar_wrapper {
      display: flex;
      margin-bottom: 30px;
      max-width: 800px;
      .selectbar-input {
        .btn-more {
          height: 27px;
          line-height: 27px;
        }
      }
      .select-range-bar {
        height: 30px;
      }
      .select_wrapper-lable {
        width: 100px;
        flex: none;
        margin-top: 10px;
      }
      .selectbar-lable {
        width: 120px;
        flex: none;
        margin-top: 10px;
      }
      .participant {
        flex: 0 0 120px;
        margin-left: 10px;
        height: 32px;
        line-height: 32px;
      }
      .button {
        height: 30px;
        margin-left: 10px;
        width: 126px;
        color: var(--color-primary06,#407FFF);
      }
    }
    .errorTips_tapath {
      color: #ff7e7e;
      transform: translate(96px, -20px);
    }
  }

  .error--tips {
    height: 0;
    line-height: 1.2;
    opacity: 0;
    overflow: hidden;
    width: 100%;
    color: #ff7e7e;
    box-sizing: border-box;
    margin-top: 0px;
    font-size: 14px;
    padding-left: 10px;
    .transition;
  }
  .error {
    .error--tips {
      margin-top: 1px;
      height: auto;
      opacity: 1;
    }
  }

  .wrapper--upload {
    display: flex;
    .wrapper--upload__display {
      flex: 1 1 auto;
      margin-right: 10px;
    }
    .display__bar {
      height: 36px;
      border: 1px solid #ddd;
      border-radius: 4px;
      display: flex;
      align-items: center;
      padding-left: 15px;
      box-sizing: border-box;
      font-size: 13px;
      color: #606266;
      cursor: pointer;
      .transition;
    }
    .button__input {
      display: none;
    }
  }
  .wrapper--tips {
    margin-left: -200px;
    text-align: right;
    margin-top: 5px;
    font-size: 12px;
    color: #999999;
    line-height: 18px;
  }
  .filter_err-phone{
    height: 22px;
    margin-top: 8px;
  }
  .filter__wrapper{
    display: flex;
    .filter-switch{
      font-size: 13px;
      color: #181c25;
      padding-top: 13px;
      margin-left: 32px;
    }

  }
  .template--type {
    font-size: 13px;
  }
  .template--content {
    position: relative;
    .icon-btn {
      position: absolute;
      cursor: pointer;
      &.disabled {
        cursor: default;
      }
      &.hide {
        display: none;
      }
    }
    .km-ico-link {
      top: 110px;
      left: 5px;
    }
    .add-material-link-btn {
      font-style: normal;
      top: 110px;
      left: 32px;
      line-height: 22px;
      content: " ";
      display: inline-block;
      width: 22px;
      height: 22px;
      background-image: url("../../../../assets/images/icons/sms-add-material.png");
      background-repeat: no-repeat;
      background-size: contain;
      overflow: hidden;
      vertical-align: middle;
    }
    .mini-app-btn {
      top: 110px;
      left: 60px;
      width: 20px;
      height: 20px;
      background-repeat: no-repeat;
      background-size: contain;
      background-image: url("../../../../assets/images/icon/miniprogram-icon-gray.png");
    }
    .insert-param-select {
      top: 104px;
      left: 80px;
      height: 30px;
      overflow: hidden;
      .el-input__inner {
        border: none;
        color: var(--color-info06,#407FFF);
        &::placeholder {
          color: var(--color-info06,#407FFF);
        }
      }
      .el-input__suffix {
        transform: translateX(-38px);
        border: none;
      }
      .el-input__icon {
        &::after {
          background-image: url("../../../../assets/images/icon/icon-arrow-blue.png");
          transform: rotate(180deg);
        }
      }
    }
  }
  .template--content__tips {
    font-size: 12px;
    line-height: 1.2;
    color: #999999;
    margin-top: 5px;
    opacity: 1;
    height: auto;
    overflow: hidden;
    .transition;
    &.disabled {
      opacity: 0;
      height: 0;
    }
    .count {
      color: #f09835;
    }
  }
  .sms-open-tips {
    font-size: 12px;
    color: #91959e;
    margin-top: 12px;
  }
  .sendTimeForTtype1__picker {
    margin-left: 10px;
  }
  a {
    font-size: 12px;
    color: var(--color-primary06,#407FFF);
    cursor: pointer;
  }
  input {
    .transition;
  }
  .textarea__wrapper {
    height: 135px;
    border: 1px solid #ddd;
    border-radius: 4px;
  }
  textarea {
    border: none;
    padding-bottom: 20px;
    display: block;
    font-size: 13px;
    color: #333;
    width: 680px;
    height: 110px;
    overflow: auto;
    resize: none;
    padding: 8px 15px;
    box-sizing: border-box;
    transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
    .transition;
    &.disabled {
      background: #f2f2f2;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
  }
  .question {
    display: inline-block;
    vertical-align: -2px;
    margin-left: 3px;
    .question-tips {
      font-size: 12px;
    }
  }
  .create-new-template-tips {
    background: #fff3eb;
    border: 1px solid #ffe3ca;
    border-radius: 4px;
    padding: 10px 14px;
    line-height: 24px;
    margin-bottom: 16px;
    font-size: 14px;
    line-height: 22px;
    color: #545861;
  }
  .sms-params-wrapper{
    .wrapper-title{
      color: #545861;
      font-size: 14px;
      font-weight: 400;
      line-height: 20px;
      margin:20px 0 8px 0;
    }
  }
}
</style>
