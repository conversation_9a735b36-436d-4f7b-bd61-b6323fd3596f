<template>
  <v-scaffold
    :title="$t('marketing.pages.promotion_activity.yjtgrw_a9e83d')"
    :btn-state="btnState"
    :btn-names="btnNames"
    :need-cancel-confirm="false"
    @cancel="handleCancel"
    @send="handleSend"
  >
    <div class="marketing-mail-promotion__wrapper" v-loading="loading">
      <div class="marketing-mail-promotion">
        <div class="notice-header">
          <div class="notice-title">
            {{ $t('marketing.pages.promotion_activity.glyxftyyjm_8aef60') }}
          </div>
          <div class="notice-detail-item">
            <div class="notice-detail-item-label">
              {{ $t('marketing.commons.rwmc_78caf7') }}
            </div>
            <div>{{ noticeDetail.title }}</div>
          </div>
          <div class="notice-detail-item">
            <div class="notice-detail-item-label">
              {{ $t('marketing.pages.promotion_activity.rwsm_1d35dc') }}
            </div>
            <div>{{ noticeDetail.description }}</div>
          </div>
          <div class="notice-detail-item">
            <div class="notice-detail-item-label">
              {{ $t('marketing.commons.rwsj_b341f9') }}
            </div>
            <div>{{ timeRange }}</div>
          </div>
          <div class="notice-detail-item">
            <div class="notice-detail-item-label">
              {{ $t('marketing.pages.promotion_activity.rwxfr_145abf') }}
            </div>
            <div>{{ fullName }}</div>
          </div>
        </div>
        <div class="notice-content">
          <div class="notice-content-title">
            <div>{{ $t('marketing.commons.yjnr_425169') }}</div>
          </div>
          <div
            v-if="noticeDetail.emailItem"
            class="notice-content-body"
          >
            <div class="notice-content-body-item">
              <div class="notice-content-body-label">
                {{ $t('marketing.pages.promotion_activity.zt_9970ad') }}
              </div>
              <div>{{ noticeDetail.emailItem.title }}</div>
            </div>
            <div class="notice-content-body-item">
              <div class="notice-content-body-label">
                {{ $t('marketing.pages.promotion_activity.zw_58378f') }}
              </div>
              <div
                style="flex: 1; max-width: 600px; width: calc(100% - 100px)"
                v-html="mailContent"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </v-scaffold>
</template>

<script>
import VScaffold from '@/components/scaffold/index.vue'
import http from '@/services/http/index.js'
import util from '@/services/util/index.js'

export default {
  components: {
    VScaffold,
  },
  props: {
    params: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      loading: true,
      btnNames: [$t('marketing.pages.promotion_activity.qwyxfs_6dfcfb'), $t('marketing.pages.promotion_activity.tc_c39922')],
      noticeDetail: {},
      mailContent: '',
    }
  },
  computed: {
    timeRange() {
      return `${this.noticeDetail.startTime
        ? util.formatDateTime(this.noticeDetail.startTime) : '--'
      } - ${
        this.noticeDetail.endTime ? util.formatDateTime(this.noticeDetail.endTime) : '--'}`
    },
    fullName() {
      return `${this.noticeDetail.departmentName ? `${this.noticeDetail.departmentName}-` : ''}${FS.contacts.getUser().name || ''}`
    },
  },
  created() {},
  mounted() {
    // 从路由参数解出 noticeId
    const { noticeId } = this.$route.query
    if (noticeId) {
      this.getMailPromotionNoticeDetail(noticeId)
    }
  },
  methods: {
    handleCancel() {
      window.close()
    },
    handleSend() {
      CRM.api.mail({
        // 对象数据相关
        apiname: 'MarketingActivityObj', // 例如：AccountObj, LeadsObj, ContactObj
        _id: this.noticeDetail.marketingActivityId, // CRM对象的唯一标识符

        // 收件人相关
        // emails: [],
        // ccList: ["抄送邮箱地址数组"],
        // scList: ["密送邮箱地址数组"],

        // 邮件内容
        subject: this.noticeDetail.emailItem.title,
        content: this.mailContent,

        // 邮件模板相关
        noTemplate: true, // 是否不使用模板
        senderModelName: 'marketing',
        mailSendSuccess: () => {
          this.$confirm($t('marketing.pages.promotion_activity.yfscg_3e2f10'), $t('marketing.commons.ts_02d981'), {
            distinguishCancelAndClose: true,
            confirmButtonText: $t('marketing.pages.promotion_activity.tc_c39922'),
            cancelButtonText: $t('marketing.pages.promotion_activity.jxfs_a95d8a'),
            type: 'success',
          })
            .then(() => {
              window.close()
            })
            .catch(action => {
              if (action === 'cancel') {
                this.handleSend()
              }
            })
        },
      })
    },
    getMailPromotionNoticeDetail(noticeId) {
      http.getNoticeByUser({
        noticeId,
      }).then(res => {
        this.loading = false
        if (res && res.errCode === 0 && res.data) {
          this.noticeDetail = res.data
          const { emailItem, marketingActivityId, marketingEventId } = res.data
          const { content } = emailItem || {}

          const spreadFsUid = FS.contacts.getUser().id

          this.mailContent = content.replace(/!!marketingActivityId!!/g, marketingActivityId || '')
            .replace(/!!marketingEventId!!/g, marketingEventId || '')
            .replace(/!!spreadFsUid!!/g, spreadFsUid || '')
            .replace(/!!email!!/g, '')
            .replace(/!!qrCodeId!!/g, '')
            .replace(/!!wxAppId!!/g, '')
            .replace(/&amp;/g, '&')
        }
      }).catch(() => {
        this.loading = false
      })
    },
  },
}
</script>

<style lang="less" scoped>
.marketing-mail-promotion__wrapper {
  display: flex;
  justify-content: center;
  width: 100%;
  height: calc(100vh - 80px);
  padding: 20px;
  box-sizing: border-box;
  color: var(--color-neutrals19, '#181c25');
  line-height: 18px;
  font-size: 13px;
  overflow-y: auto;

  .marketing-mail-promotion {
    display: flex;
    flex-direction: column;
    width: 100%;
    max-width: 1200px;
  }

  .notice-header {
    background: var(--color-neutrals02, '#fafafa');
    border-radius: 8px;
    padding: 12px;

    .notice-title {
      margin-bottom: 4px;
    }
    .notice-detail-item {
      display: flex;
      align-items: center;
      margin-top: 12px;
    }

    .notice-detail-item-label {
      color: #91959e;
      width: 100px;
    }
  }

  .notice-content {
    margin-top: 20px;

    .notice-content-title {
      font-size: 15px;
      line-height: 24px;
      font-weight: 600;
    }

    .notice-content-tips {
      display: flex;
      padding: 8px 12px;
      align-items: center;
      margin-top: 10px;
      background: #FFFBF0;
      border-radius: 8px;
      color: var(--color-neutrals15, #545861);

      .tips-icon {
        margin-right: 4px;
        color: #FF9A42;
        font-size: 16px;
      }
    }

    .notice-content-body {
      margin-top: 10px;

      .notice-content-body-item {
        display: flex;
        align-items: flex-start;
        margin-top: 10px;

        .notice-content-body-label {
          width: 100px;
          color: var(--color-neutrals15, #545861);
        }
      }
    }
  }
}
</style>
