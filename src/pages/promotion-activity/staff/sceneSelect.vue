<template>
    <div class="marketing-promotion__scene-select">
      <fx-checkbox-group class="scene-checkbox-group" v-model="selectedTypes" @change="handleSelectedChange">
        <template v-for="(item, index) in sceneList">
          <div v-if="item.visible" :key="index" class="scene-item">
            <div class="scene-header">
              <fx-checkbox
                :label="item.type"
                style="line-height: 18px;"
              >
                {{ item.title }}
              </fx-checkbox>
              <!-- <span class="scene-title">{{ item.title }}</span> -->
            </div>
            <div class="scene-desc">{{ item.description }}</div>
            <template v-if="item.enabled">
              <div class="scene-selctor-item" v-if="item.type === 'staff'">
                <Selectbar
                  :btn-text="$t('marketing.commons.xzfsfw_779f6a')"
                  :selects="staffForm.spreadAllVisibilityArg"
                  class="select-range-bar"
                  style="width: 100%;"
                  @update:select="toggleSpDialogVisible"
                />
              </div>
              <div class="scene-selctor-item" v-else-if="item.type === 'partner'">
                <div class="partner-select-bar"></div>
              </div>
              <div class="scene-selctor-item member-display" v-else>
                <FilterDisplay
                  :isShowClearAll="false"
                  :showMore="true"
                  class="member-display-filter"
                  v-model="memberRangeData"
                  @filterMore="handleMemberSendRange"
                />
                <fx-button
                  type="text"
                  size="mini"
                  v-show="!memberRangeData.filters.length"
                  style="font-size: 12px;padding-left: 12px;padding-top: 6px;"
                  @click="handleMemberSendRange"
                >
                  +{{ $t('marketing.commons.xzfsfw_779f6a') }}
                </fx-button>
              </div>
            </template>
            <div class="scene-sendnum" v-if="item.enabled">
              {{ $t('marketing.pages.promotion_activity.yjfsrs_408ae8') }}<span class="yellow"> {{ item.sendNum }} </span>
            </div>
          </div>
        </template>
      </fx-checkbox-group>
      <AdvancedFilter
        v-if="showMemberFilter"
        :title="$t('marketing.commons.gjsx_df2f0a')"
        width="800px"
        :object-names="objectNames"
        :visible="showMemberFilter"
        :default-filter-item="memberRangeData.filters"
        @confirm="handleMemberRangeConfirm"
        @onClose="showMemberFilter = false"
      />
      <StaffSelectorDialog
          :options="{
            includeCompany: true,
            zIndex: 99999,
            showRole: marketingDataIsolation ? false:true,
            usergroup: (!marketingDataIsolation) ? true : false
          }"
          source="marketingPromotion"
          :select-result.sync="staffForm.spreadAllVisibilityArg"
          :sp-dialog-visible.sync="spDialogVisible"
        />
    </div>
</template>

<script>
import FilterDisplay from "@/components/table-filter/filterdisplay.vue";
import Selectbar from '@/components/selectbar/index.vue'
import StaffSelectorDialog from '@/modules/staff-selector-dialog/index.vue'
import AdvancedFilter from '@/components/advanced-filter/index.vue'
import kisvData from '@/modules/kisv-data.js'
import { requireAsync } from "@/utils/index";
import http from '@/services/http/index.js'
import { addressBookResult2Str } from '@/utils/tranformUtil.js'


export default {
  name: "SceneSelect",
  components: {
    FilterDisplay,
    Selectbar,
    StaffSelectorDialog,
    AdvancedFilter
  },
  data() {
    return {
      showMemberFilter: false,
      memberRangeData: {
        filters: [],
        tags: [],
        tagOperator: 'LIKE',
      },
      objectNames: [
        {
          name: $t('marketing.commons.hy_4d9dd5'),
          value: 'MemberObj',
        },
      ],
      vDatas: kisvData.datas,
      spDialogVisible: false,
      sceneList: [
        {
          type: "staff",
          title: $t('marketing.commons.qytg_966c63'),
          description: $t('marketing.pages.promotion_activity.gnbygxftgr_c7e6fe'),
          icon: require("@/assets/images/promotion/employee.png"),
          enabled: true,
          sendNum: 0,
          visible: true,
          spreadType: 1
        },
        {
          type: "partner",
          title: $t('marketing.commons.hbtg_e84b17'),
          description: $t('marketing.pages.promotion_activity.ghlhzhbdls_c0a6c0'),
          icon: require("@/assets/images/promotion/partner.png"),
          enabled: false,
          sendNum: 0,
          visible: false,
          spreadType: 7
        },
        {
          type: "member",
          title: $t('marketing.commons.hytg_05ca56'),
          description: $t('marketing.pages.promotion_activity.gwhlzhdygh_8774f0'),
          icon: require("@/assets/images/promotion/member.png"),
          enabled: false,
          sendNum: 0,
          visible: false,
          spreadType: 13
        }
      ],
      staffForm: {
        spreadAllVisibilityArg: {},
        memberVisibilityArg: {},
        partnerVisibilityArg: {}
      },
      selectedTypes: ['staff'],
    };
  },
  computed: {
    marketingDataIsolation() {
      return this.vDatas.uinfo.marketingDataIsolation
    },
    addressBookTypeNum() {
      return this.$store.state.Global.addressBookTypeNum;
    },
    addressBookType() {
      return this.$store.state.Global.addressBookType
    },
  },
  watch: {
    vDatas: {
      handler(val){
        if(val.pluginInfo.memberMarketingPluginEnable){
          this.sceneList.forEach(item => {
            if(item.type === 'member'){
              item.visible = true
            }
          })
        }
        if(val.uinfo.partnerMarketingEnabled){
          this.sceneList.forEach(item => {
            if(item.type === 'partner'){
              item.visible = true
            }
          })
        }
      },
      deep: true,
      immediate: true
    },
    'memberRangeData.filters': {
      deep: true,
      handler(val) {
        if (val && val.length) {
          const { query } = val[0]
          const _filters = query && query.filters ? query.filters : []
          this.staffForm.memberVisibilityArg.filters = _filters
          this.getMarketingPreviewData(_filters)
        } else {
          this.staffForm.memberVisibilityArg.filters = []
          this.sceneList.forEach(item => {
            if(item.type === 'member'){
              item.sendNum = 0
            }
          })
        }
        // 查询预计发送范围
      },
    },
    'staffForm.spreadAllVisibilityArg': {
      deep: true,
      handler(val) {
        this.getEmployeeMarketingPreviewData(val,'staff')
      }
    },
    selectedTypes: {
      handler(newVal) {
        this.sceneList.forEach(item => {
          item.enabled = newVal.includes(item.type)
        })
      },
      immediate: true
    },
  },
  methods: {
    handleSelectedChange(item) {
      if(item.includes('partner')){
        this.$nextTick(()=>{
          this.initIcselector()
        })
      }
    },
    handleMemberSendRange() {
      this.showMemberFilter = true
    },
    handleMemberRangeConfirm(data) {
      this.showMemberFilter = false
      this.memberRangeData.filters = data
      this.$emit('confirm', data)
    },
    toggleSpDialogVisible() {
      this.spDialogVisible = false
      this.$nextTick(()=>{
        this.spDialogVisible = true
      })
    },
    initIcselector(partnerNoticeVisibilityArg) {
      requireAsync("icmanage-modules/icselector/icselector", mod => {
        let Icselector = Vue.extend(mod.icSelector);
        const SelectWarp = new Icselector({
          propsData: {
            propsLine: { label: $t('marketing.commons.xzfsfw_779f6a') },
            propsBox: { tabs: ["outerTenantIds", "outerTgroupIds"] },
            mode: "box",
            icsOptions: {
              sourceType: 0 // 0 获取当前企业的下游数据， 1 获取当前企业的上游数据，缺少键名则不区分上下游
            },
            selected: { 						// 已勾选项
              outerTenantIds: (partnerNoticeVisibilityArg && partnerNoticeVisibilityArg.eaList) || [],
              outerTgroupIds: (partnerNoticeVisibilityArg && partnerNoticeVisibilityArg.tenantGroupIdList) || [],
            },
            //控制全选按钮
            isAllFlags: [4],
          }
        }).$mount(".partner-select-bar");
        SelectWarp.$on("change", value => {
          let {outerTenantIds=[],outerTgroupIds=[]} = value
          this.staffForm.partnerVisibilityArg = {
            eaList:outerTenantIds,
            tenantGroupIdList:outerTgroupIds,
            executor: addressBookResult2Str(SelectWarp.getSelectedItems())
          }
          this.getPartnerMarketingPreviewData({
            eaList:outerTenantIds,
            tenantGroupIdList:outerTgroupIds
          },2)
        });
      });
    },
    getMarketingPreviewData(filters) {
      http.getMarketingPreviewData({
        spreadType: 1,
        sendNoticePreviewArg: {
          type: 10,
          filters,
        },
      }).then(res => {
        if (res && res.errCode === 0 && res.data) {
          this.sceneList.forEach(item => {
            if(item.type === 'member'){
              item.sendNum = res.data.sendNoticePreviewResult && res.data.sendNoticePreviewResult.totalCount || 0
            }
          })
        }
      })
    },
    // 计算预计发送人数
    async getEmployeeMarketingPreviewData(item,type){
      const params = {
        addressBookType: this.addressBookTypeNum || 0,
        noticeVisibilityVO: this.formatParams(item)
      }
      const res = await http.getEmployeeMarketingPreviewData(params)
      if(res && res.errCode === 0){
        this.sceneList.forEach(item => {
          if(item.type === type){
            item.sendNum = res.data || 0
          }
        })
      }
    },
    formatParams(val){
      const {
        colleague = [],depart = [],filters = [],role = [],usergroup = []
      } = val || {}
      const params = {}
      if(this.addressBookType === 'ding'){
        params.outUserIds = colleague.map(item =>item.id);
      } else {
        params.userIds = colleague.map(item =>item.id);
      }
      params.roles = role.map(item=>{
        return {
          roleCode: item.id,
          roleName: item.name
        }
      })
      params.departmentIds = depart.map(item=>item.id)
      params.usergroup = usergroup.map(item=>{
        return {
          userGroupId: item.id,
          userGroupName: item.name
        }
      })
      return params
    },
    // 计算伙伴营销预计发送人数
    async getPartnerMarketingPreviewData(item){
      const params = {
        noticeVisibilityVO: item
      }
      const res = await http.getPartnerMarketingPreviewData(params)
      if(res && res.errCode === 0){
        this.sceneList.forEach(item => {
          if(item.type === 'partner'){
            item.sendNum = res.data || 0
          }
        })
      }
    },
    getAddEmployeeMarketingActivityParams(){
      const spreadTypes = this.sceneList.filter(item=>{
        return item.enabled && item.spreadType
      }).map(item=>{
        return item.spreadType
      })
      const { spreadAllVisibilityArg,memberVisibilityArg,partnerVisibilityArg } = this.staffForm
      return {
        spreadTypes,
        spreadAllVisibilityArg: {
          ...this.formatParams(spreadAllVisibilityArg),
          executor: addressBookResult2Str(spreadAllVisibilityArg)
        },
        memberVisibilityArg,
        partnerVisibilityArg
      }
    },
    // 检查员工发送范围
    validateStaffVisibility() {
      if (!this.selectedTypes.includes('staff')) return true;

      const visibility = this.staffForm.spreadAllVisibilityArg;
      if (!visibility) return false;

      const hasValidSelection =
        (visibility.colleague && visibility.colleague.length > 0) ||
        (visibility.depart && visibility.depart.length > 0) ||
        (visibility.role && visibility.role.length > 0) ||
        (visibility.usergroup && visibility.usergroup.length > 0);

      return hasValidSelection;
    },

    // 检查会员发送范围
    validateMemberVisibility() {
      if (!this.selectedTypes.includes('member')) return true;
      const visibility = this.staffForm.memberVisibilityArg;
      return visibility && visibility.filters && visibility.filters.length > 0;
    },

    // 检查合作伙伴发送范围
    validatePartnerVisibility() {
      if (!this.selectedTypes.includes('partner')) return true;

      const visibility = this.staffForm.partnerVisibilityArg;
      if (!visibility) return false;

      return (visibility.eaList && visibility.eaList.length > 0) ||
             (visibility.tenantGroupIdList && visibility.tenantGroupIdList.length > 0);
    },

    validate(callback) {
      if(this.selectedTypes.length === 0){
        FxUI.Message.warning(this.$t('marketing.commons.qxzfsfw_6d3514'));
        callback(false);
        return;
      }
      const validations = [
        { type: 'staff', name:  $t('marketing.commons.qytg_966c63'), validate: this.validateStaffVisibility },
        { type: 'member', name: $t('marketing.commons.hytg_05ca56'), validate: this.validateMemberVisibility },
        { type: 'partner', name: $t('marketing.commons.hbtg_e84b17'), validate: this.validatePartnerVisibility }
      ];

      // 检查所有选中的类型
      for (const { type, validate } of validations) {
        console.log('type', type, validate.call(this))
        if (this.selectedTypes.includes(type) && !validate.call(this)) {
          FxUI.Message.warning($t('marketing.commons.qxzfsfw_6d3514'));
          callback(false);
          return;
        }
      }

      callback(true);
    }
  },
};
</script>

<style lang="scss" scoped>
.marketing-promotion__scene-select {
  width: 100%;
  .scene-checkbox-group{
    /deep/ .fx-checkbox-group{
      display: flex;
      gap: 10px;
    }
  }
  .scene-item {
    padding: 10px;
    border: 1px solid var(--color-neutrals05);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    gap: 6px;
    flex: 1;
    min-width: 0;
  }
  .scene-header {
    display: flex;
    align-items: flex-start;
    gap: 6px;
    .scene-title {
      font-size: 13px;
      font-weight: 500;
      color: var(--Text-H1, #181C25);
      line-height: 18px;
    }
  }

  .scene-desc {
    font-size: 12px;
    line-height: 18px;
    color: #91959E;
    flex: 1;
  }
  .scene-sendnum{
    color: var(--color-neutrals19);
    font-feature-settings: 'liga' off, 'clig' off;
    font-family: "Source Han Sans CN";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 18px; /* 150% */
    .yellow{
      color: var(--color-primary06);
    }
  }
  .scene-selctor-item{
    display: flex;
    align-items: center;
    min-height: 28px;
    height: auto;
    &.member-display{
      line-height: 20px;
      font-size: 12px;
      border: 1px solid #e9edf5;
      flex-direction: column;
      justify-content: flex-start;
      align-items: flex-start;
    }
  }
  /deep/ .person-btn-wrap {
    flex-wrap: wrap;
  }
  /deep/ .icsel-vcnts{
    width: 100%;
    .selector-input-list-wrap {
      max-height: 33px !important;
      overflow: hidden;
      .btn-trigger{
        color: var(--color-primary06) !important;
        .trigger-text{
          color: var(--color-primary06) !important;
          }
        }
    }
  }
}

</style>
