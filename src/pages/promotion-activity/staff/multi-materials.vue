<template>
  <div :class="$style.multiMaterials">
    <vue-draggable
      :class="$style.materialWrapper"
      v-model="data"
      v-bind="dragOptions"
      :forceFallback="true"
      handle=".drag-item"
      @start="drag = true"
      @end="drag = false"
      @change="handleDataChange"
    >
      <div
        :class="[$style.materialItem, 'drag-item']"
        v-for="(item, index) in data"
        :key="item.id"
      >
        <img :src="item.image || item.coverUrl" alt="" />
        <div :class="$style.closeIcon" @click="delMaterial(index)">
          <i class="el-icon-error"></i>
        </div>
      </div>
      <div
        v-if="!(data && data.length >= 9)"
        :class="$style.addBtn"
        @click="addMaterial"
      >
        <i class="el-icon-plus"></i>
      </div>
    </vue-draggable>

  </div>
</template>

<script>
import VueDraggable from "vuedraggable";
import http from "@/services/http/index";

export default {
  components: {
    VueDraggable
  },
  props: {
    data: {
      type: Array,
      default: () => []
    },
    
  },
  watch: {},
  computed: {
    //1:文章 3:会议 4:产品 10:落地页 24:海报 16:表单 25:邀请函

    dragOptions() {
      return {
        animation: 200,
        group: "menu",
        disabled: false
      };
    }
  },
  data() {
    return {};
  },
  methods: {
    handleDataChange(e) {
      this.$emit('onChange', this.data);
    },
    addMaterial() {
      this.$emit("select");
    },
    delMaterial(index) {
      this.$emit("del", index);
    }
  },
  mounted() {}
};
</script>

<style lang="less" module>
.multiMaterials {
  .materialWrapper {
    padding: 20px;
    max-width: 270px;
    box-sizing: border-box;
    display: flex;
    flex-wrap: wrap;
    background: #666;
    background: #f9f9f9;

    .materialItem {
      width: 70px;
      height: 0;
      padding-bottom: 70px;
      position: relative;
      margin: 0 10px 10px 0;
    }

    .materialItem:nth-of-type(3n) {
      margin-right: 0;
    }

    .materialItem img {
      width: 100%;
      height: 100%;
      border-radius: 3px;
      position: absolute;
      top: 0;
      left: 0;
      object-fit: fill;
    }

    .closeIcon {
      position: absolute;
      border-radius: 50%;
      width: 8px;
      height: 8px;
      font-size: 16px;
      color: #c1c5ce;
      background-color: #fff;
      top: 0;
      right: 0;
      transform: translate(4px, -4px);
      cursor: pointer;
      i {
        position: absolute;
        top: 0;
        left: 0;
        transform: translate(-4px, -4px);
      }
    }

    .addBtn {
      width: 70px;
      height: 70px;
      font-size: 20px;
      background-color: #fff;
      display: flex;
      color: var(--color-primary06,#407FFF);
      align-items: center;
      justify-content: center;
      border-radius: 3px;
      cursor: pointer;
      border: 1px solid #e9edf5;
      box-sizing: border-box;
    }
  }
}
</style>
