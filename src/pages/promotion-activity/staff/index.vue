<template>
    <v-scaffold
      :title="isMemberPromotion ? $t('marketing.commons.xjhytg_36686c') : $t('marketing.commons.xjqytg_4d6a65')"
      :btn-state="btnState"
      @send="handleSend"
      @save="handleSave"
      @cancel="handleCancel"
    >
      <div :class="[$style.staff_activity_wrapper, $style[displayMode]]">
        <el-form
          ref="staffForm"
          :model="staffForm"
          :rules="rules"
          label-width="130px"
          label-position="left"
          :class="$style.staffForm"
        >
          <el-form-item
            v-if="$route.query.promotionType !== 'memberActivity' && $route.query.promotionType !=='staffActivity'"
            :label="$t('marketing.pages.promotion_activity.tgcj_1370d9')"
            required
          >
            <sceneSelect style="margin-bottom: 12px;" ref="promotionSceneSelect" />
          </el-form-item>
        
          <el-form-item
            v-if="vDatas.status.crmark"
            :label="$t('marketing.commons.schd_a8559e')"
            prop="marketingEventId"
          >
            <v-pick-selfobject
              v-model="staffForm.marketingEventId"
              :disabled="
                !!staffForm.marketingEventId.id && disabled_marketingEventId
              "
            />
          </el-form-item>
          <el-form-item
            ref="article"
            :label="$t('marketing.commons.tgnr_a6ec90')"
            prop="content"
          >
            <div :class="$style.article" style="margin-bottom: 12px;">
              <v-materials
                ref="materials"
                v-model="staffForm.content"
                :display-mode="displayMode"
                :preset-material="material"
                :marketing-event-id="staffForm.marketingEventId.id"
                :multi-materials-data="multiMaterialsData"
                @input="materialsInputChange"
              />
            </div>
            <div
              v-if="showContentTips"
              :class="$style.contentTips"
            >
              {{ $t('marketing.pages.promotion_activity.qytgsxthzd_547a34') }}
            </div>
          </el-form-item>
          <el-form-item
            :label="$t('marketing.commons.tgrwbt_971653')"
            prop="title"
          >
            <fx-input
              v-model="staffForm.title"
              :placeholder="$t('marketing.commons.qsr_02cc4f')"
              :maxlength="100"
            />
            <span
              :class="$style.count"
              style="top:0;color:#c1c5ce;"
            >{{ staffForm.title.length }}/100</span>
          </el-form-item>
          <el-form-item
            :label="spreadMode === 3 ? $t('marketing.pages.promotion_activity.rwsm_1d35dc') : $t('marketing.commons.xcy_ff27ff')"
            prop="description"
          >
            <fx-input
              v-if="spreadMode === 3"
              v-model="staffForm.description"
              type="textarea"
              rows="3"
              :placeholder="$t('marketing.commons.qsr_02cc4f')"
              :maxlength="255"
            />
            <AiSlogan
              v-else
              v-model="staffForm.description"
              :placeholder="$t('marketing.pages.promotion_activity.ygzfskyzjz_a9f2c1')"
              :maxlength="255"
              :slogan-material="sloganMaterial"
            />
          </el-form-item>
          <el-form-item
            :label="$t('marketing.commons.fmt_641cfa')"
            prop="coverPath"
          >
            <v-pick-cover
              v-model="staffForm.coverPath"
              :default-image-url="defaultImageUrl"
              output-path-type="a"
              @update:cut="showImage"
            />
            <div :class="$style.cover__tips" style="margin-bottom: 12px;">
              {{ $t('marketing.commons.jyccxszcgs_326c37') }}
            </div>
          </el-form-item>
          <el-form-item
            :label="dateLable"
            prop="date"
          >
            <picker-time
              v-model="staffForm.date"
              value-format="timestamp"
            />
          </el-form-item>
          <el-form-item
            v-if="isMemberPromotion && $route.query.promotionType === 'memberActivity'"
            :label="$t('marketing.commons.fsfw_c8ff46')"
            prop="noticeVisibilityArg"
          >
            <div :class="$style.memberRange">
              <FilterDisplay
                v-model="memberRangeData"
              />
              <fx-button
                type="text"
                icon="fx-icon-add-2"
                style="margin:12px;padding:0;"
                @click="handleMemberSendRange"
              >
                {{ $t('marketing.commons.qxz_708c9d') }}
              </fx-button>
            </div>
            <div
              v-if="memberRangeData.filters && memberRangeData.filters.length"
              :class="$style.memberRangeTips"
            >
              {{ $t('marketing.pages.promotion_activity.yjfshys_e22769') }}
              <span>{{ rangeCount }}</span>
            </div>
          </el-form-item>
          <el-form-item
            v-else-if="$route.query.promotionType === 'staffActivity'"
            :label="$t('marketing.commons.fsfw_c8ff46')"
            prop="noticeVisibilityArg"
          >
            <div :class="$style.selectRange">
              <Selectbar
                :btn-text="$t('marketing.commons.xzfsfw_779f6a')"
                :remove-range-item="removeItem"
                :selects="staffForm.noticeVisibilityArg"
                class="select-range-bar"
                @change="changeItem"
                @update:select="toggleSpDialogVisible"
              />
              <!-- <div :class="$style.selectQywxstaff" @click="hanldeSelectQywxStaff">
                请选择企业微信员工
              </div> -->
            </div>
          </el-form-item>
          <el-form-item
            :label="$t('marketing.commons.fssj_f6cbc9')"
            prop="sendConfig"
          >
            <sendtime-pker v-model="staffForm.sendConfig" />
          </el-form-item>
        </el-form>
        <StaffSelectorDialog
          :dialogTitle="$t('marketing.commons.xzfsfw_779f6a')"
          :options="{
            includeCompany: true,
            zIndex: 99999,
            showRole: marketingDataIsolation ? false:true,
            usergroup: (usergroup && !marketingDataIsolation) ? true : false
          }"
          source="marketingPromotion"
          :select-result.sync="staffForm.noticeVisibilityArg"
          :sp-dialog-visible.sync="spDialogVisible"
        />
        <!-- <SelectPersonDialog
          :options="{ includeCompany: true }"
          :selectResult.sync="staffForm.noticeVisibilityArg"
          :spDialogVisible.sync="spDialogVisible"
        ></SelectPersonDialog> -->
      </div>
      <AdvancedFilter
        v-if="showMemberFilter"
        :title="$t('marketing.commons.gjsx_df2f0a')"
        width="800px"
        :object-names="objectNames"
        :visible="showMemberFilter"
        :default-filter-item="memberRangeData.filters"
        @confirm="handleMemberRangeConfirm"
        @onClose="showMemberFilter = false"
      />
    </v-scaffold>
</template>

<script>
import { mapActions } from 'vuex'
import http from '@/services/http/index.js'
// import VHeader from '../common/header.vue'
import VScaffold from '../common/scaffold.vue'
import VPickSelfobject from '../common/pick-selfobject.vue'
import sendtimePker from '../common/sendtime-pker.vue'
import VMaterials from './materials.vue'
import StaffSelectorDialog from '@/modules/staff-selector-dialog/index.vue'
// import SelectPersonDialog from '@/modules/select-person-dialog/select.vue'
import Selectbar from '@/components/selectbar/index.vue'
import PickerTime from '../common/picker-time.vue'
// import VPickCover from '../common/pick-cover';
import VPickCover from '@/components/picture-cutter/index.vue'
import kisvData from '@/modules/kisv-data.js'
import { getEmployeeByIdAndVuex, getDepartmentByIdAndVuex, watchFormValidateFieldHandler } from '@/utils/index.js'
import { addressBookResult2Str, sendLinkVariableReplace } from '@/utils/tranformUtil.js'
import AdvancedFilter from '@/components/advanced-filter/index.vue'
import FilterDisplay from '@/components/table-filter/filterdisplay.vue'
import sceneSelect from './sceneSelect.vue'
import AiSlogan from '../common/ai-slogan.vue'
import { OToCTypeMaps } from '@/components/select-material-dialog/material.js'
import mailCoverImg from '@/assets/images/mail/mail-cover.png'

let _enterFrom = ''

export default {
  components: {
    // VHeader,
    VScaffold,
    VPickSelfobject,
    // ElSelect: FxUI.Select.components.ElSelect,
    // ElOption: FxUI.Select.components.ElSelect.components.ElOption,
    sendtimePker,
    ElForm: FxUI.Form,
    ElFormItem: FxUI.FormItem,
    VMaterials,
    // SelectPersonDialog,
    StaffSelectorDialog,
    Selectbar,
    PickerTime,
    VPickCover,
    AdvancedFilter,
    FilterDisplay,
    sceneSelect,
    AiSlogan,
  },
  beforeRouteEnter(to, from, next) {
    _enterFrom = from.name
    next(vm => {
      vm.mailMarketingId = from.query.mailMarketingId || from.query.id
      if (from.name === 'mail-template-create') {
        vm.spreadMode = 3
        vm.staffForm.content = vm.mailMarketingId
        vm.$nextTick(() => {
          vm.$refs.materials.getEmailMaterial(vm.mailMarketingId)
        })
      }
    })
  },
  props: {
    material: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  data() {
    return {
      isAgainSend: false,
      mailMarketingId: '',
      displayMode: 'scaffold', // 或者是dialog
      disabled_marketingEventId: false,
      notSendAgain: true,
      defaultImageUrl: '',
      spDialogVisible: false,
      vDatas: kisvData.datas,
      dateLable: $t('marketing.commons.tgsj_0845c3'),
      btnState: [],
      showContentTips: false,
      staffForm: {
        title: '',
        content: {},
        coverPath: '',
        description: '',
        date: [],
        marketingEventId: { id: this.$route.query.id },
        noticeVisibilityArg: {},
        sendConfig: {
          type: 1,
        },
      },
      spreadMode: 1, // 1: 单物料 2: 多物料 3: 邮件
      multiMaterialsData: [],
      multiObjectId: '',
      multiObjectType: '',
      rules: {
        marketingEventId: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (kisvData.datas.status.crmark && (!value || !value.id)) {
                callback(new Error($t('marketing.commons.qxzschd_9e5f1b')))
              }
              callback()
            },
          },
        ],
        title: [
          {
            required: true,
            message: $t('marketing.commons.qsrtgrwbt_c1caba'),
            trigger: 'change',
          },
        ],
        coverPath: [
          {
            required: true,
            message: $t('marketing.commons.qxzfmtp_5b90d1'),
            trigger: 'change',
          },
        ],
        content: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (_.isEmpty(value)) {
                callback(new Error($t('marketing.commons.qxztgnr_4da728')))
              }
              callback()
            },
            trigger: 'blur',
          },
        ],
        noticeVisibilityArg: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (
                !(
                  (value.colleague && value.colleague.length)
                  || (value.depart && value.depart.length)
                  || (value.role && value.role.length)
                  || (value.usergroup && value.usergroup.length)
                  || (value.filters && value.filters.length)
                )
              ) {
                callback(new Error($t('marketing.commons.qxzfsfw_6d3514')))
              }
              callback()
            },
            trigger: 'blur',
          },
        ],
        description: [
          {
            required: true,
            message: $t('marketing.commons.qsrxcy_c3d431'),
            trigger: 'change',
          },
        ],
        date: [
          {
            required: true,
            validator: (rule, value, callback) => {
              const [startTime, endTime] = value
              if (!startTime) {
                callback(new Error($t('marketing.commons.qxztgkssj_547e46')))
              } else if (!endTime) {
                callback(new Error($t('marketing.commons.qxztgjssj_23ba76')))
              }
              callback()
            },
            trigger: 'change',
          },
        ],
        sendConfig: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (value && value.type == 2 && !value.time) {
                callback(new Error($t('marketing.commons.qxzdsfssj_88824a')))
              }
              callback()
            },
            trigger: 'change',
          },
        ],
      },
      showMemberFilter: false,
      memberRangeData: {
        filters: [],
        tags: [],
        tagOperator: 'LIKE',
      },
      objectNames: [
        {
          name: $t('marketing.commons.hy_4d9dd5'),
          value: 'MemberObj',
        },
      ],
      rangeCount: 0,
    }
  },
  computed: {
    addressBookType() {
      return this.$store.state.Global.addressBookType
    },
    addressBookTypeNum() {
      return this.$store.state.Global.addressBookTypeNum
    },
    usergroup() {
      /**
       * 在纷享通讯录下需要用户组
       */
      return this.addressBookType === 'fs'
    },
    marketingDataIsolation() {
      return this.vDatas.uinfo.marketingDataIsolation
    },
    isMemberPromotion() {
      return this.$route.name === 'promotion-activity-member'
    },
    sloganMaterial() {
      const { name = '' } = this.staffForm.marketingEventId || {}
      const { id, type } = this.staffForm.content || {}
      if (this.spreadMode === 1) {
        const entry = Object.entries(OToCTypeMaps).find(([key, value]) => value === type)
        if (entry) {
          return {
            objectId: id,
            objectType: parseInt(entry[0], 10),
            campaignName: name,
          }
        }

        return {
          objectId: id,
          objectType: type,
          campaignName: name,
        }
      }

      if (this.spreadMode === 2 && this.multiMaterialsData.length) {
        return {
          materialInfos: this.multiMaterialsData.map(item => item.coverPath),
          campaignName: name,
        }
      }

      return null
    },
  },
  watch: {
    ...watchFormValidateFieldHandler(
      [
        {
          watchField: 'staffForm.content',
          validateField: 'content',
        },
        {
          watchField: 'staffForm.noticeVisibilityArg',
          validateField: 'noticeVisibilityArg',
        },
      ],
      'staffForm',
    ),
    staffForm: {
      deep: true,
      handler(newVal, oldVal) {
        console.log(newVal)
      },
    },
    vDatas: {
      deep: true,
      handler(newVal) {},
    },
    'memberRangeData.filters': {
      deep: true,
      handler(val) {
        if (val && val.length) {
          const { query } = val[0]
          const _filters = query && query.filters ? query.filters : []
          this.staffForm.noticeVisibilityArg.filters = _filters
          this.getMarketingPreviewData(_filters)
        } else {
          this.staffForm.noticeVisibilityArg.filters = []
        }
        // 查询预计发送范围
      },
    },
  },
  mounted() {
    this.presetTime()
    if (this.displayMode === 'scaffold') {
      this.presetContent({
        conferenceId: this.$route.params.conferenceID,
        materiel: this.$route.params.materiel,
      })
    } else if (this.displayMode === 'dialog') {
      this.staffForm.marketingEventId = { id: this.defaultMarketingEventId }
    }
    // 再次发送
    if (this.$route.query.marketingActivityId) {
      this.queryMarketingActivityDetail()
      this.isAgainSend = true
    }

    console.log('MarktingAIHelper >>>', this)
  },
  methods: {
    ...mapActions('PromotionActivity', [
      'fakeAddMarketingActivity',
      'addActivityDraft',
    ]),
    getMarketingPreviewData(filters) {
      http.getMarketingPreviewData({
        spreadType: 1,
        sendNoticePreviewArg: {
          type: 10,
          filters,
        },
      }).then(res => {
        if (res && res.errCode === 0 && res.data) {
          // eslint-disable-next-line no-mixed-operators
          this.rangeCount = res.data.sendNoticePreviewResult && res.data.sendNoticePreviewResult.totalCount || 0
        }
      })
    },
    handleMemberSendRange() {
      this.showMemberFilter = true
    },
    handleMemberRangeConfirm(data) {
      this.showMemberFilter = false
      this.memberRangeData.filters = data
      this.$emit('confirm', data)
    },
    materialsInputChange(value, mode) {
      // 重置选项
      if (!value) {
        this.showContentTips = false
        this.defaultImageUrl = ''
        this.staffForm.coverPath = ''
        this.multiObjectId = ''
        this.multiObjectType = ''
        this.multiMaterialsData = []

        return
      }

      this.spreadMode = mode || 1
      if (mode === 1) {
        if (value.qrPostForwardType == 10) {
          this.showContentTips = true
        } else {
          this.showContentTips = false
        }
        if (value.data.image && value.data.aPath) {
          this.defaultImageUrl = value.data.image
          this.staffForm.coverPath = value.data.aPath
        }
      } else if (mode === 2) {
        if (value && value.length > 0) {
          this.multiObjectId = value[0].id || value[0].objectId
          this.multiObjectType = value[0].contentType === 24 ? 5 : value[0].contentType
          this.defaultImageUrl = value[0].image || value[0].coverUrl
          this.staffForm.coverPath = value[0].aPath || value[0].coverPath

          this.multiMaterialsData = value.map(item => {
            if (item.objectId) {
              return item
            }
            return {
              objectId: item.id,
              contentType: item.contentType === 24 ? 5 : item.contentType,
              coverPath: item.aPath || item.coverPath,
              coverUrl: item.image,
            }
          })
        }
        this.showContentTips = false
      }
    },
    formatParams() {
      const {
        title,
        content,
        description,
        date,
        marketingEventId,
        noticeVisibilityArg,
        sendConfig,
        coverPath,
      } = this.staffForm
      // 海报在选择素材组件的type是'24'，但全员推广是5
      if (content.type == 24) {
        content.type = 5
      }
      // 微页面在选择素材组件的type是'26'，但全员推广是10
      if (content.type == 26) {
        content.type = 10
      }

      // 如果推广的是会议主页，并且是微页面类型，这里把类型改成3
      if (this.material.siteType === 'EVEN_HOMEPAGE' && this.material.contentType === 10) {
        content.type = 3
        content.id = this.material.conferenceId
      }

      const _role = []
      const userGroups = []
      if (!this.isMemberPromotion) {
        if (noticeVisibilityArg.role) {
          noticeVisibilityArg.role.forEach(item => {
            _role.push({
              roleCode: item.id,
              roleName: item.name,
            })
          })
        }

        // 用户组
        if (noticeVisibilityArg.usergroup) {
          noticeVisibilityArg.usergroup.forEach(item => {
            userGroups.push({
              userGroupId: item.id,
              userGroupName: item.name,
            })
          })
        }
      }

      const promotionParams = this.formatParamsForAddEmployeeMarketingActivity({
        role: _role,
        userGroups
        
      })
      const params = {
        spreadType: 1,
        ...(marketingEventId && marketingEventId.id
          ? { marketingEventId: marketingEventId.id }
          : {}),
        marketingActivityAuditData: {
          ...(this.isMemberPromotion ? {} : { executor: addressBookResult2Str(noticeVisibilityArg) }),
          sendLink: sendLinkVariableReplace(content?.data?.url),
        },
        ...promotionParams,
        // 绑定素材到市场活动
        ...(this.spreadMode === 1 && {
          materialInfos: [
            {
              objectId: content.id,
              contentType: content.type,
            },
          ],
        }),
        ...(this.spreadMode === 2 && {
          materialInfos: this.multiMaterialsData ? this.multiMaterialsData.map(item => ({
            objectId: item.objectId,
            contentType: item.contentType,
          })) : [],
        }),
        ...(this.spreadMode === 3 && {
          materialInfos: [
            {
              objectId: this.mailMarketingId ? this.mailMarketingId : '',
              contentType: 503,
            },
          ],
        }),
      }
      // 递归删除空字符串
      function eachObject(obj) {
        Object.keys(obj).forEach(key => {
          if (obj[key] instanceof Object) {
            eachObject(obj[key])
          } else if (!obj[key]) {
            delete obj[key]
          }
        })
      }
      eachObject(params)
      return params
    },
    formatParamsForAddEmployeeMarketingActivity(params){
      const {
        title,
        content,
        description,
        date,
        noticeVisibilityArg,
        sendConfig,
        coverPath,
      } = this.staffForm

      const baseParams = {
        title,
          ...(this.spreadMode === 1 && {
            ...(content && content.id ? { content: content.id } : {}),
            ...(content && content.type ? { contentType: content.type } : {}),
          }),
          ...(this.spreadMode === 2 && {
            content: this.multiObjectId ? this.multiObjectId : '',
            contentType: this.multiObjectType ? this.multiObjectType : '',
            materialInfoList: this.multiMaterialsData ? this.multiMaterialsData : [],
          }),
          ...(this.spreadMode === 3 && {
            content: this.mailMarketingId ? this.mailMarketingId : '',
            contentType: 503,
          }),
          ...http.$.kk({ type: 'sendType', time: 'timingDate' }, sendConfig),
          ...(date ? { startTime: date[0] } : {}),
          ...(date ? { endTime: date[1] } : {}),
          ...(description ? { description } : {}),
          addressBookType: this.addressBookTypeNum,
          coverPath
      }
      if(this.$route.query.promotionType === 'memberActivity' || this.$route.query.promotionType === 'staffActivity'){
        return {
        marketingActivityNoticeSendVO: {
          ...baseParams,
          noticeVisibilityArg: this.isMemberPromotion ? noticeVisibilityArg : {
            departmentIds:
              noticeVisibilityArg.depart instanceof Array
                ? noticeVisibilityArg.depart.map(item => item.id)
                : [],
            [this.addressBookType === 'ding' ? 'outUserIds' : 'userIds']:
              noticeVisibilityArg.colleague instanceof Array
                ? noticeVisibilityArg.colleague.map(item => item.id)
                : [],
            ...params
          },
          ...(this.isMemberPromotion ? { type: 10 } : {}),
        },
        }
      }
      const multiPromotionParams = this.$refs.promotionSceneSelect.getAddEmployeeMarketingActivityParams()
      console.log(multiPromotionParams,'multiPromotionParams')
      return {
        ...baseParams,
        ...multiPromotionParams
      }
    },
    handleSend(callback) {
      this.$refs.staffForm.validate(valid => {
        // 添加sceneSelect组件的验证
        let sceneSelectValid = true
        if(this.$refs.promotionSceneSelect){
          this.$refs.promotionSceneSelect.validate(valid => {
            sceneSelectValid = valid
          })
        }
        if (typeof callback === 'function') {
          callback(valid && sceneSelectValid)
        }
        if (valid && sceneSelectValid) {
          console.log(this.staffForm)
          this.btnState = [1, 0, 0]
          const params = this.formatParams()
          const api = (this.$route.query.promotionType === 'memberActivity' || this.$route.query.promotionType === 'staffActivity') ? this.fakeAddMarketingActivity(params) : this.addEmployeeMarketingActivity(params)
          api
            .then(({ errCode }) => {
              if (errCode == 0) {
                this.handleComplete()
              }
            })
            .catch(() => {})
            .then(() => {
              this.btnState = []
            })
        }
      })
    },
    handleSave() {
      this.btnState = [1, 2, 0]
      this.addActivityDraft(this.formatParams()).then(() => {
        this.btnState = []
      })
    },
    handleComplete() {
      if (this.displayMode === 'dialog') {
        this.$emit('submited')
        return
      }

      // 全员推广有多个来源路由，点击取消时，都是 $router.back() 直接后退到来源页面
      // 点击确定时，视不同来源，跳往不同路由
      // 首页，跳推广列表
      // 活动、产品、文章新建成功后，跳全员营销
      // 全员营销、推广列表，直接退回源页面
      if (_enterFrom === 'home') {
        this.$router.replace({ name: 'promotion-activity' })
      } else if (_enterFrom === 'materiel-create-success' || _enterFrom === 'mail-template-create') {
        this.$router.replace({ name: 'promotion' })
      } else {
        this.$router.back()
      }
    },
    handleCancel() {
      if (_enterFrom === 'mail-template-create') {
        this.$router.replace({ name: 'promotion' })
      } else {
        this.$router.back()
      }
    },
    changeItem() {
      this.$refs.staffForm.validateField('noticeVisibilityArg')
    },
    removeItem(type, id) {
      const ds = this.staffForm.noticeVisibilityArg[type]
      for (const i in ds) {
        if (ds[i].id == id) {
          ds.splice(i, 1)
          break
        }
      }
      this.staffForm.noticeVisibilityArg[type] = ds
      this.staffForm.noticeVisibilityArg = _.extend(
        {},
        this.staffForm.noticeVisibilityArg,
      )
    },
    toggleSpDialogVisible() {
      this.spDialogVisible = !this.spDialogVisible
    },
    // hanldeSelectQywxStaff() {
    //   new QywxStaffSelector({
    //     defaultSelectedItems: {
    //       qywxUser: _.map(this.selectQywxUser, item => item.id),
    //     },
    //     onSubmit: data => {
    //       this.staffForm.noticeVisibilityArg = {
    //         colleague: data.qywxStaff,
    //         depart: data.qywxDepartment,
    //       }
    //       console.log(
    //         'hanldeSelectQywxStaff noticeVisibilityArg',
    //         this.staffForm.noticeVisibilityArg,
    //       )
    //       // this.handleSelectChange();
    //     },
    //   })
    // },
    queryMarketingActivityDetail() {
      http
        .queryMarketingActivityDetail({
          id: this.$route.query.marketingActivityId,
        })
        .then(async res => {
          if (res && res.errCode === 0) {
            // 再次发送除发送时间外数据回填
            this.staffForm.marketingEventId = { id: res.data.marketingEventId }
            const result = res.data.marketingActivityNoticeSendVO
            this.staffForm.title = result.title
            this.staffForm.description = result.description
            if (this.isMemberPromotion) {
              const _filters = result.noticeVisibilityArg.filters || []
              this.memberRangeData.filters = [{ objectAPIName: 'MemberObj', query: { filters: _filters } }]
            } else {
              const _departmentIds = result.noticeVisibilityArg.departmentIds

              const depart = []

              const usergroup = (result.noticeVisibilityArg.userGroups || []).map(item => ({
                id: item.userGroupId,
                name: item.userGroupName,
              }))

              if (_departmentIds && _departmentIds.length) {
                _departmentIds.forEach(item => {
                  depart.push({
                    id: item,
                    name: getDepartmentByIdAndVuex(item, this.$store).name,
                  })
                })
              }
              const _userId = result.noticeVisibilityArg.userIds
              const colleague = []
              if (_userId && _userId.length) {
                _userId.forEach(item => {
                  colleague.push({
                    id: item,
                    name: getEmployeeByIdAndVuex(item, this.$store).name,
                  })
                })
              }
              const _roleId = result.noticeVisibilityArg.roles
              const roles = []
              if (_roleId && _roleId.length) {
                _roleId.forEach(item => {
                  roles.push({
                    id: item.roleCode,
                    name: item.roleName,
                  })
                })
              }
              this.staffForm.noticeVisibilityArg = {
                colleague,
                depart,
                roles,
                usergroup,
              }
            }
            if (result.materialInfoList && result.materialInfoList.length > 0) {
              this.spreadMode = 2
              this.multiObjectId = result.content
              this.multiObjectType = result.contentType
              // this.defaultImageUrl = result.materialInfoList[0].coverUrl;
              // this.staffForm.coverPath = result.materialInfoList[0].coverPath;
              this.staffForm.content = result.content
              this.staffForm.contentType = result.contentType
              this.staffForm.materialInfoList = result.materialInfoList
              this.multiMaterialsData = result.materialInfoList
            } else if (result.contentType === 503) {
              this.spreadMode = 3
              this.$refs.materials.item = {
                photoUrl: mailCoverImg,
                summaryLine1: result.title,
                id: result.content,
                type: result.contentType,
              }
              this.mailMarketingId = result.content
              this.staffForm.content = result.content
              this.staffForm.contentType = result.contentType
              this.contentDetail = result.contentDetail
            } else {
              this.spreadMode = 1
              this.$refs.materials.item = {
                photoUrl: result.contentDetail.image,
                summaryLine1: result.title,
                id: result.content,
                type: result.contentType,
              }
              this.staffForm.content = this.$refs.materials.item
            }
            this.defaultImageUrl = res.data.coverUrl
            this.staffForm.coverPath = res.data.coverApath
          }
        })
    },
    presetTime() {
      this.staffForm.date = [
        +new Date(),
        +new Date() + 3 * 24 * 60 * 60 * 1000,
      ]
    },
    presetContent({ conferenceId, materiel }) {
      // 会议营销特殊处理
      if (conferenceId) {
        http
          .queryConferenceDetail({ id: conferenceId }) // this.$route.params.conferenceID
          .then(res => {
            if (res && res.errCode === 0) {
              const conferenceDetail = res.data
              this.staffForm.title = $t('marketing.commons.yqkhcj_60bad6', { data: ({ option0: conferenceDetail.title }) })
              const date = new Date()
              this.staffForm.date = [
                new Date().getTime(),
                date.setDate(date.getDate() + 3),
              ]
              this.dateLable = $t('marketing.commons.rwsj_b341f9')
              if (
                conferenceDetail.flowStatus == 0
                || conferenceDetail.flowStatus == 1
              ) { return }
              (this.$refs.materials.priValue = {
                type: 1,
                id: conferenceDetail.marketingEventId,
              }),
              (this.$refs.materials.item = {
                photoUrl: conferenceDetail.coverImageThumbUrl,
                summaryLine1: conferenceDetail.title,
                summaryLine2: conferenceDetail.location,
                id: conferenceDetail.id,
                type: '3',
                activityDetailSiteId: conferenceDetail.activityDetailSiteId,
              })
              this.staffForm.content = this.$refs.materials.item
            }
          })
      }
      if (materiel) {
        if (materiel.materielType === 'article') {
          http
            .queryArticleDetail({ articleId: materiel.materielId })
            .then(res => {
              const { data } = res
              this.$refs.materials.item = {
                photoUrl: data.photoUrl,
                summaryLine1: data.title,
                summaryLine2: data.summary,
                id: data.id,
                type: 1,
              }
              this.staffForm.content = this.$refs.materials.item
            })
        } else if (materiel.materielType === 'product') {
          http.queryProductDetail({ id: materiel.materielId }).then(res => {
            const { data } = res
            this.$refs.materials.item = {
              photoUrl: data.sharePicOrdinaryCutUrl || data.headPicsThumbs[0],
              summaryLine1: data.name,
              summaryLine2: data.summary,
              id: data.id,
              type: 4,
            }
            this.staffForm.content = this.$refs.materials.item
          })
        }
      }
    },
    showImage(params) {
      this.staffForm.coverPath = params.tapath
      this.defaultImageUrl = params.url
      this.$refs.staffForm.validateField('coverPath')
    },
    async addEmployeeMarketingActivity(params){
      const res = await http.addEmployeeMarketingActivity(params) || {}
      return res
    }
  },
}
</script>

<style lang="less" module>
.staff_activity_wrapper {
  margin-left: 60px;
  padding: 20px 0;
  width: 860px;
  box-sizing: border-box;
  &.dialog {
    margin-left: 0;
    width: auto;
    padding: 0;
    padding-left: 20px;
  }
  .staffTitle {
    font-size: 14px;
    color: #151515;
    margin: 40px 0 36px;
  }
  .memberRange {
    line-height: 20px;
    font-size: 12px;
    border: 1px solid #e9edf5;
  }
  .memberRangeTips {
    cursor: pointer;
    width: 100%;
    line-height: 36px;
    padding-left: 5px;
    margin-top: 10px;
    background: #f4f4f4;

    & > span {
      font-size: 14px;
      color: #ff8800;
      margin-left: 2px;
    }
  }
  .selectRange {
    border: 1px solid #e9edf5;
    height: 36px;
    line-height: 1;
    border-radius: 4px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    :global {
      .selectbar-input {
        border: 0;
      }
    }
  }
  :global {
    .el-form-item__label {
      font-size: 13px;
      color: #151515;
    }
    .el-input.el-date-editor {
      width: 346px;
    }
  }
  .article {
    // height: 90px;
    // background: #fcfcfc;
    // border: 1px dashed #dddddd;
    .contentTips {
      font-size: 12px;
      color: #333;
    }
  }
  .count {
    font-size: 13px;
    color: #c1c5ce;
    position: absolute;
    right: 5px;
    top: 45px;
  }
  .tips {
    color: #c1c5ce;
    font-size: 12px;
    margin-left: 9px;
    margin-top: -7px;
    display: flex;
    height: 22px;
  }
  .cover__tips {
    font-size: 12px;
    color: #91959e;
    line-height: 18px;
    margin-top: 12px;
  }
}
</style>
