<template>
  <div :class="$style.materials">
    <div v-if="showSelect" :class="$style.buttonWrapper">
      <div
        :class="$style.button"
      >
        <div
          :class="$style.selectBtn"
          @click="selectSpreadMode(1)"
        >
          <i class="el-icon-document-add" />
          <span>{{ $t('marketing.pages.promotion_activity.nrtg_cd1525') }}</span>
        </div>
        <div :class="$style.splitLine" />
        <div
          :class="$style.selectBtn"
          @click="selectSpreadMode(2)"
        >
          <i class="el-icon-copy-document" />
          <span>{{ $t('marketing.commons.dttg_261678') }}</span>
        </div>
        <template v-if="fxEmailSpreadEnabled">
          <div :class="$style.splitLine" />
          <div
            :class="$style.selectBtn"
            @click="handleEmailSpread"
          >
            <i class="el-icon-message" />
            <span>{{ $t('marketing.commons.yjtg_5f7157') }}</span>
          </div>
        </template>
      </div>
      <fx-link
        type="standard"
        target="_blank"
        :class="$style.promotionDoc"
        href="https://help.fxiaoke.com/93d5/9188/9302/d712"
      >
        {{ $t('marketing.pages.promotion_activity.tgsl_906d43') }}
      </fx-link>
    </div>
    <div
      v-else
      :class="$style.preview"
    >
      <mate-view
        v-if="spreadMode === 1 || spreadMode === 3"
        :vu-data="item"
        :vu-type="priValue.type"
      />
      <multi-materials
        v-if="spreadMode === 2"
        :data="multiMaterialsData"
        @select="handleMultiMaterialsSelect"
        @del="handleMultiMaterialsDel"
        @onChange="handleMaterialsChange"
      />
      <div
        v-if="!disable"
        :class="$style.previewBtn"
      >
        <div
          v-if="spreadMode === 3 && !isAgainSend"
          :class="$style.pbutton"
          @click="handleEmailSpread"
        >
          {{ $t('marketing.pages.promotion_activity.bjmb_a23b2d') }}
        </div>
        <div
          :class="$style.pbutton"
          @click="resetSelect"
        >
          {{ $t('marketing.commons.qcnr_c51a85') }}
        </div>
      </div>
    </div>
    <SelectMaterialDialog
      v-if="showDialog"
      :marketing-event-id="marketingEventId"
      :menus="['marketingEvent', 'materials']"
      :tabbar="smd_tabbar"
      :marketing-event-tabbar="smd_marketingEventTabbar"
      :visible.sync="showDialog"
      :is-multi-select="spreadMode === 2"
      :is-submit-hide="false"
      :ispartner="ispartner"
      @onSubmit="handleApply"
      @onClose="showDialog = false"
    />
  </div>
</template>

<script>
import { Message } from 'element-ui'
import SelectMaterialDialog, { MaterialInstance } from '@/components/select-material-dialog'
// import SelectDialog from '@/modules/notify-dialog/index';
import mateView from '@/components/mate-view';
import MultiMaterials from './multi-materials';
import http from '@/services/http/index';
import { messageBoxConfirm } from "@/utils/message-box";
import mailCoverImg from '@/assets/images/mail/mail-cover.png'
import kisvData from '@/modules/kisv-data.js'

export default {
  components: {
    // SelectDialog,
    mateView,
    SelectMaterialDialog,
    MultiMaterials,
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      vm.curMailMarketingId = from.query.mailMarketingId
      vm.getEmailMaterial(vm.curMailMarketingId)
    })
  },
  props: {
    value: {
      type: Object,
      default() {
        return {}
      },
    },
    ispartner: {
      type: Boolean,
      default: false,
    },
    marketingEventId: {
      type: String,
      default: '',
    },
    displayMode: {
      type: String,
      default: 'scaffold',
    },
    presetMaterial: {
      type: Object,
      default() {
        return {}
      },
    },
    multiMaterialsData: {
      type: Array,
      default: [],
    },
    mailMarketingId: {
      type: String,
      default: '',
    },
    isAgainSend: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      priValue: {
        type: 0,
        id: '',
      },
      item: null,
      showDialog: false,
      isConferenceMarketingEvent: false,
      disable: false,
      showSelect: true,
      vDatas: kisvData.datas,
      spreadMode: 0, // 0: 初始状态 1: 单物料 2: 多物料 3: 邮件推广
      curMailMarketingId: '',
      // multiMaterialsData: []
    }
  },
  computed: {
    // 1:文章 3:会议 4:产品 10:落地页 24:海报 16:表单 25:邀请函
    smd_tabbar() {
      if (this.spreadMode === 1) {
        return this.isConferenceMarketingEvent ? [10, 3, 1] : [10, 1, 4]
      }
      return [2]
    },
    smd_marketingEventTabbar() {
      if (this.spreadMode === 1) {
        return this.isConferenceMarketingEvent ? [3, 10, 1, 24, 9999] : [10, 1, 4, 24, 9999]
      }
      return [24]
    },
    fxEmailSpreadEnabled() {
      return this.vDatas.pluginInfo.fxEmailSpreadEnabled
    },
  },
  watch: {
    value: {
      deep: true,
      handler() {
        this.peek()
      },
    },
    marketingEventId() {
      console.log('marketingEventId', this.marketingEventId)
      this.checkConferenceStatus()
    },
    mailMarketingId(val) {
      this.curMailMarketingId = val
      this.getEmailMaterial(val)
    },
    presetMaterial() {
      this.getMaterialDetail()
    },
    multiMaterialsData() {
      if (this.multiMaterialsData && this.multiMaterialsData.length) {
        this.showSelect = false
        this.spreadMode = 2
      }
    },
    item() {
      const isMulti = this.multiMaterialsData && this.multiMaterialsData.length
      if (!isMulti && this.item && this.item.id) {
        this.showSelect = false
        this.spreadMode = 1
      }
    },
  },
  mounted() {
    this.peek()
    this.checkConferenceStatus()
    this.getMaterialDetail()

    if (this.multiMaterialsData && this.multiMaterialsData.length) {
      this.showSelect = false
      this.spreadMode = 2
    }

    if (this.curMailMarketingId) {
      this.getEmailMaterial(this.curMailMarketingId)
    }
  },
  methods: {
    handleEmailSpread() {
      this.spreadMode = 3
      this.$router.push({
        name: 'mail-template-create',
        query: {
          scene: 'mail-marketing',
          id: this.curMailMarketingId,
          marketingEventId: this.marketingEventId,
        },
      })
    },
    selectSpreadMode(mode) {
      this.showDialog = true
      this.spreadMode = mode
    },
    handleMultiMaterialsSelect() {
      this.showDialog = true
      console.log('this.multiMaterialsData: ', this.multiMaterialsData)
    },
    handleMultiMaterialsDel(index) {
      if (this.multiMaterialsData && this.multiMaterialsData.length === 1) {
        this.resetSelect()
        return
      }

      if (this.multiMaterialsData && this.multiMaterialsData.length > index) {
        const multiMaterialsData = this.multiMaterialsData.filter((el, idx) => idx !== index)
        this.$emit('input', multiMaterialsData, this.spreadMode)
      }
    },
    handleMaterialsChange(multiMaterialsData) {
      this.$emit('input', multiMaterialsData, this.spreadMode)
    },
    resetSelect() {
      this.showSelect = true
      this.spreadMode = 0
      this.curMailMarketingId = ''
      this.$emit('input')
    },
    peek() {
      $.extend(true, this.priValue, this.value)
    },
    handleApply(data) {
      console.log('handleApply', data)
      if (this.spreadMode === 1) {
        this.showSelect = false
        this.showDialog = false
        const { priValue } = this
        priValue.id = data.id
        priValue.type = data.type
        priValue.qrPostForwardType = data.qrPostForwardType || null
        priValue.data = data
        priValue.activityDetailSiteId = data.activityDetailSiteId
        this.item = {
          photoUrl: data.image,
          summaryLine1: data.title,
          summaryLine2: data.summary,
        }
        this.$emit('input', priValue, this.spreadMode)
      } else if (this.spreadMode === 2) {
        if (data && data.length) {
          const obj = Object.fromEntries(data.map(item => [item.id || item.objectId, true]))
          const merged = this.multiMaterialsData.reduce((pre, cur) => {
            obj[cur.id || cur.objectId] ? '' : obj[cur.id || cur.objectId] = true && pre.push(cur)
            return pre
          }, data)
          if (merged.length > 9) {
            messageBoxConfirm({
              mainText: $t('marketing.commons.zdxzz_7429db'),
              boxConfig: {
                showCancelButton: false,
                zIndex: 10000,
              },
            })
            return
          }
          const _merged = merged.filter(item => !item.size || (item.size && item.size < 10000000))
          _merged.length < merged.length && Message.error($t('marketing.commons.dttgxztpdx_370a77'))
          this.showSelect = false
          this.showDialog = false
          this.$emit('input', _merged, this.spreadMode)
        } else {
          this.showSelect = false
          this.showDialog = false
        }
      }
    },
    async checkConferenceStatus() {
      if (!this.marketingEventId) return
      const { errCode, data } = await http.checkConferenceStatus({
        marketingEventId: this.marketingEventId,
      })
      if (errCode == 0) {
        this.isConferenceMarketingEvent = data.bindConference
      } else {
        this.isConferenceMarketingEvent = false
      }
    },
    // 带入contentType和contentId时回填数据，目前仅支持文字类型和海报类型输入
    async getMaterialDetail() {
      let DATA
      if (this.displayMode !== 'dialog') {
        DATA = this.$route.query
      } else {
        this.spreadMode = 1
        this.showSelect = true
        DATA = this.presetMaterial
      }
      const { contentType, contentId } = DATA
      if (contentType && contentId) {
        this.disable = true
        const result = await MaterialInstance.getMaterialDetailByContentType(contentType, contentId)
        if (result && result.errCode === 0) {
          const data = MaterialInstance.formatDataByContentType(contentType, result.data)
          console.log('getMaterialDetail', data)
          this.handleApply({
            id: contentId,
            type: contentType,
            image: data.image,
            aPath: data.aPath,
            title: data.title,
            summary: data.summary,
            objectType: data.objectType,
            activityDetailSiteId: data.activityDetailSiteId || '',
          })
        }
      }
    },
    getEmailMaterial(id) {
      this.spreadMode = 3
      if (id) {
        http.getEmailMaterial({
          id,
        }).then(res => {
          if (res && res.errCode === 0) {
            this.showSelect = false
            this.item = {
              photoUrl: mailCoverImg,
              title: res.data.title,
            }
          }
        })
      }
    },
  },
}
</script>

<style lang="less" module>
@images: '../../../assets/images/';
.materials {
  min-height: 90px;
  display: flex;
  align-items: stretch;

  .buttonWrapper {
    display: flex;
    width: 100%;
  }
  .button {
    display: flex;
    flex: 1 auto;
    justify-content: center;
    align-items: center;
    color: var(--color-info06,#407FFF);
    text-align: center;
    cursor: pointer;
    border: 1px dashed #ddd;
    background: #fcfcfc;
    .icon {
      display: inline-block;
      width: 12px;
      height: 12px;
      margin-right: 5px;
      background: url('@{images}icons/add-dark.png') left center no-repeat;
      background-size: contain;
    }
      .selectBtn {
        flex: 1;
      }
      .splitLine {
        height: 40px;
        border-left: 1px solid #DEE1E8;
      }
  }
  .preview {
    display: flex;
    flex: 1 auto;
    .previewBtn {
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
      .pbutton {
        padding: 0 20px;
        white-space: nowrap;
        color: var(--color-info06,#407FFF);
        align-self: flex-end;
        cursor: pointer;
        flex-shrink: 1;
        height: 24px;
        line-height: 24px;
      }
    }
  }
  .promotionDoc {
    display: flex;
    align-items: flex-end;
    margin-left: 8px;
    font-size: 14px;
    line-height: 20px;
  }
}
</style>
