<!-- 组件说明 -->
<template>
  <div>
    <sideslip-popup
      class="website-pie-sideslip"
      width="900px"
      @close="handleClose"
      :visible="visible"
      :overflowyHidden="detailVisible"
    >
      <div class="header">
        <div class="content">
          <div class="main">
            <div class="p1">
              {{ currentItem.qrCodeName }}
            </div>
            <div class="info">
              <span>{{$t('marketing.commons.lx_e91f5a')}}{{ currentItem.type || "--" }} </span
              ><span>{{$t('marketing.pages.qywx_manage.tjhysz_36ab40')}}{{ currentItem.skipVerify || "--" }} </span>
            </div>
            <div class="info">
              <span
                >{{$t('marketing.pages.qywx_manage.bz_751ce6')}}{{
                  (currentItem.remark && currentItem.remark.text) || "--"
                }}
              </span>
            </div>
          </div>
        </div>
      </div>
      <div ref="friends-record-table" style="height: calc(100vh - 176px)"></div>
      <!-- <div class="side-title">已添加客户明细</div>
      <div class="operation">
        <div class="left">
          <div class="operation__select" v-show="selectedNumber > 0">
            <div class="select__count">已选中{{ selectedNumber }}人</div>
            <fx-button
              class="select__cancel"
              type="text"
              size="mini"
              v-show="selectedNumber !== 0"
              @click="handleCancelSelect"
              >取消选中</fx-button
            >
          </div>
          <fx-button
            class="operation__button"
            size="mini"
            v-show="selectedNumber !== 0"
            @click="handleEditTags"
            >设置标签</fx-button
          >
        </div>
        <div class="right">
          <fx-input
            class="operation__search"
            placeholder="搜索微信昵称"
            suffix-icon="el-icon-search"
            size="small"
            v-model="model_nickname"
            @change="handleNicknameEnter"
          ></fx-input>
          <fx-input
            class="operation__search"
            placeholder="搜索添加人"
            suffix-icon="el-icon-search"
            size="small"
            v-model="model_nickname"
            @change="handleNicknameEnter"
          ></fx-input>
          <DatePicker
            class="operation__search"
            placeholder="选择添加时间"
            type="date"
            v-model="model_time"
          ></DatePicker>
          <fx-link type="standard" style="padding:8px 20px 8px 10px" style="padding: 0 8px">导出</fx-link>
        </div>
      </div>
      <div class="side-wrapper">
        <v-table
          class="tablewbg"
          ref="customer"
          @selection-change="selectionChange"
          :columns="tableColumns"
          emptyText="暂无数据"
          :data="tableDatas"
          :settable="true"
          tid="marketing-meeting-participants-table"
          :rowKey="getRowKeys"
          @click:pictext="handleClickWxName"
        >
          <template slot-scope="scope">
            <template v-if="scope.col.exComponent === 'custom-tags'">
              <fx-popover title="标签" trigger="click" width="300">
                <div class="qywx-customer-tag_wrapper" slot="reference">
                  <template v-for="(item, index) in scope.row.tagNameList">
                    <span
                      :class="[
                        'tag',
                        index + 1 === scope.row.tagNameList.length && 'last'
                      ]"
                      v-if="index <= _getNumsTag2Show(scope.row.tagNameList)"
                      :key="index"
                    >
                      {{
                        index == _getNumsTag2Show(scope.row.tagNameList) &&
                        index + 1 !== scope.row.tagNameList.length
                          ? ""
                          : item.firstTagName +
                            (item.secondTagName
                              ? `：${item.secondTagName}`
                              : "")
                      }}
                    </span>
                  </template>
                </div>
                <div class="qywx-customer-tag_wrapper_pop">
                  <span
                    class="tag_pop"
                    v-for="(item, index) in scope.row.tagNameList"
                    :key="index"
                    >{{
                      item.firstTagName +
                        (item.secondTagName ? `：${item.secondTagName}` : "")
                    }}</span
                  >
                </div>
              </fx-popover>
            </template>
          </template>
        </v-table>
        <v-pagen
          v-if="pageData.totalCount > 5"
          :pagedata.sync="pageData"
          @change="handlePageChange"
        ></v-pagen>
      </div>
      <loading-mask :show="showLoadingMask"></loading-mask>
      <user-detail
        top="0px"
        :userId="userMarketingAccountId"
        :visible="detailVisible"
        @close="handleCloseDetail"
      ></user-detail> -->
    </sideslip-popup>
    <TagsSelector
      v-if="showAddFor"
      :visible="showAddFor"
      :defaultTags="selectTags"
      @update:visible="showAddFor = false"
      @change="addForSubmit"
    ></TagsSelector>
  </div>
</template>

<script>

import LoadingMask from "@/components/loading-mask/index";
import VTable from "@/components/table-ex";
import SideslipPopup from "@/components/sideslip-popup/index.vue";
import http from "@/services/http/index";
import UserDetail from "@/pages/user/detail";
import TagsSelector from "@/components/tags-selector-new/tags-selector";
import VPagen from "@/components/kitty/pagen";
import CONFIG from "./config";
import { requireAsync } from "@/utils";

export default {
  components: {
elButton: FxUI.Button,
SideslipPopup,
VTable,
LoadingMask,
UserDetail,
TagsSelector,
VPagen,
DatePicker: FxUI.DatePicker
},
  data() {
    return {
      tableColumns: [],
      tableDatas: [],
      showPage: "spreadDetail",
      currentItem: {},
      showLoadingMask: true,
      // 列表接口相关
      model_nickname: "",
      model_time: "",
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 5,
        time: 0,
        pageSizes: [5, 15, 25, 35]
      },

      // 客户详情页相关
      detailVisible: false,
      userMarketingAccountId: null,

      // 客户标签相关
      selectTags: [],
      selectedCustomers: [],
      showAddFor: false,
      getRowKeys: function(row) {
        return row.id;
      }
    };
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    item: {
      type: Object,
      default: {}
    }
  },
  computed: {
    selectedNumber() {
      return this.selectedCustomers.length;
    }
  },
  methods: {
    handleclueTablePage() {},
    changePage(pageName) {
      if (
        pageName == "linkDetail" &&
        this.mailGroupSendResult.clickCount == 0
      ) {
        return;
      }
      this.showPage = pageName;
    },
    handleClose() {
      this.$emit("close");
    },
    prview() {
      this.showPreviewDialog = true;
    },
    handleClueDetail(row) {
      if (row.saveCrmStatus === 0) {
        if(!row.otherCrmObjectBind) return;
        CRM.api.show_detail({
          apiname: row.otherCrmObjectBind.apiName,
          id: row.otherCrmObjectBind.objectId
        });
      }
    },
    handleClueRow(row, column, event) {
      console.log("click clue row", ...arguments);
      this.handleClueDetail(row);
    },
    queryContactList() {
      let params = {
        name: this.model_nickname,
        pageNum: this.pageData.pageNum,
        pageSize: this.pageData.pageSize,
        fanQrCodeId: this.currentItem.id
      };
      http.queryContactList(params).then(res => {
        this.showLoadingMask = false;
        if (res && res.errCode == 0) {
          this.pageData.totalCount = res.data.totalCount;
          this.tableColumns = CONFIG.getColumns(res.data);
          this.tableDatas = CONFIG.getDatas(res.data);
          this.handleCancelSelect();
        }
      });
    },
    initData() {
      console.log(this.currentItem);
      //发送详情
      this.queryContactList();
    },
    /* 获取可显示标签数量 */
    _getNumsTag2Show(tags) {
      let acc = 0;
      let len = tags.length;
      _.some(tags, (tag, index) => {
        acc +=
          (
            tag.firstTagName +
            (tag.secondTagName ? `：${tag.secondTagName}` : "")
          ).replace(/[^\x00-\xff]/g, "xx").length *
            6 +
          26;
        if (acc < 270) return false;
        len = index;
        return true;
      });
      return len;
    },
    selectionChange(val) {
      this.selectedCustomers = val;
    },
    handleCancelSelect() {
      this.selectedCustomers = [];
      this.toggleSelection();
    },
    toggleSelection(rows) {
      if (!this.$refs.customer) return;

      if (rows) {
        rows.forEach(row => {
          this.$refs.customer.toggleRowSelection(row);
        });
      } else {
        this.$refs.customer.clearSelection();
      }
    },
    // 点击微信昵称
    handleClickWxName(name, data) {
      http.associateContact({ id: data.id }).then(
        res => {
          if (res.errCode) return FxUI.Message.error($t('marketing.commons.hqxqxxsb_35fd2d'));
          this.userMarketingAccountId = res.data.userMarketingAccountId;
          this.detailVisible = true;
        },
        () => FxUI.Message.error($t('marketing.commons.hqxqxxsb_35fd2d'))
      );
    },
    // 页码改变
    handlePageChange(data) {
      this.pageData.pageNum = data.pageNum;
      this.pageData.pageSize = data.pageSize;
      this.queryContactList();
    },
    // 关闭侧滑详情
    handleCloseDetail(isChanged) {
      this.detailVisible = false;
      isChanged && this.queryContactList();
    },
    // 聚焦搜索框时，回车触发搜索
    handleNicknameEnter(e) {
      this.handlePageChange({
        pageNum: 1,
        pageSize: this.pageData.pageSize
      });
    },
    // 批量操作营销用户标签
    handleEditTags(action) {
      var selectTags = [];
      this.selectedCustomers.forEach(row => {
        selectTags.push(...row.tagNameList);
      });
      this.selectTags = selectTags;
      this.showAddFor = true;
    },
    // 设置标签提交
    async addForSubmit(tags) {
      if (!tags.length) {
        this.showAddFor = false;
        return;
      }
      const msg = $t('marketing.commons.szbqcg_3eef7a');
      const errMsg = $t('marketing.commons.szbqsb_fce3a1');

      const req = {
        crmObjectDescribeApiName: "WechatWorkExternalUserObj",
        tagNames: [],
        crmObjectIds: _.map(this.selectedCustomers, item => item.id)
      };

      const { addTagNames, delTagNames } = this.getRequestTagNames(
        this.selectTags,
        tags
      ); // 解析出新增的标签数组、及删除的标签数组
      let ress = [];
      if (addTagNames.length) {
        ress.push(
          await http.commonBatchAddTagNamesToCrmData({
            ...req,
            tagNames: addTagNames
          })
        );
      }
      if (delTagNames.length) {
        ress.push(
          await http.commonBatchDeleteTagNamesToCrmData({
            ...req,
            tagNames: delTagNames
          })
        );
      }
      this.showAddFor = false;
      if (ress.some(item => item.errCode !== 0)) {
        return FxUI.Message.error(errMsg);
      }
      FxUI.Message.success(msg);
      this.queryContactList();
    },
    getRequestTagNames(oldTagNames, newTagNames) {
      const addTagNames = [];
      const delTagNames = [];
      oldTagNames = oldTagNames.map(item => ({
        ...item,
        nameid:
          item.firstTagName +
          (item.secondTagName ? `:${item.secondTagName}` : "")
      }));

      oldTagNames.forEach(oitem => {
        if (newTagNames.map(nitem => nitem.nameid).indexOf(oitem.nameid) === -1)
          delTagNames.push(oitem);
      });
      newTagNames.forEach(nitem => {
        if (oldTagNames.map(oitem => oitem.nameid).indexOf(nitem.nameid) === -1)
          addTagNames.push(nitem);
      });
      return { addTagNames, delTagNames };
    },
    initPageList() {
      requireAsync("crm-modules/page/list/list", List => {
        let passbackTable = new List({
          wrapper: this.$refs["friends-record-table"],
          apiname: "WechatFriendsRecordObj",
          showMultiple: true, // 不显示复选框
          // noAlwaysShowPage: true,
          // title: '11',
          autoHeight: false // 是否和内容等高
        });

        passbackTable.setFilterParam("qr_code_id", {
          field_name: "qr_code_id",
          field_values: [this.item.id || ""],
          operator: "EQ"
        });

        const originTrclickHandle = passbackTable.trclickHandle;
        passbackTable.trclickHandle = (data, $tr, $target) => {
          originTrclickHandle.bind(passbackTable)(data, $tr, $target);
          console.log(data);
        };

        passbackTable.render();
        
        passbackTable.getOperateBtns = function() {
          return [
            {
              action: "export",
              text: $t('marketing.commons.dc_55405e'),
              index: 1
            }
          ];
        };
        passbackTable.on("complete", () => {
          passbackTable.$(".crm-btn-group + .crm-btn-group").hide();
        });
      });
    }
  },
  mounted() {
    this.currentItem = this.item;
    this.initData();
    this.initPageList();
  },
  created() {},
  destroyed() {} //生命周期 - 销毁完成
};
</script>

<style lang="less" scoped>
.website-pie-sideslip {
  .header {
    display: flex;
    align-items: center;
    height: 120px;
    background-color: #f6f9fc;
    justify-content: space-between;
    padding: 0px 55px 0 25px;
    .button {
      height: 32px;
    }
    .content {
      display: flex;
      height: 100px;
      .main {
        display: flex;
        flex-direction: column;
        font-size: 12px;
        justify-content: center;
        color: #91959e;
      }
      .p1 {
        font-size: 14px;
        color: #181c25;
        margin-bottom: 10px;
        width: 500px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        /* autoprefixer: off */
        -webkit-box-orient: vertical;
        /* autoprefixer: on */
        -webkit-line-clamp: 1;
        margin-bottom: 10px;
        font-weight: bold;
      }
      .p2 {
        color: #545861;
      }
      .blue {
        color: var(--color-info06);
      }
      .info {
        margin-top: 8px;
        color: #545861;
        span {
          margin-right: 30px;
        }
      }
    }
  }
  .side-title {
    margin-top: 20px;
    font-size: 12px;
    color: #181c25;
    padding-left: 16px;
    border-left: 4px solid var(--color-primary06,#ff8000);
    margin-bottom: 5px;
  }
  .number-wrapper {
    margin: 0px 18px 0px 25px;
    width: 856px;
    border: 1px solid #e9edf5;
    height: 125px;
    display: flex;
    align-items: center;
    .num {
      font-size: 16px;
      color: #333333;
      .mini {
        display: inline-block;
        font-size: 12px;
        border-left: 1px solid #ccc;
        height: 12px;
        line-height: 12px;
        margin-left: 5px;
        padding-left: 5px;
      }
    }
    .blue {
      color: var(--color-info06);
      cursor: pointer;
    }
    .des {
      font-size: 12px;
      color: #545861;
      .mini {
        font-size: 12px;
        display: inline-block;
        border-left: 1px solid #ccc;
        height: 12px;
        line-height: 12px;
        margin-left: 5px;
        padding-left: 5px;
      }
    }
    .number {
      width: 623px;
      display: flex;
      flex-wrap: wrap;
      .item {
        height: 82px;
        width: 123px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
      }
    }
    .line {
      width: 1px;
      height: 57px;
      background-color: #e9edf5;
    }
    .total {
      margin-left: 45px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
    }
  }
  .operation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 50px;
    margin-right: 20px;
    .left {
      display: flex;
    }
    .right {
      display: flex;
    }
    .operation__search {
      width: 160px;
      margin-right: 10px;
      &:last-child {
        margin-right: 20px;
      }
    }
    .operation__select {
      display: flex;
      align-items: center;
      margin-left: 20px;
    }
    .operation__button {
      margin-left: 16px;
      &.button--send {
        margin-left: auto;
        margin-right: 20px;
      }
    }
  }
  .side-wrapper {
    margin: 0 20px;

    .tablewbg {
      width: auto;
      border-top: 1px solid #e9edf5 !important;
      border-left: 1px solid #e9edf5 !important;
      box-sizing: border-box;
      overflow: auto;
      .table_image {
        display: flex;
        width: 100%;
        align-items: center;
        > img {
          width: 30px;
          height: 30px;
          margin-right: 5px;
        }
      }
    }
    .table__more {
      height: 40px;
      border: 1px solid #e9edf5;
      border-top-width: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      a {
        font-size: 12px;
        text-decoration: none;
      }
    }
  }
  .detail-header {
    display: flex;
    height: 82px;
    background-color: #f6f9fc;
    font-size: 14px;
    color: #181c25;
    align-items: center;
    padding: 0 20px;
    .back {
      flex-shrink: 0;
      padding-right: 16px;
      cursor: pointer;
      color: var(--color-primary06,#407FFF);
    }
    .arrow-img {
      width: 16px;
      transform: rotate(90deg);
    }
    .line {
      background-color: #e9edf5;
      width: 1px;
      height: 20px;
    }
    .title {
      margin-left: 16px;
      width: 550px;
    }
  }
}
</style>
