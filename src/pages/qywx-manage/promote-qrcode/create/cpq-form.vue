<template>
  <div class="create-promote-qrcode-form">
    <el-form
      ref="form"
      class="form__wrapper"
      :model="cpq_form"
      :rules="cpq_form_rules"
      label-width="auto"
      label-position="left"
    >
      <el-form-item
        :label="$t('marketing.pages.qywx_manage.yghmmc_937c44')"
        prop="qrCodeName"
      >
        <fx-input
          v-model="cpq_form.qrCodeName"
          class="formitem_input formitem_input--mb0"
          maxlength="100"
          size="small"
          show-word-limit
        />
      </el-form-item>
      <el-form-item
        :label="$t('marketing.commons.lx_226b09')"
        prop="type"
      >
        <el-form-radio-group
          v-model="cpq_form.type"
          :disabled="formTypeDisabled"
          @change="handleTypeChange"
        >
          <el-form-radio :label="1">
            {{ $t('marketing.commons.dr_9a91ff') }}
          </el-form-radio>
          <el-form-radio :label="2">
            {{ $t('marketing.commons.dr_118c90') }}
          </el-form-radio>
        </el-form-radio-group>
      </el-form-item>
      <el-form-item
        :label="$t('marketing.pages.qywx_manage.syry_fb2357')"
        prop="userId"
      >
        <staff-filters
          v-model="staffFiltersValue"
          :placeholder="$t('marketing.pages.qywx_manage.xzsyry_09fce9')"
          :show-select="false"
          :select-options="{
            qywxDepartment: cpq_form.type === 2,
            single: cpq_form.type === 1,
          }"
        />
      </el-form-item>
      <el-form-item
        :label="$t('marketing.pages.qywx_manage.tjsz_6ebf43')"
        prop="skipVerify"
      >
        <fx-switch
          v-model="cpq_form.skipVerify"
          :active-value="0"
          :inactive-value="1"
          size="small"
        />
        <span class="form__item--tips">{{ $t('marketing.pages.qywx_manage.khtjswxjgq_c9dac2') }}</span>
      </el-form-item>
      <el-form-item
        :label="$t('marketing.pages.qywx_manage.tjhyhdbq_6bc033')"
        prop="tag"
      >
        <selector-line
          v-model="cpq_form.tag"
          class="tag"
          :add-text="$t('marketing.commons.xzbq_f9b05d')"
          :single-line="false"
        />
      </el-form-item>
      <el-form-item
        :label="$t('marketing.commons.tjhyzdhf_7fd4ed')"
        prop="welcomeContent"
      >
        <MsgContents
          ref="conts"
          v-model="cpq_form.attachContents"
          class="content"
          :marketing-event-id="marketingEventObj.id"
          :tabbar="[4]"
          :send-content.sync="cpq_form.welcomeContent"
        />
        <!-- <div class="reply">
          <el-input
            v-model="cpq_form.welcomeContent"
            class="reply-text"
            type="textarea"
            maxlength="450"
            resize="none"
            :placeholder="$t('marketing.pages.qywx_manage.qyzdhfxxgn_801b64')"
            show-word-limit
          />
          <div class="reply-media">
            <i
              v-if="mediaType"
              class="reply-media-clear el-icon-circle-close"
              @click="hanldeClearMedia"
            />
            <div
              v-if="mediaType === 'picture' && cpq_form.welcomeImagePath"
              class="reply-media-preview"
            >
              <i class="el-icon-picture-outline" />
              {{
                selectMaterial.photoName || cpq_form.welcomeImageTitle || "--"
              }}
            </div>
            <div
              v-else-if="
                mediaType === 'content' && cpq_form.welcomeMiniprogramTitle
              "
              class="reply-media-preview"
            >
              <i class="el-icon-notebook-2" />
              {{
                selectMaterial.title || cpq_form.welcomeMiniprogramTitle || "--"
              }}
            </div>
            <div
              v-else-if="mediaType === 'link' && cpq_form.welcomeLinkTitle"
              class="reply-media-preview"
            >
              <i class="el-icon-link" />
              <a
                :href="cpq_form.welcomeLinkUrl"
                target="_blank"
              >
                {{ cpq_form.welcomeLinkTitle || "--" }}
              </a>
            </div>
            <Popover
              v-else
              placement="top"
              width="260"
              trigger="click"
              popper-class="reply__popover"
              :offset="-10"
            >
              <div class="reply-media-list">
                <div
                  class="reply-media-list-type"
                  @click="handleChooseMaterial('picture')"
                >
                  <i class="el-icon-picture-outline" />
                  <p>{{ $t('marketing.commons.tp_20def7') }}</p>
                </div>
                <div
                  class="reply-media-list-type"
                  @click="handleChooseMaterial('link')"
                >
                  <i class="el-icon-link" />
                  <p>{{ $t('marketing.commons.wy_18a481') }}</p>
                </div>
                <div
                  class="reply-media-list-type"
                  @click="handleChooseMaterial('content')"
                >
                  <i class="el-icon-notebook-2" />
                  <p>{{ $t('marketing.commons.nr_2d711b') }}</p>
                </div>
              </div>
              <div
                slot="reference"
                class="reply-placeholder"
              >
                <i :class="['el-icon-plus', 'reply-placeholder-icon']" />
                {{ $t('marketing.commons.qxztp_260b4d') }}/{{ $t('marketing.commons.wy_18a481') }}/{{ $t('marketing.commons.nr_2d711b') }}
              </div>
            </Popover>
          </div>
        </div> -->
      </el-form-item>
      <el-form-item
        :label="$t('marketing.commons.tgqd_054ed9')"
        prop="channel"
      >
        <ChannelSelector
          :value="cpq_form.channelValue"
          :hide-wechat-selector="true"
          :encode-other-value="false"
          @change="handleSpreadChannelChange"
        />
      </el-form-item>
      <el-form-item
        :label="$t('marketing.commons.schd_833ba0')"
        prop="marketingEventId"
      >
        <v-pick-selfobject
          v-model="marketingEventObj"
          class="formitem_input"
          :disabled="marketingDisabled"
        />
      </el-form-item>
      <el-form-item
        :label="$t('marketing.pages.qywx_manage.zdbzm_b7ca05')"
        prop="remark"
      >
        <div class="remark-wrapper">
          <div class="remark-switch-wrapper">
            <fx-switch
              v-model="cpq_form.userBaseRemarkStatus"
              size="small"
              :active-value="1"
              :inactive-value="0"
            />
            <span class="remark-switch-text">
              {{ $t('marketing.pages.qywx_manage.kqhkyzhymc_83b854') }}
            </span>
          </div>
          <div class="remark-content-wrapper" v-if="cpq_form.userBaseRemarkStatus === 1">
            <fx-input
              v-model="cpq_form.userBaseRemark"
              prop="userBaseRemark"
              class="formitem_input formitem_input--mb0"
              size="small"
              :maxlength="8"
              :placeholder="$t('marketing.pages.qywx_manage.qsrbznryyq_680b11')"
              show-word-limit
            />
          </div>
        </div>
      </el-form-item>
    </el-form>
    <SelectMaterialDialog
      v-if="metarialDialogVisible"
      :menus="['materials']"
      :tabbar="metarialTabs"
      :title="metarialTitle"
      :visible="metarialDialogVisible"
      @onSubmit="handleMaterialConfirm"
      @onClose="metarialDialogVisible = false"
    />
    <Dialog
      :title="$t('marketing.commons.tjwy_3cd281')"
      :visible.sync="linkVisible"
      @onClose="linkVisible = false"
      @onSubmit="handleLinkFormConfirm"
    >
      <el-form
        ref="linkForm"
        :model="linkForm"
        :rules="linkFormRules"
        label-width="80px"
        label-position="left"
        style="padding: 10px 20px 0 20px"
      >
        <el-form-item
          :label="$t('marketing.commons.ljbt_03e840')"
          prop="welcomeLinkTitle"
        >
          <fx-input
            v-model="linkForm.welcomeLinkTitle"
            :placeholder="$t('marketing.commons.qsrljbt_3f4396')"
          />
        </el-form-item>
        <el-form-item
          :label="$t('marketing.commons.wylj_6beee8')"
          prop="welcomeLinkUrl"
        >
          <fx-input
            v-model="linkForm.welcomeLinkUrl"
            placeholder="https://www.fxiaoke.com"
          />
        </el-form-item>
      </el-form>
    </Dialog>
    <PictureSelector
      :visible.sync="isShowCutterDialog"
      :need-apath="true"
      output-path-type="a"
      @submit="handlePictureSelectorSubmit"
    />
  </div>
</template>

<script>
import StaffFilters from '@/components/staff-filters-selector/index.vue'
import SelectorLine from '@/components/tags-selector-new/tags-line.vue'
import SelectMaterialDialog from '@/components/select-material-dialog/index.vue'
import PictureSelector from '@/components/PictureSelector/index.vue'
import Dialog from '@/components/dialog/index.vue'
import http from '@/services/http/index.js'
import { alert } from '@/utils/globals'
import ChannelSelector from '@/components/ChannelSelector/index.vue'
import VPickSelfobject from '@/pages/promotion-activity/common/pick-selfobject.vue'
import MsgContents from '@/components/msg-contents/index.vue'
import { formatAttachToMedia, formatMediaToAttach, filterMaterialMaps } from '@/components/msg-contents/const.js'

export default {
  components: {
    ElForm: FxUI.Form,
    ElFormItem: FxUI.FormItem,
    ElFormRadioGroup: FxUI.RadioGroup,
    ElFormRadio: FxUI.Radio,
    ElSwitch: FxUI.Switch,
    SelectorLine,
    SelectMaterialDialog,
    Popover: FxUI.Popover,
    Dialog,
    PictureSelector,
    ChannelSelector,
    VPickSelfobject,
    StaffFilters,
    MsgContents,
  },
  data() {
    return {
      id: '',
      selectQywxUser: [],
      selectQywxDeparment: [],
      metarialTitle: $t('marketing.commons.qxznr_db30d5'),
      metarialDialogVisible: false,
      metarialTabs: [],
      selectMaterial: {},
      channels: [{ value: $t('marketing.commons.qywx_ff17b9'), label: $t('marketing.commons.qywx_ff17b9') }],
      linkVisible: false,
      mediaType: '',
      linkForm: {
        welcomeLinkTitle: null,
        welcomeLinkUrl: null,
        welcomeLinkDesc: null,
        welcomeLinkImagePath: null,
      },
      marketingEventObj: {},
      cpq_form: {
        attachContents: [],
        qrCodeName: '',
        type: 1,
        userId: [],
        departmentIds: [],
        tagIds: [],
        skipVerify: 0, // 0：跳过验证 1：需要验证
        channelDesc: '',
        remark: '',
        tag: [],
        welcomeContent: '',
        welcomeImagePath: null,
        welcomeImageTitle: null,
        welcomeMiniprogramTitle: null,
        welcomeMiniprogramMediaPath: null,
        welcomeMinigramPage: null,
        channelValue: '', // 默认企业微信
        userBaseRemarkStatus: 0,
        userBaseRemark: '',
      },
      marketingDisabled: false,
      replyCount: 0,
      cpq_form_rules: {
        qrCodeName: [
          { required: true, message: $t('marketing.commons.qsrewmmc_410465'), trigger: 'blur' },
          { max: 100, message: $t('marketing.commons.zsccxz_723c70'), trigger: 'blur' },
        ],
        type: [{ required: true, message: $t('marketing.pages.qywx_manage.qxzlx_95f11c'), trigger: 'change' }],
        userId: [{ required: true, validator: this.validateUserId, trigger: 'blur' }],
        // skipVerify: [{ required: true, message: '请选择类型', trigger: 'change' }],
        channelDesc: [{ max: 30, message: $t('marketing.pages.qywx_manage.qdqfzdzdgz_00d330'), trigger: 'blur' }],
        remark: [{ max: 30, message: $t('marketing.pages.qywx_manage.bzzdzdgzf_d8d40a'), trigger: 'blur' }],
        // tag: [{ validator: this.validateTag, trigger: "blur" }],
        channelValue: [{ required: true, message: $t('marketing.commons.qxztgqd_d5ff9d'), trigger: 'blur' }],
        welcomeContent: [
          {
            validator: this.validateWelcomeContent,
            trigger: 'change',
          },
        ],
        userBaseRemark: [
          { 
            validator: (rule, value, callback) => {
              if (this.cpq_form.userBaseRemarkStatus === 1 && !value) {
                callback(new Error($t('marketing.pages.qywx_manage.qsrbznr_088043')))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          },
        ],
      },
      linkFormRules: {
        welcomeLinkTitle: [{ required: true, message: $t('marketing.commons.qtxljbt_7d35af'), trigger: 'blur' }],
        welcomeLinkUrl: [
          {
            required: true,
            trigger: 'change',
            validator: (rule, value, callback) => {
              if (
                value
                && value.match(
                  /(http(s)?:\/\/.)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/g,
                ) == null
              ) {
                callback(new Error(`${$t('marketing.commons.qsrzqdwylj_ecfa59')}https://www.fxiaoke.com`))
              } else if (!value) {
                callback(new Error($t('marketing.commons.qtxwylj_fec91f')))
              } else {
                callback()
              }
            },
          },
        ],
      },
      isShowCutterDialog: false,
      staffFiltersValue: {
        type: 1,
        colleague: [],
        departmentIds: [],
        tagIds: [],
      },
    }
  },
  computed: {
    formTypeDisabled() {
      return !!this.id
    },
  },
  watch: {
    staffFiltersValue(newVal) {
      console.log('staffFiltersValue newVal: ', newVal)

      const { colleague, departmentIds, tagIds } = newVal
      this.cpq_form.userId = colleague && colleague.length ? colleague.map(item => item.id) : []
      this.cpq_form.departmentIds = departmentIds && departmentIds.length ? departmentIds.map(item => item.id) : []
      this.cpq_form.tagIds = tagIds && tagIds.length ? tagIds.map(item => item.id) : []
    },
  },
  mounted() {
    this.id = this.$route.query.id
    if (this.$route.query.marketingEventId) {
      this.marketingEventObj = { id: this.$route.query.marketingEventId }
      this.marketingDisabled = true
    }
    this.queryAddfanQrCode()
  },
  methods: {
    hanldeClearMedia() {
      if (this.mediaType === 'picture') {
        this.cpq_form = {
          ...this.cpq_form,
          welcomeImagePath: null,
          welcomeImageTitle: null,
        }
      } else if (this.mediaType === 'content') {
        this.cpq_form = {
          ...this.cpq_form,
          welcomeMiniprogramTitle: null,
          welcomeMiniprogramMediaPath: null,
          welcomeMinigramPage: null,
        }
      } else {
        this.cpq_form = {
          ...this.cpq_form,
          welcomeLinkTitle: null,
          welcomeLinkUrl: null,
          welcomeLinkDesc: null,
          welcomeLinkImagePath: null,
        }
      }
      this.mediaType = ''
    },
    handleChooseMaterial(type) {
      this.mediaType = type
      if (type === 'picture') {
        // this.metarialTitle = "请选择海报";
        // this.metarialTabs = [24];
        // this.metarialDialogVisible = true;
        this.isShowCutterDialog = true
      } else if (type === 'content') {
        this.metarialTitle = $t('marketing.commons.qxznr_db30d5')
        this.metarialTabs = [10, 1, 4, 3]
        this.metarialDialogVisible = true
      } else {
        this.linkVisible = true
      }
    },
    handleLinkFormConfirm() {
      this.$refs.linkForm.validate(valid => {
        if (valid) {
          this.cpq_form = {
            ...this.cpq_form,
            ...this.linkForm,
          }
          this.linkVisible = false
        }
      })
    },
    handleMaterialConfirm(data) {
      this.selectMaterial = data
      console.log('==meatrial:', data)
      // 海报素材赋值
      if (this.mediaType === 'poster') {
        this.cpq_form = {
          ...this.cpq_form,
          welcomeImagePath: data.aPath,
          welcomeImageTitle: data.title || data.name,
          welcomeImageSize: data.size,
        }
      } else {
        // 内容素材赋值
        this.cpq_form = {
          ...this.cpq_form,
          welcomeMiniprogramTitle: data.title || data.name,
          welcomeMiniprogramMediaPath: data.aPath || data.coverAPath,
          welcomeImageSize: data.size,
          welcomeMinigramPage:
            (data.mpUrl && data.mpUrl.replace(/!!marketingActivityId!!/g, '')) || null,
        }
      }
    },
    insertCustomerNameName() {
      this.$refs.editor.insertText($t('marketing.commons.khmc_83b0d2'), 'link', '', {
        'customer-name': '!!customerName!!',
        href: '',
      })
    },
    handleEditorCountChange(count) {
      this.replyCount = count
    },
    queryAddfanQrCode() {
      if (this.id) {
        http.queryAddfanQrCode({ id: this.id }).then(res => {
          if (res === false) {
            FxUI.Message.error($t('marketing.commons.xtcwqshzs_c236af'))
            return
          }
          if (res.errCode) {
            FxUI.Message.error(res.errMsg || $t('marketing.commons.qqsbqshzs_d5dd7d'))
            return
          }
          const data = res.data || {}
          let mediaType
          if (data.welcomeImagePath) {
            mediaType = 'picture'
          } else if (data.welcomeMiniprogramTitle) {
            mediaType = 'content'
          } else if (data.welcomeLinkTitle) {
            mediaType = 'link'
          }
          this.mediaType = mediaType
          this.cpq_form = {
            ...this.cpq_form,
            ...data,
            attachContents: data.qywxAttachmentsVO ? formatMediaToAttach(data.qywxAttachmentsVO) : [],
          }
          this.staffFiltersValue = {
            ...this.staffFiltersValue,
            colleague: res.data.employee
              ? res.data.employee.map(item => ({
                id: item.employeeUserId,
                name: item.employeeName,
              }))
              : [],
            departmentIds: res.data.departmentResults
              ? res.data.departmentResults.map(item => ({
                id: item.id,
                name: item.name,
              }))
              : [],
            tagIds: res.data.employeeTagResults
              ? res.data.employeeTagResults.map(item => ({
                id: item.tagid,
                name: item.tagname,
              }))
              : [],
          }
          console.log('newVal 12', this.staffFiltersValue)
          this.marketingEventObj = { id: res.data.marketingEventId }
          this.marketingDisabled = !!res.data.marketingEventId
        })
      }
    },
    // 切换类型时，需要将使用人员清空
    handleTypeChange() {
      this.staffFiltersValue = {
        type: 1,
        colleague: [],
        departmentIds: [],
        tagIds: [],
      }
    },
    handleSubmit() {
      const { attachContents, ...rest } = this.cpq_form

      let params = {
        ...rest,
        qywxAttachmentsVO: formatAttachToMedia(attachContents),
        materialInfos: filterMaterialMaps(attachContents),
      }
      if (this.id) {
        params.id = this.id
      }

      const { groupId } = this.$route.query
      if (groupId) {
        params.groupId = groupId
      }
      params.marketingEventId = this.marketingEventObj.id
      try {
        let strParams = JSON.stringify(params)
        strParams = strParams.replace(/!!marketingActivityId!!/g, '').replace(/!!wxAppId!!/g, '').replace(/!!marketingEventId!!/g, params.marketingEventId || '')
        params = JSON.parse(strParams)
      } catch (error) {}
      return new Promise(resolve => {
        this.$refs.form.validate(valid => {
          if (valid) {
            http
              .createOrUpdatefanQrCode(params)
              .then(res => {
                if (res.errCode === 0) {
                  resolve(true)
                } else {
                  FxUI.MessageBox.alert(
                    `<p style="font-size: 14px; color: #545861;">${$t('marketing.pages.qywx_manage.cjqwyghmsb_514293')}
                    <a style="color: #0C6CFF;" target="_blank" href="https://help.fxiaoke.com/93d5/9188/7bd2/5f49#header-3">${$t('marketing.commons.sbyy_84dl24')}</a></p>`,
                    $t('marketing.commons.ts_02d981'),
                    {
                      dangerouslyUseHTMLString: true,
                    },
                  )
                  resolve(false)
                }
              })
              .catch(() => {
                resolve(false)
              })
          } else {
            resolve(false)
          }
        })
      })
    },
    validateUserId(rule, value, callback) {
      let errText = ''
      if (
        this.cpq_form.userId.length === 0
        && this.cpq_form.departmentIds.length === 0
        && this.cpq_form.tagIds.length === 0
      ) {
        errText = $t('marketing.pages.qywx_manage.syrybnwk_13e524')
      }
      callback(errText ? new Error(errText) : '')
    },
    handlePictureSelectorSubmit(file) {
      console.log(file)
      this.cpq_form = {
        ...this.cpq_form,
        welcomeImagePath: file.photoPath,
        welcomeImageTitle: file.photoName,
        welcomeImageSize: file.photoSize,
      }
    },
    handleSpreadChannelChange(value) {
      this.cpq_form.channelValue = value
    },
  },
}
</script>

<style lang="less" scoped>
.create-promote-qrcode-form {
  .form__wrapper {
    padding: 30px 60px;

    /deep/ .el-form-item__label{
      padding-right: 16px;
    }
    /deep/ .el-form-item__label-wrap {
      margin-left: 0 !important;
    }
    .formitem_input {
      max-width: none;
      width: 100%;
    }
    .formitem_input--mb0 {
      margin-bottom: 0;
    }
    .tag {
      max-width: none;
    }

    .option {
      display: flex;
      flex-flow: row wrap;
      align-items: center;
      margin-bottom: 10px;
      padding: 4px 10px 0;
      max-width: 700px;
      min-height: 34px;
      line-height: initial;
      font-size: 12px;
      color: @color-link;
      border-radius: 3px;
      border: 1px solid @border-color-base;
      box-sizing: border-box;
    }
    .option:last-child {
      margin-bottom: 0;
    }
    .item {
      margin-right: 4px;
      margin-bottom: 4px;
      padding: 4px 6px;
      font-size: 12px;
      color: #212b36;
      background: #e3eafa;
      border-radius: 2px;
      & > span {
        color: #999;
        cursor: pointer;
      }
    }
    .button {
      flex: 0 0 auto;
      margin-bottom: 4px;
      cursor: pointer;
      > i {
        font-weight: bold;
        margin-right: 3px;
        font-size: 13px;
      }
    }
    .form__item--tips {
      margin-left: 10px;
    }
    .reply {
      max-width: 700px;
      height: 160px;
      border: 1px solid @border-color-base;
      border-radius: 3px;
      display: flex;
      flex-direction: column;
      .reply-text {
        border: 0;
        flex: 1;
        :global {
          .el-textarea__inner {
            border: 0;
            height: 100% !important;
          }
        }
      }
      .reply-media {
        height: 36px;
        display: flex;
        align-items: center;
        border-top: 1px solid @border-color-base;
        position: relative;
        &-clear {
          position: absolute;
          right: 10px;
          top: 50%;
          transform: translate(0, -50%);
          font-size: 16px;
          cursor: pointer;
          color: #999;
          &:hover {
            color: @color-title;
          }
        }
        .reply-media-preview {
          flex: 1;
          color: @color-title;
          padding: 0 10px;
          > img {
            height: 45px;
          }
          a {
            color: @color-title;
          }
        }
        .reply-placeholder {
          flex: 1;
          color: @color-link;
          padding: 0 10px;
          cursor: pointer;
          &-icon {
            font-weight: bold;
            margin-right: 3px;
          }
        }
      }
    }
    .editor {
      .ql-editor {
        min-height: auto;
        height: 140px;
        &.ql-blank::before {
          color: #c1c5ce;
        }
      }
    }
    .remark-content-wrapper{
      margin-top: 8px;
    }
    .editor-toolbar {
      height: 20px;
      line-height: 15px;
      .tool-item {
        display: inline-block;
        margin-left: 15px;
        color: var(--color-primary06, #407fff);
        font-size: 12px;
        cursor: pointer;
        .tool-icon {
          width: 12px;
          height: 12px;
          display: inline-block;
          vertical-align: -1px;
        }
      }
    }
  }
}
.reply__popover {
  .reply-media-list {
    display: flex;
    &-type {
      flex: 1;
      display: flex;
      flex-direction: column;
      width: 50px;
      height: 50px;
      align-items: center;
      justify-content: center;
      border: 1px solid @border-color-base;
      border-radius: 3px;
      margin: 7px 10px;
      cursor: pointer;
      &:hover {
        border-color: @color-link;
      }
      > i {
        font-size: 16px;
      }
      > p {
        font-size: 12px;
        margin-top: 3px;
      }
    }
  }
}
</style>
