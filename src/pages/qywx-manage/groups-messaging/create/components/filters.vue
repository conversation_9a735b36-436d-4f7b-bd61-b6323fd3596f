<template>
  <div :class="[isFlex ? $style.flex : '', $style.filters_wrapper]">
    <el-select
      v-if="list.concat(exlist).length > 1"
      v-model="select"
      :class="['el-select', $style.select, isFlex ? '' : $style.top_select]"
      :size="size"
    >
      <el-option
        v-for="item in list.concat(exlist)"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </el-select>
    <div
      v-if="select === 1"
      :class="$style.box"
    >
      <div :class="$style.option">
        <div
          v-for="(label, index) in selectFiltersLabels"
          :key="index"
          :class="$style.item"
        >
          {{ label }}
          <span @click="handleDeleteFilters(index)">&times;</span>
        </div>
        <div
          :class="$style.button"
          @click="filterDialog = true"
        >
          + {{ $t('marketing.commons.xzsxtj_e4f179') }}
        </div>
      </div>
    </div>
    <div
      v-else-if="select === 2"
      :class="$style.box"
    >
      <div :class="$style.option">
        <div
          v-for="item in selectCrowd"
          :key="item.id"
          :class="$style.item"
        >
          {{ item.name }}
          <span
            v-if="!disabled"
            @click="handleDeleteCrowd(item)"
          >&times;</span>
        </div>
        <div
          v-if="!disabled"
          :class="$style.button"
          @click="handleSelectCrowd"
        >
          + {{ $t('marketing.commons.xzmbrq_07d9ba') }}
        </div>
      </div>
    </div>
    <div
      v-else-if="select === 3 || list[0].value === 3"
      :class="$style.box"
    >
      <div :class="$style.option">
        <tag-selector-line
          v-model="selectTags"
          :add-text="$t('marketing.commons.xzbq_f9b05d')"
          :single-line="true"
          v-bind="$attrs"
          @input="handleSubmit"
        />
      </div>
      <div
        v-if="isShowLabelTip"
        :class="$style.tips"
        style="margin-left:10px;"
      >
        <QuestionTooltip>
          <div
            slot="question-content"
            class="sum-question-content"
          >
            {{ $t('marketing.pages.qywx_manage.zcdxwhgxbh_9984a1') }}
          </div>
        </QuestionTooltip>
      </div>
    </div>
    <div
      v-else-if="select === 4"
      :class="$style.box"
    >
      <div :class="$style.option">
        <div
          v-for="item in selectGroupOwner"
          :key="item.id"
          :class="$style.item"
        >
          {{ item.name }}
          <span @click="handleDeleteGroupOwner(item)">&times;</span>
        </div>
        <div
          :class="$style.button"
          @click="handleSelectGroupOwner"
        >
          + {{ $t('marketing.commons.xzqz_b51e8b') }}
        </div>
      </div>
    </div>
    <!-- 0：全部企业微信客户 -->
    <template v-else>
      <div
        v-for="item in exlist"
        :key="item.value"
        :class="$style.box"
      >
        <slot
          v-if="item.value === select"
          :name="item.value"
        />
      </div>
    </template>
    <v-dialog
      :title="$t('marketing.commons.atjxz_99745e')"
      width="800px"
      :z-index="900"
      :visible="filterDialog"
      @onClose="filterDialog = false"
      @onSubmit="handleSelectFilters"
    >
      <ObjectFilter
        v-show="filterDialog"
        ref="crmFilter"
        :object-lists="filterObjectList"
        :filter-data="selectFilters"
        :filter-fields="filterFields"
        :initial="true"
      />
    </v-dialog>

    <fx-selector-box-v2
      v-if="groupOwnerSelectorReady"
      :title="$t('marketing.commons.xzqz_b51e8b')"
      :show.sync="groupOwnerSelectorVisible"
      v-bind="groupOwnerSelectorOpt"
      @confirm="handleGroupOwnerSelectorConfirm"
    />
  </div>
</template>

<script>
import _ from 'lodash'
import http from '@/services/http/index.js'
import VDialog from '@/components/dialog/index.vue'
// import CrmFilter from '@/components/crm-filter';
import ObjectFilter from '@/components/object-filter/index.vue'
import CrowdSelector from '@/utils/crowd-selector.js'
// import GroupOwnerSelector from '@/utils/group-owner-selector.js'
import TagSelectorLine from '@/components/tags-selector-new/tags-line.vue'
import { getFilterPreviewData } from '@/utils/filter-helper.js'
import QuestionTooltip from '@/components/questionTooltip/index.vue'

export default {
  components: {
    VDialog,
    // CrmFilter,
    ObjectFilter,
    ElSelect: FxUI.Select.components.ElSelect,
    ElOption: FxUI.Select.components.ElSelect.components.ElOption,
    TagSelectorLine,
    QuestionTooltip,
  },
  props: {
    value: {
      type: Object,
      default: () => ({}),
    },
    size: {
      type: String,
      default: '',
    },
    // appId为公众号ID，用于获取标签和条件筛选相关信息
    appId: {
      type: String,
      default: 'FSAID_10b0778b',
    },
    // 默认筛选列表
    list: {
      type: Array,
      default: () => [
        { label: $t('marketing.commons.ambrqxz_ace093'), value: 2 },
        { label: $t('marketing.commons.abqsx_6d0ce3'), value: 3 },
        { label: $t('marketing.commons.atjsx_7357aa'), value: 1 },
        { label: $t('marketing.commons.aqzxzkhq_fb6cd4'), value: 4 },
        { label: $t('marketing.commons.qbqywxkh_3cfdc8'), value: 0 },
      ],
    },
    // 筛选列表扩展，通过 slot 插入选择框
    exlist: {
      type: Array,
      default: () => [],
    },
    // 单排或双排布局
    isFlex: {
      type: Boolean,
      default: true,
    },
    /**
     * 是否展示标签问好提示
     */
    isShowLabelTip: {
      type: Boolean,
      default: false,
    },
    /**
     * 是否禁止编辑
     */
    disabled: {
      type: Boolean,
      default: false,
    },
    // 子活动自动创建的目标人群会在列表中过滤 所以只能单独根据id去查询详情
    isSingleDetail: {
      type: Boolean,
      default: false,
    },
    // 筛选条件的对象
    filterObjectList: {
      type: Array,
      default: () => ([{
        name: $t('marketing.commons.qywxkh_000b5c'), value: 'WechatWorkExternalUserObj',
      }]),
    },
    // 过滤字段
    filterFields: {
      type: Array | Function,
      default: () => [],
    },
  },
  data() {
    const { crowd, sendRange, tagIdList = [] } = this.value || {}
    return {
      select: sendRange || 0,
      selectTags: tagIdList || [],
      selectFilters: [],
      selectFiltersLabels: [],
      selectCrowd: crowd || [],
      selectGroupOwner: [],
      filterDialog: false,
      groupOwnerSelectorReady: false,
      groupOwnerList: [],
      groupOwnerSelectorVisible: false,
    }
  },
  computed: {
    groupOwnerSelectorOpt() {
      const opts = {
        tabs: [
          {
            id: 'list',
            type: 'list',
            data: [...this.groupOwnerList],
          },
        ],
        defaultSelectedItems: { list: this.selectGroupOwner.map(el => el.id) },
      }

      return FS.selectorParseContactV2.parseContacts(opts)
    },
  },
  watch: {
    select(newVal) {
      this.handleSelectChange()
    },
    value() {
      console.log(this.disabled, 'cfrq_c42af3')
    },
    selectTags() {
      this.handleSelectChange()
    },
    /**
     * groupOwnerSelectorOpt变化时候，fx-selector-box-v2内部状态不会同步
     * 这里强制重新渲染一下
     */
    groupOwnerSelectorOpt() {
      if (this.groupOwnerSelectorVisible) {
        return
      }
      this.groupOwnerSelectorReady = false
      setTimeout(() => {
        this.groupOwnerSelectorReady = true
      }, 10)
    },
  },
  async mounted() {
    const {
      crowd, ownerList, filters,
    } = this.value

    this.groupOwnerListPromise = http.queryGroupOwnerList()
    this.groupOwnerListPromise.then(({ errCode, data = [] }) => {
      if (errCode === 0) {
        this.groupOwnerList = (data || []).map(item => ({
          id: item.groupOwnerUserId,
          name: item.groupOwnerUserName,
        }))
      }

      setTimeout(() => {
        this.groupOwnerSelectorReady = true
      }, 10)
    })

    if (this.select === 2 && crowd && crowd.length) {
      if (!this.isSingleDetail) {
        http.queryListAllMarketingUserGroup().then(({ errCode, data = [] }) => {
          if (errCode === 0) {
            this.selectCrowd = crowd.reduce((arr, id) => {
              const matched = data.filter(item => item.id === id)
              if (matched.length) {
                arr.push(matched[0])
              }
              return arr
            }, [])
            this.handleSelectChange()
          }
        })
      } else {
        http.queryMarketingUserGroupDeatil({ id: crowd[0] }).then(res => {
          if (res.errCode === 0) {
            this.selectCrowd = [res.data]
          }
        })
      }
    } else if (this.select === 4 && ownerList && ownerList.length) {
      this.groupOwnerListPromise.then(({ errCode, data = [] }) => {
        if (errCode === 0) {
          const defaultIdMap = ownerList.reduce((map, id) => {
            map[id] = id
            return map
          }, {})
          this.selectGroupOwner = data.reduce((arr, item) => {
            if (defaultIdMap[item.groupOwnerUserId]) {
              arr.push({
                id: item.groupOwnerUserId,
                name: item.groupOwnerUserName,
              })
            }
            return arr
          }, [])
        }
      })
    } else if (this.select === 1 && filters && filters.length) {
      this.selectFilters = [
        {
          objectAPIName: this.filterObjectList[0]?.value,
          query: {
            filters,
          },
        },
      ]
      const previewData = await getFilterPreviewData(this.selectFilters[0])
      this.selectFiltersLabels = this.handleParseFilterData([previewData])
    }
    // this.queryGroupOwnerList();
  },
  methods: {
    handleSubmit() {},

    async handleSelectFilters() {
      const filter = this.$refs.crmFilter
      if (!filter.validate()) return
      this.selectFilters = filter.getValue()
      console.log('selectFilters: ', this.selectFilters)
      const previewData = await filter.getPreviewValue()
      console.log('previewData', previewData)
      this.selectFiltersLabels = this.handleParseFilterData(previewData)
      this.handleSelectChange()
      this.filterDialog = false
    },
    handleParseFilterData(data = []) {
      const head = data[0] || {}
      return _.map(head.filter, item => `${head.displayApiName}.${item.join(' ')}`)
    },
    handleDeleteFilters(index) {
      this.selectFilters[0].query.filters.splice(index, 1)
      this.selectFiltersLabels.splice(index, 1)
      this.handleSelectChange()
    },
    handleSelectCrowd() {
      // eslint-disable-next-line no-new
      new CrowdSelector({
        defaultSelectedItems: {
          crowd: _.map(this.selectCrowd, item => item.id),
        },
        onSubmit: data => {
          this.selectCrowd = data.crowd
          this.handleSelectChange()
        },
      })
    },
    handleDeleteCrowd(crowd) {
      this.selectCrowd = _.filter(
        this.selectCrowd,
        item => item.id !== crowd.id,
      )
      this.handleSelectChange()
    },
    handleSelectGroupOwner() {
      // eslint-disable-next-line no-new
      // new GroupOwnerSelector({
      //   defaultSelectedItems: {
      //     groupOwner: _.map(this.selectGroupOwner, item => item.id),
      //   },
      //   onSubmit: data => {
      //     this.selectGroupOwner = data.groupOwner
      //     this.handleSelectChange()
      //   },
      // })
      this.groupOwnerSelectorVisible = true
    },
    handleDeleteGroupOwner(groupOwner) {
      this.selectGroupOwner = _.filter(
        this.selectGroupOwner,
        item => item.id !== groupOwner.id,
      )
      this.handleSelectChange()
    },
    handleSelectChange() {
      const { query } = this.selectFilters[0] || {}
      const tagIdList = _.map(this.selectTags, item => ({
        firstTagName: item.firstTagName,
        secondTagName: item.secondTagName,
      }))

      const filters = (query && query.filters) || []
      filters.__label = this.selectFiltersLabels

      const ownerList = this.selectGroupOwner.map(item => item.id) || []
      ownerList.__label = this.selectGroupOwner.map(item => item.name) || []

      this.$emit('input', {
        tagIdList: this.select === 3 ? tagIdList : [],
        sendRange: this.select,
        filters: this.select === 1 ? filters : [],
        crowd: this.select === 2 ? (this.selectCrowd.slice() || []) : [],
        ownerList: this.select === 4 ? ownerList : [],
      })
      this.$emit('change', this.select)
    },
    queryGroupOwnerList() {
      console.log('queryGroupOwnerList')
      http.queryGroupOwnerList().then(({ errCode, data = [] }) => {
        // console.log('queryGroupOwnerList', res);
        if (errCode === 0) { /* empty */ }
      })
    },
    handleGroupOwnerSelectorConfirm(e) {
      const selectGroupOwner = (e.list || []).map(id => {
        const item = this.groupOwnerList.find(el => el.id === id)
        return item
      })

      this.selectGroupOwner = selectGroupOwner
      this.handleSelectChange()
    },
  },
}
</script>

<style lang="less" module>
.filters_wrapper {
  flex-wrap: nowrap;
  align-items: center;
  flex: 1 1 auto;
  .tips {
    margin-right: 6px;
    color: #ff7663;
  }
  .select {
    flex: 0 0 auto;
    align-self: flex-start;
    margin-right: 10px;
    width: 160px;
  }
  .box {
    display: flex;
    align-items: baseline;
    flex: 1 1 auto;
    :global {
      .tags-line {
        margin-top: -7px;
      }
    }
  }
  .option {
    display: flex;
    flex-flow: row wrap;
    align-items: center;
    margin-bottom: 10px;
    padding: 4px 10px 0;
    width: 100%;
    min-height: 34px;
    line-height: initial;
    font-size: 12px;
    color: var(--color-info06, #407fff);
    border-radius: 3px;
    border: 1px solid @border-color-base;
    box-sizing: border-box;
  }
  .option:last-child {
    margin-bottom: 0;
  }
  .item {
    margin-right: 4px;
    margin-bottom: 4px;
    padding: 4px 6px;
    font-size: 12px;
    color: #212b36;
    background: #e3eafa;
    border-radius: 2px;
    & > span {
      color: #999;
      cursor: pointer;
    }
  }
  .button {
    flex: 0 0 auto;
    margin-bottom: 4px;
    cursor: pointer;
  }
}
.flex {
  display: flex;
}
.top_select {
  width: 100% !important;
  margin-bottom: 10px;
}
</style>
