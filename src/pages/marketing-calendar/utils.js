import http from '@/services/http/index.js'

/**
 * 跳转市场活动概览页详情
 */

let isEventTypeChecking = false
export async function jumpToMarketingEventDetailPage({
  marketingEventId, // 市场活动ID
  newlyCreated, // 是否是新创建，如果是新创建则会显示引导页
  eventType, // 市场活动类型，参数为空时从接口获取
  eventForm, // 市场活动形式，参数为空时从接口获取
  $router, // vue router 实例
  target = '_self', // 链接跳转方式 _blank：新窗口打开 _self：当前窗口打开
}) {
  if (!marketingEventId) return

  let realEventType = eventForm
  if (isEventTypeChecking) return
  // 如果未传入市场活动类型则通过接口获取
  if (!realEventType) {
    isEventTypeChecking = true
    const res = await http.queryMarketingEventObjDetail({
      objectDataId: marketingEventId,
    })
    isEventTypeChecking = false
    const marketingEventData = res.data || {}
    realEventType = marketingEventData.event_form || marketingEventData.eventForm // 市场活动形式
  }

  let routeData = null

  // 如果是会议类型，检查是否绑定营销通会议活动
  if (realEventType === 'conference_marketing') {
    isEventTypeChecking = true
    const { errCode, data } = await http.checkConferenceStatus({
      marketingEventId,
    })
    isEventTypeChecking = false
    if (errCode === 0 && data.bindConference) {
      routeData = {
        name: 'meeting-detail',
        params: { id: data.bindConferenceId, source: 'calendar' },
      }
    }
  } else if (realEventType === 'live_marketing') {
    // 直播营销概览页面
    routeData = {
      name: 'live-dashboard',
      params: { id: marketingEventId },
    }
  } else if (realEventType === 'multivenue_marketing') {
    // 多组合活动概览页面
    routeData = {
      name: 'content-dashboard',
      query: {
        formType: 6,
      },
      params: { id: marketingEventId, from: 'marketing-calendar' },
    }
  } else if (realEventType === 'online_marketing') {
    // 线上营销概览页面
    routeData = {
      name: 'content-dashboard',
      query: {
        formType: 0,
      },
      params: { id: marketingEventId, from: 'marketing-calendar' },
    }
  } else {
    // 活动营销概览页面
    routeData = {
      name: 'content-dashboard',
      params: { id: marketingEventId, from: 'marketing-calendar' },
    }
  }

  if (!routeData) return

  if (target === '_blank') {
    const route = $router.resolve(routeData)
    window.open(route.href, '_blank')
  } else {
    $router.push(routeData)
  }

  // 默认都直接跳转市场活动详情
  // $router.push({
  //   name: "marketing-calendar-dashboard",
  //   params: {
  //     id: marketingEventId
  //   },
  //   ...((newlyCreated && {
  //     query: {
  //       new: "true"
  //     }
  //   }) ||
  //     {})
  // });
}
