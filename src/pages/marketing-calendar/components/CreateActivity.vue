<template>
    <div :class="$style.create_activity">
      <div :class="$style.title">{{ $t('marketing.commons.kscj_433d87') }}</div>
      <div :class="$style.activity_list">
        <div
          v-for="item in activityTypes"
          :key="item.type"
          :class="$style.activity_item"
        >
          <img :src="item.icon" :class="$style.icon" />
          <div :class="$style.content">
            <div :class="$style.name">{{ item.name }}</div>
            <div :class="$style.desc">{{ item.desc }}</div>
            <fx-button size="small" type="text" @click.stop="handleCreate(item)" :class="$style.create_btn">
              +{{ $t('marketing.commons.ljcj_9fd000') }}
            </fx-button>
          </div>
        </div>
      </div>
    </div>
</template>

<script>
import { requireAsync } from '@/utils/index.js'
import http from '@/services/http/index.js'
export default {
  name: "CreateActivity",
  data() {
    return {
     activityTypes: [
        {
          type: "content",
          name: $t('marketing.pages.marketing_calendar.xsyx_6c9e91'),
          desc: $t('marketing.pages.marketing_calendar.shyxzdyyhh_d60bd6'),
          icon: require("@/assets/images/content-marketing/content-market.png")
        },
        {
          type: "live",
          name: $t('marketing.commons.zbyx_a9fa5d'),
          desc: $t('marketing.pages.marketing_calendar.gkkzbzxshh_b7d00c'),
          icon: require("@/assets/images/content-marketing/live-market.png")
        },
        {
          type: "meeting",
          name: $t('marketing.commons.hyyx_5f60fd'),
          desc: $t('marketing.pages.marketing_calendar.xxhddyyqdh_be50e3'),
          icon: require("@/assets/images/content-marketing/meeting-market.png")
        },
        {
          type: "multivenue",
          name: $t('marketing.pages.marketing_calendar.dhdzh_0bf57e'),
          desc: $t('marketing.pages.marketing_calendar.glhdjqfhch_2e300d'),
          icon: require("@/assets/images/content-marketing/multivenue-market.png")
        }
      ],
      activityTypeOptions: []
    };
  },
  methods: {
    handleCreate(item) {
      switch (item.type) {
        case "content":
          this.addContentMarketing(0);
          break;
        case "meeting":
          this.$router.push({ name: "meeting-marketing-create" });
          break;
        case "live":
          this.$router.push({
            name: "live-create"
          });
          break;
        case "multivenue":
          this.addContentMarketing(6);
          break;
      }
    },
    async addContentMarketing(type) {
      console.log('type', type)
      const that = this;
      const obj = await this.getCRMDescribeLayout();
      const event_form = type === 0 ? 'online_marketing' : 'multivenue_marketing'
      const activityTypeOptions = await this.getMarketingEventCommonSetting(type)
      // 新建营销内容
      requireAsync("crm-modules/action/field/field", field => {
        CRM.api.add({
          apiname: "MarketingEventObj",
          Model: field.Model.extend({
            parse(res) {
              const _options = res.objectDescribe.fields.event_type.options;
              const submitOptions = [];
              _options.forEach(item => {
                activityTypeOptions.forEach(i => {
                  if (item.value === i.apiName) {
                    submitOptions.push(item)
                  }
                })
              })
              res.objectDescribe.fields.event_type.options = submitOptions
              // 因为目前市场活动新建event_form字段没支持到禁用能力  所以只展示当前活动类型 其他的都隐藏 
              res.objectDescribe.fields.event_form.options = res.objectDescribe.fields.event_form.options.filter(item =>item.value === event_form)
             return field.Model.prototype.parse.apply(this, arguments)
            }
          }),
          title: $t("marketing.commons.xj_8b0ecd", {
            data: { option0: obj.display_name }
          }),
          show_type: "full",
          nonEditable: true,
          data: {
            event_type: activityTypeOptions[0].apiName,
            event_form: event_form,
          },
          success: (_type, data) => {
            that.$router.push({ name: 'content-dashboard', params: { id: data._id }, query: {formType: type} })
          },
          renderComplete: (comp, model) => {}
        });
      });
    },
    getCRMDescribeLayout() {
      return new Promise(resolve => {
        FS.util.FHHApi({
          url:
            "/EM1HNCRM/API/v1/object/MarketingEventObj/controller/DescribeLayout",
          data: {
            apiname: "MarketingEventObj",
            include_detail_describe: true,
            include_layout: true,
            layout_type: "add",
            recordType_apiName: "default__c"
          },
          success: res => {
            if (!res.Value || res.Value.errCode) return;
            const obj = res.Value.objectDescribe || {};
            resolve(obj);
          }
        });
      });
    },
    async getMarketingEventCommonSetting(type) {
      const res = await http.getMarketingEventCommonSetting({ type: Number(type) })
      if (res && res.errCode == 0) {
        const contentMarketingMapping = res.data.activityTypeMapping.filter(item => {
          return item.activityType === type
        })
        return contentMarketingMapping[0].mapping
      }
      return []
    },
  },
};
</script>

<style lang="less" module>
.create_activity {
  background: #fff;
  padding: 12px;
  border-radius: 8px;

  .title {
    color: var(--color-neutrals19);
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    margin-bottom: 12px;
  }

  .activity_list {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
  }

  .activity_item {
    padding: 10px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
    position: relative;
    display: flex;
    gap: 10px;
    .icon {
      width: 80px;
      height: 80px;
    }

    .content {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: flex-start;
      .name {
        font-size: 14px;
        font-weight: 700;
        color: #181C25;
        margin-bottom: 4px;
      }

      .desc {
        font-size: 12px;
        color: #91959E;
        line-height: 18px;
        margin-bottom: 4px;
      }
      .create_btn {
        color: var(--color-info06);
        line-height: 18px;
        font-size: 12px;
        padding: 0;
      }
    }

  }
}
</style>
