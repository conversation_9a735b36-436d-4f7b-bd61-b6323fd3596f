<template>
  <v-dialog
    :title="$t('marketing.commons.xjschd_80899e')"
    :class="$style.chooseActivityDialog"
    append-to-body
    :okText="$t('marketing.commons.xyb_38ce27')"
    :visible="dialogVisible"
    @onClose="handleClose"
    @onSubmit="handleSubmit"
  >
    <div :class="$style.content">
      <div :class="$style.inner">
        <div
          :class="[$style.item,item.checked ? $style.checked:'',item.disable ? $style.disabled:'']"
          v-for="(item,index) in items"
          :key="index"
          @click="handleChoose(index)"
        >
          <span :class="[$style.radio, 'iconfont', item.checked ? 'iconchenggong_mian':'']" v-if="!item.disable"></span>
          <span :class="[$style.radio,$style.tips]" v-if="item.disable">{{ $t('marketing.commons.wkt_686e8a') }}</span>
          <img :class="$style.icon" :src="item.disable ? item.disableIcon:item.icon">
          <div :class="$style.title">{{item.title}}</div>
          <div :class="$style.desc">{{item.desc}}</div>
        </div>
      </div>
    </div>
  </v-dialog>
</template>

<script>

import VDialog from '@/components/dialog/index.vue';
export default {
  components: {
VDialog,
ElRadio: FxUI.Radio,
ElRadioGroup: FxUI.RadioGroup
},
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    items: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    visible(bool) {
      this.dialogVisible = bool;
    },
    activity() {
      this.validate = true;
    },
  },
  data() {
    return {
      activity: {},
      validate: true,
      dialogVisible: this.visible,
      waiting: false,
    };
  },
  methods: {
    handleClose() {
      this.$emit('onClose');
    },
    handleChoose(index) {
      if (this.items[index].disable) {
        return;
      }
      this.items.forEach((item, i) => {
        item.checked = i === index;
      });
    },
    handleSubmit() {
      const [{ value }] = this.items.filter((item) => item.checked);
      this.$emit('onSubmit', value);
    },
  },
};
</script>

<style lang="less" module>
.chooseActivityDialog {
  .content {
    padding: 10px 0;
    > .title {
      margin-right: 16px;
      font-size: 13px;
      > span {
        margin-right: 7px;
        color: #f56c6c;
      }
    }
    > .tips {
      font-size: 12px;
      color: #999999;
      margin-top: 5px;
    }
    .radio_group {
      display: none;
      margin-left: 16px;
    }
    .chooseActivity {
      margin: 10px 10px 0 10px;
      box-sizing: border-box;
      line-height: 0;
      &.error {
        border: 1px solid #f56c6c;
        :global {
          div[name='pick_object_wrapper'] {
            border: 0;
          }
        }
      }
    }
    .inner {
      display: flex;
      display: -ms-flex;
      justify-content: center;
      gap: 10px;
      .item {
        flex: 1;
        text-align: center;
        border: 1px solid #DEE1E8;
        border-radius: 8px;
        padding: 10px 10px 20px 10px;
        width: 200px;
        box-sizing: border-box;
        cursor: pointer;
        position: relative;
        transition: all 0.3s ease;
        &:hover {
          box-shadow: 0 0 16px 0 rgba(0, 0, 0, 0.1);
        }
        &:last-child {
          margin-right: 0;
        }
        .radio {
          width: 20px;
          height: 20px;
          border: 1px solid #dddddd;
          border-radius: 100%;
          display: inline-block;
          position: absolute;
          top: 11px;
          right: 11px;
          box-sizing: border-box;
          color: var(--color-primary06, #407fff);
          font-size: 20px;
          line-height: 20px;
          &::before {
            position: absolute;
            top: 0;
            left: 0;
          }
          &.tips {
            width: auto;
            border: 0;
            font-size: 12px;
          }
        }
        &.checked {
          border: 1px solid var(--color-primary06,#407FFF);
          transition-delay: 0.1s;
          .radio {
            border: 0;
            &::after {
              transform: scale(1, 1);
            }
          }
        }
        &.disabled {
          background: #fafafa;
          .title {
            color: #666;
          }
          .desc {
            color: #ccc;
          }
        }
        .icon {
          width: 80px;
          height: 80px;
        }
        .title {
          color: var(--Text-H1, #181C25);
          text-align: center;
          font-feature-settings: 'liga' off, 'clig' off;
          font-family: "Source Han Sans CN";
          font-size: 14px;
          font-style: normal;
          font-weight: 700;
          line-height: 20px;
          margin-bottom: 4px;
        }
        .desc {
          color: #91959E;
          text-align: center;
          font-feature-settings: 'liga' off, 'clig' off;
          font-family: "Source Han Sans CN";
          font-size: 12px;
          font-style: normal;
          font-weight: 400; 
          line-height: 18px;
        }
      }
    }
  }
}
</style>
