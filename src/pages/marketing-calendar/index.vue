<template>
  <div :class="$style.marketing_calebdar">
    <div :class="$style.marketing_calebdar_header" slot="header">
        <div :class="$style.create_activity">
          <create-activity />
        </div>
      </div>
      <div :class="$style.content_header">
          <Select
            class="el-select"
            v-model="searchTemplateId"
            size="small"
            @change="queryActivitiesByDate"
            :placeholder="$t('marketing.commons.qxz_708c9d')"
          >
            <Option
              v-for="item in calendarOptions"
              :key="item._id"
              :label="item.label"
              :value="item._id"
            ></Option>
          </Select>
          <div :class="$style.right">
            <RadioGroup v-model="calendarView">
              <Radio :label="'Calendar'">{{ $t('marketing.pages.marketing_calendar.rlst_4ce80d') }}</Radio>
              <Radio :label="'Plan'">{{ $t('marketing.pages.marketing_calendar.jhst_ee89fc') }}</Radio>
            </RadioGroup>
            <fx-link
              type="standard"
              :underline="false"
              target="_blank"
              @click="handleGoMarketingActivity"
              >{{ $t('marketing.pages.marketing_calendar.jrschdlb_abaaeb') }}
              </fx-link>
          </div>
      </div>
      <div :class="$style.content">
        <div :class="$style.fullcalendar_wrap">
          <FullCalendar
            ref="fullCalendar"
            :class="[
              $style.fullcalendar,
              calendarView === 'Plan' ? $style.fullheight : ''
            ]"
            :selected="selected"
            :view="calendarView"
            :activities="activities"
            @dateChange="onDateChange"
            @activity-click="onActivityClick"
            @activity-enter="handleActivityEnter"
            @activity-leave="handleActivityLeave"
            @date-click="onDateClick"
          />
        </div>
        <div
          :class="[$style.activities, calendarView === 'Plan' ? $style.hide : '']"
        >
          <div :class="$style.activities_title">
            {{
              dates.date
                ? $t("marketing.commons.nyr_93cd14", {
                    data: {
                      option0: dates.year,
                      option1: dates.month,
                      option2: dates.date
                    }
                  }) + ' ' + dayName[dates.day]
                : $t("marketing.commons.ny_e37021", {
                    data: { option0: dates.year, option1: dates.month }
                  })
            }}
          </div>
          <div :class="$style.activities_content">
            <div :class="$style.loading" v-if="loading">
              <div v-loading="true"></div>
            </div>
            <template v-if="activities.length">
              <div
                :class="[
                  $style.list,
                  (hoverIndex === index && $style.hover) || ''
                ]"
                v-for="(item, index) in activities"
                :key="index"
                @click="handleClickEventList(item)"
              >
                <Tag
                  :class="$style.tag"
                  :type="item.tagType"
                  effect="plain"
                  size="mini"
                  v-if="item.statusLabel"
                  >{{ item.statusLabel }}</Tag
                >
                <div :class="[$style.title]">
                  <span
                    :class="$style.dot"
                    :style="`background: ${item.hoverBackground}`"
                  ></span>
                  <span :class="['km-t-ellipsis2']">{{ item.name }}</span>
                </div>
                <div :class="$style.item">
                  <div :class="$style.label">{{ $t('marketing.commons.kssj_61e84e') }}</div>
                  <div :class="$style.con">{{ item.beginTime }}</div>
                </div>
                <div :class="$style.item">
                  <div :class="$style.label">{{ $t('marketing.pages.marketing_calendar.jssj_590dbb') }}</div>
                  <div :class="$style.con">{{ item.endTime }}</div>
                </div>
                <div :class="$style.item">
                  <div :class="$style.label">{{ $t('marketing.commons.fzr_41b7c4') }}</div>
                  <div :class="$style.con">{{ item.owners || "--" }}</div>
                </div>
                <div :class="$style.item">
                  <div :class="$style.label">{{ $t('marketing.commons.hdlx_8e63fd') }}</div>
                  <div :class="$style.con">{{ item.typeLabel || "--" }}</div>
                </div>
                <!-- <div :class="$style.item">
                  <div :class="$style.label">实际成本</div>
                  <div :class="$style.con">{{item.actualCost ? '¥'+ item.actualCost: '--'}}</div>
                </div>
                <div :class="$style.item">
                  <div :class="$style.label">活动地点</div>
                  <div :class="$style.con">{{item.location || '--'}}</div>
                </div>-->
              </div>
            </template>
            <template v-else>
              <Empty
                :title="$t('marketing.pages.marketing_calendar.zwschd_ca58ce')"
                :button="$t('marketing.pages.marketing_calendar.ljxj_2044ef')"
                @onClick="handleCreateMarketingEvent"
              />
            </template>
          </div>
        </div>
      </div>
      <Choose
        v-if="visible"
        :visible="visible"
        @onSubmit="handleChooseCreateType"
        :items="marketingEventTypeLists"
        @onClose="visible = false"
      />
      <Popover :visible.sync="popoverTip" />
    </div>
</template>

<script>
import { mapState, mapActions } from "vuex";
import http from "@/services/http/index";
import FullCalendar from "@/components/marketing-calendar/components/FullCalendar.vue";
import ContentHeader from "@/components/content-header";
import Choose from "./components/choose.vue";
import Empty from "@/components/common/empty";
import Popover from "./components/popover";
import kisvData from "@/modules/kisv-data";
import { jumpToMarketingEventDetailPage } from "./utils";
import { requireAsync } from '@/utils/index';
import CreateActivity from './components/CreateActivity.vue'

const dayName = [$t('marketing.commons.zr_562d74'), $t('marketing.commons.zy_1603b0'), $t('marketing.commons.ze_b5a6a0'), $t('marketing.commons.zs_e60725'), $t('marketing.commons.zs_170fc8'), $t('marketing.commons.zw_eb79ce'), $t('marketing.commons.zl_245751')];
const marketingEventTypes = [
  {
    icon: require("@/assets/images/icons/activity-invit-icon.png"),
    title: $t('marketing.commons.xsyx_6c9e91'),
    value: "online_marketing",
    desc: $t('marketing.pages.content_marketing.jymbyhcjsj_5a1365'),
    checked: false
  },
  {
    icon: require("@/assets/images/icons/live-marketingevent-icon.png"),
    title: $t('marketing.commons.zxzb_21b908'),
    value: "live_marketing",
    desc: $t('marketing.commons.gkkzbzxslh_2f6737'),
    checked: false
  },
  {
    icon: require("@/assets/images/icons/meeting-icon.png"),
    title: $t('marketing.commons.hyyx_5f60fd'),
    value: "3",
    desc: $t('marketing.commons.xxhdrslyth_12c498'),
    checked: true
  },
  {
    icon: require("@/assets/images/icons/other-icon.png"),
    title: $t('marketing.pages.setting.dhdzh_0bf57e'),
    value: "multivenue_marketing",
    desc: $t('marketing.pages.content_marketing.xlxdxhdyzs_21bf13'),
    checked: false
  }
];

export default {
  components: {
ContentHeader,
FullCalendar,
Tag: FxUI.Tag,
Empty,
Choose,
Popover,
RadioGroup: FxUI.RadioGroup,
Radio: FxUI.Radio,
Select: FxUI.Select.components.ElSelect,
Option: FxUI.Select.components.ElSelect.components.ElOption,
ElLink: FxUI.Link,
CreateActivity
},
  data() {
    return {
      kisvData: kisvData.datas,
      popoverTip: false,
      visible: false,
      marketingEventTypes,
      loading: false,
      loadingMarketingEventDialog: false,
      selected: "",
      dayName,
      isCheckEventTyping: false,
      hoverIndex: ""
    };
  },
  mounted() {
    this.selected = this.dates.selected;
    this.queryActivitiesByDate();
    if (!!this.$route.params.visible) {
      setTimeout(() => {
        this.visible = true;
      }, 500);
    }
  },
  computed: {
    ...mapState("MarketingCalendar", {
      activities: "activities",
      dates: "dates",
      calendarOptions: "calendarOptions",
      searchTemplateType: "searchTemplateType",
      MARKETING_TYPE: "MARKETING_TYPE"
      //optionNotify: 'optionNotify',
    }),
    calendarView: {
      get() {
        return this.$store.state.MarketingCalendar.calendarView;
      },
      set(calendarView) {
        this.$store.commit("MarketingCalendar/setState", {
          calendarView
        });
      }
    },
    searchTemplateId: {
      get() {
        return this.$store.state.MarketingCalendar.searchTemplateId;
      },
      set(searchTemplateId) {
        this.$store.commit("MarketingCalendar/setState", {
          searchTemplateId
        });
      }
    },
    marketingEventTypeLists() {
      const { isPrivateCloud } = this.kisvData;
      //华为云隐藏直播新建入口
      if (isPrivateCloud) {
        return this.marketingEventTypes.filter(
          item => item.value !== "live_marketing"
        );
      }
      return this.marketingEventTypes;
    }
  },
  // watch: {
  //   calendarView(type){
  //     if(type === 'Plan' && this.optionNotify){
  //       setTimeout(() => {
  //         this.notify = this.$notify({
  //           title: '操作提示',
  //           message: '光标在计划视图区域，按住SHIFT键+鼠标滚轮，可左右滚动计划视图',
  //           position: 'bottom-right',
  //           duration: 0,
  //         });
  //       }, 1500);
  //       this.$store.commit('MarketingCalendar/setState', {
  //         optionNotify: false,
  //       })
  //     }
  //   }
  // },
  methods: {
    ...mapActions("MarketingCalendar", [
      "queryActivities",
      "setDates"
      // "checkConferenceStatus"
    ]),
    handleGoMarketingActivity() {
      window.open(this.$router.resolve({
        name: "marketing-activity"
      }).href, '_blank');
    },
    async handleClickEventList(row) {
      jumpToMarketingEventDetailPage({
        marketingEventId: row.id,
        eventType: row.eventType || row.event_type,
        eventForm: row.eventForm || row.event_form,
        newlyCreated: row.new,
        $router: this.$router
      });
      //检查是否绑定会议销售
      // if (this.isCheckEventTyping) return;
      // this.isCheckEventTyping = true;
      // const { errCode, data } = await this.checkConferenceStatus({
      //   marketingEventId: row.id
      // });
      // this.isCheckEventTyping = false;
      // if (errCode == 0 && data.bindConference) {
      //   this.$router.push({
      //     name: "meeting-detail",
      //     params: { id: data.bindConferenceId, source: "calendar" }
      //   });
      //   return;
      // }
      // if (
      //   row.eventType === "content_marketing" ||
      //   row.event_type === "content_marketing"
      // ) {
      //   this.$router.push({
      //     name: "content-dashboard",
      //     params: { id: row.id }
      //   });
      //   return;
      // } else if (
      //   row.eventType === "live_marketing" ||
      //   row.event_type === "live_marketing"
      // ) {
      //   this.$router.push({
      //     name: "live-dashboard",
      //     params: { id: row.id }
      //   });
      //   return;
      // }
      // this.$router.push({
      //   name: "marketing-calendar-dashboard",
      //   params: {
      //     id: row.id
      //   },
      //   ...((row.new && {
      //     query: {
      //       new: "true"
      //     }
      //   }) ||
      //     {})
      // });
    },
    getStartTimeToEndTime() {
      const { year, month, date } = this.dates;
      return {
        begin: new Date(year, month - 1, date || 1).getTime(),
        end:
          (date
            ? new Date(year, month - 1, date - 0 + 1)
            : new Date(year, month, 1)
          ).getTime() - 1
      };
    },
    queryActivitiesByDate() {
      const { begin, end } = this.getStartTimeToEndTime();
      this.loading = true;
      this.queryActivities({
        begin,
        end,
        searchTemplateId: this.searchTemplateId,
        searchTemplateType: this.searchTemplateType
      }).then(() => {
        this.loading = false;
        //当月没有活动时提示创建
        this.$nextTick(() => {
          if (!this.activities.length && !this.dates.date) {
            // this.popoverTip = true;
            this.popoverTip = false;
          } else {
            this.popoverTip = false;
          }
        });
      });
    },
    onDateChange({ year, month }) {
      console.log('onDateChange', year, month)
      if (this.dates.year != year || this.dates.month != month) {
        this.setDates({
          year,
          month,
          date: ""
        });
        this.$refs.fullCalendar.clearSelectDate();
        this.queryActivitiesByDate();
      }
    },
    onDateClick(event, date) {
      const dates = date.split("-");
      this.setDates({
        year: dates[0],
        month: dates[1],
        date: dates[2],
        day: new Date(date).getDay(),
        selected: new Date(date)
      });
      this.queryActivitiesByDate();
    },
    onActivityClick(event, data) {
      this.handleClickEventList({ id: data.id, eventType: data.eventType, eventForm: data.eventForm });
    },
    toAddConfenrence(data) {
      http.addConference({ marketingEventId: data._id }).then(results => {
        if (results && results.errCode == 0) {
          this.$router.push({
            name: "meeting-detail",
            params: { id: results.data.id }
          });
        }
      });
    },
    async handleChooseCreateType(type) {
      if (type == "3") {
        this.$router.push({ name: "meeting-marketing-create" });
        return
      }
      const { date, year, month } = this.dates;
      this.visible = false;
      this.popoverTip = false;
      if (type === "live_marketing") {
        this.$router.push({
          name: "live-create"
        });
        return;
      }
      const marketingEventCommonSetting = await this.getMarketingEventCommonSetting(type)
      console.log(marketingEventCommonSetting, 'marketingEventCommonSetting')
      const typeLabel = type === 'multivenue_marketing' ? $t('marketing.pages.setting.dhdzh_0bf57e') : $t('marketing.commons.xsyx_6c9e91')
      requireAsync('crm-modules/action/field/field',field=>{
        CRM.api.add({
        apiname: "MarketingEventObj",
        title: $t('marketing.commons.xjschd_6609ae', {data: {'option0': type ? "-" + typeLabel : ""}}),
        show_type: "full",
        data: {
          event_form: type,
          event_type: marketingEventCommonSetting.length > 0 ? marketingEventCommonSetting[0].apiName : '',
          ...((date && { begin_time: new Date(year, month - 1, date) }) || {})
        },
        Model:field.Model.extend({
            parse: function(res) {
              let _options = res.objectDescribe.fields.event_type.options;
              let submitOptions = [];
              _options.forEach(item => {
                marketingEventCommonSetting.forEach(i => {
                  if (item.value === i.apiName) {
                    submitOptions.push(item)
                  }
                });
              });
              res.objectDescribe.fields.event_type.options = submitOptions;
              res.objectDescribe.fields.event_type.disable_after_filter = false
               // 因为目前市场活动新建event_form字段没支持到禁用能力  所以只展示当前活动类型 其他的都隐藏 
              res.objectDescribe.fields.event_form.options = res.objectDescribe.fields.event_form.options.filter(item =>item.value === type)
              console.log(res.objectDescribe.fields, 'res.objectDescribe.fields.event_form.options')
              return field.Model.prototype.parse.apply(this, arguments);
            }
          }),
          success: (_type, data) => {
            console.log(_type, data);
            //会议绑定
          if (type == 3) {
            this.toAddConfenrence(data);
            return;
          }
          FxUI.Message.success(
            $t('marketing.commons.schdcjcgjx_66c6fa')
          );
          this.handleClickEventList({
            id: data._id,
            new: true,
            event_type: data.event_type,
            event_form: data.event_form
          });
        },
      });
      })
    },
    handleCreateMarketingEvent() {
      // const loading = this.$loading();
      this.visible = true;
    },
    async getMarketingEventCommonSetting(type) {
      const realType = type === 'multivenue_marketing' ? 6 : 0
      const res = await http.getMarketingEventCommonSetting({ type: realType })
      if(res.data.activityTypeMapping && res.data.activityTypeMapping.length > 0) {
        const contentMarketingMapping = res.data.activityTypeMapping.filter(item => {
          return item.activityType === realType
        })
        return contentMarketingMapping[0].mapping
      }
      return []
    }
  },
  beforeDestroy() {
    if (this.notify) {
      this.notify.close();
    }
  }
};
</script>

<style lang="less" module>
.marketing_calebdar {
  display: flex;
  flex-flow: column nowrap;
  height: 100%;
  background: var(---Special-02, #EEF0F3);
  .create_activity{
    margin: 10px;
  }
  .content_header {
    display: flex;
    height: 50px;
    padding: 12px;
    margin-top: 12px;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid @border-color-base;
    background: #fff;
    border-radius: @border-radius-base @border-radius-base 0 0;
    .right {
      display: flex;
      align-items: center;
      gap: 12px;
      :global {
        .el-radio:last-child {
          margin-right: 0;
        }
      }
    }
  }
  .content {
    flex: 1 1 auto;
    display: flex;
    flex-flow: row;
    overflow: auto;
    background: #fff;
    border-radius: 0 0 8px 8px;
  }
  .activities {
    flex: 0 0 auto;
    width: 280px;
    height: 100%;
    display: flex;
    flex-flow: column nowrap;
    border-left: 1px solid @border-color-base;
    background: #fff;
    &.hide {
      display: none;
    }
    .activities_title {
      color: @color-title;
      font-size: 14px;
      text-align: center;
      line-height: 39px;
      border-bottom: 1px solid @border-color-base;
      background-color: #fff;
      // position: sticky;
      // top: 0;
      // z-index: 1;
    }
    .activities_content {
      flex: 1;
      border-left: 1px solid @border-color-base;
      margin-left: -1px;
      position: relative;
      .loading {
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.5);
        z-index: 1;
        text-align: center;
        padding-top: 200px;
        padding-top: 30vh;
      }
    }
    .list {
      cursor: pointer;
      padding: 8px;
      border-bottom: 1px solid @border-color-base;
      position: relative;
      padding-left: 26px;
      &:hover,
      &.hover {
        background-color: #f0f4fc;
      }
      .tag {
        margin-left: 6px;
        border-radius: 100%;
        width: 48px;
        height: 48px;
        line-height: 48px;
        position: absolute;
        right: 14px;
        top: 50%;
        transform: translate(0, -42%);
      }
      .title {
        color: @color-title;
        font-size: 14px;
        margin-bottom: 6px;
        position: relative;
        .dot {
          display: inline-block;
          width: 8px;
          height: 8px;
          border-radius: 100%;
          background: #c1c5ce;
          position: absolute;
          left: -14px;
          top: 50%;
          transform: translate(0, -50%);
        }
      }
      .item {
        display: flex;
        margin-bottom: 0;
        &:last-child {
          margin-bottom: 0;
        }
        .label {
          width: 66px;
          margin-right: 20px;
          color: @color-subtitle;
          font-size: 12px;
        }
        .con {
          flex: 1;
          font-size: 12px;
          color: @color-title;
        }
      }
    }
  }
  .fullcalendar_wrap {
    flex: 1 1 auto;
    min-width: 0;
    .fullcalendar {
      border-bottom: 1px solid @border-color-base;
      // position: sticky;
      // top: 0;
      &.fullheight {
        height: 99.9%;
        :global {
          .f__fullcalendar {
            height: 100%;
          }
        }
      }
    }
  }
}
</style>
