<template>
  <div :class="$style.marketing_assistant_list">
    <div :class="$style.illustrate">
      <div :class="$style.title">{{ $t('marketing.pages.marketing_collaboration.xgsm_5e1613') }}</div>
      <div :class="$style.con">
        <p>1{{ $t('marketing.pages.marketing_collaboration.yxzssz_5fa95f') }}App{{ $t('marketing.pages.marketing_collaboration.wxdddtgdyg_863a3a') }} {{ $t('marketing.pages.marketing_collaboration.yycykyjstg_bc4029') }} <a
            href="https://help.fxiaoke.com/93d5/9188/0f9e/b30c#header-0" target="_blank">{{ $t('marketing.commons.ljgd_06a3c1') }}</a></p>
        <p>2{{ $t('marketing.pages.marketing_collaboration.sycylbzssy_756a64') }} <a
            href="https://help.fxiaoke.com/93d5/9188/0f9e/b30c#header-1" target="_blank">{{ $t('marketing.pages.marketing_collaboration.ckqxsm_4fd095') }}</a></p>
        <p>3{{ $t('marketing.pages.marketing_collaboration.dyhbdwdqds_9d3658') }} <a
            href="https://help.fxiaoke.com/93d5/9188/0f9e/b30c#header-2" target="_blank">{{ $t('marketing.pages.marketing_collaboration.ljrhbddqds_8e3e82') }}</a></p>
      </div>
    </div>
    <div :class="$style.table_wrap">
      <div :class="$style.table_header">
        <div :class="$style.filter_wrap">
          <fx-input :class="[$style.filter, 'search-input']" v-model="keyword" :placeholder="$t('marketing.pages.marketing_collaboration.ssxmsjhyx_06a095')" clearable
            is-search size="small" @change="handleFilterChange" style="width: 230px"></fx-input>
          <fx-select
            v-model="userType"
            :class="[$style.filter]"
            :placeholder="$t('marketing.commons.yhlx_d340e9')"
            size="small"
            :options="userTypeSelectOptions"
            clearable
            @change="handleFilterChange"
          />
          <fx-select :class="[$style.filter]" v-model="originUserType" :placeholder="$t('marketing.commons.csly_7000b2')" size="small"
            :options="originalUserTypeOptions" clearable @change="handleFilterChange"></fx-select>
          <fx-select :class="[$style.filter]" v-model="cardStatus" :placeholder="$t('marketing.commons.mpktzt_183b84')" size="small"
            :options="cardStatusOptions" clearable @change="handleFilterChange"></fx-select>
        </div>
        <div>
          <fx-button :class="$style.button" type="primary" size="small" @click="userRelationInit"
          :loading="userRelationInitLoading">{{ $t('marketing.pages.marketing_collaboration.tbcysj_aaad4f') }}</fx-button>
        <fx-button :class="$style.button" size="small" @click="userRelationExport"
          :loading="userRelationExporting">{{ $t('marketing.commons.dc_55405e') }}</fx-button>
        </div>
      </div>
      <VTable ref="table" v-loading="listLoading" :columns="columns" :emptyText="$t('marketing.commons.zwsj_21efd8')" :data="lists"
        :settable="true" tid="marketing_assistant_table" :row-style="{ cursor: 'pointer' }"
        @click:text="handleTextClick"
      >
        <template slot-scope="scope">
          <template v-if="scope.col.exComponent === 'userType'">
            <fx-tag
              v-if="userTypeEnum[scope.row.userType]"
              :type="userTypeEnum[scope.row.userType].iconType"
              size="small"
            >
              {{ userTypeEnum[scope.row.userType].label }}
            </fx-tag>
            <fx-tag
              v-else
              type="info"
              size="small"
            >
              {{ $t('marketing.commons.zw_f61f4c') }}
            </fx-tag>
          </template>
        </template>
      </VTable>
      <VPagen :pagedata.sync="pageData" @change="handlePageChange" />
    </div>
    <UserDetail :userId="marketingUserId" v-if="marketingUserDetailVisible" :visible="marketingUserDetailVisible"
      @close="marketingUserDetailVisible = false" />
  </div>
</template>
<script>
import VTable from "@/components/table-ex";
import VPagen from '@/components/kitty/pagen.vue'
import UserDetail from "@/pages/user/detail";
import http from '@/services/http';
import tableTool from "./table-tools";

import { userTypeEnum } from './const.js'

export default {
  components: {
    VTable,
    VPagen,
    UserDetail,
  },
  data() {
    return {
      activeIndex: '1',
      userRelationInitLoading: false,
      userRelationExporting: false,
      listLoading: false,
      originalUserTypeOptions: [
        {
          label: $t('marketing.commons.fxygtxl_8f6417'),
          value: 'CRM',
        },
        {
          label: $t('marketing.pages.marketing_collaboration.qwwxygtxl_0eb941'),
          value: 'QYWX',
        },
        // {
        //   label: $t('marketing.pages.marketing_collaboration.ddyg_6baaa3'),
        //   value: 'DINGDING',
        // },
        {
          label: $t('marketing.pages.marketing_collaboration.fxxyhlqydj_055216'),
          value: 'EMPLOYEE_MEMBER',
        },
        {
          label: $t('marketing.pages.marketing_collaboration.mzhytjtgsf_0c5a8a'),
          value: 'PARTNER_MEMBER',
        },
        {
          label: $t('marketing.pages.marketing_collaboration.mzhytjtgsf_0f9d50'),
          value: 'EMPLOYEE',
        },
      ],
      userType: '',
      userTypeEnum,
      cardStatusOptions: [{
        label: $t('marketing.commons.ykt_3caba8'),
        value: 'OPEN',
      }, {
        label: $t('marketing.commons.wkt_686e8a'),
        value: 'NOT_OPEN',
      }],
      keyword: '',
      originUserType: '',
      cardStatus: '',
      columns: tableTool.getColumns(),
      lists: [],
      pageData: {
        pageSize: 10,
        pageNum: 1,
        totalCount: 10
      },
      marketingUserId: "",
      marketingUserDetailVisible: false,
    }
  },
  computed: {
    userTypeSelectOptions() {
      return Object.values(userTypeEnum)
    },
  },
  methods: {
    handlePageChange(data) {
      this.pageData.pageNum = data.pageNum
      this.pageData.pageSize = data.pageSize
      this.queryUserRelationList()
    },
    userRelationExport() {
      if(this.userRelationExporting) return;
      this.userRelationExporting = true;
      const params = this.getListParams();
      http.userRelationExport(params).then(({ errCode, errMsg }) => {
        this.userRelationExporting = false;
        if (errCode === 0) {
          FxUI.MessageBox.alert($t('marketing.commons.dcsjknxyjz_e02eef'), $t('marketing.commons.ts_02d981'), {
            confirmButtonText: $t('marketing.commons.zdl_ce2695'),
          })
        } else {
          FxUI.Message.error(errMsg);
        }
      });
    },
    userRelationInit() {
      if(this.userRelationInitLoading) return;
      this.userRelationInitLoading = true;
      http.userRelationInit().then(({ errCode, errMsg }) => {
        this.userRelationInitLoading = false;
        if (errCode === 0) {
          FxUI.Message.success($t('marketing.commons.tbcg_52b85c'));
        } else {
          FxUI.Message.error(errMsg);
        }
      });
    },
    getListParams(){
      const params = {
        pageSize: this.pageData.pageSize,
        pageNum: this.pageData.pageNum,
        ...(this.keyword && { keyword: this.keyword }),
        ...(this.originUserType && { originUserType: this.originUserType }),
        ...(this.cardStatus && { cardStatus: this.cardStatus }),
        ...(this.userType && { userType: this.userType }),
      };
      return params;
    },
    queryUserRelationList() {
      const params = this.getListParams();
      this.listLoading = true;
      http.queryUserRelationList(params).then(({ errCode, data }) => {
        this.listLoading = false;
        if (errCode === 0) {
          this.pageData.totalCount = data.totalCount;
          this.lists = tableTool.getDatas(data.result || []);
        }
      });
    },
    handleTextClick(prop, data) {
      console.log(prop, data)
      switch (prop) {
        case 'personObjName':
          if (!data.personObjId) return;
          CRM.api.show_detail({
            apiname: "PersonnelObj",
            id: data.personObjId,
          });
          break;
        case 'qywxUserName':
          if (!data.qywxEmployeeObjId) return;
          CRM.api.show_detail({
            apiname: "WechatEmployeeObj",
            id: data.qywxEmployeeObjId,
          });
          break;
        case 'partnerName':
          if (!data.partnerId) return;
          CRM.api.show_detail({
            apiname: "PublicEmployeeObj",
            id: data.partnerId,
          });
          break;
        case 'wechatFanName':
          if (!data.wechatFanId) return;
          CRM.api.show_detail({
            apiname: "WechatFanObj",
            id: data.wechatFanId,
          });
          break;
        case 'miniAppUserName':
          if (data.userMarketingId) {
            this.marketingUserId = data.userMarketingId;
            this.marketingUserDetailVisible = true;
          }
          break;
        case 'memberName':
          if (!data.memberId) return;
          CRM.api.show_detail({
            apiname: "MemberObj",
            id: data.memberId,
          });
          break;
        default:
          break;
      }
    },
    handleFilterChange() {
      this.pageData.pageNum = 1;
      this.queryUserRelationList();
    }
  },
  created() {
    this.queryUserRelationList();
  }
}
</script>

<style lang="less" module>
.marketing_assistant_list {
  .illustrate {
    font-size: 14px;
    padding: 15px 20px;

    .title {
      color: #666;
    }

    .con {
      p {
        margin-top: 6px;
      }
    }
  }

  .table_wrap {
    border-top: 10px solid #f2f2f5;

    .table_header {
      padding: 10px 20px;
      display: flex;

      .filter_wrap {
        display: flex;
        flex: 1;
        margin-top: -10px;
      }

      .filter {
        margin-right: 10px;
        margin-top: 10px;
      }

      .button {
        margin-left: 20px;
      }
    }
  }
}
</style>