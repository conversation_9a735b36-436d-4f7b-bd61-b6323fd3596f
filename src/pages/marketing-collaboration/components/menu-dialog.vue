<template>
  <v-dialog
    append-to-body
    width="550px"
    :title="$t('marketing.pages.marketing_collaboration.cdsz_d8ad11')"
    :ok-text="$t('marketing.pages.setting.bc_56df61')"
    :visible="visible"
    :loading="submitLoading"
    :class="$style.menu_dialog"
    @onClose="handleCancel"
    @onSubmit="handleSave"
  >
    <div :class="$style.content">
      <fx-form
        ref="form"
        label-width="100px"
        :rules="rules"
        :model="form"
      >
        <fx-form-item
          :label="$t('marketing.pages.setting.cdmc_8ee9f2')"
          prop="name"
        >
          <fx-input
            v-model="form.name"
            size="small"
            maxlength="10"
            show-word-limit
          />
        </fx-form-item>
        <fx-form-item
          :label="$t('marketing.pages.setting.cdtb_948304')"
          prop="iconTitle"
        >
          <div :class="$style.icon_show">
            <div
              v-if="form.lineIcon"
              :class="$style.icon_wrap"
            >
              <i
                :class="{
                  'hexagon-icon': /^hicon/.test(form.lineIcon),
                  'iconfont': /^icon/.test(form.lineIcon),
                  [form.lineIcon]: true,
                }"
              />
            </div>
            <div
              v-else
              :class="[$style.icon_wrap, $style.icon_picker_add]"
              @click="menuIconVisible = true"
            >
              <i class="el-icon-plus" />
              <p>{{ $t('marketing.pages.setting.xztb_598f69') }}</p>
            </div>
          </div>
          <div
            v-if="form.lineIcon"
            :class="$style.icon_picker_wrapper"
          >
            <ColorPicker
              v-model="form.color"
              show-alpha
              size="mini"
            />
            <div
              @click="menuIconVisible = true"
            >
              {{ $t('marketing.commons.gh_531401') }}
            </div>
          </div>
        </fx-form-item>
        <fx-form-item
          :label="$t('marketing.commons.djtz_01db4f')"
          prop="targetMaterialType"
        >
          <fx-select
            v-model="form.targetMaterialType"
            :class="$style.action_select"
            :options="actionTypesOption"
            size="small"
            :placeholder="$t('marketing.commons.qxz_708c9d')"
            @change="handleActionChange"
          />
        </fx-form-item>
        <fx-form-item
          v-if="enableRange"
          :label="$t('marketing.pages.marketing_collaboration.zssj_df8ba7')"
          prop="showData"
        >
          <fx-select
            v-model="form.objectAccessibleRule"
            :class="$style.action_select"
            :options="menuDataRangeOption"
            size="small"
            :placeholder="$t('marketing.commons.qxz_708c9d')"
            @change="handleRangeChange"
          />
          <div
            v-if="form.objectAccessibleRule === 'TAG'"
            :class="$style.filter_wrapper_tags"
          >
            <fx-radio-group
              v-model="queryTags.queryType"
              :class="$style.radio_group"
              text-color="#181c25"
            >
              <fx-radio
                :class="$style.radio"
                :label="1"
              >
                {{ $t('marketing.commons.bhyxrybq_f86759') }}
              </fx-radio>
              <fx-radio
                :class="$style.radio"
                :label="2"
              >
                {{ $t('marketing.commons.bhyxsybq_a99dd2') }}
              </fx-radio>
            </fx-radio-group>
            <div :class="$style.tag_selector">
              <TagsLine v-model="queryTags.tags" />
            </div>
          </div>
          <div
            v-if="form.objectAccessibleRule === 'SINGLE_GROUP'"
            :class="$style.filter_wrapper"
          >
            <GroupDropdown
              :active-group-id="form.groupList && form.groupList.length ? form.groupList[0].id : null"
              :object-type="objectType"
              :hide-group-ids="['-1', '-2', '-3']"
              style="width: auto;"
              @change="handleSingleGroupChange"
            />
          </div>
          <div
            v-if="form.objectAccessibleRule === 'MULTI_GROUP'"
            :class="$style.filter_wrapper"
          >
            <div
              :class="$style.add"
              @click="multiGroupVisible = true"
            >
              <i class="el-icon-plus" />
              <span>{{ $t('marketing.pages.marketing_collaboration.szlbzsfz_c92b6d') }}</span>
            </div>
            <div
              v-for="(item, i) in form.groupList"
              :key="i"
              :class="$style.material_item"
              style="padding: 0 10px;"
            >
              <div :class="$style.title">
                {{ item.name }}
              </div>
              <fx-button
                type="text"
                :class="$style.remove"
                @click="handleRemoveGroupItem(item, i)"
              >
                {{ $t('marketing.commons.yc_86048b') }}
              </fx-button>
            </div>
          </div>
          <div
            v-if="form.objectAccessibleRule === 'OBJECT_FILTER'"
            :class="$style.filter_wrapper"
          >
            <ObjectFilterLine
              :value="queryFilters"
              :filter-fields="filterMarketingEventObjField"
              :show-tag="false"
              @change="handleMarketingEventObjFilterChange"
            />
          </div>
        </fx-form-item>
        <fx-form-item
          v-if="isKnowledgeDatabase"
          :label="$t('marketing.pages.marketing_collaboration.zssj_df8ba7')"
          prop="showData"
        >
          <fx-select
            v-model="form.knowledgeScene"
            :class="$style.action_select"
            :options="knowledgeSceneOptions"
            size="small"
            :placeholder="$t('marketing.commons.qxz_708c9d')"
            @change="handleRangeChange"
          />
        </fx-form-item>
        <fx-form-item
          v-if="isShareGPT"
          :label="$t('marketing.pages.marketing_collaboration.ky_d3b439')"
          prop="agentList"
        >
          <ShareGPTSelector
            v-model="form.agentList"
            size="small"
          />
        </fx-form-item>
      </fx-form>
    </div>
    <MenuIcon
      :value="form.lineIcon"
      :visible.sync="menuIconVisible"
      @input="handleIconSubmit"
    />
    <TagsQueryDialog
      v-if="tagDialogVisible"
      :visible="tagDialogVisible"
      :init-form="queryTags"
      @onSubmit="handleSubmitTagsDialog"
      @onClose="tagDialogVisible = false"
    />
    <MultiGroupDialog
      v-if="multiGroupVisible"
      :visible.sync="multiGroupVisible"
      :object-type="objectType"
      :group-list="form.groupList"
      @close="multiGroupVisible = false"
      @submitGroupSelect="handleSubmitGroupSelect"
    />
  </v-dialog>
</template>
<script>
import { mapGetters } from 'vuex'
import { MenuIcon } from '@/components/Hexagon/index.js'
import ShareGPTSelector from './sharegpt-selector.vue'

import http from '@/services/http/index.js'

import VDialog from '@/components/dialog/index.vue'

import {
  menuTypeEnum, menusEnum, actionTypes, objMenuDataRangeEnum, materialMenuDataRangeEnum, formatMenuItem,
} from './const.js'

import TagsQueryDialog from '@/components/content-tags-selector/tags-query-dialog.vue'

import GroupDropdown from '@/components/group-manage-new/group-dropdown.vue'

import MultiGroupDialog from './multi-group-dialog.vue'

import ObjectFilterLine from '@/components/ObjectFilterLine/index.vue'

import TagsLine from '@/components/content-tags-selector/tags-line.vue'
import ColorPicker from '$page-designer/components/comp/components/ColorPicker.vue'

import kisvData from '@/modules/kisv-data.js'

export default {
  components: {
    MenuIcon,
    VDialog,
    TagsQueryDialog,
    GroupDropdown,
    MultiGroupDialog,
    ObjectFilterLine,
    TagsLine,
    ColorPicker,
    ShareGPTSelector,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    templateId: {
      type: String,
      default: '',
    },
    menuId: {
      type: String,
      default: null,
    },
    userType: {
      type: String,
      default: 'null',
    },
  },
  data() {
    return {
      submitLoading: false,
      queryFilters: [
        {
          objectAPIName: 'MarketingEventObj',
          query: {
            filters: [],
          },
        },
      ],
      queryTags: {
        queryType: 1,
        tags: [],
      },
      multiGroupVisible: false,
      tagDialogVisible: false,
      form: {
        name: '',
        tagList: [],
        groupList: [],
        targetMaterialType: null,
        objectAccessibleRule: '',
        knowledgeScene: null,
        agentList: [],
      },
      menuIconVisible: false,
      options: [
        {
          value: 'site',
          label: $t('marketing.commons.wymlb_9afef8'),
        },
      ],
      rules: {
        name: [{ required: true, message: $t('marketing.pages.setting.qsrcdmc_4cd562'), trigger: 'blur' }],
        lineIcon: [{ required: true, message: $t('marketing.pages.setting.qxzcdtb_3e856d'), trigger: 'blur' }],
        targetMaterialType: [
          {
            required: true,
            trigger: 'blur',
            validator: (rule, value, callback) => {
              callback()
            },
          },
        ],
        showData: {
          required: true,
          validator: (rule, value, callback) => {
            const { objectAccessibleRule, groupList, knowledgeScene } = this.form
            if ((objectAccessibleRule === 'SINGLE_GROUP' || objectAccessibleRule === 'MULTI_GROUP')) {
              if (!groupList || !groupList.length) {
                callback(new Error($t('marketing.commons.qzsxzyg_a59de9', { data: { option0: $t('marketing.commons.fz_829abe') } })))
              }
            }

            if (objectAccessibleRule === 'TAG') {
              if (!this.queryTags.tags || !this.queryTags.tags.length) {
                callback(new Error($t('marketing.commons.qzsxzyg_a59de9', { data: { option0: $t('marketing.commons.bq_14d342') } })))
              }
            }

            if (this.isKnowledgeDatabase && !knowledgeScene) {
              callback(new Error($t('marketing.pages.marketing_collaboration.qxzzskcj_beb7fa')))
            }

            callback()
          },
        },
        agentList: {
          required: true,
          validator: (rule, value, callback) => {
            if (this.isShareGPT && !(value && value.length)) {
              callback(new Error($t('marketing.pages.marketing_collaboration.qxzky_3857d5')))
            }
            callback()
          },
        },
      },
      vDatas: kisvData.datas,
      menusEnum,
      knowledgeSceneOptions: [],
    }
  },
  computed: {
    ...mapGetters('MarketingEventSet', ['marketSetFilters']),
    isShareGPT() {
      return this.form.targetMaterialType === menuTypeEnum.shareGpt
    },
    menuDataRangeOption() {
      return this.isMarketingEventType(this.form.targetMaterialType) ? objMenuDataRangeEnum : materialMenuDataRangeEnum
    },
    enableRange() {
      return Object.keys(menusEnum)
        .filter(k => menusEnum[k].enableRange)
        .map(i => Number(i))
        .includes(this.form.targetMaterialType)
    },
    objectType() {
      return menusEnum[this.form.targetMaterialType].objectType
    },
    actionTypesOption() {
      return actionTypes.filter(a => {
        const scope = a.userTypes && this.userType && a.userTypes.includes(this.userType)
        if (scope && a.value === menuTypeEnum.shareGpt) {
          return this.vDatas.uinfo.marketingAIPluginEnable
        }
        if (scope && a.value === menuTypeEnum.knowledgeDatabase) {
          const val = this.vDatas.uinfo.serviceKnowledgeManagement
          if (val) {
            this.queryServiceKnowledgeScenaryList()
          }
          return val
        }
        return scope
      })
    },
    isKnowledgeDatabase() {
      return this.menusEnum[this.form.targetMaterialType] && this.menusEnum[this.form.targetMaterialType].knowledgeScene
    }
  },
  watch: {
    value: {
      handler(val) {
        this.getAppMenuTemplateMenuDetail(val)
      },
    },
  },
  created() {
    this.getAppMenuTemplateMenuDetail(this.menuId)
  },
  methods: {
    queryServiceKnowledgeScenaryList() {
      http.queryServiceKnowledgeScenaryList().then(res => {
        if (res && res.errCode === 0) {
          this.knowledgeSceneOptions = res.data || []
          return
        }
        FxUI.Message.error($t('marketing.pages.setting.cxcjlbsb_13d573'))
      })
    },
    handleRemoveGroupItem(item, i) {
      this.form.groupList.splice(i, 1)
    },
    isMarketingEventType(type) {
      return Object.keys(menusEnum)
        .filter(k => menusEnum[k].rangeType === 2)
        .map(i => Number(i))
        .includes(type)
    },
    handleMarketingEventObjFilterChange(data) {
      this.queryFilters = data
    },
    filterMarketingEventObjField(field) {
      const apiName = field.api_name || field.data
      if (field.api_name === 'event_type') {
        const showEventTypes = ['3', 'multivenue_marketing', 'live_marketing']
        field.options = field.options.filter(item => showEventTypes.indexOf(item.value) !== -1)
      }
      // 显示自定义字段
      if (field.define_type === 'custom') {
        return false
      }
      const showFields = [
        'name',
        'event_type',
        'biz_status',
        'status',
        'life_status',
        'parent_id',
        'begin_time',
        'end_time',
        'description',
        'location',
        'owner',
        'owner_department',
        'data_own_department',
      ]
      return showFields.indexOf(apiName) === -1
    },
    handleSubmitGroupSelect(value) {
      this.form.groupList = value.map(v => ({
        id: v.groupId,
        name: v.groupName,
      }))
    },
    getAppMenuTemplateMenuDetail(id) {
      if (!id) return
      http.appMenuTemplateMenuDetail({
        id,
      }).then(res => {
        if (res && res.errCode === 0 && res.data) {
          const result = res.data
          const defaultObjectAccessibleRule = this.isMarketingEventType(result.targetMaterialType) ? 'PAAS_OBJECT_ACCESSIBLE' : 'OBJECT_GROUP_ACCESSIBLE'
          
          const form = {
            objectAccessibleRule: this.isShareGPT ? 'SPECIFIC' : defaultObjectAccessibleRule,
            groupList: result.groupList || [],
            tagList: result.tagList || [],
            ...formatMenuItem(result),
          }
          
          this.form = _.extend(this.form, form)
          if (result.objectAccessibleRule === 'TAG') {
            this.queryTags = {
              queryType: result.tagOperator || 1,
              tags: (result.tagList || []).map(tag => ({
                tagId: tag.id,
                firstTagName: tag.firstTagName,
                secondTagName: tag.secondTagName,
                nameid: `${tag.firstTagName}：${tag.secondTagName}`,
              })),
            }
          }
          if (result.objectAccessibleRule === 'OBJECT_FILTER') {
            this.queryFilters = [
              {
                objectAPIName: 'MarketingEventObj',
                query: {
                  filters: result.filters || [],
                },
              },
            ]
          }
        }
      })
    },
    handleCancel() {
      this.$emit('update:visible', false)
    },
    handleSave() {
      const params = {
        name: this.form.name,
        targetMaterialType: this.form.targetMaterialType,
        objectAccessibleRule: this.form.objectAccessibleRule,
        lineIcon: this.form.lineIcon,
        flatIcon: this.form.flatIcon,
        color: this.form.color,
      }
      params.templateId = this.templateId
      if (this.menuId) {
        params.id = this.menuId
      }
      if (this.isKnowledgeDatabase) {
        params.knowledgeScene = this.form.knowledgeScene
        params.objectAccessibleRule = 'SPECIFIC'
      }
      if (this.form.objectAccessibleRule === 'OBJECT_FILTER') {
        params.filters = this.queryFilters?.[0]?.query?.filters || []
      }
      if (this.form.objectAccessibleRule === 'TAG') {
        params.tagOperator = this.queryTags.queryType
        params.tagIdList = this.queryTags.tags.map(tag => tag.tagId)
      }
      if (this.form.objectAccessibleRule === 'SINGLE_GROUP' || this.form.objectAccessibleRule === 'MULTI_GROUP') {
        params.groupIdList = (this.form.groupList || []).map(group => group.id)
      }

      if (this.isShareGPT) {
        params.agentList = this.form.agentList
        params.objectAccessibleRule = 'SPECIFIC'
      }

      this.$refs.form.validate(isOk => {
        if (isOk) {
          this.submitLoading = true
          http.appMenuTemplateCreateOrUpdateMenu(params).then(res => {
            this.submitLoading = false
            if (res && res.errCode === 0) {
              this.$emit('update:success')
              FxUI.Message.success(this.menuId ? $t('marketing.commons.xgcg_69be67') : $t('marketing.commons.xzcg_a5bfd7'))
            }
          })
        }
      })
    },
    handleIconSubmit(icon) {
      this.form.lineIcon = icon.line
      this.form.flatIcon = icon.flat
    },
    handleActionChange(item) {
      this.form.objectAccessibleRule = this.isMarketingEventType(item) ? 'PAAS_OBJECT_ACCESSIBLE' : 'OBJECT_GROUP_ACCESSIBLE'
    },
    handleRangeChange(item) {},
    handleSubmitTagsDialog(query) {
      this.tagDialogVisible = false
      this.queryTags = query
      this.form.tagOperator = query.queryType
      this.form.tagList = query.tags.map(t => ({
        name: t.nameid,
        id: t.tagId,
        firstTagName: t.firstTagName,
        secondTagName: t.secondTagName,
      }))
    },
    handleSingleGroupChange(item) {
      this.form.groupList = [
        {
          name: item.groupName,
          id: item.groupId,
        },
      ]
    },
  },
}
</script>
<style lang="less" module>
.menu_dialog {
  .icon_show {
    display: inline-block;
    margin-right: 6px;
  }

  :global {
    .el-form-item__content {
      .el-select {
        width: 100%;
      }
    }
    .el-form-item__label {
      line-height: 32px;
    }
  }

  .icon_wrap {
    width: 90px;
    height: 90px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 4px;
    border: 1px solid #e9edf5;
    &.icon_picker_add {
      flex-direction: column;
      cursor: pointer;
      i {
        font-size: 14px;
        font-weight: bold;
        color: var(--color-primary06, #407fff);
      }
      p {
        font-size: 14px;
        line-height: 16px;
        color: @color-subtitle;
        margin-top: 2px;
      }
    }
    i {
      font-size: 42px;
      color: #545861;
    }
    .imageIcon {
      width: 42px;
      height: 42px;
    }
  }
  .icon_picker_wrapper {
    display: inline-block;
    line-height: 20px;
    vertical-align: bottom;
    color: var(--color-info06, #407fff);
    cursor: pointer;
  }

  .action_select {
    width: 100%;
  }

  .tag_select_wrapper {
    border-radius: 4px;
    border: 1px solid #C1C5CE;
    background: #FFF;
    width: 100%;
    padding: 6px 8px;
  }

  .tag_select_add {
    color: var(--color-primary06,#ff8000);
  }
  .add {
    width: 100%;
    box-sizing: border-box;
    height: 50px;
    margin: 10px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-info06,#407FFF);
    font-size: 13px;
    border: 1px solid #e9edf5;
    cursor: pointer;
    i {
      font-size: 15px;
      color: var(--color-info06,#407FFF);
      font-weight: bold;
      margin-right: 5px;
    }
  }
  .filter_wrapper_tags {
    margin-top: 10px;
    min-height: 32px;
    border: 1px solid #DCDFE6;
    border-radius: 4px;
    line-height: 32px;
    padding: 8px 12px;
    cursor: pointer;
    color: #407FFF;
    font-size: 12px;

    .tag_item {
      border: none;
      color: #839ab6;
      padding: 0 10px;
      margin-right: 6px;
      word-break: keep-all;
      display: inline-block;
      line-height: 24px;
      font-size: 12px;
      border-radius: 2px;
      background: #e0eeff;
    }

      .radio_group {
        padding-top: 5px;
        .radio {
          display: block;
          margin-left: 0;
          margin-top: 10px;
          :global {
            .el-radio__label {
              color: @color-title;
              font-weight: normal;
              font-size: 13px;
            }
          }
        }
      }
      .tag_selector {
        margin-top: 10px;
        padding: 0 10px;
        min-height: 100px;
        background: #fff;
        border-radius: 4px;
        border: 1px solid @border-color-base;
        margin-bottom: 15px;
      }
  }
  .filter_wrapper {
    margin-top: 10px;

    .material_item {
      height: 50px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border: 1px solid @border-color-base;
      margin-bottom: 10px;
      .left_content{
        display: flex;
        align-items: center;
        flex: 1;
        width: 0;
        .image {
          width: 63px;
          height: 50px;
          object-fit: cover;
        }
        .title {
          font-size: 13px;
          color: @color-title;
          flex: 1;
          padding: 0 10px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
      .right_content{
        width: 65px;
        display: flex;
        align-items: center;
        .drag_handle{
          font-size: 16px;
          margin: 0 16px 0 5px;
          cursor: pointer;
        }
        .remove {
          font-size: 16px;
          cursor: pointer;
          margin-right: 13px;
        }
      }
    }
  }
}
</style>
