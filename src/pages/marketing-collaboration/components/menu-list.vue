<template>
  <div :class="$style.marketing_menu_list">
    <div :class="$style.setting_tips">
      {{ $t('marketing.pages.marketing_collaboration.rqyydywxfz_37d218') }}
    </div>
    <div :class="$style.operate_wrapper">
      <fx-button
        type="primary"
        size="small"
        :class="$style.create_btn"
        @click="showCreateDialog = true"
      >
        <i class="el-icon-plus" />
        {{ $t('marketing.pages.marketing_collaboration.cjyymb_6f85ef') }}
      </fx-button>
    </div>
    <v-table
      v-loading="loading"
      :class="$style.menu_list"
      :row-style="{ height: '100px' }"
      :data="tableData"
      :columns="columns"
      :show-overflow-tooltip="false"
    >
      <template slot-scope="scope">
        <template v-if="scope.col.exComponent === 'tplInfo'">
          <div :class="$style.list_title">
            <span>{{ scope.row.name }}</span>
            <fx-tag
              v-if="/^SYSTEM/.test(scope.row.type)"
              style="margin-left: 10px"
              type="primary"
              size="mini"
            >
              {{ $t('marketing.pages.marketing_collaboration.yz_5c888f') }}
            </fx-tag>
          </div>
          <fx-tooltip
            class="item"
            :effect="effectType"
            placement="top"
            :disabled="!scope.row.range"
            :content="scope.row.range"
          >
            <div :class="$style.list_label">
              {{ $t('marketing.commons.syfw_901c0d') }}：{{ scope.row.range || '--' }}
            </div>
          </fx-tooltip>
          <div :class="$style.list_label">
            {{ $t('marketing.commons.gxsj_a001a2') }}：{{ scope.row.formateUpdateTime || '--' }}
          </div>
        </template>
        <template v-else-if="scope.col.exComponent === 'userType'">
          <template v-if="userTypeEnum[scope.row.userType]">
            <fx-tag
              :type="userTypeEnum[scope.row.userType].iconType"
              size="small"
            >
              {{ userTypeEnum[scope.row.userType].label }}
            </fx-tag>
          </template>
          <fx-tag
            v-else
            type="info"
            size="small"
          >
            {{ $t('marketing.commons.zw_f61f4c') }}
          </fx-tag>
        </template>
        <template v-else-if="scope.col.exComponent === 'status'">
          <fx-tag
            v-if="scope.row.status === 'ENABLE'"
            type="success"
            size="small"
          >
            {{ $t('marketing.commons.yqy_53ace4') }}
          </fx-tag>
          <fx-tag
            v-else
            type="info"
            size="small"
          >
            {{ $t('marketing.commons.yty_69b0f6') }}
          </fx-tag>
        </template>
        <template v-else-if="scope.col.exComponent === 'userCount'">
          <fx-button
            type="text"
            size="small"
            @click="handleClickCount(scope.row)"
          >
            {{ scope.row.userCount || '--' }}
          </fx-button>
        </template>
        <template v-else>
          <fx-button
            type="text"
            @click="handlePreview(scope.row)"
          >
            {{ $t('marketing.commons.yl_645dbc') }}
          </fx-button>
          <fx-button
            type="text"
            @click="handleEdit(scope.row)"
          >
            {{ $t('marketing.commons.bj_95b351') }}
          </fx-button>
          <fx-dropdown
            style="margin-left: 10px"
            @command="handleDropdownClick($event, scope.row)"
          >
            <fx-button
              type="text"
              size="small"
            >
              {{ $t('marketing.commons.gd_0ec9ea') }}
              <i class="fx-icon-arrow-down fx-icon--right" />
            </fx-button>
            <fx-dropdown-menu slot="dropdown">
              <fx-dropdown-item command="copy">
                {{ $t('marketing.commons.fz_79d3ab') }}
              </fx-dropdown-item>
              <fx-dropdown-item command="status">
                {{
                  scope.row.status === 'ENABLE' ? $t('marketing.commons.ty_5c56a8') : $t('marketing.commons.qy_7854b5')
                }}
              </fx-dropdown-item>
              <fx-dropdown-item
                :style="scope.row.status === 'ENABLE' && 'cursor: default;color: #bbb;pointer-events: none;'"
                :disabled="scope.row.status === 'ENABLE'"
                command="delete"
              >
                {{ $t('marketing.commons.sc_2f4aad') }}
              </fx-dropdown-item>
            </fx-dropdown-menu>
          </fx-dropdown>
        </template>
      </template>
    </v-table>
    <v-pagen
      :pagedata.sync="pageData"
      @change="handlePageChange"
    />
    <v-dialog
      :visible.sync="showCreateDialog"
      :title="$t('marketing.pages.marketing_collaboration.cjyymb_6f85ef')"
      width="600px"
      append-to-body
      class="add-template-dialog"
      :loading="submitting"
      @onClose="showCreateDialog = false"
      @onSubmit="handleCreateSubmit"
    >
      <fx-form
        ref="createForm"
        :model="createForm"
        :rules="createRules"
      >
        <fx-form-item
          :label="$t('marketing.pages.marketing_collaboration.yymbmc_94498c')"
          prop="templateName"
        >
          <fx-input
            v-model="createForm.templateName"
            style="width: 100%"
            :placeholder="$t('marketing.commons.qsr_02cc4f')"
          />
        </fx-form-item>
        <fx-form-item
          :label="$t('marketing.pages.marketing_collaboration.syrq_410a7f')"
          prop="userType"
        >
          <fx-radio-group
            v-model="createForm.userType"
          >
            <fx-radio
              v-for="item in userRangeOptions"
              :key="item"
              :label="item.value"
            >
              <span style="white-space: normal;">{{ item.label }}</span>
            </fx-radio>
          </fx-radio-group>
        </fx-form-item>
      </fx-form>
    </v-dialog>
    <v-dialog
      :visible.sync="showPreviewDialog"
      :title="$t('marketing.pages.marketing_collaboration.ylyymb_9467cd')"
      width="440px"
      append-to-body
      class="menu-template-dialog"
      :show-confirm="false"
      :show-cancel="false"
      @onClose="showPreviewDialog = false"
    >
      <MenuPreview
        v-loading="previewLoading"
        :icon-type="previewData.iconType || 1"
        :menu-data="previewData.menuData"
        :title="previewData.title"
      />
    </v-dialog>
    <menu-range-sideslip
      v-if="menuRangeSideslipVisible"
      :visible="menuRangeSideslipVisible"
      :template-id="curTemplateId"
      @close="menuRangeSideslipVisible = false"
    />
  </div>
</template>
<script>
import http from '@/services/http/index.js'

import VTable from '@/components/table-ex/index.vue'
import VPagen from '@/components/kitty/pagen.vue'
import VDialog from '@/components/dialog/index.vue'

import util from '@/services/util/index.js'

import MenuPreview from './menu-preview.vue'

import MenuRangeSideslip from './menu-range-sideslip.vue'

import {
  getMemberPreviewData, formatMenuData, userTypeEnum, getFormatPartnerName,
} from './const.js'

import kisvData from '@/modules/kisv-data.js'

export default {
  components: {
    VTable,
    VPagen,
    VDialog,
    MenuPreview,
    MenuRangeSideslip,
  },
  data() {
    return {
      columns: [
        {
          label: $t('marketing.pages.marketing_collaboration.yymb_176a2f'),
          prop: 'tplInfo',
          minWidth: '500px',
          exComponent: 'tplInfo',
        },
        {
          label: $t('marketing.pages.marketing_collaboration.syrq_410a7f'),
          prop: 'userType',
          minWidth: '140px',
          exComponent: 'userType',
        },
        {
          label: $t('marketing.commons.zt_3fea7c'),
          prop: 'status',
          minWidth: '140px',
          exComponent: 'status',
        },
        {
          // id: 'count',
          label: $t('marketing.commons.syrs_8e843f'),
          prop: 'userCount',
          minWidth: '200px',
          exComponent: 'userCount',
        },
        {
          label: $t('marketing.commons.cz_2b6bc0'),
          prop: 'options',
          minWidth: '180px',
          exComponent: 'custom',
        },
      ],
      showPreviewDialog: false,
      showCreateDialog: false,
      loading: true,
      previewLoading: true,
      submitting: false,
      createForm: {
        templateName: '',
        userType: 'EMPLOYEE',
      },
      createRules: {
        templateName: [
          {
            required: true,
            message: $t('marketing.pages.marketing_collaboration.qsryymbmc_0e3cf3'),
            trigger: 'blur',
          },
        ],
      },
      pageData: {
        layout: 'prev, pager, next, total, sizes',
        pageNum: 1,
        totalCount: 0,
        pageSize: 10,
        pageSizes: [10, 20, 30, 40],
        time: 0,
      },
      tableData: [],
      previewData: {
        name: '',
        title: '',
        iconType: 1,
        menuData: [],
      },
      menuRangeSideslipVisible: false,
      curTemplateId: '',
      vDatas: kisvData.datas,
      userTypeEnum,
    }
  },
  computed: {
    isQywxOpen() {
      return this.$store.state.Global.isQywxOpen
    },
    memberMarketingPluginEnable() {
      return this.vDatas.pluginInfo.memberMarketingPluginEnable
    },
    // formateTableData() {
    //   return this.tableData.map(item => ({
    //     ...item,
    //     range: this.formatRange(item),
    //     formateUpdateTime: util.formatDateTime(item.updateTime, 'YYYY-MM-DD hh:mm'),
    //   }))
    // },
    partnerMarketingEnabled() {
      return this.vDatas.uinfo.partnerMarketingEnabled
    },
    userRangeOptions() {
      return Object.values(userTypeEnum).map(item => ({
        label: item.desc,
        value: item.value,
      }))
    },
  },
  watch: {
    showCreateDialog(val) {
      if (!val) {
        this.submitting = false
      }
    },
  },
  created() {
    this.getAppMenuTemplateList()
  },
  methods: {
    handleClickCount(row) {
      this.menuRangeSideslipVisible = true
      this.curTemplateId = row.id
    },
    async formatRange(row) {
      let fsRange = ''
      let qywxRange = ''
      let memberFilter = ''
      let partnerRange = ''
      if (row) {
        if (row.fsAddressBookScope && row.fsAddressBookScope.departmentList) {
          const _fsRange = row.fsAddressBookScope.departmentList.map(i => i.name).join(',')
          fsRange = _fsRange ? `${$t('marketing.pages.marketing_collaboration.fxtxl_657fe0')}${_fsRange}；` : ''
        }
        if (this.isQywxOpen && row.qywxAddressBookScope && row.qywxAddressBookScope.departmentList) {
          const _qywxRange = row.qywxAddressBookScope.departmentList.map(i => i.name).join(',')
          qywxRange = _qywxRange ? `${$t('marketing.pages.marketing_collaboration.qwtxl_7caff7')}${_qywxRange}；` : ''
        }
        if (this.memberMarketingPluginEnable && row.memberScope && row.memberScope.filters) {
          const memberFilters = [
            {
              objectAPIName: 'MemberObj',
              query: {
                filters: row.memberScope.filters,
              },
            },
          ]
          const _memberFilter = await getMemberPreviewData(memberFilters)
          memberFilter = _memberFilter ? `${$t('marketing.pages.marketing_collaboration.hy_47130b')}${_memberFilter}；` : ''
        }
        if (this.partnerMarketingEnabled && row.enterpriseRelationScope) {
          const _partnerRange = getFormatPartnerName(row.enterpriseRelationScope)
          partnerRange = _partnerRange ? `${$t('marketing.pages.marketing_collaboration.hb_039e5f')}${_partnerRange}；` : ''
        }
      }
      return fsRange + qywxRange + memberFilter + partnerRange
    },
    formatTableData(data = []) {
      return new Promise(resolve => {
        const _tableData = [];
        (data || []).forEach(async (item, index) => {
          const _item = JSON.parse(JSON.stringify(item))
          _item.range = await this.formatRange(item)
          _item.formateUpdateTime = util.formatDateTime(item.updateTime, 'YYYY-MM-DD hh:mm')
          _item.userCount = item.userCount
          _item.type = item.type
          _item.index = index
          _tableData.push(_item)
          if (index === data.length - 1) {
            // 不加延迟会导致数组还未更新就进行排序，导致顺序错乱
            setTimeout(() => {
              resolve(_tableData.sort((a, b) => a.index - b.index))
            }, 100)
          }
        })
      })
    },
    getAppMenuTemplateList() {
      this.tableData = []
      this.loading = true
      http
        .appMenuTemplateList({
          pageSize: this.pageData.pageSize,
          pageNum: this.pageData.pageNum,
        })
        .then(async res => {
          if (res && res.errCode === 0 && res.data) {
            this.tableData = await this.formatTableData(res.data.result || [])
            this.pageData.totalCount = res.data.totalCount || 0
            this.loading = false
          }
        })
    },
    handlePageChange(data) {
      this.pageData.pageNum = data.pageNum
      this.pageData.pageSize = data.pageSize
      this.getAppMenuTemplateList()
    },
    handleCreateSubmit() {
      this.$refs.createForm.validate(async valid => {
        if (valid) {
          this.submitting = true
          const res = await http.appMenuTemplateCreate(
            {
              name: this.createForm.templateName,
              userType: this.createForm.userType,
            },
          )
          if (res && res.errCode === 0 && res.data) {
            this.showCreateDialog = false
            this.$emit('detail', res.data)
          }
        }
      })
    },
    handlePreview(row) {
      this.getAppMenuTemplateDetail(row.id)
      this.showPreviewDialog = true
      this.curTemplateRow = row
    },
    getAppMenuTemplateDetail(id) {
      this.previewLoading = true
      http.appMenuTemplateDetail({ id }).then(res => {
        if (res && res.errCode === 0 && res.data) {
          this.previewData = {
            name: res.data.name || $t('marketing.commons.yzyymb_e75a4c'),
            title: res.data.title || '',
            iconType: res.data.iconType || 1,
            menuData: formatMenuData(res.data.menuList || [], res.data.iconType || 1),
          }
          this.previewLoading = false
        }
      })
    },
    handleEdit(row) {
      this.$emit('detail', row.id)
    },

    handleCopy(row) {
      if (row.id) {
        http.appMenuTemplateCopy({ sourceId: row.id }).then(res => {
          if (res && res.errCode === 0) {
            FxUI.Message.success($t('marketing.commons.fzcg_20a495'))
            this.getAppMenuTemplateList()
          }
        })
      }
    },

    handleStatus(row) {
      if (row.id) {
        http
          .appMenuTemplateUpdateStatus({
            id: row.id,
            status: row.status === 'ENABLE' ? 'DISABLE' : 'ENABLE',
          })
          .then(res => {
            if (res && res.errCode === 0) {
              FxUI.Message.success($t('marketing.commons.xgcg_69be67'))
              this.getAppMenuTemplateList()
            }
          })
      }
    },

    handleDelete(row) {
      if (row.id) {
        http.appMenuTemplateDelete({ id: row.id }).then(res => {
          if (res && res.errCode === 0) {
            FxUI.Message.success($t('marketing.commons.sccg_0007d1'))
            this.getAppMenuTemplateList()
          }
        })
      }
    },

    handleDropdownClick(command, row) {
      switch (command) {
        case 'copy':
          this.handleCopy(row)
          break
        case 'status':
          this.handleStatus(row)
          break
        case 'delete':
          this.handleDelete(row)
          break
        default:
          break
      }
    },
  },
}
</script>

<style lang="less">
.add-template-dialog {
  .el-radio {
    margin-top: 10px;
  }
}
.menu-template-dialog {
  .el-dialog__body {
    height: 720px;
    display: flex;
    justify-content: center;
    overflow: scroll;
  }
}
</style>

<style lang="less" module>
.marketing_menu_list {
  .setting_tips {
    padding: 16px;
    background: #f2f4fb;
    font-weight: 400;
    font-size: 12px;
    line-height: 24px;

    .link {
      font-size: 12px;
      line-height: 24px;
      color: #3487e2;
    }
  }

  .operate_wrapper {
    padding: 12px;
    display: flex;
    justify-content: flex-end;

    .create_btn {
      height: 32px;
    }
  }

  .menu_list {
    .list_title {
      display: flex;
      align-items: center;
      color: #181c25;
      font-size: 16px;
      line-height: 24px;
    }

    .list_label {
      margin-top: 6px;
      color: #545861;
      font-size: 14px;
      line-height: 20px;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
  }

  .createForm {
    .input {
      flex: 1;
    }
  }
}
</style>
