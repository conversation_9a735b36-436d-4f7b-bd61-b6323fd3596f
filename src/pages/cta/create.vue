<template>
  <v-scaffold
    :title="$route.query.id && $route.query.type !== 'copy' ? $t('marketing.pages.cta.bjzj_71a37d') : $t('marketing.pages.cta.xjzj_98b925')"
    @send="validateForm" :btnState="btnState" @cancel="handleCancel" :sendButtonText="sendButtonText">
    <div :class="$style.cta_create" v-loading="loading">
      <div :class="$style.preview">
        <div :class="$style.title">{{ $t('marketing.commons.yl_645dbc') }}</div>
        <img :class="$style.preview_img" v-if="guideComponents.length"  v-preload-i18n-img="guideComponents[0].previewImage">
        <div :class="$style.preview_empty" v-else>{{ $t('marketing.pages.cta.zwylsj_65fa2e') }}</div>
      </div>
      <div :class="$style.content">
        <fx-form ref="form" :model="formData" :rules="rules" label-position="top" label-width="150px" size="small">
          <fx-form-item :label="$t('marketing.commons.zjmc_3791ba')" prop="name">
            <fx-input :placeholder="$t('marketing.commons.qsrmc_06e2f8')" v-model="formData.name"></fx-input>
          </fx-form-item>
          <fx-form-item :label="$t('marketing.pages.cta.cfsjktskqd_e6b9d0')" prop="trigger">
            <div :class="$style.trigger_list">
              <div :class="$style.trigger_item">
                <div :class="$style.icon">
                  <i :class="['iconfont icondianji1']"></i>
                </div>
                <div :class="$style.con">
                  <div :class="$style.title">{{ $t('marketing.commons.djymzjscf_fce3fb') }}</div>
                  <div :class="$style.desc">{{ $t('marketing.pages.cta.yhdjnrzdan_183d36') }}
                  </div>
                  <div :class="$style.memo" v-if="!formData.triggerSettings.onComponentClick.buttonConfig.id">
                    <span>{{ $t('marketing.pages.cta.zwpztyan_b99bc3') }}</span><a @click="handleAddButton">{{ $t('marketing.commons.xz_66ab5e') }}</a>
                  </div>
                  <div :class="$style.memo" v-else>
                    <span>{{ $t('marketing.pages.cta.ypztyan_334783') }}</span><a @click="handleAddButton">{{ $t('marketing.commons.bj_95b351') }}</a>
                  </div>
                </div>
                <div :class="$style.option">
                  <fx-switch :class="$style.switch" size="small"
                    v-model="formData.triggerSettings.onComponentClick.enabled" @change="handleTriggerItemChange" />
                </div>
              </div>
              <div :class="$style.trigger_item">
                <div :class="$style.icon">
                  <i :class="['iconfont iconliulan']"></i>
                </div>
                <div :class="$style.con">
                  <div :class="$style.title">{{ $t('marketing.commons.llnrydjdsc_ae4a8f') }}</div>
                  <div :class="$style.desc">{{ $t('marketing.pages.cta.yhllnrdydj_014a11') }}
                  </div>
                  <div :class="$style.memo">
                    <span>{{ $t('marketing.pages.cta.llnr_8e1917') }}/{{ $t('marketing.pages.cta.gkbfb_7a21d0')
                      }}</span><fx-input style="width: 69px;margin: 0 10px;" type="number" size="small"
                      v-model="formData.triggerSettings.onScrollProgress.duration"
                      :hasFormItem="false"></fx-input><span>%</span>
                  </div>
                </div>
                <div :class="$style.option">
                  <fx-switch :class="$style.switch" size="small"
                    v-model="formData.triggerSettings.onScrollProgress.enabled" @change="handleTriggerItemChange" />
                </div>
              </div>
              <div :class="$style.trigger_item">
                <div :class="$style.icon">
                  <i :class="['iconfont iconshijian1']"></i>
                </div>
                <div :class="$style.con">
                  <div :class="$style.title">{{ $t('marketing.commons.jgydsjhcf_ba6475') }}</div>
                  <div :class="$style.desc">{{ $t('marketing.pages.cta.yhggjrymhz_1615f0') }}
                  </div>
                  <div :class="$style.memo">
                    <span>{{ $t('marketing.commons.cfsj_858ac2') }}</span><fx-input style="width: 69px;margin: 0 10px;"
                      type="number" size="small" v-model="formData.triggerSettings.onTimeElapsed.duration"
                      :hasFormItem="false"></fx-input><span>{{ $t('marketing.commons.m_0c1fec') }}</span>
                  </div>
                </div>
                <div :class="$style.option">
                  <fx-switch :class="$style.switch" size="small"
                    v-model="formData.triggerSettings.onTimeElapsed.enabled" @change="handleTriggerItemChange" />
                </div>
              </div>
              <!-- <div :class="$style.trigger_item">
                <div :class="$style.icon">
                  <i :class="['iconfont icontuichu']"></i>
                </div>
                <div :class="$style.con">
                  <div :class="$style.title">计划离开页面时触发</div>
                  <div :class="$style.desc">点击关闭浏览器窗口，或者返回上一步时触发，如进入详情页后，未留咨就退出时，直接出现引导组件
                  </div>
                </div>
                <div :class="$style.option">
                  <fx-switch :class="$style.switch" size="small" v-model="formData.triggerSettings.onExitIntent.enabled"
                    @change="handleTriggerItemChange" />
                </div>
              </div> -->
            </div>
          </fx-form-item>
          <fx-form-item :label="$t('marketing.pages.cta.ydzjzczdpz_fe58e6')" prop="guideComponents">
            <div :class="$style.components">
              <Draggable :value="guideComponents" handle=".comp-handle" @input="handleGuideComponentsSortChange">
                <div :class="$style.component_item" v-for="(item, index) in guideComponents" :key="index">
                  <div :class="$style.con">
                    <div :class="$style.title"><i
                        :class="[$style.drag, 'iconfont', 'icontuodongpaixu', 'comp-handle']"></i>{{
                          item.componentName }}</div>
                    <div :class="$style.memo">
                      <div :class="$style.memo_item" v-if="item.componentType === 1">
                        <span :class="$style.memo_key">{{ $t('marketing.commons.wymbd_2c8d12') }}</span>
                        <span>{{ item.siteName }}</span>
                      </div>
                      <div :class="$style.memo_item" v-if="item.componentType === 2">
                        <span :class="$style.memo_key">{{ $t('marketing.commons.hydly_1e0dc7') }}</span>
                        <span>{{ item.loginPageType === 1 ? $t('marketing.pages.cta.yddhydlym_0c2757') :
                          $t('marketing.pages.cta.ddlym_5f2624') }}</span>
                      </div>
                      <div :class="$style.memo_item" v-if="item.componentType === 3 || item.componentType === 5">
                        <span :class="$style.memo_key">{{ $t('marketing.commons.gzh_215fee') }}</span>
                        <span>{{ item.wxAppName }}</span>
                      </div>
                      <div :class="$style.memo_item" v-if="item.componentType === 5">
                        <span :class="$style.memo_key">{{ $t('marketing.commons.qdewm_07ba01') }}</span>
                        <span>{{ item.qrCodeName }}</span>
                      </div>
                      <div :class="$style.memo_item" v-if="item.componentType === 6">
                        <span :class="$style.memo_key">{{ $t('marketing.pages.cta.qywxqdewm_6b5e79') }}</span>
                        <span>{{ item.wxworkQrCodeName }}</span>
                      </div>
                      <div :class="$style.memo_item">
                        <span :class="$style.memo_key">{{ $t('marketing.commons.sfyxtg_138f5e') }}</span>
                        <span>{{ item.allowSkip ? $t('marketing.commons.yx_e6a5c3') :
                          $t('marketing.commons.byx_e06828') }}</span>
                      </div>
                    </div>
                  </div>
                  <div :class="$style.option">
                    <i :class="[$style.delete, 'iconfont', 'iconshanchu2']" @click="handleDelete(index)"></i>
                    <i :class="[$style.edit, 'iconfont', 'iconbianji']" @click="handleEdit(index)"></i>
                  </div>
                </div>
              </Draggable>
              <div v-if="guideComponents.length < 3" :class="[$style.component_item, $style.add]"  @click="handleAddAction">
                <fx-button :class="$style.addbtn" size="small" type="text"
                  icon="fx-icon-add-2">{{ $t('marketing.commons.xzydzj_91a4f9')
                  }}</fx-button>
              </div>
            </div>
          </fx-form-item>
        </fx-form>
      </div>
      <ActionDialog v-if="actionDialogVisible" :visible="actionDialogVisible" :activeIndex="actionActiveIndex" :data="guideComponents" :miniapp_accesspermissions_setting="formData.settings && formData.settings.miniapp_accesspermissions_setting || {}"
        @cancel="handleActionCancel" @confirm="handleActionConfirm" @miniappAuthSettingChange="handleSettingsChange" />
      <CtaButton v-if="ctaButtonVisible" :visible="ctaButtonVisible" :data="formData.triggerSettings.onComponentClick.buttonConfig" @submit="handleCtaButtonSubmit" @close="handleCtaButtonClose" />
    </div>
  </v-scaffold>
</template>

<script>
import Draggable from "vuedraggable"
import http from "@/services/http/index";
import VScaffold from '@/pages/promotion-activity/common/scaffold.vue';
import ActionDialog from "./components/actions-dialog";
import kisvData from "@/modules/kisv-data";
import { watchFormValidateFieldHandler } from "@/utils/index";
import { actions } from "./components/actions";
import CtaButton from "./components/cta-button.vue";
import { safeJsonParse } from './util';


const actionMaps = actions.reduce((acc, cur) => {
  acc[cur.value] = cur;
  return acc;
}, {});

export default {
  components: {
    Draggable,
    VScaffold,
    ActionDialog,
    CtaButton
  },
  data() {
    const { id, type } = this.$route.query;
    return {
      ctaButtonVisible: false,
      loading: false,
      sendButtonText: $t('marketing.commons.bc_be5fbb'),
      vDatas: kisvData.datas,
      btnState: [],
      actionDialogVisible: false,
      actionActiveIndex: -1,
      formData: {
        settings: {
          miniapp_accesspermissions_setting: null,
        },
        name: "",
        triggerSettings: {
          onComponentClick: {
            triggerType: 1,
            enabled: false,
            customButtonEnabled: false,
            buttonConfig: {}
          },
          onScrollProgress: {
            triggerType: 2,
            enabled: false,
            duration: 0
          },
          onTimeElapsed: {
            triggerType: 3,
            enabled: false,
            duration: 0
          },
          onExitIntent: {
            triggerType: 4,
            enabled: false,
          },
        },
        guideComponents: []
      },
      rules: {
        name: [{ required: true, message: $t('marketing.commons.qsrmc_06e2f8'), trigger: 'change' }],
        trigger: [{
          required: true, message: $t('marketing.pages.cta.qszcfsj_d1f7d4'), trigger: 'change', validator: (rule, value, callback) => {
            //只要有任意事件类型开启即可通过校验
            if (Object.values(this.formData.triggerSettings).some(item => item.enabled)) {
              callback();
            } else {
              callback(new Error($t('marketing.pages.cta.qszcfsj_d1f7d4')))
            }
          }
        }],
        guideComponents: [{
          required: true, message: $t('marketing.pages.cta.tjydzj_454bc5'), trigger: 'change', validator: (rule, value, callback) => {
            const { guideComponents } = this.formData;
            if (guideComponents.length) {
              callback();
            } else {
              callback(new Error($t('marketing.pages.cta.tjydzj_454bc5')))
            }
          }
        }],
      }
    };
  },
  computed: {
    guideComponents() {
      return this.formData.guideComponents.map(item => ({
        ...item,
        componentName: actionMaps[item.componentType].name,
        previewImage: actionMaps[item.componentType].image,
      }))
    }
  },
  watch: {
    ...watchFormValidateFieldHandler(
      [
        {
          watchField: "formData.marketingObj",
          validateField: "marketingObj"
        }
      ],
      "form"
    )
  },
  created() {
    this.queryDetail();
  },
  methods: {
    handleAddButton() {
      this.ctaButtonVisible = true;
    },
    handleCtaButtonClose() {
      this.ctaButtonVisible = false;
    },
    handleCtaButtonSubmit(data) {
      this.formData.triggerSettings.onComponentClick.buttonConfig = data;
      this.ctaButtonVisible = false;
    },
    //新增引导组件
    handleAddAction() {
      this.actionDialogVisible = true;
      this.actionActiveIndex = -1;
    },
    handleGuideComponentsSortChange(value) {
      this.formData.guideComponents = value;
    },
    //删除引导组件
    handleDelete(index) {
      this.formData.guideComponents.splice(index, 1);
    },
    //编辑引导组件
    handleEdit(index) {
      this.actionDialogVisible = true;
      this.actionActiveIndex = index;
    },
    handleActionConfirm(data) {
      this.actionDialogVisible = false;
      //新增引导组件
      if (this.actionActiveIndex === -1) {
        this.formData.guideComponents.push(data);
      } else {
        //编辑引导组件时，将表单数据赋值
        this.$set(this.formData.guideComponents, this.actionActiveIndex, data);
      }
    },
    handleSettingsChange(data) {
      if(!this.formData.settings) {
        this.formData.settings = {};
      }
      this.formData.settings.miniapp_accesspermissions_setting = data;
    },
    handleActionCancel() {
      this.actionDialogVisible = false;
    },
    handleTriggerItemChange() {

    },
    validateForm() {
      this.$refs["form"].validate(valid => {
        if (!valid) return false;
        this.handleSubmit();
      });
    },
    queryDetail() {
      if (!this.$route.query.id) return;
      this.loading = true;
      http
        .queryCtaDetail({
          id: this.$route.query.id,
        })
        .then(({ errCode, data = {} }) => {
          this.loading = false;
          if (errCode === 0) {
            const res = data || {}
            this.formData = {
              ...this.formData,
              ...res,
              triggerSettings: safeJsonParse(res.triggerSettings, {}),
              guideComponents: safeJsonParse(res.guideComponents, [])
            }
            console.log('queryCtaDetail:', data, this.formData)
          }
        })
    },
    handleSubmit() {
      const { id, type, groupId } = this.$route.query;
      if (type === 'copy') {
        delete this.formData.id;
      }
      //如果引导组件内存在表单formId，需要将表单formId提取并传参，没有时将置为空
      if (!this.formData.guideComponents.some(item => {
        if (item.componentType === 1 && item.formId) {
          this.formData.formId = item.formId;
          return true;
        }
      })) {
        //否值移除formId，防止后端报错
        delete this.formData.formId;
      }

      if (groupId) {
        this.formData.groupId = groupId;
      }

      http[id && type !== 'copy' ? "updateCta" : "addCta"]({
        ...this.formData,
        triggerSettings: JSON.stringify(this.formData.triggerSettings),
        guideComponents: JSON.stringify(this.formData.guideComponents)
      })
        .then(({ errCode }) => {
          if (errCode === 0) {
            this.handleComplete();
          }
        })
        .catch(() => { })
        .then(() => {
          this.btnState = [];
        });
    },
    handleComplete() {
      this.handleCancel();
    },
    handleCancel() {
      this.$router.back();
      window.close();
    }
  },
};
</script>

<style lang="less" module>
.cta_create {
  flex: 1 1 auto;
  display: flex;

  .preview {
    width: 435px;
    min-height: 745px;
    background: #fff;
    padding: 20px 30px;
    box-sizing: border-box;
    border-right: 1px solid #e9edf5;

    .preview_img,
    .preview_empty {
      margin-top: 10px;
      width: 375px;
      height: 744px;
      background-color: #f4f6f9;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #666;
    }
  }

  .content {
    flex: 1;
    min-width: 0;
    max-width: 1043px;
    padding: 30px;

    .trigger_list {}

    .trigger_item {
      display: flex;
      padding: 12px 20px;
      border-radius: var(--spacing-6, 6px);
      border: 1px solid var(--color-neutrals05);
      margin-bottom: 20px;

      .icon {
        width: 54px;
        height: 54px;
        flex-shrink: 0;
        border-radius: 8px;
        background: var(--color-info01);
        color: var(--color-info06);
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 12px;

        i {
          font-size: 24px;
        }
      }

      .con {
        flex: 1;

        .title {
          color: var(--color-neutrals19);
          font-size: 16px;
          font-weight: 400;
          line-height: 24px;
        }

        .desc {
          color: var(--color-neutrals11);
          font-size: 14px;
          font-weight: 400;
          line-height: 20px;
          margin-top: 6px;
        }

        .memo {
          color: var(--color-neutrals19);
          font-size: 14px;
          font-weight: 400;
          line-height: 20px;
          margin-top: 10px;
          display: flex;
          align-items: center;

          a {
            margin-left: 20px;
            cursor: pointer;
          }
        }
      }
    }

    .component_item {
      display: flex;
      align-items: center;
      padding: 12px 20px;
      border-radius: var(--spacing-6, 6px);
      border: 1px solid var(--color-neutrals05);
      margin-bottom: 20px;
      background-color: #fff;
      &.add{
        justify-content: center;
        cursor: pointer;
      }

      .addbtn {
        align-self: center;
        justify-self: center;
      }

      .con {
        flex: 1;

        .title {
          color: var(--color-neutrals19);
          font-size: 16px;
          font-weight: 400;
          line-height: 24px;

          .drag {
            margin-right: 10px;
            color: #737C8C;
            cursor: pointer;
          }
        }

        .memo {
          margin-top: 10px;
          display: flex;

          &_item {
            color: var(--color-neutrals19);
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px;
            margin-right: 30px;
          }

          &_key {
            color: var(--color-neutrals11);
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px;
            margin-right: 10px;
          }
        }
      }

      .option {
        :global(.iconfont) {
          margin-left: 10px;
          color: #545861;
          cursor: pointer;
        }
        .delete {
          font-size: 19px;
        }
      }
    }
  }
}
</style>
