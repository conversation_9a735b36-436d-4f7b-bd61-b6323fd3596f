import _ from 'lodash'
import { formatStringByEmpty } from '@/utils/index.js'
import util from '@/services/util/index.js'

const formatter = (rowData, column, cellValue) => formatStringByEmpty(cellValue)

export default {
  getColumns(data = {}, showOperation = true) {
    const columns = []
    columns.push({
      prop: 'objectName',
      label: $t('marketing.pages.cta.nrmc_64eaec'),
      minWidth: 100,
      formatter,
    })
    columns.push({
      prop: 'objectTypeName',
      label: $t('marketing.commons.lx_226b09'),
      minWidth: 120,
      formatter,
    })
    columns.push({
      prop: 'triggerCount',
      label: $t('marketing.commons.cfcs_ed25e7'),
      minWidth: 120,
      formatter,
    })

    columns.push({
      prop: 'wechatNicknameCount',
      label: $t('marketing.pages.cta.wxsqtxncs_347d51'),
      minWidth: 120,
      formatter,
    })
    columns.push({
      prop: 'publicAccountFansCount',
      label: $t('marketing.pages.cta.xzgzhfss_075a88'),
      minWidth: 120,
      formatter,
    })
    columns.push({
      prop: 'addQywxFriendsCount',
      label: $t('marketing.pages.cta.xzqywxhys_e381f3'),
      minWidth: 120,
      formatter,
    })
    columns.push({
      prop: 'submitFormCount',
      label:$t('marketing.commons.hqxss_73f905'),
      minWidth: 120,
      formatter,
    })
    columns.push({
      prop: 'wechatPhoneCount',
      label: $t('marketing.pages.cta.wxsqsjhcs_51cfc9'),
      minWidth: 120,
      formatter,
    })
    columns.push({
      prop: 'memberLoginCount',
      label: $t('marketing.pages.cta.hydlcs_0dddf9'),
      minWidth: 120,
      formatter,
    })
    columns.push({
      prop: 'memberRegisterCount',
      label: $t('marketing.pages.cta.hyzcrs_56555c'),
      minWidth: 120,
      formatter,
    })
    columns.push({
      prop: 'publicAccountQrCount',
      label: $t('marketing.pages.cta.gzhsmcs_8ce891'),
      minWidth: 120,
      formatter,
    })
    return columns
  },
  getDatas(result, data = {}) {
    const datas = []
    const list = _.cloneDeep(result)
    list.forEach((item, index) => {
      const _pushItem = Object.assign(item)

      const objectTypeMaps  = {
        26: $t('marketing.commons.wym_5fd4fb'),
        27: $t('marketing.commons.wym_5fd4fb'),
        6: $t('marketing.commons.wz_c75625'),
        4: $t('marketing.commons.cp_a01543'),
        16: $t('marketing.commons.xsbd_102b1f'),
        9999: $t('marketing.commons.wbnr_6ecce8'),
        1: $t('marketing.commons.mp_758853'),
        28: $t('marketing.commons.gw_847652'),
      }
      _pushItem.objectTypeName = objectTypeMaps[_pushItem.objectType]

      

      datas.push(_pushItem)
    })

    return datas
  },
}
