import _ from 'lodash'
import { formatStringByEmpty } from '@/utils/index.js'
import util from '@/services/util/index.js'
import { getTriggerNames, getGuideComponentNames, safeJsonParse } from './util'

const formatter = (rowData, column, cellValue) => formatStringByEmpty(cellValue)


export default {
  getColumns(setting = {
    showOperation: true,
    selection: false
  }, data = {}) {
    const columns = []
    if (setting.selection) {
      columns.push({
        prop: 'selection',
        label: '',
        exComponent: 'slot',
        slotName: 'singleSelection',
        width: 36
      })
    }
    columns.push({
      prop: 'name',
      label: $t('marketing.commons.zjmc_3791ba'),
      minWidth: 160,
      formatter,
    })
    columns.push({
      prop: 'triggerNames',
      label: $t('marketing.commons.cffs_159dbc'),
      minWidth: 120,
      formatter,
    })
    columns.push({
      prop: 'triggerButton',
      label: $t('marketing.commons.dlan_22d15c'),
      minWidth: 100,
      unselected: true,
      formatter,
    })

    columns.push({
      prop: 'guideComponentNames',
      label: $t('marketing.pages.cta.lzzjjgz_113ae8'),
      minWidth: 120,
      formatter,
    })
    // columns.push({
    //   prop: 'cardStatus',
    //   label: '关联内容',
    //   minWidth: 120,
    //   formatter,
    // })
    // columns.push({
    //   prop: 'personObjName',
    //   label: '触发次数',
    //   minWidth: 120,
    //   exComponent: 'text',
    //   formatter,
    // })
    columns.push({
      prop: 'createBy',
      label: $t('marketing.commons.cjr_95a43e'),
      minWidth: 120,
      formatter,
    })
    columns.push({
      prop: 'createTime',
      label: $t('marketing.commons.cjsj_eca37c'),
      minWidth: 120,
      formatter,
    })
    columns.push({
      prop: 'updateBy',
      label: $t('marketing.commons.gxr_cfe71a'),
      minWidth: 120,
      unselected: true,
      formatter,
    })
    columns.push({
      prop: 'updateTime',
      label: $t('marketing.commons.gxsj_a001a2'),
      minWidth: 120,
      unselected: true,
      formatter,
    })
    if (setting.showOperation) {
      columns.push({
        prop: 'operations',
        label: $t('marketing.commons.cz_2b6bc0'),
        width: 140,
        exComponent: 'slot',
        slotName: 'operations',
        unsettable: true,
        fixed: 'right',
      });
    }

    return columns
  },
  getDatas(result, data = {}) {
    const datas = []
    const list = _.cloneDeep(result)
    list.forEach((item, index) => {
      const _pushItem = Object.assign(item)

      const triggerSettings = safeJsonParse(_pushItem.triggerSettings, {})
      const guideComponents = safeJsonParse(_pushItem.guideComponents, [])

      _pushItem.triggerNames = getTriggerNames(_pushItem.triggerSettings)

      _pushItem.triggerButton = triggerSettings.onComponentClick && triggerSettings.onComponentClick.customButtonEnabled && $t('marketing.pages.cta.y_cc4ad2') || $t('marketing.commons.w_d81bb2')

      _pushItem.guideComponentNames = getGuideComponentNames(_pushItem.guideComponents)

      const createEmployee = FS.contacts.getEmployeeById(_pushItem.createBy) || {}
      _pushItem.createBy = createEmployee.name;

      const updateEmployee = FS.contacts.getEmployeeById(_pushItem.updateBy) || {}
      _pushItem.updateBy = updateEmployee.name;

      _pushItem.createTime = util.formatDateTime(
        _pushItem.createTime,
        "YYYY-MM-DD hh:mm"
      );

      _pushItem.updateTime = util.formatDateTime(
        _pushItem.updateTime,
        "YYYY-MM-DD hh:mm"
      );

      _pushItem.operation = [
        {
          id: "copy",
          name: $t('marketing.commons.fz_79d3ab')
        },
        {
          id: "edit",
          name: $t('marketing.commons.bj_95b351')
        },
        {
          id: "sdk",
          name: $t('marketing.pages.cta.fz_0f39a7')
        },
        {
          id: "delete",
          name: $t('marketing.commons.sc_2f4aad')
        }
      ];

      datas.push(_pushItem)
    })

    return datas
  },
}
