import http from '@/services/http';

//JSON 解析函数
const safeJsonParse = (str, defaultValue) => {
  if (!str) return defaultValue
  try {
    return JSON.parse(str)
  } catch (error) {
    return defaultValue
  }
}

//获取事件名称
const getTriggerNames = (triggerSettings) => {

  const settings = safeJsonParse(triggerSettings, {})
  const keyNames = {
    onComponentClick: $t('marketing.pages.cta.djymzj_f4b515'),
    onScrollProgress: $t('marketing.commons.llnrydjdsc_ae4a8f'),
    onTimeElapsed: $t('marketing.commons.jgydsjhcf_ba6475'),
    onExitIntent: $t('marketing.pages.cta.jhlkymscf_66c449')
  }
  return settings instanceof Array ? '--' : Object.keys(settings).reduce((pre, key) => {
    if(!settings[key].enabled){
      return pre;
    }
    if (pre) {
      pre += "、"// [ignore-i18n]
    }
    pre += (keyNames[key] || '--');
    return pre;
  }, "")
}

const getGuideComponentLists = (guideComponents) => {
  const components = (typeof guideComponents ==='string' ? safeJsonParse(guideComponents,[]) : guideComponents) || []
  return components.map(item => ({
    ...item,
    componentName: ['', $t('marketing.commons.tjbd_4e3400'), $t('marketing.commons.yzhysf_a1d6ad'), $t('marketing.commons.sqwxtxnc_a60289'), $t('marketing.commons.sqwxsjh_1f70b1'), $t('marketing.commons.gzgzh_8f54bf'), $t('marketing.pages.cta.tjqwhy_02d94e')][item.componentType] || '--',
  }))
}

const getGuideComponentNames = (guideComponents) => {
  const components = getGuideComponentLists(guideComponents);
  return components.map(item => item.componentName).join('>')
}

//根据引导组件类型获取可展示数据字段
const getGuideComponentDataFields = (guideComponents) => {
  const components = (typeof guideComponents ==='string' ? safeJsonParse(guideComponents,[]) : guideComponents) || []
  const dataFields = [];
  components.forEach(item => {
    if (item.componentType === 1) {
      dataFields.push({
        label: $t('marketing.commons.hqxss_73f905'),
        fieldName: 'submitFormCount',
      })
    } else if (item.componentType === 2) {
      dataFields.push(...[{
        label: $t('marketing.pages.cta.hydlcs_0dddf9'),
        fieldName: 'memberLoginCount',
      },{
        label: $t('marketing.pages.cta.hyzcrs_56555c'),
        fieldName: 'memberRegisterCount',
      }])
    } else if (item.componentType === 3) {
      dataFields.push({
        label: $t('marketing.pages.cta.wxsqtxncs_347d51'),
        fieldName: 'wechatNicknameCount',
      })
    } else if (item.componentType === 4) {
      dataFields.push({
        label: $t('marketing.pages.cta.wxsqsjhcs_51cfc9'),
        fieldName: 'wechatPhoneCount',
      })
    } else if (item.componentType === 5) {
      dataFields.push(...[{
        label: $t('marketing.pages.cta.gzhsmcs_8ce891'),
        fieldName: 'publicAccountQrCount',
      },{
        label: $t('marketing.pages.cta.xzgzhfss_075a88'),
        fieldName: 'publicAccountFansCount',
      }])
    } else if (item.componentType === 6) {
      dataFields.push({
        label: $t('marketing.pages.cta.xzqwhys_e6faf7'),
        fieldName: 'addQywxFriendsCount',
      })
    }
  })
  return dataFields;
}

//删除cta组件
const deleteCTAById = (id, success) => {
  //删除前确认
  FxUI.MessageBox.confirm(
    $t('marketing.pages.cta.cczjyjsccz_60d470'),
    $t('marketing.commons.ts_02d981'),
    {
      confirmButtonText: $t('marketing.commons.qd_38cf16'),
      cancelButtonText: $t('marketing.commons.qx_625fb2'),
    },
  )
    .then(() => {
      http.deleteCta({
        id: id
      }).then(({ errCode, errMsg }) => {
        if (errCode === 0) {
          FxUI.Message.success($t('marketing.commons.sccg_0007d1'));
          if(success){
            success();
          }
        } else {
          FxUI.Message.error(errMsg);
        }
      });
    })
    .catch(() => {
    })
}

function copyTextToClipboard(text) {
  if (navigator.clipboard && window.isSecureContext) {
    // 使用 Clipboard API 复制文本（仅限安全上下文，如 HTTPS）
    navigator.clipboard.writeText(text)
      .then(() => {
        FxUI.Message.success($t('marketing.commons.fzcg_8a4831'))
      })
      .catch(err => {
        FxUI.Message.error($t('marketing.commons.fzsb_cd9817'))
      });
  } else {
    // 使用 document.execCommand 作为回退方案（适用于旧版浏览器）
    const textArea = document.createElement('textarea');
    textArea.value = text;
    // 确保 textarea 不会影响页面布局
    textArea.style.position = 'fixed';
    textArea.style.top = '-1000px';
    textArea.style.left = '-1000px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
      const successful = document.execCommand('copy');
      if (successful) {
        FxUI.Message.success($t('marketing.commons.fzcg_8a4831'))
      } else {
        FxUI.Message.error($t('marketing.commons.fzsb_cd9817'))
      }
    } catch (err) {
      FxUI.Message.error($t('marketing.commons.fzsb_cd9817'))
    } finally {
      document.body.removeChild(textArea);
    }
  }
}

export {
  safeJsonParse,
  getTriggerNames,
  getGuideComponentNames,
  getGuideComponentLists,
  deleteCTAById,
  copyTextToClipboard,
  getGuideComponentDataFields,
}
