<template>
  <div class="sdr-kanban">
    <div
      ref="dashboard"
      class="lw__body-dashboard"
    />
    <BaseShareGPT default-helper-name="Copilot_10Rmv__c" />
  </div>
</template>

<script>
import BaseShareGPT from './base-share-gpt.vue'
import { requireAsync } from '@/utils/index.js'

export default {
  components: {
    BaseShareGPT,
  },
  data() {
    return {
      loading: false,
      title: 'SDR 运营看板',
    }
  },
  computed: {
    dashboardId() {
      return 'BI_DSB_681b1d50c9f3ff0001700295'
    },
    dashboardName() {
      return 'SDR 运营看板'
    },
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      const me = this
      this.title = this.dashboardName
      if (!this.dashboardId) {
        FxUI.Message.error($t('marketing.pages.data_cockpit.sjjscbnwk_98d5f0'))
        return
      }
      this.loading = true

      $(me.$refs.dashboard).empty() // 在驾驶舱切换时，清空上一个驾驶舱
      me.dashboardView = null // 内存回收

      requireAsync('bi-modules/components/pages/dashboard/dashboard', DashboardVue => {
        me.dashboardView = new DashboardVue({
          propsData: {
            opt: {
              id: me.dashboardId, // 市场活动分析数据驾驶舱的id
            },
          },
        })
        me.dashboardView.$on('already', () => {
          // 驾驶舱基础数据已拉取到了
          me.loading = false
          // const dashboardcolor = me.dashboardView.getDashboardColor() // 获取驾驶舱配色

          me.$refs.dashboard.appendChild(me.dashboardView.$mount().$el) // 挂载位置
          // me.dashboardView.enterFullScreen();
        })

        me.dashboardView.$on('enterFullScreen', () => {
          // 进入全屏回调
        })

        me.dashboardView.$on('exitFullScreen', () => {
          // 退出全屏回调
        })
      })
    },
  },
  destroy() {
    this.dashboardView.$el.remove()
    this.dashboardView.$destroy()
  },
}
</script>

<style lang="less">
.sdr-kanban {
  height: 100%;
  box-sizing: border-box;
  overflow-y: auto;
  display: flex;
  gap: 10px;
  background-color: #fff;
  border-radius: 8px;

  .lw__body-dashboard {
    flex: 1;

    .bi-dashboard-common-wrapper {
      margin: 0;
      width: 100%;
    }
  }
}
</style>
