<template>
  <div>
    <!-- crm list组件有bug，自身设置了height: calc(100% - 12px)!important;导致这里的失效，加入!important强行覆盖一下 -->
     <!-- 因为在父元素没有设置height属性的情况，calc(100% - 12px)是有问题，进而导致列表的高度为0 -->
    <div
      ref="ad-passback-table"
      style="height:calc(100vh - 112px) !important"
    />
  </div>
</template>
<script>
import { requireAsync } from '@/utils/index.js'

export default {
  data() {
    return {}
  },
  mounted() {
    this.initPageList()
  },
  methods: {
    initPageList() {
      requireAsync('crm-modules/page/list/list', List => {
        const passbackTable = new List({
          wrapper: this.$refs['ad-passback-table'],
          apiname: 'AdDataReturnDetailObj',
          showMultiple: true, // 不显示复选框
          // noAlwaysShowPage: true,
          // title: '11',
          autoHeight: false, // 是否和内容等高
        })

        const originTrclickHandle = passbackTable.trclickHandle
        passbackTable.trclickHandle = (data, $tr, $target) => {
          originTrclickHandle.bind(passbackTable)(data, $tr, $target)
          console.log(data)
        }

        passbackTable.render()

        passbackTable.getOperateBtns = () => ([
          {
            action: 'repassback',
            text: $t('marketing.pages.ad_marketing.zxhc_005410'),
            index: 1,
          },
        ])
        passbackTable.repassback = () => {
          console.log('repassback========')
          const checkedData = passbackTable.getCheckedData()
          if (!checkedData.length) {
            return
          }

          const params = {
            idList: checkedData.map(data => data._id),
          }
          FS.util.ajax({
            url: '/FHH/EM8HMARKETING/ocpc/reUploadAdData',
            type: 'post',
            headers: {
              'Content-Type': 'application/json',
            },
            data: JSON.stringify(params),
            success: res => {
              const { errCode } = res.Value
              if (errCode === 0) {
                FS.util.remind(0, $t('重新回传成功！'))
                passbackTable.refresh()
              } else {
                FS.util.remind(3, $t('重新回传失败！'))
              }
            },
            error: err => {
              console.log(err)
              FS.util.remind(3, $t('重新回传失败！'))
            },
          })
        }
      })
    },
  },
}
</script>
