<!-- 组件说明 -->
<template>
  <div class="ad-btedance__detail__wrapper">
    <div
      v-loading="loading"
      class="home"
    >
      <content-header
        :title="crumbs"
        :border="true"
        class="header"
      />
      <ad-header
        :campaign-detail="campaignDetail"
        :marketing-event-id="marketingEventId"
      />
      <el-menu
        :default-active="currentTab"
        class="activity-el-menu-wrapper"
        mode="horizontal"
        @select="handleSelect"
      >
        <el-menu-item
          class="set-menu-item"
          index="overview"
        >
          {{ $t('marketing.commons.gggl_ceb8a8') }}
        </el-menu-item>
        <el-menu-item
          class="set-menu-item"
          index="participants"
        >
          {{ $t('marketing.commons.ggxs_5b3e6e') }}
        </el-menu-item>
        <el-menu-item
          class="set-menu-item"
          index="landing"
        >
          {{ $t('marketing.commons.ldy_6e66b9') }}
        </el-menu-item>
        <el-menu-item
          v-for="menu in mainMenusData"
          :key="menu.id"
          class="set-menu-item"
          :index="`custom-component-${menu.id}`"
        >
          {{ menu.name }}
        </el-menu-item>
        <el-submenu
          v-if="moreMenusData && moreMenusData.length"
          class="set-submenu"
          index="more"
        >
          <template slot="title">
            {{ $t('marketing.commons.gd_0ec9ea') }}
          </template>
          <el-menu-item
            v-for="menu in moreMenusData"
            :key="menu.id"
            :index="`custom-component-${menu.id}`"
          >
            {{
              menu.name
            }}
          </el-menu-item>
        </el-submenu>
        <div class="date-selector">
          <v-date-range-picker
            v-if="currentTab === 'overview'"
            ref="drp"
            :pick-menu="pickMenu"
            :custom-show-time="true"
            :custom-end-time-count="false"
            abort-hours
            @update:picked="handleDatePicker"
          />
        </div>
      </el-menu>
      <component
        :is="currentTab"
        v-if="readyRenderComponent"
        :id="id"
        ref="marketingComp"
        v-loading="loading"
        :marketing-event-id="marketingEventId"
        :object-detail-data="objectDetailData"
        scene-type="marketing_event"
        :leads="leads"
        :ad-account-id="adAccountId"
        ad-account-type="baidu"
        :campaign-detail="campaignDetail"
        :filter-params="filterParams"
      />
    </div>
  </div>
</template>

<script>
import ContentHeader from '@/components/content-header/index.vue'
import overview from './overview.vue'
import participants from '../../components/participants-table.vue'
import landing from '../../components/landing.vue'
import adHeader from '../../components/ad-header.vue'
import http from '@/services/http/index.js'
import crmLayoutMenusMixin from '@/mixins/crm-layout-menus.js'
import vDateRangePicker from '@/components/date-range-picker/date-range-picker.vue'
import { getTimestampOfPastDay } from '@/utils/date.js'

export default {
  components: {
    adHeader,
    ContentHeader,
    // DashboardHeader,
    ElMenu: FxUI.Menu,
    ElMenuItem: FxUI.MenuItem,
    ElSubmenu: FxUI.Submenu,
    overview,
    participants,
    landing,
    vDateRangePicker,
  },
  mixins: [crmLayoutMenusMixin],
  data() {
    return {
      leads: 0,
      loading: true,
      campaignDetail: {},
      // currentTab: this.$route.query.type ? this.$route.query.type : "overview",
      crumbs: [
        {
          text: $t('marketing.commons.bdgg_694aaa'),
          to: { name: 'ad-baidu' },
        },
        {
          text: '--',
          to: false,
        },
      ],
      headData: {},
      eventType: '',
      eventTypeList: [],
      kanbanNum: '',
      id: this.$route.params.id,
      marketingEventId: this.$route.params.marketingEventId,
      adAccountId: this.$route.query.adAccountId,
      filterParams: {
        startTime: this.getDay(-1),
        endTime: this.getDay(),
      },
      pickMenu: [
        {
          label: $t('marketing.commons.zr_23c9bc'),
          offsetStartDay: -1,
          offsetEndDay: 0,
        },
        {
          label: $t('marketing.commons.r_a863d1'),
          offsetStartDay: -7,
          offsetEndDay: 0,
        },
        {
          label: $t('marketing.commons.r_76b044'),
          offsetStartDay: -30,
          offsetEndDay: 0,
        },
        {
          label: $t('marketing.commons.zdysj_87a294'),
          disabledStart: this.getDay(-365),
          disabledEnd: this.getDay(-1),
          custom: true,
        },
      ],
    }
  },
  computed: {
    currentTab() {
      const { type } = this.$route.query
      return type || 'overview'
    },
    marketingEventType() {
      return (this.campaignDetail && this.campaignDetail.eventType) || ''
    },
  },
  watch: {
    filterParams: {
      handler() {
        this.queryCampaignDetail()
      },
      deep: true,
    },
  },
  mounted() {
  },
  created() {
    this.queryCampaignDetail()
  },
  destroyed() {},
  methods: {
    getDay(offset = 0) {
      return getTimestampOfPastDay(offset)
    },
    queryCampaignDetail() {
      this.loading = true
      const { dataType = '' } = this.$route.query
      const { startTime, endTime } = this.filterParams
      const params = {
        id: this.$route.params.id,
        startTime,
        endTime,
      }

      let promise = null
      if (dataType === 'BAIDU_SEARCH_AD_GROUP') {
        promise = http.queryCampaignAdGroupDetail(params)
      } else if (dataType === 'BAIDU_FEED_AD_GROUP') {
        promise = http.queryCampaignFeedAdGroupDetail(params)
      } else {
        promise = http.queryCampaignDetail(params)
      }

      promise.then(res => {
        this.loading = false
        if (!(res && res.errCode === 0)) {
          return
        }
        this.leads = res.data && res.data.leads
        this.campaignDetail = res.data

        if (dataType === 'BAIDU_SEARCH_AD_GROUP') {
          this.campaignDetail.marketingEventName = this.campaignDetail.adGroupName
        }

        if (dataType === 'BAIDU_FEED_AD_GROUP') {
          this.campaignDetail.marketingEventName = this.campaignDetail.adgroupFeedName
          this.campaignDetail.campaignName = this.campaignDetail.campaignFeedName
        }

        this.crumbs[1].text = this.campaignDetail.marketingEventName
      })
    },
    handleSelect(index) {
      console.log('handleSelect===>', index)
      const { dataType = '' } = this.$route.query
      const route = {
        name: 'ad-baidu-detail',
        params: { id: this.id, marketingEventId: this.marketingEventId },
        query: {
          type: index,
          adAccountId: this.adAccountId,
          dataType,
        },
      }
      // if (index == "radar") {
      //   route.query = { type: index, source: "contentTab" };
      // }
      this.$router.push(route)
    },
    // refresh() {
    //   this.$store
    //     .dispatch("ActivityMarketing/getMarketingEventsDetail", { id: this.id })
    //     .then(res => {
    //       this.headData = res.marketingEvent;
    //       this.crumbs[1].text = res.marketingEvent.name;
    //     });
    // },
    handleDatePicker(_, range) {
      this.filterParams.startTime = range.startTime
      this.filterParams.endTime = range.endTime
    },
  }, // 生命周期 - 销毁完成
}
</script>
<style lang="less" scoped>
.ad-btedance__detail__wrapper {
  background: #f2f2f5;
  height: 100%;
  position: relative;
  .home {
    background: #f2f2f5;
  }
  .activity-el-menu-wrapper {
    margin: 10px 10px 0 10px;
    padding-left: 22px;
    border-top: 1px solid @border-color-base;
    margin-bottom: 10px;
    .date-selector {
      float: right;
      margin-top: 5px;
      /deep/ .date-range-picker-com {
        padding: 0 10px;
        height: 40px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        background: #fff;
        cursor: pointer;
        .com__bar .label {
          background: none;
          padding: 0;
        }
        .com__dropdown {
          top: 40px;
        }
      }
    }
    .set-menu-item {
      margin: 0 40px 0 0;
      padding: 0;
      height: 50px;
      line-height: 52px;
      // &.is-active {
      //   border-bottom-color: #ea7504;
      // }
      .kanban-num {
        font-size: 12px;
        color: #c1c1c1;
        margin-left: 3px;
        vertical-align: top;
      }
    }

    /deep/ .el-submenu {
      .el-submenu__title {
        padding: 0;
        height: 50px;
        line-height: 52px;
      }
    }
    // /deep/ .el-submenu {
    //   &.is-active {
    //     .el-submenu__title {
    //       border-bottom-color: #ea7504;
    //     }
    //   }
    // }

    .mask {
      height: 100%;
      min-width: 1024px;
    }
  }
}
</style>
