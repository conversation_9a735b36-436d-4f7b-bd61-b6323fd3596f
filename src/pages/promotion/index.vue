<template>
  <div :class="$style.promotion">
    <v-header v-if="isMemberPromotion" :title="isMemberPromotion ? $t('marketing.commons.hyyx_b017e9') : $t('marketing.pages.setting.ygyx_f7ac22')" />

    <div class="yxt-mncn-bgpd" style="padding-top: 0">
      <v-cards
        v-if="!enableDataPermission"
        :is-member-promotion="isMemberPromotion"
        :class="$style.card"
      />

      <div :class="$style.spread">
        <div :class="$style.title">
          {{ isMemberPromotion ? $t('marketing.commons.hytg_05ca56') : $t('marketing.commons.qytg_966c63') }}
        </div>
        <div :class="$style.ctrls">
          <fx-input
            v-model="searchVal"
            size="small"
            class="search-input"
            :placeholder="$t('marketing.pages.promotion.ssrwbt_90c429')"
            prefix-icon="el-icon-search"
            style="margin-right: 20px"
            @change="updateNoticeList"
          />
          <fx-button type="primary" size="small" @click="createPrm">
            <i class="yxt-icon16 icon-add" />
            <span>{{ isMemberPromotion ? $t('marketing.commons.xjhytg_36686c') : $t('marketing.commons.xjqytg_4d6a65') }}</span>
          </fx-button>
        </div>
      </div>

      <div v-loading="loading" :class="$style.wnlist">
        <v-table
          :columns="columns"
          :data="dataList"
          :row-style="{ height: '100px' }"
          :header-row-style="{ height: '40px' }"
          @custom:cancel-action="handleCancelSend"
          @custom:again-action="handleSendAgain"
          @refreshTable="getNoticeList"
        >
          <template slot-scope="scope">
            <div
              v-if="scope.col.exComponent === 'custom-content'"
              :class="$style.table_content"
              @click.stop="handleOpenDetail(scope.row)"
            >
              <div :class="$style.title">
                {{ scope.row.title }}
              </div>
              <div v-if="promotionContent(scope.row)" :class="$style.wrapper">
                <div
                  :class="$style.image"
                  :style="{
                    backgroundImage: `url(${promotionContent(scope.row).thumbnail})`,
                  }"
                />
                <div :class="$style.description">
                  <div class="km-t-ellipsis2" style="white-space: initial">
                    {{ promotionContent(scope.row).title }}
                  </div>
                </div>
              </div>
            </div>
            <div
              v-if="scope.col.exComponent === 'custom-marketing'"
              :class="scope.row.marketingEventId && $style.marketing_event"
              @click.stop="handleOpenMarketingDetail(scope.row)"
            >
              {{ scope.col.formatter(null, null, scope.row.marketingEventName) }}
            </div>
          </template>
        </v-table>
      </div>
      <v-pagen :pagedata.sync="pageData" @change="pageChange" />
    </div>
    <sendInvation v-if="showSend" @listenToChildEvent="showMsgFromChild" />
    <market-promotion-details :params="detailParams" :visible="detailVisible" @close="detailVisible = false" />
  </div>
</template>

<script>
import { mapState } from 'vuex';
import VTable from '@/components/table-ex/index.vue';
import VPagen from '@/components/kitty/pagen.vue';
import VCards from './cards.vue';
import VHeader from '@/components/content-header/index.vue';

import { redirectByMarketingEventType } from '@/utils/index.js';
import { confirm, message } from '@/utils/globals.js';
import http from '@/services/http/index.js';
import util from '@/services/util/index.js';
import mateData from '@/modules/mate-data.js';
import MarketPromotionDetails from '@/components/market-promotion-details/index.vue';
import sendInvation from './send-invation.vue';
import columns from './columns.js';
import kisvData from '@/modules/kisv-data.js';
import mailCoverImg from '@/assets/images/mail/mail-cover.png'

export default {
  components: {
    VCards,
    VHeader,
    VTable,
    VPagen,
    sendInvation,
    MarketPromotionDetails,
  },
  data() {
    return {
      loading: true,
      detailVisible: false,
      detailParams: {},
      showSend: false,
      dataList: [],
      pageData: {
        pageNum: 1,
        pageSize: 10,
        totalCount: 0,
      },
      typeNames: mateData.names(),
      searchVal: '',
      mailCoverImg,
    };
  },
  computed: {
    ...mapState('MarketingEventSet', ['marketingActivityAudit']),
    isQywxOpen() {
      return this.$store.state.Global.addressBookType === 'qywx';
    },
    bindRate() {
      const total = this.bindUserCount + this.unOpenUserCount || 1;
      return ((100 * this.bindUserCount) / total).toFixed(1);
    },
    isMemberPromotion() {
      return this.$route.name === 'member-promotion'
    },
    columns() {
      return columns({
        marketingActivityAudit: this.marketingActivityAudit,
        isMemberPromotion: this.isMemberPromotion,
      });
    },
    enableDataPermission() {
      return kisvData.datas.uinfo.marketingDataIsolation;
    },
  },
  beforeDestroy() {
    if (this.$detail) {
      this.$detail.destroy();
    }
  },
  mounted() {
    this.getNoticeList();
    console.log(this, 66666);
  },
  methods: {
    promotionContent(rowData) {
      const { promotionContent, contentType, contentDetail } = rowData

      if (promotionContent) {
        const { thumbnail, title } = promotionContent

        return {
          thumbnail: contentType === 503 ? mailCoverImg : thumbnail,
          title,
        }
      }

      if (contentDetail) {
        const { title, image } = contentDetail

        return {
          thumbnail: image,
          title,
        }
      }

      return null
    },
    handleCancelSend(item) {
      FxUI.MessageBox.confirm($t('marketing.commons.qdychtgrwm_a0e82b'), $t('marketing.commons.ts_02d981'), {
        type: 'warning',
      }).then(() => {
        http
          .fakeCancelMarketingActivityRevokeSend({
            id: item.marketingActivityId,
          })
          .then(({ errCode, errMsg }) => {
            if (errCode === 0) {
              FxUI.Message.success($t('marketing.commons.chcg_52bb3c'));
              this.getNoticeList();
            } else {
              FxUI.Message.error(errMsg || $t('marketing.commons.czsbqshzs_fb5ded'));
            }
          });
      });
    },
    handleOpenDetail(params) {
      // 侧滑打开详情
      const { marketingActivityId } = params;
      if (!marketingActivityId) {
        this.detailVisible = false; // 没有活动 id 的项，点击时无法打开详情
        return;
      }
      if (this.detailParams.id !== marketingActivityId) {
        this.detailParams = {
          id: marketingActivityId,
        };
      }
      this.detailVisible = true;
    },
    handleOpenMarketingDetail(data) {
      redirectByMarketingEventType(data, this.$router);
    },
    createPrm() {
      this.$router.push({
        name: this.isMemberPromotion ? 'promotion-activity-member' : 'promotion-activity-staff',
        params: { type: 'create' },
        query: {
          promotionType: this.isMemberPromotion ? 'memberActivity' : 'staffActivity'
        },
      });
    },
    handleSendAgain(item) {
      this.$router.push({
        name: this.isMemberPromotion ? 'promotion-activity-member' : 'promotion-activity-staff',
        params: { type: 'create' },
        query: {
          marketingActivityId: item.marketingActivityId,
          promotionType: this.isMemberPromotion ? 'memberActivity' : 'staffActivity'
        },
      });
    },
    pageChange(data) {
      Object.assign(this.pageData, data);
      this.getNoticeList();
    },
    showMsgFromChild(data) {
      this.showSend = data;
    },
    preDealDatalist(dataList) {
      return dataList.map((item) => {
        item.sendTime = util.formatDateTime(item.sendTime);
        item.startTime = util.formatDateTime(item.startTime, 'YYYY-MM-DD');
        item.endTime = util.formatDateTime(item.endTime, 'YYYY-MM-DD');
        item._se_Time =
          item.startTime && item.endTime
            ? `${item.startTime} ${$t('marketing.commons.z_981cbe')} ${item.endTime}`
            : '--';
        item.contype = this.typeNames[item.contentType];

        const operations = [];

        if (this.marketingActivityAudit && item.auditStatus === 'normal' && item.spreadStatus === 1) {
          operations.push({
            type: 'marketing-activity-send',
            name: $t('marketing.commons.ljfs_1176c5'),
            marketingActivityId: item.marketingActivityId,
          });
        }

        operations.push({ id: 'again', name: $t('marketing.commons.fz_79d3ab') });
        if (item.sendRevocable) {
          operations.push({ id: 'cancel', name: $t('marketing.commons.chtg_4d5ae4') });
        }
        // if (item.sendCancelable) {
        //   operations.push({ id: "cancel", name: "取消发送" });
        // }
        item.operations = operations;

        return item;
      });

      // for (const i in dataList) {
      //   const item = dataList[i]
      //   item.sendTime = util.formatDateTime(item.sendTime)
      //   item.startTime = util.formatDateTime(item.startTime, 'YYYY-MM-DD')
      //   item.endTime = util.formatDateTime(item.endTime, 'YYYY-MM-DD')
      //   item._se_Time = item.startTime && item.endTime
      //     ? `${item.startTime} ${$t('marketing.commons.z_981cbe')} ${item.endTime}`
      //     : '--'
      //   item.contype = this.typeNames[item.contentType]

      //   const operations = []

      //   if (this.marketingActivityAudit && item.auditStatus === 'normal' && item.spreadStatus === 1) {
      //     operations.push({ type: 'marketing-activity-send', name: $t('marketing.commons.ljfs_1176c5'), marketingActivityId: item.marketingActivityId })
      //   }

      //   operations.push({ id: 'again', name: $t('marketing.commons.fz_79d3ab') })
      //   if (item.sendRevocable) {
      //     operations.push({ id: 'cancel', name: $t('marketing.commons.chtg_4d5ae4') })
      //   }
      //   // if (item.sendCancelable) {
      //   //   operations.push({ id: "cancel", name: "取消发送" });
      //   // }
      //   item.operations = operations
      // }
      // return dataList
    },
    getNoticeList() {
      console.log(this.pageData.pageSize);
      // 获取全员推广列表数据
      const options = {
        title: this.searchVal,
        pageNum: this.pageData.pageNum,
        pageSize: this.pageData.pageSize,
        ...(this.isMemberPromotion ? { spreadType: 13 } : {}),
      };
      this.loading = true;
      http
        .listAllEmployeeMarketingActivity(options)
        .then((res) => {
          if (res.errCode === 0 && res.data) {
            this.dataList = this.preDealDatalist(res.data.result);
            this.pageData.totalCount = res.data.totalCount;
          }
        })
        .catch()
        .then(() => {
          this.loading = false;
        });
    },
    /**
     * 更新列表
     */
    updateNoticeList() {
      this.pageData.pageNum = 1;
      this.getNoticeList();
    },
  },
};
</script>

<style lang="less" module>
.promotion {
  display: flex;
  flex: 1 1 auto;
  flex-direction: column;
  .table_content {
    padding-left: 3px;
    cursor: pointer;
    .title {
      overflow: hidden;
      text-overflow: ellipsis;
      line-height: 1;
      margin-bottom: 10px;
    }
  }
  .marketing_event {
    color: var(--color-info06);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
  }
  .card {
    margin-top: 16px;
  }
  .spread {
    margin-top: 16px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    height: 56px;
    background: #fff;
    .title {
      flex: 1 1 auto;
      font-size: 14px;
      color: @color-title;
    }
    .ctrls {
      align-self: center;
    }
  }

  .wnlist {
    border-top: 1px solid #ececec;
    border-radius: 2px;
    overflow-y: auto;
    background-color: #fff;
    .header-cell {
      background: #f9fbff;
    }
  }

  .wrapper {
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    height: 50px;
    background: #fafafa;
    border-radius: 3px;
    .image {
      flex: none;
      width: 70px;
      height: 50px;
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
    }
    .description {
      margin: 0 12px;
      min-width: 0;
      font-size: 12px;
      line-height: 1.5;
    }
  }
  .wrapper:hover {
    background: #eaeff6;
  }
}
</style>
