<template>
  <div :class="$style.vibeMarketingHistoryAgentContent">history</div>
</template>

<script>
import BaseAgentContent from '../../conponents/BaseAgent/components/Content.vue'

export default {
  name: 'HistoryAgentContent',
  extends: BaseAgentContent,
  props: {
    activeAsset: {
      type: Object,
      default: null,
    },
    agentContext: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {}
  },
  mounted() {},
  methods: {
    /**
     * Handle agent message from chat
     */
    onAgentMessage(data) {
      console.log('HistoryAgent received message:', data)
      // Handle history-specific agent messages
    },
  },
}
</script>

<style lang="less" module>
.vibeMarketingHistoryAgentContent {
  height: 100%;
}
</style>
