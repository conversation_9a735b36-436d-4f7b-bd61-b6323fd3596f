<script>
import BaseAgent from '../conponents/BaseAgent/index.vue'
import Content from './components/Content.vue'

export default {
  components: {
    Content,
  },
  extends: BaseAgent,
  data() {
    return {
      agentApiName: '',
    }
  },
  methods: {
    getCrumbs() {
      const { title } = this.$route.query
      return [
        {
          text: 'Vibe Marketing',
          to: { name: 'vibe-marketing-init' },
        },
        {
          text: '工作空间',
          to: { name: 'vibe-marketing-workspace' },
        },
        {
          text: decodeURIComponent(title || 'AI智能沟通'),
          to: false,
        },
      ]
    },
    getSessionId() {
      const { sessionId } = this.$route.query
      return {
        sessionId: sessionId || '',
        bizSessionId: '',
      }
    },
  },
}
</script>
