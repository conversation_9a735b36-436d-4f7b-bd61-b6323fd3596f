<script>
import BaseAgent from '../conponents/BaseAgent/index.vue'
import Content from './components/Content.vue'
export default {
  extends: BaseAgent,
  components: {
    Content,
  },
  data() {
    return {}
  },
  methods: {
    onAgentReady(agent) {
      agent.sendConfigData({
        bizSessionId: this.getSessionId(),
      })

      agent.eventBus.on('onInitMessageLoaded', event => {
        setTimeout(() => {
          this.handleSend()
        }, 2000)
      })
    },
    handleSend(data) {
      let payload = data

      if (!payload) {
        try {
          const key = `template_${this.$route.query.templateId}`
          payload = JSON.parse(sessionStorage.getItem(key))
        } catch (error) { /* empty */ }
      }

      if (!payload) return
      const {
        activeAgentId = '',
        defaultHelperName = '',
        objects = [],
        prompt = '',
        templateId = '',
      } = payload

      this.$context.agent.sendConfigData({
        activeAgentId,
        defaultHelperName,
      })

      const message = {
        msg: prompt,
        templateId,
        objects,
      }
      this.$context.agent.sendMessageToAI(message, res => {
        console.log('sendMessageToAI', res)
        const { errMsg, errCode } = res
        if (errCode === 0 && errMsg === 'receivePeriod:resolve') {
          this.$context.$content.updateData('message', res.message)
        }
      })
    },
  }
}
</script>
