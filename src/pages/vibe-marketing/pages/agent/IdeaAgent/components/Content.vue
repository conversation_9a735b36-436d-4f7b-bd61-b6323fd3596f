<template>
  <ArticleTinymceEditor
    :height="editorHeight"
    :content="message && message.previewContent"
    @setEditorContent="handleSetEditorContent"
  />
</template>

<script>
import BaseAgentContent from '../../conponents/BaseAgent/components/Content.vue'
import ArticleTinymceEditor from '@/pages/article/edit/ArticleTinymceEditor.vue'
export default {
  name: 'ArticleAgentContent',
  extends: BaseAgentContent,
  components: {
    ArticleTinymceEditor,
  },
  data() {
    return {
      editorHeight: 500,
      message: {},
    }
  },
  mounted() {
    // 这里需要延迟等编辑器初始化好
    setTimeout(() => {
      this.initEditorHeight()
    }, 1000)
    window.addEventListener('resize', this.initEditorHeight)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.initEditorHeight)
  },
  methods: {
    initEditorHeight() {
      const { height: applyTmpPageHeight } = this.$context.$agentPage.getBoundingClientRect()
      const { height: applyTmpPageHeaderHeight } = this.$context.$agentPageHeader.getBoundingClientRect()

      const height = applyTmpPageHeight - applyTmpPageHeaderHeight - 12 - 12
      this.editorHeight = height
    },
    handleSetEditorContent(content) {
      this.message.previewContent = content
    },
    updateData(key, value) {
      this[key] = value
    }
  },
}
</script>

