<template>
  <ArticleTinymceEditor
    :height="editorHeight"
    :content="message && message.previewContent"
    @setEditorContent="handleSetEditorContent"
  />
</template>

<script>
import BaseAgentContent from '../../conponents/BaseAgent/components/Content.vue'
import ArticleTinymceEditor from '@/pages/article/edit/ArticleTinymceEditor.vue'
export default {
  name: 'IdeaAgentContent',
  extends: BaseAgentContent,
  components: {
    ArticleTinymceEditor,
  },
  props: {
    activeAsset: {
      type: Object,
      default: null,
    },
    agentContext: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      editorHeight: 500,
      message: {},
    }
  },
  mounted() {
    // 这里需要延迟等编辑器初始化好
    setTimeout(() => {
      this.initEditorHeight()
    }, 1000)
    window.addEventListener('resize', this.initEditorHeight)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.initEditorHeight)
  },
  methods: {
    /**
     * Handle agent message from chat
     */
    onAgentMessage(data) {
      console.log('IdeaAgent received message:', data)
      // Handle idea-specific agent messages
      if (data && data.message && data.message.content) {
        this.message = data.message
      }
    },

    initEditorHeight() {
      // Use parent element for height calculation since context structure changed
      const parentElement = this.$el.parentElement
      if (parentElement) {
        const { height } = parentElement.getBoundingClientRect()
        this.editorHeight = height - 32 // Account for padding
      } else {
        this.editorHeight = 500 // Fallback height
      }
    },

    handleSetEditorContent(content) {
      this.message.previewContent = content
    },

    updateData(key, value) {
      this[key] = value
    }
  },
}
</script>

