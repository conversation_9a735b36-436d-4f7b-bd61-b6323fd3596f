<template>
  <ArticleTinymceEditor
    :height="editorHeight"
    :content="message && message.previewContent"
    @setEditorContent="handleSetEditorContent"
  />
</template>

<script>
import BaseAgentContent from '../../conponents/BaseAgent/components/Content.vue'
import ArticleTinymceEditor from '@/pages/article/edit/ArticleTinymceEditor.vue'
export default {
  name: 'ArticleAgentContent',
  extends: BaseAgentContent,
  components: {
    ArticleTinymceEditor,
  },
  props: {
    activeAsset: {
      type: Object,
      default: null,
    },
    agentContext: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      editorHeight: 500,
      message: {},
      promptDetail: {},
    }
  },
  created() {
    this.fetchTemplateDetail()
  },
  mounted() {
    // 这里需要延迟等编辑器初始化好
    setTimeout(() => {
      this.initEditorHeight()
    }, 1000)
    window.addEventListener('resize', this.initEditorHeight)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.initEditorHeight)
  },
  methods: {
    /**
     * Handle agent message from chat
     */
    onAgentMessage(data) {
      console.log('ArticleAgent received message:', data)
      // Handle article-specific agent messages
      if (data && data.message && data.message.content) {
        this.message = data.message
      }
    },

    initEditorHeight() {
      // Use parent element for height calculation since context structure changed
      const parentElement = this.$el.parentElement
      if (parentElement) {
        const { height } = parentElement.getBoundingClientRect()
        this.editorHeight = height - 32 // Account for padding
      } else {
        this.editorHeight = 500 // Fallback height
      }
    },
    fetchTemplateDetail() {
      const { templateId } = this.$route.query
      if (!templateId) return

      // Import http here since it's not imported at the top
      const http = require('@/services/http/index.js').default

      http.queryObjDataById({
        objectAPIName: 'PromptObj',
        selectFields: [
          'name',
          'category',
          'title',
          'content',
          'status',
          'last_modified_time',
          'aihelper_id',
          'step_split',
          'created_by',
          'number_of_collections',
          'scope_type',
        ],
        id: templateId,
      }).then(res => {
        if (res && res.errCode === 0) {
          this.promptDetail = {
            objectData: res.data,
          }

          // Update agent context with new API name
          if (this.agentContext && res.data?.aihelper_id) {
            // Emit event to parent to update agent API name
            this.$emit('updateAgentApiName', res.data.aihelper_id)
          }

          // Note: Context-based operations removed since we're using new architecture
          // The parent component will handle crumbs and header operations
        }
      })
    },
    handleSetEditorContent(content) {
      this.message.previewContent = content
    },
  },
}
</script>

