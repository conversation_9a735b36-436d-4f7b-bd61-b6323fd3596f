<template>
  <div :class="{ [$style.vibeMarketingHeader]: true, [$style.isChatOpen]: isChatOpen }">
    <div :class="$style.vibeMarketingHeader__left">
      <div :class="$style.vibeMarketingHeader__title">
        <div :class="$style.vibeMarketingHeader__title__left">
          <img
            :src="iconAiMagicStick"
            alt="ai magic stick"
          >
          {{ $t('marketing.pages.vibe_marketing.gzkj_98d69f') }}
        </div>
        <div
          v-if="workspaceDetail"
          :class="$style.vibeMarketingHeader__title__right"
        >
          <fx-popover
            ref="titlePopover"
            placement="bottom"
            width="80"
            trigger="click"
            :popper-class="$style.vibeMarketingHeader__title__popover__wrapper"
          >
            <div :class="$style.vibeMarketingHeader__title__popover">
              <div
                :class="$style.vibeMarketingHeader__title__popover__item"
                @click="handleBack"
              >
                {{ $t('marketing.pages.vibe_marketing.fhgzkj_138693') }}
              </div>
              <div
                :class="$style.vibeMarketingHeader__title__popover__item"
                @click="handleWorkspaceDialogShow"
              >
                {{ $t('marketing_pd.components.setting.bj_95b351') }}
              </div>
              <div
                :class="$style.vibeMarketingHeader__title__popover__item"
                @click="handlePosterAgent"
              >
                {{ $t('marketing.commons.zzhb_80c7a4') }}
              </div>
            </div>
            <div
              slot="reference"
              :class="$style.vibeMarketingHeader__title_content"
            >
              - {{ workspaceDetail.name }}
              <img
                :class="$style.triangle"
                :src="iconTriangleDown"
                alt="triangle down"
              >
            </div>
          </fx-popover>
        </div>
      </div>
      <div
        :class="$style.vibeMarketingHeader__fold"
        @click="handleChatToggle"
      >
        <img
          :src="isChatOpen ? iconFold : iconUnfold"
          alt="fold"
        >
      </div>
    </div>
    <div :class="$style.vibeMarketingHeader__right">
      <div
        v-if="marketingEventDetail"
        :class="$style.header__right__marketing"
      >
        <fx-link
          type="standard"
          size="small"
          @click="handleToEventDetail"
        >
          <img :src="iconHorn" />
          {{ marketingEventDetail.name }}
        </fx-link>
      </div>
      <div :class="$style.vibeMarketingHeader__assets">
        <fx-popover
          ref="assetsPopover"
          placement="bottom"
          width="240"
          trigger="click"
          :disabled="assetList.length === 0"
        >
          <div
            :class="$style.vibeMarketingHeader__assets__popover"
          >
            <template v-for="assetType in Object.keys(renderAssets)">
              <div
                :key="assetType"
                :class="$style.vibeMarketingHeader__assets__popover__item"
              >
                <div :class="$style.item__title">
                  <img
                    :src="assetsIconMap[assetType]"
                    alt="assets"
                  >
                  {{ assetsTypeMap[assetType] }}
                </div>
                <div :class="$style.item__content">
                  <div
                    v-for="asset in renderAssets[assetType]"
                    :key="asset.id"
                    :class="$style.item__content__asset"
                    @click="handleAssetClick(asset)"
                  >
                    {{ asset.name }}
                  </div>
                </div>
              </div>
            </template>
          </div>
          <div
            slot="reference"
            :class="$style.vibeMarketingHeader__assets_content"
          >
            <img
              :class="$style.icon"
              :src="titleIcon"
              alt="assets"
            >
            {{ title }}
            <img
              v-if="assetList.length > 0"
              :class="$style.triangle"
              :src="iconTriangleDown"
              alt="triangle down"
            >
          </div>
        </fx-popover>
      </div>
      <div
        :class="$style.vibeMarketingHeader__full_screen"
        @click="handleFullScreen"
      >
        <img
          :src="isFullScreen ? iconCloseFullScreen : iconOpenFullScreen"
          alt="full screen"
        >
        <span>{{ isFullScreen ? $t('marketing.pages.vibe_marketing.tcqp_49041f') : $t('marketing.pages.vibe_marketing.qp_185926') }}</span>
      </div>
    </div>

    <WorkspaceDialog
      v-if="workspaceDialogVisible"
      :visible="workspaceDialogVisible"
      :origin-workspace-detail="workspaceDetail"
      @cancel="handleWorkspaceDialogCancel"
      @submit="handleWorkspaceDialogSubmit"
    />
  </div>
</template>

<script>
import iconOpenFullScreen from '@/assets/images/icon-open-fullscreen.svg'
import iconCloseFullScreen from '@/assets/images/icon-close-fullscreen.svg'
import iconTriangleDown from '@/assets/images/icon-triangle-down.svg'
import iconAiMagicStick from '@/assets/images/icon-ai-magic-stick.svg'
import iconFold from '@/assets/images/icon-sharegpt-fold.svg'
import iconUnfold from '@/assets/images/icon-sharegpt-unfold.svg'
import iconHorn from '@/assets/images/icon-horn.svg'

import iconProduct from '@/assets/images/workspace-asset/icon-product.svg'
import iconVideo from '@/assets/images/workspace-asset/icon-video.svg'
import iconFile from '@/assets/images/workspace-asset/icon-file.svg'
import iconMicropage from '@/assets/images/workspace-asset/icon-micropage.svg'
import iconEvent from '@/assets/images/workspace-asset/icon-event.svg'
import iconArticle from '@/assets/images/workspace-asset/icon-article.svg'
import iconPoster from '@/assets/images/workspace-asset/icon-poster.svg'
import iconPicture from '@/assets/images/workspace-asset/icon-picture.svg'
import iconMarkdown from '@/assets/images/workspace-asset/icon-markdown.svg'
import iconSop from '@/assets/images/workspace-asset/icon-sop.svg'
import iconKanban from '@/assets/images/workspace-asset/icon-kanban.svg'

import WorkspaceDialog from '@/pages/vibe-marketing/components/workspace-dialog.vue'

import http from '@/services/http/index.js'
import { jumpToMarketingEventDetailPage } from '@/pages/marketing-calendar/utils.js'

const assetsIconMap = {
  kanban: iconKanban,
  sop: iconSop,
  markdown: iconMarkdown,
  image: iconPicture,
  poster: iconPoster,
  article: iconArticle,
  event: iconEvent,
  micropage: iconMicropage,
  file: iconFile,
  video: iconVideo,
  product: iconProduct,
  default: iconEvent,
}

const assetsTypeMap = {
  kanban: $t('marketing.commons.kb_6a58c8'),
  sop: 'SOP',
  markdown: 'markdown',
  image: $t('marketing_pd.commons.tp_20def7'),
  poster: $t('marketing.commons.hb_ff361e'),
  article: $t('marketing.commons.wz_c75625'),
  event: $t('marketing.commons.hd_36c6f5'),
  micropage: $t('marketing.commons.wym_5fd4fb'),
  file: $t('marketing.commons.wj_2a0c47'),
  video: $t('marketing.commons.sp_7fcf42'),
  product: $t('marketing.commons.cp_a01543'),
  default: $t('marketing_pd.commons.nr_2d711b'),
}

export default {
  name: 'NewHeader',
  components: {
    WorkspaceDialog,
  },
  inject: ['showAiPosterDialog'],
  props: {
    isChatOpen: {
      type: Boolean,
      default: true,
    },
    assetList: {
      type: Array,
      default: () => [],
    },
    activeAsset: {
      type: Object,
      default: null,
    },
    loaderType: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      iconOpenFullScreen,
      iconCloseFullScreen,
      iconTriangleDown,
      iconAiMagicStick,
      iconFold,
      iconUnfold,
      iconHorn,
      assetsIconMap,
      assetsTypeMap,
      isFullScreen: false,
      workspaceDetail: null,
      marketingEventDetail: null,
      workspaceDialogVisible: false,
    }
  },
  computed: {
    renderAssets() {
      const assets = {}
      this.assetList.forEach(item => {
        const { target_type__c } = item
        if (!assets[target_type__c]) {
          assets[target_type__c] = []
        }

        assets[target_type__c].push(item)
      })
      return assets
    },
    title() {
      if (this.loaderType === 'default') {
        return $t('marketing.pages.vibe_marketing.gzkjzc_db15ed')
      }

      if (this.loaderType) {
        return `${assetsTypeMap[this.loaderType]}${$t('marketing.pages.vibe_marketing.zz_8cdf04')}`
      }

      return ''
    },
    titleIcon() {
      if (this.loaderType) {
        return assetsIconMap[this.loaderType]
      }

      return assetsIconMap.micropage
    },
  },
  watch: {
    isFullScreen() {
      if (this.isFullScreen) {
        $('.bd.f-g-bd').addClass('z-index-auto')
        $('.bd-inner.app-inner').addClass('z-index-auto')
      } else {
        $('.bd.f-g-bd').removeClass('z-index-auto')
        $('.bd-inner.app-inner').removeClass('z-index-auto')
      }
    },
  },
  mounted() {
    this.fetchWorkspaceDetail()
    if (!this.isFullScreen) {
      this.handleFullScreen()
    }
  },
  beforeDestroy() {
    if (this.isFullScreen) {
      this.handleFullScreen()
    }
  },
  methods: {
    handleFullScreen() {
      this.isFullScreen = !this.isFullScreen
      const app = document.querySelector('.yxt-app')

      if (app) {
        if (this.isFullScreen) {
          app.classList.add('yxt-app-fc')
        } else {
          app.classList.remove('yxt-app-fc')
        }
      }

      const menuDom = document.querySelector('.yxt-app .g-menu-wrapper')
      if (menuDom) {
        menuDom.style.display = this.isFullScreen ? 'none' : 'block'
      }
    },

    fetchWorkspaceDetail() {
      const { workspaceId } = this.$route.query
      if (!workspaceId) {
        return
      }

      const payload = {
        objectAPIName: 'VMWorkspaceObj__c',
        selectFields: ['name', 'summary__c', 'works_space_type__c', 'icon__c', 'marketing_event_id__c'],
        id: workspaceId,
      }

      http.queryObjDataById(payload)
        .then(res => {
          const { errCode, data } = res
          if (errCode === 0) {
            this.workspaceDetail = data
            this.fetchMarketingEvent()
          }
        })
    },

    fetchMarketingEvent() {
      const { marketing_event_id__c } = this.workspaceDetail || {}
      if (!marketing_event_id__c) {
        return
      }

      FS.util.FHHApi({
        url: '/EM1HNCRM/API/v1/object/MarketingEventObj/controller/SimpleDetail',
        data: {
          isFromRecycleBin: false,
          objectDataId: marketing_event_id__c,
          objectDescribeApiName: 'MarketingEventObj',
        },
        success: res => {
          if (!res.Value || res.Value.errCode) return
          const obj = res.Value.data || {}
          this.marketingEventDetail = obj
        },
      })
    },

    handleEditWorkspace() {
      const { workspaceId } = this.$route.query
      if (!workspaceId) {
        return
      }

      FS.util.FHHApi(
        {
          url: '/EM1HNCRM/API/v1/object/VMWorkspaceObj__c/controller/Detail',
          data: {
            objectDescribeApiName: 'VMWorkspaceObj__c',
            objectDataId: workspaceId,
          },
          success: ({ Result: { StatusCode }, Value }) => {
            if (StatusCode === 0) {
              CRM.api.edit({
                data: Value.data,
                title: $t('marketing.commons.bjgzkjyh_2fce97'),
                apiname: 'VMWorkspaceObj__c',
                show_type: 'full',
                dataId: workspaceId,
                nonEditable: true,
                success: () => {
                  this.fetchWorkspaceDetail()
                },
              })

              if (this.$refs.titlePopover && this.$refs.titlePopover.handleBlur) {
                this.$refs.titlePopover.handleBlur()
              }
            }
          },
        },
        {
          errorAlertModel: 1,
        },
      )
    },
    handleBack() {
      this.$router.push({
        name: 'vibe-marketing-workspace',
      })
    },
    handleWorkspaceDialogCancel() {
      this.workspaceDialogVisible = false
    },
    handleWorkspaceDialogShow() {
      const { workspaceId } = this.$route.query
      if (!workspaceId) {
        return
      }
      this.workspaceDialogVisible = true

      if (this.$refs.titlePopover && this.$refs.titlePopover.handleBlur) {
        this.$refs.titlePopover.handleBlur()
      }
    },
    handlePosterAgent() {
      // 使用注入的showAiPosterDialog方法，只传递组件支持的参数
      this.showAiPosterDialog({
        title: 'AI海报生成',
        content: '为当前工作空间生成宣传海报'
      }).then(res => {
        console.log('[INFO] ┃ [NewHeader.vue] ┃ [ui] ┃ Poster generation completed ┃ res=' + JSON.stringify(res))
      })
    },
    handleWorkspaceDialogSubmit() {
      this.fetchWorkspaceDetail()
    },
    handleAssetClick(asset) {
      if (this.$refs.assetsPopover && this.$refs.assetsPopover.handleBlur) {
        this.$refs.assetsPopover.handleBlur()
      }

      if (this.activeAsset && this.activeAsset._id === asset._id) {
        return
      }

      this.$emit('onAssetChange', asset)
    },
    handleChatToggle() {
      this.$emit('onChatToggle')
    },
    handleToEventDetail() {
      jumpToMarketingEventDetailPage({
        marketingEventId: this.marketingEventDetail._id,
        eventType: this.marketingEventDetail.event_type,
        $router: this.$router,
        target: '_blank',
      })
    },
  },
}
</script>

<style lang="less" module>
.vibeMarketingHeader {
  height: 50px;
  display: flex;
  align-items: center;
  padding: 0 12px;

  &.isChatOpen {
    .vibeMarketingHeader__left {
      width: 375px;
    }
  }

  .vibeMarketingHeader__left {
    width: auto;
    font-size: 15px;
    color: #181C25;
    line-height: 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .vibeMarketingHeader__title {
      display: flex;
      align-items: center;
    }

    .vibeMarketingHeader__title__left {
      display: flex;
      align-items: center;

      img {
        width: 14px;
        height: 14px;
        margin-right: 4px;
      }
    }

    .vibeMarketingHeader__title__right {

      .vibeMarketingHeader__title_content {
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        margin-left: 3px;

        img {
          width: 16px;
          height: 16px;
        }

        .triangle {
          margin-left: 4px;
        }
      }
    }

    .vibeMarketingHeader__fold {
      font-size: 0;
      display: flex;
      align-items: center;
      margin-left: 10px;
      cursor: pointer;
      img {
        width: 16px;
        height: 16px;
      }
    }
  }

  .vibeMarketingHeader__right {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    .vibeMarketingHeader__assets_content {
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-size: 14px;
      color: #181C25;
      line-height: 24px;

      img {
        width: 16px;
        height: 16px;
      }

      .icon {
        margin-right: 4px;
      }

      .triangle {
        margin-left: 4px;
      }
    }

    .header__right__marketing {
      display: flex;
      align-items: center;
      justify-content: center;
      position: absolute;
      left: 12px;
      height: 50px;
      font-size: 13px;
      color: #181C25;
      line-height: 18px;

      img {
        width: 16px;
        height: 16px;
        margin-right: 2px;
        position: relative;
        top: 2px;
      }
    }

    .vibeMarketingHeader__full_screen {
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      position: absolute;
      right: 0;
      height: 50px;
      font-size: 13px;
      color: #181C25;
      line-height: 18px;

      img {
        width: 16px;
        height: 16px;
        margin-right: 6px;
      }
    }
  }
}

.vibeMarketingHeader__title__popover__wrapper {
  padding-left: 0;
  padding-right: 0;

  .vibeMarketingHeader__title__popover__item {
    height: 32px;
    line-height: 32px;
    color: #181C25;
    font-size: 14px;
    cursor: pointer;
    padding: 0 16px;

    &:hover {
      background-color: #FFF7E6;
    }
  }
}

.vibeMarketingHeader__assets__popover {

  .vibeMarketingHeader__assets__popover__item {
    .item__title {
      height: 28px;
      display: flex;
      align-items: center;
      color: #181C25;
      font-size: 12px;

      img {
        width: 12px;
        height: 12px;
        margin-right: 4px;
        position: relative;
        top: -1px;
      }
    }

    .item__content {
      .item__content__asset {
        min-height: 28px;
        display: flex;
        align-items: center;
        color: #181C25;
        font-size: 12px;
        line-height: 18px;
        padding: 4px 4px 2px 16px;
        cursor: pointer;

        &:hover {
          background-color: #FFF7E6;
        }
      }
    }
  }
}
</style>
