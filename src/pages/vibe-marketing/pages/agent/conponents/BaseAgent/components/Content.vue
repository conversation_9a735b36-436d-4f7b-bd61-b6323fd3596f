<template>
  <div :class="$style.BaseAgentContent">
    <div :class="$style.BaseAgentContent__header">
      oops...
      <br>{{ $t('marketing.pages.vibe_marketing.shmyzddyds_0005a8') }}
    </div>
    <div :class="$style.BaseAgentContent__list">
      <div
        v-for="item in agentList"
        :key="item.type"
        :class="$style['content--item']"
        @click="handleAgentSelect(item)"
      >
        <img
          :src="item.icon"
          :alt="item.name"
        >
        <span>{{ item.name }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import inject from '../mixins/inject.js'
import iconPoster from '@/assets/images/workspace-asset/icon-poster.svg'
import iconMicropage from '@/assets/images/workspace-asset/icon-micropage.svg'
import iconEvent from '@/assets/images/workspace-asset/icon-event.svg'
import iconSop from '@/assets/images/workspace-asset/icon-sop.svg'

export default {
  name: 'BaseAgentContent',
  mixins: [inject],
  data() {
    return {
      agentList: [
        {
          icon: iconPoster,
          name: $t('marketing.pages.vibe_marketing.hbsc_ed2e6e'),
          type: 'poster',
        },
        {
          icon: iconMicropage,
          name: $t('marketing.pages.vibe_marketing.wymzz_a09429'),
          type: 'site',
        },
        {
          icon: iconSop,
          name: $t('marketing.pages.vibe_marketing.sc_0ca965'),
          type: 'sop',
        },
      ],
    }
  },
  methods: {
    handleAgentSelect(item) {
      this.$emit('onAgentSelect', item.type)
    },
  },
}
</script>

<style lang="less" module>
.BaseAgentContent {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #FFF;

  .BaseAgentContent__header {
    width: 500px;
    font-size: 18px;
    color: #333;
    margin-bottom: 20px;
    line-height: 24px;
  }

  .BaseAgentContent__list {
    display: flex;
    flex-wrap: wrap;
    gap: 24px;
    width: 500px;

    .content--item {
      width: calc((100% - 24px) / 2);
      height: 100px;
      display: flex;
      border: 1px solid var(--color-neutrals04, #EAEBEE);
      background: #FFF;
      cursor: pointer;
      transition: transform 0.3s ease;
      box-sizing: border-box;
      font-size: 18px;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      position: relative;
      overflow: hidden;
      border-radius: 8px;

      &:hover {
        box-shadow: 0 4px 12px rgba(255, 128, 0, 0.08);
        transform: translateY(-2px);
      }

      img {
        width: 24px;
        height: 24px;
      }

      span {
        line-height: 24px;
        margin-top: 4px;
      }
    }
  }
}
</style>
