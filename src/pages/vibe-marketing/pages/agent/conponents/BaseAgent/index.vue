<template>
  <div
    ref="agentPage"
    :class="$style['agent__page']"
    :style="`background-image: url(${bg});`"
  >
    <div
      ref="agentPageHeader"
      :class="$style['agent__page__header']"
    >
      <Header
        :is-chat-open="isChatOpen"
        :asset-list="assetList"
        :active-asset="activeAsset"
        :loader-type="loaderType"
        @onAssetChange="handleAssetChange"
        @onChatToggle="handleChatToggle"
      />
    </div>
    <div
      :class="{
        [$style['agent__page__content']]: true,
        [$style['chat-open']]: isChatOpen,
      }"
    >
      <div
        :class="$style['agent__page__content-wrapper']"
      >
        <div :class="$style['agent__page__content__left']">
          <Chat
            :prompt="prompt"
            :default-helper-name="agentApiName"
            @onReady="_handleShareGPTReady"
            @onAgentMessage="_handleAgentMessage"
          />
        </div>
        <!-- <div :class="$style.chat_toggle">
          <img
            :src="iconArrowLeft"
            alt="arrow left"
            @click="handleChatToggle"
          >
        </div> -->
        <div :class="$style['agent__page__content__right']">
          <Content
            ref="content"
            style="height: 100%; flex: 1;"
            :active-asset="activeAsset"
            @onAgentSelect="handleAgentSelect"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Chat from './components/Chat.vue'
import Content from './components/Content.vue'
import Header from './components/NewHeader.vue'
import mixins from './mixins/mixins.js'
import http from '@/services/http/index.js'
import iconArrowLeft from '@/assets/images/icon-arrow-left.svg'
import bg from '@/assets/images/workspace-bg.png'

export default {
  name: 'BaseAgent',
  components: {
    Chat,
    Content,
    Header,
  },
  mixins: [mixins],
  props: {
    assetList: {
      type: Array,
      default: () => [],
    },
    activeAsset: {
      type: Object,
      default: null,
    },
    loaderType: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      prompt: this.$route.query.prompt,
      iconArrowLeft,
      bg,
      agentApiName: '',
      isChatOpen: true,
    }
  },
  watch: {
    hasSessionId(val) {
      if (val) {
        this.addWorkspaceUser()
      }
    },
  },
  mounted() {
    setTimeout(() => {
      this.addWorkspaceUser()
    }, 3000)
  },
  methods: {
    /**
     * agent初始化
     */
    onAgentReady(agent) {
      console.log('onAgentReady', agent)
    },
    /**
     * 处理agent消息
     */
    onAgentMessage(data) {
      console.log('onAgentMessage', data)
    },
    /**
     * 处理header事件
     */
    onHeaderEvent(args) {
      console.log('onHeaderEvent', args)
    },
    /**
     * 设置会话ID
     */
    getSessionId() {
      const { workspaceId } = this.$route.query
      const { id, enterpriseAccount } = FS.contacts.getCurrentEmployee()
      return `${enterpriseAccount}-${id}-${this.agentApiName}-${workspaceId || ''}`
    },
    /**
     * 获取crumbs
     */
    getCrumbs() {
      return [
        {
          text: 'Vibe Marketing',
          to: { name: 'vibe-marketing-init' },
        },
        {
          text: '创意',
          to: { name: 'vibe-marketing-idea' },
        },
        {
          text: '',
          to: false,
        },
      ]
    },
    fetchSessionIdByBiz() {
      const bizSessionId = this.getSessionId()
      const params = {
        objectAPIName: 'AIChatSessionObj',
        pageNum: 1,
        pageSize: 50,
        selectFields: ['biz_session_id', 'title'],
        query: JSON.stringify({
          filters: [
            {
              field_name: 'biz_session_id',
              field_values: [bizSessionId],
              operator: 'EQ',
            },
          ],
        }),
      }

      return http.pageQueryObjDatas(params)
        .then(res => {
          const { errCode, data = {} } = res
          if (errCode === 0) {
            const {
              result = [],
            } = data

            if ((result || []).length > 0) {
              return result[0]._id
            }
          }

          return ''
        })
    },
    checkWorkspaceUser(sessionId) {
      const { workspaceId } = this.$route.query
      const params = {
        objectAPIName: 'VMWorkspaceUserObj__c',
        pageNum: 1,
        pageSize: 50,
        selectFields: ['chat_session_id__c', 'workspace_id__c', 'user_id__c'],
        query: JSON.stringify({
          filters: [
            {
              field_name: 'workspace_id__c',
              field_values: [workspaceId],
              operator: 'EQ',
            },
            {
              field_name: 'chat_session_id__c',
              field_values: [sessionId],
              operator: 'EQ',
            },
          ],
        }),
      }

      return http.pageQueryObjDatas(params)
        .then(res => {
          const { errCode, data = {} } = res
          if (errCode === 0) {
            const {
              result,
            } = data

            return (result || []).length > 0
          }

          return false
        })
    },
    async addWorkspaceUser() {
      const { workspaceId } = this.$route.query
      if (!workspaceId) {
        return
      }

      const sessionId = await this.fetchSessionIdByBiz()
      if (!sessionId) {
        this.hasSessionId = false
        return
      }

      const recordAdded = await this.checkWorkspaceUser(sessionId)
      if (recordAdded) {
        return
      }

      const employee = window.Fx.contacts.getCurrentEmployee()
      const employeeID = (employee && employee.employeeID) || ''
      const departmentIds = (employee && employee.departmentIds) || []

      const payload = {
        object_data: {
          created_by: employeeID ? [`${employeeID}`] : [],
          data_own_department: departmentIds.map(el => `${el}`),
          object_describe_api_name: 'VMWorkspaceUserObj__c',
          record_type: 'default__c',
          owner: employeeID ? [`${employeeID}`] : [],
          user_id__c: `${employeeID}`,
          workspace_id__c: workspaceId || '',
          chat_session_id__c: sessionId,
        },
      }
      http.addVMWorkspaceUserObj(payload)
    },
    addWorkspaceAsset(payload, callback) {
      const { workspaceId } = this.$route.query
      if (!workspaceId) {
        return
      }

      const employee = window.Fx.contacts.getCurrentEmployee()
      const employeeID = (employee && employee.employeeID) || ''
      const departmentIds = (employee && employee.departmentIds) || []

      const data = {
        object_data: {
          created_by: employeeID ? [`${employeeID}`] : [],
          data_own_department: departmentIds.map(el => `${el}`),
          object_describe_api_name: 'VMWorkspaceAssetObj__c',
          record_type: 'default__c',
          workspace_id__c: workspaceId || '',
          owner: employeeID ? [`${employeeID}`] : [],
          // name: '',
          // asset_data__c: '',
          // target_type__c: '',
          // target_id__c: '',
          ...payload,
        },
      }

      http.addVMWorkspaceAssetObj(data)
        .then(res => {
          if (typeof callback === 'function') {
            callback(res)
          }
        })
    },
    handleChatToggle() {
      console.log('handleChatToggle2')
      this.isChatOpen = !this.isChatOpen
    },
    handleAssetChange(asset) {
      this.$emit('onAssetChange', asset)
    },
    handleAgentSelect(type) {
      console.log('handleAgentSelect', type)
      this.$emit('onAgentSelect', type)
    },
  },
}
</script>

<style lang="less" module>
.agent__page {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #F7F9FF;
  background-position: left top;
  background-repeat: no-repeat;

  .agent__page__options {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }

  .agent__page__content {
    display: flex;
    flex-direction: column;
    height: calc(100% - 50px);
    flex: 1;
    padding: 12px 12px 12px 0;
    box-sizing: border-box;

    &.chat-open {
      padding-left: 12px;

      .agent__page__content__left {
        width: 375px;
      }

      .chat_toggle img {
        transform: rotate(0deg);
      }
    }

    .agent__page__content-wrapper {
      display: flex;
      height: 100%;
      align-items: stretch;
      flex: 1;
    }

    .agent__page__content__left {
      width: 0;
      transition: width 0.2s ease-in-out;
      overflow: hidden;
    }

    .chat_toggle {
      width: 30px;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        width: 10px;
        cursor: pointer;
        transition: transform 0.2s ease-out;
        transform: rotate(180deg);
      }

    }

    .agent__page__content__right {
      flex: 1;
      min-width: 0;
      display: flex;
      flex-direction: column;
      box-shadow: 0px 2px 6px 0px #00000026;
      border-radius: 6px;
      overflow: hidden;
      margin-left: 12px;
    }
  }
}
</style>
