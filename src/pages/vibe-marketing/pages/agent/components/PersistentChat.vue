<template>
  <div :class="$style['persistent-chat']">
    <BaseShareGPT
      ref="baseShareGPT"
      :default-helper-name="currentAgentApiName"
      @onReady="handleChatReady"
      @onAgentMessage="handleAgentMessage"
    />
  </div>
</template>

<script>
import BaseShareGPT from '../../components/baseShareGPT.vue'
import http from '@/services/http/index.js'

export default {
  name: 'PersistentChat',
  components: {
    ChatLLM,
  },
  props: {
    currentAgentType: {
      type: String,
      default: 'default',
    },
    currentAgentApiName: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      agent: null,
      sessionId: null,
      hasSessionId: true,
    }
  },
  watch: {
    // When agent type changes, update the chat configuration
    currentAgentApiName(newApiName, oldApiName) {
      if (newApiName !== oldApiName && this.agent) {
        this.updateAgentConfiguration()
      }
    },
  },
  methods: {
    /**
     * Handle chat ready event
     */
    handleChatReady(agent) {
      console.log('PersistentChat ready:', agent)
      this.agent = agent
      this.initializeAgent()
      this.$emit('onReady', agent)
    },

    /**
     * Handle agent message
     */
    handleAgentMessage(data) {
      const { message } = data
      if (message && message.finish && message.sessionId) {
        this.hasSessionId = true
        this.sessionId = message.sessionId
      }
      this.$emit('onAgentMessage', data)
    },

    /**
     * Initialize agent with current configuration
     */
    async initializeAgent() {
      if (!this.agent) return

      const sessionData = await this.getSessionId()
      const configData = {
        enableAgentMessageListener: true,
        enableBackground: false,
      }

      if (typeof sessionData === 'string') {
        configData.bizSessionId = sessionData
      }

      if (typeof sessionData === 'object') {
        configData.bizSessionId = sessionData.bizSessionId || ''
        configData.sessionId = sessionData.sessionId || ''
      }

      this.agent.sendConfigData(configData)
      this.addWorkspaceUser()
    },

    /**
     * Update agent configuration when switching agent types
     */
    updateAgentConfiguration() {
      if (!this.agent) return
      
      // Update the agent's helper name
      this.agent.setHelperName(this.currentAgentApiName)
      
      // Re-initialize with new configuration
      this.initializeAgent()
    },

    /**
     * Get session ID for current agent
     */
    async getSessionId() {
      const { workspaceId } = this.$route.query
      const { id, enterpriseAccount } = FS.contacts.getCurrentEmployee()
      const bizSessionId = `${enterpriseAccount}-${id}-${this.currentAgentApiName}-${workspaceId || ''}`
      
      const params = {
        objectAPIName: 'AIChatSessionObj',
        pageNum: 1,
        pageSize: 50,
        selectFields: ['biz_session_id', 'title'],
        query: JSON.stringify({
          filters: [
            {
              field_name: 'biz_session_id',
              field_values: [bizSessionId],
              operator: 'EQ',
            },
          ],
        }),
      }

      try {
        const res = await http.pageQueryObjDatas(params)
        const { errCode, data = {} } = res
        if (errCode === 0) {
          const { result = [] } = data
          if (result.length > 0) {
            return result[0]._id
          }
        }
        return bizSessionId
      } catch (error) {
        console.error('Error fetching session ID:', error)
        return bizSessionId
      }
    },

    /**
     * Add workspace user
     */
    async addWorkspaceUser() {
      const { workspaceId } = this.$route.query
      if (!workspaceId) return

      const sessionId = await this.getSessionId()
      if (!sessionId) {
        this.hasSessionId = false
        return
      }

      // Check if user already exists
      const recordAdded = await this.checkWorkspaceUser(sessionId)
      if (recordAdded) return

      const employee = window.Fx.contacts.getCurrentEmployee()
      const employeeID = (employee && employee.employeeID) || ''
      const departmentIds = (employee && employee.departmentIds) || []

      const payload = {
        object_data: {
          created_by: employeeID ? [`${employeeID}`] : [],
          data_own_department: departmentIds.map(el => `${el}`),
          object_describe_api_name: 'VMWorkspaceUserObj__c',
          record_type: 'default__c',
          owner: employeeID ? [`${employeeID}`] : [],
          user_id__c: `${employeeID}`,
          workspace_id__c: workspaceId || '',
          chat_session_id__c: sessionId,
        },
      }

      try {
        await http.addVMWorkspaceUserObj(payload)
      } catch (error) {
        console.error('Error adding workspace user:', error)
      }
    },

    /**
     * Check if workspace user exists
     */
    async checkWorkspaceUser(sessionId) {
      const { workspaceId } = this.$route.query
      const params = {
        objectAPIName: 'VMWorkspaceUserObj__c',
        pageNum: 1,
        pageSize: 50,
        selectFields: ['chat_session_id__c', 'workspace_id__c', 'user_id__c'],
        query: JSON.stringify({
          filters: [
            {
              field_name: 'workspace_id__c',
              field_values: [workspaceId],
              operator: 'EQ',
            },
            {
              field_name: 'chat_session_id__c',
              field_values: [sessionId],
              operator: 'EQ',
            },
          ],
        }),
      }

      try {
        const res = await http.pageQueryObjDatas(params)
        const { errCode, data = {} } = res
        if (errCode === 0) {
          const { result } = data
          return (result || []).length > 0
        }
        return false
      } catch (error) {
        console.error('Error checking workspace user:', error)
        return false
      }
    },

    /**
     * Public method to send message to chat
     */
    sendMessage(message) {
      if (this.agent) {
        this.agent.sendMessage(message)
      }
    },

    /**
     * Public method to get chat history
     */
    getChatHistory() {
      if (this.$refs.chatLLM && this.$refs.chatLLM.getChatHistory) {
        return this.$refs.chatLLM.getChatHistory()
      }
      return []
    },

    /**
     * Public method to clear chat
     */
    clearChat() {
      if (this.$refs.chatLLM && this.$refs.chatLLM.clearChat) {
        this.$refs.chatLLM.clearChat()
      }
    },
  },
}
</script>

<style lang="less" module>
.persistent-chat {
  height: 100%;
  width: 100%;
}
</style>
