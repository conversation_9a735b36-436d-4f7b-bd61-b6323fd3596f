<template>
  <div :class="$style['site-agent-content']">
    <div :class="$style['site-agent-content-wrapper']">
      <PageDesigner ref="pageDesigner" :is-agent="true" />
    </div>
  </div>
</template>

<script>
import PageDesigner from '@/pages/site/page-designer/index.vue'
export default {
  name: 'SiteAgentContent',
  components: {
    PageDesigner,
  },
  props: {
    activeAsset: {
      type: Object,
      default: null,
    },
    agentContext: {
      type: Object,
      default: () => ({}),
    },
  },
  mounted() {
    // Register page data methods with agent context
    if (this.agentContext) {
      this.agentContext.setPageData = this.setPageData
      this.agentContext.getPageData = this.getPageData
    }
  },
  methods: {
    /**
     * Handle agent message from chat
     */
    onAgentMessage(data) {
      console.log('SiteAgent handleAgentMessage', data);
      try {
        if(typeof data === 'object'){
          console.log('pageData object:', data);
          this.setPageData(data);
        } else {
          //移除\n、\t
          const content = data.message.content.replace(/\n/g, '').replace(/\t/g, '');
          const pageData = JSON.parse(content);
          console.log('pageData', pageData);
          this.setPageData(pageData.page ? pageData.page : pageData);
        }
      } catch (error) {
        console.error('handleAgentMessage error', error);
      }
    },

    getPageData() {
      return this.$refs.pageDesigner.$refs.designer?.$refs.hexagon?.getCurrentPageData()
    },

    setPageData(pageData) {
      this.$refs.pageDesigner.$refs.designer?.$refs.hexagon?.updateCurrentPage(pageData);
    },
  },
}
</script>

<style lang="less" module>
.site-agent-content {
  height: calc(100vh - 100px);
  overflow: hidden;
  .site-agent-content-wrapper {
    width: 100%;
    height: 100%;
    overflow: auto;
  }
}
</style>
