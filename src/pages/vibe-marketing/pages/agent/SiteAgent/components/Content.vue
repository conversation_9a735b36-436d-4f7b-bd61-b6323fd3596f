<template>
  <div :class="$style['site-agent-content']">
    <div :class="$style['site-agent-content-wrapper']">
      <PageDesigner ref="pageDesigner" :is-agent="true" />
    </div>
  </div>
</template>

<script>
import PageDesigner from '@/pages/site/page-designer/index.vue'
export default {
  name: 'SiteAgentContent',
  components: {
    PageDesigner,
  },
  methods: {
    getPageData() {
      return this.$refs.pageDesigner.$refs.designer?.$refs.hexagon?.getCurrentPageData()
    },
    setPageData(pageData) {
      this.$refs.pageDesigner.$refs.designer?.$refs.hexagon?.updateCurrentPage(pageData);
    },
  },
}
</script>

<style lang="less" module>
.site-agent-content {
  height: calc(100vh - 100px);
  overflow: hidden;
  .site-agent-content-wrapper {
    width: 100%;
    height: 100%;
    overflow: auto;
  }
}
</style>
