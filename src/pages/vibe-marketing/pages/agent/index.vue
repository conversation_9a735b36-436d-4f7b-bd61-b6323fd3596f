<template>
  <div
    v-loading="isLoading"
    :class="$style['mark-agent-page']"
  >
    <div
      v-if="errorMsg"
      style="color: red; text-align: center; margin-top: 40px;"
    >
      {{ errorMsg }}
    </div>
    <component
      :is="component && component.component"
      v-else
      :asset-list="assetList"
      :active-asset="activeAsset"
      :loader-type="component && component.type"
      @onAssetChange="handleAssetChange"
      @onAgentSelect="handleAgentSelect"
    />
    <AiPosterDialog ref="aiPosterDialog" />
  </div>
</template>
<script>
import http from '@/services/http/index.js'
import AiPosterDialog from '@/pages/vibe-marketing/components/AiPosterDialog.vue'

const componentLoaderMap = {
  default: {
    component: () => import('./conponents/BaseAgent/index.vue'),
    type: 'default',
  },
  article: {
    component: () => import('./ArticleAgent/index.vue'),
    type: 'article',
  },
  site: {
    component: () => import('./SiteAgent/index.vue'),
    type: 'micropage',
  },
  poster: {
    component: () => import('./PosterAgent/index.vue'),
    type: 'poster',
  },
  dashboard: {
    component: () => import('./DashboardAgent/index.vue'),
    type: 'event',
  },
  history: {
    component: () => import('./HistoryAgent/index.vue'),
    type: 'history',
  },
  idea: {
    component: () => import('./IdeaAgent/index.vue'),
    type: 'idea',
  },
}

export default {
  name: 'MarkAgentPage',
  components: {
    AiPosterDialog,
  },
  provide() {
    return {
      showAiPosterDialog: this.showAiPosterDialog
    }
  },
  data() {
    return {
      component: null,
      errorMsg: '',
      isLoading: true,
      assetList: [],
      activeAsset: null,
    }
  },
  created() {
    const assetListPromise = this.fetchAssetList()
    const { name } = this.$route.query

    if (name !== undefined) {
      const loader = this.getComponentLoader(name)
      if (loader) {
        this.component = loader
      } else {
        this.component = componentLoaderMap.default
        // this.errorMsg = `未找到对应的 Agent：${name || '未知'}`
      }

      return
    }

    assetListPromise.then(() => {
      if (this.assetList.length > 0) {
        this.activeAsset = this.assetList[0]
        const loader = this.getComponentLoaderByAsset(this.activeAsset.target_type__c) || componentLoaderMap.default

        this.component = loader
      } else {
        this.component = componentLoaderMap.default
      }
    })
  },
  methods: {
    getComponentLoader(name) {
      if (name === 'article') {
        return componentLoaderMap.article
      } if (name === 'site') {
        return componentLoaderMap.site
      } if (name === 'poster') {
        return componentLoaderMap.poster
      } if (name === 'dashboard') {
        return componentLoaderMap.dashboard
      } if (name === 'history') {
        return componentLoaderMap.history
      } if (name === 'idea') {
        return componentLoaderMap.idea
      }
      return null
    },
    getComponentLoaderByAsset(targetType) {
      if (targetType === 'article') {
        return componentLoaderMap.idea
      }

      if (targetType === 'micropage') {
        return componentLoaderMap.site
      }

      if (targetType === 'poster') {
        return componentLoaderMap.poster
      }

      if (targetType === 'event') {
        return componentLoaderMap.dashboard
      }

      return null
    },
    fetchAssetList() {
      this.isLoading = true

      const payload = {
        objectAPIName: 'VMWorkspaceAssetObj__c',
        pageNum: 1,
        pageSize: 100,
        selectFields: ['name', 'asset_data__c', 'target_type__c', 'target_id__c', 'workspace_id__c'],
        query: JSON.stringify({
          filters: [
            {
              field_name: 'workspace_id__c',
              field_values: [this.$route.query.workspaceId],
              operator: 'EQ',
            },
          ],
        }),
      }

      return http.pageQueryObjDatas(payload)
        .then(res => {
          const { errCode, data } = res
          if (errCode === 0) {
            this.assetList = data.result || []
          }
        })
        .finally(() => {
          this.isLoading = false
        })
    },
    handleAssetChange(asset) {
      this.activeAsset = asset
      const loader = this.getComponentLoaderByAsset(asset.target_type__c)
      if (loader) {
        this.component = loader
      } else {
        this.component = componentLoaderMap.default
        // this.errorMsg = `未找到对应的 Agent：${asset.target_type__c || '未知'}`
      }
    },
    handleAgentSelect(type) {
      if (componentLoaderMap[type]) {
        this.component = componentLoaderMap[type]
      } else {
        this.component = componentLoaderMap.default
      }
    },
    showAiPosterDialog(objectData = {}) {
      console.log('[INFO] ┃ [agent/index.vue] ┃ [ui] ┃ AiPosterDialog called ┃ data=' + JSON.stringify(objectData))
      return this.$refs.aiPosterDialog.show(objectData)
    }
  },
}
</script>

<style lang="less" module>
.mark-agent-page {
  height: 100%;
}
</style>
