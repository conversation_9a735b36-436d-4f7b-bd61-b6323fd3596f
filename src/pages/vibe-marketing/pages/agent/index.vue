<template>
  <div
    v-loading="isLoading"
    :class="$style['mark-agent-page']"
  >
    <div
      v-if="errorMsg"
      style="color: red; text-align: center; margin-top: 40px;"
    >
      {{ errorMsg }}
    </div>
    <div
      v-else
      ref="agentPage"
      :class="$style['agent__page']"
      :style="`background-image: url(${bg});`"
    >
      <div
        ref="agentPageHeader"
        :class="$style['agent__page__header']"
      >
        <Header
          :is-chat-open="isChatOpen"
          :asset-list="assetList"
          :active-asset="activeAsset"
          :loader-type="currentAgentType"
          @onAssetChange="handleAssetChange"
          @onChatToggle="handleChatToggle"
        />
      </div>
      <div
        :class="{
          [$style['agent__page__content']]: true,
          [$style['chat-open']]: isChatOpen,
        }"
      >
        <div
          :class="$style['agent__page__content-wrapper']"
        >
          <div :class="$style['agent__page__content__left']">
            <PersistentChat
              ref="persistentChat"
              :current-agent-type="currentAgentType"
              :current-agent-api-name="currentAgentApiName"
              @onReady="handleChatReady"
              @onAgentMessage="handleAgentMessage"
            />
          </div>
          <div :class="$style['agent__page__content__right']">
            <component
              :is="contentComponent"
              ref="content"
              style="height: 100%; flex: 1;"
              :active-asset="activeAsset"
              :agent-context="agentContext"
              @onAgentSelect="handleAgentSelect"
              @addWorkspaceAsset="handleAddWorkspaceAsset"
              @updateAgentApiName="handleUpdateAgentApiName"
            />
          </div>
        </div>
      </div>
    </div>
    <AiPosterDialog ref="aiPosterDialog" />
  </div>
</template>
<script>
import Vue from 'vue'
import http from '@/services/http/index.js'
import AiPosterDialog from '@/pages/vibe-marketing/components/AiPosterDialog.vue'
import PersistentChat from './components/PersistentChat.vue'
import Header from './conponents/BaseAgent/components/NewHeader.vue'
import bg from '@/assets/images/workspace-bg.png'

// Content component loaders - only load content components, not full agents
const contentComponentLoaderMap = {
  default: {
    component: () => import('./conponents/BaseAgent/components/Content.vue'),
    type: 'default',
    agentApiName: '',
  },
  article: {
    component: () => import('./ArticleAgent/components/Content.vue'),
    type: 'article',
    agentApiName: '',
  },
  site: {
    component: () => import('./SiteAgent/components/Content.vue'),
    type: 'micropage',
    agentApiName: 'Copilot_hexagon_ai__c',
  },
  poster: {
    component: () => import('./PosterAgent/components/Content.vue'),
    type: 'poster',
    agentApiName: '',
  },
  dashboard: {
    component: () => import('./DashboardAgent/components/Content.vue'),
    type: 'event',
    agentApiName: '',
  },
  history: {
    component: () => import('./HistoryAgent/components/Content.vue'),
    type: 'history',
    agentApiName: '',
  },
  idea: {
    component: () => import('./IdeaAgent/components/Content.vue'),
    type: 'idea',
    agentApiName: '',
  },
}

export default {
  name: 'MarkAgentPage',
  components: {
    AiPosterDialog,
    PersistentChat,
    Header,
  },
  provide() {
    return {
      showAiPosterDialog: this.showAiPosterDialog,
      agentContext: this.agentContext
    }
  },
  data() {
    return {
      errorMsg: '',
      isLoading: true,
      assetList: [],
      activeAsset: null,
      // New architecture data
      currentAgentType: 'default',
      currentAgentApiName: '',
      contentComponent: null,
      isChatOpen: true,
      bg,
      // Shared context for communication between chat and content
      agentContext: Vue.observable({
        agent: null,
        sessionId: null,
        workspaceId: null,
        setPageData: null,
        getPageData: null,
      })
    }
  },
  created() {
    // Initialize workspace context
    this.agentContext.workspaceId = this.$route.query.workspaceId

    const assetListPromise = this.fetchAssetList()
    const { name } = this.$route.query

    if (name !== undefined) {
      this.loadAgentByName(name)
      return
    }

    assetListPromise.then(() => {
      if (this.assetList.length > 0) {
        this.activeAsset = this.assetList[0]
        this.loadAgentByAsset(this.activeAsset.target_type__c)
      } else {
        this.loadAgentByName('default')
      }
    })
  },
  methods: {
    /**
     * Load agent by name
     */
    async loadAgentByName(name) {
      const loader = this.getContentLoader(name)
      if (loader) {
        this.currentAgentType = loader.type
        this.currentAgentApiName = loader.agentApiName
        this.contentComponent = await loader.component()
      } else {
        this.loadAgentByName('default')
      }
    },

    /**
     * Load agent by asset type
     */
    async loadAgentByAsset(targetType) {
      const agentName = this.getAgentNameByAsset(targetType)
      await this.loadAgentByName(agentName)
    },

    /**
     * Get content loader by agent name
     */
    getContentLoader(name) {
      return contentComponentLoaderMap[name] || null
    },

    /**
     * Map asset type to agent name
     */
    getAgentNameByAsset(targetType) {
      const mapping = {
        'article': 'idea',
        'micropage': 'site',
        'poster': 'poster',
        'event': 'dashboard'
      }
      return mapping[targetType] || 'default'
    },
    fetchAssetList() {
      this.isLoading = true

      const payload = {
        objectAPIName: 'VMWorkspaceAssetObj__c',
        pageNum: 1,
        pageSize: 100,
        selectFields: ['name', 'asset_data__c', 'target_type__c', 'target_id__c', 'workspace_id__c'],
        query: JSON.stringify({
          filters: [
            {
              field_name: 'workspace_id__c',
              field_values: [this.$route.query.workspaceId],
              operator: 'EQ',
            },
          ],
        }),
      }

      return http.pageQueryObjDatas(payload)
        .then(res => {
          const { errCode, data } = res
          if (errCode === 0) {
            this.assetList = data.result || []
          }
        })
        .finally(() => {
          this.isLoading = false
        })
    },
    /**
     * Handle chat toggle
     */
    handleChatToggle() {
      this.isChatOpen = !this.isChatOpen
    },

    /**
     * Handle chat ready event
     */
    handleChatReady(agent) {
      this.agentContext.agent = agent
      console.log('Chat ready for agent:', this.currentAgentType, agent)
    },

    /**
     * Handle agent message from chat
     */
    handleAgentMessage(data) {
      console.log('Agent message received:', data)
      // Forward message to content component if it has a handler
      if (this.$refs.content && this.$refs.content.onAgentMessage) {
        this.$refs.content.onAgentMessage(data)
      }
    },

    /**
     * Handle asset change
     */
    async handleAssetChange(asset) {
      this.activeAsset = asset
      await this.loadAgentByAsset(asset.target_type__c)
    },

    /**
     * Handle agent selection
     */
    async handleAgentSelect(type) {
      await this.loadAgentByName(type)
    },

    /**
     * Handle add workspace asset from content components
     */
    handleAddWorkspaceAsset(payload) {
      this.addWorkspaceAsset(payload, () => {
        // Refresh asset list after adding
        this.fetchAssetList()
      })
    },

    /**
     * Handle update agent API name from content components
     */
    handleUpdateAgentApiName(apiName) {
      this.currentAgentApiName = apiName
      // Update the persistent chat with new API name
      if (this.$refs.persistentChat) {
        this.$refs.persistentChat.updateAgentConfiguration()
      }
    },

    /**
     * Add workspace asset
     */
    addWorkspaceAsset(payload, callback) {
      const { workspaceId } = this.$route.query
      if (!workspaceId) {
        return
      }

      const employee = window.Fx.contacts.getCurrentEmployee()
      const employeeID = (employee && employee.employeeID) || ''
      const departmentIds = (employee && employee.departmentIds) || []

      const data = {
        object_data: {
          created_by: employeeID ? [`${employeeID}`] : [],
          data_own_department: departmentIds.map(el => `${el}`),
          object_describe_api_name: 'VMWorkspaceAssetObj__c',
          record_type: 'default__c',
          workspace_id__c: workspaceId || '',
          owner: employeeID ? [`${employeeID}`] : [],
          ...payload,
        },
      }

      http.addVMWorkspaceAssetObj(data)
        .then(res => {
          if (typeof callback === 'function') {
            callback(res)
          }
        })
    },
    showAiPosterDialog(objectData = {}) {
      console.log('[INFO] ┃ [agent/index.vue] ┃ [ui] ┃ AiPosterDialog called ┃ data=' + JSON.stringify(objectData))
      return this.$refs.aiPosterDialog.show(objectData)
    }
  },
}
</script>

<style lang="less" module>
.mark-agent-page {
  height: 100%;
}

.agent__page {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #F7F9FF;
  background-position: left top;
  background-repeat: no-repeat;

  .agent__page__header {
    height: 50px;
    flex-shrink: 0;
  }

  .agent__page__content {
    display: flex;
    flex-direction: column;
    height: calc(100% - 50px);
    flex: 1;
    padding: 12px 12px 12px 0;
    box-sizing: border-box;

    &.chat-open {
      padding-left: 12px;

      .agent__page__content__left {
        width: 375px;
      }
    }

    .agent__page__content-wrapper {
      display: flex;
      height: 100%;
      align-items: stretch;
      flex: 1;
    }

    .agent__page__content__left {
      width: 0;
      transition: width 0.2s ease-in-out;
      overflow: hidden;
      margin-right: 0;
      border: 0;
      box-shadow: 0px 2px 6px 0px #00000026;
      border-radius: 8px;
    }

    .agent__page__content__right {
      flex: 1;
      min-width: 0;
      display: flex;
      flex-direction: column;
      box-shadow: 0px 2px 6px 0px #00000026;
      border-radius: 6px;
      overflow: hidden;
      margin-left: 12px;
    }
  }
}
</style>
