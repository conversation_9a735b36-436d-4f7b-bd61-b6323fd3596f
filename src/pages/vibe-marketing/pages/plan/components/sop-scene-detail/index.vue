<template>
  <fx-dialog
    :class="$style['sop-scene-detail']"
    :visible="visible"
    title="选择SOP场景"
    width="800px"
    append-to-body
    @close="handleCancel"
  >
    <div :class="$style['sop-scene-detail__content']">
      <live-content
        v-if="scene.type === 'live'"
        v-model="activeItem"
      />
    </div>
    <div
      slot="footer"
      :class="$style['create-sop__footer']"
    >
      <fx-button
        size="small"
        type="primary"
        :class="$style['create-sop__footer--btn']"
        :loading="isSubmitLoading"
        @click="handleSubmit"
      >
        确认
      </fx-button>
      <fx-button
        size="small"
        type="default"
        plain
        :class="$style['create-sop__footer--btn']"
        @click="handleCancel"
      >
        取消
      </fx-button>
    </div>
  </fx-dialog>
</template>

<script>
import { vibeMarketingWorkSpaceIconMap } from '@/utils/constant.js'
import http from '@/services/http/index.js'
import LiveContent from './live-content.vue'

export default {
  name: 'SopSceneDetail',
  components: {
    LiveContent,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    scene: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      activeItem: null,
      isSubmitLoading: false,
    }
  },
  methods: {
    getWorkSpaceId() {
      return new Promise(resolve => {
        const employee = window.Fx.contacts.getCurrentEmployee()
        const employeeID = (employee && employee.employeeID) || ''
        const departmentIds = (employee && employee.departmentIds) || []

        const payload = {
          object_data: {
            object_describe_api_name: 'VMWorkspaceObj__c',
            record_type: 'default__c',
            icon__c: vibeMarketingWorkSpaceIconMap.campaign,
            summary__c: '可以生成各种活动的SOP，如：活动、直播、会议、公众号、企微、邮件、官网运营。',
            works_space_type__c: 'campaign',
            name: `SOP生成_${Date.now()}`,
            owner: employeeID ? [`${employeeID}`] : [],
            created_by: employeeID ? [`${employeeID}`] : [],
            data_own_department: departmentIds.map(el => `${el}`),
          },
        }

        http.addVMWorkspaceObj(payload).then(res => {
          if (res.Result.StatusCode === 0) {
            const {
              objectData: {
                _id,
              } = {},
            } = res.Value

            resolve(_id)
          }
          resolve('')
        })
      })
    },
    handleCancel() {
      this.$emit('update:visible', false)
    },
    async handleSubmit() {
      if (!this.activeItem && this.scene.type === 'live') {
        FxUI.Message.warning('请选择直播内容')
        return
      }

      this.isSubmitLoading = true
      const workSpaceId = await this.getWorkSpaceId()

      if (this.scene.type === 'live') {
        const { id = '', marketingEventId = '' } = this.activeItem

        this.$router.push({
          name: 'vibe-marketing-agent',
          query: {
            name: 'sop',
            workspaceId: workSpaceId,
            sceneId: id,
            sceneType: 'live',
            marketingEventId,
          },
        })
      }
      this.isSubmitLoading = false
      this.$emit('update:visible', false)
    },
  },
}
</script>

<style lang="less" module>
.sop-scene-detail {
  :global {
    .el-dialog__body {
      padding: 0;
    }
  }

  .sop-scene-detail__content {
    height: 70vh;
  }
}
</style>
