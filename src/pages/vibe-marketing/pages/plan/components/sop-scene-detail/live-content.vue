<template>
  <div
    v-loading="refreshing"
    :class="$style['live-content']"
  >
    <div :class="$style['live-content__list']">
      <div
        v-for="item in lists"
        :key="item.id"
        :class="{
          [$style['live-content__list-item']]: true,
          [$style['selected']]: value && value.id === item.id,
        }"
        @click="handleSelectLive(item)"
      >
        <div :class="$style['live-content__list-item-cover']">
          <VImage
            style="width: 100%; height: 100%;"
            fit="cover"
            :src="item.cover"
          />
          <LiveStatus
            :class="$style.status"
            :life-status="item.lifeStatus"
            :status="item.status"
          />
        </div>
        <div :class="$style['live-content__list-item-title']">
          {{ item.title }}
        </div>
      </div>
    </div>
    <div :class="$style['live-content__footer']">
      <v-pagen
        :pagedata="pageData"
        @change="handlePageChange"
      />
    </div>
  </div>
</template>

<script>
import store from '@/pages/live-marketing/store.js'
import VPagen from '@/components/kitty/pagen.vue'
import LiveStatus from '@/pages/live-marketing/components/live-status.vue'

export default {
  name: 'LiveContent',
  components: {
    VPagen,
    VImage: FxUI.Image,
    LiveStatus,
  },
  mixins: [store],
  props: {
    value: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      fitlers: [],
      materialTagFilter: {
        queryType: 2,
        tags: [],
      },
      activeLive: null,
    }
  },
  methods: {
    handlePageChange(data) {
      this.pageData.pageNum = data.pageNum
      this.pageData.pageSize = data.pageSize
      this.queryLists()
    },
    handleSelectLive(item) {
      this.$emit('input', item)
    },
  },
}
</script>

<style lang="less" module>
.live-content {
  display: flex;
  flex-direction: column;
  height: 100%;

  .live-content__list {
    flex: 1;
    overflow-y: auto;
    display: flex;
    flex-wrap: wrap;
    padding: 24px 24px 12px;

    .live-content__list-item {
      width: calc((100% - 24px) / 3);
      margin-right: 12px;
      margin-bottom: 12px;
      border-radius: 4px;
      overflow: hidden;
      border: 1px solid #e9edf5;
      box-sizing: border-box;
      position: relative;

      &:nth-child(3n) {
        margin-right: 0;
      }

      &.selected {
        border-color: var(--color-primary06, #407FFF);

        &::before {
          content: '\A0';
          display: inline-block;
          border: 2px solid #fff;
          border-top-width: 0;
          border-right-width: 0;
          width: 9px;
          height: 5px;
          transform: rotate(-50deg);
          position: absolute;
          top: 2px;
          left: 2px;
          z-index: 3;
        }

        &::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 0;
          height: 0;
          border-top: 25px solid var(--color-primary06);
          border-right: 25px solid transparent;
        }
      }

      .live-content__list-item-cover {
        width: 100%;
        height: 130px;

        .status {
          position: absolute;
          top: 10px;
          left: 20px;
        }
      }

      .live-content__list-item-title {
        font-size: 14px;
        color: #181c25;
        margin-top: 12px;
        padding: 0 13px;
        height: 42px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
      }
    }
  }

  .live-content__footer {
    height: 49px;
  }
}
</style>
