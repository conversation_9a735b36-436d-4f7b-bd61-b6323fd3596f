<template>
  <fx-dialog
    :class="$style['create-sop']"
    :visible="visible"
    title="选择SOP场景"
    width="800px"
    append-to-body
    @close="handleCancel"
  >
    <div :class="$style['create-sop__content']">
      <div
        v-for="item in sceneList"
        :key="item.type"
        :class="{
          [$style['content--item']]: true,
          [$style['active']]: activeScene && activeScene.type === item.type,
        }"
        @click="handleSelectScene(item)"
      >
        <i
          :class="['iconfont', item.icon]"
          :style="{ color: item.color }"
        />
        <span>{{ item.name }}</span>
      </div>
    </div>
    <div
      slot="footer"
      :class="$style['create-sop__footer']"
    >
      <fx-button
        size="small"
        type="primary"
        :class="$style['create-sop__footer--btn']"
        @click="handleSubmit"
      >
        确认
      </fx-button>
      <fx-button
        size="small"
        type="default"
        plain
        :class="$style['create-sop__footer--btn']"
        @click="handleCancel"
      >
        取消
      </fx-button>
    </div>
  </fx-dialog>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      sceneList: [
        {
          icon: 'iconhuodongyingxiao',
          name: '活动营销',
          color: 'rgb(255, 82, 42)',
          type: 'marketing_event',
        },
        {
          icon: 'iconzhiboyingxiao',
          name: '直播营销',
          color: 'rgb(22, 180, 171)',
          type: 'live',
        },
        {
          icon: 'iconhuiyiyingxiao',
          name: '会议营销',
          color: 'rgb(12, 108, 255)',
          type: 'conference',
        },
        {
          icon: 'iconweixingongzhonghaoyingxiao',
          name: '公众号营销',
          color: 'rgb(48, 199, 118)',
          type: 'wx_service_account',
        },
        {
          icon: 'iconqiyeweixin3',
          name: '企微营销',
          color: 'rgb(24, 157, 255)',
          type: 'qywx',
        },
        {
          icon: 'iconyoujian1',
          name: '邮件营销',
          color: 'rgb(24, 157, 255)',
          type: 'email',
        },
        {
          icon: 'iconguanwang',
          name: '官网运营',
          color: 'rgb(48, 199, 118)',
          type: 'official_website',
        },
      ],
      activeScene: null,
    }
  },
  methods: {
    handleSelectScene(item) {
      this.activeScene = item
    },
    handleCancel() {
      this.$emit('update:visible', false)
    },
    handleSubmit() {
      this.$emit('update:visible', false)
      this.$emit('submit', this.activeScene)
    },
  },
}
</script>

<style lang="less" module>
.create-sop {
  .create-sop__content {
    display: flex;
    flex-wrap: wrap;

    .content--item {
      display: flex;
      width: calc((100% - 24px) / 3);
      height: 96px;
      border-radius: 8px;
      border: 1px solid var(--color-neutrals04, #EAEBEE);
      background: #FFF;
      cursor: pointer;
      transition: transform 0.3s ease;
      margin-bottom: 12px;
      margin-right: 12px;
      box-sizing: border-box;
      font-size: 18px;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      position: relative;
      overflow: hidden;

      &:nth-child(3n) {
        margin-right: 0;
      }

      &:hover {
        box-shadow: 0 4px 12px rgba(255, 128, 0, 0.08);
        transform: translateY(-2px);
      }

      &.active {
        border: 1px solid var(--color-primary06, #407FFF);

        &::before {
          content: "\A0";
          display: inline-block;
          border: 2px solid #fff;
          border-top-width: 0;
          border-right-width: 0;
          width: 9px;
          height: 5px;
          transform: rotate(-50deg);
          position: absolute;
          top: 2px;
          left: 2px;
          z-index: 3;
        }

        &::after {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          width: 0;
          height: 0;
          border-top: 25px solid var(--color-primary06, #407FFF);
          border-right: 25px solid transparent;
        }
      }

      i {
        font-size: 24px;
      }
    }
  }
}
</style>
