<template>
  <div class="loadingSvg">
    <svg
      width="24"
      height="24"
      viewBox="0 0 48 48"
      class="circular"
    >
      <circle
        cx="24"
        cy="8"
        r="4"
        fill="none"
        transform="rotate(0 24 24)"
        class="path2"
      >
        <animate
          attributeName="r"
          values="2;4;2"
          dur="0.9s"
          keyTimes="0;0.5;1"
          calcMode="spline"
          keySplines="0.4 0 0.2 1; 0.4 0 0.2 1"
          repeatCount="indefinite"
          begin="0s"
        />
        <animate
          attributeName="opacity"
          values="1;0.8;1"
          dur="0.9s"
          repeatCount="indefinite"
          begin="0s"
        />
      </circle>
      <circle
        cx="24"
        cy="8"
        r="4"
        fill0="#000000"
        transform="rotate(45 24 24)"
        class="path2"
      >
        <animate
          attributeName="r"
          values="2;4;2"
          dur="0.9s"
          keyTimes="0;0.5;1"
          calcMode="spline"
          keySplines="0.4 0 0.2 1; 0.4 0 0.2 1"
          repeatCount="indefinite"
          begin="0.1125s"
        />
        <animate
          attributeName="opacity"
          values="1;0.8;1"
          dur="0.9s"
          repeatCount="indefinite"
          begin="0.1125s"
        />
      </circle>
      <circle
        cx="24"
        cy="8"
        r="4"
        fill0="#000000"
        transform="rotate(90 24 24)"
        class="path2"
      ><animate
        attributeName="r"
        values="2;4;2"
        dur="0.9s"
        keyTimes="0;0.5;1"
        calcMode="spline"
        keySplines="0.4 0 0.2 1; 0.4 0 0.2 1"
        repeatCount="indefinite"
        begin="0.225s"
      /><animate
        attributeName="opacity"
        values="1;0.8;1"
        dur="0.9s"
        repeatCount="indefinite"
        begin="0.225s"
      /></circle><circle
        cx="24"
        cy="8"
        r="4"
        fill0="#000000"
        transform="rotate(135 24 24)"
        class="path2"
      ><animate
        attributeName="r"
        values="2;4;2"
        dur="0.9s"
        keyTimes="0;0.5;1"
        calcMode="spline"
        keySplines="0.4 0 0.2 1; 0.4 0 0.2 1"
        repeatCount="indefinite"
        begin="0.3375s"
      /><animate
        attributeName="opacity"
        values="1;0.8;1"
        dur="0.9s"
        repeatCount="indefinite"
        begin="0.3375s"
      /></circle><circle
        cx="24"
        cy="8"
        r="4"
        fill0="#000000"
        transform="rotate(180 24 24)"
        class="path2"
      ><animate
        attributeName="r"
        values="2;4;2"
        dur="0.9s"
        keyTimes="0;0.5;1"
        calcMode="spline"
        keySplines="0.4 0 0.2 1; 0.4 0 0.2 1"
        repeatCount="indefinite"
        begin="0.45s"
      /><animate
        attributeName="opacity"
        values="1;0.8;1"
        dur="0.9s"
        repeatCount="indefinite"
        begin="0.45s"
      /></circle><circle
        cx="24"
        cy="8"
        r="4"
        fill0="#000000"
        transform="rotate(225 24 24)"
        class="path2"
      ><animate
        attributeName="r"
        values="2;4;2"
        dur="0.9s"
        keyTimes="0;0.5;1"
        calcMode="spline"
        keySplines="0.4 0 0.2 1; 0.4 0 0.2 1"
        repeatCount="indefinite"
        begin="0.5625s"
      /><animate
        attributeName="opacity"
        values="1;0.8;1"
        dur="0.9s"
        repeatCount="indefinite"
        begin="0.5625s"
      /></circle><circle
        cx="24"
        cy="8"
        r="4"
        fill0="#000000"
        transform="rotate(270 24 24)"
        class="path2"
      ><animate
        attributeName="r"
        values="2;4;2"
        dur="0.9s"
        keyTimes="0;0.5;1"
        calcMode="spline"
        keySplines="0.4 0 0.2 1; 0.4 0 0.2 1"
        repeatCount="indefinite"
        begin="0.675s"
      /><animate
        attributeName="opacity"
        values="1;0.8;1"
        dur="0.9s"
        repeatCount="indefinite"
        begin="0.675s"
      /></circle><circle
        cx="24"
        cy="8"
        r="4"
        fill0="#000000"
        transform="rotate(315 24 24)"
        class="path2"
      ><animate
        attributeName="r"
        values="2;4;2"
        dur="0.9s"
        keyTimes="0;0.5;1"
        calcMode="spline"
        keySplines="0.4 0 0.2 1; 0.4 0 0.2 1"
        repeatCount="indefinite"
        begin="0.7875s"
      /><animate
        attributeName="opacity"
        values="1;0.8;1"
        dur="0.9s"
        repeatCount="indefinite"
        begin="0.7875s"
      /></circle></svg>
  </div>
</template>

<script>
export default {

}
</script>

<style lang="less" scoped>
.loadingSvg {
  .path2 {
    fill: var(--color-primary06, #ff8000);
  }
}
</style>
