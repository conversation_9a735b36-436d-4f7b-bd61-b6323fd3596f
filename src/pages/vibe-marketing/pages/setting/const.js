import agentIcon from '@/assets/images/vibe-marketing/agent.png'
import sceneIcon from '@/assets/images/vibe-marketing/red-note.png'

export const agents = [
  {
    name: '营销Agent',
    type: 1,
    description: '说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明',
    icon: agentIcon,
    isActive: true,
  },
  {
    name: '营销Agent',
    type: 2,
    description: '说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明',
    icon: agentIcon,
    isActive: false,
  },
  {
    name: '营销Agent',
    type: 3,
    description: '说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明',
    icon: agentIcon,
    isActive: false,
  },
  {
    name: '营销Agent',
    type: 4,
    description: '说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明',
    icon: agentIcon,
    isActive: false,
  },
  {
    name: '营销Agent',
    type: 5,
    description: '说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明',
    icon: agentIcon,
    isActive: true,
  },
  {
    name: '营销Agent',
    type: 6,
    description: '说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明',
    icon: agentIcon,
    isActive: false,
  },
]

export const scenes = [
  {
    name: '小红书爆款笔记',
    type: 1,
    description: '轻松掌握流量密码，打造热门笔记',
    icon: sceneIcon,
    isActive: true,
  },
  {
    name: '小红书爆款笔记',
    type: 2,
    description: '轻松掌握流量密码，打造热门笔记',
    icon: sceneIcon,
    isActive: false,
  },
  {
    name: '小红书爆款笔记',
    type: 3,
    description: '轻松掌握流量密码，打造热门笔记',
    icon: sceneIcon,
    isActive: false,
  },
]
