<template>
  <div class="scene-list">
    <div
      v-for="(scene, index) in scenes"
      :key="scene.type"
      class="scene-item"
    >
      <img :src="scene.icon">
      <div class="scene-item-content">
        <div class="scene-item-title">
          {{ scene.name }}
        </div>
        <div class="scene-item-description">
          {{ scene.description }}
        </div>
        <div class="scene-item-actions">
          <fx-switch
            :value="scene.isActive"
            size="small"
            @change="(value) => handleSwitchChange(index, value)"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { scenes } from '../const.js'

export default {
  name: 'AgentList',
  data() {
    return {
      scenes,
    }
  },
  methods: {
    handleSwitchChange(index, value) {
      this.$set(this.scenes[index], 'isActive', value)
    },
  },
}
</script>

<style lang="less" scoped>
.scene-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  .scene-item {
    display: flex;
    min-width: 340px;
    box-sizing: border-box;
    width: calc((100% - 16px * 2) / 3);
    gap: 8px;
    border-radius: 8px;
    padding: 16px;
    border: 1px solid #DEE1E8;
    transition: all 0.3s ease;
    cursor: pointer;

    & > img {
      height: 40px;
      width: 40px;
      border-radius: 4px;
    }

    .scene-item-content {
      display: flex;
      flex: 1;
      flex-direction: column;

      .scene-item-title {
        line-height: 24px;
        font-size: 15px;
        font-weight: 700;
        color: var(--color-neutrals19, #181C25);
      }

      .scene-item-description {
        margin-top: 8px;
        line-height: 18px;
        font-size: 12px;
        color: var(--color-neutrals15, #545861);
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }

      .scene-item-actions {
        margin-top: 16px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
      }
    }
  }
}
</style>
