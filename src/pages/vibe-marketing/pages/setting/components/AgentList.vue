<template>
  <div class="agent-list">
    <div
      v-for="(agent, index) in agents"
      :key="agent.type"
      class="agent-item"
    >
      <img :src="agent.icon">
      <div class="agent-item-content">
        <div class="agent-item-title">
          {{ agent.name }}
        </div>
        <div class="agent-item-description">
          {{ agent.description }}
        </div>
        <div class="agent-item-actions">
          <fx-button
            size="mini"
            type="text"
          >
            设置
          </fx-button>
          <fx-switch
            :value="agent.isActive"
            size="small"
            @change="(value) => handleSwitchChange(index, value)"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { agents } from '../const.js'

export default {
  name: 'AgentList',
  data() {
    return {
      agents,
    }
  },
  methods: {
    handleSwitchChange(index, value) {
      this.$set(this.agents[index], 'isActive', value)
    },
  },
}
</script>

<style lang="less" scoped>
.agent-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  .agent-item {
    display: flex;
    min-width: 340px;
    box-sizing: border-box;
    width: calc((100% - 16px * 2) / 3);
    gap: 8px;
    border-radius: 8px;
    padding: 16px;
    border: 1px solid #DEE1E8;
    transition: all 0.3s ease;
    cursor: pointer;

    & > img {
      height: 40px;
      width: 40px;
      border-radius: 4px;
    }

    .agent-item-content {
      display: flex;
      flex: 1;
      flex-direction: column;

      .agent-item-title {
        line-height: 24px;
        font-size: 15px;
        font-weight: 700;
        color: var(--color-neutrals19, #181C25);
      }

      .agent-item-description {
        margin-top: 8px;
        line-height: 18px;
        font-size: 12px;
        color: var(--color-neutrals15, #545861);
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }

      .agent-item-actions {
        margin-top: 16px;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
    }
  }
}
</style>
