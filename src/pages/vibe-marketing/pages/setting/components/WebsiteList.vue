<template>
  <div class="website-list">
    <div
      v-for="website in websites"
      :key="website.type"
      class="website-item"
    >
      <div class="website-item-content">
        <div class="website-item-title">
          {{ website.title }}
        </div>
        <div class="website-item-link">
          {{ website.link }}
        </div>
        <div
          class="website-item-index"
          :class="{ 'is-index': website.isIndex }"
        >
          {{ website.isIndex ? '已索引' : '未索引' }}
        </div>
        <fx-dropdown
          class="website-item-dropdown"
          size="mini"
          placement="bottom"
        >
          <i class="fx-icon-more" />
          <fx-dropdown-menu slot="dropdown">
            <fx-dropdown-item v-if="!website.isIndex">
              <i class="el-icon-document" />
              <span>索引</span>
            </fx-dropdown-item>
            <fx-dropdown-item v-else>
              <i class="el-icon-document" />
              <span>重新索引</span>
            </fx-dropdown-item>
            <fx-dropdown-item>
              <i class="el-icon-edit" />
              <span>修改</span>
            </fx-dropdown-item>
            <fx-dropdown-item>
              <i class="el-icon-delete" />
              <span>删除</span>
            </fx-dropdown-item>
          </fx-dropdown-menu>
        </fx-dropdown>
      </div>
    </div>
    <div
      class="website-item-add"
    >
      <i class="el-icon-plus" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'WebsiteList',
  data() {
    return {
      websites: [
        {
          title: '纷享销客',
          link: 'https://www.baidu.com',
          isIndex: true,
        },
        {
          title: '百度',
          link: 'https://www.baidu.com',
          isIndex: false,
        },
      ],
    }
  },
}
</script>

<style lang="less" scoped>
.website-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;

  .website-item {
    position: relative;
    display: flex;
    min-width: 340px;
    height: 114px;
    box-sizing: border-box;
    width: calc((100% - 16px * 2) / 3);
    gap: 8px;
    border-radius: 8px;
    padding: 16px;
    border: 1px solid #DEE1E8;
    transition: all 0.3s ease;
    cursor: pointer;

    .website-item-index {
      position: absolute;
      box-sizing: border-box;
      right: 0;
      top: 0;
      padding: 0px 8px;
      border-radius: 0 4px 0 4px;
      background-color: #F5F7FA;
      font-size: 12px;
      line-height: 1;
      height: 24px;
      display: flex;
      align-items: center;
    }

    .is-index {
      color: #16B4AB;
      background-color: #E1F5F1;
    }

    .website-item-content {
      display: flex;
      flex: 1;
      flex-direction: column;

      .website-item-title {
        line-height: 24px;
        font-size: 15px;
        font-weight: 700;
        color: var(--color-neutrals19, #181C25);
      }

      .website-item-link {
        margin-top: 4px;
        line-height: 24px;
        font-size: 13px;
        color: #0C6CFF;
      }

      .website-item-dropdown {
        margin-top: 12px;
        font-size: 16px;
        align-self: flex-end;
      }
    }
  }

  .website-item-add {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    height: 114px;
    color: var(--color-info06, #ff8000);
    min-width: 340px;
    box-sizing: border-box;
    width: calc((100% - 16px * 2) / 3);
    border-radius: 8px;
    border: 1px solid #DEE1E8;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      border-color: var(--color-info06, #ff8000);
      box-shadow: 0 4px 12px rgba(12, 108, 255, 0.1);
    }
  }
}
</style>
