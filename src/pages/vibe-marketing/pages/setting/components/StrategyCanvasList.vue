<template>
  <div class="knowledge-space-list">
    <div
      v-for="knowledgeSpace in knowledgeSpaces"
      :key="knowledgeSpace.type"
      class="ks-item"
    >
      <div class="ks-item-content">
        <div class="ks-item-icon">
          <i class="iconfont iconneirongzhongxin" style="color: #189DFF;" />
        </div>
        <div class="ks-item-info">
          <div class="ks-item-title">
            {{ knowledgeSpace.title }}
          </div>
          <div class="ks-item-update-time">
            最后更新于{{ formatRelativeTime(knowledgeSpace.updateTime) }}
          </div>
        </div>
      </div>
      <div class="ks-item-description">
        {{ knowledgeSpace.description }}
      </div>
      <fx-dropdown
        class="ks-item-dropdown"
        size="mini"
        placement="bottom"
      >
        <i class="fx-icon-more" />
        <fx-dropdown-menu slot="dropdown">
          <fx-dropdown-item>
            <i class="el-icon-edit" />
            <span>修改</span>
          </fx-dropdown-item>
          <fx-dropdown-item>
            <i class="el-icon-delete" />
            <span>删除</span>
          </fx-dropdown-item>
        </fx-dropdown-menu>
      </fx-dropdown>
    </div>
  </div>
</template>

<script>
export default {
  name: 'KnowledgeSpaceList',
  data() {
    return {
      knowledgeSpaces: [
        {
          title: '产品画布',
          updateTime: '2025-05-27 19:45:00',
          description: '## 工作描述:包括常见问题解答的全面SEO优化文章 ##工作流程 第一步。开始写文章前，必须为关键词{{prompt}}开发一个全面的"大纲"，该大纲要包含至少18个吸引人的标题和副标题',
        },
        {
          title: '产品画布',
          updateTime: '2025-05-27 19:44:00',
          description: '## 工作描述:包括常见问题解答的全面SEO优化文章 ##工作流程 第一步。开始写文章前，必须为关键词{{prompt}}开发一个全面的"大纲"，该大纲要包含至少18个吸引人的标题和副标题',
        },
        {
          title: '产品画布',
          updateTime: '2025-05-27 10:00:00',
          description: '## 工作描述:包括常见问题解答的全面SEO优化文章 ##工作流程 第一步。开始写文章前，必须为关键词{{prompt}}开发一个全面的"大纲"，该大纲要包含至少18个吸引人的标题和副标题',
        },
        {
          title: 'SEO策略画布',
          updateTime: '2025-05-24 10:00:00',
          description: '## 工作描述:包括常见问题解答的全面SEO优化文章 ##工作流程 第一步。开始写文章前，必须为关键词{{prompt}}开发一个全面的"大纲"，该大纲要包含至少18个吸引人的标题和副标题',
        },
        {
          title: '客户旅程画布',
          updateTime: '2025-04-23 10:00:00',
          description: '## 工作描述:包括常见问题解答的全面SEO优化文章 ##工作流程 第一步。开始写文章前，必须为关键词{{prompt}}开发一个全面的"大纲"，该大纲要包含至少18个吸引人的标题和副标题',
        },
      ],
    }
  },
  methods: {
    formatRelativeTime(dateStr) {
      const date = new Date(dateStr)
      const now = new Date()
      const diffInSeconds = Math.floor((now - date) / 1000)

      if (diffInSeconds < 60) {
        return `${diffInSeconds}秒前`
      }

      const diffInMinutes = Math.floor(diffInSeconds / 60)
      if (diffInMinutes < 60) {
        return `${diffInMinutes}分钟前`
      }

      const diffInHours = Math.floor(diffInMinutes / 60)
      if (diffInHours < 24) {
        return `${diffInHours}小时前`
      }

      const diffInDays = Math.floor(diffInHours / 24)
      if (diffInDays < 30) {
        return `${diffInDays}天前`
      }

      // 超过一个月显示实际日期
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
      })
    },
  },
}
</script>

<style lang="less" scoped>
.knowledge-space-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;

  .ks-item {
    position: relative;
    display: flex;
    flex-direction: column;
    min-width: 340px;
    box-sizing: border-box;
    width: calc((100% - 16px * 2) / 3);
    gap: 12px;
    border-radius: 8px;
    padding: 16px;
    border: 1px solid #DEE1E8;
    transition: all 0.3s ease;
    cursor: pointer;

    .ks-item-icon {
      font-size: 16px;
      margin-right: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      border-radius: 6px;
      border: 1px solid #EAEBEE;
    }

    .ks-item-content {
      display: flex;

      .ks-item-content-wrapper {
        flex: 1;
        display: flex;
        flex-direction: column;
      }

      .ks-item-title {
        line-height: 24px;
        font-size: 15px;
        font-weight: 700;
        color: var(--color-neutrals19, #181C25);
      }

      .ks-item-description {
        margin-top: 4px;
        line-height: 24px;
        font-size: 13px;
        color: var(--color-neutrals15, #545861);
      }
    }
    .ks-item-dropdown {
      align-self: flex-end;
      font-size: 16px;
    }
  }
}
</style>
