<template>
  <div
    v-loading="loading"
    class="knowledge-space-list"
  >
    <div
      v-for="knowledgeSpace in knowledgeSpaces"
      :key="knowledgeSpace.type"
      class="ks-item"
    >
      <div class="ks-item-content">
        <div
          class="ks-item-icon"
        >
          <img
            :src="knowledgeSpace.logo"
          >
        </div>
        <div class="ks-item-content-wrapper">
          <div class="ks-item-title">
            {{ knowledgeSpace.title }}
          </div>
          <div class="ks-item-description">
            {{ knowledgeSpace.description }}
          </div>
          <div
            class="ks-item-index"
            :class="{ 'enabled': knowledgeSpace.enabled }"
          >
            {{ knowledgeSpace.enabled ? '已启用' : '已停用' }}
          </div>
        </div>
        <!-- <fx-dropdown
          class="ks-item-dropdown"
          size="mini"
          placement="bottom"
        >
          <i class="fx-icon-more" />
          <fx-dropdown-menu slot="dropdown">
            <fx-dropdown-item>
              启用
            </fx-dropdown-item>
            <fx-dropdown-item>
              停用
            </fx-dropdown-item>
          </fx-dropdown-menu>
        </fx-dropdown> -->
      </div>
    </div>
    <!-- <div
      class="ks-item-add"
    >
      <i class="el-icon-plus" />
    </div> -->
  </div>
</template>

<script>
import http from '@/services/http/index.js'

export default {
  name: 'KnowledgeSpaceList',
  data() {
    return {
      knowledgeSpaces: [],
      loading: true,
    }
  },
  mounted() {
    this.queryKnowledgeList()
  },
  methods: {
    queryKnowledgeList() {
      this.loading = true
      http.queryKnowledgeBaseList({
        recordType: 'default__c',
        sceneType: 1,
      }).then(res => {
        if (res && res.Result && res.Result.StatusCode === 0) {
          this.knowledgeSpaces = res.Value?.data?.knowledgeList?.map(el => ({
            title: el.sceneName,
            description: el.description || '这个管理员很懒，还没有写描述',
            enabled: el.status === 1,
            id: el.id,
            logo: el.knowledgeLogo,
          }))
        }
        this.loading = false
      })
    },
  },
}
</script>

<style lang="less" scoped>
.knowledge-space-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;

  .ks-item {
    position: relative;
    display: flex;
    min-width: 340px;
    height: 116px;
    box-sizing: border-box;
    width: calc((100% - 16px * 2) / 3);
    gap: 8px;
    border-radius: 8px;
    padding: 16px;
    border: 1px solid #DEE1E8;
    transition: all 0.3s ease;
    cursor: pointer;

    .ks-item-index {
      position: absolute;
      box-sizing: border-box;
      right: 0;
      top: 0;
      padding: 0px 8px;
      border-radius: 0 4px 0 4px;
      background-color: #F5F7FA;
      font-size: 12px;
      line-height: 1;
      height: 24px;
      display: flex;
      align-items: center;
    }

    .enabled {
      color: #16B4AB;
      background-color: #E1F5F1;
    }

    .ks-item-icon {
      box-sizing: border-box;
      padding: 8px;
      margin-right: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      border-radius: 6px;
      border: 1px solid #EAEBEE;

      img {
        width: 100%;
        height: 100%;
      }
    }

    .ks-item-content {
      display: flex;

      .ks-item-content-wrapper {
        flex: 1;
        display: flex;
        flex-direction: column;
      }

      .ks-item-title {
        line-height: 24px;
        font-size: 15px;
        font-weight: 700;
        color: var(--color-neutrals19, #181C25);
      }

      .ks-item-description {
        margin-top: 4px;
        line-height: 24px;
        font-size: 13px;
        color: var(--color-neutrals15, #545861);
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }

      .ks-item-dropdown {
        position: absolute;
        right: 16px;
        bottom: 16px;
        font-size: 16px;
      }
    }
  }

  .ks-item-add {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    height: 116px;
    color: var(--color-info06, #ff8000);
    min-width: 340px;
    box-sizing: border-box;
    width: calc((100% - 16px * 2) / 3);
    border-radius: 8px;
    border: 1px solid #DEE1E8;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      border-color: var(--color-info06, #ff8000);
      box-shadow: 0 4px 12px rgba(12, 108, 255, 0.1);
    }
  }
}
</style>
