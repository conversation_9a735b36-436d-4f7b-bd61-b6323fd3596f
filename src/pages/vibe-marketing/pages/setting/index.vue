<template>
  <div class="m-base-setting-wrapper">
    <div
      class="setting-item"
      :style="{ background: setting ? '#E9F2FF' : '#ffffff' }"
    >
      <div class="base-info">
        <div class="base-info-content">
          <div class="set-title">
            {{ $t('marketing.commons.gsjs_59c473') }}
          </div>
          <div class="row">
            <span>
              <span>企业LOGO</span>
              <QuestionTooltip class="data-qtip">
                <div
                  slot="question-content"
                  class="sum-question-content"
                >
                  {{ $t('marketing.commons.gjxszgsdtq_e0a9d7') }}
                </div>
              </QuestionTooltip>
            </span>
            <div class="row-right">
              <div class="row-item">
                <span class="title">
                  {{ $t('marketing.components.PictureSelector.cc_aed516') }}<span v-if="setting">{{ $t('marketing.pages.setting.zcjyccxszd_78852a') }}</span>
                </span>
                <div class="content">
                  <PictureListUpload
                    ref="uploadComp"
                    class="upload-comp upload-size1"
                    :photolist="coverlist1"
                    :max="1"
                    :max-size="maxSize"
                    :tapathlist.sync="coverTapathlist"
                    :error-code.sync="coverErrorCode"
                    :single-change="true"
                  />
                  <div
                    v-if="setting"
                    class="change-img-btn-wrapper"
                  >
                    <span
                      v-if="setting"
                      class="change-img-btn"
                      @click="changeImg(1)"
                    >{{ $t('marketing.commons.ggtp_38fe99') }}</span>
                  </div>
                </div>
              </div>
              <div class="row-item">
                <span
                  v-if="!setting"
                  class="title"
                >
                  {{ $t('marketing.components.PictureSelector.cc_c10847') }}{{ $t('marketing.pages.setting.xs_bf13c5') }}
                </span>
                <span
                  v-else
                  class="title"
                >{{ $t('marketing.pages.setting.zcjyccxszd_6a9444') }}</span>
                <div class="content">
                  <PictureListUpload
                    ref="uploadComp"
                    class="upload-comp upload-size2"
                    :photolist="coverlist2"
                    :max="1"
                    :max-size="maxSize"
                    :tapathlist.sync="coverTapathlist"
                    :error-code.sync="coverErrorCode"
                    :single-change="true"
                  />
                  <div class="change-img-btn-wrapper">
                    <span
                      v-if="setting"
                      class="change-img-btn"
                      @click="changeImg(2)"
                    >{{ $t('marketing.commons.ggtp_38fe99') }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="row">
            <span>公司名称</span>
            <span
              v-if="!setting"
              class="value"
            >{{ fullName }}</span>
            <div
              v-else
              class="name-input"
            >
              <input
                v-model="fullName"
                type="text"
                name="fullName"
              >
            </div>
          </div>
          <div class="row">
            <span>
              <span>公司简称</span>
              <QuestionTooltip class="data-qtip">
                <div
                  slot="question-content"
                  class="sum-question-content"
                >
                  {{ $t('marketing.commons.gjcjxszgsd_5accf9') }}
                </div>
              </QuestionTooltip>
            </span>
            <span
              v-if="!setting"
              class="value"
            >{{ shortName }}</span>
            <div
              v-else
              class="name-input"
            >
              <input
                v-model="shortName"
                type="text"
                name="shortName"
              >
            </div>
          </div>
          <div class="row">
            <span>
              <span>企业常用颜色</span>
              <QuestionTooltip class="data-qtip">
                <div
                  slot="question-content"
                  class="sum-question-content"
                >
                  {{ $t('marketing.commons.gjcjxszgsd_5accf9') }}
                </div>
              </QuestionTooltip>
            </span>
            <div
              class="value color-picker-wrapper"
            >
              <template v-if="colorList.length > 0">
                <fx-color-picker
                  v-for="(colorItem, index) in colorList"
                  :key="colorItem"
                  :value="colorItem"
                  show-hue
                  show-preview
                  color-format="rgb"
                  size="mini"
                  :append-to-body="false"
                  @change="(color) => handleColorItemChange(color, index)"
                />
              </template>
              <span v-else-if="!setting">
                --
              </span>
              <template v-if="setting">
                <fx-button
                  size="micro"
                  icon="fx-icon-add-2"
                  plain
                  square
                  @click="openColorPicker"
                />
                <fx-color-picker
                  ref="colorAdd"
                  v-model="color"
                  class="color-picker"
                  show-hue
                  show-preview
                  color-format="rgb"
                  size="mini"
                  :append-to-body="false"
                  @change="handleColorChange"
                />
              </template>
            </div>
          </div>
          <div
            v-if="setting"
            class="btn-group"
          >
            <fx-button
              type="primary"
              size="mini"
              :disabled="!isSubmitReady"
              @click="saveSetting"
            >
              {{ $t('marketing.commons.bc_be5fbb') }}
            </fx-button>
            <fx-button
              type="default"
              size="mini"
              @click="cancel"
            >
              {{ $t('marketing.commons.qx_625fb2') }}
            </fx-button>
          </div>
        </div>
        <div
          v-if="!setting"
          class="btn-setting"
        >
          <a @click="settingStatus(true)">{{ $t('marketing.commons.sz_e366cc') }}</a>
        </div>
      </div>
    </div>
    <div
      class="setting-item"
    >
      <div class="set-title">
        营销 Agent
      </div>
      <AgentList />
    </div>
    <div
      class="setting-item"
    >
      <div class="set-title">
        官网
      </div>
      <WebsiteList />
    </div>
    <div
      class="setting-item"
    >
      <div class="set-title">
        策略画布
      </div>
      <CanvasList />
    </div>
    <div
      class="setting-item"
    >
      <div class="set-title">
        营销知识空间
      </div>
      <KnowledgeSpaceList />
    </div>
    <div
      class="setting-item"
    >
      <div class="set-title">
        营销场景
      </div>
      <MarketingSceneList />
    </div>
    <PictureSelector
      :visible.sync="isShowCutterDialog"
      :cut-size="cutSize"
      :need-apath="true"
      output-path-type="a"
      @submit="handlePictureSelectorSubmit"
    />
  </div>
</template>

<script>
import { createNamespacedHelpers } from 'vuex'

import defaultImg from '@/assets/images/def-enterprice.jpg'
import acrossDefaultImg from '@/assets/images/card-logo.jpg'
import PictureListUpload from '@/components/picture-list-upload/picture-list-upload.js'
import QuestionTooltip from '@/components/questionTooltip/index.vue'
import PictureSelector from '@/components/PictureSelector/index.vue'
import wxList from '@/modules/wx-list.js'
import AgentList from './components/AgentList.vue'
import WebsiteList from './components/WebsiteList.vue'
import KnowledgeSpaceList from './components/KnowledgeSpaceList.vue'
import MarketingSceneList from './components/MarketingSceneList.vue'
import CanvasList from './components/StrategyCanvasList.vue'

const { mapState, mapActions, mapMutations } = createNamespacedHelpers(
  'EnterpriseInfoSetting',
)

export default {
  components: {
    PictureListUpload,
    QuestionTooltip,
    PictureSelector,
    AgentList,
    WebsiteList,
    KnowledgeSpaceList,
    MarketingSceneList,
    CanvasList,
  },
  data() {
    return {
      defaultImg,
      coverlist1: [defaultImg],
      coverlist2: [acrossDefaultImg],
      coverTapathlist: [],
      coverErrorCode: '',
      colorList: [],
      fullName: '',
      shortName: '',
      iconUrl1: '',
      iconUrl2: '',
      taIconPath1: '',
      taIconPath2: '',
      maxSize: 1024 * 1024 * 2,
      isShowCutterDialog: false,
      wxLists: [],
      cutSize: {
        width: 300,
        height: 300,
      },
      cutSizeType: 1,
      color: '#409EFF',

    }
  },
  computed: {
    ...mapState({
      enterpriseInfo: state => state.enterpriseInfo,
      setting: state => state.setting,
    }),
    isSubmitReady() {
      return this.fullName && this.shortName && this.iconUrl1 && this.iconUrl2
    },
  },
  watch: {
    enterpriseInfo: {
      handler(newVal) {
        this.fullName = newVal.fullName
        this.shortName = newVal.shortName
        this.iconUrl1 = /https?/.test(newVal.iconUrl) ? newVal.iconUrl : defaultImg
        this.iconUrl2 = /https?/.test(newVal.iconAcrossUrl) ? newVal.iconAcrossUrl : acrossDefaultImg
        this.id = newVal.id
        this.coverlist1 = [this.iconUrl1]
        this.coverlist2 = [this.iconUrl2]
        this.useLogo = newVal.drawQrcodeElogo === 1
      },
      deep: true,
    },
  },
  mounted() {
    this.queryEnterpriseInfo()
    wxList.queryList().then(datas => {
      this.wxLists = datas.list || []
    })
  },
  methods: {
    ...mapActions(['setEnterpriseInfo', 'queryEnterpriseInfo']),
    ...mapMutations(['settingStatus']),
    updateCover([logo]) {
      this.iconUrl1 = logo
      this.taIconPath1 = this.coverTapathlist[0] || ''
    },
    handleColorItemChange(color, index) {
      this.$nextTick(() => {
        this.$set(this.colorList, index, color)
      })
    },
    handleColorChange(color) {
      this.colorList.push(color)
    },
    openColorPicker() {
      // 使用 $refs 访问 el-color-picker 的内部 input 元素
      this.$nextTick(() => {
        const { colorAdd } = this.$refs
        const input = colorAdd.$el.querySelector('.el-color-picker__trigger')
        if (input) {
          input.click() // 模拟点击，打开颜色选择器
        }
      })
    },
    changeImg(size) {
      this.cutSizeType = size
      this.cutSize = size === 2
        ? { width: 200, height: 50 }
        : { width: 300, height: 300 }
      this.isShowCutterDialog = true
    },
    saveSetting() {
      const option = {
        ext: /A_/.test(this.taIconPath)
          ? this.taIconPath.split('.')[1]
          : this.enterpriseInfo.ext,
        fullName: this.fullName,
        shortName: this.shortName,
        drawQrcodeElogo: this.useLogo ? 1 : 2,
      }
      if (this.taIconPath1) {
        option.iconPath = this.taIconPath1
      }
      if (this.taIconPath2) {
        option.iconAcrossPath = this.taIconPath2
      }
      const payload = {
        option,
        iconUrl: this.iconUrl1,
        iconAcrossUrl: this.iconUrl2,
      }
      this.setEnterpriseInfo(payload)
    },
    cancel() {
      this.settingStatus(false)
      const { fullName, shortName, iconUrl } = this.enterpriseInfo
      this.fullName = fullName
      this.shortName = shortName
      this.iconUrl = iconUrl || defaultImg
      this.coverlist = [this.iconUrl]
    },
    handlePictureSelectorSubmit(file) {
      const type = this.cutSizeType
      if (type === 1) {
        this.iconUrl1 = file.url
        this.taIconPath1 = file.path
        this.coverlist1 = [file.url]
      } else {
        this.iconUrl2 = file.url
        this.taIconPath2 = file.path
        this.coverlist2 = [file.url]
      }
    },
  },
}
</script>

<style lang="less">
.m-base-setting-wrapper {
  flex: 1 1 auto;
  background: #fff;
  font-size: 13px;
  a {
    color: @color-link;
  }
  .btn-setting {
    align-self: center;
    a {
      cursor: pointer;
    }
  }
  .btn-group {
    margin-top: 13px;
    margin-bottom: 20px;
    margin-left: 113px;
    button {
      padding: 8px 30px;
      margin-right: 12px;
      margin-left: 0;
    }
  }
  .setting-item {
    display: flex;
    flex-direction: column;
    padding: 16px;
    a {
      cursor: pointer;
    }
    .set-title {
      margin-bottom: 12px;
      display: flex;
      align-items: center;
      font-size: 15px;
      font-style: normal;
      font-weight: 500;
      line-height: 24px; /* 160% */
      color: var(--color-neutrals19, #181C25);

        &:before {
          content: '';
          display: inline-block;
          width: 4px;
          height: 14px;
          background-color: var(--color-primary06,#ff8000);
          border-radius: 2px;
          margin-right: 6px;
        }
    }
    .ent-img {
      width: 60px;
      height: 60px;
    }
    .upload-comp {
      flex: 0 0 auto;
      .box_showPicture {
        width: 60px;
        height: 60px;
        .hb_btn {
          &:first-child {
            display: none;
          }
        }
      }
      .hover-box {
        display: none !important;
      }
      .km-ico-close {
        display: none !important;
      }
    }
    .upload-size2 {
      .box_showPicture {
        width: 200px;
        height: 50px;
        background-color: #eaeaea;
      }
    }
    .name-input {
      width: 451px;
      border: 1px solid #ddd;
      border-radius: 3px;
      box-sizing: border-box;
      padding: 6px 10px;
      background-color: #fff;
      input {
        width: 100%;
        border: none;
      }
    }

    .color-item {
      width: 20px;
      height: 20px;
      border-radius: 4px;
      margin-right: 8px;
    }
    .color-picker-wrapper {
      position: relative;
      display: flex;
      align-items: center;
    }
    .color-picker {
      visibility: hidden;
    }
    .color-item-add {
      width: 20px;
      height: 20px;
      border-radius: 4px;
      margin-right: 8px;
      background-color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-size: 24px;
      line-height: 1;
      color: var(--color-neutrals11, #91959E);
      border: 1px solid var(--color-neutrals04, #EAEBEE);
    }
    .change-img-btn-wrapper {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      margin-left: 15px;
    }
    .change-img-btn {
        font-size: 14px;
        color: var(--color-info06);
        position: relative;
        cursor: pointer;
        height: 25px;
        input[type="file"] {
          position: absolute;
          left: 0;
          opacity: 0;
          cursor: pointer;
          -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
        }
      }
    .row {
      display: flex;
      line-height: 18px;

      .row-right {
        .row-item {
          &:first-child {
            margin-bottom: 20px;
          }
          .title {
            margin-bottom: 10px;
            display: inline-block;
          }
          .content{
            display: flex;
            align-items: flex-end;
          }
        }
      }
      .data-qtip {
        margin-left: 4px;
        width: 16px;
        height: 16px;
      }
      .box {
        margin-bottom: 0px;
      }
      .value {
        color: var(--color-neutrals19, #181C25);
      }
      &:first-child {
        align-items: flex-start;
        margin-bottom: 40px;
        height: 60px;
      }
      > span {
        font-size: 13px;
        color: var(--color-neutrals19, #181C25);
        line-height: 32px;
        height: 32px;
        display: flex;
        align-items: center;

        &:first-child {
          width: 115px;
          display: flex;
        }
      }
    }

    .row + .row {
      margin-top: 20px;
    }
  }
  .base-info {
    display: flex;
    justify-content: space-between;
  }
}
</style>
