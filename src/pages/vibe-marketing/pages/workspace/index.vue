<template>
  <div :class="$style.vibeMarketingWorkspace">
    <div
      v-loading="isLoading"
      :class="$style.vibeMarketingWorkspace__content"
    >
      <div :class="$style.listWrapper">
        <div
          v-for="(item, index) in listData"
          :key="index"
          :class="$style.listItem"
        >
          <div
            v-if="item.workspaceDetail"
            :class="$style.listItemHeader"
            @click="handleItemClick(item)"
          >
            <div :class="$style.listItemHeaderIcon">
              <img
                :src="item.workspaceDetail.icon__c"
                :class="$style.listItemHeaderIconImg"
              >
            </div>
            <div :class="$style.listItemHeaderContent">
              <div :class="$style.listItemHeaderTitle">
                {{ item.workspaceDetail.name }}
              </div>
              <div :class="$style.listItemHeaderType">
                {{ getWorkspaceType(item.workspaceDetail.works_space_type__c) }}
              </div>
            </div>
          </div>
          <div
            v-if="item.workspaceDetail"
            :class="$style.listItemDescription"
            @click="handleItemClick(item)"
          >
            {{ item.workspaceDetail.summary__c }}
          </div>
          <div :class="$style.listItemOptions">
            <div :class="$style.optionsMore">
              <fx-dropdown
                size="small"
                @command="command => handleCommand(command, node, item)"
              >
                <img
                  :src="iconMore"
                  :class="$style.optionsMoreIcon"
                >
                <fx-dropdown-menu slot="dropdown">
                  <fx-dropdown-item command="edit">
                    {{ $t('marketing.commons.xg_8347a9') }}
                  </fx-dropdown-item>
                  <fx-dropdown-item command="delete">
                    {{ $t('marketing.commons.sc_2f4aad') }}
                  </fx-dropdown-item>
                </fx-dropdown-menu>
              </fx-dropdown>
            </div>
          </div>
        </div>
      </div>

      <Empty
        v-if="listData.length === 0"
        class="empty"
        :title="$t('marketing.commons.zwsj_21efd8')"
      />
    </div>

    <div :class="$style.vibeMarketingWorkspace__pagination">
      <v-pagen
        :pagedata.sync="pageData"
        @change="handlePageChange"
      />
    </div>

    <WorkspaceDialog
      v-if="workspaceDialogVisible"
      :visible="workspaceDialogVisible"
      :origin-workspace-detail="activeWorkspace.workspaceDetail"
      @cancel="handleWorkspaceDialogCancel"
      @submit="handleWorkspaceDialogSubmit"
    />
  </div>
</template>

<script>
import http from '@/services/http/index.js'
import VPagen from '@/components/kitty/pagen.vue'
import Empty from '@/components/common/empty.vue'
import iconMore from '@/assets/images/icon-more.svg'
import WorkspaceDialog from '@/pages/vibe-marketing/components/workspace-dialog.vue'

export default {
  name: 'VibeMarketingWorkspace',
  components: {
    VPagen,
    Empty,
    WorkspaceDialog,
  },
  data() {
    return {
      iconMore,
      isLoading: true,
      listData: [],
      pageData: {
        layout: 'prev, pager, next, total, sizes, jumper',
        pageNum: 1,
        totalCount: 0,
        pageSize: 12,
        pageSizes: [12, 24, 36, 48],
      },
      workspaceDialogVisible: false,
      activeWorkspace: null,
    }
  },
  mounted() {
    this.workspaceCache = {}
    this.fetchSessionList()
  },
  methods: {
    fetchSessionList() {
      this.isLoading = true

      const payload = {
        objectAPIName: 'VMWorkspaceUserObj__c',
        pageNum: this.pageData.pageNum,
        pageSize: this.pageData.pageSize,
        selectFields: ['chat_session_id__c', 'workspace_id__c', 'user_id__c'],
        query: JSON.stringify({
          filters: [],
        }),
      }

      http.pageQueryObjDatas(payload)
        .then(res => {
          const { errCode, data } = res
          if (errCode === 0) {
            this.pageData.totalCount = data.totalCount
            return data.result || []
          }

          return []
        })
        .then(list => {
          const listDataPromise = list.map(record => this.fetchWorkspaceDetail(record))
          return Promise.all(listDataPromise)
        })
        .then(list => {
          this.listData = list
        })
        .finally(() => {
          this.isLoading = false
        })
    },
    fetchWorkspaceDetail(record) {
      const cache = this.workspaceCache[record.workspace_id__c]
      if (cache) {
        return Promise.resolve(cache)
      }

      const payload = {
        objectAPIName: 'VMWorkspaceObj__c',
        selectFields: ['name', 'summary__c', 'works_space_type__c', 'icon__c', 'marketing_event_id__c'],
        id: record.workspace_id__c,
      }

      return http.queryObjDataById(payload)
        .then(res => {
          const { errCode, data } = res
          if (errCode === 0) {
            return data
          }

          return null
        })
        .then(data => {
          const newRecord = {
            ...record,
            workspaceDetail: data,
          }
          this.workspaceCache[record.workspace_id__c] = newRecord
          return newRecord
        })
    },
    updateWorkspaceUser(record) {
      this.workspaceCache[record.workspace_id__c] = null
      this.fetchWorkspaceDetail(record)
        .then(data => {
          const index = this.listData.findIndex(item => item._id === record._id)
          this.$set(this.listData, index, data)
        })
    },
    getWorkspaceType(type) {
      switch (type) {
        case 'content':
          return $t('marketing_pd.commons.nr_2d711b')
        case 'campaign':
          return $t('marketing_pd.commons.hd_36c6f5')
        case 'seo':
          return 'SEO'
        case 'ad':
          return $t('marketing.commons.gg_bacd6f')
        case 'social':
          return $t('marketing.pages.vibe_marketing.sm_2a63ae')
        default:
          return ''
      }
    },
    handleCommand(command, node, item) {
      if (command === 'edit') {
        // this.handleEditObject(item)
        this.handleWorkspaceDialogShow(item)
      }

      if (command === 'delete') {
        this.handleDeleteObject(item)
      }
    },
    handleEditObject(record) {
      FS.util.FHHApi(
        {
          url: '/EM1HNCRM/API/v1/object/VMWorkspaceObj__c/controller/Detail',
          data: {
            objectDescribeApiName: 'VMWorkspaceObj__c',
            objectDataId: record.workspace_id__c,
          },
          success: ({ Result: { StatusCode }, Value }) => {
            if (StatusCode === 0) {
              CRM.api.edit({
                data: Value.data,
                title: $t('marketing.commons.bjgzkjyh_2fce97'),
                apiname: 'VMWorkspaceObj__c',
                show_type: 'full',
                dataId: record.workspace_id__c,
                nonEditable: true,
                success: () => {
                  this.updateWorkspaceUser(record)
                },
              })
            }
          },
        },
        {
          errorAlertModel: 1,
        },
      )
    },
    handleDeleteObject(record) {
      FxUI.MessageBox.confirm(
        $t('marketing.pages.vibe_marketing.sfscdqjl_800fb6'),
        $t('marketing_pd.commons.ts_02d981'),
        {
          confirmButtonText: $t('marketing.pages.vibe_marketing.scjl_a79020'),
          cancelButtonText: $t('marketing_pd.commons.qx_625fb2'),
          type: 'warning',
        },
      )
        .then(() => {
          const payload = {
            dataList: [
              {
                object_describe_api_name: 'VMWorkspaceUserObj__c',
                tenant_id: window.CRM ? window.CRM.enterpriseId : '',
                _id: record._id,
              },
            ],
          }
          FS.util.FHHApi(
            {
              url: '/EM1HNCRM/API/v1/object/VMWorkspaceUserObj__c/action/BulkInvalid',
              data: {
                json: JSON.stringify(payload),
              },
              success: ({ Result: { StatusCode }, Value }) => {
                if (StatusCode === 0) {
                  this.listData = this.listData.filter(item => item._id !== record._id)
                }
              },
            },
            {
              errorAlertModel: 1,
            },
          )
        })
    },
    handlePageChange(data) {
      this.pageData.pageNum = data.pageNum
      this.pageData.pageSize = data.pageSize
      this.fetchSessionList()
    },
    handleItemClick(item) {
      const { workspace_id__c } = item
      this.$router.push({
        name: 'vibe-marketing-agent',
        query: {
          workspaceId: workspace_id__c,
        },
      })
    },
    handleWorkspaceDialogCancel() {
      this.workspaceDialogVisible = false
      this.activeWorkspace = null
    },
    handleWorkspaceDialogShow(item) {
      this.workspaceDialogVisible = true
      this.activeWorkspace = item
    },
    handleWorkspaceDialogSubmit() {
      this.updateWorkspaceUser(this.activeWorkspace)
    },
  },
}
</script>

<style lang="less" module>
.vibeMarketingWorkspace {
  height: 100%;
  display: flex;
  flex-direction: column;

  .vibeMarketingWorkspace__content {
    flex: 1;
    overflow-y: auto;
    padding: 16px 0;
    background: #FFF;
  }

  .listWrapper {
    display: flex;
    flex-wrap: wrap;

    .listItem {
      padding: 16px 16px 0;
      border: 1px solid #DEE1E8;
      border-radius: 6px;
      margin-left: 16px;
      margin-bottom: 16px;
      width: calc((100% - 16px * 4) / 3);
      box-sizing: border-box;
      transition: all 0.3s ease;
      cursor: pointer;
      display: flex;
      flex-direction: column;

      &:hover {
        box-shadow: 0 4px 12px rgba(255, 128, 0, 0.08);
        transform: translateY(-2px);
      }

      .listItemHeader {
        display: flex;
        align-items: center;
        flex: none;

        .listItemHeaderIcon {
          width: 40px;
          height: 40px;
          background: #F5F7FA;

          .listItemHeaderIconImg {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        .listItemHeaderContent {
          margin-left: 6px;

          .listItemHeaderTitle {
            font-size: 15px;
            font-weight: bold;
            color: #181C25;
            line-height: 24px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
          }

          .listItemHeaderType {
            font-size: 12px;
            color: #545861;
            line-height: 18px;
            margin-top: 4px;
          }
        }
      }

      .listItemDescription {
        font-size: 12px;
        color: #545861;
        line-height: 18px;
        margin-top: 12px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        height: 54px;
      }

      .listItemOptions {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        font-size: 16px;
        color: #181C25;
        flex: none;

        .optionsMoreIcon {
          width: 16px;
          height: 16px;
        }
      }
    }
  }
}
</style>
