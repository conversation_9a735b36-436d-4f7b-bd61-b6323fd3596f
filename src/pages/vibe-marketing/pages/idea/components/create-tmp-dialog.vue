<template>
  <fx-dialog
    :class="$style['create-tmp-dialog']"
    :visible="visible"
    :title="title"
    width="1000px"
    append-to-body
    @close="handleCancel"
  >
    <div :class="$style['create-tmp-dialog__content']">
      <CreateTmp
        :id="id"
        ref="createTmp"
        @update:title="handleUpdateTitle"
      />
    </div>
    <div
      slot="footer"
      :class="$style['create-tmp-dialog__footer']"
    >
      <fx-button
        size="small"
        type="default"
        plain
        :class="$style['create-tmp-dialog__footer--btn']"
        @click="handleCancel"
      >
        {{ $t('marketing_taro.commons.qx_625fb2') }}
      </fx-button>
      <fx-button
        size="small"
        type="primary"
        :class="$style['create-tmp-dialog__footer--btn']"
        @click="handleSave"
      >
        {{ $t('marketing_taro.commons.bc_be5fbb') }}
      </fx-button>
    </div>
  </fx-dialog>
</template>

<script>
import http from '@/services/http/index.js'
import CreateTmp from './create-tmp.vue'

export default {
  components: {
    CreateTmp,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    id: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      title: $t('marketing.commons.xjcy_ccb3df'),
      isLoading: false,
    }
  },
  methods: {
    handleUpdateTitle(title) {
      this.title = title
    },
    handleCancel() {
      this.$emit('onClose')
    },
    handleSave() {
      if (this.isLoading) return

      const params = this.$refs.createTmp.getSaveParams()
      if (!params) return

      this.isLoading = true
      const api = this.id ? 'aiChatEditPrompt' : 'aiChatCreatePrompt'
      http[api](params).then(res => {
        this.isLoading = false
        if (res.errCode === 0) {
          FxUI.Message.success($t('marketing_taro.commons.bccg_3b1083'))
          setTimeout(() => {
            this.$emit('onSave')
            this.handleCancel()
          }, 2000)
        }
      }).catch(error => {
        this.isLoading = false
      })
    },
  },
}
</script>

<style lang="less" module>
.create-tmp-dialog {

  :global {
    .el-dialog__body {
      padding: 0;
    }
  }

  .create-tmp-dialog__content {
    height: 60vh;
  }

  .create-tmp-dialog__footer {
    display: flex;
    box-sizing: border-box;
    justify-content: flex-end;

    .create-tmp-dialog__footer--btn {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 32px;
      line-height: 32px;
      border-radius: 2px;
      font-weight: 400;
      font-size: 13px;
      cursor: pointer;
      padding: 0 15px;
      margin-right: 16px;
      width: 100px;

      &:last-of-type {
        margin-right: 0;
      }

      .icon {
        width: 16px;
        height: 16px;
        margin-right: 4px;
        font-size: 0;
        vertical-align: text-bottom;
      }
    }
  }
}
</style>
