<template>
  <div :class="classPrefix">
    <div :class="`${classPrefix}__content`">
      <div
        :class="[`${classPrefix}__form`, 'wide', { preview: isPreview }]"
      >
        <div :class="`${classPrefix}__content--prompt-wrap`">
          <div :class="`${classPrefix}__content--prompt`">
            <div :class="`${classPrefix}__content--mustache-label`">
              <span>{{ $t('marketing_taro.pkgs.tscyl_cc7774') }}</span>
              <img
                :src="isPreview ? iconFold : iconUnfold"
                class="icon"
                @click="handlePreviewToggle"
              >
            </div>
            <div :class="`${classPrefix}__content--mustache-input-wrap`">
              <fx-input
                v-model="promptDetail.content"
                type="textarea"
                autofocus
                size="small"
                :class="`${classPrefix}__content--mustache-input`"
                :placeholder="$t('marketing_taro.commons.qsr_02cc4f')"
              />
            </div>
          </div>
        </div>
        <div :class="`${classPrefix}__content--mustaches`">
          <div :class="`${classPrefix}__content--mustaches-content`">
            <div :class="`${classPrefix}__content--mustache-label title`">
              <span>{{ $t('marketing_taro.pkgs.mbsz_f8d568') }}</span>
            </div>
            <div :class="`${classPrefix}__content--mustache textarea`">
              <div :class="`${classPrefix}__content--mustache-label`">
                <span class="require">*</span>
                {{ $t('marketing_taro.pkgs.qsrtsybt_f38dc2') }}
              </div>
              <fx-input
                v-model="promptDetail.title"
                size="small"
                :class="`${classPrefix}__content--mustache-input`"
                :placeholder="$t('marketing_taro.commons.qsr_02cc4f')"
              />
            </div>
            <div :class="`${classPrefix}__content--mustache select`">
              <div :class="`${classPrefix}__content--mustache-label`">
                <span class="require">*</span>
                {{ $t('marketing_taro.pkgs.qxzfl_8bb820') }}
              </div>
              <fx-select
                v-model="promptDetail.category"
                :options="categoryList"
                :class="`${classPrefix}__content--mustache-input`"
                size="small"
              />
            </div>
            <div :class="`${classPrefix}__content--mustache select`">
              <div :class="`${classPrefix}__content--mustache-label`">
                <span class="require">*</span>
                {{ $t('marketing_taro.pkgs.qxzkjfw_e48369') }}
              </div>
              <fx-select
                v-model="promptDetail.scope_type"
                :options="scopeTypeList"
                :class="`${classPrefix}__content--mustache-input`"
                size="small"
              />
            </div>
          </div>
          <div :class="`${classPrefix}__content--mustaches-tips`">
            <div>{{ $t('marketing_taro.pkgs.smtsmbzcyx_712f84') }}</div>
            <div>
              {{ $t('marketing_taro.pkgs.wbcs_2c087b') }}
              {{ '{' }}{{ '{' }}
              {{ $t('marketing_taro.pkgs.wbmc_4c7555') }}
              {{ `}` }}{{ `}` }}
            </div>
            <div>
              {{ $t('marketing_taro.pkgs.xxcs_7559c7') }}
              {{ '[[' + $t('marketing_taro.pkgs.xxmc_f69608') + ' | {' + $t('marketing_taro.pkgs.xxzxxzxxz_b86af4') + '}]]' }}
            </div>
            <div>
              {{ $t('marketing_taro.pkgs.dxcs_4d1a2a') }}
              {{ '[[' + $t('marketing_taro.pkgs.dxmc_06cef4') + '｜ApiName]]' }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { $cdnPath } from '@/utils/constant.js'
import http from '@/services/http/index.js'

export default {
  props: {
    id: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      classPrefix: 'ai-create-tmp__page',
      isPreview: false,
      categoryList: [],
      isLoading: false,
      showCategoryPicker: false,
      showScopeTypePicker: false,
      iconFold: `${$cdnPath}/images/ai-icon-fold.svg`,
      iconUnfold: `${$cdnPath}/images/ai-icon-unfold.svg`,
      iconRight: `${$cdnPath}/images/ai-icon-right.svg`,
      scopeTypeList: [
        { value: 'ALL', label: $t('marketing_taro.pkgs.qbkj_0d7942') },
        { value: 'SELF', label: $t('marketing_taro.pkgs.jzjkj_4d4d11') },
      ],
      promptDetail: {
        id: '',
        title: '',
        category: '',
        content: '',
        scope_type: '',
      },
    }
  },
  created() {
    this.fetchTemplateDetail()
    this.initCategoryList()
  },
  methods: {
    async initCategoryList() {
      const res = await http.queryObjFiledInfo({
        objectAPIName: 'PromptObj',
        objectFiledAPIName: 'category',
      })

      if (res && res.errCode === 0) {
        const { enumDetails = [] } = res.data
        this.categoryList = enumDetails.map(item => {
          const { itemCode, itemName } = item
          return {
            value: itemCode,
            label: itemName,
          }
        })
      }
    },
    fetchTemplateDetail() {
      if (!this.id) return

      http.queryObjDataById({
        objectAPIName: 'PromptObj',
        selectFields: [
          'name',
          'category',
          'title',
          'content',
          'status',
          'last_modified_time',
          'aihelper_id',
          'step_split',
          'created_by',
          'number_of_collections',
          'scope_type',
        ],
        id: this.id,
      }).then(res => {
        if (res && res.errCode === 0) {
          this.promptDetail = res.data

          if (this.promptDetail.title) {
            this.$emit('update:title', this.promptDetail.title)
          }
        }
      })
    },
    handlePreviewToggle() {
      this.isPreview = !this.isPreview
    },
    getSaveParams() {
      if (!this.promptDetail.content) {
        FxUI.Message.warning($t('marketing_taro.pkgs.qsrtsy_b91aba'))
        return null
      }

      if (!this.promptDetail.title) {
        FxUI.Message.warning($t('marketing_taro.pkgs.qsrtsybt_f38dc2'))
        return null
      }

      if (!this.promptDetail.category) {
        FxUI.Message.warning($t('marketing_taro.pkgs.qxzfl_8bb820'))
        return null
      }

      if (!this.promptDetail.scope_type) {
        FxUI.Message.warning($t('marketing_taro.pkgs.qxzkjfw_e48369'))
        return null
      }

      const params = {
        title: this.promptDetail.title,
        category: this.promptDetail.category,
        scopeType: this.promptDetail.scope_type,
        prompt: this.promptDetail.content,
      }

      if (this.id) {
        params.id = this.id
      }

      return params
    },
    async handleSave() {
      if (this.isLoading) return

      if (!this.promptDetail.content) {
        FxUI.Message.warning($t('marketing_taro.pkgs.qsrtsy_b91aba'))
        return
      }

      if (!this.promptDetail.title) {
        FxUI.Message.warning($t('marketing_taro.pkgs.qsrtsybt_f38dc2'))
        return
      }

      if (!this.promptDetail.category) {
        FxUI.Message.warning($t('marketing_taro.pkgs.qxzfl_8bb820'))
        return
      }

      if (!this.promptDetail.scope_type) {
        FxUI.Message.warning($t('marketing_taro.pkgs.qxzkjfw_e48369'))
        return
      }

      this.isLoading = true
      const params = {
        title: this.promptDetail.title,
        category: this.promptDetail.category,
        scopeType: this.promptDetail.scope_type,
        prompt: this.promptDetail.content,
      }

      if (this.id) {
        params.id = this.id
      }

      const api = this.id ? 'aiChatEditPrompt' : 'aiChatCreatePrompt'
      try {
        const res = await http[api](params)
        if (res.errCode === 0) {
          FxUI.Message.success($t('marketing_taro.commons.bccg_3b1083'))
          setTimeout(this.handleCancel, 2000)
        }
      } catch (error) {
        console.error(error)
      } finally {
        this.isLoading = false
      }
    },
  },
}
</script>

<style lang="less" scoped>
.ai-create-tmp__page {
  background-color: #F7F8FA;
  height: 100%;
  display: flex;
  flex-direction: column;

  &__content {
    background-color: #F7F8FA;
    box-sizing: border-box;
    height: 100%;
  }

  &__bottom {
    padding-bottom: 24px;
    display: flex;
    box-sizing: border-box;
    margin-top: 24px;
    justify-content: flex-end;
  }

  &__bottom--btn {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 32px;
    border-radius: 2px;
    font-weight: 400;
    font-size: 13px;
    cursor: pointer;
    padding: 0 15px;
    margin-right: 16px;
    flex: 1;

    &:last-of-type {
      margin-right: 0;
    }

    .icon {
      width: 16px;
      height: 16px;
      margin-right: 4px;
      font-size: 0;
    }

    // &.cancel {
    //   border-color: #DEE1E8;
    //   background: #FFF;
    //   color: #181C25;

    //   &:hover, &:active {
    //     border-color: #5b70ea;
    //     color: #5b70ea;
    //     background-color: #FFF;
    //   }
    // }
  }

  &__form {
    background: #F7F8FA;
    min-height: 100%;

    &.wide {
      display: flex;
      align-items: stretch;
      height: 100%;

      .ai-create-tmp__page__content--prompt-wrap {
        flex: 1;
        display: flex;
        flex-direction: column;

        .ai-create-tmp__page__content--prompt {
          flex: 1;
          display: flex;
          flex-direction: column;

          .ai-create-tmp__page__content--mustache-input-wrap {
            flex: 1;
          }
        }
      }

      .ai-create-tmp__page__content--mustaches {
        width: 375px;
        overflow-y: auto;

        &::-webkit-scrollbar {
          width: 6px;
        }
        &::-webkit-scrollbar-thumb {
          background: #C1C5CE;
          border-radius: 10px;

          &:hover {
            background: #737C8C;
          }
        }
      }
    }

    &.preview {
      display: flex;
      align-items: stretch;
      height: 100%;

      .ai-create-tmp__page__content--prompt-wrap {
        flex: 1;
        display: flex;
        flex-direction: column;

        .ai-create-tmp__page__content--prompt {
          flex: 1;
          display: flex;
          flex-direction: column;

          .ai-create-tmp__page__content--mustache-input-wrap {
            flex: 1;
          }
        }
      }

      .ai-create-tmp__page__content--mustaches {
        display: none;
      }
    }
  }

  &__content--mustaches {
    display: flex;
    flex-direction: column;
    padding: 0 16px;
    background-color: #F7F8FA;
  }

  &__content--mustaches-content {
    flex: 1;
  }

  &__content--mustaches-tips {
    font-size: 12px;
    color: #91959E;
    line-height: 18px;
    padding: 0 0 24px 0;
  }

  &__content--mustache {
    display: flex;
    flex-direction: column;
    margin-top: 20px;

    &.prePrompt {
      .ai-create-tmp__page__content--mustache-input-wrap {
        height: 150px;
        max-height: inherit;
        padding: 0 10px;

        .ai-create-tmp__page__content--mustache-input {
          height: 100%;
          box-sizing: border-box;
        }
      }
    }
  }

  &__content--mustache-label {
    font-size: 14px;
    line-height: 20px;
    color: #545861;

    .require {
      color: #FF5730;
      position: relative;
      top: 3px;
      margin-right: 2px;
    }

    .tips {
      color: #91959E;
    }

    &.title {
      line-height: 20px;
      font-size: 14px;
      color: #181C25;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-top: 20px;
    }
  }

  &__content--mustache-input-wrap {
    margin-top: 10px;
    box-sizing: border-box;
    max-height: 93px;
    border-radius: 4px;
    padding: 0 8px;
    border: 1px solid #C1C5CE;
    overflow-y: auto;
    background: #FFF;

    &::-webkit-scrollbar {
      width: 6px;
    }
    &::-webkit-scrollbar-thumb {
      background: #C1C5CE;
      border-radius: 10px;

      &:hover {
        background: #737C8C;
      }
    }
  }

  &__content--mustache-input {
    display: block;
    width: 100%;
    padding: 8px 0;
    font-size: 14px;
    line-height: 20px;

    &.picker {
      height: 40px;
      line-height: 40px;
      padding: 0 8px;
      position: relative;
      overflow: initial;

      &.placeholder {
        color: rgba(193, 197, 206, 1);
        background: transparent;
      }

      .icon {
        position: absolute;
        width: 16px;
        height: 16px;
        right: 5px;
        top: 12px;
        font-size: 0;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    /deep/ textarea {
      height: 100%;
      resize: none;
      font-size: 14px;
      line-height: 20px;
      border: none;
      padding: 0;

      &.auto-height {
        height: auto;
      }
    }

    &::-webkit-scrollbar, /deep/ textarea::-webkit-scrollbar {
      width: 6px;
    }
    &::-webkit-scrollbar-thumb, /deep/ textarea::-webkit-scrollbar-thumb {
      background: #C1C5CE;
      border-radius: 10px;

      &:hover {
        background: #737C8C;
      }
    }
  }

  &__content--prompt-wrap {
    background: #FFF;
    padding: 20px 16px;

    .ai-create-tmp__page__content--prompt .ai-create-tmp__page__content--mustache-input-wrap {
      padding: 0;

      .ai-create-tmp__page__content--mustache-input {
        padding: 8px;
      }
    }
  }

  &__content--prompt {
    border-radius: 3px;
    border: 0.5px solid #DEE1E8;

    .ai-create-tmp__page__content--mustache-label {
      height: 36px;
      line-height: 36px;
      background: #F2F3F5;
      padding: 0 10px;
      font-size: 14px;
      color: #181C25;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .icon {
        width: 16px;
        height: 17px;
        cursor: pointer;
        font-size: 0;
      }
    }

    .ai-create-tmp__page__content--mustache-input-wrap {
      height: 250px;
      border: none;
      max-height: inherit;
      padding: 0 10px;

      .ai-create-tmp__page__content--mustache-input {
        height: 100%;
        box-sizing: border-box;
      }
    }
  }

  &__content--scene {
    font-size: 14px;
    color: #0C6CFF;
    cursor: pointer;
    margin-top: 16px;
  }
}
</style>
