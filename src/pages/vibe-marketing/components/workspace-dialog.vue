<template>
  <fx-dialog
    :class="$style['workspace-dialog']"
    :visible="visible"
    :title="$t('marketing.pages.vibe_marketing.bjgzkj_fa8e67')"
    width="800px"
    append-to-body
    @close="handleCancel"
  >
    <div :class="$style['workspace-dialog__content']">
      <fx-form
        ref="form"
        size="small"
        label-position="left"
        label-width="100px"
        :model="workspaceDetail"
        :rules="rules"
      >
        <fx-form-item
          prop="name"
          label="名称"
        >
          <div :class="$style['form-item-content']">
            <fx-input
              v-model="workspaceDetail.name"
              :class="$style.input"
              :placeholder="$t('marketing.commons.qsrmc_06e2f8')"
              maxlength="100"
              :show-word-limit="true"
            />
          </div>
        </fx-form-item>
        <fx-form-item
          prop="icon__c"
          :label="$t('marketing_pd.components.setting.tb_5ef69f')"
        >
          <div :class="$style['form-item-content']">
            <fx-popover
              ref="iconPopover"
              v-model="popoverVisible"
              placement="bottom"
              width="264"
              trigger="manual"
              :popper-class="$style['workspace-dialog__icon-popover-wrapper']"
              style="display: inline-block;"
            >
              <div
                slot="reference"
                ref="reference"
                :class="$style['icon-popover']"
                @click="popoverVisible = true"
              >
                <div
                  v-if="workspaceDetail.icon__c"
                  :class="$style['icon-popover__image']"
                >
                  <img
                    :src="workspaceDetail.icon__c"
                    :alt="workspaceDetail.name"
                  >
                </div>
                <i
                  v-else
                  class="el-icon-plus"
                />
              </div>
              <div
                ref="popover"
                :class="$style['icon-popover__image-list']"
              >
                <div
                  v-for="item in defaultIconList"
                  :key="item"
                  :class="[$style['icon-popover__image-list__item'], { [$style.active]: activeIcon === item }]"
                  @click="handleIconClick(item)"
                >
                  <img
                    :src="item"
                    :alt="workspaceDetail.name"
                  >
                </div>
                <div
                  :class="[$style['icon-popover__image-list__item'], $style.upload, { [$style.active]: uploadFile && activeIcon === uploadFile.url }]"
                  @click="handleIconClick(uploadFile && uploadFile.url)"
                >
                  <div
                    v-if="!uploadFile"
                    :class="$style['icon-popover__upload-icon']"
                    @click.stop="handleShowCutterDialog"
                  >
                    <i class="el-icon-plus" />
                  </div>
                  <template v-else>
                    <img :src="uploadFile.url">
                    <div :class="$style['icon-popover__upload-replace']">
                      <span @click.stop="handleShowCutterDialog">{{ $t('marketing_pd.commons.th_e22c9a') }}</span>
                    </div>
                  </template>
                </div>
              </div>
              <div :class="$style['icon-popover__options']">
                <fx-button
                  size="mini"
                  type="primary"
                  :class="$style['icon-popover__options--btn']"
                  @click="handleIconSubmit"
                >
                  {{ $t('marketing.commons.qr_e83a25') }}
                </fx-button>
                <fx-button
                  size="mini"
                  type="default"
                  plain
                  :class="$style['icon-popover__options--btn']"
                  @click="handleIconCancel"
                >
                  {{ $t('marketing.commons.qx_625fb2') }}
                </fx-button>
              </div>
            </fx-popover>
            <div :class="$style['form-item-tips']">
              {{ $t('marketing.pages.vibe_marketing.jycc_50f474')}}120*120
            </div>
          </div>
        </fx-form-item>
        <fx-form-item
          prop="summary__c"
          :label="$t('marketing.commons.ms_3bdd08')"
        >
          <div :class="$style['form-item-content']">
            <fx-input
              v-model="workspaceDetail.summary__c"
              type="textarea"
              :class="$style.input"
              :placeholder="$t('marketing.commons.qsrms_11956a')"
              maxlength="2000"
              :show-word-limit="true"
              :autosize="{ minRows: 3, maxRows: 6 }"
            />
          </div>
        </fx-form-item>
        <fx-form-item
          prop="marketing_event_id__c"
          :label="$t('marketing.store.schd_3634ed')"
        >
          <div :class="$style['form-item-content']">
            <PickSelfobject
              :value="pickObject"
              :disabled="!!originWorkspaceDetail.marketing_event_id__c"
              @input="handleCampaignChange"
            />
          </div>
        </fx-form-item>
      </fx-form>
    </div>
    <div
      slot="footer"
      :class="$style['workspace-dialog__footer']"
    >
      <fx-button
        size="small"
        type="primary"
        :class="$style['workspace-dialog__footer--btn']"
        @click="handleSubmit"
      >
        {{ $t('marketing.commons.qr_e83a25') }}
      </fx-button>
      <fx-button
        size="small"
        type="default"
        plain
        :class="$style['workspace-dialog__footer--btn']"
        @click="handleCancel"
      >
        {{ $t('marketing.commons.qx_625fb2') }}
      </fx-button>
    </div>

    <PictureSelector
      :visible.sync="isShowCutterDialog"
      :cut-size="cutSize"
      :need-apath="true"
      output-path-type="a"
      @submit="handlePictureSelectorSubmit"
    />
  </fx-dialog>
</template>

<script>
import { $cdnPath } from '@/utils/constant.js'
import http from '@/services/http/index.js'
import PictureSelector from '@/components/PictureSelector/index.vue'
import PickSelfobject from '@/pages/promotion-activity/common/pick-selfobject.vue'

export default {
  components: {
    PictureSelector,
    PickSelfobject,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    originWorkspaceDetail: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      defaultIconList: [
        `${$cdnPath}/images/workspace-icons/1.png`,
        `${$cdnPath}/images/workspace-icons/2.png`,
        `${$cdnPath}/images/workspace-icons/3.png`,
        `${$cdnPath}/images/workspace-icons/4.png`,
        `${$cdnPath}/images/workspace-icons/5.png`,
        `${$cdnPath}/images/workspace-icons/6.png`,
        `${$cdnPath}/images/workspace-icons/7-1.png`,
        `${$cdnPath}/images/workspace-icons/8.png`,
        `${$cdnPath}/images/workspace-icons/9.png`,
        `${$cdnPath}/images/workspace-icons/10.png`,
        `${$cdnPath}/images/workspace-icons/11.png`,
        `${$cdnPath}/images/workspace-icons/12.png`,
        `${$cdnPath}/images/workspace-icons/13.png`,
        `${$cdnPath}/images/workspace-icons/14.png`,
        `${$cdnPath}/images/workspace-icons/15.png`,
      ],
      workspaceDetail: {
        name: '',
        icon__c: '',
        summary__c: '',
        marketing_event_id__c: '',
        ...this.originWorkspaceDetail,
      },
      isShowCutterDialog: false,
      cutSize: {
        width: 240,
        height: 240,
      },
      uploadFile: null,
      popoverVisible: false,
      activeIcon: '',
      rules: {
        name: [
          { required: true, message: $t('marketing.commons.qsrmc_06e2f8'), trigger: 'blur' },
        ],
        summary__c: [
          { required: true, message: $t('marketing.commons.qsrms_11956a'), trigger: 'blur' },
        ],
        icon__c: [
          {
            required: true,
            trigger: 'change',
            validator: (_, value, callback) => {
              if (!this.workspaceDetail.icon__c) {
                callback(new Error($t('marketing_pd.components.common.qxztb_cb8e0b')))
              } else {
                callback()
              }
            },
          },
        ],
      },
    }
  },
  computed: {
    pickObject() {
      return {
        id: this.workspaceDetail.marketing_event_id__c || '',
      }
    },
  },
  watch: {
    originWorkspaceDetail() {
      this.workspaceDetail = {
        ...this.originWorkspaceDetail,
      }
    },
    isShowCutterDialog() {
      // 这里和handlePopoverVisible方法中的逻辑冲突了，所以需要延迟执行
      setTimeout(() => {
        this.popoverVisible = !this.isShowCutterDialog
      }, 0)
    },
  },
  mounted() {
    document.addEventListener('click', this.handlePopoverVisible)
  },
  beforeDestroy() {
    document.removeEventListener('click', this.handlePopoverVisible)
  },
  methods: {
    handlePopoverVisible(e) {
      if (!this.popoverVisible || this.isShowCutterDialog) {
        return
      }

      if (this.$refs.reference && this.$refs.reference.contains(e.target)) {
        return
      }
      if (this.$refs.popover && this.$refs.popover.contains(e.target)) {
        return
      }

      this.popoverVisible = false
    },
    handleCancel() {
      this.$emit('cancel')
    },
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          const payload = {
            object_data: {
              ...this.workspaceDetail,
            },
          }
          http.editVMWorkspaceObj(payload).then(res => {
            if (res.Result.StatusCode === 0) {
              this.$emit('submit', this.workspaceDetail)
              this.handleCancel()
            }
          })
        }
      })
    },
    handlePictureSelectorSubmit(data) {
      this.uploadFile = data
      this.activeIcon = data.url
    },
    handleIconSubmit() {
      if (!this.activeIcon) {
        FxUI.Message.warning($t('marketing_pd.components.common.qxztb_cb8e0b'))
        return
      }

      this.workspaceDetail.icon__c = this.activeIcon
      this.popoverVisible = false
    },
    handleIconCancel() {
      this.popoverVisible = false
    },
    handleIconClick(url) {
      this.activeIcon = url
    },
    handleShowCutterDialog() {
      this.isShowCutterDialog = true
    },
    handleCampaignChange(data) {
      this.workspaceDetail.marketing_event_id__c = data._id
    },
  },
}
</script>

<style lang="less" module>
.workspace-dialog {

  .workspace-dialog__content {
    .form-item-content {
      .icon-popover {
        width: 68px;
        height: 68px;
        box-sizing: border-box;
        border: 1px dashed #DEE1E8;
        background: #FAFAFA;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        border-radius: 14px;
        overflow: hidden;

        .icon-popover__image {
          width: 100%;
          height: 100%;

          img {
            width: 100%;
            height: 100%;
          }
        }
      }

      .form-item-tips {
        font-size: 12px;
        line-height: 18px;
        color: #91959E;
      }
    }
  }
}

.workspace-dialog__icon-popover-wrapper {
  .icon-popover__image-list {
    display: flex;
    flex-wrap: wrap;

    .icon-popover__image-list__item {
      width: 40px;
      height: 40px;
      margin-right: 16px;
      margin-bottom: 16px;
      border-radius: 8px;
      overflow: hidden;
      cursor: pointer;
      box-sizing: border-box;
      border: 1px solid transparent;

      &.active {
        border: 1px solid #3879ed;
      }

      &:hover {
        box-shadow: 0px 6px 24px 0px #00000026;
      }

      &:nth-child(5n) {
        margin-right: 0;
      }

      img {
        width: 100%;
        height: 100%;
      }

      &.upload {
        border: 1px solid #DEE1E8;
        font-size: 16px;
        color: #0C6CFF;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;

        &:hover {
          .icon-popover__upload-replace {
            display: flex;
          }
        }

        &.active {
          border: 1px solid #3879ed;
        }
      }

      .icon-popover__upload-replace {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        display: none;

        span {
          cursor: pointer;

          &:hover {
            color: #0C6CFF;
          }
        }
      }
    }
  }

  .icon-popover__options {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }
}
</style>
