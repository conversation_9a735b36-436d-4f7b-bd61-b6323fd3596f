<template>
  <div :class="$style['base-share-gpt']">
    <MarketingAIHelperIframeBox
      ref="ShareGPT"
      :class-name="$style.shareGPT"
      :enterprise-account="enterpriseAccount"
      service-name="mkt"
      :default-helper-name="defaultHelperName"
      :is-use-new-agent-api="true"
    />
  </div>
</template>

<script>
import { MarketingAIHelperIframeBox } from 'ai-helper-web'
import 'ai-helper-web/dist/style.css'

export default {
  components: {
    MarketingAIHelperIframeBox,
  },
  props: {
    defaultHelperName: {
      type: String,
      default: '',
    },
    prompt: {
      type: String,
      default: '',
    },
  },
  data() {
    const { enterpriseAccount } = FS.contacts.getCurrentEmployee() || {}
    return {
      enterpriseAccount,
    }
  },
  mounted() {
    this.initShareGPT()
  },
  beforeDestroy() {
    if (this.marktingAIHelper) {
      this.marktingAIHelper.destroy()
    }
  },
  methods: {
    initShareGPT() {
      const marktingAIHelper = this.$refs.ShareGPT.getInstance()
      marktingAIHelper.onReady.then(() => {
        marktingAIHelper.setMenuOptions([], true, ['template', 'history', 'newRecord', 'aiHelperName'])
        this.$emit('onReady', marktingAIHelper)
      })

      marktingAIHelper.eventBus.on('onAgentMessage', data => {
        this.$emit('onAgentMessage', data)
      })

      marktingAIHelper.eventBus.on('onInitMessageLoaded', event => {
        if (this.prompt) {
          setTimeout(() => {
            const message = {
              msg: this.prompt,
            }
            marktingAIHelper.sendMessageToAI(message, res => {
              const { errMsg, errCode } = res
              if (errCode === 0 && errMsg === 'receivePeriod:resolve') {
                this.$emit('onAgentMessage', res.message)
              }
            })
          }, 1000)
        }
      })

      this.marktingAIHelper = marktingAIHelper
    },
  },
}
</script>

<style lang="less" module>
.base-share-gpt {
  width: 375px;
  height: 100%;

  .shareGPT {
    height: 100%;
  }
}
</style>
