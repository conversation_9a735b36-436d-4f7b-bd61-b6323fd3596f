<template>
  <div
    :class="$style.sharegpt_selector"
    @click="handleShowObjectDialog"
  >
    <fx-input
      :readonly="true"
      :size="size"
      :placeholder="placeholder"
      :collapse-tags="false"
      :value="selectedList"
    >
      <i
        slot="suffix"
        class="el-input__icon fx-icon-arrow-down"
        style="font-size: 12px;"
      />
    </fx-input>
  </div>
</template>

<script>
import { requireAsync } from '@/utils/index.js'

export default {
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    size: {
      type: String,
      default: 'small',
    },
    placeholder: {
      type: String,
      default: $t('marketing.commons.qxz_708c9d'),
    },
    apiname: {
      type: String,
      default: '',
    },
    isMultiple: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    selectedList() {
      return (this.value || []).map(item => item.name)
    },
  },
  methods: {
    handleShowObjectDialog() {
      requireAsync('crm-modules/components/pickselfobject/pickselfobject', PickObject => {
        const picker = new PickObject()
        picker.on('select', itemList => {
          console.log('itemList', itemList)
          if (this.isMultiple) {
            const agentList = itemList.map(item => {
              const { _id, name } = item
              return { _id, name }
            })
            this.$emit('input', agentList)
          } else {
            const agentList = [itemList].map(item => {
              const { _id, name } = item
              return { _id, name }
            })
            this.$emit('input', agentList)
          }

          picker.destroy()
        })
        picker.render({
          layout_type: 'list',
          include_layout: true,
          apiname: this.apiname,
          dataId: this.value,
          isMultiple: this.isMultiple,
        })
      })
    },
  },
}
</script>

<style lang="scss" module>
.sharegpt_selector {
  width: auto;
}
</style>
