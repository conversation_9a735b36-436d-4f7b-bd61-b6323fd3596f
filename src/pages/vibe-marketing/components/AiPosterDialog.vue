<template>
  <div class="ai-poster-dialog">
    <fx-dialog
      width="1000px"
      :visible="visible"
      :title="title"
      :before-close="handleClose"
      :close-on-click-modal="false"
      :destroy-on-close="false"
    >
      <div class="poster-dialog">
        <!-- 步骤指示器 -->
        <div class="steps-indicator" :class="{ 'is-loading': loading }">
          <div
            class="step-item"
            :class="{
              active: currentStep === 1,
              completed: currentStep === 2,
              disabled: loading
            }"
            @click="!loading && (currentStep = 1)"
          >
            <span class="step-number">1</span>
            <span class="step-text">编辑提示词</span>
          </div>
          <div class="step-line"></div>
          <div
            class="step-item"
            :class="{
              active: currentStep === 2,
              disabled: loading
            }"
            @click="!loading && (currentStep = 2)"
          >
            <span class="step-number">2</span>
            <span class="step-text">预览图片</span>
          </div>
        </div>

        <div class="poster-dialog__content">
          <!-- 第一步：编辑提示词和表单 -->
          <div v-show="currentStep === 1" class="dialog-content-vertical">
            <div v-if="loading" class="loading-overlay">
              <div class="loading-animation">
                <div class="loading-dots">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
                <div class="loading-icon">
                  <fx-icon name="magic" size="48" />
                </div>
              </div>
              <p class="loading-text">AI正在创作中，请稍候...</p>
              <p class="loading-tip">{{ currentLoadingTip }}</p>
            </div>
            <div class="editor-row">
              <apply-tmp-form
                :prompt="templatePrompt"
                :pre-chat-result="emptyPreChatResult"
                style="flex: 1; min-width: 0;"
                :class="`${classPrefix}__form`"
                @onChange="handlePromptChange"
              />
            </div>
            <div class="form-controls-inline">
              <fx-form :model="formData" label-width="80px" size="mini" inline>
                <fx-form-item label="图片尺寸" required>
                  <fx-select
                    v-model="formData.size"
                    placeholder="请选择图片尺寸"
                    size="mini"
                    :options="sizeOptions"
                    :disabled="loading"
                  />
                </fx-form-item>
                <fx-form-item label="图片质量" required>
                  <fx-select
                    v-model="formData.quality"
                    placeholder="请选择图片质量"
                    size="mini"
                    :options="qualityOptions"
                    :disabled="loading"
                  />
                </fx-form-item>
                <fx-form-item label="生成数量" required>
                  <fx-select
                    v-model="formData.n"
                    placeholder="请选择生成数量"
                    size="mini"
                    :options="countOptions"
                    :disabled="loading"
                  />
                </fx-form-item>
                <div class="reference-image-upload-inline">
                  <template v-if="referenceImage">
                    <img :src="referenceImage" alt="参考图" class="reference-thumb" />
                    <div class="reference-actions">
                      <fx-button
                        type="text"
                        size="mini"
                        :disabled="loading"
                        @click="triggerFileUpload"
                      >更换</fx-button>
                      <fx-button
                        type="text"
                        size="mini"
                        :disabled="loading"
                        @click="removeReferenceImage"
                      >清除</fx-button>
                    </div>
                  </template>
                  <template v-else>
                    <div class="upload-placeholder" @click="triggerFileUpload">
                      选择参考图
                    </div>
                  </template>
                  <!-- <input
                    ref="fileInput"
                    type="file"
                    accept="image/*"
                    style="display: none"
                    @change="handleFileSelect"
                  /> -->
                </div>
              </fx-form>
            </div>
          </div>

          <!-- 第二步：图片预览 -->
          <div v-show="currentStep === 2" class="preview-section" :class="previewSectionClass">
            <div v-if="loading" class="preview-loading-overlay">
              <div class="loading-animation">
                <div class="loading-dots">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
                <div class="loading-icon">
                  <fx-icon name="magic" size="48" />
                </div>
              </div>
              <p class="loading-text">AI正在创作中，请稍候...</p>
              <p class="loading-tip">{{ currentLoadingTip }}</p>
            </div>
            <div v-else-if="generatedImages.length" class="preview-grid" :style="gridColumnsStyle">
              <div
                v-for="(image, index) in generatedImages"
                :key="index"
                class="preview-item"
                :class="{ 'preview-item--selected': selectedImageIndex === index }"
                @click="handleSelectImage(index)"
              >
                <div class="preview-item__wrapper">
                  <img
                    :src="image"
                    class="preview-image"
                    :alt="`生成的海报 ${index + 1}`"
                  />
                  <div class="preview-item__actions">
                    <fx-button
                      type="text"
                      size="small"
                      @click.stop="handlePreviewImage(index)"
                    >
                      <span class="fx-icon-show"></span>
                    </fx-button>
                    <fx-button
                      type="text"
                      size="small"
                      @click.stop="handleDownloadImage(image, index)"
                    >
                    <span class="fx-icon-download"></span>
                    </fx-button>
                    <fx-button
                      type="text"
                      size="small"
                      @click.stop="setReferenceImageAndBack(image)"
                    >
                      设为参考图
                    </fx-button>
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="preview-placeholder">
              <fx-icon name="image" size="48" />
              <p>暂无生成的图片</p>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <fx-button @click="handleClose">取消</fx-button>
          <template v-if="currentStep === 1">
            <fx-button
              type="primary"
              :disabled="!canSubmit || loading"
              :loading="loading"
              @click="handleGenerate"
            >
              {{ loading ? '生成中...' : '开始生成' }}
            </fx-button>
          </template>
          <template v-else>
            <fx-button @click="currentStep = 1">上一步</fx-button>
            <fx-button
              v-if="selectedImageIndex !== -1"
              type="primary"
              @click="handleConfirm('poster')"
            >
              创建海报
            </fx-button>
            <fx-button
              type="primary"
              :disabled="selectedImageIndex === -1"
              @click="handleConfirm"
            >
              确定
            </fx-button>
          </template>
        </div>
      </template>
    </fx-dialog>
    <ImageViewer
      v-if="isShowImageViewer"
      :on-close="handleViewerClose"
      :url-list="previewImageUrls"
      :initial-index="previewIndex"
      :z-index="9999"
    />
    <PictureSelector
      :visible.sync="isShowCutterDialog"
      @submit="handlePictureSelectorSubmit"
      :cutSize="{}"
    />
  </div>
</template>

<script>
import { getTNPath } from '@/utils/files'
import { vibeMarketingWorkSpaceIconMap } from '@/utils/constant.js'
import http from '@/services/http/index.js'
import { generateImage } from '../utils/generateImage'
import ApplyTmpForm from './apply-tmp-form.vue'
import PictureSelector from '@/components/PictureSelector';  

export default {
  name: 'AiPosterDialog',
  components: {
    ImageViewer: FxUI.Image.extends.components.ImageViewer,
    ApplyTmpForm,
    PictureSelector
  },
  data() {
    return {
      templatePrompt: '',
      currentStep: 1,
      loading: false,
      visible: false,
      title: 'AI海报生成',
      selectedImageIndex: -1,
      currentTaskId: null,
      taskStates: new Map(),
      formData: {
        prompt: '',
        size: '1024x1536',
        quality: 'medium',
        n: 1,
        image: ''
      },
      generatedImages: [],
      sizeOptions: [
        { label: '1:1 (1024x1024)', value: '1024x1024' },
        { label: '2:3 (1024x1536)', value: '1024x1536' },
        { label: '3:2 (1536x1024)', value: '1536x1024' },
        { label: '自动', value: 'auto' }
      ],
      qualityOptions: [
        { label: '自动', value: 'auto' },
        { label: '高质量', value: 'high' },
        { label: '中等质量', value: 'medium' },
        { label: '低质量', value: 'low' }
      ],
      countOptions: [
        { label: '1张', value: 1 },
        { label: '2张', value: 2 },
        { label: '3张', value: 3 },
        { label: '4张', value: 4 }
      ],
      loadingTips: [
        'AI正在思考如何让海报更有创意...',
        '正在调整构图和色彩搭配...',
        '正在优化细节，让海报更专业...',
        '正在添加品牌元素...',
        '正在生成高清图片...'
      ],
      currentLoadingTip: '',
      tipInterval: null,
      resolvePromise: null,
      rejectPromise: null,
      dialogPromise: null,
      xhr: null,
      referenceImage: '',
      isShowImageViewer: false,
      previewImageUrls: [],
      previewIndex: 0,
      pendingReferenceFile: null,
      isShowCutterDialog: false,
      emptyPreChatResult: {}, // 稳定的空对象引用
    }
  },
  computed: {
    canSubmit() {
      return this.formData.prompt && this.formData.size && this.formData.quality && !this.loading
    },
    gridColumnsStyle() {
      const count = this.generatedImages.length || 1
      return {
        display: 'grid',
        'grid-template-columns': `repeat(${count}, 1fr)`
      }
    },
    previewSectionClass() {
      return {
        'preview-section': true,
        'is-loading': this.loading
      }
    }
  },
  methods: {
    show(objectData) {
      const taskId = objectData?._id || 'default'
      this.title = objectData?.title || 'AI海报生成'
      
      // 只在首次打开时设置 templatePrompt
      if (!this.taskStates.has(taskId)) {
        this.templatePrompt = objectData?.content || ''
      }

      if (!this.taskStates.has(taskId)) {
        this.taskStates.set(taskId, {
          formData: {
            prompt: '',
            size: '1024x1536',
            quality: 'medium',
            n: 1,
            image: ''
          },
          generatedImages: [],
          selectedImageIndex: -1,
          currentStep: 1,
          referenceImage: '',
          pendingReferenceFile: null,
          loading: false,
          xhr: null,
          loadingTips: null,
        })
      }

      this.currentTaskId = taskId
      const taskState = this.taskStates.get(taskId)

      this.formData = { ...taskState.formData }
      if (!this.formData.prompt && this.templatePrompt) {
        this.formData.prompt = this.templatePrompt
      }
      this.generatedImages = [...taskState.generatedImages]
      this.selectedImageIndex = taskState.selectedImageIndex
      this.currentStep = taskState.currentStep
      this.referenceImage = taskState.referenceImage
      this.pendingReferenceFile = taskState.pendingReferenceFile
      this.loading = taskState.loading
      this.xhr = taskState.xhr
      this.tipInterval = taskState.loadingTips

      if (this.loading && !this.tipInterval) {
        this.startLoadingTips()
      }

      this.visible = true

      // 返回 Promise
      return new Promise((resolve, reject) => {
        this.resolvePromise = resolve
        this.rejectPromise = reject
      })
    },

    saveCurrentTaskState() {
      if (!this.currentTaskId) return

      this.taskStates.set(this.currentTaskId, {
        formData: { ...this.formData },
        generatedImages: [...this.generatedImages],
        selectedImageIndex: this.selectedImageIndex,
        currentStep: this.currentStep,
        referenceImage: this.referenceImage,
        pendingReferenceFile: this.pendingReferenceFile,
        loading: this.loading,
        xhr: this.xhr,
        loadingTips: this.tipInterval,
      })
    },

    handleClose() {
      this.visible = false
      this.saveCurrentTaskState()
      this.stopLoadingTips()
      
      // 如果 Promise 还未解决，则 reject
      if (this.rejectPromise) {
        this.rejectPromise(new Error('用户取消操作'))
        this.resolvePromise = null
        this.rejectPromise = null
      }
    },

    async handleGenerate() {
      if (this.loading) {
        this.$message.info('海报正在生成中，请稍候...')
        return
      }

      try {
        this.loading = true
        this.saveCurrentTaskState()

        const prevLength = this.generatedImages.length
        if (prevLength > 0) {
          this.generatedImages = []
          this.saveCurrentTaskState()
        }

        const xhr = new AbortController()
        this.xhr = xhr
        this.saveCurrentTaskState()
        this.startLoadingTips()

        if (this.pendingReferenceFile) {
          const res = await getTNPath([this.pendingReferenceFile])
          if (res && res.length > 0) {
            this.formData.image = res[0].path
          } else {
            throw new Error('获取图片路径失败')
          }
          this.pendingReferenceFile = null
        }

        const images = await generateImage(this.formData, { signal: xhr.signal })
        this.generatedImages = images
        this.saveCurrentTaskState()

        if (this.generatedImages.length) {
          this.$nextTick(() => {
            this.currentStep = 2
            this.saveCurrentTaskState()
          })
        }
      } catch (error) {
        console.error('生成失败', error)
        const taskState = this.taskStates.get(this.currentTaskId)
        if (taskState) {
          taskState.loading = false
          taskState.xhr = null
          if (taskState.loadingTips) {
            clearInterval(taskState.loadingTips)
            taskState.loadingTips = null
          }
          this.taskStates.set(this.currentTaskId, taskState)
        }

        this.loading = false
        this.xhr = null
        this.tipInterval = null

        if (error.name === 'AbortError') {
          this.$message.error('请求被中断，请重试')
        } else {
          this.$message.error(error.message || '生成海报失败，请重试')
        }
      } finally {
        const taskState = this.taskStates.get(this.currentTaskId)
        if (taskState) {
          taskState.loading = false
          taskState.xhr = null
          if (taskState.loadingTips) {
            clearInterval(taskState.loadingTips)
            taskState.loadingTips = null
          }
          this.taskStates.set(this.currentTaskId, taskState)
        }

        this.loading = false
        this.xhr = null
        this.tipInterval = null
      }
    },
    setReferenceImageAndBack(image) {
      this.setReferenceImage(image)
      this.currentStep = 1
      this.saveCurrentTaskState()
    },
    handleSelectImage(index) {
      this.selectedImageIndex = index
      this.saveCurrentTaskState()
    },
    getWorkSpaceId() {
      const employee = window.Fx.contacts.getCurrentEmployee()
      const employeeID = (employee && employee.employeeID) || ''
      const departmentIds = (employee && employee.departmentIds) || []

      const payload = {
        object_data: {
          object_describe_api_name: 'VMWorkspaceObj__c',
          record_type: 'default__c',
          icon__c: vibeMarketingWorkSpaceIconMap.content,
          summary__c: this.formData.prompt,
          works_space_type__c: 'content',
          name: `${this.title}_${Date.now()}`,
          owner: employeeID ? [`${employeeID}`] : [],
          created_by: employeeID ? [`${employeeID}`] : [],
          data_own_department: departmentIds.map(el => `${el}`),
        },
      }

      return new Promise((resolve, reject) => {
        http.addVMWorkspaceObj(payload).then(res => {
            if (res.Result.StatusCode === 0) {
              const {
                objectData: {
                  _id,
                } = {},
              } = res.Value

              resolve(_id)
            }
            resolve('')
          })
      })
    },
    handleConfirm(target) {
      if (this.selectedImageIndex !== -1) {
        const selectedImage = this.generatedImages[this.selectedImageIndex]
        this.$emit('select', selectedImage)
        this.saveCurrentTaskState()

        // 返回选中的图片数据
        const result = {
          image: selectedImage,
          index: this.selectedImageIndex,
          allImages: [...this.generatedImages],
          formData: { ...this.formData }
        }

        if (target === 'poster') {
          this.getWorkSpaceId().then(workspaceId => {
            this.$router.push({
              name: 'vibe-marketing-agent',
              query: {
                name: 'poster',
                workspaceId,
              },
              params: {
                image: selectedImage
              }
            })
          })
        } else {
          // this.handleDownloadImage(selectedImage, this.selectedImageIndex)
        }

        this.visible = false
        
        // 如果 Promise 还未解决，则 resolve
        if (this.resolvePromise) {
          this.resolvePromise(result)
          this.resolvePromise = null
          this.rejectPromise = null
        }
      }
    },
    handlePreviewImage(index) {
      this.previewIndex = index
      this.previewImageUrls = this.generatedImages
      this.isShowImageViewer = true
    },
    handleViewerClose() {
      this.isShowImageViewer = false
    },
    handleDownloadImage(image, index) {
      const link = document.createElement('a')
      link.href = image
      link.download = `AI海报_${index + 1}.png`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    },
    triggerFileUpload() {
      this.isShowCutterDialog = true
      // this.$refs.fileInput.click()
    },
    async handleFileSelect(event) {
      const file = event.target.files[0]
      if (!file) return

      event.target.value = ''

      this.pendingReferenceFile = file
      this.formData.image = ''

      const reader = new FileReader()
      reader.onload = (e) => {
        this.referenceImage = e.target.result
      }
      reader.readAsDataURL(file)
    },
    removeReferenceImage() {
      this.referenceImage = ''
      this.formData.image = ''
      this.pendingReferenceFile = null
      this.saveCurrentTaskState()
    },
    setReferenceImage(image) {
      this.base64ToFile(image).then(file => {
        this.pendingReferenceFile = file
        this.formData.image = ''
        this.referenceImage = image
      }).catch(error => {
        console.error('设置参考图失败:', error)
        this.$message.error('设置参考图失败，请重试')
        this.removeReferenceImage()
      })
    },
    async base64ToFile(base64Url, filename = 'image.png') {
      try {
        const base64Data = base64Url.split(',')[1]
        const byteString = atob(base64Data)
        const ab = new ArrayBuffer(byteString.length)
        const ia = new Uint8Array(ab)
        for (let i = 0; i < byteString.length; i++) {
          ia[i] = byteString.charCodeAt(i)
        }
        const blob = new Blob([ab], { type: 'image/png' })
        return new File([blob], filename, { type: 'image/png' })
      } catch (error) {
        console.error('转换图片格式失败:', error)
        throw error
      }
    },
    parseData(data) {
      try {
        return JSON.parse(JSON.parse(data))
      } catch (error) {
        return data
      }
    },
    startLoadingTips() {
      const taskState = this.taskStates.get(this.currentTaskId)
      if (taskState) {
        taskState.loadingTips = this.tipInterval
      }

      this.currentLoadingTip = this.loadingTips[0]
      let index = 0
      this.tipInterval = setInterval(() => {
        index = (index + 1) % this.loadingTips.length
        this.currentLoadingTip = this.loadingTips[index]
      }, 3000)
    },
    stopLoadingTips() {
      if (this.tipInterval) {
        clearInterval(this.tipInterval)
        this.tipInterval = null
      }
    },
    handleStopGenerate() {
      const taskState = this.taskStates.get(this.currentTaskId)
      if (taskState && taskState.xhr) {
        taskState.xhr.abort()
        taskState.xhr = null
        taskState.loading = false
        if (taskState.loadingTips) {
          clearInterval(taskState.loadingTips)
          taskState.loadingTips = null
        }
        this.taskStates.set(this.currentTaskId, taskState)
      }

      if (this.currentTaskId) {
        const currentState = this.taskStates.get(this.currentTaskId)
        this.loading = currentState.loading
        this.xhr = currentState.xhr
        this.tipInterval = currentState.loadingTips
      }
    },
    handlePromptChange(e) {
      const { promptText, isValidate, objects } = e
      
      if (promptText) {
        this.formData.prompt = promptText
        if (objects && objects.length) {
          // 如果有对象选择，可以在这里处理
          console.log('Selected objects:', objects)
        }
      }
    },
    handlePictureSelectorSubmit({url, photoPath}) {
      this.referenceImage = url;
      this.pendingReferenceFile = null;
      this.formData.image = photoPath;
    }
  },
  watch: {
    currentStep(newStep) {
      if (newStep === 2 && this.generatedImages.length) {
        this.$nextTick(() => {
        })
      }
    }
  },
  beforeDestroy() {
    this.taskStates.forEach(taskState => {
      if (taskState.xhr) {
        taskState.xhr.abort()
      }
      if (taskState.loadingTips) {
        clearInterval(taskState.loadingTips)
      }
    })
    this.taskStates.clear()
    this.stopLoadingTips()
  }
}
</script>

<style lang="less" scoped>
.ai-poster-dialog {
  // 保持现有样式
}

.steps-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
  padding: 0 24px;
  position: relative;

  &.is-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.6);
    z-index: 1;
  }

  .step-item {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 8px 16px;
    border-radius: 16px;
    transition: all 0.3s;
    position: relative;
    z-index: 2;

    &.disabled {
      cursor: not-allowed;
      opacity: 0.6;
    }

    &.active {
      background: #ecf5ff;
      color: #409eff;

      &.disabled {
        background: #f5f7fa;
        color: #909399;
      }
    }

    &.completed {
      color: #67c23a;

      &.disabled {
        color: #909399;
      }
    }

    .step-number {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background: #f5f7fa;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 8px;
      font-weight: bold;
    }

    &.active .step-number {
      background: #409eff;
      color: #fff;

      &.disabled {
        background: #909399;
      }
    }

    &.completed .step-number {
      background: #67c23a;
      color: #fff;

      &.disabled {
        background: #909399;
      }
    }
  }

  .step-line {
    width: 60px;
    height: 2px;
    background: #dcdfe6;
    margin: 0 8px;
    position: relative;
    z-index: 2;
  }
}

.poster-dialog {
  &__content {
    height: 500px;
    box-sizing: border-box;
  }
}

.dialog-content-vertical {
  display: flex;
  flex-direction: column;
  height: 500px;
  min-height: 0;
  position: relative;
}
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255,255,255,0.85);
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  pointer-events: all;
}
.editor-row {
  flex: 1;
  display: flex;
  align-items: flex-start;
  min-height: 0;
  gap: 0;
  ::v-deep .ai-apply-tmp__form__content--prompt-wrap {
    padding: 0 16px;
  }
}
.prompt-preview {
  flex: 1;
  min-width: 0;
  background: #FFF;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
  .prompt-preview__header {
    padding: 12px 16px;
    background: #F7F8FA;
    .title {
      font-size: 14px;
      font-weight: 500;
      color: #181C25;
    }
  }
  .prompt-preview__content {
    flex: 1;
    padding: 16px;
    .preview-textarea {
      height: 100%;
      :deep(.fx-input__textarea) {
        height: 100%;
        background: #F7F8FA;
        border: none;
        resize: none;
        font-size: 14px;
        line-height: 1.6;
        color: #181C25;
      }
    }
  }
}
.template-settings {
  width: 320px;
  background: #FFF;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
  padding: 0 0 0 0;
}
.form-controls-inline {
  margin-top: auto;
  display: flex;
  align-items: center;
  gap: 16px;
  background: #FFF;
  border-radius: 4px;
  padding: 12px 16px 0;
  .fx-form {
    display: flex;
    align-items: center;
    gap: 16px;
    width: 100%;
  }
  .fx-form-item {
    display: flex;
    align-items: center;
    margin-bottom: 0 !important;
    min-width: 160px;
    ::v-deep .el-form-item__label {
      width: 80px;
      white-space: nowrap;
      display: flex;
      align-items: center;
      font-size: 14px;
      margin-right: 4px;
      padding: 0;
      line-height: 14px;
    }
    .el-form-item__content {
      display: flex;
      align-items: center;
    }
    .el-form-item__required {
      color: #ff4d4f;
      margin-right: 2px;
      font-size: 14px;
      padding: 0;
    }
    .fx-select {
      height: 32px;
      line-height: 32px;
    }
  }
  .reference-image-upload-inline {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    min-width: 120px;
    position: relative;
    width: 80px;
    height: 80px;

    .reference-thumb {
      width: 80px;
      height: 80px;
      object-fit: cover;
      border-radius: 4px;
      border: 1px solid #e1e1e1;
      background: #fff;
      display: block;
    }

    .reference-actions {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 4px;
      background: rgba(0, 0, 0, 0.5);
      border-radius: 4px;
      opacity: 0;
      transition: opacity 0.3s;
      pointer-events: none;
    }

    &:hover .reference-actions {
      opacity: 1;
      pointer-events: auto;
    }

    .fx-button {
      height: 24px;
      line-height: 24px;
      padding: 0 8px;
      font-size: 12px;
      color: #fff;
      background: transparent;
      border: none;
      z-index: 2;
      pointer-events: auto;
      margin: 0 0 2px 0;
      &:hover {
        background: rgba(255, 255, 255, 0.1);
      }
    }

    .upload-placeholder {
      width: 80px;
      height: 80px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px dashed #e1e1e1;
      border-radius: 4px;
      background: #fafbfc;
      color: #909399;
      font-size: 14px;
      cursor: pointer;
      transition: border-color 0.2s;
      &:hover {
        border-color: #409eff;
        color: #409eff;
      }
    }
  }
}

.preview-section {
  height: 100%;
  padding: 0 24px;
  position: relative;
  transform: translateZ(0);
  will-change: transform;

  &.is-loading {
    pointer-events: none;
  }
}

.preview-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255,255,255,0.85);
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  pointer-events: all;
}

.loading-animation {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.loading-dots {
  display: flex;
  gap: 8px;

  span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #409eff;
    animation: bounce 1.4s infinite ease-in-out both;

    &:nth-child(1) {
      animation-delay: -0.32s;
    }

    &:nth-child(2) {
      animation-delay: -0.16s;
    }
  }
}

.loading-icon {
  animation: pulse 2s infinite;
  color: #409eff;
}

.loading-text {
  font-size: 16px;
  color: #303133;
  margin: 0;
}

.loading-tip {
  font-size: 14px;
  color: #909399;
  margin: 0;
  text-align: center;
  min-height: 20px;
  transition: opacity 0.3s;
}

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1.0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.preview-grid {
  display: grid;
  gap: 16px;
  padding: 16px;
  position: relative;
  overflow-y: auto;
  transform: translateZ(0);
  will-change: transform;
}

.preview-item {
  position: relative;
  aspect-ratio: 2 / 3;
  flex: 1;
  max-width: 300px;
  background: #f5f7fa;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translateZ(0);
  will-change: transform;

  &__wrapper {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    transform: translateZ(0);
    will-change: transform;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    .preview-item__actions {
      opacity: 1;
    }
  }

  &--selected {
    border-color: #409eff;
    background: #ecf5ff;

    &::after {
      content: '';
      position: absolute;
      top: 8px;
      right: 8px;
      width: 20px;
      height: 20px;
      background: #409eff;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      font-size: 12px;
      z-index: 1;
    }

    &::before {
      content: '✓';
      position: absolute;
      top: 8px;
      right: 8px;
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      font-size: 12px;
      z-index: 2;
    }
  }
}

.preview-image {
  max-width: 90%;
  max-height: 90%;
  object-fit: contain;
  display: block;
  margin: 0 auto;
}

.preview-item__actions {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.3s;
  background: rgba(255, 255, 255, 0.9);
  padding: 4px;
  border-radius: 4px;
  z-index: 3;
  align-items: center;
}

.preview-item__actions .el-icon-download {
  font-size: 20px;
  color: #409eff;
  vertical-align: middle;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-actions {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 16px;
  z-index: 2;
  display: flex;
  justify-content: center;
  padding: 0;
  background: none;
  width: 100%;
  box-sizing: border-box;

  &__regenerate {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    padding: 0 20px;
    height: 40px;
    min-width: 100px;
    font-size: 16px;
    border-radius: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    background: #fff;
    color: #303133;
    border: 1px solid #409eff;
    transition: all 0.2s;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      background: #ecf5ff;
    }

    &:disabled {
      background: #f5f7fa;
      color: #c0c4cc;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
      border-color: #ebeef5;
    }
  }
}

.preview-placeholder {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
  gap: 16px;

  p {
    margin: 0;
  }
}

.dialog-footer {
  text-align: right;
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.reference-image-bar {
  grid-column: 1 / -1;
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 8px;
}
.reference-image-preview {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #f0f9eb;
  border-radius: 6px;
  padding: 4px 8px;
  img {
    width: 48px;
    height: 72px;
    object-fit: contain;
    border-radius: 4px;
    border: 1px solid #e1e1e1;
    background: #fff;
  }
}
.reference-upload {
  display: inline-block;
}

.form-scrollable {
  height: 530px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  padding-right: 16px;
}
.form-actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 16px;
}
.reference-image-preview-inline {
  display: flex;
  position: relative;
  width: 80%;
  align-items: center;
  gap: 8px;
  background: #f0f9eb;
  border-radius: 6px;
  padding: 4px 8px;
  margin: 16px auto;
  aspect-ratio: 2 / 3;
  img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }
  .close-icon {
    position: absolute;
    top: 10px;
    right: 10px;
    cursor: pointer;
    width: 24px;
    height: 24px;
    background: #f5f5f5;
    text-align: center;
    line-height: 24px;
    border-radius: 50%;
    font-size: 12px;
    opacity: 0.8;
    &:hover {
      opacity: 1;
    }
  }
}
</style>

