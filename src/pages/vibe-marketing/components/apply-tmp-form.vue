<template>
  <div
    :class="[
      `${classPrefix} ${className}`,
      {
        preview: isPreview,
        wide: true,
      }
    ]"
  >
    <div :class="`${classPrefix}__content--prompt-wrap`">
      <div :class="`${classPrefix}__content--prompt`">
        <div :class="`${classPrefix}__content--mustache-label`">
          <span>{{ $t('marketing_taro.pkgs.tscyl_cc7774') }}</span>
          <img
            :src="isPreview ? iconFold : iconUnfold"
            class="icon"
            @click="handlePreviewToggle"
          >
        </div>
        <div :class="`${classPrefix}__content--mustache-input-wrap`">
          <fx-input
            v-model="promptText"
            type="textarea"
            autofocus
            size="small"
            :class="`${classPrefix}__content--mustache-input`"
            :placeholder="$t('marketing_taro.commons.qsr_02cc4f')"
            @change="handlePromptTextChange"
          />
        </div>
      </div>
    </div>
    <div
      v-if="mustachesList.length > 0"
      :class="[`${classPrefix}__content--mustaches`, 'marketing-scrollbar-theme']"
    >
      <div

        :class="`${classPrefix}__content--mustache-label title`"
      >
        <span>{{ $t('marketing_taro.pkgs.mbsz_f8d568') }}</span>
      </div>

      <div
        v-for="(item, i) in mustachesList"
        :key="i"
        :class="`${classPrefix}__content--mustache ${item.type}`"
      >
        <div :class="`${classPrefix}__content--mustache-label`">
          {{ item.key }}
        </div>
        <template v-if="item.type === 'textarea'">
          <fx-input
            :value="item.value"
            size="small"
            :class="`${classPrefix}__content--mustache-input`"
            :placeholder="item.placeholder || $t('marketing_taro.commons.qsr_02cc4f')"
            @input="e => handleInputChange(e, i)"
          />
        </template>

        <template v-else-if="item.type === 'select'">
          <fx-select
            :value="objectSelected(item.placeholder)"
            :options="objectOptions(item.placeholder)"
            :class="`${classPrefix}__content--mustache-input`"
            size="small"
            @change="e => handleSelectChange(e, i)"
          />
        </template>

        <template v-else-if="item.type === 'objSelector'">
          <tmpObjectSelector
            :value="objectSelected(item.placeholder)"
            :apiname="item.placeholder"
            :class="`${classPrefix}__content--mustache-input`"
            @input="e => handleSelectChange(e, i)"
          />
        </template>
      </div>
      <slot />
    </div>
  </div>
</template>

<script>
import { $cdnPath } from '@/utils/constant.js'
import tmpObjectSelector from './tmp-object-selector.vue'

export default {
  name: 'ApplyTmpForm',
  components: {
    tmpObjectSelector,
  },
  props: {
    className: {
      type: String,
      default: '',
    },
    prompt: {
      type: String,
      default: '',
    },
    preChatResult: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      classPrefix: 'ai-apply-tmp__form',
      promptText: '',
      mustaches: [],
      objectContainer: {},
      isPreview: false,
      iconFold: `${$cdnPath}/images/ai-icon-fold.svg`,
      iconUnfold: `${$cdnPath}/images/ai-icon-unfold.svg`,
    }
  },
  computed: {
    mustachesList() {
      return this.mustaches.filter(item => item.type !== 'prePrompt')
    },
  },
  watch: {
    preChatResult() {
      this.initPrompt()
    },
    prompt() {
      this.initPrompt(false)
    },
  },
  created() {
    this.promptRef = ''
    this.initPrompt(false)
  },
  methods: {
    async initPrompt(shouldEmitChange = true) {
      if (!this.prompt) return

      this.mustaches = []
      this.objectContainer = {}

      let promptTemplate = this.prompt
      const matches = promptTemplate.match(/{{(.*?)}}|\[\[(.*?)\]\]|<<(.*?)>>/g) || []

      matches.forEach(async item => {
        if (/^{{/.test(item)) {
          const [key, placeholder] = item.replace(/{{|}}/g, '').split(/\s*\|\s*/)
          promptTemplate = promptTemplate.replace(item, `{{${key}}}`)
          this.mustaches.push({
            key, placeholder, type: 'textarea', value: '',
          })
        }

        if (/^\[\[/.test(item)) {
          const [key, placeholder] = item.replace(/\[\[|\]\]/g, '').split(/\s*\|\s*/)
          promptTemplate = promptTemplate.replace(item, `[[${key}]]`)
          const mustache = {
            key, placeholder, type: 'select', value: '',
          }

          if (/^\{.*\}$/.test(placeholder)) {
            const options = placeholder.replace(/\{|\}/g, '').split(/\s*,\s*/)
            this.objectContainer = {
              ...this.objectContainer,
              [placeholder]: {
                options: options.map(el => ({ label: el, value: el })),
                selected: '',
              },
            }
          } else {
            mustache.type = 'objSelector'
            this.objectContainer = {
              ...this.objectContainer,
              [placeholder]: {
                type: 'objSelector',
                selected: [],
              },
            }
          }

          this.mustaches.push(mustache)
          // await initObjectOptions(placeholder)
        }

        if (/^<</.test(item)) {
          const [key, placeholder] = item.replace(/<<|>>/g, '').split(/\s*\|\s*/)
          promptTemplate = promptTemplate.replace(item, `<<${key}>>`)
          this.mustaches.push({
            key, placeholder, type: 'prePrompt', value: this.preChatResult[key] || '',
          })
        }
      })

      this.promptText = promptTemplate
      this.promptRef = promptTemplate

      if (shouldEmitChange) {
        this.updatePromptText()
      }
    },
    handlePreviewToggle() {
      this.isPreview = !this.isPreview
    },
    handleInputChange(value, index) {
      const { type, key } = this.mustaches[index]
      this.$set(this.mustaches[index], 'value', value)

      if (type === 'prePrompt') {
        this.$emit('onPrePromptChange', { key, value })
      }

      this.updatePromptText()
    },
    handleSelectChange(value, index) {
      const { placeholder } = this.mustaches[index]
      this.$set(this.objectContainer[placeholder], 'selected', value)
      this.updatePromptText()
    },
    handlePromptTextChange(e) {
      this.$emit('onChange', { promptText: this.promptText })
    },
    updatePromptText() {
      let text = this.promptRef
      const validate = this.mustaches.every(item => {
        const {
          type, key, placeholder, value,
        } = item
        if (type === 'textarea' && value) return true
        if (type === 'prePrompt' && this.preChatResult[key]) return true
        if (type === 'select') {
          const { selected } = this.objectContainer[placeholder] || {}
          return !!selected
        }
        if (type === 'objSelector') {
          const { selected } = this.objectContainer[placeholder] || {}
          return selected && selected.length > 0
        }
        return false
      })

      this.mustaches.forEach(el => {
        const {
          key, type, value, placeholder,
        } = el
        if (type === 'textarea') {
          text = text.replace(`{{${key}}}`, value || `{{${key}}}`)
        }
        if (type === 'select') {
          const { selected } = this.objectContainer[placeholder] || {}
          text = text.replace(`[[${key}]]`, selected || `[[${key}]]`)
        }
        if (type === 'objSelector') {
          const { selected } = this.objectContainer[placeholder] || {}
          const name = selected.map(item => item.name).join(', ')
          text = text.replace(`[[${key}]]`, name || `[[${key}]]`)
        }
        if (type === 'prePrompt') {
          text = text.replace(`<<${key}>>`, this.preChatResult[key] || `<<${key}>>`)
        }
      })

      this.promptText = text

      const objects = Object.keys(this.objectContainer)
        .filter(objectName => {
          const { type, selected } = this.objectContainer[objectName]
          return type === 'objSelector' && selected && selected.length > 0
        })
        .map(objectName => {
          const { selected } = this.objectContainer[objectName]
          return {
            object_api_name: objectName,
            object_id: selected[0]._id,
          }
        })

      this.$emit('onChange', {
        promptText: text,
        isValidate: validate,
        objects,
      })
    },
    objectOptions(objectName) {
      const { options } = this.objectContainer[objectName] || {}
      return options || []
    },
    objectSelected(objectName) {
      const { selected } = this.objectContainer[objectName] || {}
      return selected
    },
  },
}
</script>

<style lang="less" scoped>
.ai-apply-tmp__form {
  background: #F7F8FA;
  min-height: 100%;

  &__content--mustaches {
    display: flex;
    flex-direction: column;
    padding: 0 16px 20px;
    background-color: #F7F8FA;
  }

  &__content--mustache {
    display: flex;
    flex-direction: column;
    margin-top: 20px;

    &.prePrompt {
      .ai-apply-tmp__form__content--mustache-input-wrap {
        height: 150px;
        max-height: inherit;
        padding: 0 10px;

        .ai-apply-tmp__form__content--mustache-input {
          height: 100%;
          box-sizing: border-box;
        }
      }
    }
  }

  &__content--mustache-label {
    font-size: 14px;
    line-height: 20px;
    color: #545861;

    .require {
      color: #FF5730;
      position: relative;
      top: 3px;
      margin-right: 2px;
    }

    .tips {
      color: #91959E;
    }

    &.title {
      line-height: 20px;
      font-size: 14px;
      color: #181C25;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-top: 20px;
      font-weight: bold;
    }
  }

  &__content--mustache-input-wrap {
    margin-top: 2px;
    box-sizing: border-box;
    max-height: 93px;
    border-radius: 4px;
    padding: 0 8px;
    border: 1px solid #C1C5CE;
    overflow-y: auto;
    background: #FFF;

    &::-webkit-scrollbar {
      width: 6px;
    }
    &::-webkit-scrollbar-thumb {
      background: #C1C5CE;
      border-radius: 10px;

      &:hover {
        background: #737C8C;
      }
    }
  }

  &__content--mustache-input {
    display: block;
    width: 100%;
    padding: 8px 0;
    font-size: 14px;
    line-height: 20px;

    &.picker {
      height: 40px;
      line-height: 40px;
      padding: 0 8px;
      position: relative;
      overflow: initial;

      &.placeholder {
        color: rgba(193, 197, 206, 1);
        background: transparent;
      }

      .icon {
        position: absolute;
        width: 16px;
        height: 16px;
        right: 5px;
        top: 12px;
        font-size: 0;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    /deep/ textarea {
      height: 100%;
      resize: none;
      font-size: 14px;
      line-height: 20px;
      border: none;
      padding: 0;

      &.auto-height {
        height: auto;
      }
    }

    &::-webkit-scrollbar, /deep/ textarea::-webkit-scrollbar {
      width: 6px;
      background: none;
      height: 6px;
    }
    &::-webkit-scrollbar-button, /deep/ textarea::-webkit-scrollbar-button {
      height: 0;
      width: 0;
    }

    &::-webkit-scrollbar-thumb, /deep/ textarea::-webkit-scrollbar-thumb {
      background-color: rgba(0, 0, 0, 0.2);
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb:window-inactive, /deep/ textarea::-webkit-scrollbar-thumb:window-inactive {
      background: rgba(0, 0, 0, 0.2);
    }

    &::-webkit-scrollbar-thumb:hover, /deep/ textarea::-webkit-scrollbar-thumb:hover,
    &::-webkit-scrollbar-thumb:active, /deep/ textarea::-webkit-scrollbar-thumb:active {
      background-color: rgba(0, 0, 0, 0.4);
    }

    &::-webkit-scrollbar-thumb:horizontal, /deep/ textarea::-webkit-scrollbar-thumb:horizontal {
      border-width: 6px 1px 1px;
      padding: 0 0 0 100px;
      box-shadow: inset 1px 1px 0 rgba(0, 0, 0, 0.1),
        inset -1px 0 0 rgba(0, 0, 0, 0.07);
    }

    &::-webkit-scrollbar-corner, /deep/ textarea::-webkit-scrollbar-corner {
      background: transparent;
    }
  }

  &__content--prompt-wrap {
    background: #FFF;
    padding: 24px 16px;

    .ai-apply-tmp__form__content--prompt .ai-apply-tmp__form__content--mustache-input-wrap {
      padding: 0;

      .ai-apply-tmp__form__content--mustache-input {
        padding: 8px;
      }
    }
  }

  &__content--prompt {
    border-radius: 3px;
    border: 0.5px solid #DEE1E8;

    .ai-apply-tmp__form__content--mustache-label {
      height: 36px;
      line-height: 36px;
      background: #F2F3F5;
      padding: 0 10px;
      font-size: 14px;
      color: #181C25;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .icon {
        width: 16px;
        height: 17px;
        cursor: pointer;
        font-size: 0;
      }
    }

    .ai-apply-tmp__form__content--mustache-input-wrap {
      height: 250px;
      border: none;
      max-height: inherit;
      padding: 0 10px;

      .ai-apply-tmp__form__content--mustache-input {
        height: 100%;
        box-sizing: border-box;
      }
    }
  }

  &__content--scene {
    font-size: 14px;
    color: #0C6CFF;
    cursor: pointer;
    margin-top: 16px;
  }

  &.wide {
    display: flex;
    align-items: stretch;
    height: 100%;

    .ai-apply-tmp__form__content--prompt-wrap {
      flex: 1;
      display: flex;
      flex-direction: column;

      .ai-apply-tmp__form__content--prompt {
        flex: 1;
        display: flex;
        flex-direction: column;

        .ai-apply-tmp__form__content--mustache-input-wrap {
          flex: 1;
        }
      }
    }

    .ai-apply-tmp__form__content--mustaches {
      width: 300px;
      overflow-y: auto;
    }
  }

  &.preview {
    display: flex;
    align-items: stretch;
    height: 100%;

    .ai-apply-tmp__form__content--prompt-wrap {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-right: 0;

      .ai-apply-tmp__form__content--prompt {
        flex: 1;
        display: flex;
        flex-direction: column;

        .ai-apply-tmp__form__content--mustache-input-wrap {
          flex: 1;
        }
      }
    }

    .ai-apply-tmp__form__content--mustaches {
      display: none;
    }
  }
}
</style>
