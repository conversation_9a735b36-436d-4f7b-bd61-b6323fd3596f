<template>
  <Dialog
    :class="$style['apply-tmp-dialog']"
    :visible="visible"
    :title="title"
    width="1000px"
    append-to-body
    @close="handleCancel"
  >
    <template v-if="isMultiple">
      <div :class="$style['apply-tmp-dialog__content']">
        <apply-multiple-tmp
          ref="applyMultipleTmp"
          :prompt-detail="promptDetail"
          @update:data="handleUpdateApplyMultipleTmpData"
        />
      </div>
      <div
        slot="footer"
        :class="$style['apply-tmp-dialog__footer']"
      >
        <fx-button
          size="small"
          type="default"
          plain
          :class="$style['apply-tmp-dialog__footer--btn']"
          @click="handleCancel"
        >
          {{ $t('marketing_taro.commons.qx_625fb2') }}
        </fx-button>

        <fx-button
          v-if="applyMultipleTmpData.stageIndex !== 0"
          size="small"
          type="default"
          plain
          :class="$style['apply-tmp-dialog__footer--btn']"
          @click="() => handlePrevStage(applyMultipleTmpData.stageIndex - 1)"
        >
          {{ $t('marketing_taro.pkgs.syb_eeb690') }}
        </fx-button>

        <fx-button
          v-if="applyMultipleTmpData.stageIndex !== applyMultipleTmpData.templateList.length - 1"
          size="small"
          type="primary"
          :class="$style['apply-tmp-dialog__footer--btn']"
          :loading="applyMultipleTmpData.isAIHandling"
          @click="() => handleNextStage(applyMultipleTmpData.stageIndex + 1)"
        >
          {{ applyMultipleTmpData.isAIHandling ? $t('marketing_taro.pkgs.skz_e55d20') : $t('marketing_taro.pkgs.xyb_38ce27') }}
        </fx-button>

        <fx-button
          v-if="applyMultipleTmpData.stageIndex === applyMultipleTmpData.templateList.length - 1"
          size="small"
          type="primary"
          :class="$style['apply-tmp-dialog__footer--btn']"
          @click="handleSend"
        >
          <img
            :src="iconSend"
            :class="$style.icon"
          >
          {{ $t('marketing_taro.commons.fs_1535fc') }}
        </fx-button>
      </div>
    </template>

    <template v-else>
      <div :class="$style['apply-tmp-dialog__content']">
        <apply-tmp
          ref="applyTmp"
          :prompt-detail="promptDetail"
        />
      </div>

      <div
        slot="footer"
        :class="$style['apply-tmp-dialog__footer']"
      >
        <fx-button
          size="small"
          type="default"
          plain
          :class="$style['apply-tmp-dialog__footer--btn']"
          @click="handleCancel"
        >
          {{ $t('marketing_taro.commons.qx_625fb2') }}
        </fx-button>
        <fx-button
          size="small"
          type="primary"
          :class="$style['apply-tmp-dialog__footer--btn']"
          @click="handleSend"
        >
          <img
            :src="iconSend"
            :class="$style.icon"
          >
          {{ $t('marketing_taro.commons.fs_1535fc') }}
        </fx-button>
      </div>
    </template>
  </Dialog>
</template>

<script>
import { $cdnPath } from '@/utils/constant.js'
import ApplyTmp from './apply-tmp.vue'
import ApplyMultipleTmp from './apply-multiple-tmp.vue'

export default {
  components: {
    Dialog: FxUI.Dialog,
    ApplyTmp,
    ApplyMultipleTmp,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    promptDetail: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      iconSend: `${$cdnPath}/images/ai-icon-send-white.svg`,
      applyMultipleTmpData: {
        stageIndex: 0,
        templateList: [],
        isAIHandling: false,
      },
    }
  },
  computed: {
    title() {
      const { objectData } = this.promptDetail
      return (objectData && objectData.title) || ''
    },
    isMultiple() {
      const { objectData } = this.promptDetail
      return (objectData && objectData.step_split) || false
    },
  },
  methods: {
    handleUpdateApplyMultipleTmpData(data) {
      this.applyMultipleTmpData = {
        ...this.applyMultipleTmpData,
        ...data,
      }
    },
    handleCancel() {
      this.$emit('onClose')
    },
    handleSend() {
      if (this.$refs.applyMultipleTmp) {
        const payload = this.$refs.applyMultipleTmp.getSendPayload()

        if (payload) {
          this.$emit('onSend', payload)
        }
      }

      if (this.$refs.applyTmp) {
        const payload = this.$refs.applyTmp.getSendPayload()
        if (payload) {
          this.$emit('onSend', payload)
        }
      }

      this.handleCancel()
    },
    handlePrevStage(index) {
      if (this.$refs.applyMultipleTmp) {
        this.$refs.applyMultipleTmp.handlePrevStage(index)
      }
    },
    handleNextStage(index) {
      if (this.$refs.applyMultipleTmp) {
        this.$refs.applyMultipleTmp.handleNextStage(index)
      }
    },
  },
}

</script>

<style lang="less" module>
.apply-tmp-dialog {

  :global {
    .el-dialog__body {
      padding: 0;
    }
  }

  .apply-tmp-dialog__content {
    height: 60vh;
  }

  .apply-tmp-dialog__footer {
    display: flex;
    box-sizing: border-box;
    justify-content: flex-end;

    .apply-tmp-dialog__footer--btn {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 32px;
      line-height: 32px;
      border-radius: 2px;
      font-weight: 400;
      font-size: 13px;
      cursor: pointer;
      padding: 0 15px;
      margin-right: 16px;
      width: 100px;

      &:last-of-type {
        margin-right: 0;
      }

      .icon {
        width: 16px;
        height: 16px;
        margin-right: 4px;
        font-size: 0;
        vertical-align: text-bottom;
      }
    }
  }
}
</style>
