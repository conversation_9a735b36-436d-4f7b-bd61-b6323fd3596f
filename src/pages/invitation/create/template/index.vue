<template>
  <div class="v-template-wrapper">
    <div class="template-list">
      <template-list
        :list="templateList"
        :selected="template"
        @select:template="handleSelectTemplate"
      ></template-list>
    </div>
    <div class="template-previews">
      <div class="previews-wrapper">
        <div class="previews-header">{{ $t('marketing.commons.yl_645dbc') }}</div>
        <template-preview
          :type="type"
          :data="tempData"
          :template="template"
          @update:previews="handleUpdatePreveiws"
        ></template-preview>
      </div>
    </div>
    <div class="template-info">
      <template-info
        :type="type"
        :data="tempData"
        @update:info="handleUpdateTemplateInfo"
      ></template-info>
    </div>
  </div>
</template>

<script>

import html2canvas from "@/../libs/html2canvas";
import utils from "../utils";
import TemplateList from "./list/index";
import TemplateInfo from "./info/index";
import TemplatePreview from "./previews/index";
import _ from "lodash";
// MockData
// import templateList from './description';

export default {
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    default: {
      type: Object,
      default: () => ({})
    },
    templateList: {
      type: Array,
      default: () => [{}]
    }
  },
  data() {
    return {
      tempData: {},
      template: {},
      type: this.data.activityTemplateId ? "system" : "custom"
    };
  },
  created() {
    this._parseRespondData(this.data);
    this.tempData = _.extend({}, this.tempData, this.default);
  },
  methods: {
    myTemplate2Set(template) {
      if (template && template.enrollApiName === "template2") {
        // 标题字数问题，针对第二个模板特殊处理
        let description = template.descriptionList[2] || {};
        description.lineHeight = 75;
        description.top = 240;
        description.left = 20;
        description.width = 710;
        description.overflow = "visible";
        // 活动时间字数问题
        description = template.descriptionList[4] || {};
        description.left = 50;
        description.width = 650;
      }
    },
    // 选择模板
    handleSelectTemplate(template) {
      this.myTemplate2Set(template);
      this.template = template;
      this.type = template.id ? "system" : "custom";
      this.$emit("update:template", template);
    },
    // 更新模板，包含系统模板和自定义模板信息
    handleUpdateTemplateInfo(info) {
      if (_.isEqual(this.tempData, info)) return;
      this.tempData = _.extend({}, info);
    },
    // 仅用于系统模板使用
    handleUpdatePreveiws(domPreviews) {
      this.domPreviews = domPreviews;
    },
    getValues() {
      return new Promise((resolve, reject) => {
        if (this.type === "custom") {
          const {
            invitationName,
            coverImageUrl,
            coverImageTAPath,
            isChangeCoverImage
          } = this.tempData;
          if (!invitationName) {
            FxUI.Message.error($t('marketing.commons.qsryqhmc_e618a6'));
            return reject();
          }
          // if ((!coverImageUrl && !coverImageTAPath) || (isChangeCoverImage && !coverImageTAPath)) {
          //   Message.error('请选择封面图片');
          //   return reject();
          // }
          return resolve({
            invitationName,
            coverImageUrl,
            coverImageTAPath,
            isUpdateCoverImage: !!isChangeCoverImage
          });
        }

        const {
          invitationName,
          logo,
          company,
          title,
          address,
          date
        } = this.tempData;
        if (!invitationName) {
          FxUI.Message.error($t('marketing.commons.qsryqhmc_e618a6'));
          return reject();
        }
        if (!logo) {
          FxUI.Message.error($t('marketing.commons.qxzgs_466459'));
          return reject();
        }
        if (!company) {
          FxUI.Message.error($t('marketing.commons.qsrgsmc_96e6a2'));
          return reject();
        }
        if (!title) {
          FxUI.Message.error($t('marketing.commons.qsrhdbt_c0e2a5'));
          return reject();
        }
        if (!address) {
          FxUI.Message.error($t('marketing.commons.qsrhddz_30d6ae'));
          return reject();
        }
        if (!date) {
          FxUI.Message.error($t('marketing.commons.qsrhdrq_0c8892'));
          return reject();
        }
        this._html2image(result => {
          if (result === false || !result.TAPath) {
            FxUI.Message.error($t('marketing.commons.tpscsb_0d4113'));
            return reject();
          }
          return resolve({
            invitationName: invitationName,
            activityTemplateId: this.template.id,
            coverImageTAPath: result.TAPath,
            templateCoverImageUrl: result.blob,
            isUpdateCoverImage: true,
            activityTemplateValues: _.map(
              ["logo", "company", "title", "address", "date"],
              key => {
                return { name: key, value: this.tempData[key] };
              }
            )
          });
        });
      });
    },
    _html2image(callback) {
      
      html2canvas(this.domPreviews, {
        width: 750,
        height: 1206,
        logging: false,
        onclone: doc => {
          doc.querySelector(".previews-frame").style.transform = "scale(1)";
        }
      })
        .then(
          canvas => {
            let blob = "";
            try {
              blob = utils.base64toblob(canvas.toDataURL("image/jpeg"));
            } catch (e) {
              callback(false);
            }
            utils.uploadFile(blob, TAPath => {
              if (!TAPath) {
                callback(false);
                FxUI.Message.error($t('marketing.commons.sctpsb_12fb9d'));
                return;
              }
              callback({ blob: URL.createObjectURL(blob), TAPath });
            });
          },
          () => {
            window["console"].log(reject);
          }
        )
        .catch(() => {
          window["console"]
        });
    },
    _parseRespondData(data) {
      this.type = data.activityTemplateId ? "system" : "custom";
      this.template = _.find(this.templateList, item => {
        return item.id === data.activityTemplateId;
      });
      this.myTemplate2Set(this.template);
      this.tempData = _.extend(utils.arr2obj(data.activityTemplateValues), {
        invitationName: data.invitationName,
        coverImageUrl: data.activityTemplateId ? "" : data.coverImageUrl
      });
      this.$emit("update:template", this.template);
    }
  },
  watch: {
    data(newVal) {
      this._parseRespondData(newVal);
    },
    default(newVal) {
      this.tempData = _.extend({}, this.tempData, newVal);
    },
    templateList(newVal) {
      this._parseRespondData(this.data);
      if (!this.data.activityTemplateId) return;
      this.template = _.find(newVal, item => {
        return item.id === this.data.activityTemplateId;
      });
      this.myTemplate2Set(this.template);
      this.$emit("update:template", this.template);
    }
  },
  components: {
TemplateList,
TemplateInfo,
TemplatePreview
}
};
</script>

<style lang="less">
@basePath: "../../../../";

.v-template-wrapper {
  display: flex;
  flex-flow: row nowrap;
  height: 100%;
  .template-list {
    flex: 0 0 auto;
    width: 260px;
    height: 100%;
    // display: none;
  }
  .template-info {
    flex: 0 0 auto;
    width: 330px;
    height: 100%;
  }
  .template-previews {
    flex: 1 1 auto;
    display: flex;
    flex-flow: column nowrap;
    height: 100%;
    border-right: 1px solid #ededed;
    .previews-header {
      flex: 0 0 auto;
      margin-bottom: 50px;
      padding: 0 14px;
      height: 50px;
      line-height: 50px;
      font-size: 16px;
      color: #212b36;
      border-bottom: 1px solid #ededed;
    }
  }
}
</style>
