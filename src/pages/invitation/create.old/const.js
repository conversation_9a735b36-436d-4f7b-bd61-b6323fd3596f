export default {
  stepInfo: [
    {
      name: $t('marketing.pages.invitation.txhdjbxx_b37fe2'),
      component: 'baseinfo',
    },
    {
      name: $t('marketing.commons.xzyqh_f879c7'),
      component: 'template',
    },
    {
      name: $t('marketing.commons.hdxq_4553b4'),
      component: 'detailinfo',
    },
    // {
    //   name: '设置报名表单',
    //   component: 'smartform',
    // },
    // {
    //   name: '其他设置',
    //   component: 'otherinfo',
    // },
  ],
  SCALE: 2.307692307692308,
  WIDTH: 750,
  HEIGHT: 1206,
  templateMap: {
    c4ca4238a0b933828dcc509a6f75849b: 'template1',
    c81e728d9d4c3f63af067f89cc14862c: 'template2',
    eccbc87e4b5c32fea8308fd9f2a7baf3: 'template3',
    a87ff679a2f3371d9181a67b7542122c: 'template4',
    e4da3b7fbbce334597772b0674a318d5: 'template5',
    '1679091c5a883fafafb5e6087eb1b2dc': 'template6',
    '8f14e45fceea367a9a36dedd4bea2543': 'template7',
    c9f0f895fb983b9199f51fd0297e236d: 'template8',
    '45c48cce2e2d3fbdaa1afc51c7c6ad26': 'template9',
    d3d9446802a43259b55d38e6d163e820: 'template10',
  },
};
