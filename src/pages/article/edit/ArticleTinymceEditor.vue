<template>
  <div class="tinymce marketing-editor-AI-helper-wrapper">
    <editor id="marketing-tinymce" v-model="tinymceHtml" :init="init"></editor>
    <div class="marketing-editor-AI-writing-wrapper" />
    <div
      class="marketing-editor-AI-short-cut-wrapper"
      @click="handleInitAIHelperByShortCut"
    >
      <svg class="fx-icon-AI" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M5.22643 8.05307C4.87296 8.05307 4.58643 8.3396 4.58643 8.69307V9.5464C4.58643 9.89986 4.87296 10.1864 5.22643 10.1864H5.43976C5.79322 10.1864 6.07976 9.89986 6.07976 9.5464V8.69307C6.07976 8.3396 5.79322 8.05307 5.43976 8.05307H5.22643Z" fill="url(#paint0_linear_838_8160)"/>
        <path d="M9.91992 8.69307C9.91992 8.3396 10.2065 8.05307 10.5599 8.05307H10.7733C11.1267 8.05307 11.4133 8.3396 11.4133 8.69307V9.5464C11.4133 9.89986 11.1267 10.1864 10.7733 10.1864H10.5599C10.2065 10.1864 9.91992 9.89986 9.91992 9.5464V8.69307Z" fill="url(#paint1_linear_838_8160)"/>
        <path d="M8.7393 10.2115C8.7795 10.1145 8.8002 10.0105 8.8002 9.9054H7.2002C7.2002 10.0105 7.22089 10.1145 7.26109 10.2115C7.3013 10.3086 7.36022 10.3968 7.43451 10.4711C7.5088 10.5454 7.59699 10.6043 7.69405 10.6445C7.79111 10.6847 7.89514 10.7054 8.0002 10.7054C8.10525 10.7054 8.20928 10.6847 8.30634 10.6445C8.4034 10.6043 8.49159 10.5454 8.56588 10.4711C8.64017 10.3968 8.6991 10.3086 8.7393 10.2115Z" fill="url(#paint2_linear_838_8160)"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M3.31775 2.30309C3.20105 2.05282 3.30933 1.75533 3.5596 1.63863L4.82843 1.04697C5.0787 0.930263 5.37619 1.03854 5.49289 1.28881L5.7042 1.74196C5.82091 1.99223 5.71263 2.28972 5.46236 2.40643L5.11511 2.56835L5.79509 3.90962C6.47521 3.86466 7.21092 3.84994 8 3.84994C8.78908 3.84994 9.52479 3.86466 10.2049 3.90962L10.8849 2.56835L10.5376 2.40643C10.2874 2.28972 10.1791 1.99223 10.2958 1.74196L10.5071 1.28881C10.6238 1.03854 10.9213 0.930263 11.1716 1.04697L12.4404 1.63863C12.6907 1.75533 12.7989 2.05282 12.6822 2.30309L12.4709 2.75625C12.3542 3.00652 12.0567 3.1148 11.8065 2.99809L11.429 2.82206L10.8503 3.96357C14.0264 4.29048 15.8318 5.47648 16 9.38654C16 14.8372 13.7707 14.9865 8 14.9865C2.22933 14.9865 0 14.8372 0 9.38654C0.168171 5.47648 1.97358 4.29048 5.14974 3.96357L4.57104 2.82206L4.19353 2.99809C3.94326 3.1148 3.64577 3.00652 3.52906 2.75625L3.31775 2.30309ZM11.2543 4.95967C10.9486 4.92373 10.6425 4.89052 10.3361 4.8614C9.63733 4.79564 8.8613 4.74633 8 4.74633C7.1387 4.74633 6.36267 4.79564 5.66393 4.8614C5.35746 4.89052 5.05142 4.92373 4.74568 4.95967C3.82413 5.06799 3.23569 5.27919 2.72 5.55998C2.23051 5.82651 1.86731 6.1942 1.61067 6.74995C1.34222 7.33127 1.06667 8.16716 1.06667 9.38654C1.06667 11.8725 1.64378 12.5022 2.44464 12.9597C2.91691 13.2294 3.57938 13.5194 4.52662 13.6281C5.47045 13.7364 6.61082 13.7599 8 13.7599C9.38918 13.7599 10.5295 13.7364 11.4734 13.6281C12.4206 13.5194 13.0831 13.2294 13.5554 12.9597C14.3562 12.5022 14.9333 11.8725 14.9333 9.38654C14.9333 8.16716 14.653 7.33127 14.3846 6.74995C14.1279 6.1942 13.765 5.82651 13.2756 5.55998C12.7599 5.27919 12.1759 5.06799 11.2543 4.95967Z" fill="url(#paint3_linear_838_8160)"/>
        <defs>
        <linearGradient id="paint0_linear_838_8160" x1="8" y1="1" x2="8" y2="14.9865" gradientUnits="userSpaceOnUse">
        <stop stop-color="#C98FF7"/>
        <stop offset="0.485" stop-color="#5A6FE9"/>
        <stop offset="1" stop-color="#73A1FF"/>
        </linearGradient>
        <linearGradient id="paint1_linear_838_8160" x1="8" y1="1" x2="8" y2="14.9865" gradientUnits="userSpaceOnUse">
        <stop stop-color="#C98FF7"/>
        <stop offset="0.485" stop-color="#5A6FE9"/>
        <stop offset="1" stop-color="#73A1FF"/>
        </linearGradient>
        <linearGradient id="paint2_linear_838_8160" x1="8" y1="1" x2="8" y2="14.9865" gradientUnits="userSpaceOnUse">
        <stop stop-color="#C98FF7"/>
        <stop offset="0.485" stop-color="#5A6FE9"/>
        <stop offset="1" stop-color="#73A1FF"/>
        </linearGradient>
        <linearGradient id="paint3_linear_838_8160" x1="8" y1="1" x2="8" y2="14.9865" gradientUnits="userSpaceOnUse">
        <stop stop-color="#C98FF7"/>
        <stop offset="0.485" stop-color="#5A6FE9"/>
        <stop offset="1" stop-color="#73A1FF"/>
        </linearGradient>
        </defs>
      </svg>
      <span>{{ $t('marketing.ai_helper.zs_130695') }}</span>
    </div>

    <VideoDialog
      v-if="isShowVideoDialog"
      :visible.sync="isShowVideoDialog"
      @confirm="handleConfirmVideo"
      @close="isShowVideoDialog = false"
    />
    <PictureSelector
      :visible.sync="isShowCutterDialog"
      :cut-size="{}"
      @submit="handlePictureSelectorSubmit"
    />
  </div>
</template>

<script>
import PictureSelector from '@/components/PictureSelector'
import http from "@/services/http/index";
import tinymce from "tinymce/tinymce";
import Editor from "@tinymce/tinymce-vue";
import "tinymce/themes/silver/theme";
import "tinymce/icons/default";
import "tinymce/skins/ui/oxide/skin.min.css";
import "tinymce/plugins/image";
// import "tinymce/plugins/media";
import "tinymce/plugins/link";
import "tinymce/plugins/code";
import "tinymce/plugins/table";
import "tinymce/plugins/lists";
import "tinymce/plugins/wordcount";
import "tinymce/plugins/colorpicker";
import "tinymce/plugins/textcolor";
import { requireAsync } from "@/utils";
import { getScreenDPI } from '@/utils/screen-helper.js'
import kisvData from '@/modules/kisv-data.js'
import MarkdownIt from 'markdown-it'

import VideoDialog from '@/pages/video/components/video-dialog.vue'

export default {
  name: "tinymce",
  components: {
    Editor,
    VideoDialog,
    PictureSelector,
  },
  props: {
    height: {
      type: Number,
      default: 800
    },
    content: {
      type: String,
      default: ""
    },
    marketingEventId: {
      type: String,
      default: () => ""
    }
  },
  data() {
    return {
      vDatas: kisvData.datas,
      isShowVideoDialog: false,
      percentage: 0,
      //编辑器
      tinymceHtml: this.content,
      init: {
        content_style: `.mce-content-body span,
        .mce-content-body section,
        .mce-content-body ul,
        .mce-content-body li,
        .mce-content-body strong,
        .mce-content-body p{
          margin: 0;
          padding: 0;
        }
          `,
        body_class: 'yxt_tinymce_body_class',
        indent: false, //去掉空格换行
        // forced_root_block : false,
         remove_trailing_brs: false,
        valid_elements:'*[*]', //保留所有的元素和属性在编辑的文本中
        valid_children:'+span[p],+span[strong],+p[br]',//使span标签中可以包含p标签strong标签
        base_url: FS.APP_MARKETING_MODULE.ROOT_PATH + "/static/tinymce",
        language_url:
          FS.APP_MARKETING_MODULE.ROOT_PATH + "/static/tinymce/langs/zh_CN.js",
        skin_url:
          FS.APP_MARKETING_MODULE.ROOT_PATH + "/static/tinymce/skins/ui/oxide",
        language: (FS.userLanguage || 'zh-CN').replace('-', '_'),
        icons_url:
          FS.APP_MARKETING_MODULE.ROOT_PATH +
          "/static/tinymce/icons/custom/icons.js",
        icons: "christmas",
        height: this.height, //编辑器高度
        resize: true,
        // axupimgs_filetype: ".png",
        menubar: "", //菜单栏
        powerpaste_word_import: "propmt", // 参数可以是propmt, merge, clear，效果自行切换对比
        powerpaste_html_import: "propmt", // propmt, merge, clear
        powerpaste_allow_local_images: true,
        powerpaste_keep_unsupported_src: true,
        convert_urls: false,
        toolbar_mode: "wrap", //工具栏排列模式
        table_toolbar: "", //表格工具栏
        plugins: "link lists media image code table wordcount textcolor  powerpaste ax_wordlimit",
        ax_wordlimit_num: 200000,
        ax_wordlimit_callback: function(editor,txt,num){
          console.log('当前字数：' + txt.length + '，限制字数：' + num);
        },
        external_plugins: {
          powerpaste:
            FS.APP_MARKETING_MODULE.ROOT_PATH +
            "/static/tinymce/plugins/powerpaste/plugin.min.js",
          ax_wordlimit:
              FS.APP_MARKETING_MODULE.ROOT_PATH +
              "/static/tinymce/plugins/ax_wordlimit/plugin.min.js"
        },
        toolbar:
          "AIHelperButton | undo redo  removeformat | fontsizeselect fontselect styleselect | bold  italic underline strikethrough|forecolor backcolor  | alignleft aligncenter alignright alignjustify|bullist numlist   | outdent indent | link  video  table | imagepicker | code  ",
        // color_cols: 4,
        color_map: [
          "#181c25",
          "#181c25",
          "#91959E",
          "#91959E",
          "#ffffff",
          "#ffffff",
          "#407FFF",
          "#407FFF",
          "#e9edf5",
          "#e9edf5",
          "#ff4500",
          "#ff4500",
          "#ff8c00",
          "#ff8c00",
          "#ffd700",
          "#ffd700",
          "#90ee90",
          "#90ee90",
          "#00ced1",
          "#00ced1",
          "#1e90ff",
          "#1e90ff",
          "#c71585",
          "#c71585",
          "#FF4500",
          "#FF4500",
          "#FF7800",
          "#FF7800"
        ],
         style_formats: [
          {
            title: $t('marketing.commons.hg_17169f'),
            items: [
              { title: "1", block: "p", styles: { "line-height": "1.0" } },
              { title: "1.5", block: "p", styles: { "line-height": "1.5" } },
              { title: "1.75", block: "p", styles: { "line-height": "1.75" } },
              { title: "2", block: "p", styles: { "line-height": "2" } },
              { title: "3", block: "p", styles: { "line-height": "3" } },
              { title: "4", block: "p", styles: { "line-height": "4" } },
              { title: "5", block: "p", styles: { "line-height": "5" } }
            ]
          }
        ],
        style_formats_merge: true, //设置行高
        style_formats_autohide: true, //设置行高
        fontsize_formats: "11px 12px 14px 16px 18px 24px 36px 48px",
        font_formats:
          `${$t('marketing.commons.ht_e0b004')}=SimHei, Hiragino Sans GB, STHeiti;${$t('marketing.commons.st_71b068')}=SimSun, NSimSun, STSong, STFangsong;Arial=arial,helvetica,sans-serif;`,
        images_upload_handler(blobInfo, succFun, failFun) {
          const formData = new FormData(); //新建一个FormData
          console.log("blobInfo", blobInfo);
          console.log("blobUri", blobInfo.blobUri());
          console.log("uri", blobInfo.uri());
          console.log("file", blobInfo.blob());
          let file = blobInfo.blob();
          console.log(file.__proto__.constructor.name);
          if (file.__proto__.constructor.name == "Blob") {
            //如果是Blob类型转file
            formData.append(
              "file",
              new window.File([blobInfo.blob()], blobInfo.filename(), {
                type: file.type
              })
            );
          } else {
            formData.append("file", file);
          }

          formData.append("type", 1);
          formData.append("needApath", true);
          http
            .uploadFile(formData)
            .then(res => {
              if (res.errCode === 0) {
                console.log("上传成功");
                const imageUrl = res.data.url;
                succFun(res.data.url);
              } else {
                alert(res.errMsg || $t('marketing.commons.scsbqzs_8e2f99'));
              }
            })
            .catch(() => {
              alert($t('marketing.commons.scsbqzs_8e2f99'));
            });
        },
        paste_postprocess(pluginApi, data) {},
        paste_preprocess(pluginApi, data) {},
        setup: editor => {
          var timer = setInterval(function() {
            if ($(".tox-statusbar__branding").length) {
              clearInterval(timer);
              $(".tox-statusbar__branding").css("display", "none"); //去掉右下角tiny字样
              $(".tox-statusbar__path").css("display", "none"); //去掉左下角当前编辑模板
            }
          }, 50);
          editor.ui.registry.addIcon(
            'AIHelper',
            `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M5.22643 8.05307C4.87296 8.05307 4.58643 8.3396 4.58643 8.69307V9.5464C4.58643 9.89986 4.87296 10.1864 5.22643 10.1864H5.43976C5.79322 10.1864 6.07976 9.89986 6.07976 9.5464V8.69307C6.07976 8.3396 5.79322 8.05307 5.43976 8.05307H5.22643Z" fill="url(#paint0_linear_838_8160)"/>
              <path d="M9.91992 8.69307C9.91992 8.3396 10.2065 8.05307 10.5599 8.05307H10.7733C11.1267 8.05307 11.4133 8.3396 11.4133 8.69307V9.5464C11.4133 9.89986 11.1267 10.1864 10.7733 10.1864H10.5599C10.2065 10.1864 9.91992 9.89986 9.91992 9.5464V8.69307Z" fill="url(#paint1_linear_838_8160)"/>
              <path d="M8.7393 10.2115C8.7795 10.1145 8.8002 10.0105 8.8002 9.9054H7.2002C7.2002 10.0105 7.22089 10.1145 7.26109 10.2115C7.3013 10.3086 7.36022 10.3968 7.43451 10.4711C7.5088 10.5454 7.59699 10.6043 7.69405 10.6445C7.79111 10.6847 7.89514 10.7054 8.0002 10.7054C8.10525 10.7054 8.20928 10.6847 8.30634 10.6445C8.4034 10.6043 8.49159 10.5454 8.56588 10.4711C8.64017 10.3968 8.6991 10.3086 8.7393 10.2115Z" fill="url(#paint2_linear_838_8160)"/>
              <path fill-rule="evenodd" clip-rule="evenodd" d="M3.31775 2.30309C3.20105 2.05282 3.30933 1.75533 3.5596 1.63863L4.82843 1.04697C5.0787 0.930263 5.37619 1.03854 5.49289 1.28881L5.7042 1.74196C5.82091 1.99223 5.71263 2.28972 5.46236 2.40643L5.11511 2.56835L5.79509 3.90962C6.47521 3.86466 7.21092 3.84994 8 3.84994C8.78908 3.84994 9.52479 3.86466 10.2049 3.90962L10.8849 2.56835L10.5376 2.40643C10.2874 2.28972 10.1791 1.99223 10.2958 1.74196L10.5071 1.28881C10.6238 1.03854 10.9213 0.930263 11.1716 1.04697L12.4404 1.63863C12.6907 1.75533 12.7989 2.05282 12.6822 2.30309L12.4709 2.75625C12.3542 3.00652 12.0567 3.1148 11.8065 2.99809L11.429 2.82206L10.8503 3.96357C14.0264 4.29048 15.8318 5.47648 16 9.38654C16 14.8372 13.7707 14.9865 8 14.9865C2.22933 14.9865 0 14.8372 0 9.38654C0.168171 5.47648 1.97358 4.29048 5.14974 3.96357L4.57104 2.82206L4.19353 2.99809C3.94326 3.1148 3.64577 3.00652 3.52906 2.75625L3.31775 2.30309ZM11.2543 4.95967C10.9486 4.92373 10.6425 4.89052 10.3361 4.8614C9.63733 4.79564 8.8613 4.74633 8 4.74633C7.1387 4.74633 6.36267 4.79564 5.66393 4.8614C5.35746 4.89052 5.05142 4.92373 4.74568 4.95967C3.82413 5.06799 3.23569 5.27919 2.72 5.55998C2.23051 5.82651 1.86731 6.1942 1.61067 6.74995C1.34222 7.33127 1.06667 8.16716 1.06667 9.38654C1.06667 11.8725 1.64378 12.5022 2.44464 12.9597C2.91691 13.2294 3.57938 13.5194 4.52662 13.6281C5.47045 13.7364 6.61082 13.7599 8 13.7599C9.38918 13.7599 10.5295 13.7364 11.4734 13.6281C12.4206 13.5194 13.0831 13.2294 13.5554 12.9597C14.3562 12.5022 14.9333 11.8725 14.9333 9.38654C14.9333 8.16716 14.653 7.33127 14.3846 6.74995C14.1279 6.1942 13.765 5.82651 13.2756 5.55998C12.7599 5.27919 12.1759 5.06799 11.2543 4.95967Z" fill="url(#paint3_linear_838_8160)"/>
              <defs>
              <linearGradient id="paint0_linear_838_8160" x1="8" y1="1" x2="8" y2="14.9865" gradientUnits="userSpaceOnUse">
              <stop stop-color="#C98FF7"/>
              <stop offset="0.485" stop-color="#5A6FE9"/>
              <stop offset="1" stop-color="#73A1FF"/>
              </linearGradient>
              <linearGradient id="paint1_linear_838_8160" x1="8" y1="1" x2="8" y2="14.9865" gradientUnits="userSpaceOnUse">
              <stop stop-color="#C98FF7"/>
              <stop offset="0.485" stop-color="#5A6FE9"/>
              <stop offset="1" stop-color="#73A1FF"/>
              </linearGradient>
              <linearGradient id="paint2_linear_838_8160" x1="8" y1="1" x2="8" y2="14.9865" gradientUnits="userSpaceOnUse">
              <stop stop-color="#C98FF7"/>
              <stop offset="0.485" stop-color="#5A6FE9"/>
              <stop offset="1" stop-color="#73A1FF"/>
              </linearGradient>
              <linearGradient id="paint3_linear_838_8160" x1="8" y1="1" x2="8" y2="14.9865" gradientUnits="userSpaceOnUse">
              <stop stop-color="#C98FF7"/>
              <stop offset="0.485" stop-color="#5A6FE9"/>
              <stop offset="1" stop-color="#73A1FF"/>
              </linearGradient>
              </defs>
              </svg>
              `,
          )
          editor.ui.registry.addButton('imagepicker', {
            // 添加插入图片按钮
            text: $t('marketing.commons.tpk_db7625'),
            icon: 'insert-picture',
            onAction: () => {
              this.isShowCutterDialog = true
            },
          })

          editor.ui.registry.addButton('video', {
            icon: 'embed',
            tooltip: $t('marketing.components.ueditor.crsp_a08f0c'),
            onAction: () => {
              this.isShowVideoDialog = true
            },
          })
          if (this.vDatas.uinfo.marketingAIPluginEnable) {
            editor.ui.registry.addButton('AIHelperButton', {
            // 添加AI助手按钮
              icon: 'AIHelper',
              onAction: () => {
                const selectedContent = editor.selection.getContent()
                if (selectedContent) {
                  this.handleInitAIHelperByShortCut()
                } else {
                  this.handleInitAIHelper()
                }
              },
            })
          }
          editor.on('click', e => {
            if (!e.target?.offsetParent || (e.target.offsetParent && e.target.offsetParent?.className?.indexOf('mce-content-body') === -1)) return
            const fontSize = tinymce.activeEditor.queryCommandValue('FontSize')
            // pt = px * dpi / 72   windows一般是96  mac一般是72
            const screenDPI = getScreenDPI() || 96
            this.editorFontSize = fontSize.indexOf('pt') !== -1 ? (((fontSize || '').split('pt')[0] - 0) * (screenDPI / 72)) : ((fontSize || '').split('px')[0] - 0)
            const selectedContent = editor.selection.getContent()
            this.selectionStyle = {
              top: e.y,
              left: e.x,
            }
            if (selectedContent) {
              this.setAIShortCutOptionsShow()
            } else {
              this.resetAIShortCutOptionsShow()
            }
          })
          editor.on('keydown', e => {
            this.resetAIShortCutOptionsShow()
            if (e.key === '/' && e.keyCode === 191 && !this.AIHelperObject) {
              this.handleInitAIHelper()
            } else if (this.AIHelperObject) {
              this.handleCloseAIDialog()
            }
          })
        },
        init_instance_callback: editor => {
          editor.execCommand("fontName", false, "Arial");
          editor.execCommand("fontSize", false, "12px");
        }
      },
      AIHelperObject: null,
      isShowCutterDialog: false
    };
  },
  watch: {
    tinymceHtml: {
      deep: true,
      handler(val) {
        this.$emit("setEditorContent", val);
      }
    },
    content: {
      deep: true,
      handler(val) {
        this.tinymceHtml = val;
      }
    },
    height() {
      this.init.height = this.height
      const editor = tinymce.get('marketing-tinymce')
      if (editor.getContainer()) {
        editor.getContainer().style.height = `${this.height}px`
      }
    },
  },
  mounted() {},
  created() {
    if (this.vDatas.uinfo.marketingAIPluginEnable) {
      this.init.placeholder = $t('marketing.components.tinymce_editor.srhxzbfwbn_803371')
    }
    // 预加载appcustomization
    window.seajs.use('paas-appcustomization/sdk')
  },
  methods: {
    handleConfirmVideo(data) {
      const selectedContent = tinymce.get('marketing-tinymce').selection.getContent()
      const _html = `<video src="${data.url}" poster="${data.cover}" controls width="100%">not support</video>`
      const wholeData = `${selectedContent}<br>${_html}`
      tinymce.get('marketing-tinymce').insertContent(wholeData)
    },
    getContent() {
      let content = tinymce.get("marketing-tinymce").getContent();
      console.log("content", content);
      console.log("tinymceHtml", this.tinymceHtml);
    },
    setContent(content) {
      tinymce.get("marketing-tinymce").setContent(content);
    },
    handleInitAIHelper(text) {
      const $mountDom = document.querySelector('.marketing-editor-AI-writing-wrapper')
      const modelParentNode = document.querySelector('.marketing-editor-AI-helper-wrapper')
      $mountDom.style.display = 'none'
      const me = this
      if (me.AIHelperObject) {
        return
      }
      const params = {
        type: '',
        modelParentNode,
      }
      if (text) {
        params.text = text
      }
      Fx.getBizAction('paasdev', 'openAigcPost', $mountDom,
        params,
        {
            close: () => {
              me.handleCloseAIDialog()
            },
            insert: (data) => {
              me.handleInsertAIContent(data)
            },
            replace: (data) => {
              me.handleReplaceAIContent(data)
            },
        }).then((instance) => {
        me.AIHelperObject = instance;
        me.handleSetAIHelperStyle()
      });
    },
    handleCloseAIDialog() {
      const AIHelper = document.querySelector('.ai-writing-toolbar')
      AIHelper?.remove()
      const editorIframeWindow = document.getElementById('marketing-tinymce_ifr').contentWindow
      const tinymceEdior = editorIframeWindow.document.querySelector('.mce-content-body')
      tinymceEdior.style.paddingBottom = 0
      this.handleDestroyAIObject()
    },
    handleInsertAIContent(data) {
      const selectedContent = tinymce.get('marketing-tinymce').selection.getContent()
      const md = new MarkdownIt()
      const renderHtml = md.render(data)
      const wholeData = `${selectedContent}<br>${renderHtml}`
      tinymce.get('marketing-tinymce').insertContent(wholeData)
      this.handleCloseAIDialog()
    },
    handleReplaceAIContent(data) {
      const md = new MarkdownIt()
      const renderHtml = md.render(data)
      tinymce.get('marketing-tinymce').insertContent(renderHtml)
      this.handleCloseAIDialog()
    },
    handleDestroyAIObject() {
      if (this.AIHelperObject) {
        this.AIHelperObject.$destroy()
        this.AIHelperObject = null
      }
    },
    handleSetAIHelperStyle() {
      const AIHelperWrapper = document.querySelector('.marketing-editor-AI-writing-wrapper')
      const editorIframeWindow = document.getElementById('marketing-tinymce_ifr').contentWindow
      const tinymceEdior = editorIframeWindow.document.querySelector('.mce-content-body')
      const AIHelper = document.querySelector('.ai-writing-toolbar')
      AIHelper.style.left = '10px'
      const editorHeaderHeight = document.querySelector('.marketing-editor-AI-helper-wrapper .tox-editor-header').offsetHeight
      setTimeout(() => {
        const top = (this.selectionStyle && this.selectionStyle.top) ? `${this.selectionStyle.top + editorHeaderHeight + this.editorFontSize}` : `${editorHeaderHeight + 3 * (this.editorFontSize || 12)}`
        AIHelperWrapper.style.display = 'block'
        const AIHelperOffsetHeight = document.querySelector('.marketing-editor-AI-writing-wrapper').offsetHeight
        if (AIHelperOffsetHeight + this.selectionStyle?.top + editorHeaderHeight >= 800) {
          AIHelperWrapper.style.top = `${(Number(top) - (AIHelperOffsetHeight + this.selectionStyle?.top + editorHeaderHeight - 800))}px`
          tinymceEdior.style.paddingBottom = `${AIHelperOffsetHeight + this.selectionStyle?.top + editorHeaderHeight - 800}px`
          editorIframeWindow.scrollTo(0, editorIframeWindow.document.scrollingElement.offsetHeight)
        } else {
          AIHelperWrapper.style.top = `${top}px`
        }
      }, 500)
    },
    setAIShortCutOptionsShow() {
      const AIHelperShortCut = document.querySelector('.marketing-editor-AI-short-cut-wrapper')
      AIHelperShortCut.style.display = 'flex'
      AIHelperShortCut.style.left = `${this.selectionStyle?.left}px`
      const editorHeaderHeight = document.querySelector('.marketing-editor-AI-helper-wrapper .tox-editor-header').offsetHeight
      // 20px 是富文本编辑器默认的margin
      AIHelperShortCut.style.top = this.selectionStyle?.top > (this.editorFontSize + 20) ? `${this.selectionStyle?.top + editorHeaderHeight - 36 - this.editorFontSize}px` : `${editorHeaderHeight}px`
    },
    resetAIShortCutOptionsShow() {
      const AIHelperShortCut = document.querySelector('.marketing-editor-AI-short-cut-wrapper')
      AIHelperShortCut.style.display = 'none'
    },
    handleInitAIHelperByShortCut() {
      this.resetAIShortCutOptionsShow()
      const selectedContent = tinymce.get('marketing-tinymce').selection.getContent()
      this.handleInitAIHelper(selectedContent)
    },
    handlePictureSelectorSubmit(file) {
      console.log('handlePictureSelectorSubmit', file)
      if(file.url)
      tinymce.get('marketing-tinymce').insertContent(`<img src="${file.url}">`)
    },
  }
};
</script>
<style>
.tox .tox-tbtn svg {
  fill: #545861;
  width: 20px;
  height: 20px;
  transform: scale(0.8);
}
.tox .tox-tbtn--disabled svg,
.app-marketing-scoped .tox .tox-tbtn--disabled:hover svg,
.app-marketing-scoped .tox .tox-tbtn:disabled svg,
.app-marketing-scoped .tox .tox-tbtn:disabled:hover svg {
  fill: #c1c5ce;
}
.tox .tox-tbtn--enabled,
.app-marketing-scoped .tox .tox-tbtn--enabled:hover {
  background: #d9eefe;
}
.tox .tox-tbtn:hover {
  background: #d9eefe;
}
.tox .tox-tbtn__select-chevron svg {
  fill: rgba(34, 47, 62, 0.5);
  width: 10px;
  height: 10px;
}
.tox .tox-split-button__chevron svg {
  fill: rgba(34, 47, 62, 0.5);
  width: 10px;
  height: 10px;
}
.tox .tox-tbtn__select-chevron svg {
  fill: rgba(34, 47, 62, 0.5);
  width: 10px;
  height: 10px;
}
.tox .tox-tbtn__select-label {
  font-weight: 300;
  font-size: 13px;
}
.tox .tox-tbtn--bespoke .tox-tbtn__select-label {
  width: 4rem;
}
.tox-tinymce {
  border: 1px solid #e9edf5;
}
.tox .tox-statusbar {
  border-top-color: #e9edf5;
}
.tox .tox-menubar + .tox-toolbar,
.app-marketing-scoped
  .tox
  .tox-menubar
  + .tox-toolbar-overlord
  .tox-toolbar__primary {
  border-top-color: #e9edf5;
}
.tox:not([dir="rtl"]) .tox-toolbar__group:not(:last-of-type) {
  border-right: 1px solid #e9edf5;
}
.tox .tox-toolbar,
.app-marketing-scoped .tox .tox-toolbar__overflow,
.app-marketing-scoped .tox .tox-toolbar__primary {
  background-size: 100% 39px;
  background-image: linear-gradient(0deg, #e9edf5 1px, transparent 0);
}
.marketing-editor-AI-helper-wrapper{
  position: relative;
  }
.marketing-editor-AI-writing-wrapper{
  position: absolute;
  width: calc(100% - 20px);
  line-height: 1.5;
  display: none;
}
.marketing-editor-AI-short-cut-wrapper{
  position: absolute;
  padding: 6px 8px;
  background-color: #fafafa;
  border-radius: 4px;
  box-shadow: 0px 2px 6px 0px rgba(0,0, 0, 0.15);
  display: none;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}
.marketing-editor-AI-short-cut-wrapper span{
  color: var(--Text-H1, #181C25);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  margin-left: 4px;
}
.aigc_toolbar-view-model.view-model-scoped{
  z-index: 1 !important;
}
</style>
