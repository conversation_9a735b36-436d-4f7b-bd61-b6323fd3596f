<template>
  <div class="article-create-page ueditor-wrapper">
    <div class="page-header">
      <div class="page-title">
        {{ title }}
      </div>
      <div class="step-change">
        <Button
          type="primary"
          :loading="isSubmiting"
          @click="submit"
        >
          <i
            class="el-icon-loading"
            :class="!submitLoading ? 'hide-loading' : ''"
          />{{ $t('marketing.commons.qr_e83a25') }}
        </Button>
        <Button @click="preview">
          <i
            class="el-icon-loading"
            :class="!previewLoading ? 'hide-loading' : ''"
          />{{ $t('marketing.commons.yl_645dbc') }}
        </Button>
        <Button @click="cancel">
          {{ $t('marketing.commons.qx_625fb2') }}
        </Button>
      </div>
    </div>
    <div class="page-content">
      <div class="content__preview_wrapper">
        <div class="content__preview">
          <!-- 预览区 -->
          <div class="preview__lable">
            {{ $t('marketing.commons.yl_645dbc') }}
            <span>{{ $t('marketing.pages.article.sjxgjysjyl_a3a100') }}</span>
          </div>
          <div class="preview__box">
            <PreviewArticle
              :article="article"
              :is-public-article="isPublicArticle"
              :form-style-type="isAddForm ? formStyleType : ''"
              :form-data="selectedItem"
              :form-button-name="formButtonName"
              :botton-style="selectedButtonStyle"
            />
          </div>
        </div>
      </div>
      <div class="content__form_wrapper">
        <div class="content__form">
          <div
            v-if="isPublicArticle"
            class="col"
          >
            <span class="col-name"> <em>*</em>{{ $t('marketing.pages.article.ywdz_9883a4') }} </span>
            <div class="w-title col-input">
              <div class="col-input__urlgroup">
                <Input
                  v-model="url"
                  :disabled="isPublicArticle && isEdit"
                  class="title"
                  clearable
                  :placeholder="$t('marketing.commons.qztwzljdz_945700')"
                  @change="handleValidateLink"
                />
                <!-- <Button
                  style="margin-left:10px;"
                  :type="this.url && !this.urlErrMsg ? 'primary' : 'info'"
                  :disabled="this.url && !this.urlErrMsg ? false : true"
                  @click="handlePreviewArticle"
                >预览文章</Button> -->
              </div>
              <p
                v-if="urlErrMsg"
                class="infoTitle"
              >
                {{ urlErrMsg }}
              </p>
              <!-- <div class="url-tip">
                <span>推荐使用微信公众号文章链接</span> -->
              <!-- 文章链接提示 -->
              <!-- <QuestionTooltip title="如何获得微信公众号文章链接地址?" class="export_question" placement="right-start" :offset="-10">
                  <div slot="question-content" class="export_question_content">
                    <div style="display:flex;flex-flow:column nowrap;">
                      <div>在你浏览公众号文章时，点击右上角[...]，在弹出菜单中选择复制链接</div>
                      <div class="link__tips-images"></div>
                    </div>
                  </div>
                </QuestionTooltip>
              </div> -->
            </div>
          </div>
          <div class="col">
            <span class="col-name"> <em>*</em>{{ $t('marketing.commons.wzbt_7526a0') }} </span>
            <div class="w-title col-input">
              <Input
                v-model="titleInput"
                class="title"
                type="text"
                :placeholder="$t('marketing.commons.qsrbt_96641a')"
                @keyup.native="lengthLimit"
                @blur="judgeTitle"
              />
              <span class="title-limit">
                <span>{{ (titleInput || '').length }}</span>
                <span>/{{ titleLimit }}</span>
              </span>
              <p
                v-if="isTitleInvalid"
                class="infoTitle"
              >
                {{ $t('marketing.commons.btbnwk_7bcb6e') }}
              </p>
            </div>
          </div>
          <div
            v-if="isPublicArticle"
            class="col"
            style="align-items: center;"
          >
            <span class="col-name"> <em>*</em>{{ $t('marketing.commons.gzh_215fee') }} </span>
            <div class="w-title col-input">
              <Input
                v-model="source"
                class="title"
                type="text"
                :placeholder="$t('marketing.pages.article.qsrgzh_7068c4')"
                @keyup.native="authorLengthLimit"
                @blur="judgeAuthor"
              />
            </div>
            <span
              class="col-name"
              style="padding-left: 10px;"
            >
              <em>*</em>{{ $t('marketing.commons.wzzz_8209a7') }}
            </span>
            <div class="w-title col-input">
              <Input
                v-model="authorInput"
                class="title"
                type="text"
                :placeholder="$t('marketing.commons.qsrzz_4a22b5')"
                @keyup.native="authorLengthLimit"
                @blur="judgeAuthor"
              />
            </div>
          </div>
          <div
            v-else
            class="col"
          >
            <span class="col-name"> <em>*</em>{{ $t('marketing.commons.wzzz_8209a7') }} </span>
            <div class="w-title col-input">
              <Input
                v-model="authorInput"
                class="title"
                type="text"
                :placeholder="$t('marketing.commons.qsrzz_4a22b5')"
                @keyup.native="authorLengthLimit"
                @blur="judgeAuthor"
              />
              <span class="title-limit">
                <span>{{ authorInput.length }}</span>
                <span>/{{ authorLimit }}</span>
              </span>
              <p
                v-if="isAuthorInvalid"
                class="infoTitle"
              >
                {{ $t('marketing.pages.article.zzbnwk_5fe554') }}
              </p>
            </div>
          </div>
          <div class="col col-richtxt">
            <span class="contentTitle col-name"> <em>*</em>{{ $t('marketing.pages.article.wznr_ad531b') }} </span>
            <div class="col-input">
              <!-- <div id="article-ueditor" v-show="false" class="article-ueditor"></div>
              <div class="editor-uploading-wrapper">
                <ul class="upload_file_box editor-uploading"></ul>
              </div> -->
              <ArticleTinymceEditor
                v-if="showTinymceEditor"
                :height="800"
                :content="articleContent"
                @setEditorContent="setEditorContent"
              />
              <p
                v-if="infoContent"
                class="infoTitle"
              >
                {{ $t('marketing.commons.nrbnwk_9f0dc1') }}
              </p>
            </div>
          </div>
          <div class="col">
            <span class="col-name col-cover"> <em>*</em>{{ $t('marketing.commons.zy_3ae146') }} </span>
            <div class="col-input add-cover">
              <div class="add-cover-btn" />
              <div class="wrapper__cover-summary">
                <div class="col-ta__summary">
                  <textarea
                    v-model="summary"
                    class="summary-text"
                    :placeholder="$t('marketing.commons.qsrzy_f71644')"
                    @input="summaryLengthLimit"
                    @blur="judgeSummary"
                  />
                  <span class="ta-limit">
                    <span>{{ summary.length }}</span>
                    <span>/{{ summaryLimit }}</span>
                  </span>
                  <p
                    v-if="isSummaryInvalid"
                    class="infoTitle"
                  >
                    {{ $t('marketing.commons.zybnwk_e384f9') }}
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div class="col">
            <span class="col-name col-cover"> <em>*</em>{{ $t('marketing.commons.fxyfmsz_c868a9') }}</span>
            <div class="col-input add-cover">
              <div class="add-cover-btn">
                <!-- <Checkbox v-model="isCoverInjected" class="inject-cover">封面图片显示在正文中 </Checkbox> -->
                <div class="tips">
                  {{ $t('marketing.commons.fmjycc_ae76b8') }}
                  <div class="standard">
                    <PictureStandard />
                  </div>
                </div>
              </div>
              <div class="wrapper__cover-summary">
                <div>
                  <!-- <PictureListUpload
                    ref="uploadComp"
                    class="upload-comp"
                    :photolist="coverlist"
                    :max="1"
                    :maxSize="maxSize"
                    :tapathlist.sync="coverTapathlist"
                    :errorCode.sync="coverErrorCode"
                    @update:imgUrls="updateCoverImg"
                    :singleChange="true"
                  ></PictureListUpload> -->
                  <PictureCutter
                    :max-size="1024 * 1024"
                    :cut-size="[{
                      title: $t('marketing.pages.meeting_marketing.hbfm_d8183b') + '（9:5）',
                      desc: $t('marketing.components.PictureSelector.xs_6fa81e'),
                      width: 900,
                      height: 500,
                      photoTargetType: 51,
                    },{
                      title: $t('marketing.commons.xcxfm_94b4f3') + '（5:4）',
                      desc: $t('marketing.components.PictureSelector.yyzfwxxcxy_d66d73'),
                      width: 420,
                      height: 336,
                      photoTargetType: 49,
                    },{
                      title: $t('marketing.commons.pyqfm_ea274c') + '（1:1）',
                      desc: $t('marketing.components.PictureSelector.yyzfwxhpyq_5c11c7'),
                      width: 300,
                      height: 300,
                      photoTargetType: 50,
                    }]"
                    :default-image-url="coverImage"
                    :uploadtip="$t('marketing.commons.fmjyccxszc_c7ea7f')"
                    :need-apath="false"
                    @change="handlePictureCutterChange"
                  />
                  <p
                    v-if="isCoverInvalid"
                    class="infoTitle"
                  >
                    {{ $t('marketing.commons.fmbnwk_04a507') }}
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div class="col">
            <span class="col-name"><em />{{ $t('marketing.commons.fxhb_9c7858') }}</span>
            <div
              class="custom-poster"
              @click="handleCustomPoster"
            >
              <div
                v-if="!sharePosterUrl"
                class="custom-poster__empty"
              >
                <i class="icon km-ico-add" />
                <span class="text">{{ $t('marketing.commons.xztp_ba9fc4') }}</span>
              </div>
              <div
                v-else
                class="custom-poster__empty"
              >
                <img
                  :src="sharePosterUrl"
                  class="img"
                >
              </div>
            </div>
          </div>
          <div class="col">
            <span
              class="col-name"
              style="line-height: inherit;"
            >
              <em />{{ $t('marketing.commons.tjbq_736eaa') }}
            </span>
            <div class="col-input">
              <!-- <Checkbox v-model="isAddTags">给浏览该内容的用户添加标签</Checkbox> -->
              <div>{{ $t('marketing.pages.article.gllgnrdyht_59fc29') }}</div>
              <selector-line
                v-model="tagNameList"
                style="line-height: 26px;margin-bottom: 10px;"
              />
            </div>
          </div>
          <div class="col">
            <span class="col-name"> <em />{{ $t('marketing.pages.article.xgnr_4d9234') }} </span>
            <div class="col-input">
              <div class="w-col-form__header">
                <Checkbox v-model="isAddForm">
                  {{ $t('marketing.pages.article.zwzzglqtxg_5ce086') }}
                </Checkbox>
                <!-- <fx-link v-if="isAddForm" type="standard" @click="createForm" style="padding: 0 8px">
                  <span>+</span>新建表单
                </fx-link> -->
              </div>
            </div>
          </div>
          <div
            v-show="isAddForm"
            class="col"
            style="flex-direction: column;margin-left: 94px;margin-top: -20px;"
          >
            <span
              class="col-name"
              style="line-height: 36px;"
            >
              <em>*</em>{{ $t('marketing.pages.article.zsxs_1e409f') }}
            </span>
            <div class="col-input">
              <el-select
                v-model="formStyleType"
                class="el-select elselect"
                :placeholder="$t('marketing.commons.qxz_708c9d')"
                @change="selectFromStyleHandler"
              >
                <el-option
                  v-for="item in formStyleTypes"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
          </div>
          <div
            v-show="isAddForm && /^(2|3|4)$/.test(formStyleType)"
            class="col"
            style="flex-direction: column;margin-left: 94px;margin-top: -20px;"
          >
            <span
              class="col-name"
              style="line-height: 26px;"
            >
              <em>*</em>{{ $t('marketing.commons.zsmc_879c24') }}
            </span>
            <div class="col-input">
              <div class="button-style-wrapper">
                <Input
                  v-model="formButtonName"
                  type="text"
                  :placeholder="$t('marketing.pages.article.qsrzsmc_0ff754')"
                  :maxlength="formStyleType === 3 ? 4 : 15"
                />
                <button-style
                  v-if="formStyleType === 2 || formStyleType === 4"
                  class="button-style"
                  :button-style="selectedButtonStyle"
                  @selectedButton="selectedButton"
                />
              </div>
              <p
                v-if="isFormButtonNameInvalid"
                class="infoTitle"
              >
                {{ $t('marketing.pages.article.zsmcbnwk_0e04d3') }}
              </p>
            </div>
          </div>
          <div
            v-show="isAddForm"
            class="col"
            style="flex-direction: column;margin-left: 94px;margin-top: -20px;"
          >
            <span
              class="col-name"
              style="line-height: 36px;"
            >
              <em>*</em>{{ $t('marketing.pages.article.jtnr_c4ecf0') }}
            </span>
            <div class="col-input">
              <div class="w-col-form__seachinput">
                <!-- <div>{{selectedItem.formHeadSetting && selectedItem.formHeadSetting.title}}</div> -->
                <PickerInput :placeholder="$t('marketing.pages.article.xzwl_40fda9')"
                :text="selectedItem.title || ''" style="width:100%" @click="formPickerVisible = true" />
                <!-- <div
                  class="searchinput"
                  @click="formPickerVisible = true"
                >
                  <input
                    type="text"
                    :placeholder="$t('marketing.pages.article.xzwl_40fda9')"
                    disabled
                    :value="selectedItem.title"
                  >
                  <div>
                    <span class="el-icon-search" />
                  </div>
                </div> -->
                <p
                  v-if="isFormIdInvalid"
                  class="infoTitle"
                >
                  {{ $t('marketing.pages.article.qxzygwl_382fbf') }}
                </p>
              </div>
            </div>
          </div>
          <div class="col">
            <span class="col-name">{{ $t('marketing.components.Hexagon.sz_de3a05') }}</span>
            <div class="col-input">
              <div class="w-col-form__header">
                <Checkbox :value="ctaConfig.enable" @change="handleCTAEnableChange">
                  {{ $t('marketing.components.Hexagon.kyywysqgzg_fc9e8b') }}<a href="https://help.fxiaoke.com/93d5/0f81/5171" target="_blank" style="margin-left: 10px;">{{
                  $t('marketing.commons.ljgd_06a3c1') }}</a>
                </Checkbox>
                <!-- <fx-link v-if="isAddForm" type="standard" @click="createForm" style="padding: 0 8px">
                  <span>+</span>新建表单
                </fx-link> -->
              </div>
            </div>
          </div>
          <div
            v-show="ctaConfig.enable"
            class="col"
            style="flex-direction: column;margin-left: 94px;margin-top: -20px;"
          >
            <span
              class="col-name"
              style="line-height: 36px;"
            >
              <em>*</em>{{ $t('marketing.pages.product.xz_717a64') }}
            </span>
            <div class="col-input">
              <PickerInput :placeholder="$t('marketing.components.Hexagon.qxzzj_18d4f8')"
            :text="ctaConfig && ctaConfig.ctaName || ''" style="width:100%" @click="handlePickerInputClick" />
            <p
              v-if="isCTAInvalid"
              class="infoTitle"
            >
              {{ $t('marketing.components.Hexagon.qxzzj_18d4f8') }}
            </p>
            </div>
          </div>
          <!-- <div class="col">
            <span class="col-name">
              <em>*</em>摘要
            </span>
            <div class="col-input">
              <textarea
                class="summary-text"
                placeholder="摘要只要在发送消息为单图文时显示"
                @input="summaryLengthLimit"
                v-model="summary"
                @blur="judgeSummary"
              ></textarea>
              <span class="ta-limit">
                <span>{{summary.length}}</span>
                <span>/{{summaryLimit}}</span>
              </span>
              <p class="infoTitle" v-if="isSummaryInvalid">摘要不能为空</p>
            </div>
          </div>-->
          <!-- <div class="col">
            <span class="col-name"><em>*</em>推荐语</span>
            <div class="col-input">
              <textarea class="summary-text" placeholder="请填写推荐语" v-model="recommendation" @input="recommendationLengthLimit" @blur="judgeRecommendation"></textarea>
              <span class="ta-limit"><span>{{recommendation.length}}</span><span>/{{recommendationLimit}}</span></span>
              <p class="infoTitle" v-if="isRecommendationInvalid">作者不能为空</p>
            </div>
          </div>-->
        </div>
      </div>
    </div>
    <SelectMaterialDialog
      v-if="formPickerVisible"
      :tabbar="tabbar"
      :visible="formPickerVisible"
      @onSubmit="handleMaterialSelected"
      @onClose="formPickerVisible = false"
    />
    <Dialog
      :close-on-click-modal="false"
      :visible.sync="showPreviewQrcode"
      class="custom-article-preview-dialog"
      width="400px"
      :title="$t('marketing.commons.yl_645dbc')"
      append-to-body
    >
      <img
        v-if="previewQrcodeUrl"
        :src="previewQrcodeUrl"
      >
      <div
        v-else
        style="width: 100%;height: 140px;display: flex;justify-content: center;align-items: center;"
      >
        <i
          class="el-icon-loading"
          style="font-size: 18px;"
        />
      </div>
      <div class="preview-desc">
        {{ $t('marketing.commons.sysyl_ec3c74') }}
      </div>
    </Dialog>
    <CustomPosterDialog
      :visible="customPosterDialogVisible"
      :default-img="sharePosterUrl"
      @update:visible="customPosterDialogVisible = false"
      @selected="handleCustomPosterSelected"
    />
    <CtaDialog v-if="ctaDialogVisible" :visible.sync="ctaDialogVisible" sceneType="page"
      @confirm="handleCtaDialogConfirm" />
  </div>
</template>

<script>
import _ from 'lodash'
import PreviewArticle from './preview/article.vue'
import http from '@/services/http/index.js'
import xssFilter from '@/services/xss-filter/index.js'
import util from '@/services/util/index.js'
import SelectMaterialDialog from '@/components/select-material-dialog/index.vue'
import { redirectToFS } from '@/utils/index.js'
import SelectorLine from '@/components/tags-selector-new/tags-line.vue'
import appmarketingRPC from '@/utils/appmarketing.rpc.js'
import ArticleTinymceEditor from './ArticleTinymceEditor.vue'
import buttonStyle from '@/components/button-style-picker/index.vue'
import PictureCutter from '@/components/picture-cutter/index.vue'
import PictureStandard from '@/components/PictureStandard/index.vue'
import CustomPosterDialog from '@/components/qrposter-create-dialog/custom-poster-dialog/index.vue'
import { messageBoxConfirm } from '@/utils/message-box.js'
import PickerInput from '@/components/picker-input';
import CtaDialog from '@/pages/cta/components/cta-dialog.vue';
// var parse5 = require('@/utils/parser/build');
const parse5 = require('parse5')

const formStyleTypes = [
  {
    value: 1,
    label: $t('marketing.pages.article.dbppjbd_deba62'),
  },
  {
    value: 2,
    label: $t('marketing.commons.dban_6c6c18'),
  },
  {
    value: 3,
    label: $t('marketing.pages.article.ycxfan_188918'),
  },
  {
    value: 4,
    label: $t('marketing.pages.article.dbxfan_292379'),
  },
]
let realContentNode = null
const permitCodeVersion = '7.6.0'

function parseVersion(version) {
  const versions = version.split('.')
  if (versions.length >= 3) {
    versions.length = 3
    return versions.reduce((pre, cur, index) => pre + Number(cur) * 10 ** (2 - index), 0)
  }
  console.error('版本号格式错误：', version)
  return 0
}

export default {
  components: {
    Input: FxUI.Input,
    Checkbox: FxUI.Checkbox,
    Button: FxUI.Button,
    // VueEditor,
    PreviewArticle,
    Dialog: FxUI.Dialog,
    ElSelect: FxUI.Select.components.ElSelect,
    ElOption: FxUI.Select.components.ElSelect.components.ElOption,
    SelectorLine,
    ArticleTinymceEditor,
    buttonStyle,
    SelectMaterialDialog,
    PictureCutter,
    PictureStandard,
    CustomPosterDialog,
    CtaDialog,
    PickerInput,
  },
  props: {},
  data() {
    return {
      tabbar: [16],
      articleContentCache: '',
      userArticleContentCache: false,
      selectedButtonStyle: {
        buttonColor: '#FCB058',
        buttonBorderColor: '#FCB058',
        fontColor: '#FFFFFF',
      },
      showTinymceEditor: false,
      title:
        this.$route.params.id || this.$route.params.url
          ? $t('marketing.pages.article.bjwz_d0ce13')
          : $t('marketing.commons.xjwz_0af994'),
      titleInput: '',
      authorInput: '',
      isAuthorInvalid: false,
      summary: '',
      summaryLimit: 1000,
      recommendationLimit: 1000,
      titleLimit: 60,
      authorLimit: 60,
      articleContent: '',
      customToolbar: [
        ['bold', 'italic', 'underline'],
        [{ list: 'ordered' }, { list: 'bullet' }],
        [{ background: [] }, { color: [] }],
        [{ align: '' }, { align: 'center' }, { align: 'right' }],
        ['image', 'link', 'code-block'],
        ['strike'],
        ['clean'],
      ],
      isSubmiting: false,
      infoContent: false,
      isTitleInvalid: false,
      isRecommendationInvalid: false,
      isSummaryInvalid: false,
      isCoverInvalid: false,
      isFormIdInvalid: false,
      isCTAInvalid: false,
      isFormButtonNameInvalid: false,
      isCoverInjected: false,
      coverlist: [],
      coverTapathlist: [],
      cover: '',
      coverErrorCode: '',
      iconUrl: '',
      maxSize: 1024 * 1024 * 2,
      showPreviewQrcode: false,
      previewQrcodeUrl: '',
      submitLoading: false,
      previewLoading: false,
      articleId: null,
      previewArticleId: null,
      recommendation: '',
      isAddForm: false,
      formDataList: [],
      loading: false,
      formlistTotalCount: 0,
      selectedItem: {},
      formPickerVisible: false,
      formStyleTypes, // 表单样式类型选项
      formStyleType: 1, // 表单样式类型：1底部平铺，2底部按钮，3悬浮按钮 4底部悬浮按钮
      formButtonName: '', // 表单样式按钮名称
      formButtonNameErrMsg: '',
      formIdErrMsg: '',
      source: '',
      url: '',
      isPublicArticle: false,
      updatePhoto: false,
      urlErrMsg: false,
      isShowMoreTips: false,
      thumbApath: null,
      paInitCoverTApath: null,
      isEdit: false,
      relativeFormData: null,
      realContentNode: null,
      flag: 0,
      tagNameList: [],
      isAddTags: false,
      mediaCompCount: 0, // 记录当前内容存在的媒体组件（目前只有img，后续可能会有video,audio等等）数量
      coverImage: '',
      currentCodeVersion: '',
      sharePosterUrl: '',
      sharePosterAPath: '',
      customPosterDialogVisible: false,
      cutOffsetList: [],
      originalImageAPath: '',
      ctaConfig: {
        enable: false,
        ctaId: '',
        ctaName: '',
      },
      ctaDialogVisible: false,
    }
  },
  computed: {
    article() {
      return {
        title: this.titleInput,
        author: this.authorInput,
        content: this.articleContent,
        cover: this.cover,
      }
    },
    preArticleContent() {
      const date = util.formatDateTime(Date.now(), 'YYYY-MM-DD')
      return `<div id="new-article-header">
                <p class="new-article-title" style="font-size: 22px;line-height: 1.4;text-align: left;letter-spacing: 0.5px;margin-bottom: 14px;font-weight: 400;color: #181C25;">
                  ${this.titleInput}
                </p>
                <p style="font-size: 15px;margin-bottom: 22px;color: #999;min-height:30px" class="article-author">
                  <span style="margin-bottom: 10px">${$t('marketing.commons.zz_62cea7')}</span>
                  <span style="color: #576b95;margin-left: 8px;margin-right: 24px;">${this.authorInput || '--'}</span>
                  <span>${date}</span>
                </p>
              </div>`
    },
    cFormDataList() {
      const resultList = []
      const dl = this.formDataList
      const len = dl.length
      for (let i = 0; i < len; i++) {
        const cpItem = _.clone(dl[i])
        cpItem.title = cpItem.formHeadSetting.title
        cpItem.name = cpItem.formHeadSetting.name
        resultList.push(cpItem)
      }
      return resultList
    },
    customHost(){
      return this.$store.state.Global.configOrigin;
    }
  },
  watch: {
    selectedItem: {
      deep: true,
      handler() {
        this.judgeFormId()
      },
    },
    ctaConfig: {
      deep: true,
      handler() {
        //仅在ctaId有值时，才需要判断
        if (this.ctaConfig.ctaId) {
          this.judgeCTA()
        }
      },
    },
    formButtonName() {
      this.judgeFormButtonName()
    },
    formStyleType(val) {
      if (val == 1) {
        this.tabbar = [16]
      } else {
        // this.tabbar = [10, 16, 4, 1, 3];
        this.tabbar = [10, 16, 4]
      }
    },
    preArticleContent() {
      if (this._startWatchHeaderChange && this.isPublicArticle) {
        this.debouncedUpdateHeader()
      }
    },
  },
  created() {
    // this.queryArticleDetail();
    console.log('I am src/pages/article/edit/index.vue')
  },
  mounted() {
    const atcId = this.$route.params.id
    const patcUrl = this.$route.params.url
    this.isPublicArticle = !!patcUrl || this.$route.params.type === 'pa'
    this.isEdit = !!(atcId || patcUrl)
    this.articleContentCache = localStorage.getItem('edit_articleContent')
    /**
     * 获取小程序版本
     */
    this.queryBoundMiniappInfo()
    if (this.articleContentCache && !this.isPublicArticle) {
      // 自建文章才有这个提示
      // const isShowConfirm = false
      this.userArticleContentCache = false
      FxUI.MessageBox.confirm($t('marketing.pages.article.jcdbdczscw_6087ea'), $t('marketing.commons.ts_02d981'), {
        confirmButtonText: $t('marketing.commons.qd_38cf16'),
        cancelButtonText: $t('marketing.commons.qx_625fb2'),
        type: 'warning',
      })
        .then(() => {
          // this.articleContent = articleContentCache;
          // localStorage.setItem('edit_articleContent', '');
          // this.initUEditor();
          // isShowConfirm = true;
          this.userArticleContentCache = true
          this.initArticleDetail()
          localStorage.setItem('edit_articleContent', '')
        })
        .catch(() => {
          this.initArticleDetail()
          localStorage.setItem('edit_articleContent', '')
        })
      // setTimeout(() => {
      //   // 可能某种情况下导致confirm未执行，导致页面无法初始化
      //   if (!isShowConfirm) {
      //     this.initArticleDetail();
      //   }
      // }, 2000);
    } else {
      this.initArticleDetail()
    }

    this.debouncedUpdateHeader = _.debounce(this.updateHeader, 500)
  },
  destroyed() {
    if (this.editor) {
      this.editor.destroy()
    }
    realContentNode = null
  },
  methods: {
    handleCTAEnableChange(val) {
      this.ctaConfig.enable = val
    },
    handlePickerInputClick() {
      this.ctaDialogVisible = true
    },
    handleCtaDialogConfirm(row) {
      this.ctaConfig.ctaId = row.id
      this.ctaConfig.ctaName = row.name
      this.ctaDialogVisible = false
    },
    handleMaterialSelected(row) {
      // http.getFormDataById({ id: row.id }).then((res) => {
      //   if (!(res && res.errCode == 0)) {
      //     return;
      //   }
      //   this.selectedItem = res.data;
      // });
      this.selectedItem = row
    },
    showMessageWarning() {
      // 直播错误提示
      const errorNode1 = $('.infoTitle')
      if (errorNode1.length) {
        const message = errorNode1[0].innerHTML
        FxUI.Message.error(message)
      }
    },
    selectedButton(btnStyle) {
      const { buttonColor, buttonBorderColor, fontColor } = btnStyle
      this.selectedButtonStyle = {
        buttonColor,
        buttonBorderColor,
        fontColor,
      }
    },
    setEditorContent(content) {
      const originflag = true
      if (!originflag) return
      let setContentFlag = false
      if (content.indexOf('crossorigin="anonymous"') > -1) {
        content = content.replace(/crossorigin="anonymous"/g, '')
        setContentFlag = true
      }
      const mcc = this.countMediaComps(content)
      const cpContentResult = this.filterAndTransCopyContent(content, mcc) // content; //.replace(/&nbsp;/g, "");
      content = cpContentResult || content
      if (setContentFlag || cpContentResult !== this.articleContent) {
        if (
          Math.abs(content.length - this.articleContent.length) >= 20
          && mcc !== this.mediaCompCount
        ) {
          this.articleContent = content
          this.mediaCompCount = mcc
        } else {
          this.articleContent = content
        }
        if (this.flag) {
          this.judgeContent()
        }
        this.flag += 1
      }

      this.syncHeaderInfo()
    },
    selectFromStyleHandler(item) {
      this.formButtonName = ''
      this.selectedItem = []
      console.log('selectFromStyleHandler====>', item)
    },
    // 预览文章
    handlePreviewArticle() {
      redirectToFS(this.url)
    },
    // 检测是否为微信链接
    handleValidateLink(text) {
      const wxLinkReg = /\/\/mp\.weixin\.qq\.com/
      if (!text.trim()) {
        this.urlErrMsg = $t('marketing.commons.wzljdzbnwk_8e879a')
        return
      }
      if (!wxLinkReg.test(text)) {
        this.urlErrMsg = $t('marketing.commons.mqzzcwxgzh_0b016b')
        return
      }
      this.urlErrMsg = ''
      const loadingInst = FxUI.Loading.service({
        fullscreen: true,
        lock: true,
        text: $t('marketing.commons.wzzrz_67ad13'),
      })
      // 初始化公众号文章内容
      this.queryArticleDetail(text).then(() => {
        if (this.editor) {
          this.editor.setContent(xssFilter.filter(this.articleContent))
        }
        loadingInst.close()
      })
      // return true
    },
    createForm() {
      if (this.articleContent) {
        localStorage.setItem('edit_articleContent', this.articleContent)
      }
      // let params = {
      //   from: 'article',
      //   marketingEventId: 0,
      // };
      // this.$router.push({ name: 'form-create', params });
      this.$router.push({
        name: 'form-edit',
        query: {
          from: 'article-editor',
        },
      })
    },
    queryFormList(params) {
      return http.queryFormData(params).then(results => {
        if (results && results.errCode !== 0) {
          return
        }
        const { data } = results
        this.formlistTotalCount = data.totalCount
        this.formDataList = data.result
        if (this.selectedItem && this.selectedItem.id) {
          this.selectedItem = this.formDataList.find(item => item.id === fd.formId)
        }
      })
    },
    formatRichText(articleDetail) {
      let m = null
      if (articleDetail.webPage) {
        m = articleDetail.webPage.match(/<body[\n\s\S]*?>((.|\n)+)<\/body>/)
      }
      if (m) {
        const matches = m[0] ? m[0].match(/<video\s|<iframe class=["|']video_iframe/) : null
        if (matches && matches.length) {
          messageBoxConfirm({
            mainText: $t('marketing.pages.article.cljzknyspn_88a477'),
            boxConfig: {
              showCancelButton: false,
            },
          })
        }
        // console.log('origin content------------>', data.webPage)
        this.articleContent = m
                && m[0].replace(/<script.*>(.|\n)*?<\/script>/g, (
                  match,
                  $1,
                  $2,
                ) => {
                  console.log('match==========>', match)
                  return ''
                })
        // console.log('after repalce script====>', this.articleContent)
        this.articleContent = this.articleContent
          .replace(/<link.*?>/g, () => '')
        // 替换公众号内o:p标签成span标签
          .replace(/<o:p(.*?)>|<(\/)o:p>/g, (o, s1, s2) => `<${s2 || ''}span${s1 || ''}>`)
          .replace(/<img(.+?)>/g, match => {
            if (match.indexOf('data-src=') <= -1) {
              return ''
            }
            // 移除img 标签上的data-height，否则导致小程序的富文本渲染报错
            return match.replace(/\s*\bdata-height=".*?"/, '')
          })
          .replace(
            /(<img.*? )data-src=(['|"].*?['|"])(\s?.*?)(\/?>)/g,
            (match, $1, $2, $3, $4) => {
              console.log(match, $1, $2, $3, 9999)
              if (
                /marketing\/file\/redirectDownload/.test($2)
                      || this.isOwnOrigin($2)
              ) {
                return match
              }
              if (/data\:image\/(gif|png|jpe?g)\;base64\,/.test($2)) {
                // 处理图片base64的编码
                return match
              }
              return `${$1}src=${this.getImgOrigin()}/marketing/file/redirectDownload?url=${encodeURIComponent(
                $2.replace(/\"|\'/g, ''),
              )}${$3}/>`
            },
          )
          .replace(
            /(background-image:\s*url\()(['|"|&quot;].*?['|"|&quot;])\)/g,
            (match, $1, $2, $3, $4) => {
              if (/data\:image\/(gif|png|jpe?g)\;base64\,/.test($2)) {
                // 处理图片base64的编码
                return match
              }
              return `${$1}${this.getImgOrigin()}/marketing/file/redirectDownload?url=${encodeURIComponent(
                $2.replace(/\"|\'|(\&quot;)/g, ''),
              )})`
            },
          )
          .replace(/<iframe.*?vid=(.*?)".*?<\/iframe>/g, (match, $1) => {
            if ($1) {
              return `<iframe class="video-iframe" style="max-width:100%;" width="542" height="304.875" frameborder="0" src="https://v.qq.com/txp/iframe/player.html?autoplay=false&vid=${$1}" allowFullScreen></iframe>`
            }
            return ''
          })
          .replace(/<mp\-miniprogram (.*?)<\/mp\-miniprogram>/g, () => '')
        console.log('=======>', m)
        console.log('after repalce all====>', this.articleContent)
        const document = parse5.parse(this.articleContent)
        console.log('parse5.parse', document)
        this.walkNodes(document.childNodes[0])
        const mainPaNode = realContentNode || document.childNodes[0].childNodes[1]
        this.insertHeader(mainPaNode)
        // Serializes a document.
        const html = parse5.serialize(mainPaNode) // 去掉外层<html>和<body>，直接解析出内容
        // const html = this.articleContent;
        // Serializes the <html> element content.
        // const str = parse5.serialize(document.childNodes[1]);

        console.log('result html======>', html) // > '<head></head><body>Hi there!</body>'
        this.articleContent = `${html}<section class="spec-code-html-result" copy="false" style="visibility: hidden"></section>`
        // .replace(/<div/g, `<p`)
        // .replace(/<\/div/g, `</p`);
        console.log(
          'result html after replace======>',
          this.articleContent,
        )
      }
    },
    updateHeader() {
      const document = parse5.parse(this.articleContent)
      this.walkNodes(document.childNodes[0])
      const mainPaNode = realContentNode || document.childNodes[0].childNodes[1]
      const headerNode = parse5.parse(`${this.preArticleContent}`)

      const getNodeAttrValue = (node, attrName) => {
        const { attrs = [] } = node
        const classItem = attrs.find(item => item.name === attrName)
        if (classItem) {
          return classItem.value
        }

        return ''
      }

      if (
        getNodeAttrValue(mainPaNode.childNodes[0], 'class') === 'new-article-title'
        && getNodeAttrValue(mainPaNode.childNodes[1], 'class') === 'article-author'
      ) {
        mainPaNode.childNodes.splice(0, 2, headerNode.childNodes[0].childNodes[1].childNodes[0])
      } else if (
        getNodeAttrValue(mainPaNode.childNodes[0], 'id') === 'new-article-header'
      ) {
        mainPaNode.childNodes.splice(0, 1, headerNode.childNodes[0].childNodes[1].childNodes[0])
      } else {
        mainPaNode.childNodes.unshift(headerNode.childNodes[0].childNodes[1].childNodes[0])
      }

      const html = parse5.serialize(mainPaNode)
      this.articleContent = html
    },
    syncHeaderInfo() {
      if (!this.isPublicArticle) {
        return
      }

      this._startWatchHeaderChange = false
      const document = parse5.parse(this.articleContent)
      this.walkNodes(document.childNodes[0])
      const mainPaNode = realContentNode || document.childNodes[0].childNodes[1]

      const getNodeAttrValue = (node, attrName) => {
        const { attrs = [] } = node
        const classItem = attrs.find(item => item.name === attrName)
        if (classItem) {
          return classItem.value
        }

        return ''
      }

      if (getNodeAttrValue(mainPaNode.childNodes[0], 'id') === 'new-article-header') {
        const pList = mainPaNode.childNodes[0].childNodes.filter(node => node.nodeName === 'p')
        if (pList.length === 2) {
          const titleInput = pList[0].childNodes[0]?.value?.trim()
          this.titleInput = titleInput

          const spanList = pList[1].childNodes.filter(node => node.nodeName === 'span')
          if (spanList[1]) {
            this.authorInput = spanList[1].childNodes[0]?.value?.trim()
          }
        }
      } else if (
        getNodeAttrValue(mainPaNode.childNodes[0], 'class') === 'new-article-title'
        && getNodeAttrValue(mainPaNode.childNodes[1], 'class') === 'article-author'
      ) {
        this.titleInput = mainPaNode.childNodes[0].childNodes[0]?.value?.trim()

        const spanList = mainPaNode.childNodes[1].childNodes.filter(node => node.nodeName === 'span')
        if (spanList[1]) {
          this.authorInput = spanList[1].childNodes[0]?.value?.trim()
        }
      }

      setTimeout(() => {
        this._startWatchHeaderChange = true
      }, 1000)
    },
    queryArticleDetail(paUrl) {
      const atcId = this.$route.params.id
      const patcUrl = this.$route.params.url || paUrl
      console.log('====>', atcId, patcUrl)

      let httpPromise = null
      if (atcId) {
        httpPromise = http.queryArticleDetail({
          articleId: atcId,
        })
      } else if (patcUrl) {
        httpPromise = http.initWebCrawlerArticle({
          url: patcUrl,
        })
      }

      return (
        httpPromise
        && httpPromise.then(results => {
          if (results && results.errCode === 0) {
            const { data } = results
            this.cover = data.photoUrl
            // this.coverlist = [data.photoUrl];
            this.coverImage = data.photoUrl
            this.authorInput = data.creator
            this.titleInput = data.title
            this.articleContent = data.content || data.webPage
            this.source = data.source
            this.thumbApath = data.thumbApath
            this.paInitCoverTApath = data.photoApath
            this.sharePosterUrl = data.sharePosterUrl
            this.sharePosterAPath = data.sharePosterAPath
            this.tagNameList = data.tagNameList || []
            this.isAddTags = data.tagNameList && data.tagNameList.length > 0
            this.coverTapathlist[0] = data.photoApath || data.photoThumbnailAPath
            this.cutOffsetList = data.cutOffsetList || []
            this.originalImageAPath = data.originalImageAPath || ''
            // CTA配置
            const ctaConfig = data.ctaRelationInfos && data.ctaRelationInfos[0] || {};
            if(ctaConfig.ctaId){
              this.ctaConfig = {
                ...ctaConfig,
                enable: true,
              }
            }

            this.formatRichText(data)

            this.summary = data.summary
            this.articleId = data.id
            this.recommendation = data.recommendation
            this.formStyleType = data.formStyleType
            this.formButtonName = data && data.formButtonName
            if (
              data
              && data.buttonStyle
              && data.buttonStyle.buttonBorderColor
            ) {
              this.selectedButtonStyle = data.buttonStyle
            }
            if (data.bindObjectType === 16) {
              // 表单
              this.selectedItem = {
                objectType: 16,
                id: data.formData.formId,
                title: data.formData.formName,
              }
            } else if (data.bindObjectType === 26) {
              // 微页面
              this.selectedItem = {
                objectType: 26,
                id: data.hexagonSiteData.siteId,
                title: data.hexagonSiteData.siteName,
              }
            } else if (data.bindObjectType === 4) {
              // 微页面
              this.selectedItem = {
                objectType: 4,
                id: data.bindObjectId,
                title: data.bindObjectName || '',
              }
            }
            this.url = data.url
            // const fd = data.formData;
            if (data.formData || data.hexagonSiteData || data.bindObjectId) {
              this.isAddForm = true
              // this.selectedItem = {
              //   id: fd.formId,
              //   formHeadSetting: {
              //     title: fd.formTitle,
              //     name: fd.formName,
              //   },
              // };
              // if (this.formDataList && this.formDataList.length) {
              //   this.selectedItem = this.formDataList.find((item) => {
              //     return item.id === fd.formId;
              //   });
              // }
              // http.getFormDataById({ id: fd.formId }).then((res) => {
              //   if (!(res && res.errCode == 0)) {
              //     return;
              //   }
              //   this.selectedItem = res.data;
              // });
            }
          }
        })
      )
    },
    countMediaComps(content) {
      const matchTotal = content.match(/<img.+?>/g) // 总的图片数量
      // start: 计算自家域名的图片数量
      const matchFXImg = content.match(
        /<img.*?(ceshi113|ceshi112|fxiaoke|fs)\.com.*?>/g,
      )
      let fxImgNum = 0
      if (matchFXImg) {
        matchFXImg.forEach(item => {
          if (!/marketing\/file\/redirectDownload/.test(item)) {
            fxImgNum += 1
          }
        })
      }

      // end: 计算自家域名的图片
      return ((matchTotal && matchTotal.length) || 0) - fxImgNum // 获得需要代理处理的图片数量
    },
    filterAndTransCopyContent(content, mcc) {
      // ** 每次处理完成后，内容会添加一个隐藏尾巴，以防止重复处理导致bug **
      // if (/class=\"spec-code-html-result\"/.test(content) || !content) {
      //   // 若有处理标记，说明已非首次copy内容
      //   realContentNode = null;
      //   return false;
      // }
      if (Math.abs(content.length - this.articleContent.length) < 20) {
        // 字符删减在20个以内，说明无资源内容变更，无需对比，防止频繁对比
        return content
      }
      // let mcc = this.countMediaComps(content);
      if (mcc === this.mediaCompCount) {
        return content
      }
      // this.mediaCompCount = mcc;

      const m = content.match(/<body.*>((.|\n)+)<\/body>/)
      if (m && m[1]) {
        content = m
          && m[1].replace(/<script.*>(.|\n)*?<\/script>/g, (
            match,
            $1,
            $2,
          ) => {
            console.log('match==========>', match)
            return ''
          })
      }
      console.log('after repalce script====>', content)
      content = content
        .replace(/<link.*?>/g, () => '')
        // .replace(/<img(.+?)>/, function(match) {
        //   if (match.indexOf("data-src=") <= -1) {
        //     return "";
        //   }
        //   return match;
        // })
        .replace(
          /(<img.*? )src=(['|"].*?['|"])(\s?.*?)(\/?>)/g,
          (match, $1, $2, $3, $4) => {
            if (
              /marketing\/file\/redirectDownload/.test($2)
              || this.isOwnOrigin($2)
            ) {
              return match
            }
            if (/data\:image\/(gif|png|jpe?g)\;base64\,/.test($2)) {
              // 粘贴的时候可能会有部分图片是base64的编码
              return match
            }
            return `${$1}src=${this.getImgOrigin()}/marketing/file/redirectDownload?url=${encodeURIComponent(
              $2.replace(/\"|\'/g, ''),
            )}${$3}/>`
          },
        )
        .replace(
          /(<img.*? )data-src=(['|"].*?['|"])(\s?.*?)(\/?>)/g,
          (match, $1, $2, $3, $4) => `${$1}src=${this.getImgOrigin()}/marketing/file/redirectDownload?url=${encodeURIComponent(
            $2.replace(/\"|\'/g, ''),
          )}${$3}/>`,
        )
        .replace(
          /(background-image:\s*url\()(['|"|&quot;].*?['|"|&quot;])\)/g,
          (match, $1, $2, $3, $4) => {
            if (
              /marketing\/file\/redirectDownload/.test($2)
              || this.isOwnOrigin($2)
            ) {
              return match
            }
            if (/data\:image\/(gif|png|jpe?g)\;base64\,/.test($2)) {
              return match
            }
            return `${$1}${this.getImgOrigin()}/marketing/file/redirectDownload?url=${encodeURIComponent(
              $2.replace(/\"|\'|(\&quot;)/g, ''),
            )})`
          },
        )
        // .replace(/<iframe (.*?)<\/iframe>/g, () => {
        //   return '';
        // })
        .replace(/<mp\-miniprogram (.*?)<\/mp\-miniprogram>/g, () => '')
      // .replace(/&nbsp;/g, "");
      // this.articleContent = m[1];
      console.log('=======>', m)
      console.log('after repalce all====>', content)
      // const document = parse5.parse(content);
      // this.walkNodes(document.childNodes[0], true);
      // let mainPaNode = realContentNode
      //   ? realContentNode
      //   : document.childNodes[0].childNodes[1];
      // // this.insertHeader(mainPaNode);
      //
      // Serializes a document.
      // const html = parse5.serialize(mainPaNode); // 去掉外层<html>和<body>，直接解析出内容
      // console.log("result html======>", html);
      // content = `${content}<section class="spec-code-html-result" style="visibility: hidden;"></section>`;
      // this.editor.setContent(content);
      return content
    },
    insertHeader(objNode) {
      const headerNode = parse5.parse(`${this.preArticleContent}`)
      objNode.childNodes.unshift(headerNode.childNodes[0].childNodes[1].childNodes[0])
    },
    isOwnOrigin(url) {
      return /(fxiaoke|ceshi113|ceshi112)\.com/.test(url)
    },
    getImgOrigin() {
      const { origin } = window.location
      const fxiaoke = /fxiaoke\.com/.test(origin)
      const ceshi113 = /ceshi113\.com/.test(origin)
      const ceshi112 = /ceshi112\.com/.test(origin)
      if (!fxiaoke && !ceshi113 && !ceshi112 && 0) {
        return 'https://www.ceshi112.com'
      }
      //开启了自定义域名，使用自定义域名
      if(this.customHost){
        return this.customHost;
      }
      return origin
    },
    walkNodes(node, isCopyHtml) {
      // console.log('attrs===>', node.attrs);
      let i = 0
      while (node.childNodes && i < node.childNodes.length) {
        if (
          (!realContentNode || isCopyHtml)
          && this.isRealContentNode(node.childNodes[i])
        ) {
          realContentNode = node.childNodes[i]
        }
        if (this.shouldRemove(node.childNodes[i])) {
          console.log(
            'calc remove===node=== ',
            parse5.serialize(node.childNodes[i]),
          )
          node.childNodes.splice(i, 1)
          continue
        }
        this.walkNodes(node.childNodes[i])
        i += 1
      }
    },
    // getRealContentNode(node) {
    //   let i = 0;
    //   while (node.childNodes && i < node.childNodes.length) {
    //     if (this.isRealContentNode(node.childNodes[i])) {
    //       return node.childNodes[i];
    //     }
    //     return this.getRealContentNode(node.childNodes[i++]);
    //   }
    // },
    isRealContentNode(node) {
      return (
        node.attrs
        && node.attrs.find(item => {
          if (
            item.name === 'class'
            && item.value.indexOf('rich_media_content') > -1
          ) {
            console.log('calc isRealContentNode======', true)
            return true
          }

          return false
        })
      )
    },
    shouldRemove(node) {
      const isBlank = node.nodeName === '#text' && node.value.replace(/\s/g, '') === ''
      // if (isBlank) {
      //   console.log("blank=============>", node);
      // }
      return (
        isBlank
        || (node.attrs
          && node.attrs.find(item => {
            if (
              (item.name === 'style'
                && item.value.indexOf('display:none') > -1)
              || item.value.indexOf('display: none') > -1
              // this.isTitleOrMetaInfo(item)
              || (0
                && item.name === 'class'
                && (/\brich_media_title\b/.test(item.value)
                  || /\brich_media_meta_list\b/.test(item.value)))
            ) {
              console.log('calc remove======', node)
              for (let i = 0; i < node.attrs.length; i += 1) {
                console.log('node attr===>', node.attrs[i].value)
              }
              return true
            } if (item.name === 'data-w') {
              //
              const attr = this.getStyleAttr(node.attrs)
              if (attr) {
                attr.value = `width:${item.value}px;${attr.value}max-width: 100%;height: auto;` // width:前置，防止覆盖原有样式
              }
            }
          }))
      )
    },
    getStyleAttr(attrs) {
      return attrs.find(attr => attr.name === 'style')
    },
    isTitleOrMetaInfo() {},
    // removeHiddenNode(content) {
    //   const ast = parser.parse(content, {
    //     plugins: ['jsx'],
    //   })
    //   console.log('ast =====>', ast)
    // },
    // 对于用户填写的title内容进行判断
    judgeTitle() {
      if (!this.titleInput) {
        this.isTitleInvalid = true
      } else {
        this.isTitleInvalid = false
      }
      return !this.isTitleInvalid
    },
    judgeAuthor() {
      if (!this.authorInput) {
        this.isAuthorInvalid = true
      } else {
        this.isAuthorInvalid = false
      }
      return !this.isAuthorInvalid || this.isPublicArticle
    },
    judgeContent() {
      if (!this.articleContent) {
        this.infoContent = true
      } else {
        this.infoContent = false
      }
      return !this.infoContent
    },
    judgeSummary() {
      if (!this.summary) {
        this.isSummaryInvalid = true
      } else {
        this.isSummaryInvalid = false
      }
      return !this.isSummaryInvalid
    },
    judgeCover() {
      if (!this.coverTapathlist.length) {
        this.isCoverInvalid = true
      } else {
        this.isCoverInvalid = false
      }
      return !this.isCoverInvalid
    },
    judgeCTA() {
      if (this.ctaConfig.enable && !this.ctaConfig.ctaId) {
        this.isCTAInvalid = true
      } else {
        this.isCTAInvalid = false
      }
      return !this.isCTAInvalid
    },
    judgeFormId() {
      if (this.isAddForm && !this.selectedItem.id) {
        this.isFormIdInvalid = true
      } else {
        this.isFormIdInvalid = false
      }
      return !this.isFormIdInvalid
    },
    judgeFormButtonName() {
      if (
        this.isAddForm
        && /^(2|3|4)$/.test(this.formStyleType)
        && (!this.formButtonName || !this.formButtonName.length)
      ) {
        this.isFormButtonNameInvalid = true
      } else {
        this.isFormButtonNameInvalid = false
      }
      return !this.isFormButtonNameInvalid
    },
    lengthLimit() {
      const limit = this.titleLimit
      if (this.titleInput == null) {
        this.titleInput = ''
      }
      this.titleInput = this.titleInput.substring(0, limit)
    },
    authorLengthLimit() {
      const limit = this.authorLimit
      this.authorInput = this.authorInput.substring(0, limit)
    },
    summaryLengthLimit() {
      const limit = this.summaryLimit
      this.summary = this.summary.substring(0, limit)
    },
    recommendationLengthLimit() {
      const limit = this.recommendationLimit
      this.recommendation = this.recommendation.substring(0, limit)
    },
    validate() {
      let isPass = true
      isPass = this.judgeTitle() ? isPass : false
      isPass = this.judgeAuthor() ? isPass : false
      isPass = this.judgeContent() ? isPass : false
      isPass = this.judgeSummary() ? isPass : false
      isPass = this.judgeCover() ? isPass : false
      isPass = this.judgeFormId() ? isPass : false
      isPass = this.judgeFormButtonName() ? isPass : false
      isPass = this.judgeCTA() ? isPass : false
      return isPass
    },
    submit() {
      if (this.submitLoading) {
        return
      }
      // 必填字段提示
      setTimeout(() => {
        this.showMessageWarning()
      })
      if (!this.validate()) {
        return
      }

      if (
        this.selectedItem.objectType === 26
        && this.currentCodeVersion
        && parseVersion(this.currentCodeVersion) < parseVersion(permitCodeVersion)
      ) {
        FxUI.MessageBox.alert(
          $t('marketing.commons.glxcxbbxdy_55b743', { data: { option0: permitCodeVersion } }),
        )
        return
      }

      this.submitLoading = true
      this.batchGetUrlByPath(false).then(resp => {
        let resultContent = this.articleContent
        if (resp && resp.errCode === 0) {
          const { data } = resp
          resultContent = this.replaceImglistInContent(data.urlMap)
        }

        // resultContent = xssFilter.filter(resultContent);
        console.log('xss filter result ==>', resultContent)
        // resultContent = resultContent.replace(/(>)(.*?)(<)/g, function(
        //   match,
        //   $1,
        //   $2,
        //   $3
        // ) {
        //   // console.log('===>', $2);
        //   return `${$1}${$2.replace(/ /g, "&nbsp;")}${$3}`;
        // });

        const payload = {
          content: `${resultContent}`,
          coverTapath: this.coverTapathlist[0],
          cutOffsetList: this.cutOffsetList,
          originalImageAPath: this.originalImageAPath,
          creator: this.authorInput,
          summary: this.summary,
          title: this.titleInput,
          preArticleContent: this.preArticleContent,
          recommendation: this.recommendation,
          updateContent: true,
        }
        console.log('2222', payload.coverTapath)
        if (this.articleId) {
          payload.id = this.articleId
        }
        if (this.selectedItem.id && this.isAddForm) {
          if (this.selectedItem.objectType === 16) {
            payload.formId = this.selectedItem.id
            payload.bindObjectType = this.selectedItem.objectType
          } else if (this.selectedItem.objectType === 26) {
            payload.siteId = this.selectedItem.id
            payload.bindObjectType = this.selectedItem.objectType
          } else {
            payload.bindObjectId = this.selectedItem.id
            payload.bindObjectType = this.selectedItem.objectType
            payload.bindObjectName = this.selectedItem.title
          }
        }
        if (payload.formId || payload.siteId || payload.bindObjectId) {
          payload.formStyleType = this.formStyleType
          if (/^(2|3|4)$/.test(this.formStyleType)) {
            payload.formButtonName = this.formButtonName
            payload.buttonStyle = this.selectedButtonStyle
          }
        }
        if (this.isPublicArticle) {
          payload.articleType = 2
          payload.source = this.source
          payload.sourceType = 1
          payload.updatePhoto = this.updatePhoto
          payload.url = this.url
        }

        if (this.sharePosterUrl && this.sharePosterAPath) {
          payload.sharePosterUrl = this.sharePosterUrl
          payload.sharePosterAPath = this.sharePosterAPath
        }

        if (
          this.paInitCoverTApath
          && payload.coverTapath.indexOf('http') === 0
        ) {
          // 如果用户未更改图片且公众号封面Apath存在，默认使用该封面Apath
          payload.coverTapath = this.paInitCoverTApath
        }
        if (this.thumbApath) {
          payload.thumbApath = this.thumbApath
        }
        if (true) {
          payload.tagNameList = this.tagNameList
        }

        const { groupId } = this.$route.query
        if (groupId) {
          payload.groupId = groupId
        }
        if (this.ctaConfig.enable && this.ctaConfig.ctaId) {
          payload.ctaIds = [this.ctaConfig.ctaId]
        } else {
          payload.ctaIds = []
        }

        const httpMethod = this.isPublicArticle
          ? http.addWebCrawlerArticle
          : http.addCustomArticles
        httpMethod(payload).then(
          results => {
            if (results && results.errCode === 0) {
              FxUI.Message.success(
                this.articleId ? $t('marketing.commons.wzbjcg_e5be7a') : $t('marketing.commons.wzcjcg_c3775e'),
              )
              // this.$router.replace({ name: "article" });
              if (this.$route.query.from === 'dialog') {
                // 从素材选择弹窗点击新建过来的
                if (this.$route.query.fromid) {
                  appmarketingRPC.set(this.$route.query.fromid, {
                    type: 1,
                    id: results.data.id,
                  })
                }
                window.close()
              } else {
                const { from } = this.$route.query
                const { id, url } = this.$route.params
                this.$router.replace({
                  name: 'materiel-create-success',
                  params: {
                    materielType: 'article',
                    materielId: results.data.id,
                    materielSuccessType: (id || url) ? 'materielEdit' : 'materielCreate',
                  },
                  ...((from && { query: { from } }) || {}),
                })
              }
            }
            this.submitLoading = false
          },
          () => {
            this.submitLoading = false
          },
        )
      })
    },
    // 获取Apath
    batchGetUrlByPath(isGetTempUrl) {
      const tapathList = this.getTapathListFromContent()
      console.log('tapathlist====>', tapathList)
      if (tapathList.length) {
        return http.batchGetUrlByPath({
          needApath: !isGetTempUrl,
          needPermanent: !isGetTempUrl,
          pathList: tapathList,
        })
      }
      return new Promise(resolve => {
        resolve(null)
      })
    },
    getTapathListFromContent() {
      const tapathlist = []
      const ac = this.articleContent
      const TAPathREG = /ta_path="(.*?)"/g
      const match = ac.match(TAPathREG)
      if (!match || !match.length) {
        return []
      }
      match.forEach(item => {
        if (!item) {
          return
        }
        const m = item.match(/"(TA_.+)"/)
        if (m && m[1]) {
          tapathlist.push(m[1])
        }
      })
      return tapathlist
    },
    replaceImglistInContent(urlMap) {
      return this.articleContent.replace(
        /(<img.*?src=")(.*?)(".*?>)/g,
        (match, $1, $2, $3) => {
          const m = $3.match(/ta_path="(.*?)"/)
          return `${$1}${m && m[1] ? (urlMap[m && m[1]] || {}).url : $2}${$3}`
        },
      )
    },
    preview() {
      if (this.previewLoading) {
        return
      }
      if (!this.validate()) {
        return
      }
      this.previewLoading = true
      this.showPreviewQrcode = true
      this.batchGetUrlByPath(true).then(results => {
        let resultContent = this.articleContent
        if (results && results.errCode === 0) {
          const { data } = results
          resultContent = this.replaceImglistInContent(data.urlMap)
        }
        // resultContent = xssFilter.filter(resultContent);
        // resultContent = `${this.preArticleContent}${resultContent}`;
        const payload = {
          content: resultContent,
          coverTapath: this.coverTapathlist[0],
          cutOffsetList: this.cutOffsetList,
          originalImageAPath: this.originalImageAPath,
          creator: this.authorInput,
          summary: this.summary,
          title: this.titleInput,
          updateContent: true,
        }
        if (this.selectedItem.id && this.isAddForm) {
          payload.formId = this.selectedItem.id
        }
        if (payload.formId) {
          payload.formStyleType = this.formStyleType
          if (/^(2|3|4)$/.test(this.formStyleType)) {
            payload.formButtonName = this.formButtonName
            payload.buttonStyle = this.selectedButtonStyle
          }
        }
        if (this.previewArticleId) {
          payload.previewArticleId = this.previewArticleId
        }
        if (this.isPublicArticle) {
          payload.articleType = 2
          payload.source = this.source
          payload.sourceType = 1
          payload.updatePhoto = this.updatePhoto
          payload.url = this.url
        }
        if (
          this.paInitCoverTApath
          && payload.coverTapath.indexOf('http') === 0
        ) {
          // 如果用户未更改图片且公众号封面Apath存在，默认使用该封面Apath
          payload.coverTapath = this.paInitCoverTApath
        }
        // if (this.formStyleType != 1) {
        //   delete payload.formId;
        // }
        http.previewArticle(payload).then(
          resp => {
            if (resp && resp.errCode === 0) {
              const { qrCodeUrl, previewArticleId } = resp.data
              this.previewArticleId = previewArticleId
              this.previewQrcodeUrl = qrCodeUrl
              this.previewLoading = false
            }
          },
          () => {
            this.previewLoading = false
          },
        )
      })
    },
    cancel() {
      FxUI.MessageBox.confirm($t('marketing.commons.qxjhdqwbcd_4dfdd2'), $t('marketing.commons.ts_02d981'), {
        confirmButtonText: $t('marketing.commons.qd_38cf16'),
        cancelButtonText: $t('marketing.commons.qx_625fb2'),
        type: 'warning',
      }).then(() => {
        // 从素材弹窗进入创建直接关闭窗口
        if (this.$route.query.from === 'dialog') {
          window.close()
        } else {
          this.$router.back()
        }
      })
    },
    updateCoverImg(logo) {
      console.log('====>', logo, this.coverTapathlist)
      this.updateCover = true
      this.judgeCover()
      this.cover = logo
    },
    initUEditor() {
      this.showTinymceEditor = true
      if (this.userArticleContentCache) {
        this.articleContent = this.articleContentCache
      }
      // requireAsync(['base-ueditor', 'base-ajaxform'], (BaseUeditor) => {
      //   if (!this.editor) {
      //     this.editor = new Ueditor(
      //       'article-ueditor',
      //       'article-ueditor',
      //       {
      //         ready: () => {
      //           this.editor.pinToolbar({
      //             element: 'article-ueditor',
      //             deltaWrapperTop: 72,
      //           });
      //           setTimeout(() => {
      //             let content = this.articleContent;
      //             if (!this.isPublicArticle) {
      //               content = xssFilter.filter(this.articleContent);
      //             }
      //             // content = content.replace(/&nbsp;/g, "");

      //             console.log('articleContent before set======>', content);
      //             // this.removeHiddenNode(content);
      //             this.editor.setContent(content);
      //             console.log('articleContent ======>', content);
      //             console.log('this.articleContent ======>', this.articleContent);
      //           }, 300);
      //         },
      //         change: (content, originflag = true) => {
      //           /*
      //            *   前方有坑：
      //            *   Firefox浏览器的ueditor输入中文字符时，不能触发contentChange事件，输入ASCII字符时可以
      //            *   导致doing()提交数据时me.data获取不到输入的中文字符
      //            */
      //           // me.update_data('content', content);
      //           // if ($('#layer_editor', me.element).closest('tr').css('display') != 'none' && !initialFlag) { //确保是编辑正文
      //           //     // me.validContent(content);
      //           // }
      //           if (!originflag) return;
      //           let setContentFlag = false;
      //           if (content.indexOf('crossorigin="anonymous"') > -1) {
      //             content = content.replace(/crossorigin="anonymous"/g, '');
      //             setContentFlag = true;
      //             // this.editor.setContent(content, false);
      //           }
      //           let mcc = this.countMediaComps(content);
      //           let cpContentResult = this.filterAndTransCopyContent(content, mcc); //content; //.replace(/&nbsp;/g, "");
      //           content = cpContentResult || content;
      //           if (setContentFlag || cpContentResult != this.articleContent) {
      //             if (Math.abs(content.length - this.articleContent.length) >= 20 && mcc != this.mediaCompCount) {
      //               this.articleContent = content;
      //               this.mediaCompCount = mcc;
      //               // *****很重要：纯文字更新如果setContent会一直抖动，造成输入很大困扰，因为依据常理来判断非资源内容更新幅度会在20个字符以内，因此直接赋值即可，不需要比较并更新内容
      //               this.editor.setContent(content, false);
      //             } else {
      //               this.articleContent = content;
      //             }
      //             if (this.flag) {
      //               this.judgeContent();
      //             }
      //             this.flag++;
      //           }
      //         },
      //         deltaWrapperTop: 72,
      //       },
      //       BaseUeditor,
      //     );
      //   } else {
      //     this.editor.setContent(this.articleContent);
      //   }
      // });
    },
    initUEditor2() {
      const content = xssFilter.filter(this.articleContent)
      this.articleContent = content
    },
    // 初始化文章内容以及编辑器
    initArticleDetail() {
      const atcId = this.$route.params.id
      const patcUrl = this.$route.params.url
      this.isPublicArticle = !!patcUrl || this.$route.params.type === 'pa'
      this.isEdit = !!(atcId || patcUrl)
      if (patcUrl || atcId) {
        const loadingInst = FxUI.Loading.service({
          fullscreen: true,
          lock: true,
          text: $t('marketing.commons.wzzrz_67ad13'),
        })
        this.queryArticleDetail()
          .then(() => {
            if (atcId) {
              this.initUEditor()
            } else {
              // this.articleContent = xssFilter.filter(this.articleContent);
              this.initUEditor()
            }

            this._startWatchHeaderChange = true
            loadingInst.close()
          })
          .catch(() => {
            if (atcId) {
              this.initUEditor()
            } else {
              this.articleContent = xssFilter.filter(this.articleContent)
            }
            loadingInst.close()
          })
      } else {
        this.initUEditor()
      }
    },
    showImage(params) {
      this.coverImage = params.modelurl
      this.coverTapathlist[0] = params.tapath
      this.updatePhoto = true
    },
    handlePictureCutterChange(file) {
      this.coverImage = file.cutOffsetList[0].image
      this.coverTapathlist[0] = file.cropApath
      this.originalImageAPath = file.photoPath || file.path
      this.thumbApath = file.photoPath || file.path
      this.cutOffsetList = file.cutOffsetList
      this.updatePhoto = true
    },
    handleCustomPoster() {
      this.customPosterDialogVisible = true
    },
    handleCustomPosterSelected(data) {
      const { path, url } = data || {}
      this.sharePosterUrl = url || ''
      this.sharePosterAPath = path || ''
    },
    queryBoundMiniappInfo() {
      http
        .getBoundMiniappInfo({
          platformId: 'YXT',
        })
        .then(({ errCode, data }) => {
          if (errCode === 0) {
            this.currentCodeVersion = data.currentCodeVersion
          }
        })
    },
  },
}
</script>

<style lang="less">
#edui_fixedlayer {
  z-index: 10000 !important;
}
.article-create-page {
  * {
    max-width: 100% !important;
  }
  img {
    height: auto !important;
  }
  .km-pictureListUploader .box {
    width: 200px;
    height: 111px;
  }
  .page-header {
    flex: 0 0 auto;
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    position: absolute;
    padding: 0 30px;
    height: 72px;
    background: #f4f6f9;
    box-shadow: 0 2px 2px #ddd;
    width: 100%;
    top: 0;
    box-sizing: border-box;
    z-index: 100;
    .page-title {
      margin-right: 60px;
      font-size: 28px;
      color: #212b36;
    }
    .step-change {
      flex: 1 1 auto;
      display: flex;
      flex-flow: row nowrap;
      align-items: center;
      justify-content: flex-end;
      .hide-loading {
        display: none;
      }
    }
  }
  .page-content {
    flex: 1 1 auto;
    // height: 100%;
    // overflow: auto;
    display: flex;
    // min-width: ~'calc(675px + 325px)';
    box-sizing: border-box;
    overflow-x: auto;
    margin-top: 72px;
    .content__preview_wrapper {
      flex: 0 0 322px;
      display: flex;
      justify-content: center;
    }
    .content__preview {
      .preview__lable {
        font-size: 18px;
        margin-left: 16px;
        margin-bottom: 15px;
      }
      padding: 19px 31px;
      // width: 325px;
      box-sizing: border-box;
      min-height: 100%;
      position: fixed;
      top: -66px;
      scale: 0.7;
    }
    .content__form_wrapper {
      flex: 1;
      border-left: 1px solid #ddd;
      #article-ueditor {
        position: relative;
        border: 1px solid #dedede;
        .edui-editor-toolbarbox {
          // position: absolute !important;
          width: 100%;
        }
        .edui-editor-iframeholder {
          margin-top: 32px;
        }
      }
    }
    .content__form {
      // width: ~'calc(100% - 325px)';
      // border-left: 1px solid #ddd;
      min-height: ~"calc(100vh - 80px)";
      // overflow: hidden;
      &::after {
        clear: both;
      }
      .particle-link__tips {
        // border-top: 1px dashed #eee;
        display: flex;
        flex-flow: column nowrap;
        font-size: 12px;
        color: #999;
        & > div {
          // margin-bottom: 20px;
        }
        .link__tips-more {
          // margin: 10px 0;
          font-size: 12px;
          color: var(--color-info06,#407FFF);
          cursor: pointer;
          margin-left: 17px;
        }
        .togggle-arrow {
          display: inline-block;
          margin-left: 6px;
          width: 12px;
          height: 8px;
          background: url("../../../assets/images/rectangle0.png") center
            no-repeat;
          background-size: cover;
          transition: transform 0.5s;
        }
        .togggle-arrow.active {
          transform: rotate(-180deg);
        }
        .link__tips-images {
          margin: 16px 20px 0;
          height: 200px;
          background: url("../../../assets/images/url-tip0.png") left bottom
              no-repeat,
            url("../../../assets/images/url-tip1.png") right bottom no-repeat;
        }
      }
    }
  }
  .content__form {
    * {
      box-sizing: border-box;
    }
    .col {
      display: flex;
      margin-bottom: 20px;
      // max-width: 900px;
      > .col-input {
        flex: 1;
        position: relative;
        .button-style-wrapper {
          display: flex;
          .button-style {
            margin-left: 20px;
          }
        }
        .elselect {
          width: 100%;
        }
        .col-input__urlgroup {
          display: flex;
          align-items: center;
          .el-input__inner {
            // flex: 1 0 auto;
          }
        }
        .ta-limit {
          position: absolute;
          right: 10px;
          bottom: 0;
        }
        &.add-cover {
          display: flex;
          flex-direction: column;
          // align-items: center;
          .wrapper__cover-summary {
            display: flex;
            .col-ta__summary {
              flex: 1 0 auto;
              position: relative;
              height: 111px;
            }
          }
        }
        .add-cover-btn {
          font-size: 13px;
          color: var(--color-primary06,#407FFF);
          margin-bottom: 12px;
          .tips {
            color: #999;
            line-height: 26px;
            display: flex;
            .standard{
              margin-left: 5px;
            }
          }
        }
        .summary-text {
          padding: 12px;
          height: 111px;
          border: 1px solid #ddd;
          width: 100%;
        }
        .w-col-form__header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          .el-checkbox__label {
            line-height: 40px;
          }
        }
        .form-select {
          width: 100%;
        }
        .w-col-form__seachinput {
          .searchinput {
            width: 100%;
            height: 36px;
            display: flex;
            border: 1px solid #e9edf5;
            box-sizing: border-box;
            border-radius: 3px;
            cursor: pointer;
            input {
              flex: 1 0 auto;
              padding: 7px 0 7px 10px;
              border-color: transparent;
              cursor: pointer;
            }
            > div {
              &:first-child {
              }
              &:last-child {
                width: 36px;
                height: 36px;
                border-left: 1px solid #e9edf5;
                text-align: center;
                line-height: 36px;
                > .el-icon-search {
                  font-size: 16px;
                  color: #b4b6c0;
                  font-weight: 600;
                }
              }
            }
          }
        }
      }
      .col-name {
        line-height: 40px;
        width: 108px;
        flex-shrink: 0;
        &.col-cover {
          line-height: 26px;
        }
        em {
          color: #ee6723;
          margin-right: 5px;
        }
      }
      .url-tip {
        font-size: 12px;
        color: #999;
        display: flex;
        flex-direction: row;
      }
      .custom-poster {
        width: 200px;
        height: 110px;
        font-size: 14px;
      }

      .custom-poster__empty {
        width: 100%;
        height: 100%;
        flex-direction: column;
        border: 1px solid #e9edf5;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        border-radius: 4px;
        box-sizing: border-box;

        & > .icon {
          width: 16px;
          height: 16px;
        }

        & > .text {
          font-size: 14px;
          line-height: 20px;
          color: #91959e;
          margin-top: 5px;
        }

        & > .img {
          object-fit: contain;
          width: 100%;
          height: 100% !important;
        }
      }
    }
    // margin: auto;
    padding: 20px;
    min-width: 400px;
    max-width: 900px;
    > span {
      display: block;
      margin-bottom: 10px;
      font-size: 14px;
      color: #606266;
    }
    .infoTitle {
      font-size: 11px;
      color: red;
      margin-top: 3px;
    }
    .w-title {
      position: relative;
      .title-limit {
        position: absolute;
        right: 10px;
        top: 10px;
      }
      input {
        padding: 0 50px 0 10px;
        &.title {
          // margin-left: 500px;
          width: 100%;
          height: 30px;
          border: 1px solid #bfbfbf;
          padding: 0 50px 0 10px;
        }
      }
    }
    .noticeContent {
      padding: 6px 10px;
      border: 1px solid #bfbfbf;
      // margin-left: 500px;
      width: 100%;
      height: 300px;
    }
  }
  .article-editor {
    height: 450px;
    width: 700px;
  }
}
.custom-article-preview-dialog {
  display: flex;
  justify-content: center;
  align-items: center;
  .el-dialog__header {
    border-bottom: 1px solid #ddd;
  }
  .el-dialog__body {
    text-align: center;
    .preview-desc {
      margin-top: 12px;
      color: #333;
    }
  }
  img {
    width: 140px;
    height: 140px;
  }
}
.export_question {
  display: inline-block;
  margin-left: 17px;
  vertical-align: -4px;
  color: var(--color-primary06,#407FFF);
  font-size: 12px;
  cursor: pointer;
}
.export_question_content {
  color: #666;
  padding: 5px 0;
  line-height: 23px;
  .link__tips-more {
    // margin: 10px 0;
    font-size: 12px;
    color: var(--color-info06,#407FFF);
    cursor: pointer;
    margin-left: 17px;
  }
  .togggle-arrow {
    display: inline-block;
    margin-left: 6px;
    width: 12px;
    height: 8px;
    background: url("../../../assets/images/rectangle0.png") center no-repeat;
    background-size: cover;
    transition: transform 0.5s;
  }
  .togggle-arrow.active {
    transform: rotate(-180deg);
  }
  .link__tips-images {
    margin: 16px 20px 0;
    height: 200px;
    background: url("../../../assets/images/url-tip0.png") left bottom no-repeat,
      url("../../../assets/images/url-tip1.png") right bottom no-repeat;
  }
  > .title {
    color: #333;
  }
}
</style>
