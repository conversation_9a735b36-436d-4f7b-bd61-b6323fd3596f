<template>
  <div class="MeetingInvitationContent" v-loading="!marketingEventId">
    <div class="MeetingInvitationContent__detail" v-if="false">
      <MeetingInvitationContentDetail></MeetingInvitationContentDetail>
    </div>
    <div class="MeetingInvitationContent__content">
      <SpreadContentList
        ref="spreadContentList"
        :id="marketingEventId"
        :marketingEventName="marketingEventName"
        :applyObject="{
          tips: $t('marketing.pages.meeting_marketing.gxzcnrzbhd_2e706b'),
        }"
        :lifeStatus="lifeStatus"
        :materialLabel="$t('marketing.commons.hy_ebcb81')"
        :showTemplate="false"
      ></SpreadContentList>
      <MarketingActivityQrposter
        v-if="marketingEventId"
        :lifeStatus="lifeStatus"
        scene="meeting"
        size="small"
        :resetPageSize="6"
        :marketingEventId="marketingEventId"
        class="poster"
        :title="$t('marketing.commons.qbhb_621d02')"
        :showMobileDisplayTag="true"
        :materialLabel="$t('marketing.commons.hy_ebcb81')"
      ></MarketingActivityQrposter>
    </div>
  </div>
</template>

<script>
import MeetingInvitationContentDetail from './MeetingInvitationContentDetail';
import MeetingInvitationContentContent from './MeetingInvitationContentContent';
import MarketingActivityQrposter from '@/components/MarketingActivityQrposter';
import SpreadContentList from '@/pages/content-marketing/content-dashboard/module/spread-content-list.vue';
export default {
  components: {
    MeetingInvitationContentDetail,
    MeetingInvitationContentContent,
    MarketingActivityQrposter,
    SpreadContentList,
  },
  computed: {
    marketingEventDetail() {
      return this.$store.state.MeetingMarketing.marketingEventDetail;
    },
    lifeStatus() {
      const _detail = this.$store.state.MeetingMarketing.marketingEventDetail;
      if(_detail && _detail.marketingEvent) {
        return _detail.marketingEvent.lifeStatus || ''
      }
      return ''
    },
    marketingEventId() {
      return (
        (this.marketingEventDetail &&
          this.marketingEventDetail.marketingEvent &&
          this.marketingEventDetail.marketingEvent.id) ||
        ''
      );
    },
    marketingEventName() {
      return (
        (this.marketingEventDetail &&
          this.marketingEventDetail.marketingEvent &&
          this.marketingEventDetail.marketingEvent.name) ||
        ''
      );
    },
  },
};
</script>

<style lang="less" scoped>
.MeetingInvitationContent {
  width: 100%;
  .MeetingInvitationContent__content {
    display: flex;
    justify-content: space-between;
    position: relative;
    .content {
      width: calc(100% - 376px);
    }
    .poster {
      width: 386px;
      margin-left: 12px;
      border-radius: 8px;
    }
  }
}
</style>
