<template>
    <div class="invitation-content__qrcode-wrapper">
      <div class="header"></div>
      <div class="content">
        <div class="content__title">{{ $t('marketing.commons.mtggtf_120c9a') }}</div>
        <div class="content__card">
          <div class="card__img">
            <img :src="img1" />
          </div>
          <div class="card__info">
            <div class="info__title">{{ $t('marketing.commons.bmbd_112a9c') }}</div>
            <div class="info__desc">{{ $t('marketing.commons.xzbmbdewmh_8f6d7b') }}</div>
          </div>
          <div class="card__btn">
            <template v-if="!conferenceDetail.formId">
              <el-link type="standard" :underline="false" @click="handleGoEnrollSetting()">{{ $t('marketing.commons.hmybmbdqws_bc260e') }}</el-link>
            </template>
            <template v-else-if="conferenceDetail.flowStatus <= 1">
              <el-link type="standard" :underline="false" @click="handleGoMeetingInfomation()">{{ $t('marketing.commons.hmyfbhyqwf_a09f61') }}</el-link>
            </template>
            <template v-else>
              <el-link type="standard" :underline="false" @click="handleDownload('form')">{{ $t('marketing.commons.xzewm_feea92') }}</el-link>
              <el-link type="standard" :underline="false" @click="handleCopyLink('form')">{{ $t('marketing.commons.fzlj_879058') }}</el-link>
              <el-link type="standard" :underline="false" @click="handleGetIframe('form')">{{ $t('marketing.commons.qrwy_a71b00') }}</el-link>
            </template>
          </div>
        </div>
        <div class="content__title">{{ $t('marketing.commons.qdtf_3259e6') }}</div>
        <div class="content__card">
          <div class="card__img">
            <img :src="img2" />
          </div>
          <div class="card__info">
            <div class="info__title">{{ $t('marketing.commons.hyzy_7962bd') }}</div>
            <div class="info__desc">{{ $t('marketing.commons.xzhyzyewmh_d3f076') }}</div>
          </div>
          <div class="card__btn">
            <template v-if="!conferenceDetail.formId">
              <el-link type="standard" :underline="false" @click="handleGoEnrollSetting()">{{ $t('marketing.commons.hmybmbdqws_bc260e') }}</el-link>
            </template>
            <template v-else-if="conferenceDetail.flowStatus <= 1">
              <el-link type="standard" :underline="false" @click="handleGoMeetingInfomation()">{{ $t('marketing.commons.hmyfbhyqwf_a09f61') }}</el-link>
            </template>
            <template v-else>
              <el-link type="standard" :underline="false" @click="handleDownload('detail')">{{ $t('marketing.commons.xzewm_feea92') }}</el-link>
              <el-link type="standard" :underline="false" @click="handleCopyLink('detail')">{{ $t('marketing.commons.fzlj_879058') }}</el-link>
            </template>
          </div>
        </div>
      </div>
      <qrcode-download-dialog
        :visible="isShowDownloadDialog"
        @update:visible="handleDownloadDialogVisible"
        :qrCode="curQrCode"
        :qrType="qrType"
      ></qrcode-download-dialog>
      <qrcode-copylink-dialog
        :visible="isShowCopylinkDialog"
        @update:visible="handleCopylinkDialogVisible"
        :title="  copyType === 'link' ? $t('marketing.commons.fzlj_879058') : $t('marketing.commons.qrwz_cc99e3')"
        :copytext="copyType === 'link'? getH5Url : getIframe"
      ></qrcode-copylink-dialog>
    </div>
</template>
<script>
import http from '@/services/http/index';
import { confirm, alert } from '@/utils/globals';
import { getShortUrl } from '@/utils/index';
import QrcodeDownloadDialog from './qrcode-download-dialog';
import QrcodeCopylinkDialog from './qrcode-copylink-dialog';
import { createMaterialDetailUrlByObjectType } from "@/utils/createMaterialDetailUrl";
import img1 from '@/assets/images/icons/meeting-qrcode-form.jpg';
import img2 from '@/assets/images/icons/meeting-qrcode-detail.jpg';

export default {
  components: {
ElLink: FxUI.link,
QrcodeDownloadDialog,
QrcodeCopylinkDialog
},
  data() {
    return {
      img1,
      img2,
      isShowDownloadDialog: false,
      isShowCopylinkDialog: false,

      qrType: 'form', // 二维码类型： form-表单二维码 detail-会议详情页二维码
      copyType: '', // 复制类型： link-复制链接 iframe-复制iframe

      FormQrCode: {}, // 报名表单页二维码对象
      DetailQrCode: {}, // 会议详情页二维码对象

      H5Url_form_async: '', // 会议表单页的h5链接，需要转换成短链，所以加async
      H5Url_detail_async: '', // 会议详情页的h5链接，需要转换成短链，所以加async
    };
  },
  props: {},
  computed: {
    conferenceDetail() {
      console.log(
        'this.$store.state.MeetingMarketing.conferenceDetail',
        this.$store.state.MeetingMarketing.conferenceDetail,
      );
      return this.$store.state.MeetingMarketing.conferenceDetail;
      // conferenceDetail.status 0: '未发布', 1: '未发布', 2: '未开始', 3: '进行中', 4: '已结束', 5: '已停用',
    },
    marketingEventId() {
      return this.conferenceDetail.marketingEventId;
    },
    marketingEventName() {
      return this.conferenceDetail.marketingEventDetail && this.conferenceDetail.marketingEventDetail.title;
    },
    meetingId() {
      return this.$store.state.MeetingMarketing.conferenceDetail.id;
    },
    meetingTitle() {
      return this.$store.state.MeetingMarketing.conferenceDetail.title;
    },
    curQrCode() {
      return this.qrType === 'form' ? this.FormQrCode : this.DetailQrCode;
    },
    getH5Url() {
      return this.qrType === 'form' ? this.H5Url_form_async : this.H5Url_detail_async;
    },
    getIframe() {
      return `<iframe frameborder="0" src="${this.getH5Url}" allowFullScreen="true"></iframe>`;
    },
  },
  methods: {
    // 生成报名表单二维码
    async queryConferenceFormQrCode() {
      if (!this.meetingId || !this.conferenceDetail.formId) {
        return;
      }
      http.queryConferenceFormQrCode({ id: this.meetingId }).then((results) => {
        if (results && results.errCode == 0) {
          this.FormQrCode = results.data;
        }
        console.log(results);
      });
    },
    // 生成会议详情页二维码
    async queryConferenceQrCode() {
      if (!this.meetingId) {
        return;
      }
      http.queryConferenceQrCode({ id: this.meetingId }).then((results) => {
        if (results && results.errCode == 0) {
          this.DetailQrCode = results.data;
        }
      });
    },
    async getH5ShortUrl() {
      // let form_params = `formId=${this.conferenceDetail.formId}&marketingEventId=${this.marketingEventId}&objectId=${this.meetingId}&objectType=13&needReport=true&_hash=/cml/h5/custom-form-page`;
      // let detail_params = `conferenceId=${this.meetingId}&marketingEventId=${this.marketingEventId}&byshare=1&_hash=/cml/h5/conference_detail`;
      // const longUrl = `${window.location.origin}/ec/cml-marketing/release/web/cml-marketing.html?`;
      
      const formUrl = createMaterialDetailUrlByObjectType(16, {
        id: this.conferenceDetail.formId,
        objectId: this.meetingId,
        objectType: 13,
        marketingEventId: this.marketingEventId,
        needReport: true,
      }).url
      const detailUrl = createMaterialDetailUrlByObjectType(13, {
        id: this.meetingId,
        marketingEventId: this.marketingEventId,
        byshare: 1,
      }).url
      getShortUrl(formUrl).then(
        (shortUrl) => {
          this.H5Url_form_async = shortUrl;
        },
        () => {
          this.H5Url_form_async = formUrl;
        },
      );
      getShortUrl(detailUrl).then(
        (shortUrl) => {
          this.H5Url_detail_async = shortUrl;
        },
        () => {
          this.H5Url_detail_async = detailUrl;
        },
      );
    },
    handleGoEnrollSetting() {
      this.$router.push({ name: 'meeting-enroll-setting' });
    },
    handleGoMeetingInfomation() {
      this.$router.push({ name: 'meeting-detail', params: { id: this.$route.params.id } });
    },
    handleDownload(type) {
      this.isShowDownloadDialog = true;
      this.qrType = type;
    },
    handleCopyLink(type) {
      this.qrType = type;
      this.copyType = 'link';
      this.isShowCopylinkDialog = true;
    },
    handleGetIframe(type) {
      this.qrType = type;
      this.copyType = 'iframe';
      this.isShowCopylinkDialog = true;
    },
    handleDownloadDialogVisible(visible) {
      this.isShowDownloadDialog = visible;
    },
    handleCopylinkDialogVisible(visible) {
      this.isShowCopylinkDialog = visible;
    },
    // getH5Url(data) {
    //   const params = `formId=${this.conferenceDetail.formId}&marketingEventId=${this.marketingEventId}&objectId=${this.meetingId}&objectType=13&needReport=true&_hash=/cml/h5/custom-form-page`;
    //   return `${window.location.origin}/ec/cml-marketing/release/web/cml-marketing.html?${params}`;
    // },
    // getIframe() {
    //   return `<iframe frameborder="0" src="${this.getH5Url()}" allowFullScreen="true"></iframe>`;
    // },
  },
  created() {
    this.queryConferenceFormQrCode();
    this.queryConferenceQrCode();
    this.getH5ShortUrl();
    console.log('conferenceDetail', this.conferenceDetail);
  },
};
</script>
<style lang="less" scoped>
.invitation-content__qrcode-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  .header {
    // height: 52px;
    text-align: right;
    padding-right: 21px;
  }
  .content {
    // padding-left: 15px;
    .content__title {
      margin: 30px 0 0 20px;
      font-size: 14px;
      color: #181c25;
    }
    .content__card {
      border: 1px solid #e9edf5;
      margin: 10px 20px 0;
      padding: 20px;
      display: flex;
      align-items: center;
      .card__img {
        width: 80px;
        height: 80px;
        img {
          width: 100%;
        }
      }
      .card__info {
        margin-left: 25px;
        flex: 1;
        .info__title {
          font-size: 16px;
          color: #181c25;
        }
        .info__desc {
          margin-top: 7px;
          font-size: 14px;
          color: #91959e;
        }
      }
      .card__btn {
        display: flex;
        & > * {
          margin-left: 20px;
        }
      }
    }
  }
}
</style>
