<template>
    <div class="meeting-setting-wrapper">
      <div class="meeting-setting-content">
        <div class="meeting-setting-left__bar">
          <sidebar-menu 
            :menuItems="menuList"
            :default-active-index="activeMenuIndex"
            @menu-click="handleMenuClick"
          />
        </div>
        <div class="meeting-setting-right__content">
          <component :is="activeMenu" />
        </div>
      </div>
    </div>
</template>

<script>
import SidebarMenu from '@/components/sidebar/index.vue'
import MeetingBasicInfo from '../baseinfo-setting/index.vue'
import MeetingSignInSetting from '../MeetingSignin/index.vue'
import MeetingInvitationSetting from '../MeetingInvite/index.vue'
import MeetingEnrollDataSyncRule from '../MeetingEnrollDataSyncRule/index.vue'

export default {
  components: {
    SidebarMenu,
    MeetingSignInSetting,
    MeetingInvitationSetting,
    MeetingEnrollDataSyncRule,
    MeetingBasicInfo
  },
  data() {
    return {
      menuList: [
        { title: $t('marketing.pages.meeting_marketing.hyjbxx_ffed79') ,component: 'MeetingBasicInfo'},
        { title: $t('marketing.commons.qdsz_df9514') ,component: 'MeetingSignInSetting'},
        { title: $t('marketing.commons.yysz_1216e9') ,component: 'MeetingInvitationSetting'},
        { title: $t('marketing.commons.bmsjzdtbgz_03e67b') ,component: 'MeetingEnrollDataSyncRule'}
      ],
      activeMenu: this.$route.query.defaultActiveMenu || 'MeetingBasicInfo',
    }
  },
  methods: {
    handleMenuClick(item) {
      // 更新当前激活的菜单组件
      this.activeMenu = item.component;
    }
  },
  computed: {
    activeMenuIndex() {
      return this.menuList.findIndex(item => item.component === this.activeMenu) || 0
    }
  }
}
</script>

<style lang="less" scoped>
.meeting-setting-wrapper{
  display: flex;
  height: 100%;
  .meeting-setting-content{
    display: flex;
    flex: 1;
    position: relative;
    height: 100%;
    overflow: hidden;
    .meeting-setting-left__bar{
      width: 180px;
      background-color: #fff;
      border-right: 1px solid #DEE1E8;
      box-sizing: border-box;
      border-radius: 8px 0 0 8px;
      position: sticky;
      top: 0;
      left: 0;
      z-index: 5;
    }
    .meeting-setting-right__content{
      flex: 1;
      height: 100%;
      background-color: #fff;
      border-top-right-radius: 8px;
      overflow: auto;
    }
  }
} 
</style>