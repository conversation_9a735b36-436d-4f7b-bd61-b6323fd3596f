<template>
  <div class="meeting-marketing__information-wrapper">
    <div slot="header" style="margin-bottom: 12px;">
      <content-header :title="crumbs" :border="true"></content-header>
    </div>
    <div class="meeting-info-main">
      <div class="km-g-loading-mask" v-if="detailLoading">
        <span class="loading"></span>
      </div>
      <div class="main-right" v-else>
        <router-view></router-view>
        <StepPopover v-if="isShowConferenceGuideOne" :defineSteps="stepsPopover" />
      </div>
    </div>
  </div>
</template>

<script>
import ContentHeader from "@/components/content-header/index";
// import meetingOverview from "./meeting-overview/index";

import Driver from "driver.js";
import "driver.js/dist/driver.min.css";
import StepPopover from "@/components/step-popover";
import { MEETING_MENUS } from '../MeetingDetail/components/config.js';

export default {
  components: {
    elMenu: FxUI.Menu,
    elMenuItem: FxUI.MenuItem,
    elMenuItemGroup: FxUI.MenuItemGroup,
    elButton: FxUI.Button,
    StepPopover,
    ContentHeader
  },
  data() {
    return {
      detailLoading: true,
      isShowConferenceGuideOne: false,
      stepsPopover: [
        {
          element: "#conference-guide-one",
          popover: {
            className: "yxt-guide-popover",
            position: "right",
            title: $t('marketing.pages.meeting_marketing.jxbzbhy_acc6b9'),
            stageBackground: "#407FFF",
            closeBtnText: $t('marketing.pages.meeting_marketing.shsz_657d18'),
            doneBtnText: "d",
            description:
              `<p>${$t('marketing.pages.meeting_marketing.tjhyxqrchr_37df91')}<br>${$t('marketing.pages.meeting_marketing.wcbmszjkjh_cc46f6')}<p/>` +
              `<div><button class="driver-footer-button" id="addMeetingDetail">${$t('marketing.pages.meeting_marketing.tjhyxq_fb3384')}</button></div>`
          }
        }
      ],
      conferenceDetail: {}
    };
  },
  computed: {
    crumbs(){
      const _crumbs = [
        { text: $t('marketing.commons.hyyx_5f60fd'), to: { name: "meeting-marketing-init" } },
        {
          text: this.conferenceDetail.title || '--',
          to: false,
        }
      ]
      if (this.$route.name !== 'meeting-detail') {
        let subTitle = '';
        try {
          subTitle = MEETING_MENUS.filter(item => item.route && (item.route.name === this.$route.name))[0].label;
        } catch(e) {}

        if(!subTitle){
            if(this.$route.name === 'meeting-invitation-content'){
              subTitle = $t('marketing.commons.tgnr_a6ec90');
            }
          }
        
        _crumbs[1].to = {
            name: "meeting-detail",
            params: { id: this.conferenceDetail.id }
        }
        _crumbs.push({
          text: subTitle || '--',
          to: false,
        })
      }
      return _crumbs;
    }
  },
  methods: {
    initData() {
      this.detailLoading = true;
      this.$store
        .dispatch("queryConferenceDetail", {
          conferenceId: this.$route.params.id
        })
        .then(results => {
          this.detailLoading = false;
          this.conferenceDetail = results;
        });
    }
  },
  created() {
    if (this.$route.params.source && this.$route.params.source == "calendar") {
      (this.crumbs[0].text = $t('marketing.commons.yxrl_09e6dd')),
        (this.crumbs[0].to = { name: "marketing-calendar" });
    }
    let idFromPath = this.$route.params.id;
    if (!this.$store.state.MeetingMarketing.conferenceId) {
      this.$store.commit("getConferenceId", { idFromPath });
    }
    this.initData();
  },
  beforeDestroy() {
    this.$store.commit('MeetingMarketing/setState', {
      marketingEventDetail: {},
      conferenceDetail: {},
    });
  }
};
</script>
<style lang="less" scoped>
.meeting-marketing__information-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-width: 1200px;
  .meeting-info {
    &-overview {
      height: 120px;
      width: 100%;
      display: flex;
      align-items: center;
      flex-shrink: 0;
      // border-top: 1px solid #e8e8e8;
      .overview-detail {
        flex: 1 1 auto;
        margin-left: 20px;
        &-title {
          font-size: 16px;
          color: #181c25;
        }
        &-status {
          width: 45.93px;
          height: 18px;
          border-radius: 2px;
          display: inline-block;
          font-size: 12px;
          text-align: center;
          border: 1px solid;
        }
        &-time {
          font-size: 13px;
          color: #545861;
          margin-top: 16px;
        }
      }
      .marketing-roi {
        width: 375px;
        margin: 13px 0;
        display: flex;
        align-items: center;
        flex-shrink: 0;
        .marketing-roi-wrapper {
          flex: 1 1 auto;
        }
        .line {
          height: 88px;
          width: 1px;
          border-right: 1px solid #e9edf5;
          margin-right: 31px;
        }
        .roi-wrapper {
          display: flex;
          margin-top: 10px;
          margin-bottom: 8px;
          .roi {
            width: 50%;
            .label {
              font-size: 12px;
              color: #91959e;
            }
            .number {
              font-size: 14px;
              color: #181c25;
            }
          }
        }
        .overview-link {
          margin-top: 8px;
          &:hover {
            cursor: pointer;
          }
        }
      }
    }
    &-main {
      box-sizing: border-box;
      display: flex;
      width: 100%;
      height: calc(100% - 68px); // 别删，有问题跟大雄沟通
      position: relative;
      .main-left {
        width: 130px;
        flex-shrink: 0;
        border-right: 1px solid #e9edf5;
        box-sizing: border-box;
        background-color: #f7f9ff;
        padding-left: 8px;
        padding-right: 8px;
        .el-menu {
          border: 0;
        }
        .meeting-menu-wrapper {
          .is-active {
            background-color: #e9f0ff !important;
            width: 114px;
            border-radius: 6px;
            // background: no-repeat url(../../../assets/images/icon/icon-arrow-grey.png);
          }
          .is-active::after {
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid #b4b6c0;
            content: "";
            position: relative;
            height: 0;
            transform: rotate(-90deg);
            left: 15px;
            top: -2px;
            display: inline-block;
          }
          .el-menu-item {
            font-size: 13px;
            height: 40px;
            line-height: 40px;
            &:hover {
              background-color: #e9f0ff !important;
              border-radius: 6px;
            }
          }
        }
      }
      .main-right {
        flex: 1;
        width: calc(100% - 130px);
        // background-color: #f2f2f5;
        height: 100%;
        overflow: auto;
      }
    }
  }
}
</style>
