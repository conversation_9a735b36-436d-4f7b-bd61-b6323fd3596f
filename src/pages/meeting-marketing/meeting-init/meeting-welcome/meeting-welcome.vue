<template>
    <div class="meeting-welcome">
      <content-header
        :title="$t('marketing.commons.hyyx_5f60fd')"
        :border="true"
      />
      <div class="content-summarize">
        <div
          class="content-summarize-photo"
          :style="{
            'background': `url(${bannerImg}) center / 1170px 260px no-repeat, linear-gradient(to right, #e7f4ff, #d9eeff)`,
            'background-size': 'cover'
          }"
        >
          <div class="guide__banner-h">
            {{ $t('marketing.pages.meeting_marketing.xxhdqlcszh_8b173c') }}
          </div>
          <div class="guide__banner-con">
            <span class="guide__banner-text">
              {{ $t('marketing.pages.meeting_marketing.zcslfbhzhy_b4182c') }}
            </span>
          </div>
        </div>
      </div>
      <div class="content-tips"></div>
      <div class="content-guidance">
        <div class="content-item" v-for="(item, index) in list" :key="index">
          <div class="guidance-line" v-if="index < (list.length - 1)"></div>
          <div class="guidance-arrow-line" v-else>
            <div class="guidance-arrow"></div>
          </div>
          <img class="guidance-icon" :src="item.image" />
          <div class="guidance-title">{{ item.title }}</div>
          <div class="guidance-desc">
            <div v-for="(desc, i) in item.descList" :key="i" class="guidance-desc-item">- {{ desc }}</div>
          </div>
        </div>
      </div>
      <div class="content-foot">
        <fx-button 
          class="tool-button" 
          size="small" 
          type="primary" 
          @click="addConference"
        >
          {{ $t('marketing.pages.meeting_marketing.ljxjhy_3ce47c') }}
        </fx-button>
      </div>
    </div>
</template>
<script>
import ContentHeader from '@/components/content-header'
import http from '@/services/http/index'

import introImg from '@/assets/images/meeting-welcome-3.jpg'
import introImgEn from '@/assets/images/meeting-welcome-3.jpg'

import { getImageByLang } from '@/utils/i18n.js'

export default {
  components: {
    ContentHeader,
  },
  data() {
    return {
      bannerImg: getImageByLang([introImg, introImgEn]),
      list: [
        {
          title: $t('marketing.commons.hdchynrzz_0a7038'),
          image: require("@/assets/images/content-marketing/guidance-2.png"),
          descList: [
            $t('marketing.pages.meeting_marketing.jhysp_e02d5b'),
            $t('marketing.pages.meeting_marketing.hdldy_cae1a4'),
            $t('marketing.pages.meeting_marketing.dqdtghb_4bd932'),
            $t('marketing.pages.meeting_marketing.dzcpscd_5d57ae'),
          ],
        },
        {
          title: $t('marketing.commons.dqdkhyyytg_9692d3'),
          image: require("@/assets/images/content-marketing/guidance-3.png"),
          descList: [
            $t('marketing.pages.meeting_marketing.dqdxxqf_ed01b7'),
            $t('marketing.pages.meeting_marketing.qylbtg_61c4b1'),
            $t('marketing.pages.meeting_marketing.zsqdtgm_e750e3'),
            $t('marketing.pages.meeting_marketing.ygdbm_d2e08a'),
          ],
        },
        {
          title: $t('marketing.pages.meeting_marketing.zxbmjhytx_f9c7c0'),
          image: require("@/assets/images/content-marketing/guidance-1.png"),
          descList: [
            $t('marketing.pages.meeting_marketing.bmsjssgz_ea1f15'),
            $t('marketing.pages.meeting_marketing.bmxxsh_83e165'),
            $t('marketing.pages.meeting_marketing.bmshchtxtz_fd4dc6'),
          ],
        },
        {
          title: $t('marketing.pages.meeting_marketing.xxqdyhd_4568e0'),
          image: require("@/assets/images/content-marketing/guidance-5.png"),
          descList: [
            $t('marketing.pages.meeting_marketing.khsmhchmyz_b2beb1'),
            $t('marketing.pages.meeting_marketing.skhmpyjlrx_07b762'),
            $t('marketing.pages.meeting_marketing.smlzrqgzgz_4c76f2'),
          ],
        },
        {
          title: $t('marketing.pages.meeting_marketing.hhgjjxgzz_911ed8'),
          image: require("@/assets/images/content-marketing/guidance-4.png"),
          descList: [
            $t('marketing.commons.sdbq_cc7410'),
            $t('marketing.pages.meeting_marketing.jcsjjwjzlt_b9a000'),
            $t('marketing.pages.meeting_marketing.hdxsfpjzhg_fd2dd0'),
            $t('marketing.pages.meeting_marketing.hdpg_b3a76a'),
          ],
        },
      ]
    }
  },
  computed: {},
  mounted() {},
  methods: {
    toAddConfenrence(data) {
      http.addConference({ marketingEventId: data._id }).then(results => {
        if (results && results.errCode == 0) {
          this.$router.push({ name: 'meeting-detail', params: { id: results.data.id } })
        }
      })
    },
    getCRMDescribeLayout() {
      return new Promise(resolve => {
        FS.util.FHHApi({
          url: '/EM1HNCRM/API/v1/object/MarketingEventObj/controller/DescribeLayout',
          data: {
            apiname: 'MarketingEventObj',
            include_detail_describe: true,
            include_layout: true,
            layout_type: 'add',
            recordType_apiName: 'default__c',
          },
          success: res => {
            if (!res.Value || res.Value.errCode) return
            this.loading_marketingEventName = false
            const obj = res.Value.objectDescribe || {}
            resolve(obj)
          },
        })
      })
    },
    addConference() {
      this.$router.push({ name: 'meeting-marketing-create' })
    },
  },
}
</script>
<style lang="less">
@basePath: "../../../../";
.meeting-welcome {
  width: 100%;
  background-color: #fff;
  .content-summarize {
    margin: 12px;
    display: flex;
    &-photo {
      width: 100%;
      height: 300px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding-left: 120px;
    }
    .guide__banner-h {
      max-width: 50%;
      font-weight: 700;
      font-size: 20px;
      color: #49638d;
    }

    .guide__banner-con {
      max-width: 640px;
      display: flex;
      flex-direction: column;
      text-align: left;
      margin-top: 16px;

      .guide__banner-text {
        color: #6f889c;
        font-size: 16px;
        line-height: 1.7;
      }

      .guide__banner-text + .guide__banner-text {
        margin-top: 4px;
      }
    }
  }
  
  .content-tips {
    color: #2A304D;
    font-size: 16px;
    text-align: center;
    margin: 45px 17px 23px 17px;
  }
  
  .content-guidance {
    display: flex;
    flex-flow: row nowrap;
    position: relative;
    justify-content: center;
    margin: 15px 34px 65px 34px;
    .content-item {
      display: flex;
      flex-flow: column wrap;
      align-items: center;
      flex: 1;
      &:first-of-type {
        padding-left: 0;
      }
      .guidance-line {
        position: absolute;
        height: 1px;
        width: 20%;
        border-bottom: 1px solid #e9edf5;
        top: 20px;
        transform: translateX(50%);
        z-index: 1;
      }
      .guidance-arrow-line {
        position: absolute;
        height: 1px;
        width: 30px;
        border-bottom: 1px solid #e9edf5;
        top: 20px;
        transform: translateX(50%);
        z-index: 1;
        .guidance-arrow {
          position: absolute;
          top: -7px;
          left: 100%;
          width: 14px;
          height: 15px;
          // background: url("@/assets/images/content-marketing/arrow.png") center / 14px 15px no-repeat;
        }
      }
      .guidance-icon {
        width: 40px;
        height: 40px;
        z-index: 2;
      }
      .guidance-title {
        color: #181C25;
        margin-top: 14px;
        font-size: 14px;
      }
      .guidance-desc {
        color: #91959E;
        margin-top: 12px;
        font-size: 12px;
        padding-left: 0;
        list-style: disc inside;
        text-align: left;
      }
    }
  }
  
  .content-foot {
    text-align: center;
    .tool-button {
      margin: 0 20px;
    }
  }
}
</style>
