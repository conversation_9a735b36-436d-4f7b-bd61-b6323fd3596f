<template>
  <div class="meeting-container">
    <div class="list-wrapper">
      <SmartLayout :gutter="20">
        <div
          v-for="(item, index) in meetingList"
          :key="index"
          :class="{'meeting-card': true, selected: selectedMap[item.conferenceId] > -1}"
        >
          <div
            class="meeting-image"
            @click="goDetail(item,index)"
          >
            <!-- <img
              v-if="item.thumbnailUrl"
              :src="item.thumbnailUrl"
              class="image"
            > -->
            <div
              v-if="item.thumbnailUrl"
              class="image"
              :style="{
                background: `url(${item.thumbnailUrl}) center / cover no-repeat`
              }"
            />
            <div
              v-else
              class="cover__emptytips"
            >
              <div class="emptytips__bg" />
            </div>
            <LiveStatus
              class="meeting-image-status"
              :life-status="item.lifeStatus"
              :status="item.flowStatus"
            />
            <fx-checkbox
              class="meeting-checkbox"
              :value="selectedMap[item.conferenceId] > -1"
              @click.native="handleStopDefault"
              @change="e => handleMeetingCheck(e, item)"
            />
          </div>
          <div
            class="meeting-info"
            @click="goDetail(item,index)"
          >
            <p class="meeting-info-title">
              {{ item.title }}
            </p>
            <div class="meeting-attr-wrap">
              <p class="meeting-info-title meeting-location">
                {{ item.location }}
              </p>
              <div
                class="meeting-tags"
                @click.stop="handleStopDefault"
              >
                <v-tag
                  name-key="name"
                  empty-text=""
                  :data="item.materialTags || []"
                />
              </div>
            </div>
            <p class="meeting-info-time">
              {{ util.formatDateTime(item.startTime, "YYYY-MM-DD hh:mm") }}
              &nbsp;
              {{ $t('marketing.commons.z_981cbe') }}
              &nbsp;
              {{ util.formatDateTime(item.endTime, "YYYY-MM-DD hh:mm") }}
            </p>
          </div>
          <div class="meeting-person">
            <div
              class="meeting-person-data"
              @click="goDetail(item)"
            >
              {{ $t('marketing.commons.ybm_4166d8') }}&nbsp;{{ item.enrollCount }}
          &nbsp;&nbsp;&nbsp;&nbsp;{{ $t('marketing.commons.yqd_e81a9d') }}&nbsp;
              {{ item.signInCount }}&nbsp;&nbsp;
            </div>
            <fx-popover
              v-model="item.popVisible"
              placement="bottom-start"
              popper-class="meeting-list-popover-wrapper"
            >
              <div
                v-if="item.popVisible"
                class="popover-operate"
              >
                <div
                  class="operate-list"
                  @click.stop="changeMeeting(item, index, 3)"
                >
                  {{ $t('marketing.commons.sc_2f4aad') }}
                </div>
              </div>
              <div
                slot="reference"
                class="image-wrapper"
              >
                <img
                  :src="require('@/assets/images/icons/ellipsis.png')"
                  class="image"
                >
              </div>
            </fx-popover>
          </div>
          <PermissionDialog
            :marketing-event-name="marketingEventName"
            :marketing-event-id="marketingEventId"
            :visible.sync="permissionDialogVisible"
          />
        </div>
      </SmartLayout>
    </div>
  </div>
</template>

<script>
import { confirm } from '@/utils/globals.js'
import util from '@/services/util/index.js'
import http from '@/services/http/index.js'
import LiveStatus from './components/liveStatus.vue'
import PermissionDialog from '@/pages/live-marketing/components/permissionsDialog.vue'
import kisvData from '@/modules/kisv-data.js'
import VTag from '@/components/table-ex/tag.vue'
import SmartLayout from '@/components/SmartLayout/index.vue'

export default {
  components: {
    LiveStatus,
    PermissionDialog,
    VTag,
    SmartLayout,
  },
  props: {
    meetingList: {
      type: Array,
      default: () => [],
    },
    selectedMeetings: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      backgroundColor: '',
      util,
      popVisible: false,
      permissionDialogVisible: false,
      marketingEventId: '',
      marketingEventName: '',
      vDatas: kisvData.datas,
    }
  },
  computed: {
    selectedMap() {
      const meetingsMap = {}
      this.selectedMeetings.forEach((item, index) => {
        meetingsMap[item.conferenceId] = index
      })

      return meetingsMap
    },
  },
  created() {
    this.meetingList.forEach((item, index) => {
      Vue.set(item, 'popVisible', false)
    })
  },
  mounted() {},
  methods: {
    async goDetail(item, index) {
      console.log('item', index)
      const permission = await this.checkMarketingEventPermission(item.marketingEventId)
      if (!permission) {
        this.marketingEventId = item.marketingEventId
        this.marketingEventName = item.title
        this.permissionDialogVisible = true
      } else {
        this.$router.push({
          name: 'meeting-detail',
          params: { id: item.conferenceId},
          query: {
            source: 'conference'
          }
        })
      }
    },
    async checkMarketingEventPermission(id) {
      let flag = true
      if (this.vDatas.uinfo.marketingDataIsolation) {
        const res = await http.getEntityOpenness({
          objectApiName: 'MarketingEventObj',
          objectIds: [id],
        })
        //  1是只读,2是读写
        if (res.errCode === 0 && res.data && res.data.openness) {
          flag = res.data.openness[id] === 2
        }
      }
      return flag
    },
    async changeMeeting(item, index, meetingStatus) {
      if (meetingStatus === 3) {
        await confirm($t('marketing.pages.meeting_marketing.sfschy_4ca116'), $t('marketing.commons.ts_02d981'), {})
      }
      const params = {
        id: item.conferenceId,
        status: meetingStatus,
      }
      http.updateConferenceStatus(params).then(res => {
        if (res && res.errCode === 0) {
          FxUI.Message({
            message: $t('marketing.commons.szcg_f6088e'),
            type: 'success',
            duration: 1000,
          })

          this.$emit('queryMeetingList')
        }
      })
    },
    handleStopDefault(e) {
      e.stopPropagation()
    },
    handleMeetingCheck(checked, meeting) {
      this.$emit('change', checked, meeting)
    },
  },
}
</script>

<style lang="less">
.list-wrapper {
  display: flex;
  flex-wrap: wrap;
  height: 100%;
  overflow: auto;
  padding: 17px;
  align-content: flex-start;
  box-sizing: border-box;

  .meeting-card {
    width: 266px;
    // height: 266px;
    border-radius: 2px;
    border: 1px solid rgba(233, 237, 245, 1);
    box-sizing: border-box;
    display: flex;
    flex-direction: column;

    .meeting-image {
      // width: 266px;
      height: 147px;
      position: relative;
      overflow: hidden;
      .cover__emptytips {
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #eeeeee;
        height: 147px;
        width: 266px;
        .emptytips__bg {
          background-image: url("../../../assets/images/defualt-pic.png");
          width: 70px;
          height: 57px;
          background-size: cover;
        }
      }
      .image {
        // width: 266px;
        // position: absolute;
        // top: 50%;
        // left: 50%;
        // transform: translate(-50%, -50%);
        height: 100%;
      }
      &-status {
        width: fit-content;
        height: 28px;
        position: absolute;
        left: 0;
        top: 0;
        color: #ffffff;
        font-size: 13px;
        text-align: center;
        line-height: 28px;
        min-width: 80px;
        padding: 0 4px;
      }
      .meeting-checkbox {
        position: absolute;
        right: 5px;
        top: 5px;
        opacity: 0;
      }
    }

    .meeting-info {
      padding: 12px;
      border-bottom: 1px solid rgba(233, 237, 245, 1);
      border-top: 1px solid rgba(233, 237, 245, 1);
      flex: 1;
      display: flex;
      flex-direction: column;

      &-title {
        font-size: 14px;
        color: #181c25;
        margin-bottom: 6px;
        width: 246px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        &.meeting-location {
          font-size: 12px;
          line-height: 18px;
          min-height: 18px;
          color: #91959e;
        }
      }
      &-time {
        color: #91959e;
        font-size: 12px;
        width: 246px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .meeting-tags {
        padding-bottom: 6px;
        height: 20px;
      }

      .meeting-attr-wrap {
        flex: 1;
      }
    }
    .meeting-person {
      line-height: 46px;
      height: 46px;
      display: flex;
      justify-content: space-between;
      position: relative;
      &-data {
        color: #545861;
        font-size: 12px;
        margin-left: 13px;
        width: 200px;
      }
    }
    .image-wrapper {
      height: 100%;
      padding-left: 20px;
      .image {
        margin-right: 17px;
        width: 16px;
        height: 16px;
      }
    }

    &:hover, &.selected {
      cursor: pointer;
      box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.2);

      .meeting-image .meeting-checkbox {
        opacity: 1;
      }
    }
  }
}
.meeting-list-popover-wrapper {
  min-width: 0px !important;
  width: 91px;
  padding: 8px 0px;
  border-radius: 2px !important;
  box-shadow: -1px 0px 7px 0px rgba(0, 0, 0, 0.16);
  margin-top: -8px !important;
  margin-left: -37px;
  .popover-operate {
    .operate-list {
      height: 30px;
      line-height: 30px;
      width: 100%;
      text-align: center;
      color: #181c25;
      font-size: 12px;
      &:hover {
        cursor: pointer;
        background-color: #f0f4fc;
      }
    }
  }
}
</style>
