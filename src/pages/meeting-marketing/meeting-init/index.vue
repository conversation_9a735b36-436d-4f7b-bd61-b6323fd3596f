<template>
  <div class="meeting-init">
    <div v-if="loading_list">
      <div class="km-g-loading-mask">
        <span class="loading" />
      </div>
    </div>
    <div v-if="isShow">
      <meeting-welcome />
    </div>
    <div
      v-else
      class="meeting-home"
    >
      <scroll-wrapper>
        <div slot="header">
          <content-header
            :title="$t('marketing.commons.hyyx_5f60fd')"
            :border="true"
            class="content-header"
          >
            <ContentGroupHeader
              :object-type="33"
              :data="selectedMeetings"
              type="meeting"
              @close="handleClearSelect"
              @update:list="handleUpdateList"
            />
            <div
              slot="right"
              class="meeting-tools"
            >
              <ContentTagsQuery
                v-model="materialTagFilter"
                @change="handleTagsChange"
              />
              <AdvancedFilterButton
                style="margin-left: 20px;margin-right: -10px;"
                :object-names="objectNames"
                @confirm="handleFilterConfirm"
              />
              <eventTypeSelector
                style="margin-left: 20px;"
                :expandOptions="[
                  {
                    apiName: '',
                    fieldName: $t('marketing.commons.qb_a8b0c2'),
                  },
                ]"
                :type="2"
                @change="handleEventTypeChange"
              />
              <fx-input
                v-model="searchText"
                size="small"
                class="search-input"
                :placeholder="
                  $t('marketing.pages.meeting_marketing.sshybt_247b1c')
                "
                prefix-icon="el-icon-search"
                @change="searchMeeting"
              />
              <fx-button
                type="primary"
                size="small"
                style="margin-left: 20px;"
                @click="addConference"
              >
                <i class="yxt-icon16 icon-add" />
                <span>{{ $t("marketing.commons.xjhy_fe58e6") }}</span>
              </fx-button>
            </div>
          </content-header>
        </div>
        <div
          class="meeting-main-wrapper"
        >
          <meeting-list
            v-if="list && list.length"
            :meeting-list="list"
            :selected-meetings="selectedMeetings"
            @queryMeetingList="queryMeetingList"
            @change="handleMeetingCheck"
          />
          <Empty
            v-else-if="list.length == 0"
            class="main__meeting--empty"
            :title="$t('marketing.commons.zwsj_21efd8')"
            button
          />
        </div>
        <div
          slot="footer"
          class="w-pagination"
        >
          <v-pagen
            :pagedata.sync="pageData"
            @change="handlePageChange"
          />
        </div>
      </scroll-wrapper>
    </div>
  </div>
</template>
<script>
import MeetingWelcome from './meeting-welcome/meeting-welcome.vue'
import http from '@/services/http/index.js'

import VPagen from '@/components/kitty/pagen.vue'
import ScrollWrapper from '@/components/table-ex/scroll-wrapper.vue'
import MeetingList from './meeting-list.vue'
import ContentHeader from '@/components/content-header/index.vue'
import Empty from '@/components/common/empty.vue'
import AdvancedFilterButton from '@/components/advanced-filter-button/index.vue'
import ContentTagsQuery from '@/components/content-tags-selector/tags-query.vue'
import ContentGroupHeader from '@/components/ContentGroupHeader/index.vue'
import eventTypeSelector from '@/components/eventTypeSelector/index.vue'
export default {
  components: {
    MeetingWelcome,
    VPagen,
    ScrollWrapper,
    MeetingList,
    ContentHeader,
    Empty,
    AdvancedFilterButton,
    ContentTagsQuery,
    ContentGroupHeader,
    eventTypeSelector
  },
  data() {
    return {
      list: [],
      params: {
        keyword: '',
        pageNum: 1,
        pageSize: 16,
        // time: 0,
        flowStatus: 0,
      },
      totalCount: 0,
      activeName: 0,
      searchText: '',
      pageData: {
        layout: 'prev, pager, next, total, sizes, jumper',
        pageNum: 1,
        totalCount: 0,
        pageSize: 30,
        pageSizes: [10, 20, 30, 40],
      },
      isShow: false,
      selectStatusList: [
        {
          value: 0,
          label: $t('marketing.pages.meeting_marketing.qbhy_daa200'),
        },
        // {
        //   value: 1,
        //   label: '未发布',
        // },
        {
          value: 2,
          label: $t('marketing.commons.wks_dd4e55'),
        },
        {
          value: 3,
          label: $t('marketing.commons.jhz_fb852f'),
        },
        {
          value: 4,
          label: $t('marketing.commons.yjs_047fab'),
        },
      ],
      loading_list: false,
      objectNames: [{ name: $t('marketing.commons.schd_833ba0'), value: 'MarketingEventObj' }],
      fitlers: [],
      materialTagFilter: {
        queryType: 2,
        tags: [],
      },
      selectedMeetings: [],
      // 新加活动类型筛选
      screenStatus: '',
      isSearchIndex: 0
    }
  },
  computed: {},
  mounted() {
    this.queryMeetingList()
  },
  created() {
    // this.queryMeetingList();
  },
  methods: {
    handleFilterConfirm(data) {
      this.pageData.pageNum = 1
      this.fitlers = data
      this.queryMeetingList()
    },
    // "keyword": "string",
    // "pageNum": 0,
    // "pageSize": 0,
    // "flowStatus": 0,
    // "time": 0
    //     搜索会议状态
    //     NOT_STARTED(2),    //会议未开始
    //     PROCESSING(3),     //会议进行中
    //     END(4);            //会议已结束
    // 全部：不传就行
    queryMeetingList() {
      if (this.loading_list) return
      this.loading_list = true
      
      const materialTagFilter = {
        type: this.materialTagFilter.queryType,
        materialTagIds: this.materialTagFilter.tags.map(el => el.tagId),
      }
      const options = {
        keyword: this.params.keyword,
        pageNum: this.pageData.pageNum,
        pageSize: this.pageData.pageSize,
        flowStatus: this.params.flowStatus,
        filterData: this.fitlers.length > 0 ? this.fitlers[0] : {},
        materialTagFilter,
      }
      if(this.screenStatus){
        options.eventType = this.screenStatus
      }
      http.queryMeetingList(options).then(res => {
        this.loading_list = false
        if (res && res.errCode === 0) {
          if(res.data.result.length === 0 && this.isSearchIndex === 0){
            this.isShow = true
          } else {
            this.isShow = false
          }
          this.isSearchIndex = 1
          this.list = res.data.result
          this.pageData.totalCount = res.data.totalCount
        }
      })
    },
    handleClick(index) {
      this.params.flowStatus = index
      this.pageData.pageNum = 1
      this.pageData.pageSize = 10
      this.queryMeetingList()
    },
    toAddConfenrence(data) {
      http.addConference({ marketingEventId: data._id }).then(results => {
        if (results && results.errCode === 0) {
          this.$router.push({
            name: 'meeting-detail',
            params: { id: results.data.id },
          })
        }
      })
    },
    getCRMDescribeLayout() {
      return new Promise(resolve => {
        FS.util.FHHApi({
          url:
            '/EM1HNCRM/API/v1/object/MarketingEventObj/controller/DescribeLayout',
          data: {
            apiname: 'MarketingEventObj',
            include_detail_describe: true,
            include_layout: true,
            layout_type: 'add',
            recordType_apiName: 'default__c',
          },
          success: res => {
            if (!res.Value || res.Value.errCode) return
            this.loading_marketingEventName = false
            const obj = res.Value.objectDescribe || {}
            resolve(obj)
          },
        })
      })
    },

    async addConference() {
      const that = this
      this.$router.push({ name: 'meeting-marketing-create' })
      return
    },
    searchMeeting(val) {
      this.searchText = val
      this.params.keyword = val
      this.pageData.pageNum = 1
      this.queryMeetingList()
    },
    handlePageChange(data) {
      // localStorage.setItem("pageSize",data.pageSize);
      // this.pageData.pageSize = parseInt(localStorage.getItem("pageSize"));
      this.pageData.pageNum = data.pageNum
      this.pageData.pageSize = data.pageSize
      this.queryMeetingList()
    },
    handleTagsChange() {
      this.pageData.pageNum = 1
      this.queryMeetingList()
    },
    handleClearSelect() {
      this.selectedMeetings = []
    },
    handleUpdateList() {
      this.selectedMeetings = []
      this.pageData.pageNum = 1
      this.queryMeetingList()
    },
    handleMeetingCheck(checked, meeting) {
      if (checked) {
        this.selectedMeetings.push(meeting)
      } else {
        this.selectedMeetings = this.selectedMeetings.filter(el => el.conferenceId !== meeting.conferenceId)
      }
    },
    handleEventTypeChange(value) {
      this.screenStatus = value.apiName
      this.pageData.pageNum = 1
      this.queryMeetingList()
    },
  },
}
</script>

<style lang="less">
.meeting-init {
  height: 100%;
  .meeting-home {
    height: 100%;
    .v-table-scroll-wrapper {
      min-height: auto;
      height: 100%;
      overflow: hidden;
      width: 100%;
      .j-scroll-content-wrapper {
        height: calc(100% - 158px);
      }
    }
    .content-header {
      position: relative;
    }
    .meeting-main-wrapper {
      flex: 1;
      overflow: auto;
      .main__meeting--empty {
        height: 100%;
        box-sizing: border-box;
      }
    }
    .meeting-tools {
      font-size: 0;
      width: 100%;
      text-align: right;
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }
    .search-input {
      margin-left: 20px;
    }
  }
}
</style>
