<!-- 组件说明 -->
<template>
     <div class="set-manage-wrapper__outer">
       <div
         class="set-manage-done-wrapper"
       >
         <div v-if="settingStatus" :class="['set-manage',fromCreate && 'create-set-manege']">
           <el-form
             :model="enrollSettingForm"
             ref="enrollSettingForm"
             :rules="rules"
             class="form-wrapper"
             label-width="150px"
             label-position="left"
           >
             <el-form-item
               :label="$t('marketing.commons.bmjzsj_7e58ea')"
               prop="enrollEndTime"
             >
               <fx-date-picker
                 class="form-datepicker"
                 type="datetime"
                 format="yyyy-MM-dd HH:mm"
                 value-format="timestamp"
                 size="small"
                 v-model="enrollSettingForm.enrollEndTime"
                 style="width: 175px;"
               ></fx-date-picker>
             </el-form-item>
             <el-form-item :label="$t('marketing.commons.bmsh_7a0e14')" prop="enrollReview">
               <div class="enrollReview">
                 <el-switch
                   v-model="enrollSettingForm.enrollReview"
                   class="reviewSwitch"
                   size="small"
                 ></el-switch>
                 <p class="enrollReview-text">
                   {{ $t('marketing.commons.kqhchrybmx_b3365e') }}
                 </p>
               </div>
             </el-form-item>
             <el-form-item
               label=""
               v-if="enrollSettingForm.enrollReview"
               prop="isCheckPerson"
               style="margin-bottom: 0;"
             >
               <div class="enrollReview">
                 <el-select
                   class="el-select inform-select"
                   v-model="enrollNoticePoint"
                   @change="informTimeChange"
                   size="small"
                 >
                   <el-option
                     v-for="item in selectStatusList"
                     :key="item.value"
                     :label="item.label"
                     :value="item.value"
                   ></el-option>
                 </el-select>
                   <StaffSelectorDialogBar
                     @change="handleStaffSelectorChange"
                     :defaultSelectedItems="selectResult"
                     :options="{
                       title: $t('marketing.commons.tjshry_f1ccca'),
                       label: $t('marketing.commons.tjshry_f1ccca'),
                       includeCompany: false,
                     }"
                     style="flex: 1;"
                   ></StaffSelectorDialogBar>
               </div>
               <el-form-item
                 v-if="enrollSettingForm.enrollReview"
                 label-width="185px"
                 style="margin-top: 20px;margin-bottom: 0;"
               >
               <span slot="label" style="display: flex;align-items: center;">{{ $t('marketing.pages.meeting_marketing.shztswa_ee7bae') }}
                 <QuestionTooltip
                   effect="dark"
                   :offset="192"
                   style="margin:2px 0 0 4px;"
                 >
                   <span
                     slot="question-content"
                   >{{ $t('marketing.pages.meeting_marketing.dyhjrbmymh_432c0e') }}
                   </span>
                 </QuestionTooltip>
               </span>
               <div>
                   <fx-input
                     v-model="enrollSettingForm.enrollPendingReviewTip"
                     :placeholder="$t('marketing.pages.meeting_marketing.qsrshztswa_de6ea3')"
                     size="small"
                     maxlength="100"
                     show-word-limit
                   ></fx-input>
                 </div>
               </el-form-item>
               <el-form-item
                 v-if="enrollSettingForm.enrollReview"
                 label-width="185px"
                 style="margin-bottom: 0;"
               >
               <span slot="label" style="display: flex;align-items: center;">{{ $t('marketing.pages.meeting_marketing.shwtgtswa_983ae8') }}
                   <QuestionTooltip
                     effect="dark"
                     :offset="192"
                     style="margin:2px 0 0 4px;"
                   >
                     <span
                       slot="question-content"
                     >{{ $t('marketing.pages.meeting_marketing.dyhjrbmymh_432c0e') }}
                     </span>
                   </QuestionTooltip>
                 </span>
                 <div>
                   <fx-input
                     v-model="enrollSettingForm.enrollReviewFailureTip"
                     :placeholder="$t('marketing.pages.meeting_marketing.qsrshwtgts_ea3e76')"
                     size="small"
                     maxlength="100"
                     show-word-limit
                   ></fx-input>
                 </div>
               </el-form-item>
             </el-form-item>
             <!-- 新建的时候不显示会员报名设置 -->
             <el-form-item
               v-if="isMemberOpen && !fromCreate"
               :label="$t('marketing.commons.hyyjbm_839991')"
             >
                <div class="member-map-wrapper">
                 <p class="member-tips">{{ $t('marketing.commons.hyyjbmhhyx_b7b2d3') }}</p>
                 <div class="leadpool-select">
                   <fx-select
                     :placeholder="$t('marketing.commons.qxz_708c9d')"
                     size="small"
                     v-model="poolType"
                     @change="handlePoolTypeChange"
                     :options="PoolTypeOptions"
                   ></fx-select>
                   <fx-select
                     size="small"
                     v-show="poolType === 1"
                     v-model="mappingConfig.leadPoolId"
                     filterable
                     :placeholder="$t('marketing.commons.qxzxschsrg_6f2941')"
                     :options="pools"
                   >
                   </fx-select>
                 </div>
                 <div class="member-map-btn-wrap">
                   <p class="member-map-btn-text">{{ $t('marketing.commons.szxsys_4b462f') }}</p>
                   <div v-if="mappingIsSubmited" class="member-map-btn-content">
                     <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none">
                       <path d="M8 15.3398C11.866 15.3398 15 12.2058 15 8.33984C15 4.47385 11.866 1.33984 8 1.33984C4.13401 1.33984 1 4.47385 1 8.33984C1 12.2058 4.13401 15.3398 8 15.3398ZM8 16.3398C3.58172 16.3398 0 12.7581 0 8.33984C0 3.92157 3.58172 0.339844 8 0.339844C12.4183 0.339844 16 3.92157 16 8.33984C16 12.7581 12.4183 16.3398 8 16.3398Z" fill="#30C776"/>
                       <path d="M4.14645 8.03429C3.95118 8.22955 3.95118 8.54614 4.14645 8.7414L6.62132 11.2163C6.81658 11.4115 7.13317 11.4115 7.32843 11.2163L12.1013 6.44341C12.2966 6.24815 12.2966 5.93156 12.1013 5.7363C11.906 5.54104 11.5895 5.54102 11.3942 5.73628L6.97487 10.1556L4.85355 8.03429C4.65829 7.83903 4.34171 7.83903 4.14645 8.03429Z" fill="#30C776"/>
                     </svg>
                     <span style="margin-left: 5px;">{{ $t('marketing.commons.ysz_44e607') }}</span>
                   </div>
                   <div v-else class="member-map-btn-content">
                     <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none">
                       <path d="M8 15.3398C4.13401 15.3398 1 12.2058 1 8.33984C1 4.47385 4.13401 1.33984 8 1.33984C11.866 1.33984 15 4.47385 15 8.33984C15 12.2058 11.866 15.3398 8 15.3398ZM8 16.3398C12.4183 16.3398 16 12.7581 16 8.33984C16 3.92157 12.4183 0.339844 8 0.339844C3.58172 0.339844 0 3.92157 0 8.33984C0 12.7581 3.58172 16.3398 8 16.3398Z" fill="#FF522A"/>
                       <path d="M7.5 4.33984C7.5 4.0637 7.72386 3.83984 8 3.83984C8.27614 3.83984 8.5 4.0637 8.5 4.33984V9.33984C8.5 9.61599 8.27614 9.83984 8 9.83984C7.72386 9.83984 7.5 9.61599 7.5 9.33984V4.33984Z" fill="#FF522A"/>
                       <path d="M8.75 12.0898C8.75 12.5041 8.41421 12.8398 8 12.8398C7.58579 12.8398 7.25 12.5041 7.25 12.0898C7.25 11.6756 7.58579 11.3398 8 11.3398C8.41421 11.3398 8.75 11.6756 8.75 12.0898Z" fill="#FF522A"/>
                     </svg>
                     <span style="margin-left: 5px;">{{ $t('marketing.commons.wsz_fe2d26') }}</span>
                   </div>
                 <fx-button type="text" size="small" @click="handleMappingSetting">{{ $t('marketing.commons.sz_e366cc') }}</fx-button>
                 </div>
               </div>
             </el-form-item>
           </el-form>
           <fx-button
             type="primary"
             class="btn-opt"
             size="mini"
             style="margin-left:150px"
             v-if="!fromCreate"
             @click="handleCheck"
             :loading="loading"
             >{{ $t('marketing.commons.bc_be5fbb') }}</fx-button
           >
           <fx-button @click="handleCancel" size="mini"  v-if="!fromCreate" class="btn-opt"
             >{{ $t('marketing.commons.qx_625fb2') }}</fx-button
           >
         </div>
         <div v-else class="set-manage-done">
           <div class="item">
             <div class="lable">{{ $t('marketing.commons.bmjzsj_7e58ea') }}</div>
             <div class="value">
               {{ showEnrollEndTime || "--" }}
             </div>
           </div>
           <div class="item">
             <div class="lable">{{ $t('marketing.commons.bmsh_7a0e14') }}</div>
             <div class="review">
               <div v-if="enrollSettingForm.enrollReview" class="member-map-btn-content">
                     <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none">
                       <path d="M8 15.3398C11.866 15.3398 15 12.2058 15 8.33984C15 4.47385 11.866 1.33984 8 1.33984C4.13401 1.33984 1 4.47385 1 8.33984C1 12.2058 4.13401 15.3398 8 15.3398ZM8 16.3398C3.58172 16.3398 0 12.7581 0 8.33984C0 3.92157 3.58172 0.339844 8 0.339844C12.4183 0.339844 16 3.92157 16 8.33984C16 12.7581 12.4183 16.3398 8 16.3398Z" fill="#30C776"/>
                       <path d="M4.14645 8.03429C3.95118 8.22955 3.95118 8.54614 4.14645 8.7414L6.62132 11.2163C6.81658 11.4115 7.13317 11.4115 7.32843 11.2163L12.1013 6.44341C12.2966 6.24815 12.2966 5.93156 12.1013 5.7363C11.906 5.54104 11.5895 5.54102 11.3942 5.73628L6.97487 10.1556L4.85355 8.03429C4.65829 7.83903 4.34171 7.83903 4.14645 8.03429Z" fill="#30C776"/>
                     </svg>
                     <span style="margin-left: 5px;">{{ $t('marketing.commons.ykq_9db7a8') }}</span>
                   </div>
                   <div v-else class="member-map-btn-content">
                     <span>{{ $t('marketing.commons.wkq_ea4a36') }}</span>
                   </div>
             </div>
           </div>
             <div class="item" v-if="isMemberOpen">
             <div class="lable">{{ $t('marketing.commons.hyyjbm_839991') }}</div>
             <div class="review">
               <p class="p1">{{ $t('marketing.pages.meeting_marketing.hyyjbmhhyx_c6127d', { data: ({ option0: poolTypeLabel }) }) }}</p>
               <div class="member-map-btn-wrap">
                   <p class="member-map-btn-text">{{ $t('marketing.commons.szxsys_4b462f') }}</p>
                   <div v-if="mappingIsSubmited" class="member-map-btn-content">
                     <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none">
                       <path d="M8 15.3398C11.866 15.3398 15 12.2058 15 8.33984C15 4.47385 11.866 1.33984 8 1.33984C4.13401 1.33984 1 4.47385 1 8.33984C1 12.2058 4.13401 15.3398 8 15.3398ZM8 16.3398C3.58172 16.3398 0 12.7581 0 8.33984C0 3.92157 3.58172 0.339844 8 0.339844C12.4183 0.339844 16 3.92157 16 8.33984C16 12.7581 12.4183 16.3398 8 16.3398Z" fill="#30C776"/>
                       <path d="M4.14645 8.03429C3.95118 8.22955 3.95118 8.54614 4.14645 8.7414L6.62132 11.2163C6.81658 11.4115 7.13317 11.4115 7.32843 11.2163L12.1013 6.44341C12.2966 6.24815 12.2966 5.93156 12.1013 5.7363C11.906 5.54104 11.5895 5.54102 11.3942 5.73628L6.97487 10.1556L4.85355 8.03429C4.65829 7.83903 4.34171 7.83903 4.14645 8.03429Z" fill="#30C776"/>
                     </svg>
                     <span style="margin-left: 5px;">{{ $t('marketing.commons.ysz_44e607') }}</span>
                   </div>
                   <div v-else class="member-map-btn-content">
                     <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none">
                       <path d="M8 15.3398C4.13401 15.3398 1 12.2058 1 8.33984C1 4.47385 4.13401 1.33984 8 1.33984C11.866 1.33984 15 4.47385 15 8.33984C15 12.2058 11.866 15.3398 8 15.3398ZM8 16.3398C12.4183 16.3398 16 12.7581 16 8.33984C16 3.92157 12.4183 0.339844 8 0.339844C3.58172 0.339844 0 3.92157 0 8.33984C0 12.7581 3.58172 16.3398 8 16.3398Z" fill="#FF522A"/>
                       <path d="M7.5 4.33984C7.5 4.0637 7.72386 3.83984 8 3.83984C8.27614 3.83984 8.5 4.0637 8.5 4.33984V9.33984C8.5 9.61599 8.27614 9.83984 8 9.83984C7.72386 9.83984 7.5 9.61599 7.5 9.33984V4.33984Z" fill="#FF522A"/>
                       <path d="M8.75 12.0898C8.75 12.5041 8.41421 12.8398 8 12.8398C7.58579 12.8398 7.25 12.5041 7.25 12.0898C7.25 11.6756 7.58579 11.3398 8 11.3398C8.41421 11.3398 8.75 11.6756 8.75 12.0898Z" fill="#FF522A"/>
                     </svg>
                     <span style="margin-left: 5px;">{{ $t('marketing.commons.wsz_fe2d26') }}</span>
                   </div>
                 </div>
             </div>
           </div>
         </div>
       </div>
       <CrmMappingDialog
         v-if="mappingVisible"
         :title="$t('marketing.commons.szhysjdxss_1b7f3a')"
         :objectName="$t('marketing.commons.hy_4d9dd5')"
         :targetObjectName="$t('marketing.commons.xs_ad46a9')"
         :showCrmDialog.sync="mappingVisible"
         :activityField="fields"
         :objectType.sync="mappingConfig.recordType"
         :mappingList.sync="mappingConfig.fieldMappings"
         :crm_showTips.sync="mappingShowTips"
         :crm_isSubmited.sync="mappingIsSubmited"
         objectApiName="LeadsObj"
         @submit="handleMappingSubmit"
       ></CrmMappingDialog>
     </div>
</template>

<script>
import http from "@/services/http/index";
import util from "@/services/util";
import Selectbar from "@/components/selectbar/index";
import StaffSelectorDialog from "@/modules/staff-selector-dialog/index";
import SelectorLine from "@/components/tags-selector-new/tags-line";
import tag from "./tag";
import SelectPersonDialog from "@/modules/select-person-dialog/select";
import StaffSelectorDialogBar from '@/modules/staff-selector-dialog-bar';
import { getStaffAndDepartByIdsMap } from '@/modules/staff-selector-dialog/tools.js';
import Dialog from "@/components/dialog";
// import PageRender from '@/components/Hexagon/PageRender'
import QuestionTooltip from '@/components/questionTooltip'
import CrmMappingDialog from "@/components/crm-mapping-dialog";
import { resolve } from 'tinymce/themes/silver/theme';


export default {
  components: {
    elForm: FxUI.Form,
    elFormItem: FxUI.FormItem,
    elTimePicker: FxUI.TimePicker,
    elSwitch: FxUI.Switch,
    elButton: FxUI.Button,
    ElSelect: FxUI.Select.components.ElSelect,
    ElOption: FxUI.Select.components.ElSelect.components.ElOption,
    Selectbar,
    StaffSelectorDialog,
    elCheckbox: FxUI.Checkbox,
    SelectorLine,
    SelectPersonDialog,
    StaffSelectorDialogBar,
    tag,
    Dialog,
    QuestionTooltip,
    CrmMappingDialog
  },
  props: {
    settingStatus: {
      type: Boolean,
      default: false
    },
    fromCreate:{
      type: Boolean,
      default: false
    },
    // 会议新建时 报名截止时间默认取值会议结束时间
    meetingInfo: {
      type: Object,
      default: ()=>({})
    }
  },
  data() {
    return {
      rules: {
        isCheckPerson: [
          { required: true, message: $t('marketing.commons.qxzshry_2a03bd'), trigger: "change" }
        ]
      },
      selectPersonDialogVisible: false,
      selectStatusList: [
        {
          value: 0,
          label: $t('marketing.commons.sstz_986fc5')
        },
        {
          value: 1,
          label: $t('marketing.commons.lxstzyc_8b640f')
        },
        {
          value: 2,
          label: $t('marketing.commons.essxstzyc_858693')
        }
      ],
      setMangeMode: false,
      enrollSettingForm: {
        formId: "",
        enrollEndTime: "",
        enrollReview: false,
        isCheckPerson: "",
        tagNameList: [],
        enrollPendingReviewTip: "",
        enrollReviewFailureTip: ""
      },
      enrollNoticePoint: 0,
      loading: false,
      selectResult: [],
      showReviewName: "",
      reviewPageDialogVisible: false,
      mappingVisible: false,
      mappingIsSubmited: false,
      mappingShowTips: false,
      mappingConfig: {
        leadPoolId: "",
        recordType: "",
        fieldMappings: [],
      },
      /**
       * 会议一键报名设置参数
       */
      pools: [],
      poolId: "",
      poolType: 2,
      PoolTypeOptions: [
        {
          label: $t('marketing.commons.xsc_7b62ce'),
          value: 1
        },
        {
          label: $t('marketing.commons.xsxs_d4ed8c'),
          value: 2
        }
      ],
      fields: []
    };
  },
  watch: {
    selectResult(val) {
      if (
        (val && val.depart && val.depart.length) ||
        (val && val.colleague && val.colleague.length)
      ) {
        
        this.enrollSettingForm.isCheckPerson = "isCheckPerson";
      } else {
        this.enrollSettingForm.isCheckPerson = "";
      }
      let showReviewName = "";
        val.depart.forEach(item => {
          showReviewName += $t('marketing.pages.meeting_marketing.&588b46', { data: ({ option0: item.name }) });
        });
        val.colleague.forEach(item => {
          showReviewName += $t('marketing.pages.meeting_marketing.&588b46', { data: ({ option0: item.name }) });
        });
        if (showReviewName.length) {
          showReviewName = showReviewName.substring(1);
        }
        this.showReviewName = showReviewName;
      if(this.$refs["enrollSettingForm"])
      this.$refs["enrollSettingForm"].validateField("isCheckPerson");
    },
    conferenceDetail: {
      handler() {
        this.init();
      },
      deep: true
    },
    "meetingInfo.endTime": {
      handler(val){
        if(this.fromCreate){
          this.init()
        }
      }
    },
    settingStatus(val){
      console.log('settingStatussettingStatus',val)
    }
  },
  computed: {
    showEnrollEndTime() {
      return (
        this.enrollSettingForm.enrollEndTime &&
        util.formatDateTime(
          this.enrollSettingForm.enrollEndTime,
          "YYYY-MM-DD hh:mm"
        )
      );
    },
    initFormName() {
      return `${this.$store.state.MeetingMarketing.conferenceDetail.title}${$t('marketing.commons.bd_eee1e2')}`;
    },
    conferenceDetail() {
      return this.$store.state.MeetingMarketing.conferenceDetail;
    },
    isMemberOpen() {
      return this.$store.state.Member.isOpenMember;
    },
    addressBookTypeNum() {
      return this.$store.state.Global.addressBookTypeNum;
    },
    isDingOpen() {
      return this.$store.state.Global.isDingOpen;
    },
    marketingEventId() {
      return this.conferenceDetail && this.conferenceDetail.marketingEventId;
    },
    poolTypeLabel(){
      const index = this.poolType - 1;
      return this.PoolTypeOptions[index].label
    }
  },
  methods: {
    async getPagesBySiteId(siteId) {
      const { errCode, data } = await http.getPagesBySiteId({ siteId });
      if (errCode === 0) {
        const homePage = data.filter(item => item.isHomepage === 1)[0]
          || data[0] || { content: '{name:"",components:[]}' }
          const content = JSON.parse(homePage.content) || {}
        return {
          title: content.name,
          content
        }
      }
    },
    handleStaffSelectorChange(res) {
      this.selectResult = res;
      const enrollCheckDepartment = this.selectResult.depart.map(item => item.id);
      const enrollCheckEmployee = this.selectResult.colleague.map(item => item.id);
      this.enrollCheckDepartment = enrollCheckDepartment.length > 0 ? enrollCheckDepartment : null;
      this.enrollCheckEmployee = enrollCheckEmployee.length > 0 ? enrollCheckEmployee : null;
      if (this.enrollCheckDepartment || this.enrollCheckEmployee) {
        this.enrollSettingForm.isCheckPerson = "isCheckPerson";
        this.$refs["enrollSettingForm"].validateField("isCheckPerson");
      }
    },
    handleCancel() {
      this.init();
      this.$emit('update:settingStatus', false);
    },
    handleCheck() {
      // 表单验证通过后更新会议报名设置
      this.$refs["enrollSettingForm"].validate(valid => {
        if(valid && this.mappingIsSubmited){
          Promise.all([
            this.upsertMemberMarketingEventCrmConfig(),
            this.updateConferenceEnrollSetting()
          ]).then(res => {
            this.$emit('update:settingStatus', false);
            FxUI.Message.success($t('marketing.commons.gxcg_55aa63'))
          }).catch(err => {
            FxUI.Message.error(err || $t('marketing.commons.bcsb_6de920'));
          });
        }
      });
    },
    removeItem(val) {
      let flag =
        (val && val.depart && val.depart.length) ||
        (val && val.colleague && val.colleague.length);
      if (!flag) {
        this.enrollSettingForm.isCheckPerson = "";
      }
    },
    informTimeChange(val) {
      this.enrollNoticePoint = val;
    },
    updateConferenceEnrollSetting() {
      //处理审核人
      let params = {
        enrollEndTime: this.enrollSettingForm.enrollEndTime,
        enrollReview: this.enrollSettingForm.enrollReview,
        id: this.conferenceId
      };
      if (params.enrollReview) {
        let _params = {
          enrollNoticePoint: this.enrollNoticePoint
        };
        if (this.enrollCheckEmployee != null) {
          _params[this.isDingOpen ? 'outEnrollCheckEmployee' : 'enrollCheckEmployee'] = this.enrollCheckEmployee;
        }
        if (this.enrollCheckDepartment != null) {
          _params.enrollCheckDepartment = this.enrollCheckDepartment;
        }
        _params.enrollPendingReviewTip = this.enrollSettingForm.enrollPendingReviewTip;
        _params.enrollReviewFailureTip = this.enrollSettingForm.enrollReviewFailureTip;
        params = _.extend(params, _params);
      }
      params.addressBookType = this.addressBookTypeNum;
      // if (true) {
      //   params.tagNameList = this.enrollSettingForm.tagNameList;
      // }
      this.loading = true;
      return new Promise((resolve, reject) => {
        http.updateConferenceEnrollSetting(params).then(
          res => {
            this.loading = false;
            if (res && res.errCode == 0) {
              resolve(res)
            } else {
              reject($t('marketing.commons.bcsb_6de920'));
            }
          },
          () => {
            this.loading = false;
            reject($t('marketing.commons.zs_132c5c'));
          }
        );
      });
    },
    init() {
      const results = this.conferenceDetail;
      this.conferenceId = results.id;
      this.resData = results;
      this.meetingDetailContent = results.conferenceDetails;
      this.startTime = util.formatDateTime(
        results.startTime,
        "YYYY-MM-DD hh:mm"
      );
      this.endTime = util.formatDateTime(results.endTime, "YYYY-MM-DD hh:mm");
      this.enrollSettingForm.formId = results.formId;
      this.enrollSettingForm.enrollEndTime = this.fromCreate ? this.meetingInfo.endTime :results.enrollEndTime;
      this.enrollSettingForm.enrollReview = results.enrollReview;
      this.enrollSettingForm.tagNameList = results.tagNameList;
      this.enrollSettingForm.enrollPendingReviewTip = results.enrollPendingReviewTip;
      this.enrollSettingForm.enrollReviewFailureTip = results.enrollReviewFailureTip;
      if (results.enrollReview) {
        this.enrollNoticePoint = results.enrollNoticePoint;
        this.enrollCheckEmployee = results[this.isDingOpen ? 'outEnrollCheckEmployee' : 'enrollCheckEmployee'];
        this.enrollCheckDepartment = results.enrollCheckDepartment || [];
        this.selectResult = getStaffAndDepartByIdsMap({
          colleague: this.enrollCheckEmployee,
          depart: this.enrollCheckDepartment,
        }, this.$store);
      }
      this.creator = results.creator;
    },
    handleValidateValid(){
      let validres = false
      this.$refs["enrollSettingForm"] && this.$refs["enrollSettingForm"].validate(valid=>{
        validres = valid
      })
      return validres
    },
    upsertMemberMarketingEventCrmConfig() {
      return new Promise((resolve, reject) => {
        http
          .upsertMemberMarketingEventCrmConfig({
            ...this.mappingConfig,
            marketingEventId: this.marketingEventId,
            objectType: 13,
            objectId: this.conferenceDetail.id
          })
          .then(({ errCode, errMsg }) => {
          if (errCode === 0) {
            resolve({
              errMsg,
              errCode
            })
          } else {
            reject(errMsg || $t('marketing.commons.bcsb_6de920'));
            }
          });
      });
    },
    getMemberMarketingEventCrmConfig() {
      //未开启会员不请求会员配置数据
      if(!this.isMemberOpen) return
      if(!this.marketingEventId) return;
      http
        .getMemberMarketingEventCrmConfig({
          marketingEventId: this.marketingEventId
        })
        .then(({ errCode, data }) => {
          this.loading = false;
          if (errCode === 0) {
            const { memberToLeadFieldMappings = [], leadPoolId, leadRecordType } = data || {};
            this.mappingConfig = {
              marketingEventId: this.marketingEventId,
              leadPoolId,
              fieldMappings: memberToLeadFieldMappings,
              recordType: leadRecordType || "",
            };
            if(leadPoolId){
              this.poolType = 1;
            }
            if(memberToLeadFieldMappings && memberToLeadFieldMappings.length){
              this.mappingIsSubmited = true;
            }
          }
        });
    },
    handlePoolTypeChange(val){
      if(val === 2){
        this.mappingConfig.leadPoolId = "";
      }
    },
    async handleMappingSetting() {
      await this.getCrmObjectFields();
      this.mappingVisible = true;
    },
    async getCrmObjectFields() {
      await http
        .getCrmObjectFields({
          objectApiName: 'MemberObj',
          recordType: "default__c"
        })
        .then(({ errCode, data }) => {
          if (errCode === 0) {
            this.fields = data || [];
          }
        });
    },
    queryListLeadPools() {
      //未开启会员不请求会员配置数据
      if(!this.isMemberOpen) return
      http.listLeadPools().then(
        ({ errCode, data }) => {
          if (errCode == 0) {
            this.pools = data.map(item => ({
              ...item,
              label: item.name,
              value: item.id
            })) || [];
          }
        },
        () => {
          alert($t('marketing.commons.xsclbhqsbq_ab1691'));
        }
      );
    },
    handleMappingSubmit(){
      this.isMappingSetting = true;
    },
    getManegeSettingSubmitedData(){
      const conferenceEnrollSetting = this.enrollSettingForm;
      // 会员映射
      const memberMapping = this.mappingConfig;
      console.log('memberMapping', memberMapping)
      return {
        conferenceEnrollSetting: {
          ...conferenceEnrollSetting,
          enrollCheckDepartment: this.enrollCheckDepartment || [],
          enrollCheckEmployee: this.enrollCheckEmployee || [],
          enrollNoticePoint: this.enrollNoticePoint,
          addressBookType: this.addressBookTypeNum
        },
        memberMapping
      }
    },
    queryMemberConfig() {
      if(!this.isMemberOpen) return
      http.getMemberConfig().then(({ errCode, data }) => {
        if (errCode === 0) {
          // memberToLeadFieldMappings没设置时为null  后台做了必填校验 所以这里需要处理初始值为空数组
          const { memberToLeadFieldMappings = [], leadPoolId, leadRecordType } = data || {};
          this.mappingConfig = {
            leadPoolId: leadPoolId || "",
            fieldMappings: memberToLeadFieldMappings || [],
            recordType: leadRecordType || "",
          };
          if(leadPoolId){
            this.poolType = 1;
          }
          if(memberToLeadFieldMappings && memberToLeadFieldMappings.length){
            this.mappingIsSubmited = true;
          }
        }
      });
    }
  },
  mounted() {},
  created() {
    this.queryListLeadPools();
    if(!this.fromCreate){
      this.init();
      this.getMemberMarketingEventCrmConfig();
    } else {
      this.queryMemberConfig()
    }
  },
  destroyed() {} //生命周期 - 销毁完成
};
</script>

<style lang="less" scoped>
.set-manage-wrapper__outer {
  padding-top: 20px;
  /deep/.el-form-item__content{
    line-height: 32px;
  }
  .set-manage-wrapper {
    background: #f4f8ff;
  }
  .title-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: 20px;
    .btn-set {
      margin-right: 39px;
      color: var(--color-info06,#407FFF);
      cursor: pointer;
    }
    .label {
      display: flex;
      .line {
        background-color: var(--color-primary06,#ff8000);
        width: 4px;
        height: 16px;
        margin-right: 20px;
      }
      .text {
        font-size: 14px;
        color: #181c25;
      }
    }
  }
  .set-manage {
    .form-wrapper {
      .el-form-item {
        margin-bottom: 20px;
        .select-range-bar {
          width: 330px;
          height: 36px;
          /deep/.selectbar-select-btn{
            line-height: 36px;
          }
          .person-btn-wrap li{
            height: 36px;
            line-height: 36px;
          }
          /deep/.btn-more{
            top: 5px;
          }
        }
        /deep/ .el-form-item__label{
          padding-left: 12px;
          height: 32px;
          
        }
        .el-form-item{
          /deep/ .el-form-item__label{
            padding-left: 10px;
          }
        }
      }
      .form-datepicker {
        margin-bottom: 0;
        padding-top: 0 !important;
      }
      .enrollReview {
        display: flex;
        align-items: center;
      }
      .enrollReview-text {
        font-size: 12px;
        color: #545861;
        margin-left: 10px;
      }
      .inform-select {
        width: 175px;
        margin-right: 10px;
      }
      .member-map-wrapper{
        .member-tips{
          color: var(--color-neutrals11);
          margin-bottom: 10px;
        }
        .leadpool-select{
          display: flex;
          gap: 12px;
          /deep/ .el-form-item{
            width: 50%;
            min-width: 300px;
            margin-bottom: 10px;
            .el-select{
              width: 100%;
            }
          }
        }
        .member-map-btn-wrap{
          margin-top: -10px;
          color: #181C25;
          font-size: 14px;
          display: flex;
          align-items: center;
          gap: 20px;
          .member-map-btn-content{
            display: flex;
            align-items: center;
          }
        }
      }
    }
  }
  .create-set-manege{
    .form-wrapper{
      .el-form-item{
        /deep/ .el-form-item__label{
          padding-left: 22px;
          height: 32px;
        }
      }
    }
  }

  .set-manage-done {
    padding-left: 12px;
    .item {
      display: flex;
      margin-bottom: 20px;
      .lable {
        font-size: 14px;
        color: #545861;
        width: 134px;
        flex: 0 0 134px;
      }
      .value {
        color: #15253b;
        font-size: 13px;
      }
      .review {
        display: flex;
        flex-direction: column;
        font-size: 14px;
        .member-map-btn-wrap{
          display: flex;
          .member-map-btn-text{
            margin-right: 20px;
          }
        }
        .member-map-btn-content{
          display: flex;
          align-items: center;
        }
        .p1 {
          color: var(--color-neutrals19);
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 20px; /* 142.857% */
          margin-bottom: 10px;
        }
      &:last-child{
        margin-bottom: 0;
      }
    }
  }
}

.reviewpage-dialog{
  .preview-con{
    display: flex;
    justify-content: space-between;
    padding-top: 5px;
  }
  .page-view{
    width: 375px;
    &-header{
      display: flex;
      justify-content: space-between;
      color: @color-title;
      .link{
        color: var(--color-info06);
      }
    }
    .page-render{
      border: 1px solid #DEE1E8;
      margin-top: 5px;
    }
  }
}
}
</style>
