<!-- 组件说明 -->
<template>
  <div :class="$style.site_preview_qrcode">
    <Popover
      :popper-class="$style.member__site_popover"
      width="240"
      trigger="click"
      :offset="90"
      @show="querySitePreview"
    >
      <div :class="$style.qrcode">
        <v-image
          :class="$style.qrcode_image"
          v-loading="previewLoading"
          :src="previewUrl"
        ></v-image>
        <div :class="$style.qrcode_desc">{{ $t('marketing.commons.wxsyszsjsy_02e704') }}</div>
      </div>
      <div slot="reference" :class="$style.btn">
        <i :class="['iconfont']" style="margin-right:6px;font-size:16px;"
          >&#xe69a;</i
        >{{ $t('marketing.pages.meeting_marketing.sjyl_806a04') }}
      </div>
    </Popover>
    <div :class="$style.btn" @click="handleSiteEdit">
      <i :class="['iconfont']" style="font-size:14px;margin-right:6px;"
        >&#xe643;</i
      >
      {{ $t('marketing.commons.bj_95b351') }}
    </div>
  </div>
</template>

<script>

import { querySitePreview } from "@/pages/member/components/api";
export default {
  components: {
Popover: FxUI.Popover,
VImage: FxUI.Image
},
  props: {
    siteId: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      previewLoading: false,
      previewUrl: ""
    };
  },

  computed: {
    conferenceDetail() {
      return this.$store.state.MeetingMarketing.conferenceDetail;
    },
    conferenceId() {
      return this.conferenceDetail.id;
    },
    marketingEventId() {
      return this.conferenceDetail.marketingEventDetail.marketingEventId;
    },
    openDingDing() {
      return this.$store.state.Global.openDingDing;
    },
  },
  methods: {
    handleSiteEdit() {
      this.$router.push({
        name: "site-design",
        params: {
          siteId: this.siteId
        },
        query: {
          conferenceId: this.conferenceId,
          marketingEventId: this.marketingEventId,
          templateType: 'activityComp',
          redirect: JSON.stringify({
            name: "meeting-setting",
            params: { id: this.conferenceId },
            query: {}
          })
        }
      });
    },
    querySitePreview() {
      if (!this.siteId || this.previewUrl) return;
      this.previewLoading = true;
      querySitePreview({
        type: 3,
        id: this.siteId,
        extraParam: {
          targetObjectId: this.conferenceDetail.id,
          targetObjectType: 13,
          marketingEventId: this.marketingEventId,
          hostType: this.openDingDing ?  'ding' : 'fs',
          ea: FS.contacts.getCurrentEmployee().enterpriseAccount,
        }
      }).then(data => {
        this.previewLoading = false;
        this.previewUrl = data.h5QRUrl || "";
      });
    }
  },
  mounted() {},
  created() {},
  destroyed() {} //生命周期 - 销毁完成
};
</script>

<style lang="less" module>
.member__site_popover {
  .qrcode {
    text-align: center;
    padding: 28px 0;
    &_image {
      width: 100px;
      height: 100px;
    }
    &_desc {
      font-size: 12px;
      text-align: center;
      color: @color-subtitle;
      margin-top: 6px;
    }
  }
}
.site_preview_qrcode {
  display: flex;
  padding-left: 20px;

  .btn {
    margin-right: 23px;
    font-size: 13px;
    color: #545861;
    cursor: pointer;
    height: 24px;
    line-height: 24px;
  }
}
</style>
