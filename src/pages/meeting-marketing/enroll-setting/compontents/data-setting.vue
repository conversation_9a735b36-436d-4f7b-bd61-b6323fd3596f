<!-- 组件说明 -->
<template>
     <div class="data-setting-wrapper__outer">
      <div class="km-g-loading-mask" v-if="formLoading">
        <span class="loading"></span>
      </div>
       <siteSettingContent
          ref="siteSettingContent"
          v-else
          :form-id="formId"
          :site-id="siteId"
          :form-usage="formUsage"
          :show-activity-member-set="true"
          :show-apply-object="!fromCreate"
          :fromCreate="fromCreate"
          :site-setting-type="1"
          :is-show-tag-setting="false"
        />
     </div>
</template>

<script>
import _ from 'lodash'
import http from '@/services/http/index.js'
import siteSettingContent from '@/pages/site/site-setting-dialog/content.vue'
import { mapActions, mapState } from 'vuex'
export default {
  components: {
    siteSettingContent
  },
  props: {
    saveStatus: {
      type: Boolean,
      default: false
    },
    formId: {
      type: String,
      default: ''
    },
    fromCreate: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      componentId: 'form-data-setting',
      formLoading: true
    }
  },
  computed: {
    conferenceDetail() {
      return this.$store.state.MeetingMarketing.conferenceDetail
    },
    marketingEventId() {
      return this.conferenceDetail.marketingEventDetail && this.conferenceDetail.marketingEventDetail.marketingEventId || ''
    },
    siteId() {
      return this.conferenceDetail.activityDetailSiteId || ''
    }
  },
  watch: {
    formId: {
      handler(val) {
        if (val) {
          this.formLoading = false
        }
      },
      immediate: true
    }
  },
  created() {
    this.registerComponent(this.componentId)
  },
  destroyed() {},
  methods: {
    ...mapActions('meetingFormSetting', [
      'registerComponent',
      'markComponentSaved',
      'saveError',
      'saveComplete'
    ]),
    updateFormDataDetail() {
      const submitData = this.getSubmitData()
      return new Promise((resolve, reject) => {
        http
          .updateFormDataDetail(submitData)
          .then(({ errCode, data, errMsg }) => {
            if (errCode === 0) {
              resolve()
            } else {
              if (errCode === 20001) {
                FxUI.Message.error(errMsg)
                reject(20001)
              }
              reject()
            }
          })
      })
    },
    getSubmitData() {
      const submitData = this.$refs.siteSettingContent.getSubmitData()
      return submitData
    },
    handleSetMemberCheckType() {
      const submitData = this.getSubmitData()
      return new Promise((resolve, reject) => {
        http
          .updateFormDataMoreSetting({
            id: this.formId,
            memberCheckType: submitData.moreSetting.memberCheckType,
          })
          .then(({ errCode, data }) => {
            if (errCode === 0) {
              resolve()
            }
          })
      })
    },
    async handleSave() {
      const isValid = await this.$refs.siteSettingContent.validate()
      if (!isValid) return
      Promise.all([this.updateFormDataDetail(), this.handleSetMemberCheckType()])
        .then(([updateFormDataDetail, handleSetMemberCheckType]) => {
          if (updateFormDataDetail && handleSetMemberCheckType) {
            this.markComponentSaved(this.componentId)
            FxUI.Message.success($t('marketing.commons.czcg_33130f'))
          }
        })
        .catch(err => {
            this.saveError({
              componentId: this.componentId,
              error: err
            })
        })
    },
  }, // 生命周期 - 销毁完成
}
</script>

<style lang="less" scoped>
.data-setting-wrapper__outer {
  padding-left: 12px;
  position: relative;
  /deep/.el-form-item__label {
    color: #545861;
  }
  /deep/ .el-form-item{
    margin-bottom: 20px;
  }
  /deep/ .w-other-info{
    font-size: 14px !important;
    .w-setting {
      border-bottom: none;
      .setting-area__name{
        width: 130px !important;
        font-size: 14px !important;
      }
    }
  }
  .form-wrapper {
    padding-top: 20px;
    .v-crmset-wrapper {
      background-color: #f4f8ff;
      .field-property__crm {
        position: relative;
      }
      .crm__leadPool {
        position: absolute;
      }
      .leadPool__title {
        display: none;
      }
    }
    .split {
      margin: 0 25px;
      border: 1px dotted #e9edf5;
    }
    .form_setting_btn {
      font-size: 14px;
      padding: 5px 0;
      /deep/.el-icon-circle-check {
        color: #7fc25d !important;
      }
      /deep/.el-icon-warning-outline {
        color: #ff405d;
      }
    }
    .radio-group {
      display: flex;
      flex-direction: column;

      /deep/ .el-radio {
        margin-bottom: 4px;
        line-height: 32px;
      }
    }
    .form_item {
      &.row {
        display: flex;
        align-items: center;
      }
      &_title {
        color: @color-subtitle;
        margin-right: 32px;
        font-size: 13px;
      }
    }
    .form-map-item{
      display: flex;
      align-items: center;
      color: #181c25;
      .form-map-item-content{
        display: flex;
        align-items: center;
        margin-left: 30px;
      }
    }
    ::v-deep .el-form-item__label-wrap {
      margin-left: 0 !important;
      margin-right: 15px;
      min-width: 130px;
      padding-left: 0;
    }
  }
  .title-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: 20px;
    .btn-set {
      margin-right: 39px;
      color: var(--color-info06,#407FFF);
      cursor: pointer;
    }
    .label {
      display: flex;
      .line {
        background-color: var(--color-primary06,#ff8000);
        width: 4px;
        height: 16px;
        margin-right: 20px;
      }
      .text {
        font-size: 14px;
        color: #181c25;
      }
    }
  }

  .set-manage-done-wrapper {
    /deep/.el-form-item__content {
      line-height: 34px;
    }
  }
  .merber-setting {
    font-size: 0px;
  }
}
</style>
