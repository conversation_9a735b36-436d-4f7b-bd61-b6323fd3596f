<template>
    <div class="meeting-tag-setting-wrapper">
      <div class="tag-setting-content">
        <div class="tag-setting-title">
          {{ $t("marketing.commons.tjbq_736eaa") }}
        </div>
        <div class="tag-setting-item-list">
          <div class="tag-setting-content-item">
            <p class="item-title">
              {{ $t("marketing.pages.meeting_marketing.gtjbdztjbq_c8fd2e") }}
            </p>
            <selector-line
              v-model="enrollSettingForm.submittedTagNameList"
              style="line-height: 26px;"
            ></selector-line>
          </div>
          <div class="tag-setting-content-item">
            <p class="item-title">
              {{ $t("marketing.pages.meeting_marketing.gfwhybmzyz_961883") }}
            </p>
            <selector-line
              v-model="enrollSettingForm.viewTagNameList"
            ></selector-line>
          </div>
        </div>
      </div>
    </div>
</template>

<script>
import SelectorLine from "@/components/tags-selector-new/tags-line.vue";
import { mapState, mapActions } from 'vuex';
import http from '@/services/http/index.js'
export default {
  name: 'TagSetting',
  components: {
    SelectorLine
  },
  data() {
    return {
      enrollSettingForm: {
        viewTagNameList: [],
        submittedTagNameList: []
      },
      componentId: 'tag-setting'
    };
  },
  computed: {
    ...mapState('meetingFormSetting', ['isSaving']),
    conferenceDetail() {
      return this.$store.state.MeetingMarketing.conferenceDetail;
    }
  },
  created() {
    this.registerComponent(this.componentId);
  },
  methods: {
    ...mapActions('meetingFormSetting', [
      'registerComponent',
      'markComponentSaved',
      'saveError'
    ]),
    async handleSave() {
      try {
        const params = {
          conferenceId: this.conferenceDetail.conferenceId,
          hexagonId: this.conferenceDetail.activityDetailSiteId,
          browseTagNameList: this.enrollSettingForm.viewTagNameList,
          enrollTagNameList: this.enrollSettingForm.submittedTagNameList
        };
        const res = await http.updateConferenceTag(params);
        if (res.errCode === 0) {
          await this.markComponentSaved(this.componentId);
        } else {
          await this.saveError({
            componentId: this.componentId,
            error: res.errMsg || $t('marketing.commons.bcsb_6de920')
          });
        }
      } catch (error) {
        await this.saveError({
          componentId: this.componentId,
          error
        });
      }
    },
    handleCancel() {
      this.$emit("update:settingStatus", false);
    },
    getTagList() {
      this.enrollSettingForm.viewTagNameList = this.conferenceDetail.tagNameList;
      this.enrollSettingForm.submittedTagNameList = this.conferenceDetail.tagNameList;
    }
  },
  mounted() {
    this.getTagList();
  }
};
</script>

<style lang="less" scoped>
.meeting-tag-setting-wrapper {
  .tag-setting-content {
    padding-top: 20px;
    padding-left: 12px;
    font-size: 14px;
    display: flex;
    .item-title {
      color: #181c25;
    }
    .tag-setting-title {
      width: 130px;
      color: var(--Text-H2, #545861);
      font-size: 14px;
      line-height: 20px;
    }
    .tag-list {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
      margin-top: 6px;
    }
    .tag-setting-content-item {
      &:last-child {
        .item-title {
          margin-top: 20px;
        }
      }
    }
    .tag-item-text {
      display: inline-block;
      padding: 2px 4px;
      border-radius: 2px;
      background: var(--Dark-Blue-01, #e6f4ff);
      color: var(----Dark-Blue-06-, #0c6cff);
      font-size: 12px;
      line-height: 18px;
    }
    /deep/ .tags-line {
      .tags__item {
        margin-top: 6px;
        &.el-tag--info {
          background-color: #e6f4ff;
          color: #0c6cff;
          border-color: #e6f4ff;
          border-radius: 2px;
        }
      }
    }
  }
  .tag-setting-footer {
    margin-left: 142px;
    margin-top: 20px;
  }
}
</style>
