<!-- 组件说明 -->
<template>
     <div class="meeting-marketing__enroll-setting-wrapper">
       <!-- 左侧预览区域 -->
       <div class="left">
         <div class="title">
           {{ $t('marketing.commons.yl_645dbc') }}
         </div>
         <div class="site-preview__wrapper">
          <div class="site-preview__page-header">
            <img src="@/assets/images/phoneHeaderNew.jpg" alt="phone-header" class="site-preview__page-phone-header">
            <div class="site-preview__page-header-title">
              <!-- 活动标题 -->
              {{ previewData.title || $t('marketing.commons.bmbd_112a9c') }}
            </div>
          </div>
          <page-render
           class="page-render"
           :title="previewTitle"
           :data="previewData"
           :scroll-height="609"
         />
        </div>
       </div>
       <div class="right enroll-setting-content__wrapper">
         <div :class="['enroll-item',(item.isSetting && !fromCreate) && 'enroll-item-setting']" v-for="(item, index) in enrollSettingList" :key="index">
           <div class="item-title">
             <div class="title-left">
               {{ item.title }}
             </div>
             <div v-if="!item.isSetting" class="title-right" @click="item.isSetting = true">
               <fx-button size="small" type="text"  icon="fx-icon-comment">{{ $t('marketing.commons.bj_95b351') }}</fx-button>
             </div>
           </div>
           <div class="item-content">
             <div :class="['item-content__item']">
               <!-- 报名管理设置 -->
               <component
                 :is="item.component"
                 ref="settingComponents"
                 :fromCreate="fromCreate"
                 :formId="formTemplateId"
                 :siteId="conferenceHexagonSiteId || activityDetailSiteId"
               />
             </div>
           </div>
         </div>
       </div>
     </div>
</template>

<script>
import PageRender from '@/components/Hexagon/PageRender'
import http from '@/services/http/index.js'
import dataSetting from './compontents/data-setting.vue'
import sitePreviewCode from './compontents/site-preview-qrcode.vue'
import QuestionTooltip from '@/components/questionTooltip/index.vue'
import TagSetting from './compontents/tag-setting.vue'
import { mapState, mapActions } from 'vuex';

export default {
  name: 'EnrollSetting',
  props: {
    fromCreate: {
      type: Boolean,
      default: false
    },
    formId: {
      type: String,
      default: ''
    },
    previewData: {
      type: Object,
      default: ()=>{}
    },
    previewTitle: {
      type: String,
      default: ''
    }
  },
  components: {
    PageRender,
    dataSetting,
    sitePreviewCode,
    ElCheckbox: FxUI.Checkbox,
    QuestionTooltip,
    TagSetting,
  },
  data() {
    return {
      loading: false,
      enrollSettingList: [
        {
          title: $t('marketing.commons.bmsjsz_d6a52f'),
          component: 'dataSetting',
          isSetting: this.fromCreate,
        },
        {
          title: $t('marketing.commons.tjbq_736eaa'),
          component: 'TagSetting',
          isSetting: this.fromCreate,
        },
      ],
      formTemplateId: '',
      previewFormTitle: '',
      previewFormData: {},
    };
  },
  computed: {
    ...mapState('meetingFormSetting', ['isSaving']),
    conferenceDetail() {
      return this.$store.state.MeetingMarketing.conferenceDetail;
    },
    marketingEventId() {
      return this.conferenceDetail.marketingEventDetail.marketingEventId;
    },
    addressBookTypeNum() {
      return this.$store.state.Global.addressBookTypeNum;
    },
    activityDetailSiteId() {
      return this.conferenceDetail.activityDetailSiteId;
    }
  },
  watch: {
    isSaving: {
      handler(val) {
        if (val) {
          this.$refs.settingComponents.forEach(component => {
            if (component && typeof component.handleSave === 'function') {
              component.handleSave();
            }
          });
        }
      },
      immediate: true
    }
  },
  created() {
    this.getPagesBySiteId();
  },
  methods: {
    async getPagesBySiteId() {
      const siteId = this.conferenceHexagonSiteId || this.activityDetailSiteId;
      siteId && http.getPagesBySiteId({ siteId }).then(res => {
        const { errCode } = res;
        const { data } = res;
        if (errCode === 0) {
          const formPage = data.filter(item => item.formId)[0] || { content: '{formId:"",name:"",components:[]}' };
          this.formTemplateId = formPage.formId;
          this.previewFormTitle = JSON.parse(formPage.content).name;
          this.previewFormData = JSON.parse(formPage.content) || {};
        }
      });
    },
  }
};
</script>

<style lang="less" scoped>
.meeting-marketing__enroll-setting-wrapper {
  display: flex;
  gap: 12px;
  flex: 1;
  .left{
    padding: 24px;
    border-radius: @border-radius-base;
    background: #fff;
    .title{
      font-size: 14px;
      color: var(--Text-H1, #181C25);
      font-weight: 400;
      line-height: 20px;
      margin-bottom: 20px;
    }
    .site-preview__wrapper{
      box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.15);
      .site-preview__page-header{
          position: relative;
          width: 375px;
          .site-preview__page-phone-header{
            width: 100%;
            height: 100%;
          }
        .site-preview__page-header-title{
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translateX(-50%);
            font-size: 16px;
            color: #181C25;
            font-weight: bold;
          }
      }
    }
    .page-render{
      width: 375px;
    }
    .site-tools{
      margin-top: 20px;
    }
    /deep/.site-tools-qrcode{
      .site_preview_qrcode{
        justify-content: center;
      }
    }
  }
  .enroll-setting-content__wrapper{
    flex: 1;
    background: #fff;
    border-radius: @border-radius-base;
    overflow: auto;
    .enroll-item{
      padding: 20px;
      border-bottom: 1px dashed var(--color-neutrals05);;
     .item-title{
      display: flex;
      justify-content: space-between;
      height: 24px;
      .title-left{
        position: relative;
        padding-left: 12px;
        color: var(--color-neutrals19);
        font-size: 16px;
        font-weight: bold;
        line-height: 24px;
        &::before{
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          display: inline-block;
          width: 4px;
          height: 16px;
          background: var(--FX-Orange, #FF8000);
          border-radius: 1px;
          transform: translateY(4px);
        }
      }
      .title-right{
        color: var(--color-info06, #407FFF);
      }
     }
     &:last-child{
      border-bottom: none;
     }
    }
    .enroll-item-setting{
      background-color: #f4f8ff;
    }
  }
}
</style>
