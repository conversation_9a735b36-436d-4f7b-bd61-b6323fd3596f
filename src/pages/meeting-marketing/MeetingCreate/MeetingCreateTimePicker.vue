<template>
  <div class="MeetingCreateTimePicker">
    <div class="MeetingCreateTimePicker__item">
      <fx-date-picker
        type="datetime"
        :placeholder="$t('marketing.commons.kssj_592c59')"
        format="yyyy-MM-dd HH:mm"
        value-format="timestamp"
        default-time="09:00:00"
        :hasFormItem="false"
        v-model="pickTime.startTime"
        @change="val => handleTimeChange('start', val)"
        style="width: 100%;"
      ></fx-date-picker>
    </div>
    <div class="MeetingCreateTimePicker__split">{{ $t('marketing.commons.z_981cbe') }}</div>
    <div class="MeetingCreateTimePicker__item">
      <fx-date-picker
        type="datetime"
        :placeholder="$t('marketing.commons.jssj_f78277')"
        format="yyyy-MM-dd HH:mm"
        value-format="timestamp"
        default-time="09:00:00"
        :hasFormItem="false"
        v-model="pickTime.endTime"
        @change="val => handleTimeChange('end', val)"
        :picker-options="endTimePickerOptions"
        style="width: 100%;"
      ></fx-date-picker>
    </div>
  </div>
</template>

<script>

export default {
  props: {
    value: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  watch: {
    pickTime: {
      deep: true,
      handler() {
        console.log('pickTime change', this.pickTime);
        this.$emit('input', this.pickTime);
      },
    },
    value: {
      deep: true,
      handler() {
        if (this.pickTime.startTime !== this.value.startTime && this.pickTime.endTime !== this.value.endTime) {
          this.pickTime = this.value;
        }
      },
    },
  },
  data() {
    return {
      pickTime: {
        startTime: '',
        endTime: '',
      },
      startTimePickerOptions: {
        // disabledDate: (date) => {
        //   const newDate = new Date();
        //   const newStartTime = new Date(newDate.getFullYear(), newDate.getMonth(), newDate.getDate());
        //   if (date < newStartTime) {
        //     return true;
        //   }
        //   return false;
        // },
      },
      endTimePickerOptions: {
        disabledDate: (date) => {
          const { startTime, endTime } = this.pickTime;
          if (startTime) {
            const newDate = new Date(startTime);
            const newStartTime = new Date(newDate.getFullYear(), newDate.getMonth(), newDate.getDate());
            if (date < newStartTime) {
              return true;
            }
          } 
          return false;
        },
      },
    }
  },
  methods: {
    handleTimeChange(type, val) {
      const { startTime, endTime } = this.pickTime;
      const disTime = 2 * 3600000;
      // 清空操作
      if ((type === 'start' && !startTime) || (type === 'end' && !endTime)) return
      if (type === 'start' && !endTime) {
        this.pickTime.endTime = val + disTime;
      } else if (type === 'end' && !startTime) {
        this.pickTime.startTime = val - disTime;
      }
      if(this.pickTime.endTime <= this.pickTime.startTime){
        if(type === "start"){
          this.pickTime.endTime = this.pickTime.startTime + disTime
        }else{
          this.pickTime.startTime = this.pickTime.endTime - disTime
        }
      }
    },
  },
  mounted() {
    this.pickTime = { 
      startTime: '', 
      endTime: '',
      ...this.value,
    };
  },
}
</script>

<style lang="less" scoped>
.MeetingCreateTimePicker {
  width: 100%;
  display: flex;
  align-items: center;
  .MeetingCreateTimePicker__item {
    flex: 1;
    /deep/ .el-form-item__content{
      .el-date-editor{
        width: 100%;
      }
    }
  }
  .MeetingCreateTimePicker__split {
    margin: 0 10px;
  }
}

</style>