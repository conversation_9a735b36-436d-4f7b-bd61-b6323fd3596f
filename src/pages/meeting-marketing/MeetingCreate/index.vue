<template>
      <div class="MeetingCreate__wrapper">
        <div class="MeetingCreate__header">
          <MeetingHeader
            :title="$route.query.id ? $t('marketing.pages.meeting_marketing.bjhy_bb2cbf') : $t('marketing.commons.xjhy_fe58e6')"
            :steps="meetingSteps"
            :currentStep="currentStep"
            :buttons="operateButtons[(currentStep - 1)]"
            @button-click="handleButtonClick"
            @step-change="(val)=>currentStep = val"
          />
        </div>
        <div class="MeetingCreate__body" v-if="currentStep === 1">
          <div class="MeetingCreate__left">
            <!-- 预览区域 -->
            <div class="MeetingCreate__preview">
              <div v-if="!hexagonLoading" class="MeetingCreate__preview__content">
                <SitePreview
                  :title="$t('marketing.pages.meeting_marketing.hyzyyl_ef00cd')"
                  :previewData="templatePreviewData"
                  :previewTitle="templatePreviewTitle"
                >
                  <template #title-tooltip>
                    <QuestionTooltip
                      effect="dark"
                      :offset="192"
                      style="margin:2px 0 0 4px;"
                    >
                      <span
                        slot="question-content"
                      >
                        {{ $t('marketing.pages.meeting_marketing.cjhycghxtj_fcd92f') }}
                      </span>
                    </QuestionTooltip>
                  </template>
                </SitePreview>
              </div>
            </div>
          </div>
            <div class="MeetingCreate__right">
              <div class="MeetingCreate__form">
                <div class="MeetingInfo__title">
                  {{ $t('marketing.commons.jbxx_9e5ffa') }}
                </div>
                <div class="MeetingInfo__wrapper">
                  <meeting-info-form
                    :meeting-info="formData"
                    :settingStatus="true"
                    :fromCreate="true"
                    @updateForm="handleUpdateForm"
                    ref="meetingInfoForm"
                  />
                </div>
              </div>
              <div class="MeetingCreate__form">
                <div class="MeetingInfo__title">
                  {{ $t('marketing.commons.bmsz_381009') }}
                </div>
                <div class="MeetingInfo__wrapper" style="padding-left: 0;padding-top: 0;">
                  <manageSetting ref="manageSetting" :meeting-info="formData" :settingStatus="true" :fromCreate="true" />
                  <!-- 会员报名设置 -->
                </div>
              </div>
              <meeting-crm-filed
                ref="crmFiled"
                :hideFields="hideFields"
                :fieldData="crmFiledData"
              ></meeting-crm-filed>
          </div>
        </div>
        <MeetingEnrollSetting  
          :previewData="previewFormData"
          :previewTitle="previewFormTitle"
          :saveStatus="saveStatus"
          v-if="currentStep === 2"
          :formId="formTemplateId"
          ref="meetingEnrollSetting"
          :fromCreate="true" />
      </div>
</template>

<script>
import PageHeader from "@/components/page-header";
import MarketingActivityCoverUploader from "@/components/MarketingActivityCoverUploader";
import MeetingTinymceEditor from "./MeetingTinymceEditor";
import MeetingCreateTimePicker from "./MeetingCreateTimePicker";
import MeetingCrmFiled from "@/components/meeting-crm-filed/index";
import http from "@/services/http/index";
import PictureCutter from "@/components/picture-cutter";
import MeetingCoverImageDialog from "@/components/MarketingActivityCoverUploader/MeetingCoverImageDialog";
import QuestionTooltip from "@/components/questionTooltip/index";
import PictureStandard from "@/components/PictureStandard/index";
import MeetingInfoForm from "./MeetingInfo/MeetingInfoForm.vue";
import SitePreview from "./SitePreview.vue";
import MeetingHeader from "@/components/page-header/crumbs.vue";
import MeetingEnrollSetting from '@/pages/meeting-marketing/enroll-setting/index.vue'
import manageSetting from '../enroll-setting/compontents/manage-setting.vue'
import { parseMarketingComp } from '@/utils/parseMarketingComp.js'
import { mapState, mapActions, mapGetters } from 'vuex';


export default {
  components: {
    Col: FxUI.Col,
    ElForm: FxUI.Form,
    ElFormItem: FxUI.FormItem,
    ElDatePicker: FxUI.DatePicker,
    PageHeader,
    MarketingActivityCoverUploader,
    MeetingTinymceEditor,
    MeetingCreateTimePicker,
    MeetingCrmFiled,
    PictureCutter,
    MeetingCoverImageDialog,
    ElSwitch: FxUI.Switch,
    Input: FxUI.Input,
    ElSelect: FxUI.Select.components.ElSelect,
    ElOption: FxUI.Select.components.ElSelect.components.ElOption,
    QuestionTooltip,
    ElButton: FxUI.Button,
    PictureStandard,
    MeetingInfoForm,
    SitePreview,
    MeetingHeader,
    MeetingEnrollSetting,
    manageSetting
    },
  data() {
    return {
      hideFields: [
        "begin_time",
        "end_time",
        "location",
        "name",
        "event_type",
        "owner"
      ],
      meetingId: "",
      submiting: false,
      crmFiledData: {},
      formData: {
        title: "",
        coverImagePath: "",
        startEndTime: {},
        startTime: "",
        endTime: "",
        location: "",
        conferenceDetails: "",
        updateConferenceDetails: true,
        updateCoverImage: true,
        showActivityList: false,
        showMap: false,
        mapAddress: "",
        mapLocation: {
          lng: "",
          lat: ""
        },
        marketingTemplateId: "",
        originalImageAPath: "",
        cutOffsetList: null,
        isMobileDisplay: true,
        eventType: '',
      },
      isMobileDisplayLoading: false,
      showEditor: true,
      coverImage: null,
      meetingFormTemplateList: [],
      hexagonLoading: false,
      meetingSteps: [
         { title: $t('marketing.pages.meeting_marketing.txhyjcxx_ecaeb9')},
        { title: $t('marketing.commons.hybmsz_6be8e9') } 
      ],
      currentStep: 1,
      operateButtons: [
        [
          { 
            text: $t('marketing.pages.meeting_marketing.bcbxyb_bed196'), 
            type: 'primary',
            action: 'saveAndNext',
            loading: false
          },
          { 
            text: $t('marketing.commons.qx_625fb2'),
            plain: true,
            action: 'cancel' 
          }
        ],
        [
          { 
            text: $t('marketing.pages.meeting_marketing.wcsz_647db6'), 
            type: 'primary',
            disabled: false, 
            loading: false,
            action: 'saveFormSetting' 
          },
          { 
            text: $t('marketing.pages.meeting_marketing.tgshsz_b43d62'),
            plain: true,
            action: 'jump' 
          }
        ]
      ],
      formTemplateId: '',
      templatePreviewTitle: '',
      previewFormTitle: '',
      previewFormData: {},
      templatePreviewData: {},
      saveStatus: false,
    };
  },
  watch: {
    'formData.startEndTime': {
      deep: true,
      handler() {
        this.formData.startTime = this.formData.startEndTime.startTime;
        this.formData.endTime = this.formData.startEndTime.endTime;
        this.formatPreviewData();
      }
    },
    'formData.marketingTemplateId': {
      handler(val) {
        this.getPreviewData(val)
      }
    },
    'formData.coverImagePath': {
      handler() {
        this.formatPreviewData();
      }
    },
    'formData.title': {
      handler() {
        this.formatPreviewData();
      }
    },
    'formData.conferenceDetails': {
      handler() {
        this.formatPreviewData();
      }
    },
    'formData.location': {
      handler() {
        this.formatPreviewData()
      }
    }
  },
  computed: {
    ...mapGetters('meetingFormSetting', ['areAllComponentsSaved','saveError']),
    conferenceDetail() {
      return this.$store.state.MeetingMarketing.conferenceDetail;
    },
  },
  methods: {
    ...mapActions('meetingFormSetting', [
      'startSave',
      'saveComplete',
      'saveError'
    ]),
    formatPreviewData(){
      const {
        title,conferenceDetails,cutOffsetList,location,showMap
      } = this.formData
      if (!this.templatePreviewData.components) return;
      const data = parseMarketingComp({
        ...this.formData,
        description: conferenceDetails,
        cover: (cutOffsetList && cutOffsetList.length) ? cutOffsetList[0].image : '' ,
        name: title,
        location: location,
        isShowMap: showMap
      },this.templatePreviewData);
      this.templatePreviewData = data
    },
    showMessageWarning() {
      let errorNode1 = $(".el-form-item__error");
      if (errorNode1.length) {
        let message = errorNode1[0].innerHTML;
        FxUI.Message.error(message);
        return;
      }
      //市场活动必填提示
      let errorNode = $(".fm-error, .ui-error");
      if (errorNode.length) {
        let message = errorNode[0].innerHTML;
        FxUI.Message.error(message);
      }
    },
    queryMarketingEventDetail(id) {
      FS.util.FHHApi(
        {
          url: "/EM1HNCRM/API/v1/object/MarketingEventObj/controller/Detail",
          data: {
            objectDescribeApiName: "MarketingEventObj",
            objectDataId: id
          },
          success: ({ Result: { StatusCode }, Value }) => {
            if (StatusCode == 0) {
              this.crmFiledData = Value.data;
              this.formData.isMobileDisplay = Value.data && Value.data.is_mobile_display === "0";
              this.isMobileDisplayLoading = false;
            }
          }
        },
        {
          errorAlertModel: 1
        }
      );
    },
    async loadMeetingData() {
      if (this.meetingId) {
        this.isMobileDisplayLoading = true;
        await this.$store.dispatch("queryConferenceDetail", {
          conferenceId: this.meetingId
        });
        const d = this.conferenceDetail;
        this.queryMarketingEventDetail(d.marketingEventId);
        this.formData = {
          ...this.formData,
          title: d.title,
          coverImagePath: d.coverImageUrl,
          startEndTime: {
            startTime: d.startTime,
            endTime: d.endTime
          },
          startTime: d.startTime,
          endTime: d.endTime,
          location: d.location,
          conferenceDetails: d.conferenceDetails,
          updateConferenceDetails: true,
          updateCoverImage: true,
          showActivityList: d.showActivityList,
          mapAddress: d.mapAddress,
          mapLocation: d.mapLocation || {},
          showMap: !!(d.mapLocation && d.mapLocation.lng),
          eventType: d.marketingEventDetail && d.marketingEventDetail.eventType
        };
        this.formData.showActivityList = d.conferenceDetails
          ? d.showActivityList
          : true;
        this.coverImage = d.coverImageUrl;
      }
    },
    async createOrUpdateConference(extraParams = {}) {
      this.submiting = true;
      this.formData.parentId = this.$route.query.parent_id || ''
      let params = JSON.parse(JSON.stringify(this.formData));
      //将crm布局中的必填字段传入后台
      params.createObjectDataModel = { objectData: this.crmFiledData };
      params.conferenceDetails = params.conferenceDetails.replace(/\n/g, "");
      params.updateConferenceDetails = true
      delete params.startEndTime;
      if (this.meetingId) {
        params.conferenceId = this.meetingId;
      }
      const res = await YXT_ALIAS.http.createOrUpdateConference({
        ...params,
        ...extraParams
      });
      this.submiting = false;
      if (res && res.errCode === 0) {
        this.meetingId = res.data.conferenceId
        this.conferenceHexagonSiteId = res.data.conferenceHexagonSiteId
        this.$store.dispatch('updateConferenceDetail', {
          conferenceDetail: {
            ...params,
            activityDetailSiteId: res.data.conferenceHexagonSiteId,
            conferenceId: res.data.conferenceId
          },
        })
        if( this.$route.query.isSub ) {
          this.$router.back()
        }
        return true
      }
      return false
    },
    async handleSend() {
      try {
        // 三个校验
        const crmFiledData = this.$refs.crmFiled.submit();
        const enrollSettigValid = this.$refs.manageSetting.handleValidateValid();
        const valid = await this.$refs.meetingInfoForm.validate();
        // 显示警告信息
        setTimeout(() => {
          this.showMessageWarning();
        });

        // 校验不通过直接返回false
        if (!crmFiledData || !valid || !enrollSettigValid) {
          return false;
        }

        // 设置loading状态
        this.operateButtons[0][0].loading = true;

        // 填充CRM数据
        const filledCrmData = {
          ...crmFiledData,
          begin_time: this.formData.startEndTime.startTime,
          end_time: this.formData.startEndTime.endTime, 
          location: this.formData.location,
          name: this.formData.title,
          event_type: this.formData.eventType, // 活动类型3 会议销售
          owner: [FS.contacts.getCurrentEmployee().id + ""],
          is_mobile_display: this.formData.isMobileDisplay ? "0" : "1"
        };
        this.crmFiledData = filledCrmData;

        // 获取报名设置数据
        const { conferenceEnrollSetting, memberMapping } = this.$refs.manageSetting.getManegeSettingSubmitedData();
        // 创建或更新会议
        const res = await this.createOrUpdateConference({
          conferenceEnrollSettingArg: {
            ...conferenceEnrollSetting,
            ...memberMapping
          }
        });

        return res;

      } catch (error) {
        return false;
      } finally {
        this.operateButtons[0][0].loading = false;
      }
    },
    handleCancel() {
      this.$router.push({
        name: "meeting-marketing-init"
      });
    },
    selectMeetingCover(params) {
      this.coverImage = params.coverImage;
      this.formData.coverImagePath = params.coverImageTAPath;
    },
    goToMarketingEventSet() {
      const routerData = this.$router.resolve({
        name: "setting-setitems",
        query: {
          type: "v-marketingevent-set"
        }
      });
      window.open(routerData.href, "_blank");
    },
    async handleButtonClick(action){
      let index = this.currentStep - 1
      if(action === 'saveAndNext'){
        const res =  await this.handleSend()
        this.operateButtons[index][0].loading = false
        if(res){
        this.currentStep = this.currentStep + 1
        }
      } else if(action === 'saveFormSetting'){
        try {
          this.operateButtons[index][1].loading = true;
          
          // 开始保存流程
          await this.startSave();
          
          // 等待所有组件保存完成
          const componentSaveRes = await new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
              reject(new Error($t('marketing.pages.meeting_marketing.bccs_86d740')));
            }, 10000);
            
            const checkInterval = setInterval(() => {
              if (this.areAllComponentsSaved) {
                clearInterval(checkInterval);
                clearTimeout(timeout);
                resolve(true);
              } else if(this.saveError) {
                clearInterval(checkInterval);
                clearTimeout(timeout);
                reject(false); 
              }
            }, 100);
          });
          
          if(componentSaveRes) {
            // 保存成功
            await this.saveComplete();
            this.operateButtons[index][1].loading = false;
            // 跳转到详情页
            this.$router.push({
              name: "meeting-detail", 
              params: {
                id: this.meetingId,
                meetingStatus: 0
              },
              query: {
                source: 'conference'
              }
            });
          } else {
            // 保存失败,设置loading为false
            this.operateButtons[index][1].loading = false;
          }
        } catch (error) {
          FxUI.Message.error($t('marketing.commons.bcsb_6de920'));
          this.operateButtons[index][1].loading = false;
        }
      } else if(action === 'jump'){
        // 跳过，稍后设置
        this.$router.push({
          name: "meeting-detail",
          params: {
            id: this.meetingId,
            // 未完成会议表单设置
            meetingStatus: 1
          },
          query: {
            source: 'conference'
          }
        });
      } else if(action === 'cancel'){
        // 回到
        this.handleCancel()
      }
    },
    handleUpdateForm(val) {
      this.formData = val;
    },
    // 这一步需要做对象详情组件后再处理
    getPreviewData(id) {
      this.hexagonLoading = true;
      const templateSiteId = id || this.formData.marketingTemplateId
      templateSiteId &&
        http.getPagesByTemplateId({ templateSiteId }).then(res => {
          this.hexagonLoading = false;
          const { data, errCode } = res;
          if (errCode === 0 && data.length) {
            // 首页
            const homePage = data.filter(item => item.isHomepage === 1)[0] ||
              data[0] || { content: '{name:"",components:[]}' };
            this.templatePreviewTitle = JSON.parse(homePage.content).name;
            this.templatePreviewData = JSON.parse(homePage.content) || {};
            // 表单
            const formPage = data.filter(item => item.formId)[0] || { content: '{formId:"",name:"",components:[]}' };
            this.formTemplateId = formPage.formId;
            this.previewFormTitle = JSON.parse(formPage.content).name;
            this.previewFormData = JSON.parse(formPage.content) || {};
            this.formatPreviewData()
          }
      });
    },
    async saveFormSetting(index) {
      try {
        // 保存表单设置
        this.operateButtons[index][1].loading = true;
        this.saveStatus = true;
        
        // 等待所有子组件保存完成
        await new Promise((resolve, reject) => {
          const timeout = setTimeout(() => {
            reject(new Error($t('marketing.pages.meeting_marketing.bccs_86d740')));
          }, 10000); // 10秒超时

          this.$refs.manageSetting.$on('saveComplete', () => {
            clearTimeout(timeout);
            resolve();
          });
        });

        this.operateButtons[index][1].loading = false;
        this.saveStatus = false;
        
        // 保存成功后跳转
        this.$router.push({
          name: "meeting-detail",
          params: {
            id: this.meetingId,
            meetingStatus: 0
          },
          query: {
            source: 'conference'
          }
        });
      } catch (error) {
        FxUI.Message.error($t('marketing.pages.setting.bjsb_9304e8'));
        this.operateButtons[index][1].loading = false;
        this.saveStatus = false;
      }
    }
  },
  async mounted() {
    this.meetingId = this.$route.query.id;
    await this.loadMeetingData();
    this.getPreviewData()
  },
};
</script>

<style lang="less" scoped>
.MeetingCreate__wrapper {
  background-color: var(--color-special03, #f1f3f8);
  padding: 0 12px 12px 12px;
  flex: 1;
  display: flex;
  flex-direction: column;
  .MeetingCreate__header{
    position: sticky;
    width: 100%;
    background: var(--color-special03, #f1f3f8);
    z-index: 10;
    top: 0;
  }
  .MeetingCreate__body {
    display: flex;
    gap: 12px;
    .MeetingCreate__left{
      padding: 24px;
      position: relative;
      width: 424px;
      box-sizing: border-box;
      background: #fff;
      border-radius: @border-radius-base;

    }
    .MeetingCreate__right{
      padding: 20px 12px 0 12px;
      background: #fff;
      border-radius: @border-radius-base;
      flex: 1;
      .MeetingCreate__form {
        // 设计如此
        max-width: 1000px;
        .MeetingInfo__title{
          color: var(--Text-H1, #181C25);
          font-size: 16px;
          font-weight: 400;
          line-height: 24px;
          position: relative;
          padding-left: 12px;
          &::before{
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 4px;
            height: 16px;
            background: var(--color-primary06,#FF8000);
            border-radius: 1px;
            transform: translateY(25%);
          }
        }
        .MeetingInfo__wrapper{
          padding: 20px 12px 0 12px;
        }
  }
    }
  }
  .coverShow__image {
    width: 200px;
  }
  .cover {
    display: flex;
    .cover__icon {
      display: flex;
      margin-left: 18px;
      margin-top: 90px;
      height: 20px;
      line-height: 20px;
      .iconfont {
        font-size: 13px;
        color: #91959e;
        margin-right: 8px;
      }
      .text {
        color: #91959e;
      }
      .text:hover {
        cursor: pointer;
      }
    }
  }
  .activity__label {
    margin-bottom: 0;
    margin-top: -10px;
    /deep/ .el-form-item__label {
      overflow: visible;
    }
  }

  .MeetingCreate__map {
    &-switch {
      display: flex;
      align-items: center;
      color: @color-title;
    }
    &-wrap {
      margin-top: 10px;
      height: 200px;
      /deep/ .amap-logo {
        display: none !important;
      }
      /deep/ .amap-copyright {
        display: none !important;
      }
    }
  }
}
.hexagon__btn {
  font-size: 13px;
  display: inline-block;
  width: 100%;
  padding: 0 25px;
  color: var(--color-primary06,#407FFF);
  cursor: pointer;
  line-height: 34px;
}
.hexagon__refresh{
  border: none;
  text-align: left;
}
.hexagon__refresh:hover{
   color: var(--color-primary06,#407FFF);
}
</style>
