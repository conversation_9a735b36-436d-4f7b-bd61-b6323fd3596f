<template>
    <div class="markeing-meeting-info">
      <div class="info-item">
        <div class="label">{{ $t('marketing.commons.hybt_f7732d') }}</div>
        <div class="content">{{ meetingInfo.title }}</div>
      </div>

      <div class="info-item item-cover">
        <div class="label">{{ $t('marketing.commons.hyfm_8de72a') }}</div>
        <div class="content">
          <img
            :src="meetingInfo.coverImageUrl"
            :alt="$t('marketing.commons.hyfm_8de72a')"
            class="cover-image"
          />
        </div>
      </div>

      <div class="info-item">
        <div class="label">{{ $t('marketing.commons.hysj_dcf559') }}</div>
        <div class="content">
          {{ formatTime(meetingInfo.startTime) }} -
          {{ formatTime(meetingInfo.endTime) }}
        </div>
      </div>

      <div class="info-item">
        <div class="label">{{ $t('marketing.commons.hydd_89b785') }}</div>
        <div class="content">
          {{ meetingInfo.location }}
          <span class="location-status" v-if="meetingInfo.showMap">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="#30C776"
            >
              <path
                d="M8 15C11.866 15 15 11.866 15 8C15 4.13401 11.866 1 8 1C4.13401 1 1 4.13401 1 8C1 11.866 4.13401 15 8 15ZM8 16C3.58172 16 0 12.4183 0 8C0 3.58172 3.58172 0 8 0C12.4183 0 16 3.58172 16 8C16 12.4183 12.4183 16 8 16Z"
                fill="#30C776"
              />
              <path
                d="M4.14645 7.69445C3.95118 7.88971 3.95118 8.20629 4.14645 8.40156L6.62132 10.8764C6.81658 11.0717 7.13317 11.0717 7.32843 10.8764L12.1013 6.10357C12.2966 5.9083 12.2966 5.59172 12.1013 5.39645C11.906 5.20119 11.5895 5.20118 11.3942 5.39644L6.97487 9.81577L4.85355 7.69445C4.65829 7.49919 4.34171 7.49919 4.14645 7.69445Z"
                fill="#30C776"
              /></svg
            >{{ $t('marketing.pages.meeting_marketing.dldwykq_07db50') }}
          </span>
        </div>
      </div>

      <div class="info-item">
        <div class="label">{{ $t('marketing.commons.hyxq_002cd6') }}</div>
        <div class="meeting-detail" v-html="meetingInfo.conferenceDetails"></div>
      </div>

      <div class="info-item">
        <div class="label">{{ $t('marketing.pages.meeting_marketing.hyzymb_2c1bd4') }}</div>
        <div class="content">{{ marketingTemplateName || '--' }}</div>
      </div>

      <div class="info-item">
        <div class="label">{{ $t('marketing.commons.hdlx_13955e') }}</div>
        <div class="content">{{ eventTypeField || '--' }}</div>
      </div>

      <div class="info-item">
        <!-- <div class="label">{{ $t('marketing.commons.yxzszs_a21bb0') }}</div> -->
        <div class="label">{{ $t('marketing.commons.sfzs_f3387f') }}</div>
        <div class="content">
          <span class="status-enabled" v-if="meetingInfo.isMobileDisplay">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="#30C776"
            >
              <path
                d="M8 15C11.866 15 15 11.866 15 8C15 4.13401 11.866 1 8 1C4.13401 1 1 4.13401 1 8C1 11.866 4.13401 15 8 15ZM8 16C3.58172 16 0 12.4183 0 8C0 3.58172 3.58172 0 8 0C12.4183 0 16 3.58172 16 8C16 12.4183 12.4183 16 8 16Z"
                fill="#30C776"
              />
              <path
                d="M4.14645 7.69445C3.95118 7.88971 3.95118 8.20629 4.14645 8.40156L6.62132 10.8764C6.81658 11.0717 7.13317 11.0717 7.32843 10.8764L12.1013 6.10357C12.2966 5.9083 12.2966 5.59172 12.1013 5.39645C11.906 5.20119 11.5895 5.20118 11.3942 5.39644L6.97487 9.81577L4.85355 7.69445C4.65829 7.49919 4.34171 7.49919 4.14645 7.69445Z"
                fill="#30C776"
              /></svg
            >{{ $t('marketing.commons.ykq_9db7a8') }}
          </span>
          <span v-else>
            {{ $t('marketing.commons.wkq_ea4a36') }}</span>
        </div>
      </div>
    </div>
</template>

<script>
import util from "@/services/util";

export default {
  name: "MeetingInfo",
  props: {
    eventTypeField: {
      type: String,
      default: () => '',
    },
    marketingTemplateName: {
      type: String,
      default: () => '',
    },
  },
  watch: {
    '$store.state.MeetingMarketing.conferenceDetail': {
      handler(newVal, oldVal) {
        console.log('conferenceDetailnewVal', newVal);
        this.meetingInfo = newVal;
      },
      deep: true,
      immediate: true
    }
  },
  data() {
    return {
      meetingInfo: this.$store.state.MeetingMarketing.conferenceDetail
    }
  },
  methods: {
    formatTime(date) {
      return util.formatDateTime(date);
    }
  }
};
</script>

<style lang="less" scoped>
.markeing-meeting-info {
  padding: 20px 0 0 12px;
  .info-item {
    margin-bottom: 20px;
    display: flex;
    &:last-child{
      margin-bottom: 0;
    }
    .label {
      font-size: 14px;
      color: #545861;
      line-height: 20px;
      min-width: 120px;
      margin-right: 10px;
    }

    .content {
      font-size: 14px;
      color: #181c25;
      flex: 1;

      .item-cover {
        .content {
          border: 1px solid #e9edf5;
        }
      }
      .cover-image {
        width: 200px;
        height: auto;
        border-radius: 4px;
      }
      .location-status,
      .status-enabled {
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 6px;
      }
    }
    .meeting-detail {
      border-radius: 4px;
      border: 1px solid #dee1e8;
      padding: 12px;
      width: 100%;
      min-height: 130px;
      max-height: 350px;
      overflow: auto;
      /deep/ p{
        img{
          max-width: 100%;
          height: auto;
        }
      }
    }
  }
}
</style>
