<template>
  <div class="MeetingInfoForm__wrapper">
    <fx-form
      class="MeetingInfo__form"
      v-show="settingStatus"
      ref="form"
      label-width="138px"
      label-position="left"
      :model="modelFormData"
      :rules="formRules"
    >
      <fx-input
        v-model="modelFormData.title"
        :placeholder="$t('marketing.commons.qsr_02cc4f')"
        :maxlength="50"
        :label="$t('marketing.commons.hybt_f7732d')"
        prop="title"
        show-word-limit
      ></fx-input>
      <fx-form-item
        :label="$t('marketing.commons.hyfm_8de72a')"
        prop="originalImageAPath"
      >
        <div class="cover">
          <div class="cover__image">
            <PictureCutter
              :maxSize="1024 * 1024"
              :cutSize="[
                {
                  title:
                    $t('marketing.pages.meeting_marketing.hbfm_d8183b') +
                    '（9:5）',
                  desc: $t('marketing.components.PictureSelector.xs_6fa81e'),
                  width: 900,
                  height: 500,
                  photoTargetType: 45
                },
                {
                  title: $t('marketing.commons.xcxfm_94b4f3') + '（5:4）',
                  desc: $t(
                    'marketing.components.PictureSelector.yyzfwxxcxy_d66d73'
                  ),
                  width: 420,
                  height: 336,
                  photoTargetType: 43
                },
                {
                  title: $t('marketing.commons.pyqfm_ea274c') + '（1:1）',
                  desc: $t(
                    'marketing.components.PictureSelector.yyzfwxhpyq_5c11c7'
                  ),
                  width: 300,
                  height: 300,
                  photoTargetType: 44
                }
              ]"
              :defaultImageUrl="coverImage"
              :uploadtip="$t('marketing.commons.jyccxszcgs_906ed5')"
              outputPathType="a"
              @change="handlePictureCutterChange"
            />
          </div>
        </div>
        <div class="cover__tips">
          {{ $t("marketing.commons.jyccxszcgs_326c37") }}
          <div class="cover__standard">
            <PictureStandard />
          </div>
        </div>
      </fx-form-item>
      <fx-form-item
        :label="$t('marketing.commons.hysj_dcf559')"
        prop="startEndTime"
        style="width: 100%;"
        required
      >
        <MeetingCreateTimePicker
          v-model="modelFormData.startEndTime"
        ></MeetingCreateTimePicker>
      </fx-form-item>
      <fx-form-item
        :label="$t('marketing.commons.hydd_89b785')"
        prop="location"
      >
        <fx-input
          v-model="modelFormData.location"
          :placeholder="$t('marketing.commons.qsr_02cc4f')"
          :maxlength="50"
          :hasFormItem="false"
          show-word-limit
        ></fx-input>
        <div class="MeetingCreate__map">
          <div class="MeetingCreate__map-switch">
            <fx-switch
              v-model="modelFormData.showMap"
              size="small"
              style="margin-right: 10px;vertical-align: -4px;"
            ></fx-switch>
            {{ $t("marketing.pages.meeting_marketing.dtdwkqhhyl_e97022") }}
          </div>
          <template v-if="modelFormData.showMap">
            <fx-input
              id="MeetingCreate__map_searchinput"
              style="margin-top: 10px;"
              :placeholder="
                $t('marketing.pages.meeting_marketing.qsrjdxzlmc_508a28')
              "
              v-model="modelFormData.mapAddress"
            ></fx-input>
            <div class="MeetingCreate__map-wrap" id="meetingcreate_map"></div>
          </template>
        </div>
      </fx-form-item>
      <fx-form-item :label="$t('marketing.commons.hyxq_002cd6')">
        <meeting-tinymce-editor
          :height="500"
          :content="modelFormData.conferenceDetails"
          @setEditorContent="setEditorContent"
        ></meeting-tinymce-editor>
      </fx-form-item>
      <fx-form-item v-if="!$route.query.id" prop="marketingTemplateId">
        <div slot="label" style="display: inline-block;">
          <div style="display: flex;align-items: center;">
            <span style="margin-right: 2px;">{{
              $t("marketing.pages.meeting_marketing.hyzymb_2c1bd4")
            }}</span>
            <span
              style="cursor: pointer;display: inline-block;fontsize: 14px;"
              class="fx-icon-question"
              @click="
                handleOpenHelp(
                  'https://help.fxiaoke.com/93d5/9188/78fd/8c7a',
                  '_blank'
                )
              "
            ></span>
          </div>
        </div>
        <fx-select
          v-model="modelFormData.marketingTemplateId"
          :disabled="!fromCreate"
          :options="meetingFormTemplateList"
        >
          <template slot="options" slot-scope="slotProps">
            <span
              v-if="slotProps.data === 'create'"
              class="hexagon__btn"
              @click="goToMarketingEventSet"
              >{{ $t("marketing.commons.xjmb_156a04") }}+</span
            >
            <fx-button
              v-else-if="slotProps.data === 'update'"
              :loading="hexagonLoading"
              class="hexagon__btn hexagon__refresh"
              @click="getSceneHexagonTemplates"
            >
              {{ $t("marketing.commons.sxmblb_370baa") }}
            </fx-button>
            <span v-else>{{ slotProps.data.label }}</span>
          </template>
        </fx-select>
      </fx-form-item>

      <fx-form-item :label="$t('marketing.commons.hdlx_13955e')">
        <eventTypeSelector ref="eventTypeSelector" :defaultValue="modelFormData.eventType" :type="2" @change="handleEventTypeChange" />
      </fx-form-item>
      <fx-form-item
        :label="$t('marketing.commons.sfzs_f3387f')"
        class="activity__label"
        style="font-size: 12px;line-height: 28px;"
      >
        <fx-switch
          v-model="modelFormData.isMobileDisplay"
          size="small"
        ></fx-switch>
        {{ $t("marketing.commons.gxdqhdjhts_919cef") }}
      </fx-form-item>
      <fx-form-item v-if="settingStatus && !fromCreate">
        <fx-button
          type="primary"
          :loading="submiting"
          size="mini"
          @click="handleSaveEidtMetting"
          >{{ $t("marketing.commons.bc_be5fbb") }}</fx-button
        >
        <fx-button size="mini" @click="handleCancelEidtMetting" plain>{{
          $t("marketing.commons.qx_625fb2")
        }}</fx-button>
      </fx-form-item>
    </fx-form>
    <MeetingInfo
      v-show="!settingStatus"
      :eventTypeField="eventTypeField"
      :isMobileDisplay="modelFormData.isMobileDisplay"
      :marketingTemplateName="marketingTemplateName"
      :meetingInfo="modelFormData"
    />
  </div>
</template>

<script>
import PictureCutter from "@/components/picture-cutter/index.vue";
import MeetingTinymceEditor from "../MeetingTinymceEditor.vue";
import MeetingCreateTimePicker from "../MeetingCreateTimePicker.vue";
import http from "@/services/http/index";
import PictureStandard from "@/components/PictureStandard/index.vue";
import QuestionTooltip from "@/components/questionTooltip/index";
import MeetingInfo from "./MeetingInfo.vue";
import eventTypeSelector from "@/components/eventTypeSelector/index.vue";

export default {
  name: "MeetingInfoForm",
  props: {
    meetingInfo: {
      type: Object,
      default: () => ({})
    },
    settingStatus: {
      type: Boolean,
      default: false
    },
    fromCreate: {
      type: Boolean,
      default: false
    },
    isMobileDisplay: {
      type: Boolean,
      default: false
    }
  },
  components: {
    PictureCutter,
    MeetingTinymceEditor,
    MeetingCreateTimePicker,
    PictureStandard,
    QuestionTooltip,
    MeetingInfo,
    eventTypeSelector
  },
  data() {
    return {
      eventTypeField:  '',
      marketingTemplateName: '',
      isUpdatingFromParent: false,
      formRules: {
        title: [
          {
            required: true,
            message: $t("marketing.commons.qsrhybt_dba389"),
            trigger: "change"
          }
        ],
        originalImageAPath: [
          {
            required: true,
            trigger: "change",
            validator: (rule, value, callback) => {
              if (value || this.modelFormData.coverImagePath) {
                callback();
              } else {
                callback(new Error($t("marketing.commons.qschyfm_fa139a")));
              }
            }
          }
        ],
        startEndTime: [{ validator: this.validatorTime, trigger: "change" }],
        location: [
          {
            required: true,
            message: $t("marketing.pages.meeting_marketing.qsrhydd_6492cb"),
            trigger: "change"
          }
        ],
        marketingTemplateId: [
          {
            required: true,
            message: $t("marketing.commons.qxzbdmb_c41142"),
            trigger: "change"
          }
        ]
      },
      meetingFormTemplateList: [],
      hexagonLoading: false,
      coverImage: this.meetingInfo.coverImageUrl || "",
      map: null,
      marker: null,
      isMapScriptLoading: false,
      submiting: false,
      modelFormData: {
        ...this.meetingInfo,
        startEndTime: {
          startTime: this.meetingInfo.startTime,
          endTime: this.meetingInfo.endTime
        }
      },
    };
  },
  computed: {
    conferenceDetail() {
      return this.$store.state.MeetingMarketing.conferenceDetail;
    },
    marketingEventId() {
      return this.conferenceDetail.marketingEventDetail.marketingEventId;
    }
  },
  watch: {
    modelFormData: {
      handler(newVal) {
        if (this.settingStatus) {
          newVal.startTime = newVal.startEndTime?.startTime
          newVal.endTime = newVal.startEndTime?.endTime
          this.$emit('updateForm', {
            ...newVal,
          });
        }
      },
      deep: true
    },
    "modelFormData.showMap"(val) {
      if (!val) {
        this.modelFormData.mapLocation = {};
        this.modelFormData.mapAddress = "";
      }
      this.$nextTick(() => {
        this.initMap();
      });
    },
    'meetingInfo.isMobileDisplay': {
      handler(newVal) {
        this.modelFormData = {
          ...this.modelFormData,
          isMobileDisplay: newVal
        }
      },
      immediate: true,
    },
  },
  methods: {
    handlePictureCutterChange(file) {
      this.coverImage = file.cutOffsetList[0].image;
      this.modelFormData.coverImagePath =
        file.cutOffsetList[0].path || file.cropApath;
      this.modelFormData.originalImageAPath = file.photoPath || file.path;
      this.modelFormData.cutOffsetList = file.cutOffsetList;
      this.$refs["form"].validateField("originalImageAPath");
    },
    setEditorContent(content) {
      this.modelFormData.conferenceDetails = content;
      this.$refs["form"].validateField("conferenceDetails");
    },
    goToMarketingEventSet() {
      const routerData = this.$router.resolve({
        name: "setting-setitems",
        query: {
          type: "v-marketingevent-set"
        }
      });
      window.open(routerData.href, "_blank");
    },
    async getSceneHexagonTemplates() {
      this.hexagonLoading = true;
      let result = [];
      try {
        const {
          errCode,
          errMsg,
          data = {}
        } = await http.getSceneHexagonTemplates();
        if (errCode === 0) {
          const resultData = data.simpleResult || {};
          result = resultData["conference"] || [];
          result = result.slice(); // 创建副本以避免修改原数据
          const defaultIndex = result.findIndex(item => item.isDefault);
          if (defaultIndex > -1) {
            const defaultTemplate = result.splice(defaultIndex, 1)[0];
            result.unshift(defaultTemplate);
          }
          // 转换为下拉列表格式
          const meetingFormTemplateList = result.map(el => ({
            ...el,
            label: el.name,
            value: el.id
          }));

          // 添加创建和更新选项
          this.meetingFormTemplateList = meetingFormTemplateList.concat([
            "create",
            "update"
          ]);

          // 设置默认选中的模板ID和名称
          if (result.length > 0) {
            const selectedTemplate =
              result.find(
                item => item.id === this.meetingInfo.marketingTemplateId
              ) || result[0];
            this.modelFormData.marketingTemplateId = selectedTemplate.id;
            this.marketingTemplateName = selectedTemplate.name;
          }
        } else {
          FxUI.Message.error(errMsg || $t("marketing.commons.hqmblbsb_463047"));
        }
      } catch (error) {
        FxUI.Message.error($t("marketing.commons.hqmblbsb_463047"));
      } finally {
        this.hexagonLoading = false;
      }
    },
    getFormData() {
      return this.modelFormData;
    },
    validate() {
      return new Promise(resolve => {
        this.$refs.form.validate(valid => {
          resolve(valid);
        });
      });
    },
    createMap() {
      const { mapLocation } = this.modelFormData;
      this.map && this.map.destroy();
      this.map = new AMap.Map("meetingcreate_map", {
        resizeEnable: true,
        zoom: 13
      });
      console.log(this.map,'this.map');
      debugger
      const InputDom = document.querySelector(
        "#MeetingCreate__map_searchinput .el-input__inner"
      );
      const auto = new AMap.Autocomplete({
        input: InputDom
      });
      //初始化坐标点位置
      if (mapLocation && mapLocation.lng) {
        this.setMarker(mapLocation);
      }
      AMap.event.addListener(auto, "select", e => {
        this.setMarker(e.poi.location);
        this.modelFormData.mapAddress =
          e.poi.district + e.poi.address + e.poi.name;
        this.modelFormData.mapLocation = e.poi.location;
        // });
      });
    },
    setMarker({ lng, lat }) {
      if (!this.marker) {
        this.marker = new AMap.Marker({
          icon: require("@/assets/images/icon/location.png"),
          offset: new AMap.Pixel(-10, -20),
          map: this.map
        });
        this.map.add(this.marker);
      }
      this.marker.setPosition(new AMap.LngLat(lng, lat));
      this.map.setFitView();
    },
    initMap() {
      if (this.modelFormData.showMap) {
        this.loadMapScript();
      }
    },
    loadMapScript() {
      const scriptLoadded = () => {
        this.isMapScriptLoading = false;
        this.createMap();
      };
      if (this.isMapScriptLoading) {
        return;
      }
      if (document.getElementById("hexagon_map_script")) {
        scriptLoadded();
        return;
      }
      const script = document.createElement("script");
      script.type = "text/javascript";
      script.id = "hexagon_map_script";
      if (script.readyState) {
        //IE
        script.onreadystatechange = () => {
          if (
            script.readyState == "loaded" ||
            script.readyState == "complete"
          ) {
            script.onreadystatechange = null;
            scriptLoadded();
          }
        };
      } else {
        script.onload = function() {
          scriptLoadded();
        };
      }
      this.isMapScriptLoading = true;
      script.src = `${window.location.protocol}//webapi.amap.com/maps?v=1.4.15&key=6de2e0c700534388eaa62126b411e8ef&plugin=AMap.Autocomplete`;
      document.getElementsByTagName("head")[0].appendChild(script);
    },
    handleCancelEidtMetting() {
      this.eventTypeField = this.$refs.eventTypeSelector.getEventFieldLabel(this.meetingInfo.eventType);
      this.$emit('update:settingStatus', false);
      this.$emit('settingStatusChange', false, 'MeetingInfoForm');
    },
    handleOpenHelp(url, target) {
      window.open(url || "https://help.fxiaoke.com/93d5", target || "_blank");
    },
    validatorTime(rule, value, callback) {
      if (!value.startTime && !value.endTime) {
        callback(
          new Error($t("marketing.pages.meeting_marketing.qxzhysj_178dca"))
        );
      } else if (!value.startTime) {
        callback(
          new Error($t("marketing.pages.meeting_marketing.qxzhykssj_8b7504"))
        );
      } else if (!value.endTime) {
        callback(
          new Error($t("marketing.pages.meeting_marketing.qxzhyjssj_c73f41"))
        );
      } else {
        callback();
      }
    },
    handleEventTypeChange(value) {
      this.modelFormData.eventType = value.apiName;
      this.eventTypeField = value.fieldName;
    },
    async handleSaveEidtMetting() {
      try {
        const valid = await this.validate();
        if(!valid) return;
        this.submiting = true;
        const params = this.getSaveParams();
        
        const res = await YXT_ALIAS.http.createOrUpdateConference(params);
        
        if (res?.errCode === 0) {
          this.$store.dispatch('updateConferenceDetail', {
           conferenceDetail: {
            ...this.modelFormData,
            isMobileDisplay: this.modelFormData.isMobileDisplay
           }
          });
          FxUI.Message.success($t("marketing.commons.bjcg_3bb47b"));
          this.$emit('update:settingStatus', false);
        } else {
          FxUI.Message.error($t("marketing.pages.setting.bjsb_9304e8"));
        }
      } finally {
        this.submiting = false;
      }
    },
    getSaveParams() {
      const params = JSON.parse(JSON.stringify(this.modelFormData));
      return {
        conferenceId: params.id,
        marketingEventId: this.marketingEventId,
        title: params.title,
        coverImagePath: params.coverImagePath,
        originalImageAPath: params.originalImageAPath,
        cutOffsetList: params.cutOffsetList,
        startTime: params.startEndTime?.startTime,
        endTime: params.startEndTime?.endTime,
        location: params.location,
        eventType: params.eventType,
        marketingTemplateId: params.marketingTemplateId,
        conferenceDetails: params.conferenceDetails.replace(/\n/g, ""),
        shmapAddress: params.showMap,
        mapLocation: params.mapLocation,
        mapAddress: params.mapAddress,
        updateCoverImage: true,
        updateConferenceDetails: true,
        createObjectDataModel: {
          objectData: {
            is_mobile_display: this.modelFormData.isMobileDisplay ? "0" : "1"
          }
        }
      };
    },
  },
  mounted() {
    this.initMap();
    this.getSceneHexagonTemplates();
  }
};
</script>

<style lang="less" scoped>
.MeetingInfoForm__wrapper {
  .MeetingInfo__form {
    /deep/.el-form-item {
      margin-bottom: 20px;
      &.activity__label {
        .el-form-item__label {
          line-height: 40px;
        }
      }
      .el-form-item__label-wrap {
        margin-left: 0 !important;
      }
      // input里面也有form-item类
      .el-form-item {
        margin-bottom: 0;
      }
      .el-form-item__content{
        .el-select{
          width: calc(50% - 5px);
        }
      }
    }
    .cover__tips {
      display: flex;
      font-size: 12px;
      color: #91959e;
      line-height: 18px;
      margin-top: 12px;
      .cover__standard {
        margin-left: 5px;
      }
    }
    .MeetingCreate__map {
      margin-top: 10px;
      line-height: 22px;
      .MeetingCreate__map-switch {
        font-size: 12px;
        display: flex;
      }
    }
  }
}
.el-select {
  width: 300px;
}
.hexagon__btn {
  font-size: 13px;
  display: inline-block;
  width: 100%;
  padding: 0;
  color: #0c6cff;
  cursor: pointer;
  line-height: 34px;
}
.hexagon__refresh {
  border: none;
  text-align: left;
}
.hexagon__refresh:hover,
.hexagon__refresh:active,
.hexagon__refresh:focus {
  color: #0c6cff;
  background-color: var(--color-special01, #f2f4fb);
}
.el-select-dropdown__item.hover .hexagon__refresh {
  background-color: var(--color-special01, #f2f4fb);
}
</style>

