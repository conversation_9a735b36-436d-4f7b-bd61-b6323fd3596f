<template>
    <div class="SitePreview__wrapper">
      <div class="site-preview__title">
        {{title}}
        <slot name="title-tooltip"></slot>
      </div>
      <div class="site-preview__wrapper">
        <div class="site-preview__page-header">
          <img src="@/assets/images/phoneHeaderNew.jpg" alt="phone-header" class="site-preview__page-phone-header">
          <div class="site-preview__page-header-title km-t-ellipsis1">
            <!-- 活动标题 -->
            {{ pageRenderData.title || $t('marketing.commons.hyzy_7962bd') }}
          </div>
        </div>
        <PageRender
          class="site-preview__page-render"
          :title="previewTitle"
          :data="pageRenderData"
          :scrollHeight="630"
          v-loading="loading"
        ></PageRender>
      </div>
    </div>
</template>

<script>
import PageRender from "@/components/Hexagon/PageRender";
import _ from 'lodash'

export default {
  name: "SitePreview",
  components: {
    PageRender
  },
  props: {
    previewData: {
      type: Object,
      default: ()=>({})
    },
    previewTitle: {
      type: String,
      default: ''
    },
    title:{
      type: String,
      default: $t('marketing.commons.yl_645dbc')
    }
  },
  data() {
    return {
      loading: true,
      internalData: {}
    };
  },
  computed: {
    pageRenderData() {
      return this.internalData;
    }
  },
  watch: {
    previewData: {
      handler(newVal) {
        if (!newVal || Object.keys(newVal).length === 0) {
          this.loading = true;
          return;
        }
        
        this.$nextTick(() => {
          this.internalData = _.cloneDeep(newVal);
          setTimeout(() => {
            this.loading = false;
          }, 100);
        });
      },
      immediate: true,
      deep: true
    }
  },
};
</script>

<style lang="scss" scoped>
.SitePreview__wrapper {
  .site-preview__title {
    color: var(--Text-H1, #181C25);
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    margin-bottom: 20px;
    display: flex;
  }
  .site-preview__wrapper {
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.15);
    width: 375px;
  }
  .site-preview__page-render {
    width: 100%;
  }
  .site-preview__page-header {
    position: relative;
    .site-preview__page-phone-header {
      width: 100%;
      height: auto;
      object-fit: contain;
    }
    .site-preview__page-header-title {
      font-size: 14px;
      font-weight: 400;
      line-height: 20px;
      color: var(--Text-H1, #181C25);
      position: absolute;
      bottom: 20px;
      left: 50%;
      transform: translate(-50%);
      width: 100%;
      text-align: center;
      padding: 0 16px;
      box-sizing: border-box;
    }
  }
}
</style>
