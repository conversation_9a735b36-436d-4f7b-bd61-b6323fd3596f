<template>
      <div class="detail">
        <MeetingInfo ref="MeetingInfo" />
        <el-menu
          :default-active="currentTab"
          class="meeting-menu"
          mode="horizontal"
          @select="handleSelect"
        >
          <el-menu-item
            class="set-menu-item"
            index="overview"
          >
            {{ $t('marketing.commons.gl_863853') }}
          </el-menu-item>
          <el-menu-item
            class="set-menu-item"
            index="participants"
          >
            {{ $t('marketing.commons.chry_a47805') }}
          </el-menu-item>
          <el-menu-item
            class="set-menu-item"
            index="radar"
          >
            {{ $t('marketing.commons.tgld_803716') }}
          </el-menu-item>
          <el-menu-item
            v-if="vDatas.uinfo.wechatVoucher || vDatas.uinfo.sourceCouponEnabled"
            class="set-menu-item"
            index="coupons"
          >
            {{ $t('marketing.commons.yhq_2f3635') }}
          </el-menu-item>
          <el-menu-item
            class="set-menu-item"
            v-if="vDatas.pluginInfo.marketingPlan"
            index="kanban"
          >
            {{ $t('marketing.commons.yyjh_942f80') }}<span class="kanban-num">{{ kanbanNum }}</span>
          </el-menu-item>
          <el-menu-item
            class="set-menu-item"
            index="trigger"
          >
            SOP
          </el-menu-item>
          <el-menu-item
            class="set-menu-item"
            index="analysis"
          >
            {{ $t('marketing.commons.sjfx_6450d8') }}
          </el-menu-item>
          <el-menu-item
            v-for="menu in mainMenusData"
            :key="menu.id"
            class="set-menu-item"
            :index="`custom-component-${menu.id}`"
          >
            {{ menu.name }}
          </el-menu-item>
          <el-submenu
            v-if="moreMenusData && moreMenusData.length"
            class="set-submenu"
            index="more"
          >
            <template slot="title">
              {{ $t('marketing.commons.gd_0ec9ea') }}
            </template>
            <el-menu-item
              v-for="menu in moreMenusData"
              :key="menu.id"
              :index="`custom-component-${menu.id}`"
            >
              {{
                menu.name
              }}
            </el-menu-item>
          </el-submenu>
        </el-menu>
        
        <ProcessFlow 
          v-if="currentTab === 'overview'"
          :steps="flowSteps" 
          class="meeting-process-flow"
          style="margin-bottom: 12px;"
          :title="$t('marketing.pages.meeting_marketing.hypzjlcdl_1979cb')"
          :currentStep="currentStep"
          :current-status="currentConferenceStatus"
          @action-click="handleActionClick"
          @step-click="handleStepClick"
        />

        <!-- 这里需要使用key来强制重新渲染组件，否则自定义组件切换的tab的时候，不会重新渲染 -->
        <component
          :is="currentTab"
          :key="currentTab"
          v-if="readyRenderComponent"
          ref="comp"
          :marketing-event-id="marketingEventId"
          :object-detail-data="objectDetailData"
          :auto-open-notification="autoOpenNotification"
          scene-type="conference"
          source="meeting"
          @refreshSop="refreshSop"
        />

        <GuideStep
          :steps="guideSteps"
          v-model="guideVisible"
          ref="guide"
          @guide-finish="onGuideFinish"
          @before-step="handleBeforeStep"
          @close-dialog="handleCloseDialog"
          @close-popover="handleClosePopover"
        />
        <ConfettiComp
          :confettiConfig="confettiConfig"
          @onConfirm="handleGuideSteps"
          v-if="showConfetti"
        />
      </div>
</template>

<script>
import analysis from '@/pages/content-marketing/content-dashboard/analysis'
import MeetingInfo from './components/MeetingInfo'
import overview from './overview'
import radar from '../MeetingData/index'
import trigger from './trigger'
import kanban from '@/components/sop-kanban/index.vue'
import http from '@/services/http/index'
import coupons from '@/pages/content-marketing/content-dashboard/coupons.vue'
import kisvData from '@/modules/kisv-data'
import crmLayoutMenusMixin from '@/mixins/crm-layout-menus'
import { mDatas } from '@/modules/menu/menu.js'
import participants from './components/MeetingParticipants'
import guideMixin from './mixins/guide.js'
import GuideStep from '@/components/GuideStep.vue'
import ProcessFlow from '@/components/ProcessFlow.vue'
import ConfettiComp from '@/components/ConfettiComp/index.vue'
export default {
  name: 'MeetingDetail',
  components: {
    coupons,
    MeetingInfo,
    ElMenu: FxUI.Menu,
    ElMenuItem: FxUI.MenuItem,
    ElSubmenu: FxUI.Submenu,
    overview,
    radar,
    trigger,
    analysis,
    kanban,
    participants,
    ProcessFlow,
    GuideStep,
    ConfettiComp
  },
  mixins: [crmLayoutMenusMixin, guideMixin],
  data() {
    return {
      vDatas: kisvData.datas,
      kanbanNum: '',
      currentStep: 0,
      confettiConfig: {
        showTipsDialog: false,
        status: '',
        title: '',
        tips: '',
        buttonText: $t('marketing.commons.lcyd_7696e8'),
      },
      currentTab: this.$route.query.type || 'overview',
    }
  },
  computed: {
    marketingEventDetail() {
      return this.$store.state.MeetingMarketing.marketingEventDetail
    },
    marketingEventId() {
      return this.marketingEventDetail?.marketingEvent?.id || ''
    },
    marketingEventType() {
      return this.marketingEventDetail?.marketingEvent?.eventType || ''
    },
    currentConferenceStatus() {
      const conferenceDetail = this.$store.state.MeetingMarketing.conferenceDetail
      if (!conferenceDetail) return 'before'
      const { startTime, endTime } = conferenceDetail
      if (!startTime || !endTime) return 'before'
      const now = new Date().getTime()
      const startTimeStamp = Number(startTime)
      const endTimeStamp = Number(endTime)
      if (now < startTimeStamp) {
        this.currentStep = 0
        return 'before'
      } else if (now >= startTimeStamp && now <= endTimeStamp) {
        this.currentStep = 1
        return 'processing'
      } else {
        this.currentStep = 2
        return 'after'
      }
    },
  },
  methods: {
    handleSelect(index) {
      this.currentTab = index
      this.$store.commit('MeetingMarketing/setCurrentMeetingDetailTab', { currentMeetingDetailTab: index })
    },

    refreshSop() {
      this.getSOPTabInfo()
      if (this.$refs.comp && this.$refs.comp.getSOPTabInfo) {
        this.$refs.comp.getSOPTabInfo()
      }
    },

    getSOPTabInfo() {
      if (!this.marketingEventId) return

      http.getSOPTabInfo({ objectId: this.marketingEventId }).then(res => {
        if (res?.errCode === 0) {
          const isExit = res.data?.exist
          if (isExit && res.data.workCount) {
            this.kanbanNum = `${res.data.finishedWorkCount}/${res.data.workCount}`
          } else {
            this.kanbanNum = ''
          }
          this.boardInfo = res.data
        }
      })
    },

    handleCheckMeetingStatus(){
      // 存在meetingStatus参数，则说明是新建页面跳转过来的
      const { meetingStatus } = this.$route.params;
      this.confettiConfig.status = 'success'
      this.confettiConfig.title = $t('marketing.pages.meeting_marketing.hycjcg_66004d')
      this.confettiConfig.showTipsDialog = true
      this.confettiConfig.tips = meetingStatus === 0 ? $t('marketing.pages.meeting_marketing.gdhyszxnkg_62ece7') :$t('marketing.pages.meeting_marketing.ndhyzyhmys_39dbfe')
      if(meetingStatus === 0 || meetingStatus === 1){
        this.showConfetti = true
      }
    },
    handleGuideSteps() {
      this.guideSteps = [
        {
          target: ['.meeting-homepage-wrapper'],
          content: $t('marketing.pages.meeting_marketing.ngcjdhyzyz_3f34ef'),
          placement: 'right',
        },
        {
          target: ['.meeting-process-flow'],
          content: $t('marketing.pages.meeting_marketing.zlshydlcyd_08de1c'),
          placement: 'bottom',
        }
      ]
      this.guideVisible = true;
    }
  },
  async created() {
    // 延迟加载 SOP 信息
    this.handleCheckMeetingStatus();
    setTimeout(() => {
        this.getSOPTabInfo();
      }, 2000);
  },
}
</script>

<style lang="less" scoped>
.detail {
  height: auto;
  background: #f2f2f5;
  .meeting-menu {
    margin: 12px 0 12px 0;
    padding-left: 22px;
    border-top: 1px solid #e6e6e6;
    .set-menu-item {
      margin: 0 40px 0 0;
      padding: 0;
      height: 50px;
      line-height: 52px;
      // &.is-active {
      //   border-bottom-color: #ea7504;
      // }
      .kanban-num {
        font-size: 12px;
        color: #c1c1c1;
        margin-left: 3px;
        vertical-align: top;
      }
    }
    /deep/ .el-submenu {
      .el-submenu__title {
        padding: 0;
        height: 50px;
        line-height: 52px;
      }
    }
    // /deep/ .el-submenu {
    //   &.is-active {
    //     .el-submenu__title {
    //       border-bottom-color: #ea7504;
    //     }
    //   }
    // }
  }
  .MarketingActivityQrposter {
    margin: 0 10px 10px 10px;
    height: calc(100% - 140px);
  }
  .sop-kanban-wrapper {
    height: calc(100% - 178px);
  }
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
  }
}
</style>

<style lang="less">
.meeting-success-alert {
  position: fixed !important;
  top: 100px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  margin: 0 !important;
}
</style>

