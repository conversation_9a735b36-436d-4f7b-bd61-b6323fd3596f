import { requireAsync } from "@/utils";
const guideStepMap = {
  meetingHome: [
    // 会议主页引导
    {
      target: ['.meeting-homepage-wrapper','.meetinghome-more-set-wrapper'],
      content: $t('marketing.pages.meeting_marketing.djbjymwszz_31dbb4'),
      placement: 'right',
      arrowTarget: '.meetinghome-more-set-wrapper .more-set__popover-item:nth-child(2)',
      popover: '.meetinghome-more-set-wrapper',
      preAction: async (vm) => {
        // 自动点击会议主页推广内容的"更多"按钮，打开popover
        const btn = document.querySelector('.meetinghome-more-set__btn');
        if (btn) btn.click();
        // 等待popover内容出现
        let attempts = 0;
        while (!document.querySelector('.meetinghome-more-set-wrapper') && attempts < 20) {
          await new Promise(r => setTimeout(r, 100));
          attempts++;
        }
      }
    },
    {
      target: ['.meeting-homepage-wrapper','.meetinghome-more-set-wrapper'],
      content: $t('marketing.pages.meeting_marketing.wcnrzzhhxw_879834'),
      placement: 'right',
      arrowTarget: '.meetinghome-more-set-wrapper .more-set__popover-item:nth-child(4)',
    },
    {
      target: ['.meeting-homepage-wrapper','.meetinghome-more-set-wrapper'],
      content: $t('marketing.pages.meeting_marketing.nhkykszzgn_6d3397'),
      placement: 'right',
      arrowTarget: '.meetinghome-more-set-wrapper .more-set__popover-item:nth-child(1)',
    },
  ],
  otherContent: [
    {
      target: '#guide_advertise-header-create',
      content: $t('marketing.pages.meeting_marketing.nkydjtjnrz_e5ee87'),
      placement: 'left',
    },
    {
      target: '#guide__material-select-dialog .material-select-dialog',
      content: $t('marketing.pages.meeting_marketing.nkyzzdglxd_9183c3'),
      placement: 'bottom',
      dialog: true,
      dialogEl: '#guide__material-select-dialog',
      dialogRef: 'materialSelectDialog',
      closeBtn: '.marketing-guide-close-material-btn',
      preAction: async (vm) => {
        // 打开添加推广内容弹窗
        const createMaterialBtn = document.querySelector('.create-material-btn')
        if (createMaterialBtn) {
          createMaterialBtn.click();
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
    }
  ],
  promotionPoster: [
    {
      target: '#guide__qrposter-create_btn',
      content: $t('marketing.pages.meeting_marketing.djzlzzhbqt_5f4199'),
      placement: 'left',
    },
    {
      target: ['.meeting-homepage-wrapper','.meetinghome-more-set-wrapper'],
      content: $t('marketing.pages.meeting_marketing.nhkykszzgn_6d3397'),
      placement: 'right',
      arrowTarget: '.meetinghome-more-set-wrapper .more-set__popover-item:nth-child(1)',
      preAction: async (vm) => {
        // 自动点击会议主页推广内容的"更多"按钮，打开popover
        const btn = document.querySelector('.meetinghome-more-set__btn');
        if (btn) btn.click();
        // 等待popover内容出现
        let attempts = 0;
        while (!document.querySelector('.meetinghome-more-set-wrapper') && attempts < 20) {
          await new Promise(r => setTimeout(r, 100));
          attempts++;
        }
      }
    },
  ],
  promotionContent: [
    {
      target: '#guide_spread-btn',
      content: $t('marketing.pages.meeting_marketing.djzlqtgnro_a588eb'),
      placement: 'left',
      maskClosable: false
    }
  ],
  manualAddParticipants: [
    {
      target: '#guide-active-member__add',
      content: $t('marketing.pages.meeting_marketing.djzltjchry_79552d'),
      placement: 'bottom',
      preAction: async (vm) => {
        // 切换到参会人员 tab
        vm.handleSelect('participants');
        await vm.$nextTick();
        await new Promise(resolve => setTimeout(resolve, 300));
      }
    },
  ],
  selectedParticipantsNotification: [
    {
      target: '#guide__all-participants-notification',
      content: $t('marketing.pages.meeting_marketing.xznytzdchr_707b3d'),
      placement: 'bottom',
      preAction: async (vm) => {
        // 切换到参会人员 tab
        vm.handleSelect('participants');
        await vm.$nextTick();
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }
  ],
  manualUpdateParticipantsSignInStatus: [
    {
      target: '#guide__manual-update-participants-sign-in-status',
      content: $t('marketing.pages.meeting_marketing.xznybqdchr_01df70'),
      placement: 'bottom',
      preAction: async (vm) => {
        // 切换到参会人员 tab
        vm.handleSelect('participants');
        await vm.$nextTick();
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }
  ],
  sendActivityContent: [
    {
      target: '.MeetingInvitationContent',
      content: $t('marketing.pages.meeting_marketing.xzhtjnytgd_9e8a1a'),
      placement: 'top',
    }
  ]
}

export default {
  methods: {
    /**
     * 获取SpreadContentList组件引用
     * @returns {Object|null} SpreadContentList组件引用
     */
    getSpreadContentListRef() {
      return this.getCurrentComp()?.$refs?.invitationContent?.$refs?.spreadContentList;
    },

    /**
     * 等待SpreadContentList的contentList加载完成
     * @param {Object} spreadContentListRef - SpreadContentList组件引用
     * @returns {Promise<boolean>} 是否加载成功
     */
    async waitForContentList(spreadContentListRef) {
      if (!spreadContentListRef) return false;
      
      // 如果contentList还没有加载，先等待加载完成
      if (!spreadContentListRef.contentList || spreadContentListRef.contentList.length === 0) {
        // 触发加载
        spreadContentListRef.getContentList();
        // 等待加载完成
        let attempts = 0;
        while ((!spreadContentListRef.contentList || spreadContentListRef.contentList.length === 0) && attempts < 50) {
          await new Promise(resolve => setTimeout(resolve, 100));
          attempts++;
        }
      }
      
      return spreadContentListRef.contentList && spreadContentListRef.contentList.length > 0;
    },

    /**
     * 查找会议主页类型的物料
     * @param {Array} contentList - 内容列表
     * @returns {Object|null} 会议主页物料
     */
    findMeetingHomeItem(contentList) {
      return contentList.find(item => item.material.siteType === 'EVEN_HOMEPAGE');
    },

    /**
     * 获取当前组件引用
     * @returns {Object|null} 当前组件引用
     */
    getCurrentComp() {
      return this.$refs.comp;
    },

    /**
     * 获取参会人员组件引用
     * @returns {Object|null} 参会人员组件引用
     */
    getParticipantsRef() {
      const comp = this.getCurrentComp();
      return comp;
    },

    /**
     * 切换到参会人员tab并执行回调
     * @param {Function} callback - 回调函数
     * @param {number} delay - 延迟时间，默认300ms
     */
    switchToParticipantsTab(callback, delay = 300) {
      this.handleSelect('participants');
      this.$nextTick(() => {
        setTimeout(() => {
          callback();
        }, delay);
      });
    },

    /**
     * 触发ElDropdown的hover事件
     * @param {Object} participantsRef - 参会人员组件引用
     * @returns {boolean} 是否成功触发
     */
    triggerElDropdownHover(participantsRef) {
      if (!participantsRef?.$refs?.leadsTable?.$refs?.tableHeader) {
        return false;
      }

      const dropdownBtn = participantsRef.$refs.leadsTable.$refs.tableHeader.$el.querySelector('.active-member__more .el-icon-more');
      if (!dropdownBtn) {
        return false;
      }

      // 模拟mouseenter事件来触发hover效果
      const mouseenterEvent = new MouseEvent('mouseenter', {
        bubbles: true,
        cancelable: true,
        view: window
      });
      dropdownBtn.dispatchEvent(mouseenterEvent);
      return true;
    },

    /**
     * 执行SpreadContentList相关操作
     * @param {string} operationType - 操作类型
     * @param {string} logMessage - 日志消息
     */
    async executeSpreadContentListOperation(operationType, logMessage) {
      const spreadContentListRef = this.getSpreadContentListRef();
      if (!spreadContentListRef) {
        return;
      }

      const isLoaded = await this.waitForContentList(spreadContentListRef);
      if (!isLoaded) {
        return;
      }

      const meetingHomeItem = this.findMeetingHomeItem(spreadContentListRef.contentList);
      if (!meetingHomeItem) {
        return;
      }

      // 执行具体操作
      switch (operationType) {
        case 'edit':
          spreadContentListRef.handleOperate({type: 'edit'}, meetingHomeItem);
          break;
        case 'poster':
          spreadContentListRef.handleOperate({type: 'posterContent'}, meetingHomeItem);
          break;
        case 'preview':
          spreadContentListRef.showPreviewContent(meetingHomeItem);
          break;
        default:
          return;
      }
    },

    /**
     * 打开添加推广内容弹窗
     */
    openAddContentDialog() {
      this.$nextTick(() => {
        const spreadContentListRef = this.getSpreadContentListRef();
        if (spreadContentListRef?.$refs?.AssociateContentButton) {
          // 调用子组件的添加内容方法，打开内容中心
          spreadContentListRef.$refs.AssociateContentButton.showMaterialDialog();
        } else {
          // 备用方案：直接查找DOM元素
          const createMaterialBtn = document.querySelector('.create-material-btn');
          if (createMaterialBtn) {
            createMaterialBtn.click();
        } 
        }
      });
    },

    /**
     * 执行参会人员相关操作
     * @param {string} operationType - 操作类型
     * @param {string} logMessage - 日志消息
     */
    executeParticipantsOperation(operationType, logMessage) {
      this.switchToParticipantsTab(() => {
        const participantsRef = this.getParticipantsRef();
        if (!participantsRef?.$refs?.leadsTable) {
          return;
        }

        const leadsTable = participantsRef.$refs.leadsTable;
        
        // 执行具体操作
        switch (operationType) {
          case 'add':
            leadsTable.handleShowAddDialog();
            break;
          case 'notice':
            leadsTable.handleNotice();
            break;
          case 'signin':
            leadsTable.handleSignin();
            break;
                  default:
          return;
      }
      });
    },

    /**
     * 等待参会人员列表数据加载完成
     * @param {Object} leadsTable - 参会人员表格组件引用
     * @returns {Promise<boolean>} 是否加载成功
     */
    async waitForParticipantsList(leadsTable) {
      if (!leadsTable) {
        return false;
      }
      
      // 如果lists还没有加载，先等待加载完成
      if (!leadsTable.lists || leadsTable.lists.length === 0) {
        // 触发加载
        if (leadsTable.queryLists) {
          try {
            leadsTable.queryLists();
          } catch (error) {
            // 静默处理错误
          }
        }
        
        // 等待加载完成
        let attempts = 0;
        const maxAttempts = 50;
        
        while ((!leadsTable.lists || leadsTable.lists.length === 0) && attempts < maxAttempts) {
          await new Promise(resolve => setTimeout(resolve, 100));
          attempts++;
        }
      }
      
      const result = leadsTable.lists && leadsTable.lists.length > 0;
      return result;
    },

    /**
     * 跳转到参会人员列表，根据参数进行筛选和选中操作
     * @param {Object} filterParams - 筛选参数，如 { reviewStatus: [0] } 表示筛选未审核数据
     * @param {boolean} selectFirst - 是否选中第一条数据，默认为true
     * 
     * 筛选参数说明：
     * - 筛选未审核数据：{ reviewStatus: [0] }
     * - 筛选未签到数据：{ signStatus: [3] }
     * - 筛选待邀约数据：{ inviteStatus: [1] }
     * - 筛选CRM错误数据：{ saveCrmStatus: [99] }
     * 
     * 执行流程：
     * 1. 等待切换到参会人员tab完成
     * 2. 等待组件渲染完成
     * 3. 根据参数组合执行相应操作：
     *    - 有筛选参数 + 选中：设置筛选条件 → 执行筛选查询 → 等待数据加载 → 选中第一条
     *    - 有筛选参数 + 不选中：设置筛选条件 → 执行筛选查询
     *    - 无筛选参数 + 选中：等待数据加载 → 选中第一条
     *    - 无筛选参数 + 不选中：仅切换tab
     * 
     * 参数组合处理：
     * 1. filterParams有值 + selectFirst=true：筛选并选中第一条数据
     * 2. filterParams有值 + selectFirst=false：只筛选，不选中
     * 3. filterParams为null + selectFirst=true：只选中第一条，不筛选
     * 4. filterParams为null + selectFirst=false：既不筛选也不选中，仅切换tab
     * 
     * 调用示例：
     * await this.switchToParticipantsAndSelectFirst({ reviewStatus: [0] }, true);  // 筛选未审核并选中第一条
     * await this.switchToParticipantsAndSelectFirst({ signStatus: [3] }, false);  // 只筛选未签到，不选中
     * await this.switchToParticipantsAndSelectFirst(null, true);                  // 只选中第一条，不筛选
     * await this.switchToParticipantsAndSelectFirst(null, false);                 // 仅切换tab
     */
    async switchToParticipantsAndSelectFirst(filterParams = null, selectFirst = true) {
      // 等待切换到参会人员tab完成
      await new Promise(resolve => {
        this.switchToParticipantsTab(() => {
          resolve();
        });
      });
      
      // 等待组件渲染完成
      await this.$nextTick();
      await new Promise(resolve => setTimeout(resolve, 300));
      
      const participantsRef = this.getParticipantsRef();
      if (!participantsRef) {
        return;
      }
      
      const leadsTable = participantsRef.$refs?.leadsTable;
      if (!leadsTable) {
        return;
      }
      
      const tableHeader = leadsTable.$refs?.tableHeader;
      if (!tableHeader) {
        return;
      }
      
      const filterComp = tableHeader.$refs?.Filter;
      if (!filterComp) {
        return;
      }
      
      // 根据参数组合进行不同处理
      if (filterParams && Object.keys(filterParams).length > 0) {
        // 有筛选参数的情况
        if (selectFirst) {
          // 筛选 + 选中第一条
          await this.executeFilterAndSelect(filterComp, leadsTable, filterParams);
        } else {
          // 只筛选，不选中
          await this.executeFilterOnly(filterComp, filterParams);
        }
      } else {
        // 没有筛选参数的情况
        if (selectFirst) {
          // 只选中第一条，不筛选
          await this.executeSelectOnly(leadsTable);
        } else {
          // 既不筛选也不选中
        }
      }
    },

    /**
     * 设置筛选条件并查询数据
     * @param {Object} filterComp - 筛选组件引用
     * @param {Object} filterParams - 筛选参数
     * @param {boolean} selectFirst - 是否选中第一条数据
     */
    /**
     * 执行筛选并选中第一条数据
     * @param {Object} filterComp - 筛选组件引用
     * @param {Object} leadsTable - 表格组件引用
     * @param {Object} filterParams - 筛选参数
     */
    async executeFilterAndSelect(filterComp, leadsTable, filterParams) {
      try {
        // 第一步：设置筛选条件
        await this.setFilterConditions(filterComp, filterParams);
        
        // 第二步：执行筛选查询
        await this.executeFilterQuery(filterComp);
        
        // 第三步：等待数据加载完成
        await this.waitForDataLoaded(leadsTable);
        
        // 第四步：选中第一条数据
        await this.selectFirstRowData(leadsTable);
      } catch (error) {
        // 静默处理错误
      }
    },

    /**
     * 只执行筛选，不选中数据
     * @param {Object} filterComp - 筛选组件引用
     * @param {Object} filterParams - 筛选参数
     */
    async executeFilterOnly(filterComp, filterParams) {
      try {
        // 第一步：设置筛选条件
        await this.setFilterConditions(filterComp, filterParams);
        
        // 第二步：执行筛选查询
        await this.executeFilterQuery(filterComp);
      } catch (error) {
        // 静默处理错误
      }
    },

    /**
     * 只选中第一条数据，不筛选
     * @param {Object} leadsTable - 表格组件引用
     */
    async executeSelectOnly(leadsTable) {
      try {
        // 等待数据加载完成
        await this.waitForDataLoaded(leadsTable);
        
        // 选中第一条数据
        await this.selectFirstRowData(leadsTable);
      } catch (error) {
        // 静默处理错误
      }
    },

    /**
     * 设置筛选条件
     * @param {Object} filterComp - 筛选组件引用
     * @param {Object} filterParams - 筛选参数
     */
    async setFilterConditions(filterComp, filterParams) {
      if (!filterComp || !filterComp.filterMenuSelects) {
        throw new Error('筛选组件或筛选菜单不存在');
      }
      
      // 设置筛选组件的值
      filterComp.filterMenuSelects.forEach(item => {
        if (filterParams[item.fieldName] !== undefined) {
          item.value = filterParams[item.fieldName];
        }
      });
      
      // 同时更新 preFilterMenuSelects，确保取消筛选时能正确回显
      filterComp.preFilterMenuSelects = JSON.parse(
        JSON.stringify(filterComp.filterMenuSelects)
      );
      
      // 等待筛选条件设置完成
      await this.$nextTick();
      await new Promise(resolve => setTimeout(resolve, 100));
    },

    /**
     * 执行筛选查询
     * @param {Object} filterComp - 筛选组件引用
     */
    async executeFilterQuery(filterComp) {
      if (typeof filterComp.handleSubmit !== 'function') {
        throw new Error('筛选组件的handleSubmit方法不存在');
      }
      
      // 调用筛选组件的handleSubmit方法
      filterComp.handleSubmit();
      
      // 等待筛选查询执行完成
      await new Promise(resolve => setTimeout(resolve, 200));
    },

    /**
     * 等待数据加载完成
     * @param {Object} leadsTable - 表格组件引用
     */
    async waitForDataLoaded(leadsTable) {
      if (!leadsTable) {
        throw new Error('表格组件不存在');
      }
      
      // 等待数据加载完成
      await this.waitForParticipantsList(leadsTable);
      
      // 强制更新表格组件
      await this.forceUpdateTable(leadsTable);
    },

    /**
     * 选中第一条数据
     * @param {Object} leadsTable - 表格组件引用
     */
    async selectFirstRowData(leadsTable) {
      if (!leadsTable.lists || leadsTable.lists.length === 0) {
        return;
      }
      
      // 使用重试机制确保选中成功
      await this.retrySelectFirstRow(leadsTable, 0);
    },

    /**
     * 强制更新表格组件
     * @param {Object} leadsTable - 表格组件引用
     */
    async forceUpdateTable(leadsTable) {
      // 强制更新Vue响应式数据
      this.$forceUpdate();
      leadsTable.$forceUpdate();
      
      // 如果表格组件存在，也强制更新
      if (leadsTable.$refs?.table) {
        leadsTable.$refs.table.$forceUpdate();
      }
      
      // 等待DOM更新
      await this.$nextTick();
      
      // 再次等待一帧，确保表格完全渲染
      await new Promise(resolve => setTimeout(resolve, 100));
    },

    /**
     * 重试选中第一条数据
     * @param {Object} leadsTable - 表格组件引用
     * @param {number} retryCount - 重试次数
     */
    async retrySelectFirstRow(leadsTable, retryCount) {
      const maxRetries = 10;
      
      if (retryCount >= maxRetries) {
        return;
      }
      
      // 等待DOM渲染
      await this.$nextTick();
      
      // 等待更长时间，确保表格完全渲染
      await new Promise(resolve => setTimeout(resolve, 100 + retryCount * 50));
      
      if (leadsTable.$refs?.table) {
        try {
          // 先清除所有选中状态
          leadsTable.$refs.table.clearSelection();
          
          // 选中第一行
          leadsTable.$refs.table.toggleRowSelection(leadsTable.lists[0], true);
          
          // 验证选中是否成功
          const selectedRows = leadsTable.$refs.table.selection || [];
          if (selectedRows.length === 0) {
            // 如果选中失败，继续重试
            await new Promise(resolve => setTimeout(resolve, 200));
            await this.retrySelectFirstRow(leadsTable, retryCount + 1);
          }
        } catch (error) {
          // 如果出错，继续重试
          await new Promise(resolve => setTimeout(resolve, 200));
          await this.retrySelectFirstRow(leadsTable, retryCount + 1);
        }
      } else {
        // 如果找不到表格组件，继续重试
        await new Promise(resolve => setTimeout(resolve, 200));
        await this.retrySelectFirstRow(leadsTable, retryCount + 1);
      }
    },

    /**
     * 选中第一条数据（兼容旧版本调用）
     * @param {Object} leadsTable - 表格组件引用
     */
    selectFirstRow(leadsTable) {
      // 调用新的选中方法
      this.selectFirstRowData(leadsTable);
    },

    /**
     * 测试方法：用于调试和验证不同参数组合
     */
    testSwitchToParticipantsAndSelectFirst() {
      this.$nextTick(async () => {
        try {
          // 测试场景1：筛选未审核数据并选中第一条
          await this.switchToParticipantsAndSelectFirst({ reviewStatus: [0] }, true);
          
          // 等待一段时间后测试下一个场景
          setTimeout(async () => {
            // 测试场景2：只筛选未签到，不选中
            await this.switchToParticipantsAndSelectFirst({ signStatus: [3] }, false);
            
            // 等待一段时间后测试下一个场景
            setTimeout(async () => {
              // 测试场景3：只选中第一条，不筛选
              await this.switchToParticipantsAndSelectFirst(null, true);
              
              // 等待一段时间后测试最后一个场景
              setTimeout(async () => {
                // 测试场景4：仅切换tab，不筛选不选中
                await this.switchToParticipantsAndSelectFirst(null, false);
              }, 3000);
            }, 3000);
          }, 3000);
        } catch (error) {
          // 静默处理错误
        }
      });
    },
    handleActionClick(item = {}) {
      const { type } = item;
      switch (type) {
        case 'baseInfo':
        case 'signInSetting':
        case 'invitationSetting':
          this.handleRouterPush(this.getRouteConfig(type));
          break
        case 'trigger':
        case 'radar':
        case 'analysis':
        case 'kanban':
          this.handleSelect(type);
          break
        case 'meetingHome':
        case 'otherContent':
        case 'promotionPoster':
        case 'promotionContent':
        case 'manualAddParticipants':
        case 'selectedParticipantsNotification':
        case 'manualUpdateParticipantsSignInStatus':
        case 'sendActivityContent':
          this.highlightGuide(type);
          break
        case 'viewUnreviewedList':
          this.$nextTick(async () => {
            await this.switchToParticipantsAndSelectFirst({ reviewStatus: [0] }, true);
          });
          break
        case 'importExternalData':
          this.handleImportExternalData()
          break
        case 'viewUnsignedList':
          this.handleViewUnsignedList()
          break
        case 'exportParticipantsData':
          this.handleExportParticipantsData()
          break
        case 'viewLeadsProcessingStatus':
          this.handleViewLeadsProcessingStatus()
          break
        default:
          break
      }
    },
    async highlightGuide(type) {
      // 判断是否有存储高亮状态 以及是否过期
      const guideState = JSON.parse(localStorage.getItem(`guide_state_${type}`));
      // 过期时间为1年
      if (guideState && guideState.finished && guideState.time > Date.now() - 1000 * 60 * 60 * 24 * 365) {
        this.handleAfterAction(type);
        return;
      }
      this.currentStepType = type;
      if (!guideStepMap[type]) return;
      try {
        localStorage.setItem(`guide_state_${type}`, JSON.stringify({
          finished: true,
          time: Date.now()
        }));
        await this.clearPreviousGuideState();
        await this.executePreAction(type, 0);
        this.guideSteps = guideStepMap[type];
        this.guideVisible = true;
      } catch (error) {
      }
    },
    handleAfterAction(type) {
      switch (type) {
        case 'meetingHome':
          // 触发子组件spread-content-list中的handleEditMaterial方法
          this.$nextTick(async () => {
            await this.executeSpreadContentListOperation('edit');
          });
          break
        case 'otherContent':
          // 打开添加推广内容弹窗
          this.openAddContentDialog();
          break
        case 'promotionPoster':
          // handlePosterContent 并且item为主页海报
          this.$nextTick(async () => {
            await this.executeSpreadContentListOperation('poster');
          });
          break
        case 'promotionContent':
          // 触发推广按钮，找到会议主页类型的物料
          this.$nextTick(async () => {
            await this.executeSpreadContentListOperation('preview');
          });
          break
        case 'manualAddParticipants':
          // 弹出添加参会人员弹窗
          this.executeParticipantsOperation('add');
          break
        case 'selectedParticipantsNotification':
          // 跳转到参会人员列表并选中第一条数据（不筛选）
          this.$nextTick(async () => {
            await this.switchToParticipantsAndSelectFirst(null, true);
          });
          break
        case 'manualUpdateParticipantsSignInStatus':
          // 跳转到参会人员列表，筛选未签到并选中第一条数据
          this.$nextTick(async () => {
            await this.switchToParticipantsAndSelectFirst({ signStatus: [3] }, true);
          });
          break
        case 'sendActivityContent':
          // 打开添加推广内容弹窗
          this.openAddContentDialog();
          break
        default:
          break
      }
    },
    onGuideFinish(isExecute = false) {
      this.guideVisible = false;
      if(isExecute) {
        this.handleAfterAction(this.currentStepType);
      }
      this.currentStepType = '';
      this.currentPreActionPromise = null;
    },
    handleRouterPush(item) {
      this.$router.push(item)
    },
    async executePreAction(type, stepIndex) {
      const step = guideStepMap[type]?.[stepIndex];
      if (!step?.preAction) return;
      try {
        this.currentPreActionPromise = step.preAction(this);
        await this.currentPreActionPromise;
        this.currentPreActionPromise = null;
      } catch (error) {
        this.currentPreActionPromise = null;
      }
    },
    async handleBeforeStep(stepIndex) {
      await this.executePreAction(this.currentStepType, stepIndex);
    },
    getRouteConfig(type) {
      const routeMap = {
        baseInfo: {
          name: 'meeting-setting',
          query: { defaultActiveMenu: 'MeetingBasicInfo' },
          params: { isBaseInfoEidt: true }
        },
        signInSetting: {
          name: 'meeting-setting',
          query: { defaultActiveMenu: 'MeetingSignInSetting' }
        },
        invitationSetting: {
          name: 'meeting-setting',
          query: { defaultActiveMenu: 'MeetingInvitationSetting' }
        }
      };
      return routeMap[type] || {};
    },
    // 清理之前的引导状态
    async clearPreviousGuideState() {
      try {
        // 隐藏当前的引导
        this.guideVisible = false;
        
        // 等待一个渲染周期，确保引导组件完全隐藏
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清理所有高亮元素
        const highlights = document.querySelectorAll('.marketing__guide-highlight');
        highlights.forEach(highlight => {
          Object.assign(highlight.style, {
            position: 'fixed',
            top: '0px',
            left: '0px',
            width: '0px',
            height: '0px',
            opacity: '0',
            visibility: 'hidden'
          });
        });

        // 清理遮罩区域
        const maskAreas = document.querySelectorAll('.marketing__guide-mask-area');
        maskAreas.forEach(area => {
          area.style.display = 'none';
        });

      } catch (error) {
      }
    },
    // 进入参会人员tab 打开class="active-member__more"的ElDropdown
    handleImportExternalData() {
      this.switchToParticipantsTab(() => {
        const participantsRef = this.getParticipantsRef();
        this.triggerElDropdownHover(participantsRef);
      });
    },

    // 进入参会人员tab 筛选状态为未签到
    handleViewUnsignedList() {
      this.$nextTick(async () => {
        // 筛选未签到数据 (signStatus: 3 表示未签到)
        await this.switchToParticipantsAndSelectFirst({ signStatus: [3] }, true);
      });
    },

    // 进入参会人员tab 打开class="active-member__more"的ElDropdown
    handleExportParticipantsData() {
      this.switchToParticipantsTab(() => {
        const participantsRef = this.getParticipantsRef();
        this.triggerElDropdownHover(participantsRef);
      });
    },
    // 打开市场活动详情中的线索详情
    handleViewLeadsProcessingStatus() {
      const id = this.marketingEventId;
      requireAsync("crm-components/showdetail/showdetail", Detail => {
        if (!this.$detail) {
          this.$detail = new Detail({ showMask: true,subtab: 'LeadsObj_marketing_event_id_related_list' });
        }
        this.$detail.setApiName("MarketingEventObj");
        setTimeout(() => this.$detail.show(id), 0);
      });
    }
  },
  data() {
    return {
      flowSteps: [
        {
          title: $t('marketing.pages.meeting_marketing.hqzb_2fccf4'),
          items: [
            {
              title: $t('marketing.commons.hdch_c8cba8'),
              subItems: [
                {
                  title: $t('marketing.pages.meeting_marketing.gxjbxx_be9e1a'),
                  action: true,
                  enabled: true,
                  type: 'baseInfo'
                },
                {
                  title: $t('marketing.pages.meeting_marketing.ckyyrwkb_1e0bdd'),
                  action: true,
                  enabled: true,
                  type: 'kanban'
                }
              ]
            },
            {
              title: $t('marketing.pages.meeting_marketing.nrzz_07dd38'),
              subItems: [
                {
                  title: $t('marketing.pages.meeting_marketing.gxhyzyhbmb_5c3e45'),
                  action: true,
                  enabled: true,
                  type: 'meetingHome'
                },
                {
                  title: $t('marketing.pages.meeting_marketing.tjcpzlwjdt_b69f03'),
                  action: true,
                  enabled: true,
                  type: 'otherContent'
                },
                {
                  title: $t('marketing.pages.meeting_marketing.zzdqdtghb_dea570'),
                  action: true,
                  enabled: true,
                  type: 'promotionPoster'
                }
              ]
            },
            {
              title: $t('marketing.pages.meeting_marketing.hdsz_773a88'),
              subItems: [
                {
                  title: $t('marketing.pages.meeting_marketing.pzbmcgchtx_929cd5'),
                  action: true,
                  enabled: true,
                  type: 'trigger'
                },
                {
                  title: $t('marketing.pages.meeting_marketing.ypryszjqdm_8fe1c0'),
                  action: true,
                  enabled: true,
                  type: 'signInSetting'
                },
                {
                  title: $t('marketing.pages.meeting_marketing.ygyyjdbmsz_a52e4e'),
                  action: true,
                  enabled: true,
                  type: 'invitationSetting'
                }
              ]
            },
            {
              title: $t('marketing.pages.meeting_marketing.yytg_1f194d'),
              subItems: [
                {
                  title: $t('marketing.pages.meeting_marketing.qflbddqdhd_2f30ef'),
                  action: true,
                  enabled: true,
                  type: 'promotionContent'
                },
                {
                  title: $t('marketing.pages.meeting_marketing.zdkhqdbxfy_b904cc'),
                  action: true,
                  enabled: true,
                  type: 'manualAddParticipants'
                }
              ]
            },
            {
              title: $t('marketing.pages.meeting_marketing.bmgj_d8c35d'),
              subItems: [
                {
                  title: $t('marketing.commons.bmxxsh_83e165'),
                  action: true,
                  enabled: true,
                  type: 'viewUnreviewedList'
                },
                {
                  title: $t('marketing.pages.meeting_marketing.sdbftz_bac835'),
                  action: true,
                  enabled: true,
                  type: 'selectedParticipantsNotification'
                },
                {
                  title: $t('marketing.commons.drwbbmsj_0aec68'),
                  action: true,
                  enabled: true,
                  type: 'importExternalData'
                }
              ]
            }
          ],
          completed: true,
          active: true,
          icon: 'icondone',
          status: 'before'
        },
        {
          title: $t('marketing.pages.meeting_marketing.hyjhz_76425f'),
          items: [
            {
              title: $t('marketing.pages.meeting_marketing.hdqd_e075fd'),
              subItems: [
                {
                  title: $t('marketing.pages.meeting_marketing.ckwqdmx_0b7a43'),
                  action: true,
                  enabled: true,
                  type: 'viewUnsignedList'
                },
                {
                  title: $t('marketing.pages.meeting_marketing.ypryszjqdm_8fe1c0'),
                  action: true,
                  enabled: true,
                  type: 'signInSetting'
                }
              ]
            },
            {
              title: $t('marketing.pages.meeting_marketing.xxts_81bbd9'),
              subItems: [
                {
                  title: $t('marketing.pages.meeting_marketing.pzsszdtz_de853f'),
                  action: true,
                  enabled: true,
                  type: 'trigger'
                },
                {
                  title: $t('marketing.pages.meeting_marketing.sdxzchrytz_0b6983'),
                  action: true,
                  enabled: true,
                  type: 'selectedParticipantsNotification'
                }
              ]
            },
            {
              title: $t('marketing.pages.meeting_marketing.bmxxhq_07c349'),
              subItems: [
                {
                  title: $t('marketing.pages.meeting_marketing.dcchrysj_3125f4'),
                  action: true,
                  enabled: true,
                  type: 'exportParticipantsData'
                }
              ]
            }
          ],
          completed: false,
          active: false,
          icon: 'icononprogress',
          status: 'processing'
        },
        {
          title: $t('marketing.pages.meeting_marketing.hyjsh_40a752'),
          items: [
            {
              title: $t('marketing.pages.meeting_marketing.chrygj_659d90'),
              subItems: [
                {
                  title: $t('marketing.pages.meeting_marketing.htsdbq_1f20d3'),
                  action: true,
                  enabled: true,
                  type: 'manualUpdateParticipantsSignInStatus'
                },
                {
                  title: $t('marketing.pages.meeting_marketing.ckxsclqk_2acbc0'),
                  action: true,
                  enabled: true,
                  type: 'viewLeadsProcessingStatus'
                }
              ]
            },
            {
              title: $t('marketing.pages.meeting_marketing.hhnrtg_2feddb'),
              subItems: [
                {
                  title: $t('marketing.pages.meeting_marketing.wjzlhdjcjj_de0c4e'),
                  action: true,
                  enabled: true,
                  type: 'sendActivityContent'
                }
              ]
            },
            {
              title: $t('marketing.commons.sjfx_6450d8'),
              subItems: [
                {
                  title: $t('marketing.pages.meeting_marketing.ckqfjgjrwz_7dd2cb'),
                  action: true,
                  enabled: true,
                  type: 'radar'
                },
                {
                  title: $t('marketing.pages.meeting_marketing.ckzthdsjfx_a6e432'),
                  action: true,
                  enabled: true,
                  type: 'analysis'
                }
              ]
            }
          ],
          completed: false,
          active: false,
          status: 'after'
        }
      ],
      guideSteps: [],
      guideVisible: false,
      currentStepType: '',
      currentPreActionPromise: null, // 用于存储当前正在执行的 preAction
    };
  }
};
