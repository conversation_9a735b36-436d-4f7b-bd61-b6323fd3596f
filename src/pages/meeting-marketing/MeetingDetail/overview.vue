<template>
  <div class="detail">
    <div class="rowcard">
      <AttendingSituation style="flex: 100;"></AttendingSituation>
    </div>
    <div class="rowcard">
      <invitationContent ref="invitationContent" />
    </div>
  </div>
</template>

<script>
import AttendingSituation from "./components/AttendingSituation.vue"; // 参会情况
import Participants from "../MeetingParticipants"; // 参会人员
import invitationContent from '../MeetingInvitationContent/index.vue'

export default {
  components: {
    AttendingSituation,
    Participants,
    invitationContent,
  },
  data(){
    return {
    }
  },
  computed: {
    marketingEventDetail() {
      return this.$store.state.MeetingMarketing.marketingEventDetail;
    },
    marketingEventId() {
      return (
        (this.marketingEventDetail &&
          this.marketingEventDetail.marketingEvent &&
          this.marketingEventDetail.marketingEvent.id) ||
        ""
      );
    },
  },
  mounted() {}
};
</script>

<style lang="less" scoped>
.detail {
  height: 100%;
  background: #f2f2f5;
  .rowcard {
    margin: 0 0 12px 0;
    display: flex;
    > :not(:last-child) {
      margin-right: 15px;
    }
  }
}
</style>
