<template>
    <div class="meeting-info">
      <div class="meeting__cover">
        <LiveStatus
          v-if="marketingEventDetail.marketingEvent"
          class="meeting-image-status"
          :life-status="marketingEventDetail.marketingEvent.lifeStatus"
          :status="conferenceDetail.flowStatus"
        />
        <img
          v-if="conferenceDetail.coverImageUrl"
          :src="conferenceDetail.coverImageUrl"
        />
        <div v-else class="empty"></div>
      </div>
      <div class="meeting__baseinfo">
        <div class="baseinfo__name km-t-ellipsis1">
          {{ conferenceDetail.title }}
        </div>
        <div class="meeting__date-address">
          <div class="baseinfo__time">
            {{ $t("marketing.commons.hysj_6721f1")
            }}{{ conferenceDetail.startTimeCN || "--" }}
            {{ $t("marketing.commons.z_981cbe") }}
            {{ conferenceDetail.endTimeCN || "--" }}
          </div>
          <div class="baseinfo__separator">|</div>
          <div class="baseinfo__place km-t-ellipsis2">
            {{ $t("marketing.commons.hydd_efb7f9")
            }}{{ conferenceDetail.location || "--" }}
          </div>
        </div>
        <div class="ROI__info">
          <div class="ROI__expect">
            <div class="info__title">
              {{ $t("marketing.commons.yjsr_b333b6") }}/{{
                $t("marketing.commons.yqcb_7d13d6")
              }}：
            </div>
            <div class="info__data">
              {{ marketingEventDetail.expectedIncome || "0" }}/{{
                marketingEventDetail.expectedCost || "0"
              }}({{ marketingEventDetail.expectedROI * 100 || "0" }}%)
            </div>
          </div>
          <div class="baseinfo__separator">|</div>
          <div class="ROI__expect">
            <div class="info__title">
              {{ $t("marketing.commons.sjsr_5b39df") }}/{{
                $t("marketing.commons.sjcb_5ca864")
              }}：
            </div>
            <div class="info__data">
              {{ marketingEventDetail.actualIncome || "0" }}/{{
                marketingEventDetail.actualCost || "0"
              }}({{ marketingEventDetail.actualROI * 100 || "0" }}%)
            </div>
          </div>
          <div v-if="marketingEventAudit" class="baseinfo__separator">|</div>
          <div v-if="marketingEventAudit" class="ROI__expect">
            <div class="info__title">
              {{ $t('marketing.commons.shzt_b6d0e9' )}}：
            </div>
            <div class="info__data">
              {{CampaignReviewStatusLabel[lifeStatus] || '--'}}
            </div>
          </div>
        </div>
      </div>
      <div class="meeting__buttons">
        <fx-button
          size="small"
          plain 
          @click="handleSetting"
          >
          {{ $t('marketing.commons.sz_e366cc') }}
        </fx-button>
        <fx-button
          size="small"
          plain
          @click="handleOpenMarkketingDetail"
          >
          {{ $t('marketing.pages.meeting_marketing.gdxx_a86903') }}
        </fx-button>
      </div>
    </div>
</template>

<script>
import SpreadSideslipDialog from "@/components/SpreadSideslipDialog";
import ShareSettingDialog from "./ShareSettingDialog";
import { MEETING_MENUS, MEETING_STATUS_MAP } from "./config";
import { requireAsync } from "@/utils";
import http from "@/services/http/index";
import { CampaignReviewStatusLabel, CampaignReviewStatusTips } from "@/utils/statusEnum";
import { mapState, mapActions } from 'vuex';
import QuestionTooltip from '@/components/questionTooltip/index';
import LiveStatus from '../../meeting-init/components/liveStatus.vue'

export default {
  components: {
ElLink: FxUI.Link,
ElButton: FxUI.Button,
ElDropdown: FxUI.Dropdown,
ElDropdownItem: FxUI.DropdownItem,
ElDropdownMenu: FxUI.DropdownMenu,
ElPopover: FxUI.Popover,
ElImage: FxUI.Image,
SpreadSideslipDialog,
ShareSettingDialog,
Tooltip: FxUI.Tooltip,
QuestionTooltip,
LiveStatus
},
  data() {
    return {
      MEETING_MENUS,
      MEETING_STATUS_MAP,
      isShowSpreadDialog: false,

      model_miniAppQrUrl: "",
      isShowQrPopover: false,
      loading_qrcode: false,
      message:{},
      CampaignReviewStatusLabel,
      CampaignReviewStatusTips
    };
  },
  computed: {
    ...mapState('MarketingEventSet', ['marketingEventAudit']),
    material() {
      return {
        type: 3,
        objectType: 13,
        id: this.conferenceDetail.id,
        activityDetailSiteId: this.conferenceDetail.activityDetailSiteId,
        marketingEventId: this.conferenceDetail.marketingEventId,
        title: this.conferenceDetail.title
      };
    },
    conferenceCompleteMapping() {
      return this.conferenceDetail.conferenceCompleteMapping;
    },
    conferenceDetail() {
      return this.$store.state.MeetingMarketing.conferenceDetail;
    },
    marketingEventDetail() {
      return this.$store.state.MeetingMarketing.marketingEventDetail;
    },
    marketingEventId() {
      return this.marketingEventDetail.marketingEvent.id;
    },
    openDingDing() {
      return this.$store.state.Global.openDingDing;
    },
    lifeStatus() {
      const _detail = this.$store.state.MeetingMarketing.marketingEventDetail;
      if(_detail && _detail.marketingEvent) {
        return _detail.marketingEvent.lifeStatus || ''
      }
      return ''
    },
  },
  created(){
    this.queryMarketingEventCommonSetting();
  },
  mounted() {
  },
  methods: {
    ...mapActions('MarketingEventSet', ['queryMarketingEventCommonSetting']),
    handleOpenMarkketingDetail() {
      const id = this.marketingEventId;
      this.detailLoading = true;
      requireAsync("crm-components/showdetail/showdetail", Detail => {
        this.detailLoading = false;
        if (!this.$detail) {
          this.$detail = new Detail({ showMask: true });
        }
        this.$detail.setApiName("MarketingEventObj");
        setTimeout(() => this.$detail.show(id), 0);
      });
    },
    handleSetting(){
      this.$router.push({
        path: 'meeting-setting'
      });
    }
  }
};
</script>

<style lang="less" scoped>
.meeting-info {
  background: #fff;
  display: flex;
  justify-content: space-between;
  padding: 12px;
  .meeting__cover {
    flex-shrink: 0;
    width: 150px;
    height: 84px;
    position: relative;
    overflow: hidden;
    .meeting-image-status{
        width: 70px;
        height: 28px;
        position: absolute;
        top: 2px;
        left: 0;
    }
    img {
      width: 100%;
      height: 100%;
      border-radius: 8px;
      object-fit: cover;
    }
    .empty {
      // background-image: url("../../../../assets/images/defualt-pic.png");
      // width: 100%;
      // height: 100%;
      // background-size: cover;
      width: 100%;
      height: 100%;
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
      background: #f2f2f5;
      border-radius: 4px;
    }
  }
  .meeting__baseinfo {
    flex: 1;
    flex-shrink: 0;
    margin-left: 12px;
    position: relative;
    padding: 6px 0;
    .baseinfo__name {
      color: #181c25;
      font-size: 18px;
    }
    .baseinfo__time,
    .baseinfo__place {
      color: #91959e;
      font-size: 12px;
    }
    .meeting__date-address{
      display: flex;
      align-items: center;
      margin-top: 6px;
    }
    .baseinfo__separator{
      margin: 0 10px;
      color: #91959e;
    }
    .baseinfo__operation {
      display: flex;
      margin-top: 20px;
      .meeting__edit {
        margin-right: 20px;
        font-size: 12px;
      }
      .meeting__share {
        margin-right: 20px;
        font-size: 12px;
      }
    }
    .ROI__info {
      display: flex;
      color: #91959e;
      margin-top: 6px;
      .ROI__expect{
        display: flex;
      }
    }
  }
  .meeting__buttons {
    flex-shrink: 0;
    position: relative;
    display: flex;
    align-items: center;
    .iconfont {
      font-size: 20px;
    }
    .buttons__qr {
      margin-left: 10px;
      width: 50px;
      height: 40px;
      color: #b4b6c0;
      font-size: 15px;
    }

    .spread-btn {
      display: flex;
      align-items: center;
      .question {
        margin-left: 4px;
        filter: brightness(100);
      }
    }
    .dropdown-wrapper {
      margin-left: 10px;
      position: relative;
      .el-icon-warning-outline {
        color: rgb(242, 116, 116);
        position: absolute;
        top: -6px;
        right: -4px;
        background: #fff;
        font-size: 16px;
      }
    }
  }
  .meeting__ROI {
    flex-shrink: 0;
    margin: 0 20px 0 10px;
    .ROI__header {
      margin-top: 15px;
      display: flex;
      justify-content: space-between;
      font-size: 12px;
      .ROI__title {
        color: #181c25;
      }
      .ROI__detail {
        font-size: 12px;
      }
    }
  }
}
.popover--qr {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 0 40px;
  /deep/ .el-image {
    width: 140px;
    height: 140px;
    .image-slot {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      width: 100%;
      height: 100%;
      background: #f5f7fa;
      font-size: 13px;
      cursor: pointer;
      color: var(--color-primary06,#407FFF);
      .el-icon-picture-outline {
        color: #909399;
        margin-bottom: 5px;
        font-size: 16px;
      }
    }
  }
  .popover__tips--qr {
    color: #91959e;
    font-size: 12px;
    margin-top: 12px;
  }
}
</style>
