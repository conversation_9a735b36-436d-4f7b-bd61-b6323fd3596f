export const MEETING_MENUS = [{
  id: 'meeting-marketing-create',
  label: $t('marketing.pages.meeting_marketing.bjjbxx_be1da0'),
  isRouter: true,
  route: {
    name: 'meeting-marketing-create',
  },
}, {
  id: 'meeting-enroll-setting',
  label: $t('marketing.pages.meeting_marketing.hybmsz_6be8e9'),
  isRouter: true,
  route: {
    name: 'meeting-enroll-setting',
  },
}, {
  id: 'meeting-signin',
  label: $t('marketing.pages.meeting_marketing.hyqdsz_dbec1a'),
  isRouter: true,
  route: {
    name: 'meeting-signin',
  },
}, {
  id: 'meeting-invite',
  label: $t('marketing.pages.meeting_marketing.hyyysz_e4a3bb'),
  isRouter: true,
  route: {
    name: 'meeting-invite',
  },
},
{
  id: 'meeting-setting',
  label: $t('marketing.pages.meeting_marketing.hysz_b08c9e'),
  isRouter: true,
  route: {
    name: 'meeting-setting',
  },
},
// {
//   id: 'share-setting',
//   label: $t('marketing.commons.wxfxsz_1733b6'),
//   isRouter: false,
// },
{
  id: 'sync-rules',
  label: $t('marketing.commons.bmsjzdtbgz_03e67b'),
  isRouter: false,
},
//  {
//   id: 'meeting-poster',
//   label: '推广海报',
//   isRouter: true,
//   route: {
//     name: 'meeting-poster',
//     // params: {pageType: 'qrcode'},
//   },
// }, {
//   id: 'meeting-invitation-content',
//   label: '推广内容',
//   isRouter: true,
//   route: {
//     name: 'meeting-invitation-content',
//     params: {pageType: 'invitation'},
//   },
// },
//  {
//   id: 'share-setting',
//   label: '推广雷达',
//   menuHide: true,
//   isRouter: true,
//   route: {
//     name: 'meeting-data',
//   },
// }, {
//   id: 'share-setting',
//   label: '分享设置',
//   menuHide: true,
//   isRouter: false,
// },
//  {
//   id: 'trigger-list',
//   label: 'SOP',
//   isRouter: true,
//   route: {
//     name: 'trigger-list',
//     params: {type: 'conference'},
//   },
// }
]
export const FAST_ENTER_CARDS = [{
  id: 'meeting-before',
  label: $t('marketing.pages.meeting_marketing.hq_1c30dc'),
  icon: '&#xe620;',
  links: [{
    id: 'enroll',
    label: $t('marketing.pages.meeting_marketing.szhybm_fcd0ee'),
    icon: '&#xe63d;',
    route: {
      name: 'meeting-enroll-setting',
    },
  }, {
    id: 'poster',
    label: $t('marketing.pages.meeting_marketing.zztghb_678b81'),
    icon: '&#xe63f;',
    route: {
      name: 'meeting-detail',
      query: { type: 'invitationContent' },
    },
  }, {
    id: 'content',
    label: $t('marketing.commons.zztgnr_3c501b'),
    icon: '&#xe627;',
    route: {
      name: 'meeting-detail',
      query: { type: 'invitationContent' },
    },
  }],
}, {
  id: 'meeting-in',
  label: $t('marketing.pages.meeting_marketing.hz_5b3b70'),
  icon: '&#xe61f;',
  links: [{
    id: 'signin',
    label: $t('marketing.pages.meeting_marketing.xcqdsz_c68d6e'),
    icon: '&#xe621;',
    route: {
      name: 'meeting-signin',
    },
  }],
}, {
  id: 'meeting-after',
  label: $t('marketing.pages.meeting_marketing.hh_e81339'),
  icon: '&#xe622;',
  links: [{
    id: 'download',
    label: $t('marketing.pages.meeting_marketing.hyzlxz_4a8da9'),
    icon: '&#xe631;',
    route: {
      name: 'meeting-invitation-content',
      // query: { type: 'SetitemsChannelManage' }
    },
  }, {
    id: 'questionnaire',
    label: $t('marketing.pages.meeting_marketing.zztcwj_f98c67'),
    icon: '&#xe626;',
    route: {
      name: 'meeting-invitation-content',
      // query: { type: 'SetitemsChannelManage' }
    },
  }],
}]
export const MEETING_STATUS_MAP = {
  0: {
    label: $t('marketing.commons.wfb_637e8b'),
    backgroundColor: '#FCB058',
  },
  1: {
    label: $t('marketing.commons.wfb_637e8b'),
    backgroundColor: '#FCB058',
  },
  2: {
    label: $t('marketing.commons.wks_dd4e55'),
    backgroundColor: '#81B2FE',
  },
  3: {
    label: $t('marketing.commons.jhz_fb852f'),
    backgroundColor: '#7FC25D',
  },
  4: {
    label: $t('marketing.commons.yjs_047fab'),
    backgroundColor: '#B4B8C1',
  },
  5: {
    label: $t('marketing.commons.yty_69b0f6'),
    backgroundColor: '#B4B8C1',
  },
  99: {
    label: $t('marketing.commons.ysc_5cc232'),
    backgroundColor: '#B4B8C1',
  },
}
