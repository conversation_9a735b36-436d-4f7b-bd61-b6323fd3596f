<template>
  <div class="meeting-leads-table">
    <div class="meeting-leads-table__body">
      <MeetingLeadsTable
        :title="$t('marketing.commons.chry_a47805')"
        ref="leadsTable"
        :marketingEventId="marketingEventId"
        :auto-open-notification="autoOpenNotification"
      ></MeetingLeadsTable>
    </div>
  </div>
</template>

<script>
import MeetingLeadsTable from '@/components/ActiveMemberTable/MeetingLeadsTable';

export default {
  components: {
    MeetingLeadsTable
  },
  data() {
    return {
    }
  },
  computed: {
    marketingEventDetail() {
      return this.$store.state.MeetingMarketing.marketingEventDetail;
    },
    marketingEventId() {
      return (
        (this.marketingEventDetail &&
          this.marketingEventDetail.marketingEvent &&
          this.marketingEventDetail.marketingEvent.id) ||
        ""
      );
    },
  },
  methods: {
    openNotification() {
      if (this.$refs.leadsTable && this.$refs.leadsTable.handleNotice) {
        this.$refs.leadsTable.handleNotice();
      }
    }
  }
}
</script>

<style lang="less" scoped>
</style>