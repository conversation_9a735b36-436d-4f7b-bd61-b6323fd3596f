<template>
      <div class="attending-situation-card__content">
        <!-- 不需要标题 -->
        <div class="situation__title" v-if="showTitle">
          {{ $t('marketing.commons.sjgl_3111b4') }}
        </div>
          <div class="situation__info">
            <div class="situation__info-item" v-for="item in data_situations" :key="item.id">
              <div class="info__data">{{item.value}}
                <div class="info__unit" v-if="item.unit">{{item.unit}}</div>
              </div>
              <div class="info__title">{{item.label}}</div>
            </div>
          </div>
      </div>
</template>

<script>
import Card from '@/components/card';

const sitations = [{
  id: 'uv',
  icon: '&#xe618;',
  label: $t('marketing.commons.fwrs_c3c959'),
  value: 0,
},{
  id: 'enrollCount',
  icon: '&#xe617;',
  label: $t('marketing.commons.bmrs_a06b35'),
  value: 0,
}, {
  id: 'signInCount',
  icon: '&#xe618;',
  label: $t('marketing.commons.qdrs_a99a6b'),
  value: 0,
}, {
  id: 'signInRate',
  icon: '&#xe616;',
  label: $t('marketing.pages.meeting_marketing.qdl_0b0e86'),
  value: 0,
  unit: '%',
}]
export default {
  components: {
ElInput: FxUI.Input,
Card
},
  props: {
    hideMore: {
      type: Boolean,
      default: false,
    },
    showTitle: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      data_situation: {},
    }
  },
  computed: {
    data_situations() {
      const results = [];
      const cData = this.conferenceStatisticData;
      return sitations.map(item => {
        item.value = this.conferenceStatisticData[item.id] || 0;
        if (item.id === 'signInRate') {
          item.value = cData.enrollCount === 0 ? 0 : Math.round(cData.signInCount / cData.enrollCount * 100 * 100) / 100;
        }
        return item;
      });
    },
    conferenceStatisticData() {
      return this.$store.state.MeetingMarketing.conferenceStatisticData;
    },
  },
  created(){
    this.$store.dispatch('getConferenceStatisticData', {id: this.$store.state.MeetingMarketing.conferenceDetail.id})
  },
  methods: {
    handleMore() {
      this.$router.push({
        name: "meeting-detail",
        params: {
          id: this.$route.params.id
        },
        query: { type: 'radar' }
      })
    },
  },

}
</script>

<style lang="less" scoped>
  .attending-situation-card__content {
    background-color: #fff;
    padding: 16px 12px;
    border-radius: 8px;
    .situation__title {
      font-size: 14px;
      font-weight: 700;
      color: var(--color-neutrals19);
      line-height: 20px;
      margin-bottom: 10px;
    }
      .situation__info {
        display: flex;
        align-items: center;
        justify-content: center;
        .situation__info-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          flex: 1;
          .info__title {
            color: var(--Text-H1, #181C25);
            font-size: 14px;
            font-weight: 400;
            line-height: 18px;
          }
          .info__data {
            margin-bottom: 7px;
            color: var(--Text-H1, #181C25);
            font-size: 24px;
            font-weight: 700;
            line-height: 28px;
            display: flex;
          }
        }
      }
  }
</style>