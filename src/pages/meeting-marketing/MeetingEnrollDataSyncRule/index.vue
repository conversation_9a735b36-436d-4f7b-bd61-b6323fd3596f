<template>
  <div class="enroll-sync-rules-wrapper">
    <div class="enroll-sync-rules-content" v-loading="loading">
      <div
        class="rule-item-wrapper"
        v-for="(rule, rIdx) in rules"
        :key="rule.uuid"
      >
        <div class="item-header">
          <div class="item-title">{{ $t("marketing.commons.gz_b0fae0") }}</div>
          <div class="item-delete" @click="handleDeleteRule(rIdx)">
            {{ $t("marketing.commons.sc_2f4aad") }}
          </div>
        </div>
        <div class="item-tips">
          {{ $t("marketing.components.enroll_sync_rules.bschddhdcy_03378e") }}
        </div>
        <VMarketingEventSelector
          :index="rIdx"
          :value.sync="rule.campaigns"
          :marketingEventId="marketingEventId"
          :isMultiEvent="isMultiEvent"
          @update:marketingEventChange="handleMarketingEventChange"
        />
        <div
          v-for="(filter, fIdx) in rule.filters"
          :key="`${fIdx}_${rule.uuid}`"
        >
          <ObjectFilter
            ref="objectFilter"
            :key="`object-filter__${rIdx}-${fIdx}`"
            :apiName="filter.objectAPIName"
            :filterData="filter"
            :objectLists="objectLists"
            :initial="true"
            :noPadding="noPadding"
          />
        </div>
      </div>
      <div class="add-rule" @click="addRule">
        + {{ $t("marketing.commons.tjgz_49818f") }}
      </div>
      <div class="save-btn">
        <fx-button type="primary" size="small" @click="saveRules">{{ $t("marketing.commons.bc_be5fbb") }}</fx-button>
      </div>
    </div>
  </div>
</template>
<script>
import ObjectFilter from "@/components/object-filter";
import MultiObjectFilter from "@/components/multi-object-filter/index.vue";
import VMarketingEventSelector from "@/components/enroll-sync-rules/marketing-event-selector.vue";
import http from "@/services/http";
import { uuid } from "@/utils/helper";

export default {
  components: {
    ObjectFilter,
    MultiObjectFilter,
    VMarketingEventSelector
  },
  props: {
    noPadding: {
      type: Boolean,
      default: false
    },
    isMultiEvent: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: true,
      filters: [],
      curTransformType: {},
      checkList: [],
      objectLists: [
        {
          name: $t("marketing.commons.hdcy_ab964c"),
          value: "CampaignMembersObj"
        }
      ],
      defaultFilterData: [
        {
          name: $t("marketing.commons.hdcy_ab964c"),
          value: "CampaignMembersObj",
          objectAPIName: "CampaignMembersObj"
        }
      ],
      rules: [],
      filterVisible: false,
      rulesData: []
    };
  },
  computed: {
    initFilterItem() {
      return {
        campaigns: [],
        filters: [[this.defaultFilterData]]
      };
    },
    conferenceDetail() {
      return this.$store.state.MeetingMarketing.conferenceDetail;
    },
    marketingEventId() {
      return this.conferenceDetail.marketingEventDetail.marketingEventId;
    }
  },
  methods: {
    async initRulesData() {
      this.loading = true;
      const res = await http.getSyncRules({ id: this.marketingEventId });
      if (res && res.data && res.data.length) {
        this.rules = res.data.map(item => ({
          campaigns: item.targetMarketingEvents || [],
          filters: item.ruleGroupJson.map(item => [item])
        }));
      } else {
        this.rules = [
          {
            uuid: uuid(),
            ...this.initFilterItem
          }
        ];
      }
      this.loading = false;
    },
    addRule() {
      this.rules.push({
        uuid: uuid(),
        ...this.initFilterItem
      });
    },
    handleMarketingEventChange(index, data) {
      this.$set(this.rules, index, { ...this.rules[index], campaigns: data });
    },
    handleDeleteRule(idx) {
      this.rules.splice(idx, 1);
    },
    async saveRules() {
      const $objectFilters = Array.isArray(this.$refs.objectFilter)
        ? this.$refs.objectFilter
        : [this.$refs.objectFilter];
      const filterPromise = $objectFilters.map(
        filter =>
          new Promise(async resolve => {
            const data = await filter.getValue();
            resolve(...data);
          })
      );

      const _rules = [];
      const filterList = await Promise.all(filterPromise);
      if (filterList && filterList.length) {
        this.rules.forEach((item, idx) => {
          _rules.push({
            targetMarketingEvents: item.campaigns,
            ruleGroupJson: [filterList[idx]]
          });
        });
      }
      const res = await http.saveSyncRules({
        marketingEventId: this.marketingEventId,
        rules: _rules
      });
      if (res && res.errCode === 0) {
        FxUI.Message.success($t("marketing.commons.bccg_fbd249"));
        this.$emit("close");
      }
    }
  },
  mounted() {
    if (this.marketingEventId) {
      this.initRulesData();
    }
  }
};
</script>
<style lang="less" scoped>
.enroll-sync-rules-wrapper {
  padding: 20px;
  .enroll-sync-rules-content {
    font-size: 14px;
    line-height: 18px;
    width: 660px;
    min-width: 440px;
    .rule-item-wrapper {
      border: 1px solid #dee1e8;
      color: #545861;
      .item-header {
        background: #f2f3f5;
        height: 40px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 12px 0 16px;
      }
      .item-title {
        font-size: 16px;
        color: #181c25;
      }
      .item-delete {
        color: var(--color-info06, #407fff);
        cursor: pointer;
      }
      .item-tips {
        margin: 20px 16px 0;
      }
      .item-campaigns {
        margin: 6px 20px 20px;
        border: 1px solid #c1c5ce;
        border-radius: 4px;
      }
      .item-add-btn {
        color: var(--color-info06, #407fff);
        cursor: pointer;
        display: inline-block;
      }
    }
    .rule-item-wrapper + .rule-item-wrapper {
      margin-top: 20px;
    }
    .add-rule {
      height: 40px;
      background: #f2f3f5;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--color-info06, #407fff);
      margin-top: 20px;
    }
    .save-btn {
      margin-top: 20px;
    } 
  }
}
</style>
