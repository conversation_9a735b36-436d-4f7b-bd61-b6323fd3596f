<template>
  <div class="MeetingData">
    <!-- <div class="rowcard">
      <AttendingSituation style="flex: 48;" :hideMore="true"></AttendingSituation>
      <MeetingDataFans style="flex: 29" v-if="isWechatOpen"></MeetingDataFans>
      <MeetingDataChannel style="flex: 38;"></MeetingDataChannel>
    </div> -->
    <div class="rowcard">
      <MeetingDataRadar style="flex: 100"></MeetingDataRadar>
    </div>
  </div>
</template>

<script>
import AttendingSituation from '../MeetingDetail/components/AttendingSituation';
import MeetingDataChannel from './MeetingDataChannel';
import MeetingDataRadar from './MeetingDataRadar';
import MeetingDataFans from './MeetingDataFans';

export default {
  components: {
    AttendingSituation,
    MeetingDataChannel,
    MeetingDataRadar,
    MeetingDataFans,
  },
  computed: {
    isWechatOpen() {
      const { isWechatOpen, isQywxOpen, isMemberOpen, isEnterpriseLibraryOpen } = this.MARKETING_GLOBAL.OPENINFO;
      return isWechatOpen;
    },
  },
}
</script>

<style lang="less" scoped>
.MeetingData {
  background: #F2F2F5;
  .rowcard {
    display: flex;
    >:not(:last-child) {
      margin-right: 15px;
    }
  }
}

</style>