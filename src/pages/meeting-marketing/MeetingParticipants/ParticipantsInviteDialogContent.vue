<template>
  <div class="ParticipantsInviteDialogContent">
    <div class="Content__Block">
      <div class="Block__label">{{ invitePoster.qrPosterUrl ? '1' :''}}{{ $t('marketing.commons.bmnr_0037fa') }}</div>
      <div class="Content__detail">
        <div class="Content__cover">
          <ElImage 
            class="cover__img" 
            :src="conferenceDetail.coverImageThumbUrl"
            ref="$posterImg"
            fit="contain"
            :preview-src-list="[conferenceDetail.coverImageThumbUrl]"></ElImage>
        </div>
        <div class="detail__title">{{conferenceDetail.title}}</div>
      </div>
    </div>
    <div class="Content__Block"  v-if="invitePoster.qrPosterUrl && openInvitationPoster">
      <div class="Block__label">2.{{ $t('marketing.pages.meeting_marketing.bmhb_4e345a') }}</div>
      <div class="Content__cover">
        <ElImage 
          class="cover__img" 
          :src="invitePoster.qrPosterUrl"
          ref="$posterImg"
          fit="contain"
          :preview-src-list="[invitePoster.qrPosterThumbnailUrl]">
          <div slot="error" class="image-slot">
            <i class="el-icon-picture-outline"></i>
          </div>
          </ElImage>
      </div>
    </div>
  </div>
</template>

<script>

import { mapState, mapActions } from 'vuex';
export default {
  components: {
ElCheckbox: FxUI.Checkbox,
ElImage: FxUI.Image
},
  props: {
    selectedParticipants: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      model_filter: false,
    }
  },
  computed: {
    MeetingMarketing() {
      return this.$store.state.MeetingMarketing;
    },
    conferenceDetail() {
      return this.MeetingMarketing.conferenceDetail;
    },
    conferenceId() {
      return this.conferenceDetail.id;
    },
    invitePoster() {
      return this.MeetingMarketing.invitePoster;
    },
    icSetting() {
      return this.MeetingMarketing.invitationCommonSetting || {};
    },
    openInvitationPoster() {
      return this.icSetting.openInvitationPoster || false;
    },
  },
  methods: {
    ...mapActions([
      'getInvitationCommonSetting',
      'getInvitePoster',
    ]),
  },
  mounted() {
    this.getInvitePoster({ id: this.conferenceId });
    this.getInvitationCommonSetting({ id: this.conferenceId });
  },
  created(){
    console.log(`邀约设置------${JSON.stringify(this.MeetingMarketing.invitePoster)}`)
  }
}
</script>

<style lang="less" scoped>
.ParticipantsInviteDialogContent {
  display: flex;
  .Content__Block {
    margin-right: 30px;
    .Block__label {
      color: #181C25;
      font-size: 13px;
      margin-bottom: 10px;
    }
  }
  .Content__detail {
    display: flex;
    background: #fff;
    width: 300px;
    height: 60px;
    align-items: center;
    .detail__title {
      margin-left: 12px;
      color: #181C25;
      font-size: 13px;
    }
  }
  .Content__cover {
    width: 95px;
    height: 60px;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    .cover__img {
      width: 100%;
      height: 100%;
    }
  }
}

</style>