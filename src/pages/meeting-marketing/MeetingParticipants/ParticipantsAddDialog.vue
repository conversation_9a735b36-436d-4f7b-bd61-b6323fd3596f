<template>
  <VDialog
    :title="$t('marketing.commons.tjchry_a38838')"
    v-if="dialogVisible"
    :visible.sync="dialogVisible"
    width="760px"
    class="ParticipantsAddDialog"
    append-to-body
    :okText="$t('marketing.commons.tj_a0e8fd')"
    :cancelText="$t('marketing.commons.qx_c08ab9')"
    @onSubmit="handleAddParticipants"
    @onClose="handleCloseDialog"
    :loading="isSubmitLoading"
    :zIndex="1999"
  >
    <div class="ParticipantsAddDialog__body">
      <div class="body__tips">‍通过CRM系统，您可以将拟邀客户添加到参会人员列表中，然后在参会人员列表勾选客户后，在顶部操作栏下发邀约任务。最多可以选择300条客户数据进行添加。若选择的数据量较大，可能需要等待一段时间才能完成添加。</div>
      <ElForm
        class="body__form"
        ref="form"
        label-width="107px"
        label-position="left"
        :model="formData"
        :rules="formRules"
      >
        <ElFormItem class="form__item" :label="$t('marketing.commons.tjchry_a38838')" required>
          <ElSelect class="el-select" v-model="model_curCrmObjectApiname" size="small" width="143px">
            <ElOption 
              v-for="crmObject in data_crmObjectList" 
              :key="crmObject.apiname" 
              :value="crmObject.apiname" 
              :label="crmObject.name"></ElOption>
          </ElSelect>
          <ParticipantsAddDialogSelector
            class="item__selector"
            ref="ParticipantsAddDialogSelector"
            :crmObjectApiname="model_curCrmObjectApiname"
            @update:selector="handleMemberObjDetailsChange"
          ></ParticipantsAddDialogSelector>
        </ElFormItem>
        <ElFormItem :label="$t('marketing.pages.meeting_marketing.cyzt_887bb1')" prop="coverTaPath">
          <ElSelect class="el-select" v-model="model_curAttendingState" size="small">
            <ElOption 
              v-for="attendingState in data_attendingStateList" 
              :key="attendingState.value" 
              :value="attendingState.value" 
              :label="attendingState.label"></ElOption>
          </ElSelect>
        </ElFormItem>
      </ElForm>
    </div>
  </VDialog>
</template>

<script>

import VDialog from '@/components/dialog';
import { participantsCrmObjectList } from './config';
import ParticipantsAddDialogSelector from './ParticipantsAddDialogSelector';
export default {
  components: {
ElDialog: FxUI.Dialog,
VDialog,
ElForm: FxUI.Form,
ElFormItem: FxUI.FormItem,
ElSelect: FxUI.Select.components.ElSelect,
ElOption: FxUI.Select.components.ElSelect.components.ElOption,
ElInput: FxUI.Input,
ParticipantsAddDialogSelector
},
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    marketingEventId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      data_crmObjectList: participantsCrmObjectList,
      model_curCrmObjectApiname: 'AccountObj',
      data_attendingStateList: null,
      model_curAttendingState: null,
      model_memberObjDetails: [],
      isSubmitLoading: false,

      dialogVisible: false,
      formData: {},
      formRules: {},
    }
  },
  watch: {
    visible() {
      this.dialogVisible = this.visible;
    },
    dialogVisible() {
      this.dialogVisible === false && this.$emit('update:visible', false);
    },
  },
  computed: {
    curCrmObjectName() {
      return this.data_crmObjectList.filter(item => item.apiname === this.model_curCrmObjectApiname)[0].name;
    },
  },
  methods: {
    async getCampaignMembersObjField() {
      const res = await YXT_ALIAS.http.getCampaignMembersObjField();
      if (res && res.errCode === 0) {
        this.data_attendingStateList = res.data[0].options;
        this.model_curAttendingState = this.data_attendingStateList[0].value;
      }
    },
    handleMemberObjDetailsChange(res) {
      this.model_memberObjDetails = res.map(item => ({
        apiName: this.model_curCrmObjectApiname,
        id: item.id,
        name: item.name,
        companyName: item.companyName,
      }));
    },
    async handleAddParticipants() {
      if (!this.model_memberObjDetails.length) {
        FxUI.Message.warning($t('marketing.commons.qxzchry_f5b0b6'));
        return;
      }
      this.isSubmitLoading = true;
      const res = await YXT_ALIAS.http.addCampaignMembersObj({
        campaignMembersStatus: this.model_curAttendingState,
        marketingEventId: this.marketingEventId,
        memberObjDetails: this.model_memberObjDetails,
      });
      if (res && res.errCode === 0) {
        this.model_memberObjDetails = [];
        this.isSubmitLoading = true;
        setTimeout(() => {
          this.isSubmitLoading = false;
          // 1s后再成功，否则列表拉取数据最新那条会出不来
          this.$emit('update:participantsAdd');
        }, 1000);
      }
    },
    handleCloseDialog() {
      this.dialogVisible = false;
    },
    submit() {
      this.$refs.ParticipantsAddDialogSelector.clear();
    },
  },
  mounted() {
    this.getCampaignMembersObjField();
  },
}
</script>

<style lang="less" scoped>
.ParticipantsAddDialog {
  /deep/ .el-dialog__body {
    padding: 30px 37px 100px 26px;
  }
  .ParticipantsAddDialog__body {
    .body__tips {
      font-size: 13px;
      color: #545861;
      margin-bottom: 20px;
    }
    .body__form {
      .form__item {
        /deep/ .el-form-item__content {
          display: flex;
        }
        .item__selector {
          margin-left: 10px;
          flex: 1;
        }
      }
    }
  }
}

</style>