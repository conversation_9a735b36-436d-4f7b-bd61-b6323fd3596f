<template>
  <div :class="['MeetingSignin__custom', $style.MeetingSignin__custom]">
    <div :class="$style.header">
      <div :class="$style.titleWrapper">
        <span :class="$style.title">{{ $t('marketing.pages.meeting_marketing.szhyqdysz_1aa4ea') }}</span>
      </div>
      <div>
        <fx-button
          :class="$style.btn"
          type="primary"
          :loading="saveLoading"
          @click="handleSave"
        >
          {{ $t('marketing.commons.bc_be5fbb') }}
        </fx-button>
        <fx-button
          :class="$style.btn"
          type="primary"
          :loading="saveAndBackLoading"
          @click="handleSaveAndExit"
        >
          {{ $t('marketing.commons.bcbtc_8ed8b0') }}
        </fx-button>
        <fx-button
          :class="$style.btn"
          @click="handleCancel"
        >
          {{ $t('marketing.commons.qx_625fb2') }}
        </fx-button>
      </div>
    </div>
    <div
      v-loading="loading"
      :class="$style.main"
    >
      <fx-menu
        :default-active="menuActiveIndex"
        :class="$style.menu"
        @select="handleMenuSelect"
      >
        <fx-menu-item
          v-for="item in menuOptions"
          :key="item"
          :index="item.index"
          style="padding-left: 8px;"
        >
          <span slot="title">{{ item.title }}</span>
        </fx-menu-item>
      </fx-menu>
      <div :class="$style.rightContentWrapper">
        <div :class="$style.previewWrapper">
          <div :class="$style.preview">
            <div :class="$style.previewHeader">
              <span>{{ $t('marketing.commons.hyqd_ae2e6b') }}</span>
            </div>
            <div :class="$style.previewMain">
              <template v-if="menuActiveIndex === menuOptions[0].index">
                <div :class="$style.infoWrapper">
                  <fx-image
                    v-if="conferenceDetail.coverImageUrl"
                    :class="$style.cover"
                    :src="conferenceDetail.coverImageUrl"
                    fit="cover"
                  />
                  <div :class="$style.info">
                    <div :class="$style.title">
                      {{ conferenceDetail.title }}
                    </div>
                    <span>{{ $t('marketing.commons.sj_14e6d8') }}{{ conferenceDetail.startTimeCN || '--' }} {{ $t('marketing.commons.z_981cbe') }} {{ conferenceDetail.endTimeCN || '--' }}</span>
                    <span>{{ $t('marketing.commons.dz_df3833') }}{{ conferenceDetail.location || '--' }}</span>
                  </div>
                </div>
                <div :class="$style.contentWrapper">
                  <div :class="$style.title">
                    {{ $t('marketing.commons.hyqd_ae2e6b') }}
                  </div>
                  <div :class="$style.tips">
                    {{ $t('marketing.pages.meeting_marketing.qsrbmstxdw_8f35f1', { data: ({ option0: inputTips }) }) }}
                  </div>
                  <div :class="$style.verifyWrapper">
                    <div :class="$style.input">
                      {{ $t('marketing.commons.qsr_899f77', { data: ({ option0: inputTips }) }) }}
                    </div>
                    <div
                      v-if="formData.phoneSign && formData.miniAppComponent"
                      :class="$style.verify"
                    >
                      {{ $t('marketing.pages.meeting_marketing.sqsjh_a2ac7f') }}
                    </div>
                  </div>
                  <div :class="$style.btn">
                    {{ $t('marketing.commons.qd_c9da9b') }}
                  </div>
                  <div :class="$style.link">
                    {{ `${$t('marketing.pages.meeting_marketing.hmbmmsbm_4301a8')} >` }}
                  </div>
                </div>
              </template>
              <template v-else>
                <div :class="$style.titleWrapper">
                  <div :class="$style.title">
                    {{ conferenceDetail.title }}
                  </div>
                  <div :class="$style.link">
                    {{ `${$t('marketing.commons.ckhy_23288a')} >` }}
                  </div>
                </div>
                <div
                  :class="$style.resultWrapper"
                  :style="menuActiveIndex === menuOptions[1].index && customFields && customFields.length > 2 ? '' : 'min-height: 480px'"
                >
                  <fx-image
                    :src="menuActiveIndex === menuOptions[1].index ? successIcon : warningIcon"
                    :class="$style.statusIcon"
                    fit="cover"
                  />
                  <div :class="$style.status">
                    <span v-if="menuActiveIndex === menuOptions[1].index">{{ formData.firstSignSuccess }}</span>
                    <span v-if="menuActiveIndex === menuOptions[2].index">{{ $t('marketing.pages.meeting_marketing.nhwbm_90fa0c') }}</span>
                    <span v-if="menuActiveIndex === menuOptions[3].index">{{ formData.auditingTip }}</span>
                    <span v-if="menuActiveIndex === menuOptions[4].index">{{ formData.auditFailTip }}</span>
                  </div>
                  <div
                    v-if="menuActiveIndex === menuOptions[1].index"
                    :class="$style.time"
                  >
                    {{ $t('marketing.pages.meeting_marketing.hyqdsj_0f0759') }}2022-01-01 10:00
                  </div>
                  <div
                    v-if="menuActiveIndex === menuOptions[1].index && customFields && !!customFields.length"
                    :class="$style.customWrapper"
                  >
                    <div
                      v-for="item in customFields"
                      :key="item.key"
                      :class="$style.customItem"
                    >
                      {{ (item.value || '--') + '：' + `{ ${item.label} }` }}
                    </div>
                  </div>
                  <div
                    v-if="menuActiveIndex === menuOptions[1].index"
                    :class="$style.tips"
                  >
                    {{ formData.noticeSetting }}
                  </div>
                  <div
                    v-if="menuActiveIndex === menuOptions[3].index"
                    :class="$style.tips"
                  >
                    <div>{{ formData.auditingContent }}</div>
                  </div>
                  <div
                    v-if="menuActiveIndex === menuOptions[4].index"
                    :class="$style.tips"
                  >
                    <div>{{ formData.auditFailContent }}</div>
                  </div>
                  <div
                    v-if="menuActiveIndex === menuOptions[2].index"
                    :class="$style.btn"
                  >
                    {{ $t('marketing.pages.meeting_marketing.qwbm_fd6c3a') }}
                  </div>
                  <fx-image
                    v-else-if="qrCodeUrls[menuActiveIndex]"
                    :src="qrCodeUrls[menuActiveIndex]"
                    :class="$style.qrcode"
                    fit="cover"
                  />
                </div>
              </template>
            </div>
          </div>
        </div>
        <div :class="$style.formPanel">
          <fx-form ref="form">
            <template v-if="menuActiveIndex === menuOptions[0].index">
              <div :class="$style.item">
                <span>{{ $t('marketing.pages.meeting_marketing.qdfs_096e1c') }}</span>
                <div :class="$style.formGroup">
                  <div :class="$style.formItem">
                    <span style="width: 100px">{{ $t('marketing.pages.meeting_marketing.sjhqd_e64db5') }}</span>
                    <div :class="$style.formContent">
                      <div :class="$style.formRow">
                        <fx-switch
                          v-model="formData.phoneSign"
                          @change="(val)=>handleValidPhoneAndEmail(val,'phone')"
                          size="small"
                        />
                        <fx-checkbox
                          v-if="formData.phoneSign"
                          v-model="formData.miniAppComponent"
                          style="margin-left: 10px;"
                        >
                          {{ $t('marketing.pages.meeting_marketing.syxcxksyzz_31fafb') }}
                        </fx-checkbox>
                      </div>
                      <div
                        v-if="formData.phoneSign"
                        :class="$style.miniappTips"
                      >
                        <span>{{ isCustomMiniappOpen ? $t('marketing.pages.meeting_marketing.znyrqtxdyz_836f16') : $t('marketing.pages.meeting_marketing.bdzsxcxqzx_78aaac') }}</span>
                        <fx-link
                          v-if="!isCustomMiniappOpen"
                          type="standard"
                          style="font-size: 12px"
                          href="https://www.fxiaoke.com/XV/UI/Home#/app/marketing/index/=/miniapp-setting"
                          target="_blank"
                        >
                          {{ $t('marketing.pages.meeting_marketing.ktzsxcx_d74e60') }}
                        </fx-link>
                        <fx-link
                          type="standard"
                          style="font-size: 12px"
                          href="https://developers.weixin.qq.com/miniprogram/dev/platform-capabilities/paymentManage.html"
                          target="_blank"
                        >
                          {{ $t('marketing.pages.meeting_marketing.gmyzzj_6d8844') }}
                        </fx-link>
                      </div>
                    </div>
                  </div>
                  <div :class="$style.formItem">
                    <span style="width: 100px">{{ $t('marketing.pages.meeting_marketing.yxqd_9d9a4c') }}</span>
                    <div :class="$style.formContent">
                      <fx-switch
                        v-model="formData.emailSign"
                        @change="(val)=>handleValidPhoneAndEmail(val,'email')"
                        size="small"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </template>
            <template v-if="menuActiveIndex === menuOptions[1].index">
              <div :class="$style.item">
                <span>{{ $t('marketing.pages.meeting_marketing.qdcgts_00dd5d') }}</span>
                <div :class="$style.formGroup">
                  <div :class="$style.desc">
                    {{ $t('marketing.pages.meeting_marketing.szscjzcqdt_034808') }}
                  </div>
                  <div :class="$style.colFormItem">
                    <div :class="$style.label">
                      {{ $t('marketing.pages.meeting_marketing.scqdcgts_27189b') }}
                    </div>
                    <fx-input
                      v-model="formData.firstSignSuccess"
                      :class="$style.tipsInput"
                      type="text"
                      :placeholder="$t('marketing.commons.qsrnr_a11cc7')"
                      maxlength="15"
                      show-word-limit
                    />
                  </div>
                  <div :class="$style.colFormItem">
                    <div :class="$style.label">
                      {{ $t('marketing.pages.meeting_marketing.zcqdcgts_58911c') }}
                    </div>
                    <fx-input
                      v-model="formData.secondSignSuccess"
                      :class="$style.tipsInput"
                      type="text"
                      :placeholder="$t('marketing.commons.qsrnr_a11cc7')"
                      maxlength="15"
                      show-word-limit
                    />
                  </div>
                  <div :class="$style.colFormItem">
                    <div :class="$style.label">
                      {{ $t('marketing.pages.meeting_marketing.tsy_846300') }}
                    </div>
                    <fx-input
                      v-model="formData.noticeSetting"
                      :class="$style.tipsInput"
                      :placeholder="$t('marketing.pages.meeting_marketing.szgxhtsyrq_6ddafa')"
                      :rows="3"
                      type="textarea"
                      maxlength="50"
                      show-word-limit
                    />
                  </div>
                </div>
              </div>
              <div :class="$style.item">
                <span>{{ $t('marketing.pages.meeting_marketing.zdyzszd_fb390f') }}</span>
                <div :class="$style.formGroup">
                  <div :class="$style.desc">
                    <p>{{ $t('marketing.pages.meeting_marketing.zczqdcgyxs_c60d45') }}</p>
                    <p>
                      {{ $t('marketing.pages.meeting_marketing.scsyqlxgly_fcf2ae') }}
                    </p>
                  </div>
                  <div
                    v-for="(field, index) in customFields"
                    :key="field.key"
                  >
                    <div :class="$style.customItem2">
                      <el-select
                        v-model="field.key"
                        :class="$style.customSelect"
                        size="small"
                        width="240px"
                        :placeholder="$t('marketing.pages.meeting_marketing.qxzchryzdy_80c719')"
                      >
                        <el-option
                          v-for="(value, key) in customOptions"
                          :key="key"
                          :label="value"
                          :value="key"
                          :disabled="customFields.map((f) => f.key).includes(key)"
                          @click.native="customOptionSelected(index, key, value)"
                        />
                      </el-select>
                      <fx-input
                        v-model="field.value"
                        :class="$style.customInput2"
                        size="small"
                        type="text"
                        width="380px"
                        :placeholder="$t('marketing.pages.meeting_marketing.qsrxsxgms_15029e')"
                        maxlength="15"
                        show-word-limit
                      />
                      <fx-button
                        type="text"
                        :class="$style.customDel"
                        style="padding: 0 8px;margin-bottom:16px;"
                        @click="deleteField(index)"
                      >
                        {{ $t('marketing.commons.sc_2f4aad') }}
                      </fx-button>
                    </div>
                    <div
                      v-if="!isValidateForm && (!field.key || !field.value)"
                      :class="$style.customInputTips2"
                    >
                      {{ $t('marketing.pages.meeting_marketing.qtxxgpzxx_053764') }}
                    </div>
                  </div>
                  <div
                    v-if="showAddBtn"
                    :class="$style.addBtn"
                    @click="addCustomField"
                  >
                    <div><i class="iconfont iconjiahao" /></div>
                    <div :class="$style.text">
                      {{ $t('marketing.pages.meeting_marketing.xzhdcyzdyz_d1723d') }}
                    </div>
                  </div>
                </div>
              </div>
              <div :class="$style.item">
                <span>{{ $t('marketing.pages.meeting_marketing.ylewm_1b41b6') }}</span>
                <div :class="$style.formItem">
                  <fx-select
                    v-model="formData.signSuccessQrcodeType"
                    :options="qrcodeMaps"
                    size="small"
                    filterable
                    @change="handleQrcodeTypeChange"
                  />
                  <fx-input
                    :value="formData.signSuccessQrcodeName"
                    width="400px"
                    style="margin-left: 8px;"
                    :placeholder=" formData.signSuccessQrcodeType === 1 ? $t('marketing.commons.qxzgzhqdew_b6c503') : $t('marketing.pages.meeting_marketing.qxzqwhm_f3c7d6')"
                    size="small"
                    suffix-icon="fx-icon-search"
                    @click.native="handleQrcodeSelect(formData.signSuccessQrcodeType)"
                  />
                </div>
              </div>
            </template>
            <template v-if="menuActiveIndex === menuOptions[2].index">
              <div :class="$style.item">
                <span>{{ $t('marketing.pages.meeting_marketing.xzzdxcbmnr_c51662') }}</span>
                <div :class="$style.formGroup">
                  <div :class="$style.desc">
                    {{ $t('marketing.pages.meeting_marketing.yhqdwbmsdj_c953bb') }}
                  </div>
                  <div :class="$style.formItem">
                    <fx-select
                      v-model="enrollTargetType"
                      :options="targetMaps"
                      size="small"
                      filterable
                    />
                    <fx-input
                      v-if="enrollTargetType === 'material'"
                      :value="enrollMaterial.title"
                      width="400px"
                      style="margin-left: 8px;"
                      :placeholder="$t('marketing.commons.qxz_708c9d')"
                      size="small"
                      suffix-icon="fx-icon-search"
                      @click.native="selectMaterialDialog = true"
                    />
                    <fx-input
                      v-else
                      v-model="formData.jumpUrl"
                      :placeholder="$t('marketing.commons.qsrzddbmwy_222469')"
                      width="400px"
                      style="margin-left: 8px;"
                      size="small"
                      type="input"
                    />
                  </div>
                </div>
              </div>
            </template>
            <template v-if="menuActiveIndex === menuOptions[3].index">
              <div :class="$style.item">
                <span>{{ $t('marketing.pages.meeting_marketing.qdts_ae38bf') }}</span>
                <div :class="$style.formGroup">
                  <div :class="$style.desc">
                    {{ $t('marketing.pages.meeting_marketing.szqdtsdway_68321a') }}
                  </div>
                  <div :class="$style.colFormItem">
                    <div :class="$style.label">
                      {{ $t('marketing.pages.meeting_marketing.qdsbts_6b2724') }}
                    </div>
                    <fx-input
                      v-model="formData.auditingTip"
                      :class="$style.tipsInput"
                      type="text"
                      :placeholder="$t('marketing.pages.meeting_marketing.qdsbsbts_f94d5e')"
                      maxlength="15"
                      show-word-limit
                    />
                  </div>
                  <div :class="$style.colFormItem">
                    <div :class="$style.label">
                      {{ $t('marketing.pages.meeting_marketing.wams_32fdb2') }}
                    </div>
                    <fx-input
                      v-model="formData.auditingContent"
                      :class="$style.tipsInput"
                      :rows="3"
                      type="textarea"
                      maxlength="50"
                      show-word-limit
                    />
                  </div>
                </div>
              </div>
              <div :class="$style.item">
                <span>{{ $t('marketing.pages.meeting_marketing.ylewm_1b41b6') }}</span>
                <div :class="$style.formItem">
                  <fx-select
                    v-model="formData.auditingQrcodeType"
                    :options="qrcodeMaps"
                    size="small"
                    filterable
                    @change="handleQrcodeTypeChange"
                  />
                  <fx-input
                    :value="formData.auditingQrcodeName"
                    width="400px"
                    style="margin-left: 8px;"
                    :placeholder=" formData.auditingQrcodeType === 1 ? $t('marketing.commons.qxzgzhqdew_b6c503') : $t('marketing.pages.meeting_marketing.qxzqwhm_f3c7d6')"
                    size="small"
                    suffix-icon="fx-icon-search"
                    @click.native="handleQrcodeSelect(formData.auditingQrcodeType)"
                  />
                </div>
              </div>
            </template>
            <template v-if="menuActiveIndex === menuOptions[4].index">
              <div :class="$style.item">
                <span>{{ $t('marketing.pages.meeting_marketing.qdts_ae38bf') }}</span>
                <div :class="$style.formGroup">
                  <div :class="$style.desc">
                    {{ $t('marketing.pages.meeting_marketing.szqdtsdway_68321a') }}
                  </div>
                  <div :class="$style.colFormItem">
                    <div :class="$style.label">
                      {{ $t('marketing.pages.meeting_marketing.qdsbts_6b2724') }}
                    </div>
                    <fx-input
                      v-model="formData.auditFailTip"
                      :class="$style.tipsInput"
                      type="text"
                      :placeholder="$t('marketing.pages.meeting_marketing.qdsbsbts_f94d5e')"
                      maxlength="15"
                      show-word-limit
                    />
                  </div>
                  <div :class="$style.colFormItem">
                    <div :class="$style.label">
                      {{ $t('marketing.pages.meeting_marketing.wams_32fdb2') }}
                    </div>
                    <fx-input
                      v-model="formData.auditFailContent"
                      :class="$style.tipsInput"
                      :rows="3"
                      type="textarea"
                      maxlength="50"
                      show-word-limit
                    />
                  </div>
                </div>
              </div>
              <div :class="$style.item">
                <span>{{ $t('marketing.pages.meeting_marketing.ylewm_1b41b6') }}</span>
                <div :class="$style.formItem">
                  <fx-select
                    v-model="formData.auditFailQrcodeType"
                    :options="qrcodeMaps"
                    size="small"
                    filterable
                    @change="handleQrcodeTypeChange"
                  />
                  <fx-input
                    :value="formData.auditFailQrcodeName"
                    width="400px"
                    style="margin-left: 8px;"
                    :placeholder=" formData.auditFailQrcodeType === 1 ? $t('marketing.commons.qxzgzhqdew_b6c503') : $t('marketing.pages.meeting_marketing.qxzqwhm_f3c7d6')"
                    size="small"
                    suffix-icon="fx-icon-search"
                    @click.native="handleQrcodeSelect(formData.auditFailQrcodeType)"
                  />
                </div>
              </div>
            </template>
          </fx-form>
        </div>
      </div>
    </div>
    <SelectMaterialDialog
      v-if="selectMaterialDialog"
      :menus="['marketingEvent']"
      :marketing-event-tabbar="[10, 1, 4, 3, 16]"
      :visible="selectMaterialDialog"
      :marketing-event-id="marketingEventId"
      @onSubmit="handleMaterialSelected"
      @onClose="selectMaterialDialog = false"
    />
    <QrcodeDialog
      v-if="showQrcodeDialogVisible"
      :marketing-event-id="marketingEventId"
      :visible.sync="showQrcodeDialogVisible"
      @confirm="handleQrcodeConfirm"
      @close="showQrcodeDialogVisible = false"
    />
    <WxWorkQrcodeDialog
      v-if="wxWorkQrcodeVisible"
      :visible.sync="wxWorkQrcodeVisible"
      :marketing-event-id="marketingEventId"
      @confirm="handleWxWorkConfirm"
      @close="wxWorkQrcodeVisible = false"
    />
  </div>
</template>

<script>

import http from '@/services/http/index.js'

import QrcodeDialog from '@/pages/wechat/components/qrcode-dialog.vue'
import WxWorkQrcodeDialog from '@/pages/qywx-manage/promote-qrcode/qrcode-dialog.vue'

import successIcon from '@/assets/images/sign-success.png'
import warningIcon from '@/assets/images/sign-warning.png'

import qrcode from '@/assets/images/openTipsQrcode-old.png'

import SelectMaterialDialog, { MaterialInstance } from '@/components/select-material-dialog/index.vue'

const enrollTargetMaps = [{ label: $t('marketing.commons.tgnr_a6ec90'), value: 'material' }, { label: $t('marketing.commons.wydz_66b985'), value: 'url' }]

const qrcodeMaps = [{ label: $t('marketing.commons.ylgzh_97a07f'), value: 1 }, { label: $t('marketing.pages.meeting_marketing.ylqw_25c9d5'), value: 2 }]

export default {
  components: {
    ElSelect: FxUI.Select.components.ElSelect,
    ElOption: FxUI.Select.components.ElSelect.components.ElOption,
    SelectMaterialDialog,
    QrcodeDialog,
    WxWorkQrcodeDialog,
  },
  data() {
    return {
      loading: true,
      saveLoading: false,
      saveAndBackLoading: false,
      qrCodeUrls: {},
      showQrcodeDialogVisible: false,
      wxWorkQrcodeVisible: false,
      customFieldSettings: [],
      qrcodeMaps,
      enrollTargetType: enrollTargetMaps[0].value,
      enrollMaterial: {},
      selectMaterialDialog: false,
      targetMaps: enrollTargetMaps,
      successIcon,
      warningIcon,
      qrcode,
      menuActiveIndex: 'sign-menu__1',
      menuOptions: [
        {
          title: $t('marketing.pages.meeting_marketing.qdyz_8b35e0'),
          index: 'sign-menu__1',
        },
        {
          title: $t('marketing.commons.qdcg_fd44c8'),
          index: 'sign-menu__2',
        },
        {
          title: $t('marketing.pages.meeting_marketing.qdsbwbm_181523'),
          index: 'sign-menu__3',
        },
        {
          title: $t('marketing.pages.meeting_marketing.qdsbshz_c4d122'),
          index: 'sign-menu__4',
        },
        {
          title: $t('marketing.pages.meeting_marketing.qdsbshwtg_403a71'),
          index: 'sign-menu__5',
        },
      ],
      customOptions: {},
      customFields: [],
      conferenceId: this.$route.params.id,
      formData: {
        firstSignSuccess: '',
        secondSignSuccess: '',
        isShowNotice: true,
        noticeSetting: '',
        customFieldSettings: [],
        isValidateForm: true,
        auditFailWxAppId: '',
        auditFailContent: '',
        auditFailQrcodeId: '',
        auditFailQrcodeType: 1,
        auditFailQrcodeName: '',
        auditFailTip: '',
        auditingWxAppId: '',
        auditingContent: '',
        auditingQrcodeId: '',
        auditingQrcodeType: 1,
        auditingQrcodeName: '',
        auditingTip: '',
        signSuccessWxAppId: '',
        signSuccessQrcodeId: '',
        signSuccessQrcodeName: '',
        signSuccessQrcodeType: 1,
        signSuccessQrcodeUrl: '',
        emailSign: false,
        jumpObjectId: '',
        jumpObjectType: null,
        jumpUrl: '',
        miniAppComponent: false,
        phoneSign: true,
      },
    }
  },
  computed: {
    conferenceDetail() {
      return this.$store.state.MeetingMarketing.conferenceDetail
    },
    marketingEventId() {
      return (this.conferenceDetail || {}).marketingEventId
    },
    customData() {
      const { customOptions, customFieldSettings } = this
      return { customOptions, customFieldSettings }
    },
    showAddBtn() {
      return this.customFields.length < Object.keys(this.customOptions).length
    },
    inputTips() {
      return this.formData.phoneSign && this.formData.emailSign ? $t('marketing.commons.sjhhyx_4df43e') : this.formData.phoneSign ? $t('marketing.commons.sjh_8098e2') : this.formData.emailSign ? $t('marketing.commons.yx_3bc5e6') : ''
    },
    isCustomMiniappOpen() {
      return this.$store.state.MiniappInfo && this.$store.state.MiniappInfo.isCustomMiniappOpen
    },
  },
  watch: {
    customData: {
      handler(val) {
        const { customOptions, customFieldSettings } = val
        this.customFields = customFieldSettings.map(item => {
          const _key = Object.keys(item)[0]
          return {
            key: _key,
            label: customOptions[_key],
            value: item[_key],
          }
        })
      },
      deep: true,
    },
    // 'formData.auditFailQrcodeType': {
    //   handler(val) {
    //     this.formData.auditFailQrcodeId = ''
    //     this.formData.auditFailQrcodeName = ''
    //     this.formData.auditFailQrcodeUrl = ''
    //     this.formData.auditFailWxAppId = ''
    //   },
    // },
    // 'formData.auditingQrcodeType': {
    //   handler(val) {
    //     this.formData.auditingQrcodeId = ''
    //     this.formData.auditingQrcodeName = ''
    //     this.formData.auditingQrcodeUrl = ''
    //     this.formData.auditingWxAppId = ''
    //   },
    // },
    // 'formData.signSuccessQrcodeType': {
    //   handler(val) {
    //     this.formData.signSuccessQrcodeId = ''
    //     this.formData.signSuccessQrcodeName = ''
    //     this.formData.signSuccessQrcodeUrl = ''
    //     this.formData.signSuccessWxAppId = ''
    //   },
    // },
  },
  created() {
    this.$store.dispatch('queryConferenceDetail', {
      conferenceId: this.conferenceId,
    })
  },
  mounted() {
    this.getSignInSuccessSetting()
    this.getObjectCustomFields()
  },
  methods: {
    handleQrcodeTypeChange() {
      if (this.menuActiveIndex === this.menuOptions[1].index) {
        this.formData.signSuccessQrcodeId = ''
        this.formData.signSuccessQrcodeName = ''
        this.formData.signSuccessQrcodeUrl = ''
        this.formData.signSuccessWxAppId = ''
      } else if (this.menuActiveIndex === this.menuOptions[3].index) {
        this.formData.auditingQrcodeId = ''
        this.formData.auditingQrcodeName = ''
        this.formData.auditingQrcodeUrl = ''
        this.formData.auditingWxAppId = ''
      } else if (this.menuActiveIndex === this.menuOptions[4].index) {
        this.formData.auditFailQrcodeId = ''
        this.formData.auditFailQrcodeName = ''
        this.formData.auditFailQrcodeUrl = ''
        this.formData.auditFailWxAppId = ''
      }
    },
    handleQrcodeSelect(qrcodeType) {
      if (qrcodeType === 1) {
        this.showQrcodeDialogVisible = true
      } else if (qrcodeType === 2) {
        this.wxWorkQrcodeVisible = true
      }
    },
    handleQrcodeConfirm(data) {
      this.updateQrcodeData({
        id: data.id, name: data.qrCodeName, url: data.url, wxAppId: data.gzhAppId,
      })
    },
    handleWxWorkConfirm(data) {
      this.updateQrcodeData({ id: data.id, name: data.qrCodeName, url: data.url })
    },
    updateQrcodeData(data) {
      if (this.menuActiveIndex === this.menuOptions[1].index) {
        this.formData.signSuccessQrcodeId = data.id
        this.formData.signSuccessQrcodeName = data.name
        this.formData.signSuccessQrcodeUrl = data.url
        this.formData.signSuccessWxAppId = data.wxAppId
      } else if (this.menuActiveIndex === this.menuOptions[3].index) {
        this.formData.auditingQrcodeId = data.id
        this.formData.auditingQrcodeName = data.name
        this.formData.auditingQrcodeUrl = data.url
        this.formData.auditingWxAppId = data.wxAppId
      } else if (this.menuActiveIndex === this.menuOptions[4].index) {
        this.formData.auditFailQrcodeId = data.id
        this.formData.auditFailQrcodeName = data.name
        this.formData.auditFailQrcodeUrl = data.url
        this.formData.auditFailWxAppId = data.wxAppId
      }
      this.qrCodeUrls[this.menuActiveIndex] = data.url
    },
    async getObjectDetail(objectType, objectId) {
      const res = await MaterialInstance.getMaterialDetailByObjectType(objectType, objectId)
      if (!res || res.errCode !== 0) return
      const data = MaterialInstance.formatDataByObjectType(objectType, res.data)
      this.enrollMaterial = data
      console.log('this.enrollMaterial: ', this.enrollMaterial)
    },
    handleMaterialSelected(row) {
      this.enrollMaterial = row
    },
    handleMenuSelect(index) {
      this.menuActiveIndex = index
    },
    addCustomField() {
      this.customFields.push({
        key: '',
        label: '',
        value: '',
      })
    },
    deleteField(idx) {
      this.customFields.splice(idx, 1)
    },
    customOptionSelected(index, key, value) {
      this.$set(this.customFields, index, {
        ...this.customFields[index], key, label: value, value,
      })
    },
    handleSave() {
      this.updateSignInSuccessSetting()
    },
    handleSaveAndExit() {
      this.updateSignInSuccessSetting(true)
    },
    handleCancel() {
      this.goBack()
    },
    getSignInSuccessSetting() {
      http
        .getSignInSuccessSetting({
          conferenceId: this.conferenceId,
        })
        .then(({ errCode, data }) => {
          this.loading = false
          if (errCode === 0) {
            this.formData = {
              firstSignSuccess: data.firstSignSuccess || $t('marketing.commons.qdcg_fd44c8'),
              secondSignSuccess: data.secondSignSuccess || $t('marketing.pages.meeting_marketing.nywcqdwxzc_f7f418'),
              isShowNotice: data.isShowNotice || true,
              noticeSetting: data.noticeSetting || '',
              auditFailContent: data.auditFailContent || $t('marketing.pages.meeting_marketing.ndbmxxshwt_77459c'),
              auditFailQrcodeId: data.auditFailQrcodeId || '',
              auditFailQrcodeType: data.auditFailQrcodeType || 1,
              auditFailQrcodeName: data.auditFailQrcodeName || '',
              auditFailWxAppId: data.auditFailWxAppId || '',
              auditFailQrcodeUrl: data.auditFailQrcodeUrl || '',
              auditFailTip: data.auditFailTip || $t('marketing.pages.meeting_marketing.qdsb_6c577e'),
              auditingContent: data.auditingContent || $t('marketing.pages.meeting_marketing.ndbmxxshzq_32d4ef'),
              auditingQrcodeId: data.auditingQrcodeId || '',
              auditingQrcodeType: data.auditingQrcodeType || 1,
              auditingQrcodeName: data.auditingQrcodeName || '',
              auditingWxAppId: data.auditingWxAppId || '',
              auditingQrcodeUrl: data.auditingQrcodeUrl || '',
              auditingTip: data.auditingTip || $t('marketing.pages.meeting_marketing.qdsb_6c577e'),
              signSuccessWxAppId: data.signSuccessWxAppId || '',
              signSuccessQrcodeId: data.signSuccessQrcodeId || '',
              signSuccessQrcodeName: data.signSuccessQrcodeName || '',
              signSuccessQrcodeType: data.signSuccessQrcodeType || 1,
              signSuccessQrcodeUrl: data.signSuccessQrcodeUrl || '',
              miniAppComponent: !!data.miniAppComponent,
              emailSign: !!data.emailSign,
              phoneSign: !!data.phoneSign,
            }
            this.customFieldSettings = JSON.parse(data.customFieldSettings) || []
            this.qrCodeUrls = {
              'sign-menu__2': data.signSuccessQrcodeUrl || '',
              'sign-menu__4': data.auditingQrcodeUrl || '',
              'sign-menu__5': data.auditFailQrcodeUrl || '',
            }
            if (data.jumpObjectId && data.jumpObjectType) {
              this.getObjectDetail(data.jumpObjectType, data.jumpObjectId)
            } else {
              this.enrollTargetType = 'url'
              this.formData.jumpUrl = data.jumpUrl
            }
          }
        })
    },
    getObjectCustomFields() {
      http.getObjectCustomFields().then(({ errCode, data }) => {
        if (errCode === 0) {
          this.customOptions = data
        }
      })
    },
    updateSignInSuccessSetting(isBack = false) {
      if (isBack) {
        this.saveAndBackLoading = true
      } else {
        this.saveLoading = true
      }
      this.isValidateForm = this.customFields.every(item => (
        !!item.key && !!item.value
      ))
      if (!this.isValidateForm) return
      const _customFieldSettings = this.customFields.map(item => ({ [item.key]: item.value }))
      http
        .updateSignInSuccessSetting({
          conferenceId: this.conferenceId,
          ...this.formData,
          jumpObjectId: this.enrollMaterial.id,
          jumpObjectType: this.enrollMaterial.objectType,
          customFieldSettings: JSON.stringify(_customFieldSettings),
        })
        .then(({ errCode }) => {
          this.saveLoading = false
          this.saveAndBackLoading = false
          if (errCode === 0) {
            this.getSignInSuccessSetting()
            FxUI.Message.success($t('marketing.commons.bccg_3b1083'))
          }
          if (isBack) {
            this.handleCancel()
          }
        })
    },
    handleValidPhoneAndEmail(value, type) {
      // 至少需要保留一直签到方式
      const isLastSignMethod = type === 'phone' 
        ? !value && !this.formData.emailSign
        : !value && !this.formData.phoneSign;

      if (isLastSignMethod) {
        this.formData[`${type}Sign`] = true;
        FxUI.Message.warning($t('marketing.pages.meeting_marketing.bxblyzqdfs_503253'));
      }
    },
    goBack() {
      this.$router.push({
        name: 'meeting-setting',
        query: { defaultActiveMenu: 'MeetingSignInSetting' }
      });
    }
  },
}
</script>

<style lang="scss">
.MeetingSignin__custom {
  .el-form-item {
    .el-form-item__content {
      margin-left: 0 !important;
    }
  }
}
</style>

<style lang="less" module>
.MeetingSignin__custom {
  position: fixed;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  height: 100vh;
  width: 100vw;
  background-color: var(--color-neutrals03);

  .header {
    box-sizing: border-box;
    height: 48px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    z-index: 10;
    padding: 0 12px;

    .titleWrapper {
      display: flex;
      align-items: center;
    }

    .title {
      color: var(--color-neutrals19);
      font-size: 18px;
      font-weight: 500;
      line-height: 28px;
    }

    .btn {
      font-size: 14px;
      line-height: 18px;
      padding: 6px 16px;
    }
  }

  .main {
    flex: 1;
    display: flex;
    padding: 0 12px 12px;

    .menu {
      padding: 12px;
      border-radius: 8px;

      :global {
        .el-menu-item {
          min-width: 176px;
          height: 34px;
          line-height: 34px;
          padding: 0 8px;
          border-radius: 8px;
          margin-bottom: 8px;
          font-size: 13px;
          font-weight: 400;
          color: var(--color-neutrals19);
        }
        .el-menu-item.is-active {
          font-weight: 700;
          background-color: var(--color-primary01);
        }
        .el-menu-item:hover {
          background-color: var(--color-neutrals03);
        }
      }
    }

    .rightContentWrapper {
      flex: 1;
      display: flex;
      background-color: #fff;
      border-radius: 8px;
      margin-left: 12px;
    }
    

    .previewWrapper {
      width: 435px;
      display: flex;
      justify-content: center;
      box-sizing: border-box;
      padding: 30px 0;
      border-right: 1px solid #DEE1E8;

      .preview {
        width: 375px;
        height: 779px;
        font-size: 18px;
        line-height: 28px;
        color: #181c25;
        background: #fff;
        box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.15);
      }

      .previewHeader {
        width: 100%;
        height: 89px;
        position: relative;
        background: url('@/assets/images/miniapp-header.jpg') center/contain no-repeat;

        span {
          position: absolute;
          top: 52px;
          left: 52px;
        }
      }

      .previewMain {
        display: flex;
        flex-direction: column;
        align-items: center;
        height: 670px;
        font-size: 14px;
        line-height: 18px;
        color: #91959e;
        position: relative;
        overflow-y: scroll;
        padding: 20px 0;
        background-image: url('@/assets/images/conference-bg.png');
        background-repeat: no-repeat;
        background-size: cover;

        .infoWrapper {
          display: flex;
          flex-direction: column;
          width: 335px;
          background: #fff;
          border-radius: 8px;
        }

        .cover {
          width: 100%;
          height: 200px;
          display: block;
          border-radius: 8px 8px 0 0;
        }

        .info {
          display: flex;
          flex-direction: column;
          padding: 12px;
          font-size: 14px;
          line-height: 20px;
          color: #545861;

          .title {
            font-size: 16px;
            line-height: 24px;
            font-weight: 700;
            color: #181c25;
          }

          div + span {
            margin-top: 7px;
          }

          span + span {
            margin-top: 2px;
          }
        }

        .contentWrapper {
          display: flex;
          flex-direction: column;
          align-items: center;
          width: 335px;
          box-sizing: border-box;
          margin-top: 10px;
          padding: 20px 12px;
          border-radius: 8px;
          background: rgba(255, 255, 255, 0.60);
          color: #181C25;
          font-size: 14px;
          line-height: 20px;

          .title {
            text-align: center;
            font-size: 16px;
            font-weight: 700;
            line-height: 24px;
          }

          .tips {
            margin-top: 30px;
          }

          .verifyWrapper {
            margin-top: 10px;
            width: 100%;
            display: flex;
            align-items: center;
          }

          .input {
            display: flex;
            flex: 1;
            box-sizing: border-box;
            padding: 6px 8px;
            border-radius: 4px;
            border: 1px solid #C1C5CE;
            background: #FFF;
            color: #C1C5CE;
          }

          .verify {
            display: flex;
            justify-content: center;
            margin-left: 10px;
            width: 89px;
            box-sizing: border-box;
            padding: 6px 0;
            border-radius: 4px;
            background: #FFF;
            border: 1px solid #C1C5CE;
          }

          .btn {
            margin-top: 10px;
            display: flex;
            justify-content: center;
            width: 100%;
            box-sizing: border-box;
            padding: 6px 0;
            border-radius: 4px;
            background: #0C6CFF;
            color: #fff;
          }

          .link {
            margin-top: 30px;
            color: #0C6CFF;
          }
        }

        .titleWrapper {
          width: 335px;
          box-sizing: border-box;
          padding: 12px;
          border-radius: 8px;
          opacity: 0.8;
          background: #FFF;

          .title {
            color: #181C25;
            font-size: 16px;
            font-weight: 700;
            line-height: 24px;
          }

          .link {
            font-size: 14px;
            line-height: 20px;
            margin-top: 7px;
            color: #0C6CFF;
          }
        }

        .resultWrapper {
          margin-top: 10px;
          display: flex;
          width: 335px;
          padding: 20px;
          box-sizing: border-box;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          border-radius: 8px;
          background: rgba(255, 255, 255, 0.40);
          color: #181C25;
          font-size: 14px;
          line-height: 20px;

          .statusIcon {
            width: 48px;
            height: 48px;
          }

          .status {
            margin-top: 4px;
            display: flex;
            align-items: center;
            height: 28px;
            font-size: 18px;
            line-height: 28px;
            font-weight: 700;
          }

          .time {
            margin-top: 8px;
            color: #91959E;
          }

          .customWrapper {
            margin-top: 20px;
            box-sizing: border-box;
            width: 310px;
            border-radius: 4px;
            border: 1px solid #DEE1E8;
            background: #FFF;
            display: flex;
            flex-direction: column;
            padding: 6px 8px;
            font-size: 14px;
            line-height: 22px;
            background: #fff;
          }

          .tips {
            text-align: center;
            margin-top: 20px;
          }

          .qrcode {
            margin-top: 20px;
            width: 208px;
            height: 208px;
          }

          .btn {
            margin-top: 20px;
            display: flex;
            justify-content: center;
            width: 232px;
            box-sizing: border-box;
            padding: 6px 0;
            border-radius: 4px;
            background: #0C6CFF;
            color: #fff;
            font-size: 16px;
            line-height: 24px;
          }
        }
      }
    }

    .formPanel {
      flex: 1;
      padding: 20px;
      font-size: 14px;
      line-height: 20px;
      color: #181c25;

      .formItem {
        display: flex;
        align-items: flex-start;
      }

      .formItem  + .formItem  {
        margin-top: 20px;
      }

      .colFormItem {
        width: 600px;
      }

      .formRow {
        display: flex;
        align-items: center;
      }

      .miniappTips {
        margin-top: 10px;
        max-width: 530px;
        border-radius: 4px;
        font-weight: 400;
        font-size: 12px;
        line-height: 18px;
      }

      .item {
        display: flex;
        margin-top: 26px;
        & > span {
          width: 156px;
        }

        .formGroup {
          flex: 1;

          .desc {
            margin-bottom: 20px;
            color: #91959e;
          }

          .label {
            margin-bottom: 4px;
          }

          .tipsInput {
            width: 600px;
          }
        }

        .customItem2 {
          display: flex;
          align-items: center;
        }

        .customSelect {
          margin-bottom: 16px;
        }

        .customInput2 {
          display: flex;
          margin-left: 10px;
        }

        .customInputTips2 {
          line-height: 18px;
          font-size: 12px;
          color: red;
          width: 120px;
        }

        .customDel {
          margin-left: 16px;
          color: var(--color-primary06,#407FFF);
        }

        .addBtn {
          display: flex;
          align-items: center;
          color: var(--color-primary06,#407FFF);
          width: 240px;
          cursor: pointer;

          i {
            font-size: 14px !important;
          }
        }

        .tipsArea2 {
          width: 634px;
        }
      }
    }
  }
}
</style>
