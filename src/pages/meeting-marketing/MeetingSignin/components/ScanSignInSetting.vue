<template>
  <div :class="$style.ScanSignInSetting">
    <div :class="$style.Setting__info">
      1.{{ $t('marketing.pages.meeting_marketing.qdewmxzdyz_7d06c6') }}
      <a :class="$style.Setting__alink" @click="isShowDownloadDialog = true">{{ $t('marketing.pages.meeting_marketing.xzqdewm_74837f') }}</a>
    </div>
    <!-- <div :class="$style.Setting__info">
      2.{{ $t('marketing.pages.meeting_marketing.zdxcbmnrym_fa4a32') }}<span v-loading="loading">{{data_targetText}}</span>
      <a :class="$style.Setting__alink" @click="isShowSignupDialog = true">{{ $t('marketing.commons.xg_8347a9') }}</a>
    </div> -->
    <div :class="$style.Setting__info">
      2.{{ $t('marketing.pages.meeting_marketing.qdypz_2c75a2') }}
      <a :class="$style.Setting__alink" @click="handleCustomConfig">{{ $t('marketing.pages.meeting_marketing.qpz_a532be') }}</a>
    </div>
    <v-dialog
      :title="$t('marketing.pages.meeting_marketing.zdxcbmnrym_10cde6')"
      width="635px"
      :cancelText="$t('marketing.commons.qx_625fb2')"
      :okText="$t('marketing.commons.bc_be5fbb')"
      :visible="isShowSignupDialog"
      @onSubmit="handleSave"
      @onClose="isShowSignupDialog = false"
      :zIndex="1000"
    >
      <div :class="$style.ScanSignInSetting__target">
        <div :class="$style.target__title">{{ $t('marketing.pages.meeting_marketing.qxzzdxcbmn_e10601') }}</div>
        <div :class="$style.target__desc">{{ $t('marketing.pages.meeting_marketing.yhqdwbmsdj_137fb9') }}</div>
        <div :class="$style.target__form">
          <ElSelect
            :class="['el-select', $style.target__selector]"
            v-model="model_targetType"
          >
            <ElOption
              v-for="item in targetMaps"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></ElOption>
          </ElSelect>
          <div :class="$style.target__material" v-show="model_targetType === 'material'" v-loading="loading">
            <ElInput
              :value="model_material.title"
              @click.native="selectMaterialDialog = true"
              :class="$style.material__input"
            >
              <div :class="$style.search_icon" slot="append"></div>
            </ElInput>
          </div>
          <div :class="$style.target__url" v-show="model_targetType === 'url'">
            <ElInput type="input" v-model="model_url" :placeholder="$t('marketing.commons.qsrzddbmwy_222469')"></ElInput>
          </div>
        </div>
      </div>
    </v-dialog>

    <qrcode-download-dialog :visible.sync="isShowDownloadDialog" />

    <SelectMaterialDialog
      v-if="selectMaterialDialog"
      :menus="['marketingEvent']"
      :marketingEventTabbar="[10, 1, 4, 3, 16]"
      :visible="selectMaterialDialog"
      :marketingEventId="marketingEventId"
      @onSubmit="handleMaterialSelected"
      @onClose="selectMaterialDialog = false"
    />
  </div>
</template>

<script>

import QrcodeDownloadDialog from '../qrcode-download-dialog';
import vDialog from '@/components/dialog/index';
import SelectMaterialDialog, { MaterialInstance } from "@/components/select-material-dialog";
import { vaildUrl } from "@/utils";
const targetMaps = [{ label: $t('marketing.commons.tgnr_a6ec90'), value: 'material' }, { label: $t('marketing.commons.wydz_66b985'), value: 'url' }];
export default {
  components: {
ElSelect: FxUI.Select.components.ElSelect,
ElOption: FxUI.Select.components.ElSelect.components.ElOption,
ElInput: FxUI.Input,
Button: FxUI.Button,
QrcodeDownloadDialog,
SelectMaterialDialog,
vDialog
},
  computed: {
    conferenceId() {
      return this.$store.state.MeetingMarketing.conferenceDetail.id;
    },
    marketingEventId() {
      return this.$store.state.MeetingMarketing.marketingEventDetail.marketingEvent.id
    },
    materialDesc() {
      let desc = '';
      const m = this.model_material;
      if (!m.id) return desc;
      switch (m.objectType) {
        case 4: desc = `${m.summary}`;break; // 产品
        case 6: desc = `${m.summary}`;break; // 文章
        case 13: desc = `${m.time}`;break; // 会议
        case 16: desc = `${$t('marketing.commons.cjsj_312f45')}${m.time}`;break; // 表单
        case 26: desc = `${$t('marketing.commons.cjsj_312f45')}${m.time}`;break; // 微页面
      }
      return desc;
    },
    data_targetText() {
      return this.model_targetType === 'url' ? this.model_url : this.model_material.title;
    },
  },
  data() {
    return {
      isShowDownloadDialog: false,
      isShowSignupDialog: false,
      selectMaterialDialog: false,
      loading: false,
      targetMaps,
      model_targetType: targetMaps[0].value,
      model_material: {},
      model_url: null,
      old_model: {
        model_targetType: targetMaps[0].value,
        model_material: {},
        model_url: null,
      },
    }
  },
  watch: {
    isShowSignupDialog() {
      this.getSignInSetting();
    },
  },
  methods: {
    async handleMaterialSelected(row) {
      // 添加物料回调
      console.log("添加组件", row);
      this.model_material = row;
      // await this.updateSignInSetting();
    },
    async updateSignInSetting() {
      const params = {
        activityId: this.conferenceId,
        ...(this.model_targetType === 'url' ? {
          jumpUrl: this.model_url,
        } : {
          jumpObjectId: this.model_material.id,
          jumpObjectType: this.model_material.objectType,
        })
      }
      const res = await YXT_ALIAS.http.updateSignInSetting(params);
      if(!res || res.errCode !== 0) return false;
      FxUI.Message.success($t('marketing.commons.szcg_f6088e'));
      return true;
    },
    async getSignInSetting() {
      if(!this.conferenceId) return
      this.loading = true;
      const res = await YXT_ALIAS.http.getSignInSetting({ activityId: this.conferenceId });
      this.loading = false;
      if(!res || res.errCode !== 0) { return; };
      const { jumpObjectType: objectType, jumpObjectId: objectId, jumpUrl: url } = res.data;
      if (objectType && objectId) {
        await this.getObjectDetail(objectType, objectId);
      } else {
        this.model_targetType = 'url';
        this.model_url = url;
      }
    },
    async getObjectDetail(objectType, objectId) {
      const res = await MaterialInstance.getMaterialDetailByObjectType(objectType, objectId);
      this.loading = false;
      if(!res || res.errCode !== 0) return;
      const data = MaterialInstance.formatDataByObjectType(objectType, res.data);
      console.log('getObjectDetail', data);
      this.model_material = data;
    },
    validate() {
      const { model_targetType, model_url, model_material } = this;
      if (model_targetType === 'url') {
        if (!vaildUrl(model_url)) {
          FxUI.Message.error($t('marketing.commons.qsrhfwydz_50cbcf'));
          return false;
        };
      } else {
        if (!model_material.id) {
          FxUI.Message.error($t('marketing.commons.qsrxzygtgn_2eb7d7'));
          return false;
        }
      }
      return true;
    },
    async handleSave() {
      if (!this.validate()) return;
      await this.updateSignInSetting();
      this.isShowSignupDialog = false;
      this.getSignInSetting();
    },
    handleCustomConfig() {
      this.$router.push({
        name: "meeting-signin-custom",
        params: { id:  this.conferenceId, marketingEventId: this.marketingEventId },
      });
    }
  },
  mounted() {
    // this.getSignInSetting();
  },
}
</script>

<style lang="less" module>
@basePath: '../../../../assets/images';
.ScanSignInSetting {
  .Setting__info {
    color: #91959E;
    font-size: 12px;
    margin-top: 6px;
    display: flex;
  }
  .Setting__alink {
    color: var(--color-info06,#407FFF);
    text-decoration: none;
    cursor: pointer;
    margin-left: 20px;
  }
}
.ScanSignInSetting__target {
  margin-top: 10px;
  box-sizing: border-box;
  max-width: 750px;
  font-size: 14px;
  line-height: 20px;

  .target__title {
    color: #545861;
  }
  .target__desc {
    margin-top: 8px;
    color: #91959E;
  }
  .target__form {
    margin-top: 10px;
    display: flex;
  }
  .target__selector {
    margin-right: 10px;
    flex-shrink: 0;
  }
  .target__material {
    flex: 1;
    margin-bottom: 20px;
    background: #FAFAFA;
    border-radius: 3px;
    max-width: 520px;
    .material__input {
      /deep/ .el-input-group__append {
        background: white;
      }
      /deep/ .search_icon {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        height: 34px; // FOR IE
        background: url('@{basePath}/icon/search-icon.png') center / 18px no-repeat;
        cursor: pointer;
      }
    }
  }
  .target__url {
    flex: 1;
    max-width: 520px;
    margin-bottom: 20px;
  }
}
</style>