<template>
    <div class="meeting-marketing__tickets-wrapper">
      <div class="tickets__header">
        <div class="header__desc">
          {{ $t('marketing.pages.meeting_marketing.tqszhhyqdf_6849e6') }}
        </div>
      </div>
      <div class="tickets__main">
        <div class="tickets__panel" v-for="item in config" :key="item.id">
          <div class="panel__icon">
            <img :src="item.icon" />
          </div>
          <div class="panel__info">
            <div class="info__title">
              {{item.title}}
              <div v-if="!item.noswitch" class="title__switch">
              <el-switch
                active-color="#72CE56"
                :disabled="item.switch"
                v-model="item.switch"
                size="small"
                @change="handleSwitchChange(item)"
              ></el-switch>
            </div>
            </div>
            <template v-if="!item.component">
              <div class="info__desc">
                <div class="desc__item" v-for="(ditem, dindex) in item.desc" :key="dindex">{{ditem}}</div>
              </div>
              <div class="info__selector" v-if="item.id === 'participation-code'">
                <div class="selector__label">{{ $t('marketing.pages.meeting_marketing.ypry_25a6c2') }}</div>
                <div class="selector__main">
                  <StaffSelectorDialogBar
                    @change="handleStaffSelectorChange"
                    :defaultSelectedItems="{colleague: defaultCheckInStaffs}"
                    :options="{
                      title: $t('marketing.commons.tjypry_96b660'),
                      label: $t('marketing.commons.tjypry_96b660'),
                      includeCompany: false, 
                      excludeDepart: true,
                    }"
                  ></StaffSelectorDialogBar>
                </div>
              </div>
              <div class="info__operation" v-if="item.buttons.length > 0">
                <div class="operation__buttons">
                  <el-link
                    type="standard"
                    :underline="false"
                    v-for="bitem in item.buttons"
                    :key="bitem.id"
                    v-show="bitem.show"
                    @click="hanldeButtonClick(bitem.id)"
                  >{{bitem.label}}</el-link>
                </div>
              </div>
            </template>
            <component v-else :is="item.component"></component>
          </div>
        </div>
      </div>
      <ticket-before-create
        v-if="showTicketBeforeCreateDialog"
        :visible="showTicketBeforeCreateDialog"
        @update:visible="handleTicketBeforeCreateDialogVisible"
        @update:submit="handleTicketBeforeCreateDialogSubmit"
      ></ticket-before-create>
      <meeting-ticket-create
        :visible="showMeetingTicketsCreateDialog"
        :meetingId="meetingId"
        :wxAppId="wxAppId"
        @update:visible="handleMeetingTicketsCreateDialogVisible"
        @update:submit="handleMeetingTicketsCreateDialogSubmit"
      ></meeting-ticket-create>
    </div>
</template>

<script>
import http from '@/services/http/index';
import { redirectToFS } from '@/utils/index';
import TicketBeforeCreate from './ticket-before-create-dialog';
import MeetingTicketCreate from '@/components/meeting-tickets-create';
import config from './tickets-setting-config';
import StaffSelectorDialogBar from '@/modules/staff-selector-dialog-bar';
import { getEmployeeByIdAndVuex } from '@/utils';

import ScanSignInSetting from './components/ScanSignInSetting';

export default {
  components: {
VButton: FxUI.Button,
TicketBeforeCreate,
MeetingTicketCreate,
ElSwitch: FxUI.Switch,
ElLink: FxUI.Link,
StaffSelectorDialogBar,
ScanSignInSetting
},
  data() {
    return {
      showTicketBeforeCreateDialog: false,
      showMeetingTicketsCreateDialog: false,
      config,
      wxAppId: '', // 微信appid
      defaultCheckInStaffs: [],
    };
  },
  computed: {
    meetingId() {
      return this.$store.state.MeetingMarketing.conferenceDetail.id;
    },
    isQywxOpen() {
      return this.$store.state.Global.isQywxOpen;
    },
    addressBookType() {
      return this.$store.state.Global.addressBookType;
    }
  },
  methods: {
    queryMeetingTicketDetail() {
      if(!this.meetingId) return
      http.queryMeetingTicketDetail({ associationId: this.meetingId }).then((res) => {
        if (res && res.errCode === 0) {
          const ticketIndex = this.config.indexOf(this.config.filter(item => item.id === 'tickets-setting')[0]);
          this.config[ticketIndex] && (this.config[ticketIndex].switch = res.data.status === 1);
        }
      });
    },
    // 改变开关状态
    handleSwitchChange(item) {
      this.showTicketBeforeCreateDialog = true;
      item.switch = false;
    },
    // 卡券创建前校验弹窗
    handleTicketBeforeCreateDialogVisible(visible) {
      this.showTicketBeforeCreateDialog = visible;
    },
    // 卡券创建前校验弹窗提交
    handleTicketBeforeCreateDialogSubmit(wxAppId) {
      this.wxAppId = wxAppId;
      this.showMeetingTicketsCreateDialog = true;
    },
    // 卡券创建弹窗
    handleMeetingTicketsCreateDialogVisible(visible) {
      this.showMeetingTicketsCreateDialog = visible;
    },
    // 卡券创建弹窗提交
    handleMeetingTicketsCreateDialogSubmit() {
      
      this.showMeetingTicketsCreateDialog = false;
      this.queryMeetingTicketDetail();
    },
    // 按钮点击事件分发中心
    hanldeButtonClick(buttonid) {
      switch (buttonid) {
        case 'howTickets':
          {
            // window.open('https://www.baidu.com', '__blank');
          }
          break;
        case 'setTickets':
          {
            this.handleMeetingTicketsCreateDialogVisible(true);
          }
          break;
        case 'downSignInQrcode':
          {
            // this.$router.push({ name: 'meeting-check-in' });
            this.handleDownloadDialogVisible(true);
          }
          break;
        case 'goParticipants':
          {
            // this.$router.push({ name: 'meeting-participants' });
            // 跳会议聚合页
            this.$router.push({
              name: "meeting-detail",
              params: {
                id: this.meetingId,
              }
            });
          }
          break;
        case 'goParticipantsCode':
          {
            // this.$router.push({ name: 'meeting-participants' });
          }
          break;
        default:
          console.log(buttonid);
      }
    },
    async queryTicketManager() {
      if(!this.meetingId) return
      const res = await YXT_ALIAS.http.queryTicketManager({
        conferenceId: this.meetingId,
      });
      if (res && res.errCode === 0) {
        console.log('queryTicketManager', res);
        this.defaultCheckInStaffs = (res.data?.ticketCheckManagerFsUserIds || []).map(id => {
          const employee = this.getEmployee(id);
          console.log('employee', employee);
          return {
            ...employee,
            id: employee.id,
            name: employee.name,
            fsUserId: employee.fsUserId,
          }
        })
        console.log('defaultCheckInStaffs', this.defaultCheckInStaffs);
      }

    },

    getEmployee(id) {
      let userId = id;
      if (this.addressBookType === 'ding') {
        userId = { fsUserId : id };
      }
      return getEmployeeByIdAndVuex(userId, this.$store,'fs');
    },
    async handleStaffSelectorChange(selectResult) {
      console.log('handleStaffSelectorChange', selectResult);
      const fsUserIds = this.getFsUserIds(selectResult.colleague) || [];
      // if (!fsUserIds || fsUserIds.length === 0) {
      //   return;
      // }
      const res = await YXT_ALIAS.http.addTicketManger({
        conferenceId: this.meetingId,
        ticketCheckManagerFsUserIds: fsUserIds,
      });
      if (res && res.errCode === 0) {
        FxUI.Message.success($t('marketing.pages.meeting_marketing.yprygxcg_c2f64e'));
      } else {
        this.queryTicketManager();
      }
    },
    getFsUserIds(selectResult) {
      if (this.addressBookType !== 'ding') {
        return selectResult.map(item => item.id);
      } else {
        const dingIds = selectResult.map(item => item.id);
        const fsIds = selectResult.filter(item => item.fsUserId).map(item => item.fsUserId);
        // 所选的钉钉用户全部都没有fs虚拟身份
        if (fsIds.length === 0 && dingIds.length > 0) {
          FxUI.Message.error($t('marketing.commons.dqxddqbygj_41d5e1'));
          return false;
        }
        // 所选的钉钉用户有部分没有fs虚拟身份
        if (fsIds.length < dingIds.length) {
          FxUI.Message.warning($t('marketing.commons.dqxdbfyghw_07ddb9'));
        }
        return fsIds;
      }
    },
  },
  created() {},
  mounted() {
    this.queryMeetingTicketDetail();
    this.queryTicketManager();
  },
};
</script>
<style lang="less" scoped>
.meeting-marketing__tickets-wrapper {
  height: fit-content;
  background-color: #ffffff;
  padding: 0 20px;
  .tickets__header {
    color: var(--color-neutrals15);
    font-size: 14px;
    line-height: 20px;
    margin: 20px 0;
  }
  .tickets__main {
    background-color: #ffffff;
    .tickets__panel {
      display: flex;
      padding: 12px;
      margin-bottom: 20px;
      border-radius: var(--4, 4px);
      border: 1px solid #DEE1E8;
      .panel__icon {
        margin-right: 10px;
        img {
          width: 110px;
          height: 110px;
        }
      }
      .panel__info {
        flex: 1;
        .info__title {
          color: #181c25;
          font-size: 14px;
          line-height: 20px;
          font-weight: bold;
          display: flex;
          align-items: center;
          .title__switch {
            margin: -5px 0 0 5px;
          }
        }
        .info__desc {
          color: #91959e;
          font-size: 12px;
          line-height: 18px;
          margin-top: 6px;
        }
        .info__selector {
          display: flex;
          margin-top: 6px;
          align-items: center;
          .selector__main {
            margin-left: 12px;
            width: 460px;
          }
        }
      }
      .info__operation {
        display: flex;
        // flex-direction: column;
        // align-items: flex-end;
        .operation__buttons {
          margin-top: 5px;
          &:not(:first-child) {
            margin-top: 26px;
          }
        }
        .el-link.el-link--standard {
          font-size: 13px;
          margin-right: 20px;
        }
      }
    }
  }
}
</style>
