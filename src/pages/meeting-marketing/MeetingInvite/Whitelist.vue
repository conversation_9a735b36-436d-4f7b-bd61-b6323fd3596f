<!--
 * @Author: <EMAIL>
 * @Date: 2021-06-25 17:11:24
 * @Description: 允许员工添加客户到邀约名单
-->
<template>
  <div class="Whitelist">
    <div :class="['Whitelist__form']">
      <div :class="['Whitelist__formItem']">
        <div class="formItem__label">
          {{ $t('marketing.pages.meeting_marketing.xzkffw_17f8f8') }}
        </div>
        <div
          class="formItem__input"
        >
          <Selectbar
            :btn-text="$t('marketing.pages.meeting_marketing.tjyg_2ef46a')"
            :remove-range-item="removeItem"
            :selects="model_selectResult"
            class="select-range-bar"
            @change="changeItem"
            @update:select="isShowSelector = !isShowSelector"
          />
        </div>
      </div>
    </div>
    <StaffSelectorDialog
      :options="{
        zIndex: 99999,
      }"
      :sp-dialog-visible.sync="isShowSelector"
      :select-result="model_selectResult"
      @update:selectResult="handleSubmit"
      lock='fs'
    />
  </div>
</template>

<script>
import Selectbar from '@/components/selectbar/index'
import StaffSelectorDialog from '@/modules/staff-selector-dialog'
import { getEmployeeByIdAndVuex, getDepartmentByIdAndVuex } from '@/utils'

export default {
  components: {
  ElSwitch: FxUI.Switch,
  ElButton: FxUI.Button,
  Selectbar,
  StaffSelectorDialog
},
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
    loading_submit: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      model_open: false,
      beforeSave_open: false,
      model_selectResult: {},
      beforeSave_selectResult: false,
      isShowSelector: false,
      countAllStaff: 0,
    }
  },
  computed: {
    model_staff: {
      get() {
        return {
          colleague: this.model_selectResult.colleague || [],
          depart: this.model_selectResult.depart || [],
        }
      },
      set(newValue) {
        this.model_selectResult = newValue
      },
    },
    addressBookType() {
      return this.$store.state.Global.addressBookType
    },
  },
  watch: {
    model_staff() {
      console.log('model_staff', this.model_staff)
    },
    data: {
      deep: true,
      handler() {
        this.modelReset()
      },
    },
  },
  methods: {
    modelReset() {
      const data = JSON.parse(JSON.stringify(this.data))
      this.model_open = data.openAddInvitees
      this.countAllStaff = data.countAllStaff
      this.model_selectResult = {
        colleague: data?.addressBookContainer?.fsUserId.map(id => {
          const employee = this.getEmployee(id)
          console.log('employee: ', employee)
          return {
            id: employee.id,
            name: employee.name,
            fsUserId: employee.fsUserId,
          }
        }) || [],
        depart: data?.addressBookContainer?.department?.map(id => {
          const depart = this.getDepart(id)
          console.log('depart: ', depart);
          return {
            id: depart.id,
            name: depart.name,
          }
        }) || [],
      }
    },
    getEmployee(id) {
      return getEmployeeByIdAndVuex(id, this.$store, 'fs')
    },
    getDepart(id) {
      return getDepartmentByIdAndVuex(id, this.$store, 'fs')
    },
    changeItem() {
      console.log('selector change', this.model_staff)
    },
    removeItem(type, id) {
    },
    handleSubmit(results) {
      this.model_selectResult = results
      this.handleSave()
    },
    async handleSave() {
      const fsUserIds = this.getFsUserIds()
      const departIds = this.getDepartIds()
      if ((!fsUserIds || fsUserIds.length === 0) && (!departIds || departIds.length === 0)) {
        return
      }
      this.$emit('submit', {
        openAddInvitees: this.model_open,
        addressBookContainer: {
          fsUserId: fsUserIds,
          department: departIds,
        },
      })
    },
    getFsUserIds() {
      if (this.addressBookType !== 'ding') {
        return this.model_staff.colleague.map(item => item.id)
      }
      const dingIds = this.model_staff.colleague.map(item => item.id)
      const fsIds = this.model_staff.colleague.filter(item => item.fsUserId).map(item => item.fsUserId)
      // 所选的钉钉用户全部都没有fs虚拟身份
      if (fsIds.length === 0 && dingIds.length > 0) {
        FxUI.Message.error($t('marketing.commons.dqxddqbygj_41d5e1'))
        return false
      }
      // 所选的钉钉用户有部分没有fs虚拟身份
      if (fsIds.length < dingIds.length) {
        FxUI.Message.warning($t('marketing.commons.dqxdbfyghw_07ddb9'))
      }
      return fsIds
    },
    getDepartIds() {
      const departIds = this.model_staff.depart.filter(item => item.id).map(item => item.id)
      return departIds
    },
  },
}
</script>

<style lang="less" scoped>
.Whitelist__formItem {
  display: flex;
  align-items: center;
  .formItem__label {
    font-size: 14px;
    color: #181C25;
    margin-right: 40px;
  }
  .formItem__show {
    font-size: 14px;
    color: #181C25;
    &.formItem__show--staff {
      display: flex;
      .staff__item {
        height: 22px;
        line-height: 22px;
        background: #E0EEFF;
        color: #839AB6;
        font-size: 12px;
        padding: 3px 16px;
        margin: 0 10px 10px 0;
        border-radius: 3px;
      }
    }
  }
  .formItem__button {
    margin: 0 20px 0 auto;
    padding: 0;
  }
}
.Whitelist__buttons {
  margin-left: 110px;
}
.Whitelist__button {
  width: 87px;
}
.select-range-bar {
  width: 600px;
}
</style>
