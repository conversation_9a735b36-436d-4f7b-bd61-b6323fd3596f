<template>
  <div class="ParticipantsInviteDialogPoster">
    <div class="ParticipantsInviteDialogPoster__body">
      <div 
        class="ParticipantsInviteDialogPoster__empty"
        v-if="isEmpty"
        @click="handleShowCreate">
        <div class="empty__link"><i class="iconfont">&#xe624;</i>{{ $t('marketing.commons.tjhb_a1b5f4') }}</div>
      </div>
      <div 
        class="ParticipantsInviteDialogPoster__show"
        v-else>
        <ElImage
          class="show__image"
          :src="invitePoster.qrPosterUrl"
          ref="$posterImg"
          :preview-src-list="[invitePoster.qrPosterUrl]"
        ></ElImage>
        <div class="show__mask">
          <span class="show__btn" @click="handlePreview">{{ $t('marketing.commons.yl_645dbc') }}</span>
          <span class="show__btn" @click="handleEdit">{{ $t('marketing.commons.bj_95b351') }}</span>
        </div>
      </div>
    </div>
    <InvitePosterCreateDialog
      v-if="isShowInvitePosterCreateDialog"
      :visible.sync="isShowInvitePosterCreateDialog"
      @update:list="handleAfterCreate"
      :marketingEventId="marketingEventId"
      :marketingEventName="marketingEventName || meetingTitle"
      :meetingId="meetingId"
      :meetingTitle="meetingTitle"
      @update:isSaving="setIsSaving"
      :formId="formId"
      :formName="formName"></InvitePosterCreateDialog>
  </div>
</template>

<script>

import { mapState, mapActions } from 'vuex';
import InvitePosterCreateDialog from '@/pages/poster-gallery/create/invite-poster.vue'
export default {
  components: {
InvitePosterCreateDialog,
ElImage: FxUI.Image
},
  props: {
    poster: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  computed: {
    conferenceDetail() {
      console.log('conferenceDetail', this.$store.state.MeetingMarketing.conferenceDetail);
      return this.$store.state.MeetingMarketing.conferenceDetail;
    },
    marketingEventDetail() {
      console.log('marketingEventDetail', this.$store.state.MeetingMarketing.marketingEventDetail);
      return this.$store.state.MeetingMarketing.marketingEventDetail;
    },
    marketingEventId() {
      return this.marketingEventDetail.marketingEvent.id;
    },
    marketingEventName() {
      return this.marketingEventDetail.marketingEvent.name;
    },
    meetingTitle() {
      return this.conferenceDetail.title;
    },
    meetingId() {
      return this.conferenceDetail.id;
    },
    formId() {
      return this.conferenceDetail.formId;
    },
    formName() {
      return this.conferenceDetail.formName;
    },
    isEmpty() {
      return !this.invitePoster.qrPosterUrl;
    },
    invitePoster() {
      return this.$store.state.MeetingMarketing.invitePoster;
    },
  },
  watch: {
  },
  data() {
    return {
      isShowInvitePosterCreateDialog: false,
    }
  },
  methods: {
    ...mapActions([
      'getInvitationCommonSetting',
      'getInvitePoster',
    ]),
    handleShowCreate() {
      this.isShowInvitePosterCreateDialog = true;
    },
    handleEdit() {
      this.isShowInvitePosterCreateDialog = true;
    },
    handlePreview() {
      console.log('this.$refs.$posterImg', this.$refs.$posterImg);
      this.$refs.$posterImg.showViewer = true;
    },
    handleAfterCreate(qrPosterObject) {
      this.getInvitePoster({ id: this.meetingId });
    },
    setIsSaving() {},
  },
  mounted() {
    this.getInvitePoster({ id: this.meetingId });
  },
}
</script>

<style lang="less" scoped>
.ParticipantsInviteDialogPoster {
  .ParticipantsInviteDialogPoster__body {
    display: flex;
    align-items: center;
  }
  .ParticipantsInviteDialogPoster__empty {
    width: 120px;
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #E9EDF5;
    border-radius: 4px;
    color: @color-subtitle;
    font-size: 13px;
    cursor: pointer;
    box-sizing: border-box;
    .empty__link {
      display: flex;
      flex-direction: column;
      line-height: 18px;
      align-items: center;
    }
    .iconfont {
      margin-right: 5px;
      color: var(--color-info06,#407FFF);
    }
  }
  .ParticipantsInviteDialogPoster__show {
    display: flex;
    width: 120px;
    height: 200px;
    position: relative;
    border: 1px solid #E9EDF5;
    border-radius: 4px;
    .show__image {
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;
    }
    .show__mask {
      opacity: 0;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

    }
    &:hover {
      .show__mask {
        opacity: 1;
      }
    }
    .show__btn {
      margin: 10px;
      color: #fff;
      font-size: 14px;
      cursor: pointer;
      &:hover {
        color: #66b1ff;
      }
    }
  }
}

</style>