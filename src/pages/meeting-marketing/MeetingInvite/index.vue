<template>
    <div class="MeetingInvite">
      <div class="MeetingInvite__Block">
        <div class="Block__desc" style="color: #545861;">
          {{ $t('marketing.pages.meeting_marketing.kkqmxnbygk_82e96e') }}
        </div>
      </div>
      <div class="MeetingInvite__Block">
        <div class="Block__header">
          <div class="Title">{{ $t('marketing.commons.yyhb_a8cb83') }}</div>
          <ElSwitch
            class="header__switch"
            v-model="model_setting.openInvitationPoster"
            :disabled="loading_poster_switch"
            size="small"
            @change="handleSwitchPoster"
          ></ElSwitch>
        </div>
        <div class="Block__desc Block__desc--Poster">
          {{ $t('marketing.pages.meeting_marketing.kqhygzsdyy_1a20a6') }}
        </div>
        <div class="Block__body Block__body--Poster" v-show="model_setting.openInvitationPoster">
          <Poster></Poster>
          <div class="Poster__info" v-show="invitePoster.forwardName">
            {{ $t('marketing.commons.smhtz_cc12c5') }} {{ invitePoster.forwardName
            }}{{ invitePoster.forwardContent ? ':' : '' }}{{ invitePoster.forwardContent }}
          </div>
        </div>
      </div>
      <div class="Dividing"></div>
      <div :class="['MeetingInvite__Block']">
        <div class="Block__header">
          <div class="Title">{{ $t('marketing.pages.meeting_marketing.yxygtjkhdy_a6007b') }}</div>
          <ElSwitch
            class="header__switch"
            v-model="model_setting.openAddInvitees"
            size="small"
            :disabled="loading_whitelist_switch"
            @change="handleSwitchWhitelist"
          ></ElSwitch>
        </div>
        <div class="Block__desc Block__desc--Whitelist">
          {{ $t('marketing.pages.meeting_marketing.kqhyxygzfx_c0ce71') }}
        </div>
        <div class="Block__body Block__body--Whitelist" v-show="model_setting.openAddInvitees">
          <Whitelist
            :data="model_setting"
            @submit="handleWLSubmit"
            :loading_submit="loading_WL_submit"
          ></Whitelist>
        </div>
      </div>
      <div class="Dividing"></div>
    </div>
</template>

<script>
import Poster from './Poster.vue';
import Whitelist from './Whitelist.vue';
import { mapState, mapActions } from 'vuex';
export default {
  components: {
    ElSwitch: FxUI.Switch,
    ElButton: FxUI.Button,
    Poster,
    Whitelist,
  },
  data() {
    return {
      model_poster: false,
      model_setting: {},
      loading_poster_switch: false,
      loading_whitelist_switch: false,
      loading_WL_submit: false,
    };
  },
  computed: {
    MeetingMarketing() {
      return this.$store.state.MeetingMarketing;
    },
    conferenceDetail() {
      return this.MeetingMarketing.conferenceDetail;
    },
    conferenceId() {
      return this.conferenceDetail.id;
    },
    marketingEventId() {
      return this.conferenceDetail.marketingEventId;
    },
    invitationCommonSetting() {
      return this.MeetingMarketing.invitationCommonSetting;
    },
    invitePoster() {
      return this.MeetingMarketing.invitePoster;
    },
  },
  watch: {
    invitationCommonSetting() {
      this.model_setting = this.invitationCommonSetting;
      this.backup_setting = this.invitationCommonSetting;
    },
  },
  methods: {
    ...mapActions(['getInvitePoster', 'getInvitationCommonSetting']),
    // async getInvitationCommonSetting() {
    //   const res = await YXT_ALIAS.http.getInvitationCommonSetting({ conferenceId: this.conferenceId });
    //   if (!res || res.errCode !== 0) {
    //     return;
    //   }
    //   this.model_setting = res.data;
    //   this.backup_setting = res.data;
    // },
    async upsertInvitationCommonSetting(params) {
      const res = await YXT_ALIAS.http.upsertInvitationCommonSetting({
        conferenceId: this.conferenceId,
        ...this.model_setting,
        ...params,
      });
      if (!res || res.errCode !== 0) {
        FxUI.Message.error($t('marketing.pages.meeting_marketing.gxsbqzs_7d3756'));
        this.model_setting = this.backup_setting;
        return false;
      }
      FxUI.Message.success($t('marketing.commons.gxcg_55aa63'));
      this.getInvitationCommonSetting({ id: this.conferenceId });
      return true;
    },
    async handleSwitchPoster() {
      this.loading_poster_switch = true;
      await this.upsertInvitationCommonSetting();
      this.loading_poster_switch = false;
    },
    async handleSwitchWhitelist() {
      this.loading_whitelist_switch = true;
      await this.upsertInvitationCommonSetting();
      this.loading_whitelist_switch = false;
    },
    async handleWLSubmit(params) {
      this.loading_WL_submit = true;
      const isSuccess = await this.upsertInvitationCommonSetting(params);
      this.loading_WL_submit = false;
    },
  },
  mounted() {
    this.getInvitationCommonSetting({ id: this.conferenceId });
  },
};
</script>

<style lang="less" scoped>
.MeetingInvite {
  height: 100%;
  background-color: #ffffff;
  padding: 0 20px;
}
.MeetingInvite__Block {
  padding: 20px 0;
  &.isInput {
    background: #f4f8ff;
  }
  .Block__header {
    display: flex;
    align-items: center;
    .header__switch {
      margin-left: 8px;
    }
    .header__button {
      margin-left: 20px;
      padding: 0;
    }
  }
  .Block__desc {
    font-size: 12px;
    color: #91959e;
  }
  .Block__desc--Poster,
  .Block__desc--Whitelist {
    margin-top: 6px;
    margin-bottom: 10px;
  }
  .Block__body--Poster {
    .Poster__info {
      margin-top: auto;
      color: #181c25;
      margin-top: 10px;
      font-size: 12px;
    }
  }
}

.Title {
  font-size: 14px;
  color: #181c25;
  position: relative;
  margin-bottom: 6px;

}
.Dividing {
  border-top: 1px dashed #e9edf5;
}
</style>