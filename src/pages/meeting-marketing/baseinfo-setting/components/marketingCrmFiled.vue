<template>
  <div class="marketing-crm-filed">
    <div class="marketing-crm-filed__title" @click="crmFiledDialog = true">
      <fx-button size="small" style="padding: 0" type="text" icon="fx-icon-comment">{{
        $t("marketing.commons.bj_95b351")
      }}</fx-button>
    </div>
    <fx-dialog
      :visible.sync="crmFiledDialog"
      :title="$t('marketing.pages.meeting_marketing.zd_9245d0')"
      class="marketing-crm-filed__dialog"
      width="500px"
    >
      <div class="crm-filed-dialog">
        <div class="crm-filed-tips">
          <p>1.{{$t('marketing.pages.meeting_marketing.btzdcjhbjs_a398b2')}}。</p>
          <p>2.{{$t('marketing.pages.meeting_marketing.xtxxzdwysc_d8603f')}}。</p>
        </div>
        <crm-filed
          ref="crmFiled"
          :fieldData="crmFiledData"
          :hideFields="hideFields"
          :is-mobile-display="isMobileDisplay"
          @emptyField="emptyField"
        />
        <empty v-if="marketingCrmFiledEmpty" type="box" imgWidth="375px" imgHeight="120px" :desc="$t('marketing.pages.meeting_marketing.zwbthxtxxf_a42895')"/>
      </div>
      <template #footer>
        <fx-button size="small" :loading="submitLoading" type="primary" @click="handleConfirm">{{$t('marketing.commons.qd_38cf16')}}</fx-button>
        <fx-button size="small" @click="crmFiledDialog = false">{{$t('marketing.commons.qx_625fb2')}}</fx-button>
      </template>
    </fx-dialog>
  </div>
</template>

<script>
import crmFiled from "@/components/meeting-crm-filed/index";
import http from "@/services/http/index.js";
import empty from "@/components/common/empty.vue";
export default {
  components: {
    crmFiled,
    empty
  },
  props: {
    crmFiledData: {
      type: Object,
      default: () => ({})
    },
    isMobileDisplay: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      crmFiledDialog: false,
      hideFields: ['begin_time', 'end_time', 'name', 'event_type', 'owner'],
      submitLoading: false,
      marketingCrmFiledEmpty: false
    };
  },
  computed: {
    conferenceDetail() {
      return this.$store.state.MeetingMarketing.conferenceDetail;
    },
    marketingEventId(){
      return this.conferenceDetail.marketingEventDetail.marketingEventId;
    }
  },
  methods: {
    async handleConfirm() {
      this.submitLoading = true
      let crmFiledData = this.$refs.crmFiled.submit();
      const res = await http.updateConferenceObjectData({
        marketingEventId: this.marketingEventId,
        createObjectDataModel: {
          objectData: crmFiledData
        }
      })
      if(res.errCode === 0) {
        FxUI.Message.success($t('marketing.commons.gxcg_55aa63'))
        this.submitLoading = false
        this.crmFiledDialog = false
      } else {
        FxUI.Message.error($t('marketing.commons.gxsb_930442'))
        this.submitLoading = false
      }
    },
    emptyField(isEmpty) {
      this.marketingCrmFiledEmpty = isEmpty
      this.$emit('emptyField',isEmpty)
    }
  },
  mounted() {
    const isMarketingEventEidt = this.$route.params.isMarketingEventEidt;
    if(isMarketingEventEidt) {
      this.loading = true
      this.crmFiledDialog = true
      setTimeout(() => {
        this.loading = false
      }, 1000)
    }
  }
};
</script>

<style lang="less" scoped>
.marketing-crm-filed {
  margin-top: 24px;
}
.marketing-crm-filed__dialog {
    .crm-filed-dialog {
      padding: 0;
      .crm-filed-tips {
        padding: 8px 12px;
        background: #F2F3F5;
        p{
          color: #545861;
          font-size: 12px;
          font-weight: 400;
          line-height: 18px;
        }
      }
    }
  }
</style>
