<!-- 组件说明 -->
<template>
     <div class="meeting-marketing__enroll-setting-wrapper">
       <!-- 左侧预览区域 -->
       <div class="left">
         <div class="title">
           {{ $t("marketing.commons.yl_645dbc") }}
         </div>
         <div class="site-preview__wrapper">
          <div class="site-preview__page-header">
            <img src="@/assets/images/phoneHeaderNew.jpg" alt="phone-header" class="site-preview__page-phone-header">
            <div class="site-preview__page-header-title km-t-ellipsis1">
              {{ conferenceDetail.title || $t('marketing.commons.bmbd_112a9c') }}
            </div>
          </div>
          <page-render
           class="page-render"
           :title="previewTitle"
           :data="previewData"
           :scroll-height="609"
         />
         </div>
         <div class="site-tools">
           <sitePreviewCode
             class="site-tools-qrcode"
             :site-id="activityDetailSiteId"
           />
         </div>
       </div>
       <div class="right enroll-setting-content__wrapper">
         <div
           :class="['enroll-item', item.isSetting && 'enroll-item-setting', index === 1 && marketingCrmFiledEmpty && 'enroll-item-empty']"
           v-for="(item, index) in enrollSettingList"
           v-show="item.show"
           :key="index"
         >
           <div :class="['item-title']">
             <div class="title-left">
               {{ item.title }}
             </div>
             <div
               v-if="!item.isSetting && item.component !== 'MeetingCrmFiled'"
               class="title-right"
               @click="item.isSetting = true"
             >
               <fx-button size="small" type="text" icon="fx-icon-comment"
                 >{{ $t('marketing.commons.bj_95b351') }}</fx-button
               >
             </div>
           </div>
           <div class="item-content">
             <div :class="['item-content__item']">
               <component
                 @update:settingStatus="val => (item.isSetting = val)"
                 :setting-status="item.isSetting"
                 :meetingInfo="conferenceDetail"
                 :crm-filed-data="crmFiledData"
                 @updateForm="handleUpdateForm"
                 @settingStatusChange="handleSettingStatusChange"
                 @emptyField="emptyField"
                 :is="item.component"
               />
             </div>
           </div>
         </div>
       </div>
     </div>
</template>

<script>

import PageRender from "@/components/Hexagon/PageRender";

import http from "@/services/http/index.js";
import sitePreviewCode from "../enroll-setting/compontents/site-preview-qrcode.vue";
import MeetingInfoForm from "../MeetingCreate/MeetingInfo/MeetingInfoForm.vue";
import MeetingCrmFiled from "./components/marketingCrmFiled.vue";
import manageSetting from '../enroll-setting/compontents/manage-setting.vue'
import { parseMarketingComp, hasActivityComponent } from '@/utils/parseMarketingComp.js'


export default {
  components: {
    PageRender,
    sitePreviewCode,
    ElCheckbox: FxUI.Checkbox,
    MeetingCrmFiled,
    MeetingInfoForm,
    manageSetting
  },
  data() {
    return {
      previewTitle: "",
      previewData: {},
      loading: false,
      enrollSettingList: [
        {
          title: $t('marketing.commons.jbxx_9e5ffa'),
          component: "MeetingInfoForm",
          isSetting: this.$route.params.isBaseInfoEidt || false,
          show: true
        },
         {
          title: $t('marketing.commons.bmsz_381009'),
          component: "manageSetting",
          isSetting: false,
          show: true
        },
        {
          title: $t('marketing.commons.schdbtxx_200056'),
          component: "MeetingCrmFiled",
          isSetting: false,
          show: true
        },
      ],
      crmFiledData: {},
      marketingCrmFiledEmpty: false
    };
  },
  computed: {
    conferenceDetail() {
      return {
        ...this.$store.state.MeetingMarketing.conferenceDetail,
        showMap: !!(this.$store.state.MeetingMarketing.conferenceDetail.mapLocation && this.$store.state.MeetingMarketing.conferenceDetail.mapLocation.lng),
        eventType: this.$store.state.MeetingMarketing.conferenceDetail.marketingEventDetail.eventType
      };
    },
    marketingEventId() {
      return this.conferenceDetail.marketingEventDetail.marketingEventId;
    },
    activityDetailSiteId(){
      return this.conferenceDetail.activityDetailSiteId
    }
  },
  mounted() {},
  created() {
    this.queryMarketingEventDetail()
    this.getPagesBySiteId();
  },
  destroyed() {},
  methods: {
    queryMarketingEventDetail() {
      if(!this.marketingEventId) return;
       FS.util.FHHApi(
        {
          url: "/EM1HNCRM/API/v1/object/MarketingEventObj/controller/Detail",
          data: {
            objectDescribeApiName: "MarketingEventObj",
            objectDataId: this.marketingEventId
          },
          success: ({ Result: { StatusCode }, Value }) => {
            if (StatusCode == 0) {
              this.crmFiledData = Value.data;
              const isMobileDisplay = Value.data && Value.data.is_mobile_display === "0";
              this.$store.dispatch('updateConferenceDetail', {
                conferenceDetail: {
                  ...this.conferenceDetail,
                  isMobileDisplay: isMobileDisplay
                }
              });
            }
          }
        },
        {
          errorAlertModel: 1
        }
      );
    },
    getPagesBySiteId() {
      this.activityDetailSiteId && http.getPagesBySiteId({ siteId: this.activityDetailSiteId }).then(res => {
        const { errCode } = res;
        const { data } = res;
        if (errCode === 0) {
          const homePage = data.filter(item => item.isHomepage === 1)[0] ||
            data[0] || { content: '{name:"",components:[]}' };
          this.previewTitle = JSON.parse(homePage.content).name;
          const hasActivity = hasActivityComponent(JSON.parse(homePage.content).components);
          this.hasActivityComponent = hasActivity;
          this.previewData = JSON.parse(homePage.content)
          if(!hasActivity) return;
          console.log('this.conferenceDetail', this.conferenceDetail.location)
          const parseData = parseMarketingComp({
            ...this.conferenceDetail,
            description: this.conferenceDetail.conferenceDetails,
            cover: this.conferenceDetail.coverImageUrl,
            name: this.conferenceDetail.title,
            location: this.conferenceDetail.location,
            showMap: this.conferenceDetail.showMap
          },homePage);
          this.previewData = parseData || {};
        }
      });
    },
    handleUpdateForm(formData) {
      const updateData = {
        ...formData,
        startTime: formData.startTime || this.conferenceDetail.startTime,
        endTime: formData.endTime || this.conferenceDetail.endTime
      };      
      this.formatPreviewData(updateData);
    },
    formatPreviewData(updateData){
      if(!this.hasActivityComponent) return;
      const {
        title,conferenceDetails,cutOffsetList,coverImageUrl,showMap,location
      } = updateData;
      if (!this.previewData.components) return;
      const data = parseMarketingComp({
        ...updateData,
        description: conferenceDetails,
        cover: (cutOffsetList && cutOffsetList.length) ? cutOffsetList[0].image : coverImageUrl ,
        name: title,
        isShowMap: showMap,
        location: location
      },this.previewData);
      this.previewData = data
    },
    handleSettingStatusChange(status) {
      if(!status) {
        this.formatPreviewData(this.conferenceDetail)
      }
    },
    emptyField(isEmpty) {
      this.marketingCrmFiledEmpty = isEmpty
      this.enrollSettingList.forEach(item => {
          if(item.component === 'MeetingCrmFiled') {
            item.show = !isEmpty
          }
        })
    }
  }
};
</script>

<style lang="less" scoped>
.meeting-marketing__enroll-setting-wrapper {
  display: flex;
  background-color: #fff;
  .left {
    padding: 24px;
    .title {
      font-size: 14px;
      color: var(--Text-H1, #181c25);
      font-weight: 400;
      line-height: 20px;
      margin-bottom: 20px;
    }
    .site-preview__wrapper {
      box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.15);
      .site-preview__page-header {
        position: relative;
        .site-preview__page-header-title{
          position: absolute;
          top: 64%;
          left: 50%;
          transform: translate(-50%, -50%);
          font-size: 16px;
          color: #181C25;
          font-weight: bold;
          width: 100%;
          text-align: center;
          padding: 0 16px;
          box-sizing: border-box;
        }
        .site-preview__page-phone-header {
          width: 375px;
        }
      }
    }
    .page-render {
      width: 375px;
    }
    .site-tools {
      margin-top: 20px;
    }
    .site-tools-qrcode {
      /deep/ .site_preview_qrcode {
        justify-content: center;
      }
    }
  }
  .enroll-setting-content__wrapper {
    flex: 1;
    border-left: 1px solid var(--color-neutrals05);
    .enroll-item {
      padding: 20px;
      border-bottom: 1px dashed var(--color-neutrals05);
      &:last-child {
        border-bottom: none;
      }
      &.enroll-item-empty {
        border-bottom: none;
      }
      .item-title {
        display: flex;
        justify-content: space-between;
        height: 24px;
        .title-left {
          position: relative;
          padding-left: 12px;
          color: var(--color-neutrals19);
          font-size: 16px;
          line-height: 24px;
          &::before {
            content: "";
            position: absolute;
            left: 0;
            top: 0;
            display: inline-block;
            width: 4px;
            height: 16px;
            background: var(--FX-Orange, #ff8000);
            border-radius: 1px;
            transform: translateY(4px);
          }
        }
        .title-right {
          color: var(--color-info06, #407fff);
          display: flex;
          align-items: center;
          /deep/ .el-button--small{
            padding: 0 12px;
          }
        }
      }
      &.enroll-speail-item{
        padding-top: 0;
        position: relative;
      }
    }
    .enroll-item-setting {
      background-color: #f4f8ff;
    }
  }
}
</style>
