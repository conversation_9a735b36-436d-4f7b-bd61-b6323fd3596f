<template>
  <div class="marketing-sms-template-list">
    <div class="km-g-loading-mask" v-if="smsListLoading">
      <span class="loading"></span>
    </div>
    <div class="header">
      <div class="header-tips">
        {{ $t('marketing.commons.dxyxxygzsd_29802d') }}
        <a
          class="header-link"
          target="_blank"
          href="https://help.fxiaoke.com/2615/93d4/9188/a093/a12a"
          >{{ $t('marketing.commons.ljdxshbz_113ed8') }}</a
        >
      </div>
      <div style="margin-left: auto;">
        <fx-select
          v-model="reviewStatus"
          :options="reviewStatusList"
          @change="queryTemplate()"
          size="small"
          filterable
        ></fx-select>
        <fx-input
          size="small"
          class="search-input"
          :placeholder="$t('marketing.commons.qsrmbmc_8f21b9')"
          prefix-icon="el-icon-search"
          @change="searchKey"
          v-model="searchText"
        ></fx-input>
        <fx-button class="button" type="primary" size="small" @click="addSmsTemplate">{{
          $t('marketing.commons.xzdxmb_60c799')
        }}</fx-button>
      </div>
    </div>

    <v-table
      :columns="columns"
      :emptyText="$t('marketing.commons.zwsj_21efd8')"
      @custom:delete-action="handleDelete"
      @custom:edit-action="handleEdit"
      @click:text="handleClickText"
      :data="tableData"
      class="sms-template-manage-table"
    ></v-table>
    <v-pagen @change="handlePageChange" :pagedata.sync="pageData"></v-pagen>
  </div>
</template>

<script>
import util from '@/services/util/index';
import VPagen from '@/components/kitty/pagen';
import VTable from '@/components/table-ex';

import http from '@/services/http/index';
import insertVariable from './insert-variable';

const statusTexts = {
  0: $t('marketing.commons.qryx_7ad925'),
  1: $t('marketing.commons.shz_b720a6'),
  2: $t('marketing.commons.qrwx_946581'),
  3: $t('marketing.pages.sms_marketing.yc_dce537'),
  4: $t('marketing.commons.jy_710ad0'),
  5: $t('marketing.commons.mbbcz_246b57'),
  6: $t('marketing.commons.ygq_4d5ccd'),
  7: $t('marketing.commons.sc_2f4aad'),
  8: $t('marketing.pages.sms_marketing.shsb_fe3661'),
  9: $t('marketing.pages.sms_marketing.gxzcshz_51e8f5'),
};

export default {
  components: {
    VPagen,
    VTable
  },
  props: {
    currentPlatform: {
      type: Object,
      default: ()=>({})
    }
  },
  data() {
    return {
      searchText: '',
      columns: [],
      tableData: [],
      pageData: {
        layout: 'prev, pager, next, total, sizes, jumper',
        pageNum: 1,
        totalCount: 10,
        pageSize: 10,
        pageSizes: [10, 20, 30, 40],
      },
      params: {
        pageNum: 1,
        pageSize: 10,
        keywords: '',
      },
      smsListLoading: false,
      reviewStatus: '',
      reviewStatusList:[
        {
          value: '',
          label: $t("marketing.commons.qb_a8b0c2")
        },
        {
          value: ['APPROVED'],
          label: $t("marketing.commons.shtg_871a30")
        },
        {
          value: ['NO_REPLY','APPROVING'],
          label: $t("marketing.commons.shz_b720a6")
        },
        {
          value: ['REJECTED','CANCELED'],
          label: $t("marketing.commons.shwtg_f50e9d")
        }
      ]
    };
  },
  mounted() {
    this.queryTemplate();
  },
  watch: {
    currentPlatform: {
      handler(val){
        // 切换平台时，重置分页
        this.pageData.pageNum = 1;
        this.queryTemplate();
      }
    },
    deep: true
  },
  methods: {
    getcolumns() {
      // const defaultColumns = [
      //   {
      //     prop: 'nameText',
      //     label: $t('marketing.commons.mbmc_a5d1c5'), // 模板名称
      //     exComponent: 'text',
      //   },
      //   {
      //     prop: 'contentHtml',
      //     label: $t('marketing.pages.sms_marketing.dxmbnr_067743'), // 短信模板内容
      //     minWidth: 300,
      //     exComponent: 'text',
      //   },
      //   {
      //     prop: 'channelName',
      //     label: $t('marketing.pages.sms_marketing.syf_9c919c'), // 使用方
      //   },
      //   {
      //     prop: 'tplTypeTxt',
      //     label: $t('marketing.pages.sms_marketing.mblx_bfd708'), // 模板类型
      //   },
      //   {
      //     prop: 'remarkTxt',
      //     label: $t('marketing.commons.mbcj_4d3865'), // 模板场景
      //   },
      //   {
      //     prop: 'createTimeTxt',
      //     label: $t('marketing.commons.cjsj_eca37c'), // 创建时间
      //     minWidth: 120,
      //   },
      //   {
      //     prop: 'statusTxt',
      //     label: $t('marketing.commons.shzt_b6d0e9'), // 审核状态
      //   },
      //   {
      //     prop: 'relationApiTxt',
      //     label: $t('marketing.pages.sms_marketing.gldx_999e85'), // 关联对象
      //   },
      // ];
      const thirdPlatformColumns = [
        {
          prop: 'nameText',
          label: $t('marketing.commons.mbmc_a5d1c5'), // 模板名称
          exComponent: 'text',
        },
        {
          prop: 'contentHtml',
          label: $t('marketing.pages.sms_marketing.dxmbnr_067743'), // 短信模板内容
          minWidth: 300,
          exComponent: 'text',
        },
        {
          prop: 'tplTypeTxt',
          label: $t('marketing.pages.sms_marketing.mblx_bfd708'), // 模板类型
        },
        {
          prop: 'remarkTxt',
          label: $t('marketing.commons.mbcj_4d3865'), // 模板场景
        },
        {
          prop: 'createTimeTxt',
          label: $t('marketing.commons.cjsj_eca37c'), // 创建时间
          minWidth: 120,
        },
        {
          prop: 'statusTxt',
          label: $t('marketing.commons.shzt_b6d0e9'), // 审核状态
        },
        {
          prop: 'reply',
          label: $t('marketing.commons.shyj_ab1bcd')
        },
        {
          prop: 'relationApiTxt',
          label: $t('marketing.pages.sms_marketing.gldx_999e85'), // 关联对象
        },
      ]
      const columns = thirdPlatformColumns;
      return columns;
    },
    findObjectListInfo() {
      if (this.fetchObjectInfoDic) return this.fetchObjectInfoDic;
      let resolve;
      window.CRM.util.FHHApi({
        url: '/EM1HNCRM/API/v1/object/describe/service/findDescribeManageList',
        success: (res) => {
          if(res.Value){
            const objectDescribeMap = (res.Value.objectDescribeList || []).reduce((map, item) => {
              map[item.api_name] = item.display_name;
              return map;
            }, {})
            resolve(objectDescribeMap);
          } else {
            resolve({});
          }
        },
      });
      this.fetchObjectInfoDic = new Promise((rez) => (resolve = rez));
      return this.fetchObjectInfoDic;
    },
    queryTemplate() {
      this.smsListLoading = true
      this.params.pageNum = this.pageData.pageNum;
      this.params.pageSize = this.pageData.pageSize;
      const providerId = this.currentPlatform.id || 'mw'
      http.queryThirdListSmsTemplate({
        ...this.params,
        providerId,
        statusList: this.reviewStatus || []
      }).then((ret) => {
        if (ret && ret.errCode === 0) {
          const data = ret.data || {};
          this.pageData.totalCount = data.totalCount;
          this.columns = this.getcolumns();
          this.setThirdSmstplList(data.result)
          this.fetchTableData().then((list) => {
            this.tableData = list
            this.smsListLoading = false
          });
        }
      });
    },
    setSmstplList(tplList) {
      this.tplList = (tplList || []).map((item) => {
        return Object.assign({}, item, {
          nameText: { text: item.name || '--', click: !!item.name },
          tplTypeTxt: [
            $t('marketing.pages.sms_marketing.ywtzdx_f640ba'), // 业务通知短信
            $t('marketing.pages.sms_marketing.yxdx_4ca661'), // 营销短信
          ][item.tplType],
          statusTxt: statusTexts[item.status],
          createTimeTxt: util.formatDateTime(item.createTime, 'YYYY-MM-DD hh:mm'),
          remarkTxt: item.remark || '--',
          reply: item.reply || '--',
          operations: [
            {
              id: 'edit',
              name: $t('marketing.commons.bj_95b351'),
              disabled: item.channelType === 6,
            },
            { id: 'delete', name: $t('marketing.commons.sc_2f4aad') },
          ],
        });
      });
    },
    setThirdSmstplList(tplList) {
      const tplTypeTxtMap = {
        'NOTIFICATION': $t('marketing.pages.promotion_activity.tzdx_5bd128'),
        'PROMOTION': $t('marketing.pages.sms_marketing.yxdx_4ca661'),
        'VERIFY_CODE': $t('marketing.pages.form.yzm_983f59'),
        'INTERNATIONAL': $t('marketing.pages.sms_marketing.gjgatxx_3b76bb')
      }
      const statusMap = {
        'NO_REPLY': $t('marketing.commons.shz_b720a6'),
        'APPROVING': $t('marketing.commons.shz_b720a6'),
        'APPROVED': $t('marketing.commons.shtg_871a30'),
        'REJECTED': $t('marketing.commons.shwtg_f50e9d'),
        'CANCELED': $t('marketing.pages.sms_marketing.shwtg_f50e9d')
      }
      this.tplList = (tplList || []).map((item) => {
        return Object.assign({}, item, {
          nameText: { text: item.name || '--', click: !!item.name },
          tplTypeTxt: tplTypeTxtMap[item.tplType] || '--',
          statusTxt: statusMap[item.status],
          createTimeTxt: util.formatDateTime(item.createTime, 'YYYY-MM-DD hh:mm'),
          remarkTxt: item.remark || '--',
        });
      });
    },
    fetchTableData() {
      let resolve;
      let richContents = 0;
      Promise.all([this.findObjectListInfo(), insertVariable.requireDeps()]).then(([objDic]) => {
        const plist = this.tplList.map((smsTpl) => wrapList(smsTpl, objDic));
        Promise.all(plist).then(resolve);
      });
      function wrapList(smsTpl, objDic) {
        const objApiName = smsTpl.relationApiName;
        smsTpl.relationApiTxt = objDic[objApiName] || objApiName || '--';
        return insertVariable.convertTplVars(smsTpl).then(() => {
          richContents++;
          smsTpl.contentHtml = {
            text: smsTpl.contentHtml,
            ishtml: !!smsTpl.relationApiName,
          };
          return smsTpl;
        });
      }
      setTimeout(() => {
        // 防止富文本解析PAAS字段卡死导致整个列表不显示
        if (richContents < this.tplList.length) resolve(this.tplList);
      }, 6666);
      return new Promise((rez) => (resolve = rez));
    },
    searchKey(value) {
      this.pageData.pageNum = 1;
      this.params.keywords = value; 
      this.queryTemplate();
    },
    handlePageChange(data) {
      this.pageData.pageNum = data.pageNum;
      this.pageData.pageSize = data.pageSize;
      this.queryTemplate();
    },
    handleDelete(data) {
      http.deleteTemplate({ templateId: data.id }).then((res) => {
        if (res && res.errCode == 0) {
          FxUI.Message.success($t('marketing.commons.sccg_43593d'));
          this.queryTemplate();
        }
      });
    },
    addSmsTemplate() {
      this.$emit('addSmsTemplate');
    },
    handleClickText(propName, rowData) {
      if (propName === 'nameText') this.$emit('edit-action', rowData);
    },
    handleEdit(rowData, index) {
      this.$emit('edit-action', rowData);
    },
  },
  smsTplVars: {
    convertTplVars: insertVariable.convertTplVars,
    listSmsTemplate,
    getSmsTemplate,
  },
};

function listSmsTemplate(reqData) {
  const demoReq = {
    apiName: '', // 对象apiName
    filter: '', // 模板名称关键字
  };
  return http.sendListSmsTemplateWithoutPage(reqData).then((res) => {
    return res;
  });
}

function getSmsTemplate(reqData) {
  const demoReq = {
    templateId: '', // 模板id
  };
  return http.sendGetSmsTemplateDetail(reqData).then((res) => {
    return res;
  });
}
</script>

<style lang="less" scoped>
.marketing-sms-template-list{
  position: relative;
  .search-input{
    margin-left: 20px;
  }
}
</style>
