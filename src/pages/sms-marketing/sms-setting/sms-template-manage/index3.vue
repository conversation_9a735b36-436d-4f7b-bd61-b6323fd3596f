<template>
  <div class="marketing-sms-template-manage">
    <smstpl-list
      ref="smstplList"
      :currentPlatform="currentPlatform"
      @addSmsTemplate="addSmsTemplate"
      @edit-action="handleEdit"
    ></smstpl-list>

    <el-dialog
      :title="templateDialogTitle"
      :visible="showTemplateDialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="closeTemplateDialog"
      append-to-body
      width="1000px"
      class="add-template-dialog add-template-dialog3"
    >
      <add-template-dialog
        v-if="showTemplateDialog"
        ref="addTemplateForm"
        :currentPlatform="currentPlatform"
        :style="tplDialogMode === 'edit' ? 'pointer-events: none;' : ''"
        :tplFormData="tplFormData"
      >
      </add-template-dialog>

      <div slot="footer" class="dialog-footer">
        <fx-button v-show="tplDialogMode !== 'edit'" type="primary" @click="toConfirm">{{
          $t('marketing.commons.qd_aa7527')
        }}</fx-button>
        <fx-button @click="closeTemplateDialog">{{ $t('marketing.commons.qx_c08ab9') }}</fx-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import http from '@/services/http/index';
import smstplList from './smstpl-list';
import addTemplateDialog from './add-template-dialog';

export default {
  props: {
    currentPlatform: {
      type: Object,
      default: ()=>({})
    }
  },
  components: {
    elDialog: FxUI.Dialog,
    smstplList,
    addTemplateDialog
  },
  data() {
    return {
      tplFormData: {},
      showTemplateDialog: false,
      templateDialogTitle: '',
      tplDialogMode: '',
    };
  },
  mounted() {},
  methods: {
    closeTemplateDialog() {
      this.showTemplateDialog = false;
      this.tplFormData = {};
    },
    addSmsTemplate() {
      this.tplFormData = {};
      this.templateDialogTitle = $t('marketing.commons.xzdxmb_60c799');
      this.tplDialogMode = 'add';
      this.showTemplateDialog = true;
    },
    handleEdit(rowData) {
      this.templateDialogTitle = $t('marketing.pages.sms_marketing.bjdxmb_7cd459'); // 编辑短信模板
      this.templateDialogTitle = $t('marketing.commons.dxmb_dbe8ba'); // 短信模板
      this.tplDialogMode = 'edit';
      this.tplFormData = rowData;
      this.showTemplateDialog = true;
    },
    toConfirm() {
      this.$refs['addTemplateForm'].validate((valid) => {
        const valueData = this.$refs['addTemplateForm'].getValue()
        if (valid) {
          this.saveToeditSmsTemplate(valueData);
          this.closeTemplateDialog();
        } else {
          return false;
        }
      });
    },
    saveToeditSmsTemplate(valueData) {
      const smsContentParam = valueData.varList.map((item) => {
        item = Object.assign({}, item);
        item.key = item.id;
        delete item.id;
        delete item.label;
        delete item.varid;
        return item;
      });
      const formatVariableAttributes = this.formatVariableAttributes(valueData)
      const reqData = {
        id: valueData.id || undefined,
        name: valueData.name,
        content: valueData.content,
        remark: valueData.remark,
        relationApiName: valueData.relationApiName, // 关联对象
        smsContentParam, // 短信内容参数列表
        tplType: valueData.tplType,
        channelType: 99,
        providerId: this.currentPlatform.id || 'mw',
        variableAttributes: formatVariableAttributes
      };
      http.createThirdSmsTemplate(reqData).then((res) => {
        if (res && res.errCode == 0) {
          FxUI.Message.success($t('marketing.commons.czcg_33130f'));
          this.$refs.smstplList.queryTemplate();
        } else {
          FxUI.Message.error(res.errMsg || $t('marketing.commons.czsb_5fa802'));
        }
      });
    },
    formatVariableAttributes(valueData) {
      const {variableAttributes = [],content} = valueData
      
      // 从 content 中提取所有变量
      const variableRegex = /\$\{([^}]+)\}/g;
      const contentVariables = [];
      let match;
      
      while ((match = variableRegex.exec(content)) !== null) {
        contentVariables.push(match[1]);
      }
      
      // 创建结果数组
      const result = [];
      
      // 处理每个 content 中的变量
      contentVariables.forEach(varName => {
        // 检查是否是 .##var 开头的变量
        if (varName.startsWith('.##var')) {
          const cleanVarName = varName.replace(/^\.##/, '');
          
          // 查找是否已有对应的映射
          const existingAttr = variableAttributes.find(attr => attr.name === cleanVarName);
          
          if (existingAttr) {
            result.push(existingAttr);
          } else {
            result.push({
              name: cleanVarName,
              type: 'link_param'
            });
          }
        } else {
          // 查找是否已有对应的映射
          const existingAttr = variableAttributes.find(attr => attr.name === varName);
          
          if (existingAttr) {
            result.push(existingAttr);
          } else {
            result.push({
              name: varName,
              type: 'others'
            });
          }
        }
      });
      
      return result;
    }
  },
};
</script>

<style lang="less"></style>
