<template>
  <fx-form
    :model="editFormData"
    ref="addTemplateForm"
    :rules="addTemplateFormRules"
    class="add-template-form-wrapper"
    label-width="130px"
    label-position="left"
  >
  <fx-form-item 
    :label="$t('marketing.commons.mblx_bfd708')"
    :data-label="$t('marketing.commons.mblx_bfd708')"
    prop="tplType"
    v-if="!!isThirdSmsProvider"
  >
    <div class="sms-template-type__list">
      <div
        v-for="item in templateList" 
        :key="item.tplType"
        :class="['sms-template-item', editFormData.tplType === item.tplType && 'selected', item.disabled && 'gray']"
        @click="handleChooseTypes(item)"
      >
        <p class="template-title">{{ item.title }}</p>
        <p class="template-des">{{ item.des }}</p>
      </div>
    </div>
  </fx-form-item>

  <fx-input
    :label="$t('marketing.commons.mbmc_a5d1c5')"
    :data-label="$t('marketing.commons.mbmc_a5d1c5')"
    :placeholder="$t('marketing.commons.qsrmbmc_8f21b9')"
    prop="name"
    v-model="editFormData.name"
    :maxlength="50"
  ></fx-input>

  <fx-input
    v-model="editFormData.remark"
    :maxlength="150"
    :label="$t('marketing.commons.mbcj_4d3865')"
    :data-label="$t('marketing.commons.mbcj_4d3865')"
    prop="remark"
    :placeholder="$t('marketing.commons.jygkxsmywz_31f8b3')"
  ></fx-input>

  <fx-form-item
    v-show="!editFormData.statusTxt && !isThirdSmsProvider"
    :label="$t('marketing.commons.dxqm_f32c04')"
    :data-label="$t('marketing.commons.dxqm_f32c04')"
  >
    <span>{{ signatureTxt }}</span>
  </fx-form-item>

  <fx-form-item
    v-show="editFormData.statusTxt"
    :label="$t('marketing.commons.shzt_b6d0e9')"
    :data-label="$t('marketing.commons.shzt_b6d0e9')"
  >
    <span style="color: #999;">{{ editFormData.statusTxt }}</span>
  </fx-form-item>

  <fx-select
    v-model="editFormData.relationApiName"
    :disabled="!!editFormData.id"
    filterable
    style="width: 100%;"
    :options="objectListInfo"
    :label="$t('marketing.pages.sms_marketing.gldx_999e85')"
    :data-label="$t('marketing.commons.gldx_999e85')"
    prop="relationApiName"
  >
  </fx-select>

  <fx-form-item
    :label="$t('marketing.pages.mail_marketing.mbnr_7bcd31')"
    :data-label="$t('marketing.commons.mbnr_03ae79')"
    prop="content"
  >
    <div class="smstpl-box">
      <div class="smstpl-toptip">
        <p>
          <span>{{ !!isThirdSmsProvider ? $t('marketing.pages.sms_marketing.tzldxwxshj_eddf2d') : $t('marketing.pages.sms_marketing.dxxyyysshh_ebe0ce') }}</span>
          <a style="margin-left: 20px;line-height: 1.5;float: right;" href="https://help.fxiaoke.com/93d5/9188/a093/a12a">{{ $t('marketing.pages.sms_marketing.ckshgz_966484') }}</a>
        </p>
      </div>

      <div class="smstpl-toolbox">
        <span>
          <i
            class="el-icon-link"
            :title="$t('marketing.commons.crdlj_d48017')"
            :data-title="$t('marketing.commons.crdlj_d48017')"
            @click="showCreateShortUrlDialog = true"
          ></i>
        </span>
        <span>
          <i
            class="fx-icon-obj-serviceprojectobj"
            :title="$t('marketing.commons.crdxtzlj_3b83f8')"
            :data-title="$t('marketing.commons.crdxtzlj_3b83f8')"
            @click="showCreateH5objUrlDialog = true"
          ></i>
        </span>
        <span>
          <i
            class="fx-icon-mgt-mp_miniprogram_new"
            :title="$t('marketing.commons.crxcxlj_9b6e91')"
            :data-title="$t('marketing.commons.crxcxlj_9b6e91')"
            @click="showCreateMinipUrlDialog = true"
          ></i>
        </span>
        <span>
          <a :data-title="$t('marketing.commons.crbl_b3e42b')" @click="smsParamsDialogVisible = true">
            {{ $t('marketing.commons.crbl_b3e42b') }}
          </a>
        </span>
      </div>
      <div class="sms-content-rtxholder"></div>
    </div>

    <div class="sms-content__tips" v-if="!isThirdSmsProvider">
      <div>
        {{ $t('marketing.commons.dxnrmrhjsq_8623fe', {data: ({'option0': signatureTxt})}) }}
      </div>
    </div>
  </fx-form-item>

    <create-short-url-dialog
      v-if="showCreateShortUrlDialog"
      :visible="showCreateShortUrlDialog"
      :vtexts="shortUrlVtexts"
      @update:visible="showCreateShortUrlDialog = false"
      @upadete:submit="handleCreateShortUrl"
    ></create-short-url-dialog>

    <create-h5obj-url-dialog
      v-if="showCreateH5objUrlDialog"
      :visible="showCreateH5objUrlDialog"
      :editFormData="editFormData"
      @update:visible="showCreateH5objUrlDialog = false"
      @upadete:submit="handleCreateH5urlUrl"
    ></create-h5obj-url-dialog>

    <create-minip-url-dialog
      v-if="showCreateMinipUrlDialog"
      :visible="showCreateMinipUrlDialog"
      :editFormData="editFormData"
      @update:visible="showCreateMinipUrlDialog = false"
      @upadete:submit="handleCreateMinipUrl"
    ></create-minip-url-dialog>
    <sms-params-dialog
      v-if="editFormData.relationApiName"
      :visible="smsParamsDialogVisible"
      :editFormData="editFormData"
      :currentPlatform="currentPlatform"
      @update:visible="smsParamsDialogVisible = false"
      @updateRichText="handleUpdateRichText"
      @update:submit="handleSmsParamsInsert"
      ></sms-params-dialog>
  </fx-form>
</template>

<script>
import http from '@/services/http/index';
import QuestionTooltip from '@/components/questionTooltip/index';
import createShortUrlDialog from '@/components/create-short-url/index';
import createH5objUrlDialog from './create-h5obj-url';
import createMinipUrlDialog from './create-minip-url';
import insertVariable from './insert-variable';
import smsParamsDialog from './sms-params-dialog.vue'
import SMSTemplateTools from '@/pages/promotion-activity/sms/components/utils.js'


function getEditFormData(data) {
  data = Object.assign(
    {
      id: undefined,
      tplType: 'NOTIFICATION', // 模板类型 0业务通知短信 1营销短信
      channelType: 99, // 99表示通用模板
      name: '',
      content: '', // 文本化的模板内容
      remark: '', // 场景描述
      relationApiName: '', // 关联对象
      statusTxt: '', // 审核状态文案
      smsContentParam: [], // 短信内容参数列表
    },
    data
  );
  const smsContentParam = data.smsContentParam || [];
  data.smsContentParam = smsContentParam.slice();
  return data;
}

export default {
  components: {
    elDialog: FxUI.Dialog,
    elRow: FxUI.Row,
    elCol: FxUI.Col,
    elForm: FxUI.Form,
    elFormItem: FxUI.FormItem,
    elSelect: FxUI.Select.components.ElSelect,
    elOption: FxUI.Select.components.ElSelect.components.ElOption,
    elRadioGroup: FxUI.RadioGroup,
    elRadio: FxUI.Radio,
    createShortUrlDialog,
    createH5objUrlDialog,
    createMinipUrlDialog,
    QuestionTooltip,
    smsParamsDialog
    },
  props: {
    tplFormData: Object,
    currentPlatform: {
      type: Object,
      default: ()=>({})
    }
  },
  data() {
    var contnetValidator = (rule, value, callback) => {
      value = this.contentRichText.get('value');
      if (!value) {
        callback(new Error($t('marketing.pages.sms_marketing.qsrdxnr_56ad80')));
      } else {
        callback();
      }
    };
    return {
      objectListInfo: [],
      editFormData: getEditFormData(this.tplFormData),
      showCreateShortUrlDialog: false,
      shortUrlVtexts: {
        title: $t('marketing.commons.crdlj_d48017'), // 插入短链接
        submit: $t('marketing.commons.scbbc_c3ffb5'), // 生成并保存
      },
      showCreateH5objUrlDialog: false,
      showCreateMinipUrlDialog: false,
      tempVarList: getEditFormData(this.tplFormData).smsContentParam,
      signatureTxt: '',
      addTemplateFormRules: {
        name: {
          required: true,
          message: $t('marketing.commons.qsrmbmc_8f21b9'),
          trigger: 'blur',
        },
        relationApiName: { required: true },
        content: [{ required: true, validator: contnetValidator, trigger: 'blur' }],
        remark: [{ required: true, message: $t('marketing.pages.sms_marketing.qxzmbcj_9481a4'), trigger: 'blur' }],
        tplType: [{ required: true}],
      },
      smsParamsDialogVisible: false,
      variableType: 0,
      customParam: '',
      variableAttributes: []
    };
  },
  watch: {
    tplFormData() {
      this.editFormData = getEditFormData(this.tplFormData);
      this.tempVarList = this.editFormData.smsContentParam;
    },
  },
  computed: {
    isThirdSmsProvider(){
      return this.currentPlatform.id !== 'mw'
    },
    templateList(){
      const platform = this.currentPlatform.currentPlatformId
      const data = SMSTemplateTools.getSmsTplTypeList(platform)
       if(platform !== 'mw'){
        data.splice(data.length-1,0,{
          tplType: 'VERIFY_CODE', 
          title: $t('marketing.commons.yzm_983f59'),
          des: $t('marketing.pages.sms_marketing.yzmdxyydlz_3974db')
        })
      }
      return data
    },
  },
  created() {
    this.loadSignature();
    this.findObjectListInfo();
  },
  destroyed() {
    if (this.contentRichText) {
      this.contentRichText.destroy();
      this.contentRichText = null;
    }
  },
  methods: {
    findObjectListInfo() {
      window.CRM.util.FHHApi({
        url: '/EM1HNCRM/API/v1/object/describe/service/findDescribeManageList',
        data: { 
          "isIncludeFieldDescribe": true,
          "isIncludeSystemObj": true,
          "isIncludeUnActived": false,
          "includeBigObject": true,
          "includeSocialObject": false,
          "includeChangeOrderObject": false,
          "packageName": "CRM",
          "sourceInfo": "object_management"
        },
        success: (res) => {
          if(res.Result.StatusCode === 0){
            const objectLists = res.Value.objectDescribeList || [];
            let relationApiName = this.editFormData.relationApiName;
            this.objectListInfo = objectLists.map((item) => {
              return { value: item.api_name, label: item.display_name };
            });
            if (this.objectListInfo[0] && !relationApiName) {
              relationApiName = this.objectListInfo[0].value;
              this.editFormData.relationApiName = relationApiName;
            }
          }
        },
      });
    },
    handleCreateShortUrl(shortUrl) {
      const data1 = insertVariable.createExVar({
        type: 'surl',
        value: shortUrl,
      });
      this.tempVarList.push(data1);
      this.contentRichText.insertVariable(data1);
    },
    handleCreateH5urlUrl(data0) {
      const params = {
        fsAppId: data0.fsAppId,
        upstreamEa: window.FS.contacts.getCurrentEmployee().enterpriseAccount,
        apiName: this.editFormData.relationApiName,
        dataId: '${dataId}',
        domain: data0.enterpriseDomain
      };
      let h5url = insertVariable.genH5detailUrl(params);
      h5url = h5url.replace('%2524%257BdataId%257D', '${dataId}');
      console.log('replaceh5url: ', h5url);
      const data1 = insertVariable.createExVar({
        type: 'h5obj',
        label: $t('marketing.commons.dxtzlj_909f37'),
        value: h5url,
      });
      this.tempVarList.push(data1);
      this.contentRichText.insertVariable(data1);
    },
    handleCreateMinipUrl(data0) {
      const data1 = insertVariable.createExVar({
        type: 'minip',
        label: data0.name || $t('marketing.commons.xcxlj_00b3e3'),
        value: JSON.stringify({
          fsPlatformId: data0.fsPlatformId,
          wxAppId: data0.wxAppId
        }),

        // fsPlatformId: data0.fsPlatformId, // 营销通：YXT；服务通：FWT；订货通：DHT
        // wxAppId: data0.wxAppId,
        // jumpWxa: {
        //   path: data0.path,
        //   query: data0.query,
        //   envVersion: 'release',
        // },
      });
      this.tempVarList.push(data1);
      this.contentRichText.insertVariable(data1);
    },
    validate(func) {
      return this.$refs['addTemplateForm'].validate((valid) => {
        if (!valid) return func(valid);
        let content = this.contentRichText.get('value');
        const hasVar = /\$\{[^${}]+?\}/.test(content);
        if (hasVar) content = content.replace(/\$\{[^${}]+?\}/g, '');
        if (content.length > 350) {
          valid = false;
          FxUI.Message.error($t('marketing.pages.sms_marketing.dxzsbncgz_882ccd'));
          func(valid);
        } else if (hasVar && content.length > 150) {
          FxUI.MessageBox.alert($t('marketing.pages.sms_marketing.xxzdyblqzy_dd0ab4'), {
            callback: () => {
              func(valid);
            },
          });
        } else {
          func(valid);
        }
      });
    },
    getValue() {
      const tempContent = this.contentRichText.get('value');
      console.log('getValue', tempContent)
      const valueData = insertVariable.formatExVars(tempContent, this.tempVarList); // { varList, content }
      console.log('tempContenttempContent', valueData)
      return Object.assign({}, this.editFormData, valueData, {
        variableAttributes: this.variableAttributes
      });
    },
    loadSignature() {
      http.querySignature().then((res) => {
        if ((res && res.errCode == 60004) || (res.errCode == 0 && res.data.status != 0)) {
          http.queryTrial().then((res) => {
            if (res.data && res.data.signature) this.signatureTxt = res.data.signature;
          });
        } else if (res && res.errCode === 0) {
          this.signatureTxt = res.data.signature;
        } else {
          FxUI.Message.error(res.errMsg || $t('marketing.commons.qmhqsbqshz_fc313a'));
        }
      });
    },
    handleSmsParamsInsert(data){
      if(Object.keys(data).length === 0) return
      if(this.editFormData.tplType !== 'INTERNATIONAL'){
        this.variableAttributes.push({
          name: data.id.startsWith('customVariables_') ? data.label.replace(/^\${(.*)}$/, '$1') : data.id,
          type: data.smsVariableType
        });
      }
      delete data.smsVariableType
      console.log('handleSmsParamsInsert', this.variableAttributes)
      console.log('handleSmsParamsInsert', data)
      this.tempVarList.push(data);
      this.contentRichText.insertVariable(data);
    },
    handleUpdateRichText(contentRichText){
      if (this.contentRichText) this.contentRichText.destroy();
      $(".sms-content-rtxholder").append(contentRichText.$el);
      this.contentRichText = contentRichText;
    },
    handleChooseTypes(item){
      if(item.disabled) return
      this.editFormData.tplType = item.tplType
    },
  },
};
</script>

<style lang="less" scoped>
.add-template-dialog3 {
  .add-template-form-wrapper {
    .smstpl-box {
      border: 1px solid #dddddd;
    }
    .smstpl-toptip {
      background: #fef3eb;
      color: #a0a0a0;
      padding: 10px;
      line-height: 1.5;
    }
    .smstpl-toolbox {
      text-align: right;
      padding: 0 20px;
      border-bottom: 1px solid #f0f0f0;
      [class^='fx-icon-'],
      [class^='el-icon-'] {
        margin: 0 3px;
        font-size: 20px;
        cursor: pointer;
      }
      a {
        cursor: pointer;
      }
    }
    .sms-content-varholder {
      position: relative;
    }
    .sms-content-rtxholder {
      overflow: auto;
      height: 160px;
      padding: 10px;
      font-size: 16px;
      line-height: 1.8;
    }
    .flow-richtext {
      height: 100%;
    }
    .paas-flow-variables {
      top: -35px;
      right: -5px;
    }
    .el-radio {
      line-height: 30px;
    }
    .sms-template-type__list{
      display: flex;
      gap: 20px;
    .sms-template-item{
      border: 1px solid #E9EDF5;
      border-radius: 4px;
      flex: 1;
      padding: 12px;
      position: relative;
      font-size: 14px;
      cursor: pointer;
      min-width: 0;
      &.selected {
        border: 1px solid var(--color-primary06,#ff8000);
        &::before {
          content: '\00a0';
          display: inline-block;
          border: 2px solid #fff;
          border-top-width: 0;
          border-right-width: 0;
          width: 9px;
          height: 5px;
          transform: rotate(-50deg);
          position: absolute;
          top: 2px;
          left: 2px;
          z-index: 3;
        }
        &::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 0;
          height: 0;
          border-top: 25px solid var(--color-primary06,#407FFF);
          border-right: 25px solid transparent;
        }
      }
      &.gray{
        background: #F7F8FA;
        border: 1px solid #E9EDF5;
        cursor: not-allowed;
        &::after {
          border-top: 25px solid #C1C5CE;
        }
      }
      .template-title{
        color: #181C25;
        font-weight: bold;
        line-height: 20px;
      }
      .template-des{
        margin-top: 6px;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 18px;
        color: #91959E;
      }
    }
    }
  }
}
</style>
