let paasWeEditor;
let paasflowUtils;
let paasVariables;
let paasRichtext;
let paasRtHelper;
let rdPromise;

import smstplRichtext from './smstpl-richtext';

function requireDeps() {
  if (rdPromise) return rdPromise;
  const hbld = seajs.resolve('paas-paasui/vui').match(/\/vui-\w+\.js/);
  const srcDeps = [
    'paas-paasui-modules/actions/weeditor/weeditor',
    'paas-paasui-modules/utils/utils',
    'paas-paasui-modules/variables/variables',
    'paas-paasui-modules/field/field',
    'paas-paasui-modules/field/richtext/helper',
    'paas-paasui/assets/style/all.css',
  ];
  const bldDeps = ['Field', 'Actions', 'Variables'];
  const bldSdep = [
    'paas-paasui-modules/actions/weeditor/weeditor',
    'paas-paasui-modules/field/richtext/helper',
  ];
  let resolve;
  rdPromise = new Promise((rez) => (resolve = rez));

  function cdeps(WeEditor, flowUtils, Variables, Richtext, rtHelper) {
    paasWeEditor = WeEditor;
    paasflowUtils = flowUtils;
    paasVariables = Variables;
    paasRichtext = Richtext;
    paasRtHelper = rtHelper;
    resolve({ WeEditor, flowUtils, Variables, Richtext, rtHelper });
  }

  if (hbld) {
    window.seajs.use('paas-paasui/vui', () => {
      const flowUtils = window.PaasUI.utils;
      window.PaasUI.getComponents(bldDeps).then(([Field, Actions, Variables]) => {
        window.seajs.use(bldSdep, (WeEditor, rtHelper) => {
          cdeps(WeEditor, flowUtils, Variables, Field.richtext, rtHelper);
        });
      });
    });
  } else {
    window.seajs.use(srcDeps, (WeEditor, flowUtils, Variables, fieldField, rtHelper) => {
      cdeps(WeEditor, flowUtils, Variables, fieldField.richtext, rtHelper);
    });
  }
  return rdPromise;
}

function fetchVariablesData(entityId, fromModule, fromApp) {
  let resolve;
  const cusVariablesData = paasflowUtils.vars.variables.map((variable) =>
    window.$.extend(true, {}, variable)
  );
  paasflowUtils.api.fetchDescribeExtraPersonnelObj({
    apiName: entityId,
    success(data) {
      paasflowUtils
        .getRemindFields(data, entityId, fromModule || 'message', fromApp || 'bpm')
        .then(function(fields) {
          cusVariablesData.forEach((item) => {
            if (item.name === 'object') item.variables = fields;
          });
          resolve(cusVariablesData);
        });
    },
  });
  return new Promise((rez) => (resolve = rez));
}

function fetchRichValue(content, objApiName, fromApp) {
  const rtValue = Object.assign(
    {
      fromApp: fromApp || 'bpm',
      objApiName: objApiName || 'AccountObj',
      placeholder: $t('marketing.commons.qsr_02cc4f'),
      content: '',
    },
    content
  );
  if (!rtValue.content) rtValue.content = ' '; // fix replaceRichTextV2
  return paasflowUtils.replaceRichTextV2(rtValue).then((data) => {
    return Object.assign(rtValue, {
      richValue: data.content.replace(/^ $/, ''),
      value: rtValue.content.replace(/^ $/, ''),
      content: rtValue.content.replace(/^ $/, ''),
    });
  });
}

function createRichText(opts, Richtext) {
  Richtext = Richtext || paasRichtext;
  const defOpts = {
    value: '',
    richValue: '',
    placeholder: '',
    required: true,
  };
  let richText = new Richtext({
    model: new window.Backbone.Model(Object.assign(defOpts, opts)),
  });
  return richText;
}

function createSmstplRT(opts) {
  return createRichText(opts, smstplRichtext.extendRichtext(paasRichtext, paasRtHelper));
}

function createVariables(variablesData, opts) {
  const variablesView = new paasVariables({
    data: { hasCalc: false, items: variablesData },
    useSearch: 1,
  });
  variablesView.on('select', (data0) => {
    const data1 = { label: data0.label, id: data0.name };
    if (data1.label.slice(0, 2) !== '${') data1.label = '${' + data1.label + '}';
    opts.onSelect(data1);
    // 富文本标签格式
    // <span contenteditable="false" class="editor-var" data-varid="id">${label}</span>
    // 富文取值文本格式
    // 'ugc${id}'
    // 变量名id要满足下式
    // /data-varid="([\$\{\}\w\-\.#]+)"/g
    // 流程id定义示例​
    // ${workflowInstanceStartTime} ${流程发起时间}​
    // ${applicantName} ${流程发起人}
    // ${SalesOrderObj.field_pyok1__c} ​${销售订单.创建者}
    // ${SalesOrderObj.belong_to_supplier.is_enable} ​${销售订单.所属供应商.启用状态}
  });
  return variablesView;
}

function createExVar(data0) {
  let { label, value, type = 'var', varid } = data0;
  label = label || value;
  value = value || label;
  varid = varid || 'var' + ('' + Math.random()).slice(2);
  if (value.slice(0, 2) === '${') value = value.slice(2, -1);
  if (label.slice(0, 2) !== '${') label = '${' + label + '}';
  return { type, id: '.##' + varid, varid, value, label };
  // 扩展变量名格式
  // ${.##id}
  // 富文取值扩展变量示例
  // ${.##exvar5358979}
  // 核心是以.#号开头，以区分原有流程变量和PAAS对象变量，但整体上满足'ugc${id}'基础格式
  // .#后面第二个#号，是为了符合变量名校验规则，并考虑后续扩展
}

function formatExVars(tempContent, tempVarList) {
  const allVars = [];
  const varNames = {};
  const varList = [];
  const segments = tempContent.split(/\$\{[^${}]+?\}/);
  tempVarList = tempVarList.map((item) => Object.assign({}, item));

  let segIndex = 1;
  tempContent.replace(/\$\{([^${}]+?)\}/g, (m0, m1) => {
    const varInfo = tempVarList.find((item) => item.id === m1);
    const varItem = { segIndex, id: m1, varInfo };
    varNames[varItem.id] = varItem;
    allVars.push(varItem);
    segIndex += 2;
  });

  let varNumber = 0;
  allVars.forEach((varItem) => {
    // 自定义变量的处理方式
    if(varItem.id.startsWith('customVariables_') || varItem.id.startsWith('linkVariables_')){
      segments.splice(varItem.segIndex, 0, varItem.varInfo.label);
      return;
    }
    if (!varItem.varInfo || varItem.id.slice(0, 3) !== '.##') {
      segments.splice(varItem.segIndex, 0, '${' + varItem.id + '}');
      return;
    }
    let varid = 'var' + ++varNumber;
    while (varNames['.##' + varid]) {
      varid = 'var' + ++varNumber;
    }
    const varInfo = Object.assign({}, varItem.varInfo, { id: '.##' + varid, varid });
    varNames[varItem.id] = varInfo;
    varList.push(varInfo);
    segments.splice(varItem.segIndex, 0, '${' + varInfo.id + '}');
  });
  return { tempContent, tempVarList, varList, content: segments.join('') };
}

function formatEVhtml(htmlContent, varList,objApiName) {
  console.log('objApiNameobjApiName', htmlContent, varList,objApiName)
  let content = htmlContent
  const regex = /<span.*?data-varid="(.*?)".*?>(.*?)<\/span>/g;
  let match;
  while ((match = regex.exec(htmlContent)) !== null) {
    const text = match[0]
    const varId = match[1];
    const varInfo = varList.find((item) => item.id === varId) || {};
    let holderTxt = ''
    if (varInfo.type === 'surl') {
      holderTxt = varInfo.value;
      const htmlLabel = [
        '<span',
        ' class="editor-var"',
        ' data-varid="' + varInfo.id + '"',
        '>',
        '${' + holderTxt + '}',
        '</span>',
      ].join('');
      content = content.replace(text,htmlLabel)
    } else if (varInfo.type === 'h5obj') {
      holderTxt = $t('marketing.commons.dxtzlj_909f37');
      const htmlLabel = [
        '<span',
        ' class="editor-var"',
        ' data-varid="' + varInfo.id + '"',
        '>',
        '${' + holderTxt + '}',
        '</span>',
      ].join('');
      content = content.replace(text,htmlLabel)
    } else if (varInfo.type === 'minip') {
      holderTxt = $t('marketing.commons.xcxlj_00b3e3');
      const htmlLabel = [
        '<span',
        ' class="editor-var"',
        ' data-varid="' + varInfo.id + '"',
        '>',
        '${' + holderTxt + '}',
        '</span>',
      ].join('');
      content = content.replace(text,htmlLabel)
    } else if(!varId.startsWith(objApiName)){
      const htmlLabel = [
        '<span',
        ' class="editor-var"',
        ' data-varid="' + varId + '"',
        '>',
        '${' + varId + '}',
        '</span>',
      ].join('');
      content = content.replace(text,htmlLabel)
    }
  }
  return content
}

function fetchEVrichHtml(content, objApiName, smsContentParam) {
  return fetchRichValue(content, objApiName).then((rtValue) => {
    rtValue.richValue = formatEVhtml(rtValue.richValue, smsContentParam,objApiName);
    return rtValue;
  });
}

function convertTplVars(smsTpl) {
  const demoTpl = {
    id: '',
    tplType: 0, // 模板类型 0业务通知短信 1营销短信
    channelType: 99, // 99表示通用模板
    name: '',
    content: '', // 文本化的模板内容
    remark: '', // 场景描述
    relationApiName: '', // 关联对象
    status: 0,
    smsContentParam: [{ key: '.##var1', type: 'h5obj', value: 'https://' }], // 短信内容参数列表
    paramDetail: [{ name: '', value: '' }], // 旧模板格式用的变量
    type: 2,
    channelName: '',
    sceneType: 1,
  };

  const objApiName = smsTpl.relationApiName;
  const smsContentParam = smsTpl.smsContentParam || [];
  const paramDetail = smsTpl.paramDetail || [];
  if (!objApiName) {
    let content = smsTpl.content;
    paramDetail.forEach((varInfo) => {
      const showName = '${' + varInfo.value + '}';
      content = content.replace('{' + varInfo.name + '}', showName);
    });
    smsTpl.contentHtml = content;
    return Promise.resolve(smsTpl);
  }
  smsContentParam.forEach((varInfo) => (varInfo.id = varInfo.id || varInfo.key));
  return requireDeps()
    .then(() => fetchEVrichHtml({ content: smsTpl.content }, objApiName, smsContentParam))
    .then((rtValue) => {
      smsTpl.contentHtml = rtValue.richValue;
      return smsTpl;
    });
}

function genHlcrmUrl(querys) {
  const domain = querys.domain || window.location.origin
  const baseurl = domain + '/hlcrm/avah5';
  const defQuerys = {
    upstreamEa: '',
    fsAppId: '',
    apiName: '',
    dataId: '',
    _hash: '',
  };
  const params = Object.assign({}, defQuerys, querys);
  querys = {};
  Object.keys(defQuerys).forEach((name) => (querys[name] = params[name]));
  return genURL4querys(baseurl, querys);
}

function genLoginUrl(querys) {
  const domain = querys.domain || window.location.origin
  const baseurl = domain + '/fs-er-biz/er/auth/connect';
  const defQuerys = {
    authType: '',
    fsAppId: '',
    context: undefined,
    upstreamEa: undefined,
    isRedirect: '',
    resourceUrl: '',
  };
  const params = Object.assign({}, defQuerys, querys);
  querys = {};
  Object.keys(defQuerys).forEach((name) => (querys[name] = params[name]));
  return genURL4querys(baseurl, querys);
}

function genH5detailUrl(params) {
  const defParams = {
    authType: '13', // 授权方式，13 表示手机短信
    fsAppId: '', // 互联应用id
    context: undefined, // 微信公众号id
    isRedirect: 'true', // 登录成功后 302 跳到 resourceUrl
    upstreamEa: '',
    apiName: '',
    dataId: '',
    _hash: '/object_detail/object_detail/pages/detail/index',
  };
  params = Object.assign(defParams, params);
  const resourceurl = genHlcrmUrl(params);
  params.resourceUrl = resourceurl.href;
  const loginUrl = genLoginUrl(params);
  return loginUrl.href;
}

function genURL4querys(baseurl, querys) {
  const newurl = new window.URL(baseurl);
  Object.keys(querys).forEach((name) => {
    if (querys[name] === undefined || querys[name] === null) return;
    newurl.searchParams.append(name, querys[name]);
  });
  return newurl;
}

requireDeps();

export default {
  requireDeps,
  fetchVariablesData,
  fetchRichValue,
  fetchEVrichHtml,
  convertTplVars,
  createRichText,
  createSmstplRT,
  createVariables,
  createExVar,
  formatExVars,
  genH5detailUrl,
};
