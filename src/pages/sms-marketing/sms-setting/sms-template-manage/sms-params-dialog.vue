<template>
  <fx-dialog
    class="dialog-sms-params-wrapper"
    width="660px"
    :title="$t('marketing.commons.crbl_b3e42b')"
    append-to-body
    :visible="visible"
    @click.native="onClickForm"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleCloseDialog"
  >
    <div class="sms-params-dialog-content">
      <div class="sms-params-list">
        <div class="sms-params-item">
          <div class="item-title">
            {{$t('marketing.commons.bl_ddc7d2')}}
          </div>
          <div class="item-content">
            <div class="sms-params-radio-group">
              <fx-radio v-if="paramsType.includes(0)" v-model="variableType" :label="0" @change="contentVariables && contentVariables.show()">{{$t('marketing.pages.sms_marketing.dxbl_a23d1b')}}</fx-radio>
              <fx-radio v-if="paramsType.includes(1)" v-model="variableType" :label="1" @change="contentVariables && contentVariables.hide()">{{$t('marketing.pages.sms_marketing.zdybl_b27395')}}</fx-radio>
              <fx-radio v-if="paramsType.includes(2)" v-model="variableType" :label="2" @change="contentVariables && contentVariables.hide()">{{$t('marketing.pages.sms_marketing.ljbl_bdc91d')}}</fx-radio>
            </div>
            <div v-show="variableType === 0 && paramsType.includes(0)" style="position: relative; width: 100%;">
              <div class="sms-content-varholder"></div>
              <div class="sms-params-select" @click="contentVariables && contentVariables.show()">
                <p :class="['fake-placeholder',fakePlaceholder !== $t('marketing.commons.qxz_708c9d') && 'obj-params']">{{ fakePlaceholder }}</p>
                <i class="el-select__caret el-input__icon fx-icon-arrow-down"></i>
              </div>
            </div>
            <div v-if="variableType === 1 && paramsType.includes(1)" style="width: 100%;">
              <fx-input
                class="sms-params-input"
                :placeholder="isThirdSmsProvider ? $t('marketing.pages.sms_marketing.dsfdxtgszz_993b35') : $t('marketing.pages.sms_marketing.qsrbllrbl_0cd2f0')"
                size="small"
                @input="handleInsertCustomParams"
                v-model="customParam"
              >
                <p
                  @click="quickInsertCustomParams(1)"
                  style="margin-right: 12px;"
                  slot="suffix"
                  class="input-suffix"
                  >{{ $t('marketing.pages.sms_marketing.kjsc_b899ef') }}</p
                >
              </fx-input>
              <div class="error-message" v-if="warningMessage">
                {{ warningMessage }}
              </div>
            </div>
            <div v-if="variableType === 2 && paramsType.includes(2)" style="width: 100%;">
              <fx-input
                class="sms-params-input"
                :placeholder="isThirdSmsProvider ? $t('marketing.pages.sms_marketing.dsfdxtgszz_993b35') : $t('marketing.pages.sms_marketing.qsrbllrbl_0cd2f0')"
                size="small"
                @input="handleInsertLinkParam"
                v-model="linkParam"
              >
                <p
                  @click="quickInsertCustomParams(2)"
                  style="margin-right: 12px;"
                  slot="suffix"
                  class="input-suffix"
                  >{{ $t('marketing.pages.sms_marketing.kjsc_b899ef') }}</p
                >
              </fx-input>
              <div class="error-message" v-if="warningMessage">
                {{ warningMessage }}
              </div>
            </div>
          </div>
        </div>
        <div class="sms-params-item" v-if="!!isThirdSmsProvider && editFormData.tplType !== 'INTERNATIONAL'">
          <div class="item-title">
            {{$t('marketing.commons.lx_226b09')}}
            <questionTooltip
              effect="dark"
              :offset="192"
              style="margin-left: 4px;"
            >
              <span
                slot="question-content"
              >
              {{ $t('marketing.pages.sms_marketing.gjalydxmbc_010472') }}
              <a href="https://help.aliyun.com/zh/sms/user-guide/message-template-specifications/?spm=a2c4g.11186623.help-menu-44282.d_2_1_2.363b6f22iJwI9E" target="_blank">{{$t('marketing.commons.ckxq_5b48db')}}</a>
              </span>
            </questionTooltip>
          </div>
          <div class="item-content">
            <fx-select v-model="smsVariableType" :options="variableTypeOptions" />
          </div>
        </div>
      </div>
    </div>

    <div slot="footer" class="sms-params-dialog-footer">
      <fx-button size="small" type="primary" @click="handleSubmit">{{
        $t("marketing.commons.qd_38cf16")
      }}</fx-button>
      <fx-button size="small" @click="handleCloseDialog">{{
        $t("marketing.commons.qx_625fb2")
      }}</fx-button>
    </div>
  </fx-dialog>
</template>

<script>
import insertVariable from "./insert-variable";
import questionTooltip from '@/components/questionTooltip';
import SMSTemplateTools from '@/pages/promotion-activity/sms/components/utils';

export default {
  components: {
    elDialog: FxUI.Dialog,
    elForm: FxUI.Form,
    elFormItem: FxUI.FormItem,
    elButton: FxUI.Button,
    questionTooltip
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    editFormData: {
      type: Object,
      default: () => ({})
    },
    paramsType: {
      type: Array,
      default: () => [0,1]
    },
    currentPlatform: {
      type: Object,
      default: () => ({})
    },
    defaultType: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      contentVariables: null,
      smsParamsFormData: {},
      variableType: 0,
      fakePlaceholder: $t('marketing.commons.qxz_708c9d'),
      customParam: '',
      smsVariableType: '',
      linkParam: '',
      errorMessage: ''
    };
  },
  watch: {
    "editFormData.relationApiName": {
      handler(val) {
        if (val) {
          this.createVariablesView();
        }
      },
    },
    visible(val){
      if(val){
        this.resetData()
      }
    }
  },
  mounted(){
    if(this.editFormData.relationApiName){
      this.createVariablesView()
    }
    this.variableType = this.defaultType
    console.log('currentPlatform', this.currentPlatform)
  },
  methods: {
    handleCloseDialog() {
      this.resetData()
      this.$emit("update:visible", false);
    },
    resetData(){
      this.smsParamsFormData = {}
      this.customParam = ''
      this.fakePlaceholder = $t('marketing.commons.qxz_708c9d')
      this.linkParam = ''
    },
    createVariablesView() {
      const editFormData = this.editFormData;
      if (!editFormData.relationApiName) return;
      if (this.contentVariables) {
        this.contentVariables.destroy();
        this.contentVariables = null;
      }
      insertVariable
        .requireDeps()
        .then(() =>
          insertVariable.fetchVariablesData(editFormData.relationApiName)
        )
        .then(cusVariablesData => {
          let contentRichText;
          if (cusVariablesData.length > 1) cusVariablesData.length = 1; // 只要对象变量，不需要流程变量
          const contentVariables = insertVariable.createVariables(
            cusVariablesData,
            {
              onSelect: data => this.handleCreatePaasVar(data)
            }
          );
          insertVariable
            .fetchEVrichHtml(
              { content: editFormData.content },
              editFormData.relationApiName,
              editFormData.smsContentParam
            )
            .then(rtValue => {
              if (editFormData.paramDetail && editFormData.paramDetail.length) {
                editFormData.paramDetail.forEach(varInfo => {
                  const showName = "${" + varInfo.value + "}";
                  rtValue.richValue = rtValue.richValue.replace(
                    "{" + varInfo.name + "}",
                    showName
                  );
                });
              }
              contentRichText = insertVariable.createSmstplRT(rtValue);
              this.$emit("updateRichText", contentRichText);
            });
          $(".sms-content-varholder").append(contentVariables.$el);
          if (this.contentVariables) this.contentVariables.destroy();
          this.contentVariables = contentVariables;
        });
    },
    handleCreatePaasVar(data) {
      const variablePattern = /\$\{([^}]+)\}/g;
      const label = (variablePattern.exec(data.label || '')) || []
      this.fakePlaceholder = label[1]
      this.smsParamsFormData = data;
    },
    handleSubmit() {
      // 第三方短信提供商需要选择变量类型
      if(!this.smsVariableType && this.isThirdSmsProvider && this.editFormData.tplType !== 'INTERNATIONAL'){
        FxUI.Message.error($t('marketing.pages.sms_marketing.qxzbllx_a7b62b'))
        return
      }
      if(Object.keys(this.smsParamsFormData).length === 0){
        FxUI.Message.error($t('marketing.pages.sms_marketing.qxzbl_844a7a'))
        return
      }
      this.$emit("update:submit", {
        ...this.smsParamsFormData,
        smsVariableType: this.smsVariableType
      });
      this.handleCloseDialog();
    },
    handleInsertCustomParams(val) {
      // 第三方短信提供商只允许英文、数字、下划线、连字符
      console.log('handleInsertCustomParams', val)
      if (this.isThirdSmsProvider) {
        const englishPattern = /^[A-Za-z0-9_-]*$/;
        if (!englishPattern.test(val)) {
          // 过滤掉非法字符
          const filteredVal = val.replace(/[^A-Za-z0-9_-]/g, '');
          console.log('handleInsertCustomParams filtered', filteredVal)
          // 使用 $nextTick 确保 DOM 更新后再设置值
          this.$nextTick(() => {
            this.customParam = filteredVal;
          });
          this.warningMessage = $t('marketing.pages.sms_marketing.dsfdxtgszz_993b35');
          return;
        }
      }
      this.warningMessage = '';
      this.customParam = val;
      // id只能是英文的，且需要唯一
      this.smsParamsFormData = {
        id: "customVariables_" + new Date().getTime(),
        label: "${" + this.customParam + "}",
        customVariableType: 'common'
      };
    },
    handleInsertLinkParam(val){
      // 第三方短信提供商只允许英文、数字、下划线、连字符
      if (this.isThirdSmsProvider) {
        const englishPattern = /^[A-Za-z0-9_-]*$/;
        if (!englishPattern.test(val)) {
          // 过滤掉非法字符
          const filteredVal = val.replace(/[^A-Za-z0-9_-]/g, '');
          // 使用 $nextTick 确保 DOM 更新后再设置值
          this.$nextTick(() => {
            this.linkParam = filteredVal;
          });
          this.warningMessage = $t('marketing.pages.sms_marketing.dsfdxtgszz_993b35');
          return;
        }
      }
      this.warningMessage = ''
      this.linkParam = val;
      this.smsParamsFormData = {
        id: "linkVariables_" + new Date().getTime(),
        label: "${" + this.linkParam + "}",
        customVariableType: 'link'
      };
    },
    quickInsertCustomParams(type){
      if(type === 1){
        this.handleInsertCustomParams('param1')
      }else if(type === 2){
        this.handleInsertLinkParam('param1')
      }
    },
    onClickForm(evt) {
      if (!$(evt.target).closest('.sms-params-item .item-content')[0]) {
        if (this.contentVariables) this.contentVariables.hide();
      }
    },
  },
  computed: {
    variableTypeOptions() {
      const tplType = this.editFormData.tplType
      return SMSTemplateTools.getSmsTplVariableTypeList(tplType)
    },
    isThirdSmsProvider(){
      return this.currentPlatform.id !== 'mw'
    }
  }
};
</script>

<style lang="less" scoped>
.dialog-sms-params-wrapper {
  .sms-params-dialog-content {
    padding: 25px 36px;
    .sms-params-list {
      .sms-params-item {
        display: flex;
        .item-title {
          margin-right: 24px;
          font-size: 16px;
          color: #181c25;
          display: flex;
          align-items: center;
          min-width: 50px;
          height: fit-content;
          line-height: 32px;
          height: 32px;
        }
        .item-content {
          display: flex;
          flex-direction: column;
          padding-top: 2px;
          flex: 1;
          .sms-params-radio-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin-bottom: 12px;
            align-items: flex-start;
          }
          /deep/ .el-form-item__content{
            margin-left: 0 !important;
          }
          .input-suffix{
            color: var(--color-info06);
            cursor: pointer;
          }
          .sms-params-select{
            border: 1px solid #c1c5ce;
            border-radius: 4px;
            width: 100%;
            height: 32px;
            padding: 0 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            margin-bottom: 16px;
            .fake-placeholder{
              color: #c1c5ce;
              font-size: 12px;
              &.obj-params{
                color: #6aab3e;
                font-size: 14px;
              }
            }
          }
          .error-message{
            color: var(--color-danger06,#ff522a);
            font-size: 12px;
            line-height: 12px;
            padding-top: 4px;
          }
        }
      }
    }
  }
  .sms-content-varholder{
    position: absolute;
    top: 40px;
    left: 80px;
    /deep/ .paas-flow-variables{
      left: 0;
      top: 0;
    }
  }
}
</style>
