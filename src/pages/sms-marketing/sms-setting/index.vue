<template>
  <div class="sms-manage">
    <template v-if="loading">
      <loading-mask :show="loading" />
    </template>
    <template v-else>
      <div
        v-if="!isOpen"
        class="sms-init-wrapper"
      >
        <sms-init from="smsmanage" />
      </div>
      <div
        v-else
        class="sms-setting-wrapper"
      >
        <content-header
          :title="$t('marketing.commons.dxfw_ffa488')"
          :border="true"
        >
          <div class="sms-manage-header yxt-mncn-hfom">
            <div class="title" />
            <div v-if="smsPlatformList.length > 0" class="switch-btn" @click="smsPlatformDialogVisible = true">
              {{currentPlatform.name}}
                <i class="iconfont iconqiehuan el-icon--right" />
            </div>
            <div class="platform-name" v-else>
               {{currentPlatform.name}}
            </div>
            <!-- 短信配额以及短信签名在使用第三方平台时不展示 -->
            <div
              :class="['tab-item', tabPage == 'quota' && 'active']"
              v-if="!isThirdSmsProvider"
              @click="handleSwitch('quota')"
            >
              {{ $t('marketing.commons.dxpe_02fe7d') }}
            </div>
            <div
              :class="['tab-item', tabPage == 'signature' && 'active']"
              v-if="!isThirdSmsProvider"
              @click="handleSwitch('signature')"
            >
              {{ $t('marketing.pages.sms_marketing.dxqmxx_6cb937') }}
            </div>
            <div
              v-show="false"
              :class="['tab-item', tabPage == 'TemplateManage' && 'active']"
              @click="handleSwitch('TemplateManage')"
            >
              {{ $t('marketing.pages.sms_marketing.mbgl_f19bc8') }}
            </div>
            <div
              :class="['tab-item', tabPage == 'TemplateManage3' && 'active']"
              @click="handleSwitch('TemplateManage3')"
            >
              {{ $t('marketing.pages.sms_marketing.mbgl_f19bc8') }}
            </div>
            <div
              :class="['tab-item', tabPage == 'detail' && 'active']"
              @click="handleSwitch('detail')"
            >
              {{ $t('marketing.pages.sms_marketing.dxmx_30e9f1') }}
            </div>
          </div>
        </content-header>

        <sms-quota v-if="tabPage == 'quota'" />
        <div
          v-if="tabPage == 'signature'"
          style="padding: 25px 20px;"
          :show="tabPage == 'signature'"
        >
          <sms-signature />
        </div>
        <template-manage v-if="tabPage == 'TemplateManage'" />
        <detail v-if="tabPage == 'detail'" />
        <template-manage2 v-if="tabPage == 'TemplateManage2'" />
        <template-manage3 :currentPlatform="currentPlatform" v-if="tabPage == 'TemplateManage3'" />
      </div>
    </template>
    <SmsToggleDialog
      v-if="smsPlatformDialogVisible"
      :visible="smsPlatformDialogVisible"
      :platformList="smsPlatformList"
      :currentPlatform="currentPlatform"
      @onClose="smsPlatformDialogVisible=false"
      @onSubmitted="handleToggleSmsPlatform"
    />
  </div>
</template>

<script>
import '@/assets/style/all.less'
import SmsInit from '../sms-init/index.vue'
import SmsQuota from './sms-quota/index.js'
import SmsSignature from '../sms-init/sms-result/index.vue'
import ContentHeader from '@/components/content-header/index.vue'
import TemplateManage from './sms-template-manage/index2.vue'
import TemplateManage2 from './sms-template-manage/index.vue'
import TemplateManage3 from './sms-template-manage/index3.vue'
import Detail from './sms-detail/index.vue'
import LoadingMask from '@/components/loading-mask/index.vue'
import insertVariable from './sms-template-manage/insert-variable.js'
import SmsToggleDialog from '../components/smsToggleDialog.vue'
import http from '@/services/http/index.js'

export default {
  components: {
    ContentHeader,
    SmsInit,
    SmsQuota,
    SmsSignature,
    TemplateManage,
    Detail,
    TemplateManage2,
    TemplateManage3,
    LoadingMask,
    SmsToggleDialog
  },
  data() {
    return {
      isOpen: false,
      loading: true,
      smsPlatformDialogVisible: false,
      smsPlatformList: [],
      currentPlatform: {},
      tabPage: 'quota'
    }
  },
  computed: {
    isThirdSmsProvider(){
      return this.currentPlatform.id !== 'mw'
    },
  },
  created() {
    this.queryCurrentSmsPlatformList()
  },
  methods: {
    // 不通过这个接口判断是否开通梦网
    // queryIsOpen() {
    //   const signatureObj = this.$store.dispatch('querySignature')
    //   signatureObj.then(
    //     ret => {
    //       this.loading = false
    //       this.isOpen = ret.status === 'success'
    //     },
    //     () => {
    //       this.loading = false
    //       console.log('报错啦')
    //     },
    //   )
    // },
    // 查询当前企业短信平台列表  多个平台支持切换
    async queryCurrentSmsPlatformList(){
      const  { errCode, data = [] } = await http.queryThirdListSmsProvider()
      /* 获取当前企业开通的所有短信平台（包括梦网）
      */
      if(errCode === 0 && data.length > 0){
        this.smsPlatformList = data
        // 优先第一个平台 如果当前平台不是梦网 则默认展示模板管理
        this.currentPlatform = data[0]
        if(this.currentPlatform.id !== 'mw'){
          this.tabPage = 'TemplateManage3'
        }
        this.isOpen = true
        this.loading = false
      } else {
        this.isOpen = false
        this.loading = false
      }
    },
    handleSwitch(page) {
      this.tabPage = page
    },
    handleToggleSmsPlatform(platform){
      if(this.currentPlatform.id == 'mw' && platform !== 'mw'){
        this.tabPage = 'TemplateManage3'
      }
      this.currentPlatform = platform
    }
  },
  nvprops: {
    // 非 VUE 组件属性
    convertTplVars: insertVariable.convertTplVars,
  },
}
</script>

<style lang="less">
@basePath: '../../../';
.sms-manage {
  .sms-init-wrapper {
    // padding: 30px 20px;
    display: flex;
    background: #fff;
  }
  .sms-setting-wrapper {
    display: flex;
    flex-flow: column nowrap;
    height: 100%;
    .sms-manage-header {
      flex: 0 0 auto;
      align-items: center;
      padding-left: 12px;
      display: flex;
      .title {
        width: 12px;
        height: 20px;
        line-height: 20px;
        font-size: 14px;
        color: #333333;
        border-right: 1px solid @border-color-base;
      }
      .switch-btn{
        display: flex;
        cursor: pointer;
        margin-left: 20px;
        .status{
          margin-left: 8px;
          height: 18px;
          border-radius: 2px;
          display: inline-block;
          font-size: 12px;
          text-align: center;
          border: 1px solid;
          padding: 0 4px;
        }
      }
      .platform-name{
        margin-left: 20px;
      }
      .tab-item {
        margin-left: 26px;
        height: 50px;
        line-height: 50px;
        font-size: 12px;
        color: #999999;
        text-align: center;
        cursor: pointer;
      }
      .tab-item.active,
      .tab-item:hover {
        color: #333333;
        box-shadow: 0 2px 0 0 #ff8837;
      }
    }
    .action-btn {
      font-size: 13px;
      color: var(--color-info06,#407FFF);
      cursor: pointer;
    }
    .action-btn:hover {
      text-decoration: underline;
    }
  }
}
</style>
