<!-- 组件说明 -->
<template>
  <v-dialog
    :title="title"
    append-to-body
    width="800px"
    :visible="visible"
    class="sms-create-template__dialog__wrapper"
    :loading="submittedLoading"
    @onClose="handleCloseDialog"
    @onSubmit="handleSubmitDialog"
  >
    <div class="sms-create-template__dialog">
      <div class="km-g-loading-mask" v-if="!formVisible">
        <span class="loading"></span>
      </div>
      <el-form
        ref="templateForm"
        v-if="formVisible"
        :model="templateForm"
        :rules="rules"
        label-width="auto"
        class="sms-template-form"
        label-position="left"
      >
      
        <fx-select
          ref="select_el"
          prop="currentPlatformId"
          class="select_input"
          style="width: 100%;margin-bottom: 0"
          v-model="templateForm.currentPlatformId"
          :options="platformList"
          @change="handlePlatformChange"
          :label="$t('marketing.pages.sms_marketing.dxtd_d93f73')"
          size="small"
        >
        </fx-select>

        <el-form-item style="margin-top: 5px;">
          <fx-link style="font-size: 12px;" type="standard" :underline="false" href="https://help.fxiaoke.com/93d5/9188/a093/a12a" target="_blank">{{ $t('marketing.pages.sms_marketing.ljctdmbshj_5ede4d') }}</fx-link>
        </el-form-item>
        <el-form-item
          :label="$t('marketing.commons.mblx_bfd708')"
          prop="tplType"
        >
        <div class="sms-create-template__list">
          <div
            v-for="item in templateList" :key="item.tplType"
            :class="['sms-template-item',
            templateForm.tplType === item.tplType && 'selected',item.disabled && 'gray']"
            @click="handleChooseTypes(item)"
          >
            <p class="template-title">
              {{ item.title }}
            </p>
            <p class="template-des">
              {{ item.des }}
            </p>
          </div>
        </div>
        <span class="sms-create-template__tip" v-if="(tplTypeList.length == 1 && tplTypeList.includes('PROMOTION') && templateForm.currentPlatformId ==='mw')">{{ $t('marketing.pages.sms_marketing.wlbmsytzmb_78096b') }}</span>
        </el-form-item>
        <fx-input 
          required 
          :maxlength="30"
          :show-word-limit="true"
          v-model="templateForm.name"
          :placeholder="$t('marketing.commons.qsr_02cc4f')"
          prop="name"
        >
          <span slot="label">{{ $t('marketing.pages.sms_marketing.mbmc_abc839') }}</span>
        </fx-input>
        <fx-input 
          required 
          :maxlength="100"
          :show-word-limit="true"
          v-model="templateForm.remark"
          :placeholder="$t('marketing.commons.qsr_02cc4f')"
          prop="remark"
        >
          <span slot="label">{{ $t('marketing.commons.mbcj_4d3865') }}</span>
        </fx-input>
        <el-form-item
          :label="$t('marketing.commons.mbnr_03ae79')"
          prop="content"
        >
          <templateContentBox
            ref="templateContentBox"
            v-model="templateForm.content"
            :current-platform="templateForm.currentPlatformId"
            :tpl-type="templateForm.tplType"
            @changeContent="changeContent"
          />
        </el-form-item>
      </el-form>
    </div>
  </v-dialog>
</template>
<script>
import VDialog from '@/components/dialog/index.vue'
import http from '@/services/http/index.js'
import util from '@/services/util/index.js'
import templateContentBox from './template-content-box.vue'
import SMSTemplateTools from '@/pages/promotion-activity/sms/components/utils.js'

export default {
  components: {
    VDialog,
    elForm: FxUI.Form,
    elFormItem: FxUI.FormItem,
    elCascader: FxUI.Cascader,
    templateContentBox,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    id: {
      type: String,
      default: '',
    },
    from: {
      type: String,
      default: ''
    },
    // 是否允许切换模板类型，有些入口只有营销短信
    tplTypeList: {
      type: Array,
      default: ()=>(['all','PROMOTION','NOTIFICATION'])
    }
  },
  data() {
    const nameValidator = (rule, value, callback) => {
      if (value === '') {
        callback(new Error($t('marketing.commons.qsrmbmc_8f21b9')))
      } else if (value.length > 30) {
        callback(new Error($t('marketing.commons.mbmcbncggz_30a243')))
      } else {
        callback()
      }
    }
    const contentValidator = (rule, value, callback) => {
      if (value === '') {
        callback(new Error($t('marketing.commons.qsrmbnr_73536b')))
      } else if (util.hasSpecailCharactersForSMSContent(value)) {
        callback(new Error($t('marketing.commons.qbysrtszf_d4a2fe')))
      } else {
        callback()
      }
    }
    return {
      title: $t('marketing.commons.xjmb_156a04'),
      rules: {
        tplType: [
          { required: true, validator: (rule, value, callback)=>callback(), trigger: 'change' },
        ],
        name: [{ required: true, validator: nameValidator, trigger: 'blur' }],
        content: [
          { required: true, validator: contentValidator, trigger: 'blur' },
        ],
        currentPlatformId: [
          { required: true, trigger: 'change' },
        ]
      },
      templateForm: {
        name: '',
        // 没传或者是传1 就是1
        tplType: 'PROMOTION',
        content: '',
        currentPlatformId: this.$store.state.SmsMarketing.currentPlatform.id || 'mw',
        remark: ''
      },
      submittedLoading: false,
      formVisible: false,
    }
  },
  computed: {
    templateList(){
      const platform = this.templateForm.currentPlatformId
      const data = SMSTemplateTools.getSmsTplTypeList(platform)
       if(platform === 'mw'){
        data.forEach(item=>{
        if(!this.tplTypeList.includes(item.tplType)){
          item.disabled = true
        }
      })
      }
      this.templateForm.tplType = data[0].tplType
      return data
    },
    platformList(){
      return this.$store.state.SmsMarketing.smsPlatformList.map(item=>{
        return {
          value: item.id,
          label: item.name,
          ...item
        }
      })
    },
  },
  beforeCreate() {},
  mounted() {
    setTimeout(()=>{
      this.formVisible = true
    },100)
    if (this.id) {
      // 复制也是新建
      this.title = this.from === 'copy' ? $t('marketing.commons.xjmb_156a04') : $t('marketing.commons.bjmb_c6aa35')
      this.getTemplateDetail()
    } else {
      this.title = $t('marketing.commons.xjmb_156a04')
    }
  },
  created() {
  },
  destroyed() {},
  methods: {
    changeContent(val) {
      this.templateForm.content = val
    },
    // 关闭弹框
    handleCloseDialog() {
      this.$emit('update:visible', false)
    },
    handleSubmitDialog() {
      this.$refs.templateForm.validate(valid => {
        if (!valid) {
          return
        }
        this.submittedLoading = true
        const _content = this.$refs.templateContentBox.replaceParams()
        const variableAttributes = this.$refs.templateContentBox.getVariableAttributes()
        const formatVariableAttributes = this.formatVariableAttributes(_content,variableAttributes)
        let params = {
          name: this.templateForm.name,
          content: _content,
          channelType: 1,
          tplType: this.templateForm.tplType,
          shortUrlMap: this.$refs.templateContentBox.smsMap,
          remark: this.templateForm.remark,
          providerId: this.templateForm.currentPlatformId,
          variableAttributes: formatVariableAttributes
        }
        // 复制时只是复制内容 实际是新建操作
        if (this.id && this.from !== 'copy') {
          params.id = this.id
        }
        http.createThirdSmsTemplate(params).then(res => {
          this.submittedLoading = false
          if (res && res.errCode === 0) {
            FxUI.Message.success($t('marketing.pages.sms_marketing.mbxzcg_1abb5b'))
            this.$emit('update:submit',res.data.content ? res.data : params)
            this.handleCloseDialog()
          }
        })
      })
    },
    getTemplateDetail() {
      http.sendGetSmsTemplateDetail({ templateId: this.id }).then(res => {
        if (res && res.errCode === 0) {
          const resData = res.data
          this.templateForm = {
            name: resData.name,
            tplType: resData.tplType,
            // 营销模板需要手动去掉后台返回的内容上  拒收请回复R
            content: resData.tplType === 'PROMOTION' ? resData.content.slice(0, resData.content.length - 7) : resData.content,
            currentPlatformId: resData.providerId,
            remark: resData.remark
          }
          this.$refs.templateContentBox.smsMap = resData.shortUrlMap || {}
        }
      })
    },
    handleChooseTypes(item){
      if(item.disabled) return
      this.templateForm.tplType = item.tplType
    },
    // 切换平台时，清空内容
    handlePlatformChange(val){
      this.templateForm.content = ''
    },
    formatVariableAttributes(content,variableAttributes) {
      // 从 content 中提取所有变量
      const variableRegex = /\$\{([^}]+)\}/g;
      const contentVariables = [];
      let match;
      
      while ((match = variableRegex.exec(content)) !== null) {
        contentVariables.push(match[1]);
      }
      
      // 创建结果数组
      const result = [];
      
      // 处理每个 content 中的变量
      contentVariables.forEach(varName => {
        // 查找是否已有对应的映射
          const existingAttr = variableAttributes.find(attr => attr.name === varName);
          
          if (existingAttr) {
            result.push(existingAttr);
          } else {
            result.push({
              name: varName,
              type: 'others'
            });
          }
      });
      
      return result;
    }
  }, // 生命周期 - 销毁完成
}
</script>

<style lang="less" scoped>
.sms-create-template__dialog {
  position: relative;
  /deep/ .el-form-item{
    margin-bottom: 16px;
    .el-form-item__content{
      line-height: normal;
      .el-form-item{
        margin-bottom: 0;
      }
    }
    .el-form-item__label-wrap {
      margin-left: 0px !important;
    }
    .el-select{
      width: 350px;
    } 
  }
  .sms-create-template__list{
    display: flex;
    gap: 20px;
    .sms-template-item{
      border: 1px solid #E9EDF5;
      border-radius: 4px;
      flex: 1;
      padding: 12px;
      position: relative;
      font-size: 14px;
      cursor: pointer;
      min-width: 0;
      &.selected {
        border: 1px solid var(--color-primary06,#407FFF);
        &::before {
          content: '\00a0';
          display: inline-block;
          border: 2px solid #fff;
          border-top-width: 0;
          border-right-width: 0;
          width: 9px;
          height: 5px;
          transform: rotate(-50deg);
          position: absolute;
          top: 2px;
          left: 2px;
          z-index: 3;
        }
        &::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 0;
          height: 0;
          border-top: 25px solid var(--color-primary06,#407FFF);
          border-right: 25px solid transparent;
        }
      }
      &.gray{
        background: #F7F8FA;
        border: 1px solid #E9EDF5;
        cursor: not-allowed;
        &::after {
          border-top: 25px solid #C1C5CE;
        }
      }
      .template-title{
        color: #181C25;
        font-weight: bold;
        line-height: 20px;
      }
      .template-des{
        margin-top: 6px;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 18px;
        color: #91959E;
      }
    }
  }
  .sms-create-template__tip{
    color: #545861;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 18px;
    margin-top: 8px;
    display: inline-block;
  }
}
</style>
