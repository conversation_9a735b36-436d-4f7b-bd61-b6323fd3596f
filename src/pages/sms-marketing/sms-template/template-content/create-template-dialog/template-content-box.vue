<template>
  <div class="template--content">
    <div class="textarea_wrapper">
      <textarea
        ref="template--content"
        v-model="content"
        :placeholder="placeholder"
        :maxlength="450"
        @input="handleContentInput"
      />
      <div class="sms-content-btns">
        <el-tooltip
          effect="dark"
          :content="$t('marketing.pages.sms_marketing.crryljfyxt_18633c')"
          placement="bottom-start"
        >
          <i
            class="km-ico-link"
            @click="showCreateShortUrlDialog = true"
          />
        </el-tooltip>
        <el-tooltip
          effect="dark"
          :content="$t('marketing.pages.sms_marketing.kjcryxtcjd_d1791b')"
          placement="bottom-start"
        >
          <i
            class="add-material-link-btn"
            @click="handleShowMaterialDialog('h5')"
          />
        </el-tooltip>
        <el-tooltip
          effect="dark"
          :content="$t('marketing.pages.sms_marketing.kjcryxtcjd_01e392')"
          placement="bottom-start"
        >
          <i
            class="mini-app-btn"
            @click="handleShowMaterialDialog('miniApp')"
          />
        </el-tooltip>
        <el-tooltip
          effect="dark"
          v-show="currentPlatform === 'mw'"
          :content="$t('marketing.pages.sms_marketing.zhdcjzdtcr_9f6b46')"
          placement="bottom-start"
        >
        <span @click="insertParam" class="insert-param-select">{{ $t('marketing.commons.crcs_f05a09') }}</span>
        </el-tooltip>
        <div v-show="currentPlatform !== 'mw'" class="insert-param-select">
          <span @click="showSmsParamsDialog = true">
            {{ $t('marketing.commons.crcs_f05a09') }}
          </span>
          <QuestionTooltip
              effect="dark"
              :offset="192"
              style="margin:2px 0 0 4px;"
            >
              <span
                slot="question-content"
              >
                {{ $t('marketing.pages.sms_marketing.dxmbzccrbl_fac8f9') }}
              </span>
            </QuestionTooltip>
        </div>
      </div>
    </div>
    <!-- 只保留梦网的字数统计逻辑（新平台的计费标准不一样，不做统计） -->
    <div class="template--content__tips" v-show="currentPlatform === 'mw'">
      <div class="left-tips">
        {{ $t('marketing.commons.zatdxjf_d7f0ef', {data: ({'option0': length_templateContent, 'option1': messageShort})}) }}
        <QuestionTooltip
          class="question"
          :offset="135"
        >
          <div
            slot="question-content"
            style="max-width: 280px; color: #545861"
          >
            <div class="question-tips">
              {{ $t('marketing.components.SpreadSmsDialog.zstjbhqmjs_3feff0', {data: ({'option0': tplType === 'PROMOTION' ? `、${textRejectSMS}` : ''})}) }}</div>
          </div>
        </QuestionTooltip>
      </div>
    </div>
    <create-short-url-dialog
      v-if="showCreateShortUrlDialog"
      :visible="showCreateShortUrlDialog"
      @update:visible="showCreateShortUrlDialog = false"
      @upadete:submit="handleCreateShortUrlSubmit"
    />
    <select-material-dialog
      v-if="showMaterialChooseDialog"
      :menus="smd_menus"
      :tabbar="smd_tabbar"
      :visible="showMaterialChooseDialog"
      :marketing-event-id="marketingEventId"
      @onSubmit="handleApply"
      @onClose="showMaterialChooseDialog = false"
    />
    <sms-params-dialog
      v-if="showSmsParamsDialog"
      :visible="showSmsParamsDialog"
      :params-type="[1,2]"
      :default-type="1"
      :current-platform="{
        id: currentPlatform,
      }"
      :edit-form-data="{
        tplType: tplType
      }"
      @update:visible="showSmsParamsDialog = false"
      @update:submit="handleSmsParamsSubmit"
    />
  </div>
</template>

<script>
import http from '@/services/http/index.js'
import QuestionTooltip from '@/components/questionTooltip/index.vue'
import createShortUrlDialog from '@/components/create-short-url/index.vue'
import SelectMaterialDialog from '@/components/select-material-dialog/index.vue'
import smsParamsDialog from '@/pages/sms-marketing/sms-setting/sms-template-manage/sms-params-dialog.vue';

$.fn.extend({
  insertAtCaret(myValue) {
    const $t = $(this)[0]
    if (document.selection) {
      this.focus()
      sel = document.selection.createRange()
      sel.text = myValue
      this.focus()
    } else if ($t.selectionStart || $t.selectionStart == '0') {
      const startPos = $t.selectionStart
      const endPos = $t.selectionEnd
      const { scrollTop } = $t
      $t.value = $t.value.substring(0, startPos)
        + myValue
        + $t.value.substring(endPos, $t.value.length)
      this.focus()
      $t.selectionStart = startPos + myValue.length
      $t.selectionEnd = startPos + myValue.length
      $t.scrollTop = scrollTop
    } else {
      this.value += myValue
      this.focus()
    }
  },
})
export default {
  components: {
QuestionTooltip,
elSelect: FxUI.Select.components.ElSelect,
elOption: FxUI.Select.components.ElSelect.components.ElOption,
elTooltip: FxUI.Tooltip,
createShortUrlDialog,
SelectMaterialDialog,
smsParamsDialog
},
  props: {
    tplType: {
      type: Number,
      default: 'PROMOTION',
    },
    marketingEventId: {
      type: String,
      default: '',
    },
    value: {
      type: String,
      default: '',
    },
    currentPlatform: {
      type: String,
      default: 'mw'
    }
  },
  data() {
    return {
      insertLinkType: '',
      placeholder: $t('marketing.pages.sms_marketing.zccrdtblzj_1d0453'),
      smd_menus: ['materials'],
      content: this.value,
      showCreateShortUrlDialog: false,
      showMaterialChooseDialog: false,
      paramDataList: [],
      paramData: '',
      textRejectSMS: $t('marketing.commons.jsqhf_f9fe08'),
      isConferenceMarketingEvent: false,
      materialInfo_shortUrl_map: {},
      smsMap: {},
      signature: this.$store.state.SmsMarketing.signature,
      configOrigin: this.$store.state.Global.configOrigin,
      variableAttributes: [],
      showSmsParamsDialog: false,
    }
  },
  computed: {
    // 1:文章 3:会议 4:产品 10:落地页 24:海报 16:表单 25:邀请函
    smd_tabbar() {
      return this.isConferenceMarketingEvent ? [3, 1, 4, 10] : [10, 1, 4]
    },
    length_templateContent() {
      const textRejectSMS = `${this.textRejectSMS}`
      // 营销短信需要加上textRejectSMS
      const textRejectSMSLength = this.tplType === 'PROMOTION' ? textRejectSMS.length : 0
      const signature = `${this.signature}`
      return this.content.length + textRejectSMSLength + signature.length + 2
    },
    messageShort() {
      let count = 0
      if (this.length_templateContent <= 70) {
        count = 1
      } else {
        count = Math.ceil(this.length_templateContent / 67)
      }
      return count
    },
  },
  watch: {
    value: {
      handler(value) {
        this.content = value
      },
    },
    content: {
      handler(value) {
        if(!value){
          this.paramDataList = []
        }
        this.$emit('changeContent', value)
      },
      deep: true,
    },
  },
  created() {
  },
  methods: {
    handleShowMaterialDialog(type) {
      this.insertLinkType = type // h5链接 miniApp 小程序链接
      this.showMaterialChooseDialog = true
    },
    insertParam(type,value) {
      const { paramDataList } = this
      let insertParam = value ? value : `{param${paramDataList.length + 1}}`
      if(type === 'url'){
        const prefix = location.href.match(/localhost|.ceshi112.com/) !== null ? 'https://fs8.ceshi112.com/' : 'https://fs80.cn/'
        insertParam = `${prefix}${insertParam}`
      }
      paramDataList.push(insertParam)
      this.handleCreateShortUrlSubmit(insertParam)
    },
    handleCreateShortUrlSubmit(shortUrl) {
      const obj = this.$refs['template--content']
      const str = ` ${shortUrl} ` // 在链接文字前后补空格，以免与其它文字混淆
      if (document.selection) {
        const sel = document.selection.createRange()
        sel.text = str
      } else if (
        typeof obj.selectionStart === 'number'
        && typeof obj.selectionEnd === 'number'
      ) {
        const startPos = obj.selectionStart
        const endPos = obj.selectionEnd
        let cursorPos = startPos
        const tmpStr = obj.value
        this.content = tmpStr.substring(0, startPos)
          + str
          + tmpStr.substring(endPos, tmpStr.length)
        cursorPos += str.length
        obj.selectionStart = cursorPos
        obj.selectionEnd = cursorPos
      } else {
        this.content += str
      }
    },
    setDataToMaterialInfoShortUrlMap(
      shortUrl,
      materialDataFromSelectMaterialDialog = {},
    ) {
      if (!shortUrl) return
      this.materialInfo_shortUrl_map[shortUrl] = {
        objectId: materialDataFromSelectMaterialDialog.id,
        contentType: materialDataFromSelectMaterialDialog.type,
      }
    },
    parseMaterialUrl(url) {
      url = url.replace(/!!marketingActivityId!!/g, '')
      url = url.replace(/!!wxAppId!!/g, '')
      return url
    },
    async handleApply(data) {
      console.log('handleApply', data)
      this.showMaterialChooseDialog = false
      const { enterpriseAccount } = FS.contacts.getCurrentEmployee() || {};
      const {
        id, objectType, url, mpUrl,
      } = data
      const href = this.parseMaterialUrl(url);
      let shortUrl = ''
      let longUrl = ''
      if (this.insertLinkType === 'h5') {
        // H5链接
        const res = await http.getShortUrl({
          longUrl: href,
        })
        const { errCode } = res
        if (errCode !== 0) return
        shortUrl = res.data.shortUrl
        longUrl = res.data.longUrl
      } else if (this.insertLinkType === 'miniApp') {
        // 小程序内容链接
        // let path = mpUrl.split("?")[0];
        // let resData = await http.queryMiniAppForwardUrl({
        //   expireInterval: 31,
        //   expireType: 1,
        //   isExpire: true,
        //   objectId: id,
        //   objectType,
        //   path,
        //   query: `objectId=${id}&objectType=${objectType}`
        // });
        // let { errCode, data } = resData;
        // if (errCode != 0) return;
        const origin = this.configOrigin || window.location.origin
        const params = {
          longUrl: `${origin}/proj/page/marketing-mplink?ea=${enterpriseAccount||''}&objectType=${objectType}&objectId=${id}&redirectType=wechat`,
        }
        const res = await http.getShortUrl(params)
        const { errCode } = res
        if (errCode !== 0) return
        shortUrl = res.data.shortUrl
        longUrl = this.parseMaterialUrl(mpUrl)
      }
      this.smsMap[
        shortUrl
      ] = `${longUrl}&channelType=6&spreadChannel=sms&ea=${enterpriseAccount}&marketingActivityId=`
      $(this.$refs['template--content']).insertAtCaret($t('marketing.commons.dj_4c3a12', { data: ({ option0: shortUrl }) }))
      const newTemplateContent2 = $(this.$refs['template--content']).val()
      this.content = newTemplateContent2 // 触发vue watcher
      this.setDataToMaterialInfoShortUrlMap(shortUrl, data)
    },
    replaceParams() {
      return this.content
    },
    getVariableAttributes(){
      return this.variableAttributes
    },
    // 第三方短信提供商变量处理
    handleSmsParamsSubmit(data){
      const { smsVariableType,customVariableType } = data
      this.variableAttributes.push({
        name: data.label.replace(/^\${(.*)}$/, '$1'),
        type: smsVariableType
      })
      if(customVariableType === 'link'){
        this.insertParam('url',data.label)
      }else{
        this.insertParam('common',data.label)
      }
    },
    handleContentInput(e) {
      console.log('handleContentInput', e)
      if (this.currentPlatform !== 'mw') {
        // 只处理用户新输入的内容
        const input = e.data;
        if (input === '$') {
          // 阻止 $ 的输入
          // 1. 还原 textarea 的内容
          const textarea = e.target;
          // 2. 记录光标位置
          const pos = textarea.selectionStart - 1;
          // 3. 去掉最后输入的 $
          textarea.value = textarea.value.slice(0, pos) + textarea.value.slice(pos + 1);
          // 4. 恢复光标
          textarea.setSelectionRange(pos, pos);
          // 5. 触发 v-model 更新
          this.content = textarea.value;
        }
      }
    }
  },
}
</script>

<style lang="less" scoped>
.transition() {
  transition: all 500ms ease-out 0ms;
}
.template--content {
  position: relative;
  .textarea_wrapper {
    height: 135px;
    border: 1px solid #ddd;
    border-radius: 4px;
    display: flex;
  }
  textarea {
    flex: 1;
    border: none;
    padding-bottom: 20px;
    display: block;
    font-size: 13px;
    color: #333;
    width: 557px;
    height: 110px;
    overflow: auto;
    resize: none;
    padding: 8px 15px;
    box-sizing: border-box;
    transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
    .transition;
    &.disabled {
      background: #f2f2f2;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
  }
  .km-ico-link {
    position: absolute;
    top: 110px;
    left: 5px;
    cursor: pointer;
    &.disabled {
      cursor: default;
    }
    &.hide {
      display: none;
    }
  }
  .sms-content-btns {
    .add-material-link-btn {
      font-style: normal;
      position: absolute;
      top: 110px;
      left: 32px;
      line-height: 22px;
      cursor: pointer;
      content: " ";
      display: inline-block;
      width: 22px;
      height: 22px;
      background-image: url("../../../../../assets/images/icons/sms-add-material.png");
      background-repeat: no-repeat;
      background-size: contain;
      overflow: hidden;
      vertical-align: middle;
      &.disabled {
        cursor: default;
      }
    }
    .insert-param-select {
      color: var(--color-info06,#407FFF);
      position: absolute;
      top: 112px;
      left: 90px;
      display: flex;
      cursor: pointer;
    }
    .mini-app-btn {
      cursor: pointer;
      position: absolute;
      top: 112px;
      left: 60px;
      width: 20px;
      height: 20px;
      background-repeat: no-repeat;
      background-size: contain;
      background-image: url("../../../../../assets/images/icon/miniprogram-icon-gray.png");
    }
  }

  .template--content__tips {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    line-height: 1.2;
    color: #999999;
    margin-top: 5px;
    opacity: 1;
    height: auto;
    overflow: hidden;
    .transition;
    &.disabled {
      opacity: 0;
      height: 0;
    }
    .count {
      color: #f09835;
    }
    .question {
      display: inline-block;
      vertical-align: -2px;
      margin-left: 3px;
      .question-tips {
        font-size: 12px;
      }
    }
    .right-tips{
      color: var(--color-info06);
    }
  }
}
</style>
