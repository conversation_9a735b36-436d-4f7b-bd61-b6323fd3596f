<template>
  <div
    v-loading="loading && activedModelList.length === 0"
    class="model-list"
  >
    <div class="model--create">
      <div @click="isShowCreateDialog = true">
        <i class="yxt-icon16 icon-add-b" />
        <span>{{ $t('marketing.pages.content_tags_manage.xjbqz_19214e') }}</span>
      </div>
      <!-- <div>
        <span
          class="iconfont iconshezhi"
          @click="isShowCustomize = true"
        />
      </div> -->
    </div>
    <draggable-list
      v-if="activedModelList && activedModelList.length > 0"
      v-slot="scope"
      class="list__draggable-list"
      :value="activedModelList"
      @afterDragged="handleModelAfterDragged"
    >
      <div
        :class="[
          'model__item',
          curModelId === scope.row.id && 'cur',
          scope.row.state === 2 && 'disabled'
        ]"
        @click="handleClickModel(scope.row)"
      >
        <div class="item__dicon icon_move" />
        <div class="item__name">
          {{ scope.row.name }}&nbsp;&nbsp;
        </div>
        <div class="item__count">
          ({{ scope.row.level1TagsCount + scope.row.level2TagsCount }})
        </div>
        <fx-dropdown
          class="item__micon"
          trigger="hover"
          @command="handleModelOperate($event, scope.row)"
        >
          <div class="icon_more" />
          <fx-dropdown-menu slot="dropdown">
            <fx-dropdown-item command="edit">
              {{ $t('marketing.commons.bj_95b351') }}
            </fx-dropdown-item>
            <fx-dropdown-item
              v-if="!scope.row.sceneId"
              command="status"
            >
              {{
                scope.row.state === 1 ? $t('marketing.commons.ty_5c56a8') : $t('marketing.commons.qy_7854b5')
              }}
            </fx-dropdown-item>
            <fx-dropdown-item
              v-if="!scope.row.sceneId"
              command="delete"
              :disabled="scope.row.state === 1"
            >
              {{ $t('marketing.commons.sc_2f4aad') }}
            </fx-dropdown-item>
          </fx-dropdown-menu>
        </fx-dropdown>
      </div>
    </draggable-list>
    <div
      v-if="disabledModelList && disabledModelList.length > 0"
      class="list__disable"
    >
      <div
        v-for="item in disabledModelList"
        :key="item.id"
        :class="[
          'model__item',
          curModelId === item.id && 'cur',
          item.state === 2 && 'disabled'
        ]"
        @click="handleClickModel(item)"
      >
        <div
          class="item__dicon icon_move"
          style="visibility: hidden;"
        />
        <div class="item__name">
          {{ item.name }}&nbsp;&nbsp;
        </div>
        <div class="item__count">
          ({{ item.level1TagsCount + item.level2TagsCount }})
        </div>
        <fx-dropdown
          class="item__micon"
          trigger="hover"
          @command="handleModelOperate($event, item)"
        >
          <div class="icon_more" />
          <fx-dropdown-menu slot="dropdown">
            <fx-dropdown-item command="edit">
              {{ $t('marketing.commons.bj_95b351') }}
            </fx-dropdown-item>
            <fx-dropdown-item
              v-if="!item.sceneId"
              command="status"
            >
              {{
                item.state === 1 ? $t('marketing.commons.ty_5c56a8') : $t('marketing.commons.qy_7854b5')
              }}
            </fx-dropdown-item>
            <fx-dropdown-item
              v-if="!item.sceneId"
              command="delete"
              :disabled="item.state === 1"
            >
              {{ $t('marketing.commons.sc_2f4aad') }}
            </fx-dropdown-item>
          </fx-dropdown-menu>
        </fx-dropdown>
      </div>
    </div>

    <Empty
      v-if="activedModelList.length === 0 && disabledModelList.length === 0"
      class="list__empty"
      :title="$t('marketing.pages.content_tags_manage.zwbqz_f44b57')"
    />

    <!-- 模型新建 -->
    <model-create
      v-if="isShowCreateDialog"
      :default-model-data="defaultModelData"
      :visible="isShowCreateDialog"
      @update:visible="handleUpdateCreateDialogVisible"
      @update:submit="handleCreateModel"
    />

    <VDialog
      :title="$t('marketing.pages.tags_manage.bqsz_6f458d')"
      :visible.sync="isShowCustomize"
      @onClose="isShowCustomize = false"
      @onSubmit="handleSubmitDialog"
    >
      <div class="customize-title">
        <span class="tip">{{ $t('marketing.pages.tags_manage.yxygzsxczb_698bcc') }}</span>
        <el-switch
          v-model="isCustomize"
          size="small"
        />
      </div>
      <div class="customize-example">
        <div class="title">
          {{ $t('marketing.commons.sl_614fc6') }}
        </div>
        <div>
          <img
            :src="require(/^zh/.test(userLanguage) ? '@/assets/images/tag-example.jpg' : '@/assets/images/tag-example-en.jpg')"
            alt=""
            width="100%"
          >
        </div>
      </div>
    </VDialog>
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex'
import ModelCreate from './model-create.vue'
import DraggableList from '@/components/draggable-list/index.vue'
import { confirm } from '@/utils/globals.js'
import Empty from '@/components/common/empty.vue'
import VDialog from '@/components/dialog/index.vue'

export default {
  components: {
    DraggableList,
    ModelCreate,
    Empty,
    VDialog,
    ElSwitch: FxUI.Switch,
  },
  data() {
    return {
      isShowCreateDialog: false, // 模型新建

      defaultModelData: {}, // 选中后作为新建模型的模板数据
      loading: true,
      isShowCustomize: false,
      isCustomize: false,
    }
  },
  computed: {
    ...mapState('ContentTagsManage', [
      'models',
      'modelVersion',
      'curModel',
    ]),
    activedModelList() {
      return this.models.filter(item => item.state === 1)
    },
    disabledModelList() {
      return this.models.filter(item => item.state === 2)
    },
    curModelId() {
      return this.curModel.id
    },
  },
  async mounted() {
    await this.queryModels()
    this.setCurModel(this.activedModelList[0] || {})
    // this.queryIsAllowAddTag()
  },
  methods: {
    ...mapActions('ContentTagsManage', [
      'listTagModel',
      'updateModelState',
      'deleteModel',
      'updateDisplayOrder',
      'setCurModel',
      'setOrCancelAllowAddTag',
      'isAllowAddTag',
    ]),

    async queryModels() {
      this.loading = true
      await this.listTagModel({ sourceType: 1 })
      this.loading = false
    },

    // 新建模型提交
    handleCreateModel() {
      this.queryModels()
      this.isShowCreateDialog = false
    },
    handleModelOperate(e, modelData) {
      switch (e) {
        case 'edit':
          this.handleModelEdit(modelData)
          break
        case 'status':
          this.handleModelStatus(modelData)
          break
        case 'delete':
          this.handleModelDelete(modelData)
          break
        default:
          break
      }
    },
    // 编辑
    handleModelEdit(modelData) {
      this.defaultModelData = {
        id: modelData.id,
        name: modelData.name,
        description: modelData.description,
      }
      this.isShowCreateDialog = true
    },
    // 启用/停用 1 启用 2 停用
    async handleModelStatus(modelData) {
      const { state, name } = modelData
      confirm(
        state === 1 ? $t('marketing.commons.qrytym_fed6da', { data: ({ option0: name }) }) : $t('marketing.commons.qryqym_3a4d9d', { data: ({ option0: name }) }),
        $t('marketing.commons.ts_02d981'),
        {},
      ).then(async () => {
        await this.updateModelState({
          id: modelData.id,
          state: state === 1 ? 2 : 1,
          sourceType: 1,
        })
        FxUI.Message.success($t('marketing.commons.cg_bd6c8c', { data: ({ option0: state === 1 ? $t('marketing.commons.ty_5c56a8') : $t('marketing.commons.qy_7854b5') }) }))
        this.queryModels()
      })
    },
    // 删除
    handleModelDelete(modelData) {
      const { name } = modelData
      confirm(
        $t('marketing.commons.qryscm_6a1256', { data: ({ option0: name }) }),
        $t('marketing.commons.ts_02d981'),
        { iconClass: 'el-icon-warning' },
      ).then(async () => {
        await this.deleteModel({
          id: modelData.id,
          sourceType: 1,
        })
        FxUI.Message.success($t('marketing.commons.sccg_43593d'))
        this.queryModels()
      })
    },
    // 完成拖动
    handleModelAfterDragged(e) {
      this.updateDisplayOrder({
        displayKey: 'MATERIAL_TAG_MODEL',
        displayItems: e.newList
          .concat(this.disabledModelList)
          .map(item => item.id),
        version: this.modelVersion,
        sourceType: 1,
      })
    },
    // 选中某模型
    handleClickModel(modelData) {
      this.setCurModel(modelData)
    },
    async handleSubmitDialog() {
      const res = await this.setOrCancelAllowAddTag({
        isAllow: this.isCustomize,
        sourceType: 1,
      })
      if (res && res.errCode === 0) {
        FxUI.Message.success($t('marketing.commons.szcg_f6088e'))
        this.queryIsAllowAddTag()
        this.isShowCustomize = false
      }
    },
    async queryIsAllowAddTag() {
      const res = await this.isAllowAddTag()
      if (res && res.errCode === 0) {
        this.isCustomize = res.data
      }
    },
    handleUpdateCreateDialogVisible() {
      this.isShowCreateDialog = false
      this.defaultModelData = {}
    },
  },
}
</script>

<style lang="less">
@basePath: "../../";
.model-list {
  .model--create {
    display: flex;
    justify-content: space-between;
    flex: 0 0 auto;
    padding-left: 20px;
    padding-right: 20px;
    line-height: 39px;
    color: var(--color-info06,#407FFF);
    border-bottom: 1px solid #e9edf5;
    & > div {
      cursor: pointer;
    }
  }
  .list__draggable-list {
    // min-height: 300px;
  }
  .model__item {
    height: 40px;
    display: flex;
    align-items: center;
    padding: 0 5px;
    width: 100%;
    box-sizing: border-box;
    cursor: pointer;
    &.cur {
      background: #f0f4fc;
    }
    &.disabled {
      color: #999;
    }
    &:hover {
      background: #f0f4fc;
      .item__dicon,
      .item__micon {
        visibility: visible;
      }
    }
    .item__dicon {
      flex-shrink: 0;
      visibility: hidden;
    }
    .item__name {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .item__count {
      flex-shrink: 0;
    }
    .item__tips {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 2px 7px;
      border-radius: 30px;
      overflow: hidden;
      color: #fff;
      font-size: 12px;
      transform: scale(0.8);
      flex-shrink: 0;
    }
    .item__micon {
      margin-left: auto;
      flex-shrink: 0;
      visibility: hidden;
    }
  }

  .icon_edit {
    display: inline-block;
    width: 16px;
    height: 16px;
    cursor: pointer;
    background: url("@{basePath}assets/images/tagsmanage/icon_edit.png") center
      no-repeat;
  }
  .icon_move {
    display: inline-block;
    width: 16px;
    height: 16px;
    cursor: pointer;
    background: url("@{basePath}assets/images/tagsmanage/icon_move.png")
      center/80% no-repeat;
  }
  .icon_expand {
    display: inline-block;
    width: 16px;
    height: 16px;
    cursor: pointer;
    background: url("@{basePath}assets/images/tagsmanage/icon_expand.png")
      center no-repeat;
    transform: rotate(0deg);
    transition-duration: 300ms;
    &.active {
      transform: rotate(90deg);
    }
  }
  .icon_more {
    display: inline-block;
    width: 16px;
    height: 16px;
    cursor: pointer;
    background: url("@{basePath}assets/images/tagsmanage/icon_more.png")
      center/100% no-repeat;
  }
}
.customize-title {
  margin-bottom: 18px;
  color: #181c25;
  font-size: 14px;
  .tip {
    margin-right: 10px;
  }
}
.customize-example {
  .title {
    margin-bottom: 9px;
    color: #545861;
    font-size: 12px;
  }
}
</style>
