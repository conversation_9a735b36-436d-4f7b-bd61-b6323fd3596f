import utils from "@/services/util/index";
import http from "@/services/http/index";
import { createMaterialDetailUrlByObjectType } from "@/utils/createMaterialDetailUrl";

export function materialType(objectType) {
  // objectType: 4为产品、6为文章、26为微页面..
  switch (objectType) {
    case 4:
      return $t('marketing.commons.cp_a01543');
    case 6:
      return $t('marketing.commons.wz_c75625');
    case 16:
      return $t('marketing.commons.bd_eee1e2');
    case 24:
      return $t('marketing.commons.hb_ff361e');
    case 26:
      return $t('marketing.commons.wym_5fd4fb');
    case 9999:
      return $t('marketing.commons.wbnr_6ecce8');
    default:
      return "--";
  }
}

export function contentType(objectType) {
  // 原始类型
  switch (objectType) {
    case 4:
      return 4;
    case 6:
      return 1;
    case 24:
      return 5;
    case 26:
      return 10; //特殊
    default:
      return objectType;
  }
}

export function materialPhoto(material) {
  // 因为不同type的图片字段不同，所以需要该中间件
  var defaultImg = require("@/assets/images/icons/default-content.jpg");
  switch (material.objectType) {
    case 4:
      return material.sharePicOrdinaryCutUrl || material.headPicsThumbs && material.headPicsThumbs[0] || defaultImg;
    case 6:
      return material.sharePicOrdinaryCutUrl || (material.photoThumbnailUrl
        ? material.photoThumbnailUrl
        : defaultImg);
    case 16:
      return material.sharePicOrdinaryCutUrl || material.headPhotoUrl || defaultImg;
    case 24:
      return material.bgThumbnailUrl ? material.bgThumbnailUrl : defaultImg;
    case 26:
      return material.coverUrl ? material.coverUrl : defaultImg;
    case 13:
      return material.coverImageThumbUrl ? material.coverImageThumbUrl : defaultImg;
    case 9999:
      return material.cover || defaultImg;
    default:
      return defaultImg;
  }
}

export function materialTitle(material) {
  switch (material.objectType) {
    case 4:
      return material.title || "";
    case 6:
      return material.title || "";
    case 16:
      return material.title || "";
    case 24:
      return material.title || "";
    case 26:
    case 9999:
      return material.name || "";
    case 'coupon':
      return material.title || "";
    default:
      return material.title || "";
  }
}

export function getTime(time) {
  if (!time) return "--";
  return `${utils.formatDateTime(time, "YYYY-MM-DD")}`;
}

export function getDetailTime(time) {
  if (!time) return "--";
  return `${utils.formatDateTime(time, "YYYY-MM-DD hh:mm")}`;
}

export function getName(id) {
  if (!id) return "--";
  const employee = FS.contacts.getEmployeeById(id);
  return (employee && employee.fullName) || "--";
}

export function getType(type) {
  switch (type) {
    case 1:
      return $t('marketing.commons.qytg_966c63');
    case 2:
      return $t('marketing.commons.gzhtg_556bb5');
    case 3:
      return $t('marketing.commons.dxtg_9a6fde');
    case 5:
      return $t('marketing.commons.qywxtg_726fb9');
    case 6:
      return $t('marketing.commons.yjtg_5f7157');
    case 7:
      return $t('marketing.commons.hbtg_e84b17');
    case 12:
      return $t('marketing.commons.tg_d9f3a8')
    case 13:
      return $t('marketing.commons.hytg_05ca56')
    default:
      return "--";
  }
}

export function getUrl(item, opts) {
  console.log(item, opts, 999999)
  opts = opts || {};
  var qrType = {
    "4": 10006,
    "6": 10005,
    "16": 10001,
    "26": 10010,
    "35": 10011,
    "9999": 19999
  }[item.material.objectType];
  // 海丽说去掉
  // var spreadFsUid = FS.contacts.getCurrentEmployee().employeeID;
  // var spreadFsUidParams = '';
  // if (opts.type === "3") {
  //   // 预览去掉推广人信息
  //   spreadFsUidParams = "";
  // }
  // var params = "";
  
  // if (item.material.objectType === 26) {
  //   params = `byshare=1&id=${item.material.id}&marketingEventId=${item.marketingEventId || ""}&marketingActivityId=${item.material.defaultMarketingActivityId || ""}${spreadFsUidParams}&type=${opts.type || "1"}`;
  // } else if (item.material.objectType === 16) {
  //   params = `formId=${item.material.id}&marketingEventId=${item.marketingEventId || ""}&marketingActivityId=${item.material.defaultMarketingActivityId || ""}${spreadFsUidParams}&needReport=true`;
  // } else if (item.material.objectType === 35) {
  //   params = `wxAppId=${item.wxAppId}&objectId=${item.objectId || ""}`;
  // } else if (item.material.objectType === 9999) {
  //   params = `id=${item.material.id}&marketingEventId=${item.marketingEventId || ""}&marketingActivityId=${item.material.defaultMarketingActivityId || ""}${spreadFsUidParams}`;
  // } else {
  //   params = `byshare=1&id=${item.material.id}&marketingEventId=${item.marketingEventId || ""}&marketingActivityId=${item.material.defaultMarketingActivityId || ""}${spreadFsUidParams}&qrType=${qrType}`;
  // }
  // var path = {
  //   "4": "/ec/cml-marketing/release/web/cml-marketing.html?",
  //   "6": "/ec/cml-marketing/release/web/cml-marketing.html?",
  //   "16": "/ec/h5-landing/release/form.html?",
  //   "26": "/ec/h5-landing/release/index.html?",
  //   "35": "/ec/h5-landing/release/coupons.html?",
  //   "9999": "/ec/h5-landing/release/external-content.html?"
  // }[item.material.objectType];
  // var hash = {
  //   "4": "&_hash=/cml/h5/spread_product_detail",
  //   "6": "&_hash=/cml/h5/article_detail",
  //   "16": "",
  //   "26": "",
  //   "35": "&_hash=detail",
  // }[item.material.objectType];
  // return (
  //   location.href.replace(/(\w+:\/\/[^/]+).*/, "$1") + `${path}${params}${hash || ""}`
  // );

  const urlParams = {
    id: item.material.id,
    marketingEventId: item.marketingEventId,
    marketingActivityId: item.material.defaultMarketingActivityId,
    wxAppId: item.wxAppId,
    ...(item.material.objectType === 16 ? { needReport: true } : {}),
    byshare: 1,
    qrType: qrType
  }
  if(item.material.siteType && item.material.siteType === 'EVEN_HOMEPAGE') {
    urlParams.targetObjectType = item.material.targetObjectType
    urlParams.targetObjectId = item.material.targetObjectId
  }
  return createMaterialDetailUrlByObjectType(item.material.objectType, urlParams).url;
}

export function aShortUrl(key, item, opts) {
  if (item[key] || item[key + "_fetching"]) {
    return;
  }
  item[key + "_fetching"] = 1;
  http
    .getShortUrl({ longUrl: getUrl(item, opts) })
    .then(res => {
      delete item[key + "_fetching"];
      if (res && res.errCode === 0) {
        item[key] = res.data.shortUrl;
      }
    })
    .catch(() => {
      delete item[key + "_fetching"];
    });
}

export function FS_util_getFscFileUrl(NPath) {
  return NPath ? FS.util.getFscFileUrl(NPath) : "";
  // img.src 禁止赋值空串，但高级浏览器没事
}
