<template>
  <div class="overviewWrap">
    <div class="statistical">
      <div class="statisticalItem" v-for="(i, index) in basicInformation" :key="index">
          <span class="des">{{i.des}}</span>
          <span class="num">{{i.num}}</span>
      </div>
    </div>
    <div class="contentWrap" v-loading="loading">
      <div class="contTop">
        <span class="textTitle">{{ $t('marketing.commons.hdlb_d4e63c') }}</span>
        <div class="optionWrapper">
          <div class="optionBtn" @click="enrollSyncRules">{{ $t('marketing.commons.bmsjzdtbgz_03e67b') }}</div>
          <div class="optionBtn" @click="associatedActive">+ {{ $t('marketing.commons.glzhd_6ca5c9') }}</div>
          <div class="optionBtn" @click="addActive">+ {{ $t('marketing.pages.content_marketing.xjzhd_3d2b09') }}</div>
        </div>
      </div>
      <div v-if="subMarketingEvents.length > 0" class="itemListWrap">
        <div class="itemList" v-for="(item, index) in subMarketingEvents" :key="index">
          <div class="avatar"  @click="activityTitleClick(item.eventForm, item.uniId)">

            <!--          多会场活动-->
            <div class="avatarWrap avatarWrapMoreMeeting" v-show="item.eventType == 'multivenue_marketing'">
              <img src="@/assets/images/icon/moreMeeting.png" alt="" />
              <p>{{item.fieldName}}</p>
            </div>
            <!--            直播-->
            <div class="avatarWrap avatarWrapLive" v-show="item.eventType == 'live_marketing'">
              <img src="@/assets/images/icon/live.png" alt="" />
              <p>{{item.fieldName}}</p>
            </div>
            <!--            会议营销-->
            <div class="avatarWrap avatarWrapMeeting" v-show="item.eventType == '3'">
              <img src="@/assets/images/icon/meeting.png" alt="" />
              <p>{{item.fieldName}}</p>
            </div>
            <!--            其他-->
            <div class="avatarWrap avatarWrapOther" v-show="item.eventType != 'multivenue_marketing' && item.eventType != 'live_marketing' && item.eventType != '3'">
              <img src="@/assets/images/icon/active.png" alt="" />
              <p>{{item.fieldName}}</p>
            </div>

          </div>
          <div class="itemCont"  @click="activityTitleClick(item.eventForm, item.uniId)">
            <p class="title">{{ item.name }}</p>
            <p class="time">
              {{ $t('marketing.commons.hdsj_9e62e9') }}{{ getTime(item.beginTime) }} -
              {{ getTime(item.endTime) }}
            </p>
            <p class="other" v-show="item.eventForm != 'live_marketing' && item.eventForm != 'conference_marketing'">{{ $t('marketing.commons.hdlx_8e63fd') }} {{ item.fieldName }}</p>
            <p class="other" v-show="item.eventForm == 'live_marketing'">{{ $t('marketing.commons.zblj_625d5a') }} {{ item.liveUrl || '- -' }}</p>
            <p class="other" v-show="item.eventForm == 'conference_marketing'">{{ $t('marketing.commons.hydd_efb7f9') }} {{ item.location || '— -' }}</p>

          </div>
          <div class="rightOptions">
<!--            只有会议有签到-->
            <div class="num" v-if="item.eventForm == 'conference_marketing'">
              <p class="p1">{{ item.signInCount || 0 }}</p>
              <p class="p2">{{ $t('marketing.commons.yqd_e81a9d') }}</p>
            </div>

            <div class="num" v-if="item.eventForm == 'live_marketing'">
              <p class="p1">{{ item.viewCount || 0 }}</p>
              <p class="p2">{{ $t('marketing.commons.ygk_b1489b') }}</p>
            </div>

<!--            占位-->
            <div class="num" v-if="item.eventForm != 'live_marketing' && item.eventForm != 'conference_marketing'">
              <p class="p1"></p>
              <p class="p2"></p>
            </div>
            <div class="num">
              <p class="p1">{{ item.enrollCount || 0}}</p>
              <p class="p2">{{ $t('marketing.commons.ybm_4166d8') }}</p>
            </div>
            <div class="options">
              <p class="p1" @click="activityTitleClick(item.eventForm, item.uniId)">{{ $t('marketing.pages.content_marketing.jrgl_d2c793') }}</p>
              <p class="p2" @click="removeBinding(item.id)">{{ $t('marketing.pages.content_marketing.jcbd_c7b01a') }}</p>

<!--              <Dropdown trigger="click">-->
<!--                <p class="p2"> ... </p>-->
<!--                <DropdownMenu slot="dropdown">-->
<!--                  <DropdownItem class="clearfix">-->
<!--                    解除绑定-->
<!--                  </DropdownItem>-->
<!--                  <fx-dropdown-item class="clearfix">-->
<!--                    统一报名-->
<!--                  </fx-dropdown-item>-->
<!--                </DropdownMenu>-->
<!--              </Dropdown>-->

            </div>

          </div>
        </div>
      </div>
      <div class="list__empty" v-else>
        <div class="empty__icon"></div>
        <div class="empty__text">{{ $t('marketing.commons.zwsj_21efd8') }}</div>
      </div>
    </div>

    <Choose
      v-if="visible"
      :visible="visible"
      @onSubmit="handleChooseCreateType"
      :items="marketingEventTypeLists"
      @onClose="visible = false"
    />
    <EnrollSyncRules
      v-if="activityMarketingDetail.marketingEvent.id"
      :marketingEventId="activityMarketingDetail.marketingEvent.id"
      :visible="EnrollSyncRulesVisible"
      isMultiEvent
      @close="EnrollSyncRulesVisible = false"
    />
  </div>
</template>
<script>
import http from "@/services/http/index";
import { mapState, mapActions,mapGetters } from "vuex";
import kisvData from "@/modules/kisv-data";
import EnrollSyncRules from "@/components/enroll-sync-rules/index";
import Choose from "../../../pages/marketing-calendar/components/choose.vue"; // 使用营销日历创建市场活动组件
import {jumpToMarketingEventDetailPage} from "../../marketing-calendar/utils";
import { requireAsync } from '@/utils/index';
import { getTime, getName } from "../func.js";
import _ from "lodash";



const marketingEventTypes = [
  {
    icon: require("@/assets/images/icons/activity-invit-icon.png"),
    title: $t('marketing.commons.xsyx_6c9e91'),
    value: "online_marketing",
    desc: $t('marketing.pages.content_marketing.jymbyhcjsj_5a1365'),
    checked: false
  },
  {
    icon: require("@/assets/images/icons/live-marketingevent-icon.png"),
    title: $t('marketing.commons.zxzb_21b908'),
    value: "live_marketing",
    desc: $t('marketing.commons.gkkzbzxslh_2f6737'),
    checked: false
  },
  {
    icon: require("@/assets/images/icons/meeting-icon.png"),
    title: $t('marketing.commons.hyyx_5f60fd'),
    value: "conference_marketing",
    desc: $t('marketing.commons.xxhdrslyth_12c498'),
    checked: true
  },
];

export default {
  name: 'multiEvent',
  components: {
    Choose,
    Message: FxUI.Message,
    Dropdown: FxUI.Dropdown,
    DropdownMenu: FxUI.DropdownMenu,
    DropdownItem: FxUI.DropdownItem,
    EnrollSyncRules
  },
  data(){
    return {
      kisvData: kisvData.datas,
      visible: false,
      marketingEventTypes,
      EnrollSyncRulesVisible: false,
      basicInformation: {
        promote: {
          des: $t('marketing.commons.tgcs_6848f9'),
          num: 0
        },
        access: {
          des: $t('marketing.commons.fwrc_ed9a0b'),
          num: 0
        },
        sign: {
          des: $t('marketing.pages.content_marketing.bmrc_3ebad7'),
          num: 0
        },
        signature: {
          des: $t('marketing.pages.content_marketing.qdrc_011df5'),
          num: 0
        },
        watch: {
          des: $t('marketing.pages.content_marketing.gkrc_9eaf94'),
          num: 0
        },
        playback: {
          des: $t('marketing.pages.content_marketing.hfrc_0c2f18'),
          num: 0
        },
        interactive: {
          des: $t('marketing.pages.content_marketing.hdrc_ea3b94'),
          num: 0
        }
      },
      subMarketingEvents: [],
      options: [],
      loading: false,
      contentMarketingMapping: []
    }
  },
  computed: {
    ...mapState("MarketingCalendar", {
      dates: "dates",
      MARKETING_TYPE: "MARKETING_TYPE"
    }),
    ...mapGetters('MarketingEventSet', ['marketSetFilters']),
    marketingEventTypeLists() {
      const { isPrivateCloud } = this.kisvData;
      //华为云隐藏直播新建入口
      if (isPrivateCloud) {
        return this.marketingEventTypes.filter(
          item => item.value !== "live_marketing"
        );
      }
      return this.marketingEventTypes;
    },
    activityMarketingDetail() {
      return this.$store.state.ActivityMarketing.activityMarketingDetail;
    }
  },
  mounted() {
    this.getDetail()

  },
  created() {
    this.getMarketingEventCommonSetting();
    this.getMarketingEventTypeField();
  },
  methods: {
    getMarketingEventCommonSetting() {
      http.getMarketingEventCommonSetting({ type: 0 }).then(res => {
        if (res && res.errCode == 0) {
          if(res.data.activityTypeMapping && res.data.activityTypeMapping.length > 0) {
            const contentMarketingMapping = res.data.activityTypeMapping.filter(item => {
              return item.activityType === 0
            })
            if(contentMarketingMapping && contentMarketingMapping.length > 0) {
              this.contentMarketingMapping = contentMarketingMapping[0].mapping
            }
          }
        }
      })
    },
    getMarketingEventTypeField() {
      http.getMarketingEventTypeField().then(res => {
        if (res && res.errCode == 0) {
          let _options = res.data
          this.options = _options;
        }
      });
    },
    getTime,
    getName,
    associatedActive() {
      requireAsync('crm-modules/components/pickselfobject/pickselfobject', (PickObject) => {
        const picker = new PickObject();
        picker.on('select', (value) => {
          this.selected = _.extend(value);
          console.log( this.selected, value, ' this.selected')
          let childArr = []
          this.selected.map(i => {
            childArr.push(i._id)
          })
          console.log(childArr, 'arr')
          this.relateSubMarketingEvent(childArr, picker)
          // picker.destroy();
        });
        picker.render({
          layout_type: 'list',
          include_layout: true,
          apiname: 'MarketingEventObj',
          isMultiple: true,
          zIndex: 3000,
          filters: [
            ...this.marketSetFilters,
          ],
        });
      });
    },
    enrollSyncRules() {
      if (
        this.activityMarketingDetail &&
        this.activityMarketingDetail.marketingEvent &&
        this.activityMarketingDetail.marketingEvent.id
      ) {
        this.EnrollSyncRulesVisible = true;
      }
    },
    relateSubMarketingEvent(subMarketingEventIds, picker) {
      http.relateSubMarketingEvent({
        marketingEventId: this.activityMarketingDetail.marketingEvent.id,
        subMarketingEventIds: subMarketingEventIds
      }).then(results => {
        if (results && results.errCode == 0) {
          FxUI.Message.success(
            $t('marketing.commons.glcg_55828c')
          );
          picker.destroy();
          this.getDetail()
        }
      });
    },
    addActive() {
      this.visible = true
    },
    async handleClickEventList(row) {
      jumpToMarketingEventDetailPage({
        marketingEventId: row.id,
        eventType: row.eventType || row.event_type,
        eventForm: row.eventForm || row.event_form,
        newlyCreated: row.new,
        $router: this.$router
      });
    },
    toAddConfenrence(data) {
      http.addConference({ marketingEventId: data._id }).then(results => {
        if (results && results.errCode == 0) {
          this.$router.push({
            name: "meeting-detail",
            params: { id: results.data.id }
          });
        }
      });
    },
    handleChooseCreateType(type) {
      let that = this;
      if (type == "conference_marketing") {
        this.$router.push({
          name: "meeting-marketing-create" ,
          query: {
            parent_id: this.activityMarketingDetail.marketingEvent.id,
            isSub: true
          }
        });
        return
      }
      const { date, year, month } = this.dates;
      this.visible = false;
      this.popoverTip = false;
      if (type === "live_marketing") {
        this.$router.push({
          name: "live-create",
          query: {
            parent_id: this.activityMarketingDetail.marketingEvent.id,
          }
        });
        return;
      }
      requireAsync('crm-modules/action/field/field', field => {
        CRM.api.add({
          apiname: 'MarketingEventObj',
          title: $t('marketing.pages.content_marketing.xjschdzhd_9a8379'),
          show_type: 'full',
          data: {
            event_form: type,
            event_type: this.contentMarketingMapping.length > 0 ? this.contentMarketingMapping[0].apiName : '',
            parent_id: this.activityMarketingDetail.marketingEvent.id,
            parent_id__r: this.activityMarketingDetail.marketingEvent.name,
            ...((date && { begin_time: new Date(year, month - 1, date) }) || {}),
          },
          Model: field.Model.extend({
            parse: function(res) {
              let _options = res.objectDescribe.fields.event_type.options;
              let submitOptions = [];
              _options.forEach(item => {
                that.contentMarketingMapping.forEach(i => {
                  if (item.value === i.apiName) {
                    submitOptions.push(item);
                  }
                });
              });
              console.log(submitOptions, ' submitOptions')
              res.objectDescribe.fields.event_type.options = submitOptions;
               // 因为目前市场活动新建event_form字段没支持到禁用能力  所以只展示当前活动类型 其他的都隐藏 
              res.objectDescribe.fields.event_form.options = res.objectDescribe.fields.event_form.options.filter(item =>item.value === type)
              return field.Model.prototype.parse.apply(this, arguments);
            }
          }),
          success: (_type, data) => {
            console.log(_type, data);
            this.getDetail()
            //会议绑定
            if (type == 'conference_marketing') {
              this.toAddConfenrence(data);
              return;
            }

            FxUI.Message.success(
              $t('marketing.commons.schdcjcgjx_66c6fa')
            );
            // this.handleClickEventList({
            //   id: data._id,
            //   new: true,
            //   event_type: data.event_type
            // });
          },
        });
      });

    },

    getDetail() {
      this.loading = true
      http.getMultiVenueMarketingEvent({ marketingEventId: this.activityMarketingDetail.marketingEvent.id }).then(res => {
        if (res && res.errCode == 0) {
          console.log(res.data.subMarketingEvents)
          this.subMarketingEvents = res.data.subMarketingEvents || []
          this.subMarketingEvents.length &&
          this.subMarketingEvents.forEach(element => {
            this.options.forEach(i => {
              if (element.eventType == i.apiName) {
                // element.fieldName = i.fieldName;
                element.fieldName = i.labelName;
              }
            });
            // 处理一下id 会议用会议ID 其他使用市场活动ID
            if(element.eventForm == 'conference_marketing') {
              element.uniId = element.uniqueId
            } else {
              element.uniId = element.id
            }
          });

          console.log(this.subMarketingEvents, 'subMarketingEvents')

          this.basicInformation.promote['num'] = res.data.spreadCount || 0
          this.basicInformation.access['num'] = res.data.pv || 0
          this.basicInformation.sign['num'] = res.data.enrollCount || 0
          this.basicInformation.signature['num'] = res.data.signInCount || 0
          this.basicInformation.watch['num'] = res.data.viewCount || 0
          this.basicInformation.playback['num'] = res.data.replayCount || 0
          this.basicInformation.interactive['num'] = res.data.chatCount || 0
          this.loading = false
        }
      });
    },

    removeBinding(id) {
      FxUI.MessageBox.confirm($t('marketing.pages.content_marketing.qdjcbd_1927fb'), $t('marketing.commons.ts_02d981'), {
        type: "warning"
      }).then(() => {
        http.unRelateSubMarketingEvent({ marketingEventId: id }).then(res => {
          if (res && res.errCode == 0) {
            FxUI.Message.success(
              $t('marketing.commons.jbcg_1c4385')
            );
            this.getDetail()
          }
        });
      });
    },
    activityTitleClick(type , id) {
      // this.$router.push({ name: "content-dashboard", params: { id } });
      let routeData
      if(type == 'conference_marketing') {
        // 会议
        routeData = this.$router.resolve({ name: "meeting-detail", params: { id } });
      } else if(type == 'live_marketing') {
        // 直播
        routeData = this.$router.resolve({ name: "live-dashboard", params: { id } });
      } else {
        // 线上营销
        routeData = this.$router.resolve({ name: "content-dashboard", params: { id }, query: { formType: 0 } });
      }
      window.open(routeData.href, '_blank');
    }
  }
}
</script>
<style scoped lang="less">
.overviewWrap {
  width: 100%;
  padding: 0 10px;
  box-sizing: border-box;
  .statistical {
    width: 100%;
    height: 100px;
    background: #ffffff;
    display: flex;
    padding: 26px 0;
    box-sizing: border-box;
    justify-content: space-around;
    .statisticalItem {
      display: flex;
      flex: 1;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      &:not(:last-child) {
        border-right: 1px solid #DEE1E8;
      }
      .des {
        color: #545861;
        font-size: 14px;
      }
      .num {
        color: #181C25;
        font-size: 20px;
      }
    }
  }
  .contentWrap {
    width: 100%;
    margin-top: 10px;
    height: 300px;
    min-width: 1000px;
    .contTop {
      width: 100%;
      height: 60px;
      line-height: 60px;
      margin-bottom: 1px;
      background: #ffffff;
      padding: 0 18px;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .textTitle {
        font-size: 16px;
      }
      .optionWrapper {
        display: flex;
      }
      .optionBtn {
        padding: 6px 15px;
        text-align: center;
        background: var(--color-primary06,#407FFF);
        line-height: 20px;
        border-radius: 2px;
        color: #ffffff;
        cursor: pointer;
      }
      .optionBtn + .optionBtn {
        margin-left: 10px;
      }
    }
    .itemListWrap {
      background: #F2F2F5;
    }
    .itemList {
      width: 100%;
      height: 100px;
      background: #ffffff;
      padding: 15px;
      box-sizing: border-box;
      margin-bottom: 1px;
      &:hover{
        background: #F6F9FC;
      }
      .avatar {
        width: 100px;
        height: 70px;
        background: #fff0b7;
        float: left;
        margin-right: 15px;
        .avatarWrap {
          width: 100px;
          height: 70px;
          float: left;
          margin-right: 12px;
          color: #ffffff;
          display: flex;
          justify-content: center;
          align-items: center;
          flex-direction: column;
          border-radius: 2px;
          img {
            width: 20px;
            height: 20px;
            margin-bottom: 3px;
          }
          p {
            font-weight: 800;
          }
        }
        .avatarWrapMoreMeeting {
          background: #368DFF;
        }
        .avatarWrapLive {
          background: #FFCA2B;
        }
        .avatarWrapMeeting {
          background: #40B6FF;
        }
        .avatarWrapOther {
          background: #A3D962 ;
        }
      }
      .itemCont {
        min-width: 420px;
        float: left;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        box-sizing: border-box;
        font-size: 12px;
        color: #545861;
        cursor: pointer;
        .title {
          font-size: 16px;
          color: #181C25;
        }
      }
      .rightOptions {
        width: 420px;
        height: 100%;
        float: right;
        display: flex;
        .num {
          width: 120px;
          border-right: 1px solid #DEE1E8;
          height: 35px;
          margin-top: 18px;
          display: flex;
          justify-content: center;
          align-items: center;
          flex-direction: column;
          p {
            color: #545861;
            font-size: 12px;
            span {
              color: #181C25;
              font-size: 16px;
            }
          }
        }
        .options {
          display: flex;
          flex: 1;
          justify-content: center;
          align-items: center;
          //flex-direction: column;
          .p1 {
            color: var(--color-info06,#407FFF);
            font-size: 12px;
            cursor: pointer;
          }
          .p2 {
            color: var(--color-info06,#407FFF);
            font-size: 12px;
            float: right;
            //margin-top: -12px;
            margin-left: 15px;
            cursor: pointer;

          }
        }
      }
    }
    .list__empty {
      flex: 1;
      display: flex;
      width: 100%;
      height: 100%;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: #fff;
      .empty__icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        min-width: 120px;
        height: 120px;
        background: url("../../../assets/images/no-data.png") center no-repeat;
        background-size: 100px;
        .empty-content {
          margin-top: 130px;
          color: #999;
        }
      }
      .empty__text {
        color: #666;
      }
    }
  }
}

</style>
