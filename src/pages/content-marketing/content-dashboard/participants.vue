<!-- 组件说明 -->
<template>
  <div :class="$style.activityMarketingParticipants">
    <ActivityLeadsTable
      :title="$t('marketing.commons.cyry_b13613')"
      ref="leadsTable"
      :class="$style.table"
      :marketingEventId="$route.params.id"
    >
      <template v-slot="dataDefalut">
        <ElButton
          v-show="activityMarketingDetail.marketingEvent.eventForm == 'multivenue_marketing'"
          size="mini"
          @click="synchronous(dataDefalut.selectedItems)"
          :disabled='dataDefalut.disabled'
        > {{ $t('marketing.commons.tbbmsjdzhd_339814') }} </ElButton>
      </template>

    </ActivityLeadsTable>

    <SynchronousSub
      :visible.sync="isShowSynchronousSubDialog"
      @submit="handleSynchronousSubSubmit"
      ref="synchronousSub"
      :marketingEventId="activityMarketingDetail.marketingEvent.id"
    >
    </SynchronousSub>
  </div>
</template>

<script>
import ActivityLeadsTable from "@/components/ActiveMemberTable/ActivityLeadsTable";

import SynchronousSub from "@/components/SynchronousSub";
import http from "@/services/http/index";



export default {
  components: {
ActivityLeadsTable,
ElButton: FxUI.Button,
SynchronousSub
},
  data() {
    return {
      isShowSynchronousSubDialog: false,
      selectSub: [], // 子活动
      personSelectedItems: [],
    };
  },
  computed: {
    activityMarketingDetail() {
      return this.$store.state.ActivityMarketing.activityMarketingDetail;
    }
  },
  methods: {
    synchronous(selectedItems) {
      this.isShowSynchronousSubDialog = true
      let personSelectedItems = []
      selectedItems.map(i => {
        personSelectedItems.push(i.campaignId)
      })
      this.personSelectedItems = personSelectedItems
    },

    handleSynchronousSubSubmit(selectSub) {
      console.log(selectSub, 'selectSub')
      this.selectSub = selectSub
      http.syncDataToSubMarketingEvent({
        marketingEventId:this.activityMarketingDetail.marketingEvent.id,
        subMarketingEventIds: this.selectSub,
        campaignIds: this.personSelectedItems
      }).then(results => {
        this.$refs.synchronousSub.loading_list = false
        if (results && results.errCode == 0) {
          FxUI.Message.success(
            $t('marketing.commons.tbcg_52b85c')
          );
          this.$refs.synchronousSub.handleClose()
        }
      })
    },

  },
  mounted() {},
  created() {},
  destroyed() {} //生命周期 - 销毁完成
};
</script>

<style lang="less" module>
.activityMarketingParticipants {
  margin: 0 10px 10px 10px;
  .table {
  }
}
</style>
