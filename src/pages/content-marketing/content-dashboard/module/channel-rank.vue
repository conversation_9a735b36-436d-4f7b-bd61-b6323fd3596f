<!-- 组件说明 -->
<template>
     <div
       v-loading="loading"
       class="content-marketing-channel-rank__wrapper"
     >
       <div class="title">
         <div class="title-content">
           {{ $t('marketing.pages.content_marketing.qdhqxsph_10977a') }}
         </div>
         <div class="update">
           <span
             v-if="dataPreparing"
             class="loading"
           >
             <loading class-name="icon" />
             {{ $t('marketing.commons.sjjsz_f32ed8') }}
           </span>
         </div>
         <div class="operate-btns">
           <div :class="['btn-item',activeIndex === 0 ? 'active' : '']" @click="queryConferenceChannelStatistic(0)">{{ $t('marketing.commons.fwcs_f0be64') }}</div>
           <div :class="['btn-item',activeIndex === 1 ? 'active' : '']" @click="queryConferenceChannelStatistic(1)">{{ $t('marketing.pages.content_marketing.fwyhs_ca3aba') }}</div>
           <div :class="['btn-item',activeIndex === 2 ? 'active' : '']" @click="queryMarketingEventChannelStatistics">{{ $t('marketing.commons.tjbd_4e3400') }}</div>
         </div>
       </div>
       <div class="content">
         <div
           v-show="showBar"
           ref="barchart"
           class="barchart"
         />
         <Empty
           v-show="!showBar"
           class="empty-wrapper"
           :title="$t('marketing.commons.zwsj_21efd8')"
         />
       </div>
     </div>
</template>

<script>
import * as echarts from 'echarts'
import http from '@/services/http/index.js'
import Empty from '@/components/common/empty.vue'
import loading from './loading.vue'

export default {
  components: {
    Empty,
    loading,
  },
  props: {
    marketingEventId: {
      type: String,
      default: '',
    },
    updateTick: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      loading: true,
      showBar: false,
      dataPreparing: false,
      activeIndex: 2
    }
  },
  watch: {
    marketingEventId() {
      this.handleUpdateData()
    },
    updateTick() {
      this.handleUpdateData()
    },
  },
  mounted() {
    this.queryMarketingEventChannelStatistics()
    window.addEventListener('resize', this.barResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.barResize)
    if (this.barchart) {
      this.barchart.dispose()
    }
  },
  methods: {
    initBar(barData) {
      if(!this.$refs.barchart) return;
      this.barchart = echarts.init(this.$refs.barchart)

      let option = {}
      
      if(this.activeIndex === 2) {
        // 堆叠图配置
        const xData = []
        const newUserData = []
        const oldUserData = []
        const totalData = []
        barData.forEach(item => {
          xData.push(item.channelName)
          newUserData.push(item.newUserCount)
          oldUserData.push(item.oldUserCount)
          totalData.push(0)
        })

        option = {
          color: ['#4DACFD'],
          grid: {
            top: 50,
            left: 20,
            right: 20,
            bottom: 40,
          },
          tooltip: {
            trigger: 'axis',
            order: 'seriesDesc',
          },
          legend: {
            show: true,
            icon: 'circle',
            itemWidth: 8,
            itemHeight: 8,
            top: 10,
            data: [
              { name: $t('marketing.pages.content_marketing.xkh_8759f4') },
              { name: $t('marketing.pages.content_marketing.lkh_c122f0') },
            ],
            selected: {
              [$t('marketing.pages.content_marketing.xkh_8759f4')]: true,
              [$t('marketing.pages.content_marketing.lkh_c122f0')]: true,
            },
          },
          xAxis: [{
            type: 'category',
            data: xData,
            axisLabel: {
              interval: 0,
              rotate: -15,
              formatter(value) {
                return value.length > 8 ? `${value.substring(0, 8)}...` : value
              },
            },
          }],
          yAxis: [{
            type: 'value',
            splitLine: {
              lineStyle: {
                color: '#DEE1E8',
                type: 'dashed',
              },
            },
          }],
          series: [
            {
              data: oldUserData,
              name: $t('marketing.pages.content_marketing.lkh_c122f0'),
              type: 'bar',
              stack: 'total',
              barMaxWidth: 60,
              color: '#6FDDA0',
            },
            {
              data: newUserData,
              name: $t('marketing.pages.content_marketing.xkh_8759f4'),
              type: 'bar',
              stack: 'total',
              barMaxWidth: 60,
              color: '#5498FF',
            },
            {
              data: totalData,
              name: 'totalData',
              type: 'bar',
              stack: 'total',
              barMaxWidth: 60,
              color: '#5498FF',
              tooltip: { show: false },
              label: {
                show: true,
                position: 'top',
                color: '#545861',
                fontSize: 10,
                formatter: params => {
                  const option = this.barchart.getOption()
                  const { legend } = option
                  const { selected } = legend[0]
                  const keys = Object.keys(selected).filter(key => selected[key])

                  const { dataIndex } = params
                  const values = keys.map(key => {
                    if (key === $t('marketing.pages.content_marketing.xkh_8759f4')) {
                      return newUserData[dataIndex]
                    }
                    if (key === $t('marketing.pages.content_marketing.lkh_c122f0')) {
                      return oldUserData[dataIndex]
                    }
                    return 0
                  })
                  return values.reduce((prev, cur) => prev + cur, 0)
                },
              },
            },
          ],
        }
      } else {
        // 普通柱状图配置
        option = {
          grid: {
            top: 50,
            left: 20,
            right: 20,
            bottom: 40,
          },
          tooltip: {
            trigger: 'axis'
          },
          xAxis: {
            type: 'category',
            data: barData.map(item => item.spreadChannel),
            axisLabel: {
              interval: 0,
              rotate: -15,
              formatter(value) {
                return value.length > 8 ? `${value.substring(0, 8)}...` : value
              },
            }
          },
          yAxis: {
            type: 'value',
            splitLine: {
              lineStyle: {
                color: '#DEE1E8',
                type: 'dashed',
              },
            }
          },
          series: [{
            type: 'bar',
            data: barData.map(item => item.count),
            color: '#6FDDA0',
            barMaxWidth: 60,
            label: {
              show: true,
              position: 'top',
              color: '#545861',
              fontSize: 10
            }
          }]
        }
      }
      
      this.barchart.setOption(option)
    },
    queryMarketingEventChannelStatistics() {
      this.activeIndex = 2
      if (!this.marketingEventId) {
        return
      }

      http
        .queryMarketingEventChannelStatistics({
          marketingEventId: this.marketingEventId,
        })
        .then(res => {
          this.loading = false
          this.dataPreparing = false
          if (res && res.errCode === 0) {
            if (res.data && res.data.length) {
              this.showBar = true
              this.$nextTick(() => {
                this.initBar(res.data)
              })
            } else {
              this.showBar = false
            }
          }

          if (res && res.errCode === 930001) {
            this.dataPreparing = true
          }
        })
        .catch(() => {
          this.loading = false
        })
    },
    barResize() {
      if (this.barchart) {
        this.barchart.resize()
      }
    },
    handleUpdateData() {
      this.loading = true
      const activeIndex = this.activeIndex
      if(activeIndex === 2){
        this.queryMarketingEventChannelStatistics()
      } else {
        this.queryConferenceChannelStatistic(activeIndex)
      }
    },
    async queryConferenceChannelStatistic(type) {
      this.activeIndex = type || 0
      if (!this.marketingEventId) {
        return
      }
      // type: 访问次数 0  访问用户数 1
      const { errCode,data } = await http.queryConferenceChannelStatistic({
        conferenceId: this.marketingEventId,
        type: type || 0
      }) || {}
      this.loading = false
      this.dataPreparing = false
      if(errCode === 0 && data) {
        this.showBar = true
        this.$nextTick(() => {
          this.initBar(data)
        })
      } else {
        this.showBar = false
      }
    },
  },
}
</script>

<style lang="less" scoped>
.content-marketing-channel-rank__wrapper {
  background-color: #ffffff;
  box-sizing: border-box;
  margin-bottom: 12px;
  padding: 20px 16px;
  position: relative;
  height: 305px;
  display: flex;
  border-radius: 8px;
  flex-direction: column;
  .title {
    color: #181c25;
    font-size: 14px;
    line-height: 20px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .title-content {
      display: flex;
      align-items: center;
    }

    .update {
      display: flex;
      align-items: center;
      color: #91959E;
      font-size: 12px;

      .loading {
        display: flex;
        align-items: center;
        margin-right: 10px;

        .icon {
          margin-right: 10px;
        }
      }
    }

    .operate-btns{
      display: flex;
      gap: 8px;
      .btn-item{
        padding: 2px 8px;
        border-radius: var(--4, 4px);
        background: var(--color-special01);
        color: #545861;
        font-size: 12px;
        cursor: pointer;
        font-weight: normal;
        &.active{
          color: var(--color-primary06);
          background: var(--color-primary01);
        }
      }
    }
  }

  .content {
    flex: 1;
    height: 248px;

    .barchart, .empty-wrapper {
      height: 100%;
      box-sizing: border-box;
    }
  }
}
</style>
