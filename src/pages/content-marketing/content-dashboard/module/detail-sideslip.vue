<template>
  <sideslip-popup
    class="detail-sideslip"
    :visible="visible"
    width="900px"
    @close="handleCloseDetail"
  >
    <div
      v-show="radarLoading || clueLoading"
      class="km-g-loading-mask"
    >
      <span class="loading" />
    </div>
    <template v-if="showRadarDetail">
      <!-- 推广雷达 -->
      <radar-detail
        :marketing-event-id="marketingEventId"
        :show-radar-detail="showRadarDetail"
        :material-id="message.material.id"
        :material-name="materialTitle(message.material)"
        :material-type="message.material.objectType"
        @close="closeRadarDetail"
      />
    </template>
    <template v-else-if="showCluesDetail">
      <!-- 线索 -->
      <clues-detail
        :marketing-event-id="marketingEventId"
        :show-clues-detail="showCluesDetail"
        :material="message"
        :show-type="showType"
        @close="closeCluesDetail"
      />
    </template>
    <template v-else>
      <div class="detail-header">
        <div
          class="header-photo"
          :style="{
            backgroundImage: `url('${materialPhoto(message.material)}')`
          }"
        />
        <div class="header-message">
          <div class="header-title">
            {{ materialTitle(message.material) }}
          </div>
          <div class="header-sub">
            <span style="margin-right: 16px;">{{ $t("marketing.commons.cjz_ec37bb")
            }}{{
              message.material.creatorName ||
                getName(message.material.creator)
            }}</span>
            <span>{{ $t("marketing.commons.gxsj_780fb9")
            }}{{ getTime(message.material.updateTime) }}</span>
          </div>
        </div>
        <div class="header-tool">
          <!-- 表单侧滑去掉预览 -->
          <fx-button
            v-if="message.material"
            type="primary"
            size="small"
            @click="previewMaterial"
          >
            {{ $t("marketing.commons.yl_645dbc") }}
          </fx-button>
          <fx-button
            v-if="
              !isSignUp &&
                (message.material.objectType === 26 ||
                  message.material.objectType === 16)
            "
            size="small"
            :disabled="isReadonly"
            @click="setMaterial"
          >
            {{ $t("marketing.commons.sz_e366cc") }}
            <fx-popover
              v-if="
                message.material.formUsage !== 2 && (!message.material.hadCrmMapping && message.material.formId)
              "
              placement="bottom"
              trigger="hover"
            >
              <span class="hover-tips" v-if="message.material.formId">
                {{
                  $t("marketing.commons.hwwcxsszzs_79b315")
                }}
              </span>
              <i
                slot="reference"
                class="el-icon-warning-outline el-icon--right"
                style="color: #FF3F3F;"
              />
            </fx-popover>
          </fx-button>
          <fx-button
            size="small"
            :disabled="isReadonly"
            @click="editMaterial"
          >
            {{
              $t("marketing.commons.bj_95b351")
            }}
          </fx-button>
          <fx-button
            v-show="message.material.objectType !== 26"
            size="small"
            :disabled="isReadonly"
            @click="deleteRelation"
          >
            {{ $t("marketing.commons.sc_2f4aad") }}
          </fx-button>
          <fx-dropdown
            v-show="!isSignUp && message.material.objectType === 26"
            @command="handleMore"
          >
            <fx-button size="small">
              {{ $t("marketing.commons.gd_0ec9ea") }}
              <i class="el-icon-arrow-down el-icon--right" />
            </fx-button>
            <fx-dropdown-menu slot="dropdown">
              <fx-dropdown-item
                v-if="message.material.objectType == 26 && message.material.fileType"
                :disabled="isReadonly || disabledFileUpload"
              >
                <FileToHexagon
                  :marketing-event-id="marketingEventId"
                  :hexagon-site-id="message.material.id"
                  :systemSite="1"
                  @completed="handleFileToHexagonCompleted"
                  @onUpload="val => disabledFileUpload = val"
                >
                  {{ $t('marketing.commons.zxsc_bc8851') }}
                </FileToHexagon>
              </fx-dropdown-item>
              <fx-dropdown-item
                command="copy"
                :disabled="isReadonly"
              >
                {{
                  $t("marketing.commons.fz_79d3ab")
                }}
              </fx-dropdown-item>
              <fx-dropdown-item
                v-if="message.material.system && message.material.siteType !== 'EVEN_HOMEPAGE'"
                :disabled="isReadonly"
                command="public"
              >
                {{ $t("marketing.commons.gk_b6a098") }}
              </fx-dropdown-item>
              <fx-dropdown-item
                command="delete"
                v-if="message.material.siteType !== 'EVEN_HOMEPAGE'"
                :disabled="isReadonly"
              >
                {{
                  $t("marketing.commons.sc_2f4aad")
                }}
              </fx-dropdown-item>
            </fx-dropdown-menu>
          </fx-dropdown>
        </div>
      </div>

      <div class="detail-body">
        <div class="body-item">
          <div class="item-title">
            <span>{{ $t("marketing.commons.sjtj_1b7cba") }}</span>
          </div>
          <div class="data-account">
            <div class="account-item">
              <div class="account-num">
                {{ message.pv }}
              </div>
              <div class="account-text">
                {{ $t("marketing.commons.fwcs_f0be64") }}
              </div>
            </div>
            <div class="account-item">
              <div class="account-num">
                {{ message.uv }}
              </div>
              <div class="account-text">
                {{ $t("marketing.commons.fwrs_c3c959") }}
              </div>
            </div>
            <div class="account-item">
              <div class="account-num">
                {{ message.leadNum }}
              </div>
              <div class="account-text">
                {{ $t('marketing.pages.content_marketing.ztjs_3d90db') }}
              </div>
            </div>
          </div>
        </div>
        <!-- <div class="body-item">
          <div class="item-title">
            <span>推广海报</span>
          </div>
          <div class="poster-content">
            <div class="poster-block poster-create" @click="createPoster">
              <img class="poster-create-img" :src="require('@/assets/images/icons/add.png')" />
              <div class="poster-create-text">添加海报</div>
            </div>
            <div
              class="poster-block"
              v-for="(item, index) in posterList"
              :key="index"
              @mouseover="showGrey(index)"
              @mouseout="showGrey(-1)"
            >
              <img class="poster-img" :src="item.qrPosterThumbnailUrl" />
              <div class="poster-grey" v-show="posterHover === index">
                <a
                  class="poster-grey-button"
                  target="_blank"
                  :href="FS_util_getFscFileUrl(item.qrPosterThumbnailApath)"
                  :download="item.title"
                >下载</a>
                <div class="poster-grey-button" @click="previewPoster(item)">预览</div>
                <div class="poster-grey-button" @click="deletePoster(item.qrPosterId)">删除</div>
              </div>
            </div>
          </div>
        </div>-->
        <div
          v-if="radarDatas.length > 0"
          class="body-item"
        >
          <div class="item-title">
            <span>{{ $t("marketing.commons.tgld_803716") }}</span>
          </div>
          <div class="content-radar">
            <div class="radar-list">
              <div
                v-for="(item, index) in radarDatas.filter((data, i) => i < 3)"
                :key="index"
                class="radar-item"
                @click.stop="handleShowRadarDetail(item)"
              >
                <div class="radar-message">
                  <div class="radar-title">
                    {{ item.spreadContent }}
                  </div>
                  <div class="radar-sub">
                    <span>{{ getDetailTime(item.createTime) }}</span>
                    <span>{{
                      $t("marketing.commons.fq_21801e", {
                        data: { option0: getType(item.spreadType) }
                      })
                    }}</span>
                  </div>
                </div>
                <div
                  v-if="item.spreadType === 1 || item.spreadType === 13"
                  class="radar-counter"
                >
                  <!-- 全员 -->
                  <div class="radar-visitNum">
                    <div class="counter-num">
                      {{
                        item.marketingActivityNoticeSendStatisticResult
                          .lookUpUserCount || 0
                      }}
                    </div>
                    <div class="counter-text">
                      {{ $t("marketing.commons.fwrc_ed9a0b") }}
                    </div>
                  </div>
                  <div class="radar-cluesNum">
                    <div class="counter-num">
                      {{
                        item.marketingActivityNoticeSendStatisticResult
                          .leadAccumulationCount || 0
                      }}
                    </div>
                    <div class="counter-text">
                      {{ $t("marketing.commons.hqxs_a65930") }}
                    </div>
                  </div>
                </div>
                <div
                  v-if="item.spreadType === 7"
                  class="radar-counter"
                >
                  <!-- 伙伴推广 -->
                  <div class="radar-visitNum">
                    <div class="counter-num">
                      {{
                        (item.marketingActivityPartnerSendStatisticResult &&
                          item.marketingActivityPartnerSendStatisticResult
                            .lookUpUserCount) ||
                          0
                      }}
                    </div>
                    <div class="counter-text">
                      {{ $t("marketing.commons.fwrc_ed9a0b") }}
                    </div>
                  </div>
                  <div class="radar-cluesNum">
                    <div class="counter-num">
                      {{
                        (item.marketingActivityPartnerSendStatisticResult &&
                          item.marketingActivityPartnerSendStatisticResult
                            .leadAccumulationCount) ||
                          0
                      }}
                    </div>
                    <div class="counter-text">
                      {{ $t("marketing.commons.hqxs_a65930") }}
                    </div>
                  </div>
                </div>
                <div
                  v-if="item.spreadType === 2"
                  class="radar-counter"
                >
                  <!-- 公众号 -->
                  <div class="radar-visitNum">
                    <div class="counter-num">
                      {{ item.uv || 0 }}
                    </div>
                    <div class="counter-text">
                      {{ $t("marketing.commons.fwrc_ed9a0b") }}
                    </div>
                  </div>
                  <div class="radar-cluesNum">
                    <div class="counter-num">
                      {{ item.leadCount || 0 }}
                    </div>
                    <div class="counter-text">
                      {{ $t("marketing.commons.hqxs_a65930") }}
                    </div>
                  </div>
                </div>
                <div
                  v-if="item.spreadType === 3"
                  class="radar-counter"
                >
                  <!-- 短信 -->
                  <div class="radar-visitNum">
                    <div class="counter-num">
                      {{ item.uv || 0 }}
                    </div>
                    <div class="counter-text">
                      {{ $t("marketing.commons.fwrc_ed9a0b") }}
                    </div>
                  </div>
                  <div class="radar-cluesNum">
                    <div class="counter-num">
                      {{
                        item.marketingActivitySmsSendStatisticResult
                          .actualSend || 0
                      }}
                    </div>
                    <div class="counter-text">
                      {{ $t("marketing.commons.fscg_9db9a7") }}
                    </div>
                  </div>
                </div>
                <div
                  v-if="item.spreadType === 5"
                  class="radar-counter"
                >
                  <!-- 企业微信 -->
                  <div class="radar-visitNum">
                    <div class="counter-num">
                      {{
                        item.marketingActivityQywxGroupSendMessageResult
                          .lookUpUserCount || 0
                      }}
                    </div>
                    <div class="counter-text">
                      {{ $t("marketing.commons.fwrc_ed9a0b") }}
                    </div>
                  </div>
                  <div class="radar-cluesNum">
                    <div class="counter-num">
                      {{
                        item.marketingActivityQywxGroupSendMessageResult
                          .leadAccumulationCount || 0
                      }}
                    </div>
                    <div class="counter-text">
                      {{ $t("marketing.commons.hqxs_a65930") }}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div
              v-if="radarDatas.length > 3"
              class="radar-more"
            >
              <span @click.stop="showRadarDetail = true">{{
                $t("marketing.commons.ckgd_90ef7c")
              }}</span>
            </div>
          </div>
        </div>
        <div
          v-if="message.material.objectType === 26 && message.material.formId"
          class="body-item"
        >
          <OrderTable
            :object-id="message.material.formId"
            :marketing-event-id="marketingEventId"
            @loadMore="
              showCluesDetail = true;
              showType = 'order';
            "
          />
        </div>
        <div class="body-item">
          <div class="item-title">
            <span>{{ $t('marketing.commons.tjsjmx_845319') }}</span>
            <div class="title-end">
              <span
                v-if="!isSignUp && showImportButton"
                :class="['output-table', isReadonly ? 'btn-disabled' : '']"
                @click="importClues"
              >
                <i class="iconfont output-icon">&#xe6c8;</i>{{ $t("marketing.commons.dr_8d9a07") }}</span>
              <span
                class="output-table"
                @click="exportClues"
              >
                <i class="iconfont output-icon">&#xe6c7;</i>{{ $t("marketing.commons.dc_55405e") }}</span>
            </div>
          </div>
          <div class="btn-wrapper" v-if="message.material.objectType === 26">
            <fx-button
              type="medium"
              size="mini"
              :disabled="!(selectedLeadNumber > 0)"
              @click="saveToClue"
            >
              {{ $t('marketing.commons.zxcr_d56b3f') }}
            </fx-button>
            <fx-button
              type="medium"
              size="mini"
              :disabled="!(selectedLeadNumber > 0)"
              :loading="deleteLoading"
              @click="handleDeleteClues"
            >
              {{ $t('marketing.commons.sc_2f4aad') }}
            </fx-button>
          </div>
          <div class="clue-detail">
            <v-table
              class="tablewbg"
              ref="clueTable"
              tid="content-detail-table"
              :settable="true"
              :filter-option="false"
              :data="clueData"
              :columns="clueColumns"
              :row-style="{ cursor: 'pointer' }"
              @custom:cule-action="handleClueDetail"
              @click:row="handleClueRow"
              @selection-change="selectionChange"
            />
            <div
              v-if="clueTotal > 2"
              class="table__more"
            >
              <a
                href="javascript: void(0);"
                @click.stop="showCluesDetail = true"
              >{{ $t("marketing.commons.ckgd_90ef7c") }}</a>
            </div>
          </div>
        </div>
        <MarketPromotionDetails
          class="content_marketing_promotion_detail"
          :params="detailParams"
          :append-to-body="true"
          :visible="detailVisible"
          @close="detailVisible = false"
        />
      </div>
    </template>
    <!-- 创建海报 -->
    <qrposter-dialog
      v-if="showCreateQrposterDialog"
      :visible.sync="showCreateQrposterDialog"
      :marketing-event-id="marketingEventId"
      :marketing-event-name="marketingEventName"
      :marketing-activity-id="message.material.defaultMarketingActivityId"
      :object-id="message.material.id"
      :object-type="message.material.objectType"
      :lock-jump-type="true"
      @update:list="handleQrposterCreated"
    />
    <!-- 设置微页面 -->
    <SiteSettingDialog
      :visible.sync="settingDialogVisible"
      :site-id="curSiteId"
      :form-id="curFormId"
      :site-setting-type="!fromSpread ? 3 : message.material.siteType === 'EVEN_HOMEPAGE' ? 1 : 2"
      :form-usage="message.material.formUsage"
      :is-apply-object="message.material.isApplyObject"
      :relation-id="message.material.relationId"
      show-activity-member-set
      @update:submit="handleSettingSubmit"
    />
    <copy-site-dialog
      v-if="siteNameDialogVisible"
      :material="message.material"
      :marketing-event-id="marketingEventId"
      :default-site-info="{
        name: `${message.material.title}-${$t('marketing.commons.fb_0428b3')}`
      }"
      :visible.sync="siteNameDialogVisible"
      @refresh="handleCopySiteRefresh"
    />
    <formImportDialog
      :visible.sync="showFormImportDialog"
      :form-id="importFormId"
      :marketing-activity-id="message.material.defaultMarketingActivityId"
      :marketing-event-id="marketingEventId"
      :object-id="message.material.id"
      :object-type="message.material.objectType"
      @importSuccess="handleImportSuccess"
    />
    <ExternalContentCreate
      v-if="externalContentVisible"
      :id="message.material.id"
      :visible.sync="externalContentVisible"
    />
  </sideslip-popup>
</template>

<script>
import { mapActions } from 'vuex'
import SideslipPopup from '@/components/sideslip-popup/index.vue'
import VTable from '@/components/table-ex/index.vue'
import RadarDetail from './detail-sideslip/radar-detail.vue'
import CluesDetail from './detail-sideslip/clues-detail.vue'
import CopySiteDialog from './copy-site-dialog.vue'
import QrposterDialog from '@/pages/poster-gallery/create/qrposter.vue'
import SiteSettingDialog from '@/pages/site/site-setting-dialog/index.vue'
import formDataDetail from '@/store/modules/promotion-activity/detail.js'
import MarketPromotionDetails from '@/components/market-promotion-details/index.vue'
// import SiteInfoDialog from "@/pages/site/site-info-dialog";
import OrderTable from '@/components/materiel-sideslip-detail/components/order-table/index.vue'
import formImportDialog from '@/components/form-import-dialog/index.vue'
import ExternalContentCreate from '@/pages/external-content/components/external-content-create.vue'
import { confirm } from '@/utils/globals.js'
import util from '@/services/util/index.js'
import http from '@/services/http/index.js'
import FileToHexagon from '@/components/file-to-hexagon/index.vue'

import {
  getTime,
  getDetailTime,
  getName,
  getType,
  materialType,
  materialTitle,
  materialPhoto,
  // eslint-disable-next-line camelcase
  FS_util_getFscFileUrl,
} from '../../func.js'

const defaultMaterial = {
  leadNum: 0,
  posterNum: 0,
  pv: 0,
  uv: 0,
  qrPostIdList: [],
  material: {
    createTime: '',
    defaultMarketingActivityId: '', // 营销活动id
    id: '',
    objectType: 0,
    updateTime: '',
    title: '',
  },
}

export default {
  components: {
    SideslipPopup,
    VTable,
    QrposterDialog,
    RadarDetail,
    CluesDetail,
    SiteSettingDialog,
    MarketPromotionDetails,
    CopySiteDialog,
    ElDropdown: FxUI.Dropdown,
    ElDropdownMenu: FxUI.DropdownMenu,
    ElDropdownItem: FxUI.DropdownItem,
    OrderTable,
    formImportDialog,
    ExternalContentCreate,
    FileToHexagon,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    material: {
      type: Object,
      default: () => ({}),
    },
    marketingEventName: {
      type: String,
      default: '',
    },
    marketingEventId: {
      type: String,
      default: '',
    },
    /**
     * 特殊字段，会议报名微页面展示用到，可以不理会
     */
    isSignUp: {
      type: Boolean,
      default: false,
    },
    isReadonly: {
      type: Boolean,
      default: false,
    },
    // 推广侧滑
    fromSpread: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      importFormId: '',
      selectedLeadNumber: 0,
      selectedLeads: [],
      showRadarDetail: false,
      showCluesDetail: false,
      showType: '',
      settingDialogVisible: false,
      curSiteId: '',
      curFormId: '',

      posterHover: '',
      posterList: [],
      showCreateQrposterDialog: false,

      radarDatas: [],
      radarLoading: true,

      clueTotal: 0,
      clueColumns: [],
      clueData: [],
      clueLoading: true,
      detailParams: {},
      detailVisible: false,
      siteNameDialogVisible: false,
      showFormImportDialog: false,
      externalContentVisible: false,
      showImportButton: true,
      deleteLoading: false,
      disabledFileUpload: false,
    }
  },
  computed: {
    message() {
      console.log('侧滑页', this.material)
      const { objectType, id } = this.material.material || {}
      if (objectType === 16) {
        this.material.material.formId = id
      }
      return { ...defaultMaterial, ...this.material }
    },
  },
  watch: {
    message(val) {
      console.log('watch')
      this.showRadarDetail = false
      this.showCluesDetail = false
      this.getRadars()
      this.getClues()
    },
  },
  mounted() {
    this.getBingFormByObject()

    // 会议微页面需要加载一下设置详情，用于判断是否隐藏导入按钮
    if (this.message.material.objectType === 16 || this.message.material.objectType === 26) {
      this.getFormDataById()
    }
  },
  created() {
    // this.marketingEventId = this.$route.params.id;
    this.showRadarDetail = false
    this.showCluesDetail = false
    this.getRadars()
    this.getClues()
  },
  methods: {
    // eslint-disable-next-line camelcase
    FS_util_getFscFileUrl,
    materialType,
    materialPhoto,
    materialTitle,
    getTime,
    getDetailTime,
    getName,
    getType,
    handleFileToHexagonCompleted() {
      this.$emit('uploadSuccess')
    },
    handleImportSuccess() {
      this.getClues()
    },
    importClues() {
      if (this.isReadonly) return
      if (!this.importFormId) {
        FxUI.Message.warning($t('marketing.commons.wbdbdwfdrb_7a6c79'))
        return
      }
      this.showFormImportDialog = true
    },
    handleShowRadarDetail(item) {
      this.detailVisible = true
      this.detailParams = {
        id: item.marketingActivityId,
      }
    },
    saveToClue() {
      confirm(
        $t('marketing.commons.jhdsxcrsbd_fedc65'),
        $t('marketing.commons.zxcrxs_f70585'),
        {},
      ).then(
        () => {
          const ids = this.getIDs()
          http.reImportDataToCrm({ ids }).then(results => {
            if (results && results.errCode === 0) {
              FxUI.MessageBox.alert($t('marketing.commons.zxcrknxyjz_9b0350'), $t('marketing.commons.ts_02d981'), {
                confirmButtonText: $t('marketing.commons.zdl_ce2695'),
              })
            }
          })
        },
        () => {},
      )
    },
    getIDs() {
      const { selectedLeads } = this
      const ids = []
      selectedLeads.forEach(item => {
        ids.push(item.id)
      })
      return ids
    },
    selectionChange(val) {
      this.selectedLeadNumber = (val && val.length) || 0
      this.selectedLeads = val
    },
    clearTableSelection(){
      this.selectedLeadNumber = 0
      this.selectedLeads = []
      this.$refs.clueTable.clearSelection()
    },
    previewMaterial() {
      // 回调查看预览
      this.$emit('previewMaterial', this.message)
    },
    setMaterial() {
      this.curFormId = this.message.material.formId
      if (this.message.material.objectType === 26) {
        this.curSiteId = this.message.material.id
      }

      this.settingDialogVisible = true
    },
    handleSettingSubmit() {
      // 设置微页面的回调
      // FxUI.Message({
      //   message: $t('marketing.commons.szcg_f6088e'),
      //   type: 'success',
      // })
      this.message.material.hadCrmMapping = true
    },
    editMaterial() {
      let name = ''
      const params = {}
      switch (this.message.material.objectType) {
        case 4:
          name = 'product-detail'
          params.pid = this.message.material.id
          break
        case 6:
          name = 'article-edit'
          params.id = this.message.material.id
          break
        case 16:
          name = 'form-edit'
          params.id = this.message.material.id
          break
        case 26:
          name = 'site-design'
          params.siteId = this.message.material.id
          break
        case 9999:
          this.externalContentVisible = true
          return
        default:
          return
      }
      console.log(name, params)
      window.open(
        this.$router.resolve({
          name,
          params,
          query: {
            from: 'dialog',
            marketingEventId: this.marketingEventId,
          },
        }).href,
        '_blank',
      )
    },
    deleteRelation() {
      if (this.message.leadNum || this.message.pv || this.message.uv) {
        FxUI.Message.error(
          $t('marketing.pages.content_marketing.scnryytgsj_55a8cd'),
        )
        return
      }
      confirm(
        $t('marketing.commons.sfqrsc_59c27b'),
        $t('marketing.commons.ts_02d981'),
        {
          confirmButtonText: $t('marketing.commons.qd_38cf16'),
          cancelButtonText: $t('marketing.commons.qx_625fb2'),
          type: 'warning',
        },
      )
        .then(() => {
          http
            .contentMarketingDeleteRelation({
              marketingEventId: this.marketingEventId,
              objectId: this.message.material.id,
              objectType: this.message.material.objectType,
            })
            .then(res => {
              if (res && res.errCode === 0) {
                FxUI.Message({
                  message: $t('marketing.commons.sccg_43593d'),
                  type: 'success',
                })
                this.handleCloseDetail(true)
              } else {
                FxUI.Message.error($t('marketing.commons.scsb_acf066'))
              }
            })
        })
        .catch(() => {})
    },
    handleCopySiteRefresh() {
      this.$emit('close', true)
    },
    handleCopySite() {
      console.log('handleCopySite')
      this.siteNameDialogVisible = true
    },
    handleMore(command) {
      console.log('handleMore', command)
      if (command === 'copy') {
        this.handleCopySite()
      } else if (command === 'delete') {
        this.deleteRelation()
      } else if (command === 'public') {
        // 设置微页面为公开状态
        confirm(
          $t('marketing.pages.content_marketing.swtywymhdq_b92ca6'),
          $t('marketing.pages.content_marketing.swtywym_6e0f69'),
          {},
        ).then(
          () => {
            http
              .setHexagonSystemStatus({
                hexagonSiteId: this.message.material.id,
                system: false,
              })
              .then(results => {
                if (results && results.errCode == 0) {
                  this.message.material.system = false
                  FxUI.Message.success($t('marketing.commons.szcg_f6088e'))
                } else {
                  FxUI.Message.error(
                    results.errMsg || $t('marketing.commons.szsb_9f9603'),
                  )
                }
              })
          },
          () => {},
        )
      }
    },

    // 海报相关
    showGrey(index) {
      // 海报悬浮
      this.posterHover = index
    },
    createPoster() {
      this.showCreateQrposterDialog = true
    },
    ...mapActions('MarketingCalendar/Dashboard', [
      'queryListMaterials',
      'addMaterial',
    ]),
    handleQrposterCreated() {
      // 要用vuex来关闭该组件
      this.queryListMaterials({
        id: this.marketingEventId,
      })
      this.$emit('reflesh')
    },
    previewPoster(poster) {
      // 展示海报预览
      this.$emit('previewPoster', poster, this.message)
    },
    deletePoster(id) {
      http
        .deleteQRPoster({
          qrPosterId: id,
        })
        .then(res => {
          if (res && res.errCode === 0) {
            FxUI.Message({
              message: $t('marketing.commons.sccg_43593d'),
              type: 'success',
            })
            this.$emit('reflesh')
          } else {
            FxUI.Message.error($t('marketing.commons.scsb_acf066'))
          }
        })
    },

    getRadars() {
      // 获取雷达，并根据长度>3判断是否有查看更多按钮
      this.radarLoading = true
      http
        .getRadarList({
          marketingEventId: this.marketingEventId,
          objectId: this.message.material.id,
          objectType: this.message.material.objectType,
          pageNum: 1,
          pageSize: 10,
        })
        .then(res => {
          if (res && res.errCode === 0) {
            this.radarDatas = res.data.result
            this.radarLoading = false
          }
        })
    },
    getClues() {
      // 获取线索
      this.clueLoading = true
      http
        .getCluesList({
          pageNum: 1,
          pageSize: 2,
          marketingEventId: this.marketingEventId,
          marketingActivityId: this.message.material.defaultMarketingActivityId,
          objectId:
            this.message.material.objectType === 26
              ? this.message.material.formId
              : this.message.material.id,
          objectType:
            this.message.material.objectType === 26
              ? 16
              : this.message.material.objectType,
          type: 4, // 市场活动
        })
        .then(res => {
          this.clueLoading = false
          if (res && res.errCode === 0) {
            const clueColumns = formDataDetail.actions.getColumns(res.data)
            clueColumns.push({
              prop: 'tags',
              label: $t('marketing.commons.bq_14d342'),
              minWidth: '350px',
            })
            // clueColumns.push({
            //   prop: "sourceText",
            //   label: "推广渠道",
            //   minWidth: "100px"
            // });
            this.clueTotal = res.data.totalCount
            this.clueColumns = clueColumns
            const dataResult = formDataDetail.actions.getDatas(res.data)
            this.clueData = dataResult.map(data => {
              let newData = { ...data }
              newData.wxUser = {
                text: data.enrollUserName,
                avatar: data.enrollUserAvatar,
              }
              newData.sourceText = this.getSourceType(data.sourceType)
              if (data.tagNameList) {
                newData = {
                  ...newData,
                  tags:
                    data.tagNameList.reduce((total, next) => {
                      const tagName = next.secondTagName || next.firstTagName
                      return total === '' ? tagName : `${total}, ${tagName}`
                    }, '') || '--',
                }
              } else {
                newData = {
                  ...newData,
                  tags: '--',
                }
              }
              return newData
            })
          }
        })
    },
    handleClueDetail(row) {
      if (row.saveCrmStatus === 0 || row.saveCrmStatus === 3) {
        // 销售线索存在于otherCrmObjectBind，其他任意业务对象存在于apiName, extraDataId
        const { apiName, extraDataId, otherCrmObjectBind } = row

        if (otherCrmObjectBind) {
          CRM.api.show_detail({
            apiname: otherCrmObjectBind.apiName,
            id: otherCrmObjectBind.objectId,
          })

          return
        }

        if (apiName && extraDataId) {
          CRM.api.show_detail({
            apiname: apiName,
            id: extraDataId,
          })
        }
      }
    },
    handleClueRow(row, column, event) {
      this.handleClueDetail(row)
    },
    getSourceType(type) {
      switch (type) {
        case 0:
          return $t('marketing.commons.xcx_0ed510')
        case 1:
          return $t('marketing.pages.setting.ygyx_f7ac22')
        case 2:
          return $t('marketing.commons.gzhyx_8bd4fe')
        case 3:
          return $t('marketing.commons.dxyx_de4a39')
        case 4:
          return $t('marketing.commons.wsfbm_31fde1')
        case 5:
          return $t('marketing.commons.drsj_8ef83f')
        default:
          return '--'
      }
    },
    exportClues(type) {
      // 导出线索
      const params = {
        marketingEventId: this.marketingEventId,
        objectId:
          this.message.material.objectType === 26
            ? this.message.material.formId
            : this.message.material.id,
        objectType:
          this.message.material.objectType === 26
            ? 16
            : this.message.material.objectType,
        type: 4, // 市场活动
      }
      if (type === 'order') {
        params.formUsage = 2
      }
      util.exportoFile(
        {
          action: 'exportClues',
          params,
        },
        () => {},
      )
    },

    handleCloseDetail(reflesh = false) {
      this.$emit('close', reflesh)
    },
    closeRadarDetail() {
      this.showRadarDetail = false
    },
    closeCluesDetail() {
      this.showCluesDetail = false
    },
    getBingFormByObject() {
      http
        .getBingFormByObject({
          objectId: this.message.material.id,
          objectType: this.message.material.objectType,
        })
        .then(res => {
          if (res && res.errCode === 0) {
            this.importFormId = res.data.formId
          }
        })
    },
    getFormDataById() {
      if (!this.message.material.formId) return
      const params = {
        id: this.message.material.formId,
      }
      if (!params.id) return
      http.getFormDataById(params)
        .then(res => {
          const { errCode, data } = res
          if (errCode === 0) {
            const {
              formMoreSetting: { saveCrmObjectType = 0 },
            } = data

            if (saveCrmObjectType === 1) {
              this.showImportButton = false
            }
          }
        })
    },
    handleDeleteClues(){
      const ids = this.getIDs()
      FxUI.MessageBox.confirm($t('marketing.commons.qrscdqxztj_5bc3c3'), $t('marketing.commons.ts_02d981'), {
          confirmButtonText: $t('marketing.commons.qd_38cf16'),
          cancelButtonText: $t('marketing.commons.qx_625fb2'),
          type: 'warning',
        }).then(async () => {
          this.deleteLoading = true
          const res = await http.deleteFormEnrollData({enrollUserIds: ids})
          if(res && res.errCode === 0){
            this.clearTableSelection()
            FxUI.Message.success($t('marketing.commons.sccg_0007d1'))
            this.getClues()
          } else {
            FxUI.Message.success($t('marketing.pages.setting.scsbqzs_89976e'))
          }
          this.deleteLoading = false
        }).catch((err)=>{
        })
    },
  },
}
</script>

<style lang="less">
.hover-tips {
  color: #545861;
  font-size: 12px;
}
.content_marketing_promotion_detail {
  z-index: 500 !important;
}
.detail-sideslip {
  .fx-dropdown {
    margin-left: 10px;
  }
  .detail-header {
    height: 80px;
    padding: 20px;
    background-color: #f6f9fc;
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    flex-shrink: 0;
    .header-photo {
      width: 110px;
      height: 80px;
      margin-right: 14px;
      flex-shrink: 0;
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
    }
    .header-message {
      width: 100%;
      min-width: 0;
    }
    .header-title {
      margin-bottom: 10px;
      color: #181c25;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
    .header-sub {
      color: #91959e;
      font-size: 12px;
    }
    .header-tool {
      flex-shrink: 0;
      margin-left: 20px;
      position: relative;
    }
  }
  .detail-body {
    .body-item {
      margin-top: 20px;
      .body__table {
        padding: 0 20px;
        .el-table tr th {
          background-color: #fff;
        }
        .tablewbg {
          width: auto;
          border-top: 1px solid #e9edf5 !important;
          border-left: 1px solid #e9edf5 !important;
          box-sizing: border-box;
          overflow: auto;
        }
      }
    }
    .item-title {
      margin: 0 20px;
      position: relative;
      height: 18px;
      line-height: 18px;
      font-size: 12px;
      color: #181c25;
      &:before {
        content: "";
        position: absolute;
        left: -20px;
        top: 3px;
        width: 4px;
        height: 12px;
        background: var(--color-primary06,#ff8000);
      }
      .title-end {
        color: var(--color-info06,#407FFF);
        position: absolute;
        right: 0;
        top: 0;
      }
      .create-poster {
        cursor: pointer;
        position: relative;
        padding-left: 22px;
        &:before {
          position: absolute;
          content: "";
          width: 16px;
          height: 16px;
          left: 0;
          top: -1px;
          background: url("../../../../assets/images/icon/icon-btn-add-b.png")
            center / cover no-repeat;
        }
      }
      .output-table {
        cursor: pointer;
        position: relative;
        margin-left: 30px;
        // padding-left: 22px;
        // &:before {
        //   position: absolute;
        //   content: '';
        //   width: 16px;
        //   height: 16px;
        //   left: 0;
        //   top: -1px;
        //   background: url('../../../../assets/images/icon/download.png') center / cover no-repeat;
        // }
        .output-icon {
          margin-right: 5px;
        }
      }
      .btn-disabled {
        cursor: not-allowed;
        color: #c1c5ce;
      }
    }
    .data-account {
      border: 1px solid #e9edf5;
      margin: 20px 20px 0px 20px;
      height: 110px;
      display: flex;
      align-items: center;
      .account-item {
        width: 100%;
        text-align: center;
        border-right: 1px solid #e9edf5;
        &:last-of-type {
          border-right: none;
        }
      }
      .account-num {
        font-size: 16px;
        color: #333333;
        margin-bottom: 5px;
      }
      .account-text {
        font-size: 12px;
        color: #999999;
      }
    }
    .poster-content {
      display: flex;
      flex-flow: row wrap;
      padding: 0px 10px;
      .poster-block {
        margin: 10px;
        position: relative;
      }
      .poster-create {
        cursor: pointer;
        margin-top: 10px;
        flex-flow: column nowrap;
        width: 78px;
        height: 132px;
        border: 1px solid #e6e6e6;
        display: flex;
        align-items: center;
        justify-content: center;
        &-img {
          width: 12px;
          height: 12px;
          display: inline-block;
        }
        &-text {
          margin-top: 10px;
          font-size: 12px;
          color: #91959e;
        }
      }
      .poster-img {
        width: 80px;
        height: 134px;
      }
      .poster-grey {
        width: 80px;
        height: 80px;
        padding: 27px 0px;
        position: absolute;
        top: 0;
        left: 0;
        background-color: #000000;
        opacity: 0.5;
        z-index: 100;
        display: flex;
        flex-flow: column;
        justify-content: space-between;
        &-button {
          text-align: center;
          color: #ffffff;
          cursor: pointer;
          text-decoration: none;
        }
      }
    }
    .content-radar {
      padding: 0px 20px;
      margin-top: 20px;
      .radar-item {
        border: 1px solid #e9edf5;
        border-bottom: none;
        display: flex;
        flex-flow: row nowrap;
        height: 40px;
        padding: 33px 13px;
        cursor: pointer;
        &:last-of-type {
          border-bottom: 1px solid #e9edf5;
        }
      }
      .radar-message {
        min-width: 0;
        width: 100%;
      }
      .radar-title {
        color: #181c25;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        margin-bottom: 8px;
      }
      .radar-sub {
        color: #91959e;
        font-size: 12px;
        > span {
          margin-right: 8px;
        }
      }
      .radar-counter {
        flex-shrink: 0;
        display: flex;
        flex-flow: row nowrap;
        align-items: center;
        position: relative;
        &:before {
          content: "";
          position: absolute;
          left: 0;
          width: 0px;
          height: 31px;
          border: 1px solid #e9edf5;
        }
      }
      .radar-visitNum {
        margin-right: 52px;
        margin-left: 60px;
        text-align: center;
      }
      .radar-cluesNum {
        margin-right: 51px;
        text-align: center;
      }
      .counter-num {
        color: #181c25;
        font-size: 16px;
      }
      .counter-text {
        color: #91959e;
        font-size: 12px;
      }
      .radar-more {
        border: 1px solid #e9edf5;
        border-top: none;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--color-primary06,#407FFF);
        font-size: 12px;
        > span {
          cursor: pointer;
        }
      }
    }
    .btn-wrapper{
      margin: 10px 20px 0 20px;
    }
    .clue-detail {
      padding: 0px 20px;
      margin: 10px 0;
      .tablewbg {
        width: auto;
        // margin: 0 20px;
        border-top: 1px solid #e9edf5 !important;
        border-left: 1px solid #e9edf5 !important;
        // border-width: 1px 1px 0;
        box-sizing: border-box;
        overflow: auto;
        .el-table__header {
          tr {
            th {
              background: #fff;
            }
          }
        }
        .el-table__empty-block {
          min-height: 200px;
        }
        &.bottomborder {
          border-width: 1px;
        }
      }
      .table__more {
        height: 40px;
        border: 1px solid #e9edf5;
        border-top-width: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 20px;
        a {
          font-size: 12px;
          text-decoration: none;
        }
      }
    }
  }
}
</style>
