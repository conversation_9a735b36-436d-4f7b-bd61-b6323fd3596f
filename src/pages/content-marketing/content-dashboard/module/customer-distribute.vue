<template>
  <div
    v-loading="isLoading"
    :class="$style.marketingCustomerDistribute__wrapper"
  >
    <div :class="$style.title">
      <div :class="$style['title-content']">
        <span>{{ $t('marketing.pages.content_marketing.xlkhfb_57e6b0') }}</span>
        <QuestionTooltip
          :class="$style.question"
          :offset="135"
        >
          <div
            slot="question-content"
            style="max-width: 280px; color: #545861"
          >
            <div class="question-tips">
              {{ $t('marketing.pages.content_marketing.rgcyhddyhy_8951f5') }}
            </div>
          </div>
        </QuestionTooltip>
      </div>
      <div :class="$style.update">
        <span
          v-if="dataPreparing"
          :class="$style.loading"
        >
          <loading :class-name="$style.icon" />
          {{ $t('marketing.commons.sjjsz_f32ed8') }}
        </span>
      </div>
    </div>
    <div :class="$style.content">
      <div
        ref="pieChart"
        :class="$style.pieChart"
      />
      <div
        v-if="count === 0"
        :class="$style.empty"
      >
        <Empty
          :class="$style['empty-wrapper']"
          :title="$t('marketing.commons.zwsj_21efd8')"
        />
      </div>
    </div>
  </div>
</template>
<script>
import * as echarts from 'echarts'
import QuestionTooltip from '@/components/questionTooltip/index.vue'
import Empty from '@/components/common/empty.vue'
import loading from './loading.vue'

export default {
  components: {
    QuestionTooltip,
    Empty,
    loading,
  },
  props: {
    marketingEventId: {
      type: String,
      default: '',
    },
    updateTick: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      dataSource: {
        new: 0,
        old: 0,
      },
      chartContainerWidth: 0,
      isLoading: true,
      dataPreparing: false,
    }
  },
  computed: {
    count() {
      return this.dataSource.new + this.dataSource.old
    },
    chartData() {
      return [
        { value: this.dataSource.new, name: $t('marketing.pages.content_marketing.xkh_8759f4') },
        { value: this.dataSource.old, name: $t('marketing.pages.content_marketing.lkh_c122f0') },
      ]
    },
  },
  watch: {
    marketingEventId() {
      this.handleUpdateData()
    },
    updateTick() {
      this.handleUpdateData()
    },
  },
  mounted() {
    this.queryOldAndNewCustomStatistics()
    this.chart = echarts.init(this.$refs.pieChart, 'light')
    // 初始化设置一次
    this.chartContainerWidth = this.$refs.pieChart.getBoundingClientRect().width
    this.chartResize = () => {
      // 让这个函数保持 this
      this.chart.resize()
      this.chartContainerWidth = this.$refs.pieChart.getBoundingClientRect().width
    }
    window.addEventListener('resize', this.chartResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.chartResize)
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    renderPie() {
      const _this = this
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)',
        },
        grid: {
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
        },
        legend: {
          data: _this.chartData.map(item => item.name),
          formatter(name) {
            const val = _this.chartData.filter(item => item.name === name)[0].value
            return `${name}（${val}）`
          },
          show: true,
          icon: 'circle',
          itemWidth: 8,
          itemHeight: 8,
          top: 10,
          textStyle: {
            color: '#545861',
            padding: [0, 0, 0, 0],
            fontSize: 12,
          },
        },
        series: [
          {
            name: $t('marketing.pages.content_marketing.xlkhfbt_df8b83'),
            type: 'pie',
            radius: ['40%', '48%'],
            avoidLabelOverlap: false,
            height: 300,
            label: {
              normal: {
                show: true,
                position: 'center',
                color: '#4c4a4a',
                formatter:
                  `{count|${_this.count}}` + '\n\r' + `{clue|${$t('marketing.pages.content_marketing.cykhs_ef9d74')}}`,
                rich: {
                  count: {
                    fontWeight: 400,
                    fontSize: 24,
                    color: '#181C25',
                    lineHeight: 30,
                  },
                  clue: {
                    fontWeight: 400,
                    fontSize: 14,
                    color: '#545861',
                    lineHeight: 26,
                  },
                },
              },
              emphasis: {
                // 中间文字显示
                show: true,
              },
            },
            labelLine: {
              normal: {
                show: false,
              },
            },
            data: _this.chartData,
            color: ['#5498FF', '#6FDDA0', '#FFA250'],
          },
        ],
      }

      if (this.chart) {
        this.chart.setOption(option)
      }
    },
    async queryOldAndNewCustomStatistics() {
      if (!this.marketingEventId) return
      const res = await YXT_ALIAS.http.queryOldAndNewCustomStatistics({
        marketingEventId: this.marketingEventId,
      })

      this.isLoading = false
      this.dataPreparing = false
      if (res && res.errCode === 0 && res.data) {
        this.dataSource.new = res.data.newCustomNum || 0
        this.dataSource.old = res.data.oldCustomNum || 0
        this.renderPie()
      }

      if (res && res.errCode === 930001) {
        this.dataPreparing = true
      }
    },
    handleUpdateData() {
      this.isLoading = true
      this.queryOldAndNewCustomStatistics()
    },
  },
}
</script>
<style lang="less" module>
.marketingCustomerDistribute__wrapper {
  background-color: #ffffff;
  width: 100%;
  height: 300px;
  box-sizing: border-box;
  margin-bottom: 12px;
  padding: 20px 16px;
  border-radius: 8px;
  position: relative;

  .title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #181c25;
    font-size: 14px;
    line-height: 20px;
    font-weight: bold;

    .title-content {
      display: flex;
      align-items: center;
    }

    .question {
      margin-left: 6px;
    }

    .update {
      display: flex;
      align-items: center;
      color: #91959E;
      font-size: 12px;

      .loading {
        display: flex;
        align-items: center;
        margin-right: 10px;

        .icon {
          margin-right: 10px;
        }
      }
    }
  }
  .pieChart {
    height: 248px;
  }

  .content {
    position: relative;
  }
  .empty {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #FFF;

    .empty-wrapper {
      padding: 0;
    }
  }
}
</style>
