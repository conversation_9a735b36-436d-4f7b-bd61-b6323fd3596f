<template>
  <div
    v-loading="isLoading"
    :class="$style.container"
  >
    <div :class="$style.title">
      <div :class="$style['title-content']">
        <span>{{ $t('marketing.pages.content_marketing.yxsjdd_6df012') }}</span>
        <QuestionTooltip
          :class="$style.question"
          :offset="135"
        >
          <div
            slot="question-content"
            style="max-width: 280px; color: #545861"
          >
            <div :class="$style['question-tips']">
              <p>{{ $t('marketing.pages.content_marketing.hdyxsjssyc_ba12a1') }}</p>
              <p>{{ $t('marketing.pages.content_marketing.hdyxsjjesy_023a13') }}</p>
              <p>{{ $t('marketing.pages.content_marketing.hdyxddssyc_3c83a2') }}</p>
              <p>{{ $t('marketing.pages.content_marketing.hdyxddjesy_7958b8') }}</p>
              <p>{{ $t('marketing.pages.content_marketing.hdxzsjssyc_b1e5be') }}</p>
              <p>{{ $t('marketing.pages.content_marketing.hdxzsjjesy_c0e51e') }}</p>
              <p>{{ $t('marketing.pages.content_marketing.hdxzydsjss_48b3a5') }}</p>
              <p>{{ $t('marketing.pages.content_marketing.hdxzydsjje_6b2c28') }}</p>
            </div>
          </div>
        </QuestionTooltip>
      </div>
      <div :class="$style.update">
        <span
          v-if="dataPreparing"
          :class="$style.loading"
        >
          <loading :class-name="$style.icon" />
          {{ $t('marketing.commons.sjjsz_f32ed8') }}
        </span>
      </div>
    </div>
    <div :class="$style.content">
      <div :class="$style.graph">
        <div
          v-for="item in dataList"
          :key="item.name"
          :class="$style.cell"
          @click="handleOpenSideslip(item)"
        >
          <div :class="$style.count">
            {{ item.value }}
          </div>
          <div :class="$style.name">
            {{ item.name }}
          </div>
        </div>
      </div>
      <div
        v-if="isEmpty"
        :class="$style.empty"
      >
        <Empty
          :class="$style['empty-wrapper']"
          :title="$t('marketing.commons.zwsj_21efd8')"
        />
      </div>
    </div>

    <MarketingAttributeDataSideslip
      v-if="sideslipVisible"
      :visible="sideslipVisible"
      :data="activeData"
      @onClose="handleCloseSideslip"
    />
  </div>
</template>

<script>
import http from '@/services/http/index.js'
import QuestionTooltip from '@/components/questionTooltip/index.vue'
import Empty from '@/components/common/empty.vue'
import loading from './loading.vue'
import MarketingAttributeDataSideslip from './marketing-attribute-data-sideslip.vue'

export default {
  components: {
    QuestionTooltip,
    loading,
    Empty,
    MarketingAttributeDataSideslip,
  },
  props: {
    marketingEventId: {
      type: String,
      default: '',
    },
    updateTick: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      isLoading: true,
      dataList: [],
      updateTime: 0,
      dataPreparing: false,
      sideslipVisible: false,
      activeData: null,
    }
  },
  computed: {
    isEmpty() {
      const hasValueList = this.dataList.filter(item => item.value > 0)
      return hasValueList.length === 0
    },
  },
  watch: {
    marketingEventId() {
      this.init()
    },
    updateTick() {
      this.handleUpdateData()
    },
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      if (!this.marketingEventId) {
        return
      }

      http.getAttributeData({ marketingEventId: this.marketingEventId })
        .then(res => {
          this.isLoading = false
          const { errCode, data } = res
          const {
            updateTime = 0,
            influenceOpportunityCount = 0,
            influenceOpportunityMoney = 0,
            influenceOrderCount = 0,
            influenceOrderMoney = 0,
            newOpportunityCount = 0,
            newOpportunityMoney = 0,
            newWinOpportunityCount = 0,
            newWinOpportunityMoney = 0,
            id = '',
          } = data || {}

          this.updateTime = updateTime
          this.dataPreparing = false
          this.dataList = [
            {
              name: $t('marketing.pages.content_marketing.hdyxsjsg_0667ca'),
              value: influenceOpportunityCount,
              params: {
                attributeDataId: id,
                objectType: 'NewOpportunityObj',
                objectCategory: '',
                objectStatus: '',
              },
            },
            {
              name: $t('marketing.pages.content_marketing.hdyxsjjey_5c3f30'),
              value: influenceOpportunityMoney,
              params: {
                attributeDataId: id,
                objectType: 'NewOpportunityObj',
                objectCategory: '',
                objectStatus: '',
              },
            },
            {
              name: $t('marketing.pages.content_marketing.hdyxddsg_fb0fb9'),
              value: influenceOrderCount,
              params: {
                attributeDataId: id,
                objectType: 'SalesOrderObj',
                objectCategory: '',
                objectStatus: '',
              },
            },
            {
              name: $t('marketing.pages.content_marketing.hdyxddjey_651e15'),
              value: influenceOrderMoney,
              params: {
                attributeDataId: id,
                objectType: 'SalesOrderObj',
                objectCategory: '',
                objectStatus: '',
              },
            },
            {
              name: $t('marketing.pages.content_marketing.hdxzsjsg_604be2'),
              value: newOpportunityCount,
              params: {
                attributeDataId: id,
                objectType: 'NewOpportunityObj',
                objectCategory: 'new',
                objectStatus: '',
              },
            },
            {
              name: $t('marketing.pages.content_marketing.hdxzsjjey_cfaf55'),
              value: newOpportunityMoney,
              params: {
                attributeDataId: id,
                objectType: 'NewOpportunityObj',
                objectCategory: 'new',
                objectStatus: '',
              },
            },
            {
              name: $t('marketing.pages.content_marketing.hdxzydsjsg_121a4d'),
              value: newWinOpportunityCount,
              params: {
                attributeDataId: id,
                objectType: 'NewOpportunityObj',
                objectCategory: 'new',
                objectStatus: 'win',
              },
            },
            {
              name: $t('marketing.pages.content_marketing.hdxzydsjje_26486c'),
              value: newWinOpportunityMoney,
              params: {
                attributeDataId: id,
                objectType: 'NewOpportunityObj',
                objectCategory: 'new',
                objectStatus: 'win',
              },
            },
          ]

          if (errCode === 930001) {
            this.dataPreparing = true
          }

          if (updateTime) {
            this.$emit('onUpdate', { updateTime })
          }
        })
        .catch(() => {
          this.isLoading = false
        })
    },
    handleUpdateData() {
      this.isLoading = true
      http.calculateAttributeData({ marketingEventId: this.marketingEventId })
        .then(res => {
          const { errCode } = res
          if (errCode === 0) {
            this.init()
          }
        })
    },
    handleOpenSideslip(dataItem) {
      this.sideslipVisible = true
      this.activeData = dataItem
    },
    handleCloseSideslip() {
      this.sideslipVisible = false
      this.activeData = null
    },
  },
}
</script>

<style lang="less" module>
.question-tips {
  font-size: 12px;

  p {
    margin-bottom: 10px;
    &:first-of-type {
      padding-top: 10px;
    }
  }
}
.container {
  background-color: #ffffff;
  width: 100%;
  box-sizing: border-box;
  margin-bottom: 12px;
  padding: 20px 16px;
  position: relative;
  border-radius: 8px;
  .title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #181c25;
    font-size: 14px;
    line-height: 20px;
    font-weight: bold;

    .title-content {
      display: flex;
      align-items: center;
    }

    .question {
      margin-left: 6px;
    }

    .update {
      display: flex;
      align-items: center;
      color: #91959E;
      font-size: 12px;

      .loading {
        display: flex;
        align-items: center;
        margin-right: 10px;

        .icon {
          margin-right: 10px;
        }
      }
    }
  }
  .graph {
    display: flex;
    flex-wrap: wrap;
    min-height: 208px;

    .cell {
      width: 25%;
      margin-top: 20px;
      padding: 15px 20px;
      box-sizing: border-box;
      cursor: pointer;
      .count {
        font-size: 24px;
        line-height: 28px;
        font-weight: bold;
        color: #0C6CFF;
      }
      .name {
        font-size: 14px;
        line-height: 18px;
        color: #181C25;
        margin-top: 8px;
      }
    }
  }

  .content {
    position: relative;
  }
  .empty {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #FFF;

    .empty-wrapper {
      padding: 0;
    }
  }
}
</style>
