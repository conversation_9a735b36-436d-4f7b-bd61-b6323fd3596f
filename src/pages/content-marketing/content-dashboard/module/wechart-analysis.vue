<template>
  <div class="content-marketing-wechart-analysis__wrapper" v-loading="isLoading">
    <div class="title">
      {{ $t('marketing.pages.content_marketing.wxsjtj_1e838f') }}
    </div>
    <div class="bar">
      <div class="bar-item">
        <div class="bar-label">
          {{ $t('marketing.pages.content_marketing.xzgzhfs_8937f9') }}
        </div>
        <div class="progress-box">
          <div class="progress fan-color" />
          <span>{{ fanNum }}</span>
        </div>
      </div>

      <div class="bar-item">
        <div class="bar-label">
          {{ $t('marketing.pages.content_marketing.xzqwkh_e3b5cc') }}
        </div>
        <div class="progress-box">
          <div class="progress customer-color" />
          <span>{{ customerNum }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    marketingEventId: {
      type: String,
      default: '',
    },
    updateTick: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      fanNum: 0,
      customerNum: 0,
      isLoading: true,
    }
  },
  computed: {
    allNum() {
      return this.fanNum + this.customerNum
    },
    transtoPercent() {
      return function (num) {
        return (num / this.allNum) * 100
      }
    },
  },
  watch: {
    marketingEventId() {
      this.handleUpdateData()
    },
    updateTick() {
      this.handleUpdateData()
    },
  },
  mounted() {
    this.init()
  },
  methods: {
    render() {
      setTimeout(() => {
        document.querySelector('.fan-color').style.width = `${this.transtoPercent(this.fanNum)}%`
        document.querySelector('.customer-color').style.width = `${this.transtoPercent(this.customerNum)}%`
      }, 10)
    },
    init() {
      if (!this.marketingEventId) return

      const promiseList = [
        YXT_ALIAS.http.queryCampaignWxStatistics({ marketingEventId: this.marketingEventId }),
        YXT_ALIAS.http.queryCampaignExternalContactStatistics({ marketingEventId: this.marketingEventId }),
      ]
      Promise.all(promiseList)
        .then(([resp0, resp1]) => {
          this.isLoading = false
          if (resp0 && resp0.errCode === 0) {
            this.fanNum = resp0.data.addFanNum
          }

          if (resp1 && resp1.errCode === 0) {
            this.customerNum = resp1.data.addExternalNum
          }

          this.render()
        })
        .catch(() => {
          this.isLoading = false
        })
    },
    handleUpdateData() {
      this.isLoading = true
      this.init()
    },
  },
}
</script>
<style lang="less" scoped>
.content-marketing-wechart-analysis__wrapper {
  background-color: #ffffff;
  width: 100%;
  box-sizing: border-box;
  margin-bottom: 12px;
  padding: 20px 16px;
  position: relative;
  border-radius: 8px;
  .title {
    color: #181c25;
    font-size: 14px;
    font-weight: bold;
    line-height: 20px;
  }
  .bar {
    font-size: 12px;
    color: #545861;

    .bar-item {
      display: flex;
      align-items: center;
      line-height: 20px;
      margin-top: 20px;
      .bar-label {
        width: 110px;
        color: #545861;
        font-size: 12px;
      }
    }
    .progress-box {
      display: flex;
      align-items: center;
      flex: 1;
      .progress {
        width: 0px;
        height: 14px;
        display: inline-block;
        transition: width 1.5s;
      }
      .fan-color {
        background: #ffa250;
      }
      .customer-color {
        background: #6fdda0;
      }
      span {
        margin-left: 10px;
        color: #181C25;
        font-weight: bold;
      }
    }
  }
}
</style>
