<!-- 组件说明 -->
<template>
  <div class="content-marketing-rank__wrapper">
    <div class="title">
      <div>{{ $t('marketing.commons.nrxgph_98a757') }}</div>
      <span
        v-if="rankList.length > 4"
        class="more"
        @click="goMore"
      >{{ $t('marketing.commons.gd_0ec9ea') }}</span>
    </div>
    <div v-loading="loading">
      <div
        v-if="rankList.length"
        class="ranking-wrapper"
      >
        <div class="row header">
          <div class="row-item rank-index">
            {{ $t('marketing.commons.pm_a4dc00') }}
          </div>
          <div class="row-item rank-content">
            {{ $t('marketing.pages.content_marketing.nrbt_c9b763') }}
          </div>
          <div class="row-item randk-num">
            <v-dropdown
              class="dorpdown-wrapper"
              :value="type"
              :options="typeOptions"
              @change="changeDrop"
            />
          </div>
        </div>
        <div
          v-for="(item, index) in rankList"
          :key="index"
          class="row"
        >
          <div class="row-item rank-index">
            <div class="index">
              {{ index + 1 }}
            </div>
          </div>
          <div class="row-item rank-content">
            <div class="rank-content-wrap">
              <div class="rank-image">
                <VImage
                  fit="cover"
                  style="width: 100%;height: 100%"
                  :src="item.showImage"
                />
              </div>
              <div class="rank-name">
                {{ item.showTitle }}
              </div>
            </div>
          </div>
          <div class="row-item randk-num">
            <div class="randk-num-wrap">
              <span>{{ item.showNum }}</span>{{ item.showText }}
            </div>
          </div>
        </div>
      </div>
      <div
        v-else
        class="empty-wrapper"
      >
        <Empty :title="$t('marketing.commons.zwsj_21efd8')" />
      </div>
    </div>
    <contentRankSideSilp
      v-if="showMoreSide"
      :side-type="type"
      :visible="showMoreSide"
      :marketing-event-id="marketingEventId"
      :scene-type="sceneType"
      @close="handleClose"
    />
  </div>
</template>
<script>
import http from '@/services/http/index.js'
import VDropdown from '@/components/kitty/dropdown.vue'
import Empty from '@/components/common/empty.vue'

import {
  materialType,
  materialTitle,
  materialPhoto,
} from '@/pages/content-marketing/func.js'
import contentRankSideSilp from './conent-rank-sideslip.vue'

export default {
  components: {
    VDropdown,
    Empty,
    VImage: FxUI.Image,
    contentRankSideSilp,
  },
  props: {
    marketingEventId: {
      type: String,
      default: '',
    },
    sceneType: {
      type: String,
      default: '',
    },
    updateTick: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      loading: true,
      showMoreSide: false,
      type: 1,
      typeOptions: [
        { label: $t('marketing.commons.fwcs_f0be64'), value: 1 },
        { label: $t('marketing.commons.fwrs_c3c959'), value: 0 },
        { label: $t('marketing.commons.hqxss_73f905'), value: 2 },
      ],
      rankList: [],
      rankColor: ['#F64141', '#F29949', '#FFC400', '#333333', '#333333'],
    }
  },
  watch: {
    marketingEventId() {
      this.changeDrop({ value: 1 })
    },
    updateTick() {
      this.changeDrop({ value: 1 })
    },
  },
  created() {
    this.queryMarketingEventContentStatistics()
  },
  destroyed() {},
  methods: {
    materialType,
    materialTitle,
    materialPhoto,
    changeDrop(a) {
      this.type = a.value
      this.queryMarketingEventContentStatistics()
    },
    handleClose() {
      this.showMoreSide = false
    },
    queryMarketingEventContentStatistics() {
      this.loading = true
      http
        .queryMarketingEventContentStatistics({
          marketingEventId: this.marketingEventId,
          pageNum: 1,
          pageSize: 5,
          objectTypes:
            this.sceneType === 'conference'
              ? [4, 6, 26, 16, 9999, 13]
              : [4, 6, 26, 16, 9999],
          statisticsType: this.type,
        })
        .then(res => {
          this.loading = false
          if (res && res.errCode === 0) {
            const _rankList = res.data.result
            const typeList = {
              0: 'uv',
              1: 'pv',
              2: 'clueNum',
            }
            const TextList = {
              0: $t('marketing.commons.r_465afe'),
              1: $t('marketing.commons.c_7229ec'),
              2: $t('marketing.commons.g_930882'),
            }
            _rankList.forEach(element => {
              if (element.abstractMaterialData) {
                element.showNum = element[typeList[this.type]]
                element.showTitle = this.materialTitle(
                  element.abstractMaterialData,
                )
                element.showImage = this.materialPhoto(
                  element.abstractMaterialData,
                )
                element.showText = TextList[this.type]
              }
            })
            this.rankList = _rankList
          }
        })
    },
    goMore() {
      this.showMoreSide = true
    },
  }, // 生命周期 - 销毁完成
}
</script>

<style lang="less" scoped>
.content-marketing-rank__wrapper {
  background: #fff;
  width: 100%;
  padding: 20px 16px;
  box-sizing: border-box;
  border-radius: 8px;
  .title {
    color: #181c25;
    font-size: 14px;
    font-weight: bold;
    line-height: 20px;
    display: flex;
    justify-content: space-between;
    .more {
      cursor: pointer;
      font-size: 12px;
      color: var(--color-info06,#407FFF);
      margin-left: 20px;
    }
  }
  .ranking-wrapper {
    display: flex;
    flex-direction: column;
    margin-top: 20px;

    .row {
      display: flex;
      align-items: stretch;
      margin-bottom: 15px;

      &:last-of-type {
        margin-bottom: 0;
      }
      &.header {
        background: #F7F8FA;
        font-size: 12px;
        color: #91959E;
        border-bottom: 1px solid #DEE1E8;

        .row-item {
          height: 32px;

          .dorpdown-wrapper {
            /deep/ .kitty-vbox {
              background-color: #FFF;
            }
          }
        }
      }

      .row-item {
        display: flex;
        justify-content: center;
        align-items: center;

        &.rank-index {
          width: 40px;
          .index {
            min-width: 18px;
            height: 18px;
            background: #FFF7E6;
            border-radius: 9px;
            color: #FF8000;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 3px;
            box-sizing: border-box;
          }
        }

        &.rank-content {
          flex: 1;
          justify-content: flex-start;
          .rank-content-wrap {
            display: flex;
            align-items: center;
            flex: 1;
            .rank-image {
              width: 60px;
              height: 40px;
              border-radius: 4px;
              overflow: hidden;
            }
            .rank-name {
              color: #181c25;
              font-size: 14px;
              flex: 1;
              margin-left: 10px;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 2;
              overflow: hidden;
              word-break: break-all;
            }
          }
        }

        &.randk-num {
          width: 140px;
          justify-content: flex-end;
          padding-right: 8px;

          .randk-num-wrap {
            display: flex;
            align-items: flex-end;
            font-size: 14px;
            color: #91959E;

            span {
              color: #181C25;
              font-size: 16px;
              font-weight: bold;
              margin-right: 8px;
            }
          }
        }
      }
    }
  }
}
</style>
