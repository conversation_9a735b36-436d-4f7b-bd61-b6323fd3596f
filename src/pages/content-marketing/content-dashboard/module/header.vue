<!-- 组件说明 -->
<template>
  <div :class="$style.activityOverviewHeader">
    <div :class="$style.left">
      <p :class="$style.title">
        {{ message.name }}
      </p>
      <div :class="$style.info">
        <span :class="$style.item">{{ $t('marketing.commons.sj_14e6d8') }}{{ getTime(message.beginTime) }}-{{
          getTime(message.endTime)
        }}
        </span>
        <span :class="$style.item">{{ $t('marketing.commons.lx_e91f5a') }}{{ eventTypeName }}</span>
        <span :class="$style.item">{{ $t('marketing.commons.yjsryqcb_7cfac4') }} {{ message.expectedIncome || "--" }}/{{
          message.expectedCost || "--"
        }}
          (
          {{
            message.expectedROI
              ? Math.floor(message.expectedROI * 100) ===
                message.expectedROI * 100
                ? message.expectedROI * 100
                : (message.expectedROI * 100).toFixed(2)
              : "--"
          }}% )
        </span>
        <span :class="$style.item">{{ $t('marketing.commons.sjsrsjcb_b740e8') }} {{ message.actualIncome || "--" }}/{{
          message.actualCost || "--"
        }}
          (
          {{
            message.actualROI
              ? Math.floor(message.actualROI * 100) === message.actualROI * 100
                ? message.actualROI * 100
                : (message.actualROI * 100).toFixed(2)
              : "--"
          }}% )
        </span>
        <span
          v-if="marketingEventAudit"
          :class="$style.item"
        >
          {{ $t('marketing.commons.shzt_b6d0e9' ) }}：{{ CampaignReviewStatusLabel[message.lifeStatus] || '--' }}
        </span>
      </div>
    </div>
    <div :class="$style.right">
      <fx-button
        :class="$style.detail"
        size="mini"
        @click="handleOpenMarketingDetail"
      >
        {{ $t('marketing.commons.ckxq_5b48db') }}
      </fx-button>
      <div :class="$style.more">
        <ElDropdown @command="handleMoreCommand">
          <ElButton
            size="small"
            class="el-icon-more"
            style="height:32px;"
          />
          <ElDropdownMenu slot="dropdown">
            <ElDropdownItem
              command="form"
            >
              {{ $t('marketing.commons.bmsz_381009') }}
            </ElDropdownItem>
            <ElDropdownItem command="edit">
              {{ $t('marketing.commons.bj_95b351') }}
            </ElDropdownItem>
            <ElDropdownItem command="cancel">
              {{ $t('marketing.commons.zf_c4a4d9') }}
            </ElDropdownItem>
            <ElDropdownItem
              v-if="$route.query.formType != 6"
              command="syncRules"
            >
              {{ $t('marketing.commons.bmsjzdtbgz_03e67b') }}
            </ElDropdownItem>
          </ElDropdownMenu>
        </ElDropdown>
      </div>
    </div>
    <MarketingActivityMappingSettings
      v-if="mappingSettingsVisible"
      :visible.sync="mappingSettingsVisible"
      :marketing-event-id="$route.params.id"
    />
    <EnrollSyncRules
      v-if="$route.params.id"
      :marketing-event-id="$route.params.id"
      :visible="EnrollSyncRulesVisible"
      @close="EnrollSyncRulesVisible = false"
    />
  </div>
</template>
<script>
import { mapState } from 'vuex'
import http from '@/services/http/index.js'
import { requireAsync } from '@/utils/index.js'
import EnrollSyncRules from '@/components/enroll-sync-rules/index.vue'
import { getTime, getName } from '../../func.js'

import MarketingActivityMappingSettings from '@/components/MarketingActivityMappingSettings/index.vue'
import { CampaignReviewStatusLabel } from '@/utils/statusEnum.js'

const defaultData = {
  name: '--',
  actualCost: 0,
  actualIncome: 0,
  actualROI: 0,
  beginTime: '',
  bizStatus: '',
  endTime: '',
  eventType: '',
  expectedCost: 0,
  expectedIncome: 0,
  expectedROI: 0,
  id: '',
  ownerId: null,
}

export default {
  components: {
    ElDropdown: FxUI.Dropdown,
    ElDropdownItem: FxUI.DropdownItem,
    ElDropdownMenu: FxUI.DropdownMenu,
    ElButton: FxUI.Button,
    MarketingActivityMappingSettings,
    EnrollSyncRules,
  },
  props: {
    headData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      id: '',
      detailLoading: false,
      eventTypeName: '',
      mappingSettingsVisible: false,
      options: [],
      EnrollSyncRulesVisible: false,
      CampaignReviewStatusLabel,
    }
  },
  computed: {
    ...mapState('MarketingEventSet', ['marketingEventAudit']),
    message() {
      return { ...defaultData, ...this.headData }
    },
  },
  created() {
    this.id = this.$route.params.id
    this.getMarketingEventTypeField()
    this.initMarketingEventCommonSetting()
  },
  methods: {
    initMarketingEventCommonSetting() {
      const type = Number(this.$route.query.formType)
      http.getMarketingEventCommonSetting({ type }).then(res => {
        if (res && res.errCode == 0) {
          const _options = (res.data
              && res.data.activityTypeMapping
              && res.data.activityTypeMapping[0]
              && res.data.activityTypeMapping[0].mapping)
            || []
          _options.unshift({ apiName: '', fieldName: $t('marketing.commons.qb_a8b0c2') })
          this.options = _options
        }
      })
    },
    getTime,
    getName,
    handleOpenMarketingDetail() {
      this.detailLoading = true
      requireAsync('crm-components/showdetail/showdetail', Detail => {
        this.detailLoading = false
        if (!this.$detail) {
          this.$detail = new Detail({ showMask: true })
          this.$detail.on('refresh', () => {})
        }
        this.$detail.setApiName('MarketingEventObj')
        setTimeout(() => this.$detail.show(this.id), 0)
      })
    },
    handleMoreCommand(command) {
      switch (command) {
        case 'edit':
          this.handleEdit()
          break
        case 'cancel':
          this.handleCancel()
          break
        case 'form':
          this.mappingSettingsVisible = true
          break
        case 'syncRules':
          this.EnrollSyncRulesVisible = true
          break
        default:
          break
      }
    },
    handleEdit() {
      const that = this
      const type = Number(that.$route.query.formType)
      const event_form = type === 0 ? 'online_marketing' : 'multivenue_marketing'
      FS.util.FHHApi(
        {
          url: '/EM1HNCRM/API/v1/object/MarketingEventObj/controller/Detail',
          data: {
            objectDescribeApiName: 'MarketingEventObj',
            objectDataId: this.id,
          },
          success: ({ Result: { StatusCode }, Value }) => {
            if (StatusCode === 0) {
              requireAsync('crm-modules/action/field/field', field => {
                CRM.api.edit({
                  data: Value.data,
                  Model: field.Model.extend({
                    parse(res) {
                      const _options = res.objectDescribe.fields.event_type.options
                      const submitOptions = []
                      _options.forEach(item => {
                        that.options.forEach(i => {
                          if (item.value === i.apiName) {
                            submitOptions.push(item)
                          }
                        })
                      })
                      console.log(submitOptions, 'submitOptions')
                      res.objectDescribe.fields.event_type.options = submitOptions
                      // 因为目前市场活动新建event_form字段没支持到禁用能力  所以只展示当前活动类型 其他的都隐藏 
                      res.objectDescribe.fields.event_form.options = res.objectDescribe.fields.event_form.options.filter(item =>item.value === event_form)
                      return field.Model.prototype.parse.apply(this, arguments)
                    },
                  }),
                  title: $t('marketing.commons.bjschd_75dee1'),
                  apiname: 'MarketingEventObj',
                  show_type: 'full',
                  dataId: this.id,
                  nonEditable: true,
                  success: () => {
                    this.$emit('refresh')
                  },
                })
              })
            }
          },
        },
        {
          errorAlertModel: 1,
        },
      )
    },
    handleCancel() {
      FS.util.FHHApi(
        {
          url: '/EM1HNCRM/API/v1/object/MarketingEventObj/controller/Detail',
          data: {
            objectDescribeApiName: 'MarketingEventObj',
            objectDataId: this.id,
          },
          success: ({ Result: { StatusCode }, Value }) => {
            if (StatusCode === 0) {
              CRM.api.abolish({
                apiname: 'MarketingEventObj',
                dataList: [Value.data],
                success: () => {
                  this.$router.push({ name: 'content-marketing' })
                },
              })
            }
          },
        },
        {
          errorAlertModel: 1,
        },
      )
    },
    getMarketingEventTypeField() {
      http.getMarketingEventTypeField().then(res => {
        const marketingEventFields = res.data
        marketingEventFields.forEach(item => {
          if (item.apiName === this.headData.eventType) {
            this.eventTypeName = item.labelName
          }
        })
      })
    },
  },
}
</script>

<style lang="less" module>
.activityOverviewHeader {
  display: flex;
  justify-content: space-between;
  padding: 0 24px 0 16px;
  align-items: center;
  height: 80px;
  background-color: #ffffff;
  .left {
    flex: 1;
    .title {
      font-size: 16px;
      color: #181c25;
      margin-bottom: 6px;
    }
    .item {
      margin-right: 20px;
    }
  }
  .right {
    flex: 0 0 146px;
    display: flex;
    justify-content: space-between;
    .detail {
      width: 90px;
      height: 32px;
    }
  }
}
</style>
