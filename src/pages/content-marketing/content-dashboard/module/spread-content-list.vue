<template>
    <div class="spread-content-list">
      <div
        v-loading="loading"
        class="content-advertise"
      >
        <div class="advertise-header">
          <div class="advertise-header-left">
            <div class="advertise-header-title">
              {{ $t('marketing.commons.tgnr_a6ec90') }}
            </div>
            <fx-input
              :placeholder="$t('marketing.commons.qsr_02cc4f')"
              v-model="keyword"
              size="mini"
              clearable
              is-search
              @on-search="handleResetPageNum"
              @clear="handleResetPageNum"
              @keyup.native.enter="handleResetPageNum"
              style="width:200px"
            ></fx-input>
          </div>
          <div id="guide_advertise-header-create" class="advertise-header-create">
            <AssociateContentToCampaignButton
              ref="AssociateContentButton"
              :marketing-event-id="id"
              @onMaterialAdded="onMaterialAdded"
            />
          </div>
        </div>
        <div class="advertise-body">
          <div
            v-if="showContentList"
            class="advertise-content-list"
          >
            <div class="content-list">
              <div
                v-for="(item, index) in contentList"
                :key="index"
                :style="{
                  backgroundColor: activeMaterialId === item.material.id ? '#f2f3f5' : ''
                }"
                :class="['content-list-item',item.material.status === 2 && 'unuse-wrapper', item.material.siteType === 'EVEN_HOMEPAGE' && 'meeting-homepage-wrapper']"
                @click="showDetailSideslip(item)"
                @mouseenter="fetchShortUrl(item)"
              >
                <div :class="['list-item-left']">
                  <div
                    class="list-item-head"
                  >
                    <div
                      class="list-item-photo"
                      :style="{
                        backgroundImage: `url('${materialPhoto(item.material)}')`,
                      }"
                    >
                      <span
                          v-if="item.material.status === 2"
                          class="title-unuse"
                        >{{ $t('marketing.commons.yty_69b0f6') }}</span>
                      <div
                        v-if="item.material.objectType == 26 && item.material.fileType"
                        class="type-icon-wrapper"
                      >
                        <div
                          style="background:#FF7752;"
                        >
                          {{ String(item.material.fileType).toLocaleUpperCase() }}
                        </div>
                      </div>
                      <!-- 会议主页 -->
                      <span
                        v-if="item.material.siteType === 'EVEN_HOMEPAGE'"
                        class="title-unuse"
                        style="background:#E6F4FF;color:#0c6cff;border: none;border-radius: 4px 0 0 0;"
                      >
                        {{ $t('marketing.commons.hyzy_7962bd') }}
                      </span>
                    </div>
                    <div class="list-item-message">
                      <div class="list-item-title">
                        <span class="title-text km-t-ellipsis1">
                          {{ materialTitle(item.material) }}
                        </span>
                        <div class="more-icon-wrapper">
                        <fx-tooltip
                          v-if="!!item.material.isApplyObject"
                          effect="dark"
                          :content="$t('marketing.pages.content_marketing.gtgnrwbmnr_6d5ffd')"
                          placement="top"
                        >
                          <i class="iconfont iconsign icon" style="color: #189DFF;margin-left: 8px;" />
                        </fx-tooltip>
                        <fx-tooltip
                          v-if="!!item.material.isMobileDisplay"
                          effect="dark"
                          placement="top"
                        >
                         <div slot="content">
                            <span>
                              {{ $t('marketing.pages.content_marketing.dqtgnrhzsz_df8055') }}
                              </span>
                            <fx-link href="https://help.fxiaoke.com/93d5/9188/0f9e/b30c" target="_blank" :underline="false">
                              {{ $t('marketing.commons.ljgd_06a3c1') }}
                            </fx-link>
                          </div>
                          <i class="iconfont iconmobile icon" style="color: #30C776; margin-left: 8px;" />
                        </fx-tooltip>
                        </div>
                      </div>
                      <div
                        v-if="item.material.fileToHexagonStatus !== 1 && item.material.fileToHexagonStatus !== 2"
                        class="list-item-text"
                      >
                        <div class="km-t-ellipsis1">
                          {{ $t('marketing.commons.cjz_ec37bb') }}{{ item.material.creatorName || '--' }}
                        </div>
                        <div class="km-t-ellipsis1">
                          {{ $t('marketing.commons.gxsj_780fb9') }}{{ getTime(item.material.updateTime) }}
                        </div>
                        <div class="km-t-ellipsis1">
                          {{ $t('marketing.commons.lx_e91f5a') }}{{ materialType(item.material.objectType) }}
                        </div>
                      </div>
                      <div class="list-item-data">
                        <div class="data-area">
                            <div class="account">
                              {{ $t('marketing.commons.fwcs_f0be64') }}：
                                <span class="account-num">{{ item.pv }}</span>
                            </div>
                            <div class="account">
                              {{ $t('marketing.commons.fwrs_c3c959') }}：
                                <span class="account-num">{{ item.uv }}</span>
                            </div>
                            <div class="account">
                              {{ $t('marketing.commons.xss_0ac14d') }}：
                                <span class="account-num">{{ item.leadNum }}</span>
                            </div>
                          </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="list-item-right">
                  <div
                    v-if="item.material.fileToHexagonStatus === 1"
                    class="list-item-text"
                  >
                    <i>{{ $t('marketing.commons.wymsczqsd_91cae4') }}</i>
                  </div>
                  <div
                    v-else-if="item.material.fileToHexagonStatus === 2"
                    class="warning-text"
                  >
                  <i
                    :class="['el-icon-warning-outline']"
                    style="color:#f27474;font-size:14px;"
                  />
                  <span>{{ $t('marketing.commons.zmsb_e8d985') }} ：{{ item.material.fileToHexagonFailReason }}</span>
                  </div>
                  <div class="operate-area">
                    <template v-if="!hideOperation">
                      <template v-if="item.material.fileToHexagonStatus === 1">
                        <div
                          class="refresh"
                          @click="getContentList"
                        >
                          <i class="el-icon-refresh-right refresh-icon" />
                          {{ $t('marketing.commons.sx_694fc5') }}
                        </div>
                      </template>
                      <template v-else-if="item.material.fileToHexagonStatus === 2">
                        <FileToHexagon
                          :marketing-event-id="id"
                          :hexagon-site-id="item.material.id"
                          :systemSite="1"
                          @completed="handleFileToHexagonCompleted"
                          @onUpload="val => disabledFileUpload = val"
                        >
                          <fx-button
                            size="small"
                            type="text"
                            :disabled="disabledFileUpload"
                          >
                            {{ $t('marketing.commons.zxsc_bc8851') }}
                          </fx-button>
                        </FileToHexagon>
                        <fx-button
                          size="small"
                          type="text"
                          style="margin-left:10px"
                          @click="deleteRelation(item)"
                        >
                          {{ $t('marketing.commons.sc_2f4aad') }}
                        </fx-button>
                      </template>
                      <template v-else>
                        <fx-button
                          type="primary"
                          :class="{
                            'spread-btn-wrapper': true,
                            'unuse-button': item.material.objectType === 24 || isDisabledSpreadBtn(item.material)
                          }"
                          id="guide_spread-btn"
                          :disabled="item.material.objectType === 24 || isDisabledSpreadBtn(item.material) || item.material.status === 2"
                          size="small"
                          @click.stop="showPreviewContent(item)"
                        >
                          <div class="spread-btn">
                            <i class="iconfont">&#xe625;</i><span>{{ $t('marketing.commons.tg_9a8e91') }}</span>
                            <QuestionTooltip
                              v-if="item.material.objectType === 24 || disabledSpreadBtn"
                              class="question"
                              :offset="135"
                              effect="dark"
                            >
                              <div
                                slot="question-content"
                                style="max-width: 280px; color: #fff"
                              >
                                <div class="question-tips">
                                  {{ CampaignReviewStatusTips[lifeStatus] }}
                                </div>
                              </div>
                            </QuestionTooltip>
                          </div>
                        </fx-button>
                        <fx-button plain size="small" class="operate-btn" @click.stop="showDetailSideslip(item)">
                          <i class="iconfont">&#xe61d;</i>
                        </fx-button>
                        <fx-button plain size="small" class="operate-btn" @click.stop="ShowMaterialPreview(item)">
                          <i class="iconfont iconpreview" />
                        </fx-button>
                        <fx-popover
                          :trigger="popoverTrigger"
                          placement="bottom-start"
                          popper-class="spread-content-list-more-popover"
                          :zIndex="1000"
                          :offset="10"
                          style="padding: 0;margin-left: 10px;"
                          :arrowOffset="1"
                          :append-to-body="false"
                          @click="handlePopoverClick"
                        >
                          <div :class="['more-set__popover-content',index === 0 && 'meetinghome-more-set-wrapper']">
                            <div
                              class="more-set__popover-item"
                              v-for="(action, actionIndex) in genOperateList(item)"
                              :key="actionIndex"
                              @click.stop="handleOperate(action,item)"
                            >
                              <i :class="['iconfont', action.icon]" />
                              <span>{{ action.label }}</span>
                              <!-- 表单常见下才显示 -->
                              <i
                                v-if="action.type === 'setting' && !item.material.hadCrmMapping && item.material.formId"
                                :class="['iconfont iconjingshi_mian']"
                                style="color: #FF522A;margin-left: 6px;"
                              />
                            </div>
                          </div>
                          <fx-button
                            slot="reference"
                            icon="el-icon-more"
                            @click="(e) => handlePopoverClick(e)"
                            size="small"
                            :class="['more-set__btn',item.material.siteType === 'EVEN_HOMEPAGE' && 'meetinghome-more-set__btn']"
                          />
                        </fx-popover>
                      </template>
                    </template>
                    <el-button
                      v-else
                      type="primary"
                      size="small"
                      @click.stop="showDetailSideslip(item)"
                    >
                      <div class="spread-btn">
                        <span>{{ $t('marketing.commons.ckxq_5b48db') }}</span>
                      </div>
                    </el-button>
                  </div>
                </div>
                <div
                  v-if="item.error"
                  class="list-item-error"
                >
                  <img
                    class="error-icon"
                    :src="require('@/assets/images/icon/error.png')"
                  >
                  <span class="error-message">{{ $t('marketing.pages.content_marketing.wymszccwfl_859891') }}</span>
                  <span
                    class="error-setting"
                    @click="setSomething"
                  >{{ $t('marketing.commons.ljsz_dcadb5') }} ></span>
                </div>
              </div>
            </div>
            <div v-if="pageData.totalCount > pageData.pageSize">
              <v-pagen
                :pagedata="pageData"
                @change="handlePageChange"
              />
            </div>
          </div>
          <content-module
            v-else-if="!showContentList && showTemplate"
            @getContentList="getContentList"
          />
          <AssociateContentToCampaignGuide
            v-else
            :marketing-event-id="id"
            @onMaterialAdded="onMaterialAdded"
            @onCommand="onGuideonCommand"
          />
        </div>
      </div>

      <detail-sideslip
        v-if="isShowMaterialDetail"
        :visible="isShowMaterialDetail"
        :material="message"
        :marketing-event-id="id"
        :from-spread="true"
        :is-readonly="detailSideslipIsReadonly"
        :marketing-event-name="marketingEventName"
        @close="handleCloseDetailSideslip"
        @previewPoster="showPreviewPoster"
        @previewMaterial="ShowMaterialPreview"
        @reflesh="reflesh"
        @uploadSuccess="handleFileToHexagonCompleted"
      />

      <material-preview
        v-if="isShowpreviewMaterial"
        :visible="isShowpreviewMaterial"
        :material="message"
        :marketing-event-id="id"
        @close="handleClosePreviewSideslip"
      />

      <SpreadSideslipDialog
        v-if="isShowPreviewContent"
        :visible="isShowPreviewContent"
        :marketing-event-id="id"
        :material="material"
        @refresh="getContentList"
        @close="handleClosePreviewContent"
      />
      <MaterialPosterDialog
        v-if="isShowMaterialPoster"
        :visible="isShowMaterialPoster"
        :material="message.material"
        :marketing-event-id="id"
        @close="handleCloseMaterialPoster"
      />
      <ExternalContentCreate
        v-if="externalContentVisible"
        :id="message.material.id"
        :visible.sync="externalContentVisible"
      />
      <SiteSettingDialog
        v-if="settingDialogVisible"
        :visible.sync="settingDialogVisible"
        :site-id="message.material.objectType === 26 ? message.material.id : ''"
        :form-id="message.material.objectType === 26 ? message.material.formId : message.material.id"
        :form-usage="message.material.formUsage"
        :relation-id="message.material.relationId"
        :site-setting-type="message.material.siteType === 'EVEN_HOMEPAGE' ? 1 : 2"
        :is-apply-object="message.material.isApplyObject"
        show-activity-member-set
        @update:submit="handleSettingSubmit"
      />
      <copy-site-dialog
        v-if="siteNameDialogVisible"
        :material="message.material"
        :marketing-event-id="id"
        :default-site-info="{
          name: `${message.material.title}-${$t('marketing.commons.fb_0428b3')}`
        }"
        :visible.sync="siteNameDialogVisible"
        @refresh="getContentList"
      />
      <!-- 推广内容这里创建的海报返回内容海报列表 -->
      <qrposter-create-dialog
        v-if="isShowCreateDialog"
        :visible.sync="isShowCreateDialog"
        :disabled-jump-type="true"
        :jumpModelTypeConfig="{
          model_type: message.material.siteType === 'EVEN_HOMEPAGE' ? 4 : 1,
          model_marketing_materiel: {
            id: message.material.siteType === 'EVEN_HOMEPAGE' && message.material.targetObjectId
              ? message.material.targetObjectId
              : message.material.id,
            type: formatMaterialType,
            title: message.material.name || message.material.title
          },
        }"
        :default-marketing-event-id="id"
        @complete:create="onCompleteCreate"
      ></qrposter-create-dialog>
    </div>
</template>

<script>
import { mapState, mapActions } from 'vuex'

import ContentModule from './content-module.vue'
import VPagen from '@/components/kitty/pagen.vue'
import DetailSideslip from './detail-sideslip.vue'
import MaterialPreview from './material-preview.vue'
import SpreadSideslipDialog from '@/components/SpreadSideslipDialog/index.vue'
import QuestionTooltip from '@/components/questionTooltip/index.vue'
import AssociateContentToCampaignButton from '@/components/AssociateContentToCampaignButton/index.vue'
import AssociateContentToCampaignGuide from '@/components/AssociateContentToCampaignGuide/index.vue'
import { MaterialInstance } from '@/components/select-material-dialog/index.vue'
import {
  getTime, getName, getUrl, aShortUrl, materialType, materialTitle, materialPhoto,
} from '../../func.js'
import Empty from '@/components/common/empty.vue'
import http from '@/services/http/index.js'
import { generateFeedKey } from '@/utils/feedKey.js'
import { CampaignReviewStatus, CampaignReviewStatusLabel, CampaignReviewStatusTips } from '@/utils/statusEnum.js'
import FileToHexagon from '@/components/file-to-hexagon/index.vue'
import { confirm } from '@/utils/globals.js'
import MaterialPosterDialog from '@/components/materialPoster/index.vue'
import ExternalContentCreate from '@/pages/external-content/components/external-content-create.vue'
import SiteSettingDialog from '@/pages/site/site-setting-dialog/index.vue'
import QrposterCreateDialog from '@/components/qrposter-create-dialog/index.vue';
import CopySiteDialog from '@/pages/content-marketing/content-dashboard/module/copy-site-dialog.vue'
import { formatMaterialType } from "@/constants/materialTypeMapping.js";

function FSUtilGetFscFileUrl(NPath) {
  return NPath ? FS.util.getFscFileUrl(NPath) : ''
  // img.src 禁止赋值空串，但高级浏览器没事
}

export default {
  components: {
    ElTooltip: FxUI.Tooltip,
    ElButton: FxUI.Button,
    ElPopover: FxUI.Popover,
    ElCheckbox: FxUI.Checkbox,
    ContentModule,
    VPagen,
    QuestionTooltip,
    DetailSideslip,
    MaterialPreview,
    SpreadSideslipDialog,
    AssociateContentToCampaignButton,
    AssociateContentToCampaignGuide,
    Empty,
    FileToHexagon,
    MaterialPosterDialog,
    ExternalContentCreate,
    SiteSettingDialog,
    QrposterCreateDialog,
    CopySiteDialog
  },
  provide() {
    return {
      refresh: this.getCardMessage,
    }
  },
  props: {
    applyObject: {
      type: Object,
      default: () => ({}),
    },
    mobileDisplay: {
      type: Object,
      default: () => ({}),
    },
    id: {
      type: String,
      default: '',
    },
    marketingEventName: {
      type: String,
      default: '',
    },
    materialLabel: {
      type: String,
      default: '',
    },
    lifeStatus: {
      type: String,
      default: '',
    },
    showTemplate: {
      type: Boolean,
      default: true,
    },
    hideOperation: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      crumbs: [
        {
          text: $t('marketing.commons.hdyx_f631fa'),
          to: { name: 'content-marketing' },
        },
        {
          text: '--',
          to: false,
        },
      ],
      loading: true,
      // 聚合信息部分
      headData: {}, // 头信息
      leadNum: 0,
      uv: 0,
      enterpriseSpreadNum: 0, // 企业推广次数
      isEmptyPie: false,

      wxCode: '',
      h5Code: '',
      baiduCode: '',

      // 物料列表部分
      showContentList: true, // 后续拓展筛选功能时，避免contentList为空，导致显示了模板
      contentList: [],
      pageData: {
        layout: 'prev, pager, next, total, sizes, jumper',
        pageNum: 1,
        totalCount: 0,
        pageSize: 5,
        pageSizes: [5, 10, 20],
      },

      // 侧滑页
      message: {}, // 点击后的物料信息
      material: {}, // 推广侧滑页的物料信息
      poster: {}, // 仅用于存储回掉的海报预览信息
      isShowMaterialDetail: false, // 物料详情
      isShowPreviewPoster: false, // 海报预览侧滑页
      isShowPreviewContent: false, // 推广按钮侧滑页
      selectMaterialDialog: false, // 选择素材弹窗
      isShowpreviewMaterial: false, // 物料预览
      showClueDetail: false, // 线索明细
      CampaignReviewStatusLabel,
      CampaignReviewStatusTips,
      detailSideslipIsReadonly: false,
      disabledFileUpload: false,
      keyword: '',
      isShowMaterialPoster: false,
      externalContentVisible: false,
      settingDialogVisible: false,
      siteNameDialogVisible: false,
      isShowCreateDialog: false,
      popoverTrigger: 'click',
      activeMaterialId: ''
    }
  },
  computed: {
    ...mapState('MarketingEventSet', ['marketingEventAudit']),
    disabledSpreadBtn() {
      if (this.marketingEventAudit) {
        return this.lifeStatus !== CampaignReviewStatus.normal
      }
      return false
    },
    applyObjectData() {
      return {
        enable: true,
        title: $t('marketing.commons.sdwbmnr_e9cad6'),
        tips: $t('marketing.commons.gxzcnrzbhd_a2fbfa', { data: { option0: this.materialLabel, option1: this.materialLabel } }),
        ...this.applyObject,
      }
    },
    mobileDisplayData() {
      return {
        enable: true,
        title: $t('marketing.commons.yddzs_9a4b44'),
        tips: $t('marketing.commons.gxzhzyddzz_2b5b74', { data: { option0: this.materialLabel } }),
        ...this.mobileDisplay,
      }
    },
    formatMaterialType() {
      const material = this.message.material
      return formatMaterialType(material.objectType);
    },
    conferenceDetail() {
      return this.$store.state.MeetingMarketing.conferenceDetail
    },
  },
  created() {
    this.getContentList()
    this.queryMarketingEventCommonSetting()
    this.$root.$on('set-popover-trigger', (trigger) => {
      this.popoverTrigger = trigger
      if (trigger === 'click') {
        // 点击关闭
        const btn = document.querySelector('.meetinghome-more-set__btn');
        if (btn) btn.click();
      }
    })
  },
  methods: {
    ...mapActions('MarketingEventSet', ['queryMarketingEventCommonSetting']),
    materialType,
    materialTitle,
    materialPhoto,
    getTime,
    getName,
    getUrl,
    handlePopoverClick(e) {
      e.stopPropagation()
    },
    deleteRelation(item) {
      if (item.leadNum || item.pv || item.uv) {
        FxUI.Message.error(
          $t('marketing.pages.content_marketing.scnryytgsj_55a8cd'),
        )
        return
      }
      confirm(
        $t('marketing.commons.sfqrsc_59c27b'),
        $t('marketing.commons.ts_02d981'),
        {
          confirmButtonText: $t('marketing.commons.qd_38cf16'),
          cancelButtonText: $t('marketing.commons.qx_625fb2'),
          type: 'warning',
        },
      )
        .then(() => {
          http
            .contentMarketingDeleteRelation({
              marketingEventId: this.id,
              objectId: item.material.id,
              objectType: item.material.objectType,
            })
            .then(res => {
              if (res && res.errCode === 0) {
                FxUI.Message({
                  message: $t('marketing.commons.sccg_43593d'),
                  type: 'success',
                })
                this.getContentList()
              } else {
                FxUI.Message.error($t('marketing.commons.scsb_acf066'))
              }
            })
        })
        .catch(() => {})
    },
    isDisabledSpreadBtn(material) {
      if (material && material.fileToHexagonStatus === 1) return true
      return this.disabledSpreadBtn
    },
    handleFileToHexagonCompleted() {
      this.getContentList()
    },
    onCompleteCreate() {
      console.log('onCompleteCreate!!!!!!')
      this.isShowCreateDialog = false
      // this.isShowMaterialPoster = true
      // this.getContentList()
      this.$nextTick(() => {
        this.isShowMaterialPoster = true
        this.getContentList()
        this.$root.$emit('poster-created')
      })
    },
    handleRefresh(e) {
      e.preventDefault()
      this.getContentList()
    },
    handleApplyObjectChange(val, index) {
      const item = this.contentList[index] || {}
      const isApplyObject = !!val
      http
        .updateIsApplyObject({
          id: item.material.relationId,
          isApplyObject,
        })
        .then(({ errCode }) => {
          if (errCode === 0) {
            FxUI.Message.success(
              isApplyObject ? $t('marketing.commons.szcg_f6088e') : $t('marketing.commons.qxszcg_beba4f'),
            )
          }
          if (errCode !== 0) {
            FxUI.Message.error($t('marketing.commons.szsb_9f9603'))
            item.material.isApplyObject = false
            this.$set(this.contentList, index, item)
          }
        })
    },
    handleMobileDisplayChange(val, index) {
      const item = this.contentList[index] || {}
      const isMobileDisplay = !!val
      http
        .setContentMobileDisplay({
          marketingEventId: this.id,
          objectId: item.material.id,
          objectType: item.material.objectType,
          isMobileDisplay: !!val,
        })
        .then(({ errCode }) => {
          if (errCode === 0) {
            FxUI.Message.success(
              isMobileDisplay ? $t('marketing.commons.szcg_f6088e') : $t('marketing.commons.qxszcg_beba4f'),
            )
          }
          if (errCode !== 0) {
            FxUI.Message.error($t('marketing.commons.szsb_9f9603'))
            item.material.isMobileDisplay = false
            this.$set(this.contentList, index, item)
          }
        })
    },
    onMaterialAdded() {
      // FxUI.Message.success($t('marketing.commons.tjcg_3fdaea'))
      this.getContentList()
    },
    onGuideonCommand(command) {
      if(this.$refs.AssociateContentButton && this.$refs.AssociateContentButton.handleCommand) {
        this.$refs.AssociateContentButton.handleCommand(command)
      }
    },
    getContentList() {
      if (!this.id) return
      this.loading = true
      // 获取推广列表
      http
        .listMarketingContent({
          marketingEventId: this.id,
          objectTypes: [4, 6, 26, 16, 9999],
          pageNo: this.pageData.pageNum,
          pageSize: this.pageData.pageSize,
          keyword: this.keyword,
        })
        .then(res => {
          if (res && res.errCode === 0) {
            const { data } = res
            this.pageData.totalCount = data.totalCount
            if (this.pageData.totalCount > 0) {
              this.showContentList = true
            } else {
              this.showContentList = false
            }
            this.contentList = data.result.map(item=>{
              if(item.material.siteType === 'EVEN_HOMEPAGE') {
                item.material.targetObjectId = this.conferenceDetail?.id
                item.material.targetObjectType = 13
              }
              return item
            })
          }
          this.loading = false
        })
    },

    advertiseDetail() {
      // 跳转到推广雷达
      this.$router.push({ name: 'content-radar', params: { id: this.id } })
    },
    createadvertiseContent() {
      // 创建推广内容
      this.selectMaterialDialog = true
    },
    handlePageChange(data) {
      this.pageData.pageNum = data.pageNum
      this.pageData.pageSize = data.pageSize
      this.getContentList()
    },

    // 侧滑页物料详情
    showDetailSideslip(message) {
      this.detailSideslipIsReadonly = false
      if (message.material.objectType === 24 || message.material.fileToHexagonStatus === 1) {
        // 海报数据另外处理
        // 海报预览...
        return
      }
      if (message.material.fileToHexagonStatus === 2) {
        this.detailSideslipIsReadonly = true
      }
      this.$nextTick(() => {
        this.handleCloseDetailSideslip() // 先摧毁，但是因退出动画，导致并没效果
        this.isShowMaterialDetail = true
        this.message = message
        this.activeMaterialId = message.material.id
      })
    },
    handleCloseDetailSideslip(reflesh) {
      this.isShowMaterialDetail = false
      this.activeMaterialId = ''
      if (reflesh) {
        this.getContentList()
      }
    },
    reflesh() {
      this.getContentList()
    },

    // 侧滑页海报预览
    showPreviewPoster(poster, message) {
      this.$nextTick(() => {
        this.isShowPreviewPoster = true
        this.message = message
        this.poster = poster
      })
    },
    handleClosePreviewPoster() {
      this.isShowPreviewPoster = false
    },

    // 预览按钮回调
    ShowMaterialPreview(message) {
      this.message = message
      this.isShowpreviewMaterial = true
    },
    handleClosePreviewSideslip() {
      this.isShowpreviewMaterial = false
    },

    // 推广按钮 支持会议主页作为推广内容
    showPreviewContent(message) {
      this.$nextTick(() => {
        this.activeMaterialId = message.material.id
        this.isShowPreviewContent = true
        this.message = message
        const formatMaterial = {
          ...MaterialInstance.formatDataByObjectType(message.material.objectType, message.material),
          creatorName: message.material.creatorName,
          updateTime: this.getTime(message.material.updateTime),
          isMobileDisplay: message.isMobileDisplay,
        }
        if(message.material.siteType === 'EVEN_HOMEPAGE') {
          formatMaterial.targetObjectId = this.conferenceDetail?.id
          formatMaterial.targetObjectType = 13
          formatMaterial.siteType = 'EVEN_HOMEPAGE'
          formatMaterial.conferenceId = this.conferenceDetail?.id
        }
        this.material = formatMaterial
      })
    },
    handleClosePreviewContent() {
      this.isShowPreviewContent = false
      this.activeMaterialId = ''
    },

    copyLink(item) {
      const url = item._shortUrl || this.getUrl(item)
      try {
        FS.util.inputCopy(url)
        FxUI.Message({
          message: $t('marketing.commons.fzcg_20a495'),
          type: 'success',
        })
      } catch (err) {
        FxUI.Message.error($t('marketing.commons.fzsb_5154ae'))
      }
    },
    fetchShortUrl(item) {
      item.marketingEventId = this.id
      aShortUrl('_shortUrl', item)
    },
    getCode(material) {
      // 获取二维码
      this.baiduCode = ''
      this.h5Code = ''
      this.wxCode = ''

      http
        .getQrCodeByMarketingEventIdAndObjectInfo({
          feedKey: generateFeedKey(material.id),
          objectId: material.id,
          objectType: material.objectType,
          marketingEventId: this.id,
          marketingActivityId: material.defaultMarketingActivityId,
          h5QrCodeType: {
            4: 10006,
            6: 10005,
            16: 10001,
            26: 10010,
            9999: 19999,
          }[material.objectType],
          h5QrCodeValue: JSON.stringify({
            byshare: 1,
            id: material.id,
            type: material.objectType === 26 ? 1 : undefined,
            marketingActivityId: material.defaultMarketingActivityId || '',
          }),
          miniappQrCodeValue: JSON.stringify({
            id: material.id,
            marketingActivityId: material.defaultMarketingActivityId || '',
          }),
        })
        .then(res => {
          this.h5Code = FSUtilGetFscFileUrl(res.data.h5QrCodeAPath)
          this.wxCode = FSUtilGetFscFileUrl(res.data.miniappQrCodeAPath)
          this.baiduCode = FSUtilGetFscFileUrl(res.data.baiduQrCodeAPath)
        })
    },
    showClue() {
      this.showClueDetail = true
    },
    handleCloseDetail() {
      this.showClueDetail = false
    },
    handleOperate(action,item){
      const { type } = action
      this.message = item
      switch(type){
        case 'edit':
          this.handleEditMaterial(item)
          break
        case 'setting':
          this.handleSetMaterial(item)
          break
        case 'poster':
          this.handleCreatePoster(item)
          break
        case 'posterContent':
          this.handlePosterContent(item)
          break
        case 'copy':
          this.handleCopySite(item)
          break
        case 'delete':
          this.handleDeleteMaterial(item)
          break
      }
    },
     handleEditMaterial(item) {
      let data = item || this.message
      let name = ''
      const params = {}
      const query = {
        from: 'dialog',
        marketingEventId: this.id,
      }
      switch (data.material.objectType) {
        case 4:
          name = 'product-detail'
          params.pid = data.material.id
          break
        case 6:
          name = 'article-edit'
          params.id = data.material.id
          break
        case 16:
          name = 'form-edit'
          params.id = data.material.id
          break
        case 26:
          name = 'site-design'
          params.siteId = data.material.id
          break
        case 9999:
          this.externalContentVisible = true
          return
        default:
          return
      }
      if(data.material.siteType === 'EVEN_HOMEPAGE') {
        query.conferenceId = this.conferenceDetail?.id
      }
      window.open(
        this.$router.resolve({
          name,
          params,
          query,
        }).href,
        '_blank',
      )
    },
    handleSetMaterial() {
      this.settingDialogVisible = true
    },
    handleSettingSubmit() {
      this.message.material.hadCrmMapping = true
      if (this.message.material.siteType === 'EVEN_HOMEPAGE') {
        this.$store.dispatch('queryConferenceDetail', {
          conferenceId: this.conferenceDetail?.id,
        })
      }
      this.getContentList()
    },
    handleCreatePoster(){
      this.isShowCreateDialog = true
    },
    handlePosterContent(item){
      this.activeMaterialId = item.material.id
      this.isShowMaterialPoster = true
    },
    handleCloseMaterialPoster(){
      this.activeMaterialId = ''
      this.isShowMaterialPoster = false
    },
    handleCopySite() {
      this.siteNameDialogVisible = true
    },
    handleDeleteMaterial(item) {
      if (this.message.leadNum || this.message.pv || this.message.uv) {
        FxUI.Message.error(
          $t('marketing.pages.content_marketing.scnryytgsj_55a8cd'),
        )
        return
      }
      confirm(
        $t('marketing.commons.sfqrsc_59c27b'),
        $t('marketing.commons.ts_02d981'),
        {
          confirmButtonText: $t('marketing.commons.qd_38cf16'),
          cancelButtonText: $t('marketing.commons.qx_625fb2'),
          type: 'warning',
        },
      )
        .then(() => {
          http
            .contentMarketingDeleteRelation({
              marketingEventId: this.id,
              objectId: this.message.material.id,
              objectType: this.message.material.objectType,
            })
            .then(res => {
              if (res && res.errCode === 0) {
                FxUI.Message({
                  message: $t('marketing.commons.sccg_43593d'),
                  type: 'success',
                })
                this.getContentList()
              } else {
                FxUI.Message.error($t('marketing.commons.scsb_acf066'))
              }
            })
        })
        .catch(() => {})
    },
    genOperateList(item) {
      // 基础选项
      let options = [
        {
          label: $t('marketing.commons.nrhb_77b1ed'),
          icon: 'icontupian2x',
          type: 'posterContent',
        },
        {
          label: $t('marketing.pages.content_marketing.bjym_49bcb8'),
          icon: 'iconbianji',
          type: 'edit',
        },
      ]

      if (item.material.objectType === 26) {
        options.push({
          label: $t('marketing.commons.fzwym_5a64c1'),
          icon: 'iconfuzhi',
          type: 'copy',
        })
      }

      // 在第二个位置插入设置选项
      if (item.material.objectType === 26 || item.material.objectType === 16) {
        options.push({
          label: $t('marketing.commons.sz_e366cc'),
          icon: 'iconshezhi',
          type: 'setting',
        })
      }

      // 除了会议主页 都可以删除
      if (item.material.siteType !== 'EVEN_HOMEPAGE') {
        options.push({
          label: $t('marketing.commons.sc_2f4aad'),
          icon: 'iconshanchu2',
          type: 'delete',
        })
      }
      return options
    },
    handleResetPageNum(){
      this.pageData.pageNum = 1;
      this.getContentList()
    },
  },
}
</script>

<style lang="less" scoped>
@icon: '../../../../assets/images/icon/';

.spread-content-list {
  width: 100%;
  height: 100%;
  min-height: 317px;
  display: flex;
  flex-flow: column nowrap;
  background-color: #f2f2f5;
  overflow-x: auto;
  border-radius: 8px;
  .overview__row {
    display: flex;
  }
  .content-graph {
    flex-shrink: 0;
    border-radius: 2px;
    display: flex;
    justify-content: space-between;
    padding: 16px 16px 0 16px;
    .graph-item {
      flex: 1;
      width: 33%;
      height: 160px;
      overflow: unset;
      margin-right: 16px;
      > div:first-child {
        height: 45px;
      }
      &:last-of-type {
        margin-right: 0;
      }
    }
    .graph-clue {
      cursor: pointer;
      color: var(--color-primary06,#407FFF);
    }
    .funnel-wrap {
      width: 100%;
      position: relative;
      .funnel-flow {
        top: 16px;
        left: 10%;
        width: 40%;
        height: calc(100% - 35px);
        position: absolute;
        display: flex;
        flex-flow: column nowrap;
        justify-content: center;
        > span {
          font-size: 12px;
        }
        .funnel-uv {
          position: absolute;
          top: 0;
          left: 20px;
          height: calc(50% - 20px);
          width: calc(100% - 20px);
          border-top: 1px solid #ffe06c;
          border-left: 1px solid #ffe06c;
        }
        .funnel-leadNum {
          position: absolute;
          bottom: 0;
          left: 20px;
          height: calc(50% - 20px);
          width: calc(100% - 20px);
          border-bottom: 1px solid #b5df87;
          border-left: 1px solid #b5df87;
        }
      }
    }
    .funnel-chart {
      height: 100px;
    }
    .pie-wrap {
      margin: 0 12px;
      .empty-pie {
        text-align: center;
        padding-top: 7px;
        color: #666;
        img {
          display: inline-block;
          height: 80px;
          margin-right: 20px;
          vertical-align: middle;
        }
      }
    }
    .pie-chart {
      height: 115px;
      margin-top: -6px;
    }
    .graph-slot-right {
      color: var(--color-primary06,#407FFF);
      cursor: pointer;
    }
    .graph-conversion {
      text-align: center;
      padding-top: 15px;
      span {
        color: @color-subtitle;
        font-size: 12px;
      }
      p {
        margin-top: 8px;
        font-size: 20px;
        color: @color-title;
      }
    }
  }
  .content-advertise {
    background-color: #ffffff;
    border-radius: 2px;
    display: flex;
    flex-flow: column nowrap;
    flex: 1;
    .advertise-header {
      flex-shrink: 0;
      display: flex;
      justify-content: space-between;
      padding: 14px 12px;
      &-left {
        display: flex;
        align-items: center;
      }
      &-title {
        color: #181c25;
        font-size: 14px;
        font-weight: 700;
        line-height: 20px;
        margin-right: 20px;
      }
      &-create {
        display: flex;
        color: var(--color-primary06,#0C6CFF);
        font-size: 14px;
        line-height: 20px;
        cursor: pointer;
      }
      .create-icon {
        margin: 0px 4px;
        width: 12px;
        height: 12px;
      }
    }
    .advertise-body {
      height: 100%;
      border-top: 1px solid var(--color-neutrals05);

      .advertise-content-list {
        height: 100%;
        overflow: hidden;
        .content-list {
          overflow: auto;
        }
        .content-list-item {
          padding: 12px;
          border-bottom: 1px solid var(--color-neutrals05);
          position: relative;
          display: flex;
          justify-content: space-between;
          align-items: center;
          &.unuse-wrapper{
            opacity: .7;
          }
          &:hover {
            background-color: #f2f3f5;
          }
        }
        .list-item-left {
          min-width: 300px;
          flex: 1;
        }
        .list-item-head {
          height: 100%;
          display: flex;
          align-items: center;
          cursor: pointer;
        }
        .list-item-photo {
          width: 160px;
          height: 110px;
          flex-shrink: 0;
          margin-right: 12px;
          background-size: cover;
          background-position: center;
          background-repeat: no-repeat;
          cursor: pointer;
          position: relative;
          border-radius: 4px;

          .icon {
            width: 16px;
            height: 16px;
            margin-left: 8px;
          }
          .type-icon-wrapper {
            position: absolute;
            bottom: 0;
            right: 0;
            height: 20px;
            width: 40px;
            font-size: 14px;
            font-weight: 600;
            color: #fff;
            text-align: center;
            line-height: 20px;
          }
        }
        .list-item-message {
          width: 100%;
          min-width: 0;
        }
        .warning-text {
          color: #f27474;
        }
        .list-item-title {
          display: flex;
          font-size: 16px;
          color: var(--color-neutrals19);
          font-weight: 700;
          margin-bottom: 10px;
          line-height: 24px;
        }
        .title-text {
          cursor: pointer;
        }
        .el-icon-warning-outline {
          flex-shrink: 0;
          margin-left: 4px;
          font-size: 16px;
          line-height: 22px;
        }
        .title-unuse {
          flex-shrink: 0;
          color: #181C25;
          border: 1px solid #c1c5ce;
          border-radius: 4px 0px 0px 0px;
          background: var(--color-special01);
          line-height: 18px;
          border-radius: 2px;
          padding: 1px 4px 1px 6px;
          font-size: 12px;
          position: absolute;
          left: 0;
          top: 0;
        }
        .list-item-text {
          color: var(--color-neutrals11);
          font-size: 12px;
          line-height: 18px;
          display: flex;
          margin-bottom: 10px;
          > div {
            margin-right: 24px;
          }
        }
        .list-item-data{
          .data-area{
            display: flex;
            align-items: center;
            gap: 24px;
            .account {
              color: var(--color-neutrals15);
              font-size: 12px;
              font-weight: 400;
              line-height: 18px;
            }
            .account-num {
              color: var(--color-neutrals19);
              font-size: 18px;
              line-height: 28px;
            }
          }
        }
        .list-item-tool {
          height: 50px;
          overflow: hidden;
          line-height: 50px;
          flex-shrink: 0;
          color: var(--color-primary06,#407FFF);
          position: relative;
          > span {
            position: relative;
            padding-left: 25px;
            margin-right: 44px;
            cursor: pointer;
          }
          .tool-link {
            &:before {
              content: '';
              width: 15px;
              height: 15px;
              position: absolute;
              left: 0;
              top: 0;
              background: url('@{icon}link.png') center / cover no-repeat;
            }
          }
          .tool-code {
            &:before {
              content: '';
              width: 15px;
              height: 15px;
              position: absolute;
              left: 0;
              top: 0;
              background: url('@{icon}code.png') center / cover no-repeat;
            }
          }
          .tool-poster {
            &:before {
              content: '';
              width: 15px;
              height: 15px;
              position: absolute;
              left: 0;
              top: 0;
              background: url('@{icon}img.png') center / cover no-repeat;
            }
          }
        }
        .tool-unuse {
          color: #c1c5ce;
          > span {
            cursor: default;
          }
          &-block {
            z-index: 100;
            opacity: 0;
            width: 100%;
            height: 100%;
            position: absolute;
          }
        }
        .list-item-right {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          .operate-area {
            display: flex;
            align-items: center;
            justify-content: space-around;

            .refresh {
              color: @color-link;
              font-size: 14px;
              cursor: pointer;

              .refresh-icon {
                margin-right: 3px;
                font-weight: bold;
              }
            }

            .more-set__btn {
              padding: 0 9px;
              height: 32px;
              border: 1px solid var(--color-neutrals07);
              background: var(--color-neutrals01);
            }
            .question {
              margin-left: 4px;
              filter: brightness(100);
            }
            .spread-btn-wrapper{
              height: 32px;
            }
            .spread-btn {
              display: flex;
              align-items: center;
            }
          }
          .operate-btn{
            padding: 0 9px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--4, 4px);
            border: 1px solid var(--color-neutrals07);
            background: var(--color-neutrals01);
            cursor: pointer;
            box-sizing: border-box;
            .iconfont{
              font-size: 14px;
              color: #000000;
            }
          }
        }
        .unuse-button {
          background-color: #cccccc;
          border: 1px solid #cccccc;
        }
        .list-item-error {
          position: absolute;
          bottom: 17px;
          right: 58px;
          height: 17px;
          display: flex;
          flex-flow: row nowrap;
          align-items: center;
        }
        .error-icon {
          margin-right: 6px;
          width: 15px;
          height: 15px;
        }
        .error-message {
          color: #ff3f3f;
          margin-right: 6px;
        }
        .error-setting {
          color: var(--color-primary06,#407FFF);
          cursor: pointer;
        }
      }
    }
  }
}
.spread-content-list-more-popover.el-popper {
    padding: 0;
    box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.15);
    border-radius: 3px;
  }
  .more-set__popover-content {
    padding: 10px 0;
    .more-set__popover-item {
      display: flex;
      padding: 6px 16px;
      align-items: center;
      color: var(--Text-H1, #181C25);
      font-size: 14px;
      font-weight: 400;
      line-height: 20px;
      cursor: pointer;

      &:hover {
        background-color: #f5f7fa;
      }

      .iconfont {
        margin-right: 6px;
        font-size: 14px;
      }
    }
  }

.more-set__btn {
  padding: 0 9px;
  height: 32px;
  border: 1px solid var(--color-neutrals07);
  background: var(--color-neutrals01);
}
</style>

<style lang="less">
.spread-content-list-more-popover.el-popper {
  padding: 0;
  box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.15);
  border-radius: 3px;

  .more-set__popover-content {
    padding: 10px 0;
    .more-set__popover-item {
      display: flex;
      padding: 6px 16px;
      align-items: center;
      color: var(--Text-H1, #181C25);
      font-size: 14px;
      font-weight: 400;
      line-height: 20px;
      cursor: pointer;

      &:hover {
        background-color: #f5f7fa;
      }

      .iconfont {
        margin-right: 6px;
        font-size: 14px;
      }
    }
  }
}
</style>
