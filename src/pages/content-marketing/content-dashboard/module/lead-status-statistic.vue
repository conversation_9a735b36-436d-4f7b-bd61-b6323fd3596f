<template>
  <div
    v-loading="isLoading"
    :class="$style.container"
  >
    <div :class="$style.title">
      <div :class="$style['title-content']">
        <span>{{ $t('marketing.commons.xszt_11fe18') }}</span>
        <QuestionTooltip
          :class="$style.question"
          :offset="135"
        >
          <div
            slot="question-content"
            style="max-width: 280px; color: #545861"
          >
            <div class="question-tips">
              {{ $t('marketing.pages.content_marketing.gjxsxssdzt_1faa91') }}
            </div>
          </div>
        </QuestionTooltip>
      </div>
      <div :class="$style.update">
        <span
          v-if="dataPreparing"
          :class="$style.loading"
        >
          <loading :class-name="$style.icon" />
          {{ $t('marketing.commons.sjjsz_f32ed8') }}
        </span>
      </div>
    </div>
    <div :class="$style.content">
      <div
        ref="graph"
        :class="$style.graph"
      />
      <div
        v-if="isEmpty"
        :class="$style.empty"
      >
        <Empty
          :class="$style['empty-wrapper']"
          :title="$t('marketing.commons.zwsj_21efd8')"
        />
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import http from '@/services/http/index.js'
import QuestionTooltip from '@/components/questionTooltip/index.vue'
import Empty from '@/components/common/empty.vue'
import loading from './loading.vue'

export default {
  components: {
    QuestionTooltip,
    loading,
    Empty,
  },
  props: {
    marketingEventId: {
      type: String,
      default: '',
    },
    updateTick: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      isLoading: true,
      itemList: [],
      updateTime: 0,
      dataPreparing: false,
    }
  },
  computed: {
    isEmpty() {
      const hasValueList = this.itemList.filter(item => item.count > 0)
      return hasValueList.length === 0
    },
  },
  watch: {
    marketingEventId() {
      this.init()
    },
    updateTick() {
      this.handleUpdateData()
    },
  },
  mounted() {
    this.init()
    window.addEventListener('resize', this.graphResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.graphResize)
    if (this.graph) {
      this.graph.dispose()
    }
  },
  methods: {
    init() {
      if (!this.marketingEventId) {
        return
      }

      http.leadStatusStatistic({ marketingEventId: this.marketingEventId })
        .then(res => {
          this.isLoading = false
          const { errCode, data } = res
          const {
            itemList = [],
            updateTime = 0,
          } = data || {}

          this.itemList = itemList
          this.updateTime = updateTime
          this.dataPreparing = false

          if (errCode === 930001) {
            this.dataPreparing = true
          }

          if (updateTime) {
            this.$emit('onUpdate', { updateTime })
          }

          this.render()
        })
        .catch(() => {
          this.isLoading = false
        })
    },
    render() {
      const defaultColor = ['#50AEF0', '#3ED0BE', '#88E679', '#FFCF42', '#FB8739', '#FF6678', '#D174FF']
      const seriesData = this.itemList.length === 0
        ? [
          { name: $t('marketing.commons.wfp_0e3de5'), value: 0 },
          { name: $t('marketing.pages.meeting_marketing.dcl_d16876'), value: 0 },
          { name: $t('marketing.commons.yzh_12588f'), value: 0 },
          { name: $t('marketing.commons.gjz_83f647'), value: 0 },
          { name: $t('marketing.commons.wx_1abbb1'), value: 0 },
        ]
        : this.itemList.map(item => {
          const { status, count } = item
          return {
            name: status,
            value: count,
          }
        })
      const options = {
        color: defaultColor,
        title: {
          show: this.itemList.length === 0,
          text: $t('marketing.commons.zwsj_21efd8'),
          left: 'center',
          top: 'middle',
          textStyle: {
            color: '#FFF',
            fontWeight: 'normal',
            fontSize: 14,
          },
        },
        grid: {
          top: 0,
          left: 20,
          right: 20,
          bottom: 40,
        },
        legend: {
          show: true,
          icon: 'circle',
          itemWidth: 8,
          itemHeight: 8,
          bottom: 10,
          data: seriesData.map(item => ({ name: item.name })),
        },
        series: [
          {
            data: seriesData,
            type: 'pie',
            radius: '50%',
            label: {
              color: '#545861',
              formatter: '{b}: {c} ({d}%)',
            },
            labelLine: {
              lineStyle: {
                color: '#DEE1E8',
              },
            },
          },
        ],
      }
      this.graph = echarts.init(this.$refs.graph)
      this.graph.setOption(options)
    },
    graphResize() {
      if (this.graph) {
        this.graph.resize()
      }
    },
    handleUpdateData() {
      this.isLoading = true
      http.calculateLeadData({ marketingEventId: this.marketingEventId })
        .then(res => {
          const { errCode } = res
          if (errCode === 0) {
            this.init()
          }
        })
    },
  },
}
</script>

<style lang="less" module>
.container {
  background-color: #ffffff;
  width: 100%;
  box-sizing: border-box;
  padding: 20px 16px;
  position: relative;
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
  border-right: 1px solid #DEE1E8;
  .title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #181c25;
    font-size: 14px;
    line-height: 20px;
    font-weight: bold;

    .title-content {
      display: flex;
      align-items: center;
    }

    .question {
      margin-left: 6px;
    }

    .update {
      display: flex;
      align-items: center;
      color: #91959E;
      font-size: 12px;

      .loading {
        display: flex;
        align-items: center;
        margin-right: 10px;

        .icon {
          margin-right: 10px;
        }
      }
    }
  }
  .graph-title {
    padding: 10px 0;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #545861;
    font-size: 12px;

    span {
      color: #181C25;
      font-size: 24px;
      font-weight: bold;
      padding: 0 8px;
    }
  }
  .graph {
    height: 248px;
  }
  .content {
    position: relative;
  }
  .empty {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #FFF;

    .empty-wrapper {
      padding: 0;
    }
  }
}
</style>
