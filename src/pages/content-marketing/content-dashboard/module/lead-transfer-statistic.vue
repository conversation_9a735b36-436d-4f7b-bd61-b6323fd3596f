<template>
  <div
    v-loading="isLoading"
    :class="$style.container"
  >
    <div :class="$style.title">
      <div :class="$style['title-content']">
        <span>{{ $t('marketing.pages.content_marketing.hkzhfb_741b17') }}</span>
        <QuestionTooltip
          :class="$style.question"
          :offset="135"
        >
          <div
            slot="question-content"
            style="max-width: 280px; color: #545861"
          >
            <div class="question-tips">
              {{ $t('marketing.pages.content_marketing.hslyxtszzd_a951d6') }}
            </div>
          </div>
        </QuestionTooltip>
      </div>
      <div :class="$style.update">
        <span
          v-if="dataPreparing"
          :class="$style.loading"
        >
          <loading :class-name="$style.icon" />
          {{ $t('marketing.commons.sjjsz_f32ed8') }}
        </span>
      </div>
    </div>
    <div :class="$style.content">
      <div
        ref="graph"
        :class="$style.graph"
      />
      <div
        v-if="isEmpty"
        :class="$style.empty"
      >
        <Empty
          :class="$style['empty-wrapper']"
          :title="$t('marketing.commons.zwsj_21efd8')"
        />
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import http from '@/services/http/index.js'
import QuestionTooltip from '@/components/questionTooltip/index.vue'
import Empty from '@/components/common/empty.vue'
import loading from './loading.vue'

const CONVERT_FUNNEL_MAP = {
  leadsCount: $t('marketing.commons.xs_ad46a9'),
  leadsMQLCount: 'MQL',
  leadsSQLCount: 'SQL',
  opportunityCount: $t('marketing.pages.content_marketing.sj_55290e'),
  winOpportunityCount: $t('marketing.pages.content_marketing.ydsj_6201ec'),
}

const FUNNEL_COLOR_LIST = [
  {
    type: 'linear',
    x: 0,
    y: 0,
    x2: 1,
    y2: 0,
    colorStops: [
      { offset: 0, color: '#3CACFA' },
      { offset: 1, color: '#7DCAFF' },
    ],
  },
  {
    type: 'linear',
    x: 0,
    y: 0,
    x2: 1,
    y2: 0,
    colorStops: [
      { offset: 0, color: '#58FBD4' },
      { offset: 1, color: '#36C2B6' },
    ],
  },
  {
    type: 'linear',
    x: 0,
    y: 0,
    x2: 1,
    y2: 0,
    colorStops: [
      { offset: 0, color: '#85E576' },
      { offset: 1, color: '#9EE78C' },
    ],
  },
  {
    type: 'linear',
    x: 0,
    y: 0,
    x2: 1,
    y2: 0,
    colorStops: [
      { offset: 0, color: '#FDB149' },
      { offset: 1, color: '#FFD440' },
    ],
  },
  {
    type: 'linear',
    x: 0,
    y: 0,
    x2: 1,
    y2: 0,
    colorStops: [
      { offset: 0, color: '#F15C35' },
      { offset: 1, color: '#FF9839' },
    ],
  },
  {
    type: 'linear',
    x: 0,
    y: 0,
    x2: 1,
    y2: 0,
    colorStops: [
      { offset: 0, color: '#FF5367' },
      { offset: 1, color: '#FF7383' },
    ],
  },
  {
    type: 'linear',
    x: 0,
    y: 0,
    x2: 1,
    y2: 0,
    colorStops: [
      { offset: 0, color: '#E18AFF' },
      { offset: 1, color: '#C260FF' },
    ],
  },
]

export default {
  components: {
    QuestionTooltip,
    loading,
    Empty,
  },
  props: {
    marketingEventId: {
      type: String,
      default: '',
    },
    updateTick: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      isLoading: true,
      updateTime: 0,
      dataPreparing: false,
      leadsCount: 0,
      leadsMQLCount: 0,
      leadsSQLCount: 0,
      opportunityCount: 0,
      winOpportunityCount: 0,
      transferMQLRate: 0,
      transferSQLRate: 0,
      transferOpportunityRate: 0,
      transferWinOpportunityRate: 0,
    }
  },
  computed: {
    isEmpty() {
      const {
        leadsCount, leadsMQLCount, leadsSQLCount,
        opportunityCount, winOpportunityCount,
      } = this
      return leadsCount === 0
        && leadsMQLCount === 0
        && leadsSQLCount === 0
        && opportunityCount === 0
        && winOpportunityCount === 0
    },
    renderData() {
      const rateNameMap = {
        leadsCount: $t('marketing.pages.content_marketing.zl_f6579a'),
        leadsMQLCount: $t('marketing.pages.content_marketing.zl_50af59'),
        leadsSQLCount: $t('marketing.pages.content_marketing.zsj_ca7448'),
        opportunityCount: $t('marketing.pages.content_marketing.ydl_248fd0'),
      }
      const rateValueMap = {
        leadsCount: 'transferMQLRate',
        leadsMQLCount: 'transferSQLRate',
        leadsSQLCount: 'transferOpportunityRate',
        opportunityCount: 'transferWinOpportunityRate',
      }

      return ['leadsCount', 'leadsMQLCount', 'leadsSQLCount', 'opportunityCount', 'winOpportunityCount']
        .map(el => ({
          name: CONVERT_FUNNEL_MAP[el],
          value: this[el],
          rateName: rateNameMap[el],
          rateValue: this[rateValueMap[el]],
        }))
    },
    mockRenderData() {
      return [2000, 1600, 1000, 800, 750, 550, 300, 110, 50, 10]
        .slice(0, this.renderData.length)
    },
  },
  watch: {
    marketingEventId() {
      this.init()
    },
    updateTick() {
      this.handleUpdateData()
    },
    renderData() {
      this.render()
    },
  },
  mounted() {
    this.graph = echarts.init(this.$refs.graph)
    this.init()
    window.addEventListener('resize', this.graphResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.graphResize)
    if (this.graph) {
      this.graph.dispose()
    }
  },
  methods: {
    init() {
      if (!this.marketingEventId) {
        return
      }

      http.leadTransferStatistic({ marketingEventId: this.marketingEventId })
        .then(res => {
          this.isLoading = false
          const { errCode, data } = res
          const {
            leadsCount = 0,
            leadsMQLCount = 0,
            leadsSQLCount = 0,
            opportunityCount = 0,
            winOpportunityCount = 0,
            transferMQLRate = 0,
            transferSQLRate = 0,
            transferOpportunityRate = 0,
            transferWinOpportunityRate = 0,
            updateTime = 0,
          } = data || {}

          this.leadsCount = leadsCount
          this.leadsMQLCount = leadsMQLCount
          this.leadsSQLCount = leadsSQLCount
          this.opportunityCount = opportunityCount
          this.winOpportunityCount = winOpportunityCount
          this.transferMQLRate = transferMQLRate
          this.transferSQLRate = transferSQLRate
          this.transferOpportunityRate = transferOpportunityRate
          this.transferWinOpportunityRate = transferWinOpportunityRate
          this.updateTime = updateTime
          this.dataPreparing = false

          if (errCode === 930001) {
            this.dataPreparing = true
          }

          if (updateTime) {
            this.$emit('onUpdate', { updateTime })
          }
        })
        .catch(() => {
          this.isLoading = false
        })
    },
    /**
     * 为了让label对齐，叠加两个同样的series，其中一个设置maxSize: 0，不绘制图形，只显示label
     * 另个只绘制图形，不显示label
     */
    render() {
      const defaultColor = ['#50AEF0', '#3ED0BE', '#88E679', '#FFCF42', '#FB8739', '#FF6678', '#D174FF']
      const height = this.graph.getHeight()
      const levelHeight = (height * 0.8) / this.renderData.length

      const options = {
        color: defaultColor,
        tooltip: {
          show: false,
        },
        grid: {
          top: 0,
          left: 20,
          right: 20,
          bottom: 40,
          containLabel: true,
        },
        series: [],
      }

      if (!this.isEmpty) {
        // 绘制label
        options.series.push({
          type: 'funnel',
          top: 30,
          right: '50%',
          left: 20,
          bottom: 20,
          minSize: 0,
          maxSize: 0,
          z: 1,
          sort: 'none',
          data: this.renderData,
          itemStyle: {
            borderWidth: 0,
          },
          label: {
            position: 'right',
            align: 'right',
            width: 200,
            formatter: param => {
              const {
                data: { name, value },
              } = param

              return `{label|█} {name|${name}} {value|${value}}`
            },
            rich: {
              label: {
                fontSize: 8,
              },
              name: {
                color: '#545861',
                fontFamily: 'Source Han Sans CN',
                lineHeight: 22,
                fontSize: 12,
              },
              value: {
                lineHeight: 18,
                fontSize: 12,
                color: '#181C25',
                textShadow: '0px 0px 2px #181C25',
              },
            },
          },
          labelLine: {
            lineStyle: {
              type: 'solid',
              color: '#DEE1E8',
            },
            length: 180,
          },
          tooltip: {
            show: false,
          },
        })
        // 绘制转化率
        options.series.push({
          type: 'funnel',
          top: 30,
          right: '50%',
          left: 20,
          bottom: 20,
          minSize: 0,
          maxSize: 0,
          z: 1,
          sort: 'none',
          data: this.renderData,
          label: {
            show: true,
            position: 'right',
            align: 'right',
            padding: [levelHeight, 0, 0, 0],
            verticalAlign: 'bottom',
            width: 200,
            formatter: param => {
              const {
                dataIndex, data: { rateName, rateValue },
              } = param

              if (!rateName || dataIndex === this.renderData.length - 1) {
                return ''
              }

              return `{name|${rateName}} {value|${rateValue}%}`
            },
            rich: {
              name: {
                color: '#545861',
                fontFamily: 'Source Han Sans CN',
                lineHeight: 22,
                fontSize: 10,
              },
              value: {
                lineHeight: 18,
                fontSize: 10,
                color: '#545861',
                textShadow: '0px 0px 2px #181C25',
              },
            },
          },
          labelLine: {
            lineStyle: {
              type: 'solid',
              color: '#DEE1E8',
              opacity: 0,
            },
            length: 100,
          },
          tooltip: {
            show: false,
          },
        })

        // 绘制漏斗图
        options.series.push({
          type: 'funnel',
          top: 30,
          right: '50%',
          left: 20,
          bottom: 20,
          gap: 0,
          data: this.mockRenderData,
          minSize: '10%',
          sort: 'none',
          label: {
            show: false,
          },
          emphasis: {
            label: {
              show: false,
            },
          },
          itemStyle: {
            borderWidth: 0,
            color: param => {
              const { dataIndex } = param
              return FUNNEL_COLOR_LIST[dataIndex] || FUNNEL_COLOR_LIST[FUNNEL_COLOR_LIST.length - 1]
            },
          },
          labelLine: {
            show: false,
          },
        })
      }
      this.graph.setOption(options)
    },
    graphResize() {
      if (this.graph) {
        this.graph.resize()
      }
    },
    handleUpdateData() {
      this.isLoading = true
      http.calculateLeadData({ marketingEventId: this.marketingEventId })
        .then(res => {
          const { errCode } = res
          if (errCode === 0) {
            this.init()
          }
        })
    },
  },
}
</script>

<style lang="less" module>
.container {
  background-color: #ffffff;
  width: 100%;
  box-sizing: border-box;
  margin-bottom: 12px;
  padding: 20px 16px;
  position: relative;
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
  .title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #181c25;
    font-size: 14px;
    line-height: 20px;
    font-weight: bold;

    .title-content {
      display: flex;
      align-items: center;
    }

    .question {
      margin-left: 6px;
    }

    .update {
      display: flex;
      align-items: center;
      color: #91959E;
      font-size: 12px;

      .loading {
        display: flex;
        align-items: center;
        margin-right: 10px;

        .icon {
          margin-right: 10px;
        }
      }
    }
  }
  .content {
    position: relative;
  }
  .graph-title {
    padding: 10px 0;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #545861;
    font-size: 12px;

    span {
      color: #181C25;
      font-size: 24px;
      font-weight: bold;
      padding: 0 8px;
    }
  }
  .graph {
    height: 248px;
  }
  .empty {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #FFF;

    .empty-wrapper {
      padding: 0;
    }
  }
}
</style>
