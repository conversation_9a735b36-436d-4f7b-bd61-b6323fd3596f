<!-- 组件说明 -->
<template>
  <div
    v-loading="loading"
    class="content-marketing-employee-rank__wrapper"
  >
    <div class="title">
      <div>{{ $t('marketing.commons.ygtgpm_b12107') }}</div>
    </div>
    <div class="radioWrap">
        <div @click="changeRadio('1')" :class="['btn-item',radio == '1' && 'active']">
          {{ $t('marketing.pages.content_marketing.xsb_211051') }}
        </div>
        <div @click="changeRadio('2')" :class="['btn-item',radio == '2' && 'active']">
          {{ $t('marketing.pages.content_marketing.qwkhb_cd5ca1') }}
        </div>
        <div @click="changeRadio('3')" :class="['btn-item',radio == '3' && 'active']">
          {{ $t('marketing.pages.content_marketing.qwkhb_cd5ca1') }}
        </div>
    </div>
    <div
      v-if="rankList.length"
      class="ranking-wrapper"
    >
      <div class="row header">
        <div class="row-item rank-index">
          {{ $t('marketing.commons.pm_a4dc00') }}
        </div>
        <div class="row-item rank-content">
          {{ $t('marketing.commons.ygxm_50df90') }}
        </div>
        <div class="row-item randk-num">
          {{ rankTypeNameLabel }}
        </div>
      </div>
      <div
        v-for="(item, index) in rankList"
        :key="index"
        class="row"
      >
        <div class="row-item rank-index">
          <div class="index">
            {{ index + 1 }}
          </div>
        </div>
        <div class="row-item rank-content">
          {{ item.userName || "--" }}
        </div>
        <div class="row-item randk-num">
          <div class="randk-num-wrap">
            <span>{{ $t('marketing.commons.g_e98a1d', { data: { option0: item.leadCount || item.fansCount } }) }}</span>
          </div>
        </div>
      </div>
    </div>
    <div
      v-else
      class="empty-wrapper"
    >
      <Empty :title="$t('marketing.commons.zwsj_21efd8')" />
    </div>
  </div>
</template>
<script>
import http from '@/services/http/index.js'
import Empty from '@/components/common/empty.vue'

export default {
  components: {
    Empty,
    ElRadioGroup: FxUI.RadioGroup,
    ElRadioButton: FxUI.RadioButton,
  },
  props: {
    marketingEventId: {
      type: String,
      default: '',
    },
    updateTick: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      loading: true,
      rankList: [],
      rankColor: ['#F64141', '#F29949', '#FFC400', '#333333', '#333333'],
      radio: '1',
      rankTypeName: '',
      rankTypeNameLabel: $t('marketing.commons.hqxss_73f905'),
    }
  },
  watch: {
    marketingEventId() {
      this.changeRadio('1')
    },
    updateTick() {
      this.changeRadio('1')
    },
  },
  mounted() {
    this.changeRadio('1')
  },
  methods: {
    queryStaffAddWechatFansStatistics(rankTypeName) {
      http
        .queryStaffAddWechatFansStatistics({
          marketingEventId: this.marketingEventId,
          pageNum: 1,
          pageSize: 10,
          rankTypeName,
        })
        .then(res => {
          this.loading = false
          if (res && res.errCode === 0) {
            this.rankList = res.data.result || []
            console.log(this.rankList)
          }
        })
    },
    queryStaffMarketingClueStatistics() {
      http
        .queryStaffMarketingClueStatistics({
          marketingEventId: this.marketingEventId,
          pageNum: 1,
          pageSize: 10,
        })
        .then(res => {
          this.loading = false
          if (res && res.errCode === 0) {
            if (res.data && res.data.result) {
              this.rankList = res.data.result
            }
          }
        })
    },
    changeRadio(type) {
      this.radio = type
      switch (type) {
        case '1':
          this.loading = true
          this.rankTypeName = ''
          this.rankTypeNameLabel = $t('marketing.commons.hqxss_73f905')
          this.queryStaffMarketingClueStatistics()
          break
        case '2':
          this.loading = true
          this.rankTypeName = 'WechatWorkExternalUserObj'
          this.rankTypeNameLabel = $t('marketing.pages.content_marketing.hqqwkhs_47bdf6')
          this.queryStaffAddWechatFansStatistics(this.rankTypeName)
          break
        case '3':
          this.loading = true
          this.rankTypeName = 'WechatFanObj'
          this.rankTypeNameLabel = $t('marketing.pages.content_marketing.hqfss_31c4e4')
          this.queryStaffAddWechatFansStatistics(this.rankTypeName)
          break
        default:
          break
      }
    },
  },
}
</script>

<style lang="less" scoped>
.content-marketing-employee-rank__wrapper {
  background: #fff;
  padding: 20px 16px;
  box-sizing: border-box;
  min-height: 439px;
  margin-bottom: 12px;
  border-radius: 8px;
  .radioWrap {
    width: 100%;
    margin: 16px auto;
    display: flex;
    justify-content: center;
    gap: 8px;
    .btn-item{
        padding: 2px 8px;
        border-radius: var(--4, 4px);
        background: var(--color-special01);
        color: #545861;
        font-size: 12px;
        cursor: pointer;
        &.active{
          color: var(--color-primary06);
          background: var(--color-primary01);
        }
      }
  }
  .title {
    color: #181c25;
    font-size: 14px;
    font-weight: bold;
    line-height: 20px;
    display: flex;
    justify-content: space-between;
    .more {
      cursor: pointer;
      font-size: 12px;
      color: var(--color-info06,#407FFF);
      margin-left: 19px;
    }
  }
  .ranking-wrapper {
    display: flex;
    flex-direction: column;
    margin-top: 20px;

    .row {
      display: flex;
      align-items: stretch;
      margin-bottom: 15px;

      &:last-of-type {
        margin-bottom: 0;
      }
      &.header {
        background: #F7F8FA;
        font-size: 12px;
        color: #91959E;
        border-bottom: 1px solid #DEE1E8;

        .row-item {
          height: 32px;
        }
      }

      .row-item {
        display: flex;
        justify-content: center;
        align-items: center;

        &.rank-index {
          width: 40px;
          .index {
            min-width: 18px;
            height: 18px;
            background: #FFF7E6;
            border-radius: 9px;
            color: #FF8000;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 3px;
            box-sizing: border-box;
          }
        }

        &.rank-content {
          flex: 1;
          justify-content: flex-start;
        }

        &.randk-num {
          width: 140px;
          justify-content: flex-end;
          padding-right: 8px;

          .randk-num-wrap {
            display: flex;
            align-items: flex-end;
            font-size: 14px;
            color: #91959E;

            span {
              color: #181C25;
              font-size: 16px;
              font-weight: bold;
              margin-right: 8px;
            }
          }
        }
      }
    }
  }
}
</style>
