<template>
  <Dialog
    class="material-preview"
    :title="dialogTitle"
    :visible="visible"
    :show-confirm="false"
    :cancel-text="$t('marketing.commons.gb_b15d91')"
    width="900px"
    @onClose="handleClosePreview"
  >
    <div class="preview-body">
      <div class="preview-menu">
        <div class="material-preview__title">
          {{ $t('marketing.commons.yl_645dbc') }}
        </div>
        <page-render
          v-if="message.material.objectType === 26 /*微页面*/"
          class="page-render"
          :title="previewTitle"
          :data="previewData"
          :scroll-height="610"
        />
        <form-preview
          v-if="message.material.objectType === 16 /*表单*/"
          :id="message.material.id"
          class="form-preview"
        />
        <product-preview
          v-if="message.material.objectType === 4 /*产品*/"
          :product-data="productData"
        />
        <coupons-preview
          v-if="message.material.objectType === 35 /*优惠券*/"
          :preview-info="couponData"
        />
        <preview-article
          v-if="message.material.objectType === 6 /*文章*/"
          class="preview-article"
          :article="articleDatas.article"
          :is-public-article="articleDatas.isPublicArticle"
          :form-style-type="articleDatas.formStyleType"
          :form-data="articleDatas.formData"
          :form-button-name="articleDatas.formButtonName"
        />
        <!-- 外部内容 -->
        <ExternalContentPreview
          v-if="message.material.objectType === 9999"
          :id="message.material.id"
        />
      </div>
      <div class="message-menu">
        <div class="body-item">
          <div class="item-title">
            <span>{{ $t('marketing.components.site_sideslip_preview.ylewm_02ee1b') }}</span>
          </div>
          <div
            v-loading="!h5Code && !wxCode && !baiduCode"
            class="tabs-block"
          >
            <div class="tabs-head">
              <div
                :class="{ tabs: true, active: activeTabIndex === 0 }"
                @mouseover="tabsChange(0)"
              >
                {{ $t('marketing.commons.ym_0cb4d6') }}
              </div>
              <!-- 优惠券小程序码暂时屏蔽 -->
              <div
                v-if="message.material.objectType !== 35 && message.material.objectType !== 9999"
                :class="{ tabs: true, active: activeTabIndex === 1 }"
                @mouseover="tabsChange(1)"
              >
                {{ $t('marketing.commons.wxxcx_439845') }}
              </div>
              <!-- <div
                :class="{ 'tabs': true, 'active': activeTabIndex === 2 }"
                @mouseover="tabsChange(2)"
                v-if="message.material.objectType === 26"
              >
                百度小程序
              </div> -->
            </div>
            <div
              v-show="activeTabIndex === 0"
              v-loading="false"
              class="tabs-content"
            >
              <img
                class="tabs-code"
                :src="h5Code"
              >
              <div class="tabs-text">
                {{ $t('marketing.components.site_sideslip_preview.syfxhwxsys_c89775') }}
              </div>
            </div>
            <div
              v-show="activeTabIndex === 1"
              v-loading="false"
              class="tabs-content"
            >
              <img
                class="tabs-code"
                :src="wxCode"
              >
              <div class="tabs-text">
                {{ $t('marketing.components.site_sideslip_preview.sywxsysyl_5ecab7') }}
              </div>
            </div>
            <div
              v-show="
                activeTabIndex === 2 && message.material.objectType === 26
              "
              v-loading="false"
              class="tabs-content"
            >
              <img
                class="tabs-code"
                :src="baiduCode"
              >
              <div class="tabs-text">
                <a
                  :href="baiduCode"
                  :download="materialTitle(message.material)"
                >{{ $t('marketing.commons.xzewm_feea92') }}</a>
              </div>
            </div>
          </div>
        </div>
        <div class="body-item">
          <div class="item-title">
            <span>{{ $t('marketing.components.site_sideslip_preview.yllj_c621be') }}</span>
          </div>
          <div class="link-block">
            <div class="link-text">
              {{ message._shortUrl_3 || getUrl(message) }}
            </div>
            <div
              class="link-button"
              @click="copyLink(message._shortUrl_3 || getUrl(message))"
            >
              {{ $t('marketing.commons.fz_79d3ab') }}
            </div>
          </div>
          <div class="tips">
            {{ $t('marketing.commons.tsylljbkyy_b0e2d5') }}
          </div>
        </div>
      </div>
    </div>
  </Dialog>
</template>

<script>
import PageRender from '@/components/Hexagon/PageRender'
import Dialog from '@/components/dialog/index.vue'
import ProductPreview from '@/components/product-preview/preview.vue'
import FormPreview from '@/components/form-preview/index.vue'
import ExternalContentPreview from '@/components/ExternalContentPreview/index.vue'
import CouponsPreview from '@/pages/Coupons/components/coupons-preview.vue'
import PreviewArticle from '@/pages/article/edit/preview/article.vue'
import {
  getUrl,
  aShortUrl,
  materialTitle,
  FS_util_getFscFileUrl,
} from '../../func.js'
import http from '@/services/http/index.js'
import { generateFeedKey } from '@/utils/feedKey.js'
import phoneHeader from '@/assets/images/preview-phone-header.jpg'

const defaultMaterial = {
  leadNum: 0,
  posterNum: 0,
  pv: 0,
  uv: 0,
  qrPostIdList: [],
  material: {
    createTime: '',
    defaultMarketingActivityId: '', // 营销活动id
    id: '',
    objectType: 0,
    updateTime: '',
    title: '',
  },
  _shortUrl_3: '',
}

export default {
  components: {
    Dialog,
    PageRender,
    ProductPreview,
    PreviewArticle,
    FormPreview,
    CouponsPreview,
    ExternalContentPreview,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    material: {
      type: Object,
      default: () => ({}),
    },
    marketingEventId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      activeTabIndex: 0,
      baiduCode: '',
      wxCode: '',
      h5Code: '',
      previewTitle: '',
      previewData: {},
      productData: {},
      couponData: {},
      message: { ...defaultMaterial, ...this.material },
      articleDatas: {
        isPublicArticle: false,
        formStyleType: 1,
        formData: {},
        formButtonName: '',
        article: {
          title: '',
          author: '',
          content: '',
          cover: '',
        },
      },
      phoneHeader: `url(${phoneHeader})`,
    }
  },
  computed: {
    dialogTitle() {
      const { objectType } = this.message.material || {}
      let title
      switch (objectType) {
        case 26:
          title = $t('marketing.commons.wymyl_bc9678')
          break
        case 16:
          title = $t('marketing.commons.bdyl_54ddb1')
          break
        case 4:
          title = $t('marketing.commons.cpyl_0df096')
          break
        case 6:
          title = $t('marketing.pages.content_marketing.wzyl_15f7a9')
          break
        case 35:
          title = $t('marketing.pages.content_marketing.yhqtg_10478f')
          break
        case 9999:
          title = $t('marketing.pages.content_marketing.wbnryl_df1f77')
          break
        default:
          title = $t('marketing.pages.content_marketing.nryl_d93365')
          break
      }
      return title
    },
  },
  watch: {
    material(val) {
      this.message = { ...defaultMaterial, ...val }
      this.getShortUrl()
      this.getCode(this.message.material)
    },
  },
  created() {
    this.getShortUrl()
    this.getCode(this.message.material)
    if (this.message.material.objectType === 26) {
      // 微页面
      const params1 = {}
      params1.siteId = this.message.material.id
      http.getPagesBySiteId(params1).then(result1 => {
        const errCode1 = result1.errCode
        const data1 = result1.data
        if (errCode1 === 0) {
          const homePage = data1.filter(item => item.isHomepage === 1)[0]
            || data1[0] || { content: '{name:"",components:[]}' }
          this.previewTitle = JSON.parse(homePage.content).name
          this.previewData = JSON.parse(homePage.content) || {}
        }
      })
    }
    if (this.message.material.objectType === 4) {
      // 产品
      http
        .queryProductDetail({
          id: this.message.material.id,
          type: 2,
        })
        .then(res => {
          if (!res || res.errCode !== 0) {
            FxUI.Message.error($t('marketing.commons.hqcpxxsb_b4aa99'))
          } else {
            this.productData = res.data
          }
        })
    }
    if (this.message.material.objectType === 6) {
      // 文章
      http
        .queryArticleDetail({
          articleId: this.message.material.id,
        })
        .then(res => {
          console.log(res)
          if (!res || res.errCode !== 0) {
            FxUI.Message.error($t('marketing.commons.hqcpxxsb_b4aa99'))
          } else {
            const { data } = res
            const articleDatas = {}
            const article = {}
            // articleDatas.article = article
            articleDatas.formStyleType = data.formData && data.formData.formStyleType
            articleDatas.formButtonName = data.formData && data.formData.formButtonName
            article.title = data.title
            article.author = data.creator
            article.content = data.content || data.webPage
            article.cover = data.photoUrl
            if (data.formData) {
              http.getFormDataById({ id: data.formData.formId }).then(res => {
                if (res && res.errCode === 0) {
                  articleDatas.formData = res.data
                  this.articleDatas = articleDatas
                }
              })
            } else {
              this.articleDatas = articleDatas
            }
            this.$set(this.articleDatas, 'article', article)
          }
        })
    }
    if (this.message.material.objectType === 35) {
      http
        .queryCouponDetail({ objectId: this.message.material.objectId })
        .then(res => {
          console.log(res)
          if (!res || res.errCode !== 0) {
            FxUI.Message.error($t('marketing.commons.hqyhqxxsb_ab3913'))
          } else {
            this.couponData = res.data
          }
        })
    }
  },
  mounted() {
    console.log('mounted message', this.message)
  },
  methods: {
    FS_util_getFscFileUrl,
    getUrl(item) {
      return getUrl(item, { type: '3' })
      // 同 h5QrCodeValue.type
    },
    materialTitle,
    handleClosePreview() {
      this.$emit('close')
    },
    tabsChange(index) {
      this.activeTabIndex = index
    },
    copyLink(data) {
      try {
        FS.util.inputCopy(data)
        FxUI.Message({
          message: $t('marketing.commons.fzcg_20a495'),
          type: 'success',
        })
      } catch (err) {
        FxUI.Message.error($t('marketing.commons.fzsb_5154ae'))
      }
    },
    getCode(material) {
      // 获取二维码
      this.baiduCode = ''
      this.h5Code = ''
      this.wxCode = ''
      let _h5QrCodeValue = {
        byshare: 1,
        id: material.id,
        type: material.objectType === 26 ? 3 : undefined,
        // 只针对微页面，1 普通物料，2 微页面模板，3 用于预览（已停用的微页面）
        marketingActivityId: material.defaultMarketingActivityId || '',
        marketingEventId: this.message.marketingEventId || this.marketingEventId || '',
        // spreadChannel: null,
      }
      if (material.objectType === 35) {
        _h5QrCodeValue = {
          wxAppId: material.wxAppId,
          objectId: material.objectId,
        }
      }
      http
        .getQrCodeByMarketingEventIdAndObjectInfo({
          feedKey: generateFeedKey(material.id),
          objectId: material.id,
          objectType: material.objectType,
          marketingEventId: this.message.marketingEventId || this.marketingEventId,
          marketingActivityId: material.defaultMarketingActivityId,
          // miniappQrCodeValue: material.defaultMarketingActivityId,
          h5QrCodeType: {
            4: 10006,
            6: 10005,
            16: 10001,
            26: 10010,
            35: 10011,
            9999: 19999,
          }[material.objectType],
          h5QrCodeValue: JSON.stringify(_h5QrCodeValue),
          miniappQrCodeValue: JSON.stringify({
            // byshare: 1,
            // id: material.id,
            // 只针对微页面，1 普通物料，2 微页面模板，3 用于预览（已停用的微页面）
            marketingActivityId: material.defaultMarketingActivityId || '',
            marketingEventId: this.message.marketingEventId || this.marketingEventId || '',
            // spreadChannel: null,
          }),
        })
        .then(({ errCode, data }) => {
          if (errCode === 0) {
            this.h5Code = this.FS_util_getFscFileUrl(data.h5QrCodeAPath)
            this.wxCode = this.FS_util_getFscFileUrl(data.miniappQrCodeAPath)
            this.baiduCode = this.FS_util_getFscFileUrl(
              data.baiduQrCodeAPath,
            )
          }
        })
    },
    getShortUrl() {
      this.message.marketingEventId = this.marketingEventId
      if(this.message?.material && this.message?.material?.siteType === 'EVEN_HOMEPAGE') {
        this.message.targetObjectId = this.message.material.targetObjectId
        this.message.targetObjectType = 13
      }
      aShortUrl('_shortUrl_3', this.message)
    }
  },
}
</script>

<style lang="less">
.material-preview {
  display: flex;
  flex-flow: column nowrap;
  .el-dialog__body {
    padding: 0;
  }
  .page-render {
    transform: scale(0.64);
    transform-origin: 0 0;
    box-shadow: 0 0 10px #b7b7b7;
    margin-top: 10px;
    * {
      box-sizing: border-box;
    }
  }
  .form-preview-title {
    background-size: cover;
    text-align: center;
    width: 243px;
    margin-left: 28px;
    border: 1px solid #ddd;
    border-bottom: none;
    .title {
      font-size: 12px;
      font-weight: 600;
      margin-top: 14px;
      display: inline-block;
      transform: scale(0.83);
    }
  }
  .preview-article {
    transform: scale(0.6);
    transform-origin: 0 0;
    margin-top: 10px;
  }
  .product-preview {
    transform: scale(0.93);
    transform-origin: 0 0;
    margin-top: 10px;
  }
  .form-preview .page-render {
    margin-left: 0;
  }
  .preview-header {
    padding-left: 15px;
    height: 48px;
    line-height: 48px;
    background-color: #f6f9fc;
    color: #181c25;
    font-size: 16px;
    flex-shrink: 0;
  }
  .preview-body {
    display: flex;
    flex-flow: row nowrap;
  }
  .preview-menu {
    flex-shrink: 0;
    width: 280px;
    height: 547px;
    position: relative;
    background-color: #fafafa;
    padding-left: 20px;
    padding-top: 19px;
    box-sizing: border-box;
    overflow: hidden;
    .material-preview__title {
      font-size: 13px;
      color: #545861;
    }
  }
  .message-menu {
    padding-right: 25px;
    width: 100%;
    min-width: 0;
    display: flex;
    flex-flow: column nowrap;
  }
  .body-item {
    margin-top: 20px;
    .item-title {
      margin: 0 20px;
      position: relative;
      font-size: 12px;
      color: #181c25;
      &:before {
        content: "";
        position: absolute;
        left: -20px;
        top: 3px;
        width: 4px;
        height: 12px;
        background: var(--color-primary06,#ff8000);
      }
      .title-end {
        color: var(--color-primary06,#407FFF);
        position: absolute;
        right: 0;
        top: 0;
      }
    }
    .tabs-block {
      margin-top: 16px;
      margin-left: 20px;
      width: 357px;
      height: 279px;
      border: 1px solid #e9edf5;
      border-radius: 3px;
      .tabs-head {
        height: 32px;
        display: flex;
        flex-flow: row nowrap;
        background-color: #f2f2f2;
        font-size: 12px;
        .tabs {
          border-bottom: 1px solid #e9edf5;
          color: #91959e;
          width: calc(100% / 2);
          text-align: center;
          cursor: pointer;
          height: 32px;
          line-height: 32px;
          &:last-of-type {
            border-right: none;
          }
        }
        .active {
          color: #181c25;
          background-color: #ffffff;
          border-bottom: 1px solid #ffffff;
        }
      }
      .tabs-content {
        text-align: center;
        margin-top: 46px;
      }
      .tabs-code {
        width: 150px;
        height: 150px;
      }
      .tabs-text {
        font-size: 12px;
        text-align: center;
        margin-top: 14px;
        color: #91959e;
      }
    }
    .link-block {
      min-width: 0;
      margin-left: 20px;
      margin-top: 19px;
      display: flex;
      flex-flow: row nowrap;
      min-width: 0;
      font-size: 12px;
    }
    .link-text {
      height: 34px;
      line-height: 34px;
      padding-left: 15px;
      width: 100%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      color: #c1c5ce;
      border: 1px solid #e9edf5;
    }
    .link-button {
      width: 72px;
      height: 36px;
      line-height: 36px;
      text-align: center;
      flex-shrink: 0;
      color: var(--color-primary06,#407FFF);
      cursor: pointer;
      box-sizing: border-box;
      border: 1px solid var(--color-primary06,#407FFF);
      font-size: 12px;
    }
    .tips {
      margin: 10px 0 26px 20px;
      color: @color-subtitle;
      font-size: 12px;
    }
  }
}
</style>
