<template>
  <div
    v-loading="isLoading"
    :class="$style.container"
  >
    <div :class="$style.title">
      <div :class="$style['title-content']">
        <span>{{ $t('marketing.pages.content_marketing.yxdttj_55722d') }}</span>
        <QuestionTooltip
          :class="$style.question"
          :offset="135"
        >
          <div
            slot="question-content"
            style="max-width: 280px; color: #545861"
          >
            <div class="question-tips">
              {{ $t('marketing.pages.content_marketing.wmjjlbtqdc_d586a0') }}
            </div>
          </div>
        </QuestionTooltip>
      </div>
      <div :class="$style.update">
        <span
          v-if="dataPreparing"
          :class="$style.loading"
        >
          <loading :class-name="$style.icon" />
          {{ $t('marketing.commons.sjjsz_f32ed8') }}
        </span>
      </div>
    </div>
    <div :class="$style.content">
      <div :class="$style['graph-title']">
        {{ $t('marketing.pages.content_marketing.yxdts_e0190c') }}
        <span>{{ userMarketingActionCount }}</span>
        {{ $t('marketing.commons.t_cc1bac') }}
      </div>
      <div
        ref="graph"
        :class="$style.graph"
      />
      <div
        v-if="spreadChannelStatisticItemList.length === 0"
        :class="$style.empty"
      >
        <Empty
          :class="$style['empty-wrapper']"
          :title="$t('marketing.commons.zwsj_21efd8')"
        />
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import http from '@/services/http/index.js'
import QuestionTooltip from '@/components/questionTooltip/index.vue'
import Empty from '@/components/common/empty.vue'
import loading from './loading.vue'

export default {
  components: {
    QuestionTooltip,
    loading,
    Empty,
  },
  props: {
    marketingEventId: {
      type: String,
      default: '',
    },
    updateTick: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      isLoading: true,
      userMarketingActionCount: 0,
      spreadChannelStatisticItemList: [],
      updateTime: 0,
      dataPreparing: false,
    }
  },
  watch: {
    marketingEventId() {
      this.init()
    },
    updateTick() {
      this.handleUpdateData()
    },
  },
  mounted() {
    this.init()
    window.addEventListener('resize', this.graphResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.graphResize)
    if (this.graph) {
      this.graph.dispose()
    }
  },
  methods: {
    init() {
      if (!this.marketingEventId) {
        return
      }

      http.spreadChannelStatistic({ marketingEventId: this.marketingEventId })
        .then(res => {
          this.isLoading = false
          const { errCode, data } = res
          const {
            userMarketingActionCount = 0,
            spreadChannelStatisticItemList = [],
            updateTime = 0,
          } = data || {}

          this.userMarketingActionCount = userMarketingActionCount
          this.spreadChannelStatisticItemList = spreadChannelStatisticItemList
          this.updateTime = updateTime
          this.dataPreparing = false

          if (errCode === 930001) {
            this.dataPreparing = true
          }

          if (updateTime) {
            this.$emit('onUpdate', { updateTime })
          }

          this.render()
        })
        .catch(() => {
          this.isLoading = false
        })
    },
    render() {
      const xAxisData = this.spreadChannelStatisticItemList.length === 0
        ? [
          $t('marketing.commons.gg_bacd6f'),
          $t('marketing.commons.gw_847652'),
          $t('marketing.commons.wx_cfbf6f'),
          $t('marketing.commons.dx_485c3a'),
          $t('marketing.commons.yj_e9e805'),
          $t('marketing.commons.gzh_215fee'),
        ]
        : this.spreadChannelStatisticItemList.map(item => item.spreadChannel)
      const yAxisData = this.spreadChannelStatisticItemList.length === 0
        ? [0, 0, 0, 0, 0, 0]
        : this.spreadChannelStatisticItemList.map(item => item.count)

      const defaultColor = ['#50AEF0', '#3ED0BE', '#88E679', '#FFCF42', '#FB8739', '#FF6678', '#D174FF']
      const options = {
        color: defaultColor,
        title: {
          show: this.spreadChannelStatisticItemList.length === 0,
          text: $t('marketing.commons.zwsj_21efd8'),
          left: 'center',
          top: 'middle',
          textStyle: {
            color: '#91959e',
            fontWeight: 'normal',
            fontSize: 14,
          },
        },
        grid: {
          top: 20,
          left: 50,
          right: 20,
          bottom: 40,
        },
        legend: {
          show: false,
        },
        tooltip: {
          trigger: 'axis',
          order: 'seriesDesc',
        },
        xAxis: [
          {
            type: 'category',
            data: xAxisData,
            axisLabel: {
              interval: 0, // 代表显示所有x轴标签显示
              rotate: -15,
              formatter(value, index) {
                const v = `${value.substring(0, 8)}...`
                return value.length > 8 ? v : value
              },
            },
          },
        ],
        yAxis: [
          {
            type: 'value',
            splitLine: {
              show: this.spreadChannelStatisticItemList.length > 0,
              lineStyle: {
                color: '#DEE1E8',
                type: 'dashed',
              },
            },
          },
        ],
        series: [
          {
            data: yAxisData,
            type: 'bar',
            barMaxWidth: 60,
            colorBy: 'data',
            label: {
              show: this.spreadChannelStatisticItemList.length > 0,
              position: 'top',
              color: '#545861',
              fontSize: 10,
            },
            itemStyle: {
              color: ({ dataIndex }) => {
                const idx = (dataIndex + 1) % defaultColor.length
                return defaultColor[idx]
              },
            },
          },
        ],
      }
      this.graph = echarts.init(this.$refs.graph)
      this.graph.setOption(options)
    },
    graphResize() {
      if (this.graph) {
        this.graph.resize()
      }
    },
    handleUpdateData() {
      this.isLoading = true
      http.calculateLeadData({ marketingEventId: this.marketingEventId })
        .then(res => {
          const { errCode } = res
          if (errCode === 0) {
            this.init()
          }
        })
    },
  },
}
</script>

<style lang="less" module>
.container {
  background-color: #ffffff;
  width: 100%;
  box-sizing: border-box;
  margin-bottom: 12px;
  padding: 20px 16px;
  border-radius: 8px;
  position: relative;
  .title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #181c25;
    font-size: 14px;
    line-height: 20px;
    font-weight: bold;

    .title-content {
      display: flex;
      align-items: center;
    }

    .question {
      margin-left: 6px;
    }

    .update {
      display: flex;
      align-items: center;
      color: #91959E;
      font-size: 12px;

      .loading {
        display: flex;
        align-items: center;
        margin-right: 10px;

        .icon {
          margin-right: 10px;
        }
      }
    }
  }
  .graph-title {
    padding: 10px 0;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #545861;
    font-size: 12px;

    span {
      color: #181C25;
      font-size: 24px;
      font-weight: bold;
      padding: 0 8px;
    }
  }
  .graph {
    height: 300px;
  }

  .content {
    position: relative;
  }
  .empty {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #FFF;

    .empty-wrapper {
      padding: 0;
    }
  }
}
</style>
