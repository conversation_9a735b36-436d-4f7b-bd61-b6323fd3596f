<template>
  <div class="clues-detail">
    <div class="km-g-loading-mask" v-if="loading">
      <span class="loading"></span>
    </div>
    <div class="header">
      <div class="back" @click="closeCluesDetail">< {{ $t('marketing.commons.fh_5f4112') }}</div>
      <div class="title">{{ materialTitle(message.material) }}</div>
    </div>
    <div class="body-item">
      <div class="item-title">
        <span>{{  showType === "order" ? $t('marketing.commons.ddlb_07166e') : $t('marketing.commons.hqxsmx_9718c8')}}</span>
        <div class="title-end">
          <span class="output-table" @click="exportClues">{{ $t('marketing.commons.dc_55405e') }}</span>
        </div>
      </div>
      <div class="btn-wrapper" v-if="message.material.objectType === 26">
            <!-- <fx-button
            type="medium"
            size="mini"
            style="margin-top:20px;margin-left:20px"
            :disabled="!selectedLeadNumber > 0"
            @click="saveToClue"
            >重新存入线索</fx-button
          > -->
        <fx-button
          type="medium"
          size="mini"
          :disabled="!(selectedLeadNumber > 0)"
          :loading="deleteLoading"
          @click="handleDeleteClues"
          >
          {{ $t('marketing.commons.sc_2f4aad') }}
        </fx-button>
      </div>
      <div class="clues-list">
        <v-table
          class="tablewbg"
          tid="clues-detail-table"
          :settable="true"
          :filter-option="false"
          :data="clueData"
          :columns="clueColumns"
          ref="clueTable"
          :row-style="{ cursor: 'pointer' }"
          @custom:cule-action="handleClueDetail"
          @click:row="handleClueRow"
          @selection-change="selectionChange"
        ></v-table>
      </div>
      <div class="footer">
        <v-pagen @change="handlePageChange" :pagedata.sync="pageData"></v-pagen>
      </div>
    </div>
  </div>
</template>

<script>
import VTable from "@/components/table-ex/index";
import VPagen from "@/components/kitty/pagen";
import http from "@/services/http/index";
import util from "@/services/util/index";
import { materialTitle } from "../../../func.js";
import formDataDetail from "@/store/modules/promotion-activity/detail";
import ORDER_TABLE from "@/components/materiel-sideslip-detail/components/order-table/table";
import { confirm } from "@/utils/globals";

const defaultMaterial = {
  leadNum: 0,
  posterNum: 0,
  pv: 0,
  uv: 0,
  qrPostIdList: [],
  material: {
    createTime: "",
    defaultMarketingActivityId: "", // 营销活动id
    id: "",
    objectType: 0,
    updateTime: "",
    title: ""
  }
};

export default {
  components: {
VTable,
VPagen
},
  props: {
    marketingEventId: {
      type: String,
      default: ""
    },
    showType: {
      type: String,
      default: ""
    },
    showCluesDetail: {
      type: Boolean,
      default: false
    },
    material: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    message() {
      console.log("侧滑页", this.material);
      return Object.assign({}, defaultMaterial, this.material);
    }
  },
  data() {
    return {
      selectedLeads: [],
      selectedLeadNumber: 0,
      loading: false,
      pageData: {
        layout: "prev, pager, next, total, sizes, jumper",
        pageNum: 1,
        totalCount: 0,
        pageSize: 10,
        pageSizes: [10, 20, 30, 50]
      },
      clueColumns: [],
      clueData: [],
      deleteLoading: false
    };
  },
  methods: {
    materialTitle,
    handlePageChange(data) {
      this.pageData.pageNum = data.pageNum;
      this.pageData.pageSize = data.pageSize;
      this.getCluesList();
    },
    selectionChange(val) {
      this.selectedLeadNumber = (val && val.length) || 0;
      this.selectedLeads = val;
    },
    clearTableSelection(){
      this.selectedLeadNumber = 0
      this.selectedLeads = []
      this.$refs.clueTable.clearSelection()
    },
    saveToClue() {
      confirm(
        $t('marketing.commons.jhdsxcrsbd_fedc65'),
        $t('marketing.commons.zxcrxs_f70585'),
        {}
      ).then(
        () => {
          let ids = this.getIDs();
          http.reImportDataToCrm({ ids: ids }).then(results => {
            if (results && results.errCode == 0) {
              FxUI.MessageBox.alert($t('marketing.commons.zxcrknxyjz_9b0350'), $t('marketing.commons.ts_02d981'), {
                confirmButtonText: $t('marketing.commons.zdl_ce2695'),
              })
            }
          });
        },
        () => {}
      );
    },
    getIDs() {
      let selectedLeads = this.selectedLeads;
      let ids = [];
      selectedLeads.forEach(item => {
        ids.push(item.id);
      });
      return ids;
    },
    closeCluesDetail() {
      this.$emit("close");
    },
    getCluesList() {
      this.loading = true;
      const params = {
        pageNum: this.pageData.pageNum,
        pageSize: this.pageData.pageSize,
        marketingEventId: this.marketingEventId,
        marketingActivityId: this.message.material.defaultMarketingActivityId,
        objectId:
          this.message.material.objectType === 26
            ? this.message.material.formId
            : this.message.material.id,
        objectType:
          this.message.material.objectType === 26
            ? 16
            : this.message.material.objectType,
        type: 4 // 市场活动
      };
      if (this.showType === "order") {
        params.formUsage = 2;
      }
      http.getCluesList(params).then(res => {
        if (res && res.errCode === 0) {
          let clueColumns, dataResult;
          //订单列表
          if (this.showType === "order") {
            clueColumns = ORDER_TABLE.getColumns(res.data);
            dataResult = ORDER_TABLE.getDatas(res.data);
          } else {
            clueColumns = formDataDetail.actions.getColumns(res.data);
            clueColumns.push({
              prop: "tags",
              label: $t('marketing.commons.bq_14d342'),
              minWidth: "350px"
            });
            // clueColumns.push({
            //   prop: "sourceText",
            //   label: "推广渠道",
            //   minWidth: "100px"
            // });
            dataResult = formDataDetail.actions.getDatas(res.data);
          }
          this.clueColumns = clueColumns;

          this.clueData = dataResult.map(data => {
            var newData = { ...data };
            newData.wxUser = {
              text: data.enrollUserName,
              avatar: data.enrollUserAvatar
            };
            newData.sourceText = this.getSourceType(data.sourceType);
            if (data.tagNameList) {
              newData = {
                ...newData,
                tags:
                  data.tagNameList.reduce((total, next) => {
                    const tagName = next.secondTagName || next.firstTagName;
                    return total === "" ? tagName : `${total}, ${tagName}`;
                  }, "") || "--"
              };
            } else {
              newData = {
                ...newData,
                tags: "--"
              };
            }
            return newData;
          });
          this.pageData.totalCount = res.data.totalCount;
          this.loading = false;
        }
      });
    },
    getSourceType(type) {
      switch (type) {
        case 0:
          return $t('marketing.commons.xcx_0ed510');
        case 1:
          return $t('marketing.pages.setting.ygyx_f7ac22');
        case 2:
          return $t('marketing.commons.gzhyx_8bd4fe');
        case 3:
          return $t('marketing.commons.dxyx_de4a39');
        case 4:
          return $t('marketing.commons.wsfbm_31fde1');
        case 5:
          return $t('marketing.commons.drsj_8ef83f');
        default:
          return "--";
      }
    },
    handleClueDetail(row, column, event) {
      if (row.saveCrmStatus === 0 || row.saveCrmStatus === 3) {
        if(!row.otherCrmObjectBind) return;
        CRM.api.show_detail({
          apiname: row.otherCrmObjectBind.apiName,
          id: row.otherCrmObjectBind.objectId
        });
      }
    },
    handleClueRow(row, column, event) {
      console.log("click clue row", ...arguments);
      this.handleClueDetail(row);
    },
    exportClues() {
      // 导出线索
      const params = {
        marketingEventId: this.marketingEventId,
        objectId:
          this.message.material.objectType === 26
            ? this.message.material.formId
            : this.message.material.id,
        objectType:
          this.message.material.objectType === 26
            ? 16
            : this.message.material.objectType,
        type: 4 // 市场活动
      };
      if (this.showType === "order") {
        params.formUsage = 2;
      }
      util.exportoFile(
        {
          action: "exportClues",
          params
        },
        () => {}
      );
    },
    handleDeleteClues(){
      const ids = this.getIDs()
      FxUI.MessageBox.confirm($t('marketing.commons.qrscdqxztj_5bc3c3'), $t('marketing.commons.ts_02d981'), {
          confirmButtonText: $t('marketing.commons.qd_38cf16'),
          cancelButtonText: $t('marketing.commons.qx_625fb2'),
          type: 'warning',
        }).then(async () => {
          this.deleteLoading = true
          const res = await http.deleteFormEnrollData({enrollUserIds: ids})
          if(res && res.errCode === 0){
            this.clearTableSelection()
            FxUI.Message.success($t('marketing.commons.sccg_0007d1'))
            this.getCluesList()
          } else {
            FxUI.Message.success($t('marketing.pages.setting.scsbqzs_89976e'))
          }
          this.deleteLoading = false
        }).catch((err)=>{
        })
    },
  },
  mounted() {
    // this.marketingEventId = this.$route.params.id;
    this.getCluesList();
  }
};
</script>

<style lang="less">
.clues-detail {
  display: flex;
  flex-flow: column nowrap;
  .header {
    flex-shrink: 0;
    height: 48px;
    padding: 16px 20px;
    display: flex;
    flex-flow: row nowrap;
    min-width: 0;
    align-items: center;
    background-color: #f6f9fc;
    .back {
      cursor: pointer;
      color: var(--color-primary06,#407FFF);
      font-size: 14px;
      flex-shrink: 0;
      padding-right: 20px;
      border-right: 1px solid #e9edf5;
    }
    .title {
      padding: 0px 21px;
      color: #181c25;
      font-size: 14px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
  .tablewbg {
    width: auto;
    border-top: 1px solid #e9edf5 !important;
    border-left: 1px solid #e9edf5 !important;
    box-sizing: border-box;
    overflow: auto;
    .el-table__header {
      tr {
        th {
          background: #fff;
        }
      }
    }
    .el-table__empty-block {
      min-height: 200px;
    }
    &.bottomborder {
      border-width: 1px;
    }
  }
  .body-item {
    margin-top: 20px;
    .item-title {
      margin: 0 20px;
      position: relative;
      height: 18px;
      line-height: 18px;
      font-size: 12px;
      color: #181c25;
      &:before {
        content: "";
        position: absolute;
        left: -20px;
        top: 3px;
        width: 4px;
        height: 12px;
        background: var(--color-primary06,#ff8000);
      }
      .title-end {
        color: var(--color-primary06,#407FFF);
        position: absolute;
        right: 0;
        top: 0;
      }
      .output-table {
        cursor: pointer;
        position: relative;
        padding-left: 22px;
        &:before {
          position: absolute;
          content: "";
          width: 16px;
          height: 16px;
          left: 0;
          top: -1px;
          background: url("../../../../../assets/images/icon/download.png")
            center / cover no-repeat;
        }
      }
    }

    .btn-wrapper{
      margin: 10px 20px 0 20px; 
    }
    .clues-list {
      padding: 10px 20px;
      overflow-y: auto;
      max-height: 663px;
    }
    .footer {
      display: flex;
      align-items: center;
      height: 46px;
    }
  }
}
</style>
