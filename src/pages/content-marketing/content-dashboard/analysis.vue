<!-- 组件说明 -->
<template>
  <div
    v-if="marketingEventId"
    :class="$style['content-marketing-analysis__wrapper']"
  >
    <div :class="$style.update">
      <span
        v-if="updateTime"
        :class="$style.loading"
      >
        {{ updateTimeStr }}
      </span>

      <fx-link
        :underline="false"
        type="standard"
        @click="handleUpdateData"
      >
        {{ $t('marketing.commons.gx_32ac15') }}
      </fx-link>
    </div>
    <div :class="$style.wrap">
      <div :class="$style.left">
        <AttendingSituation
          style="height: 140px;box-sizing: border-box; margin-bottom: 12px;"
          v-if="sceneType === 'conference'"
          showTitle
        />
        <fx-row>
          <!-- 渠道获取线索排行 -->
            <channelRank
              :marketing-event-id="marketingEventId"
              :update-tick="updateTick"
            />
        </fx-row>
        <!--  营销动态统计 -->
        <SpreadChannelStatistic
          :marketing-event-id="marketingEventId"
          :update-tick="updateTick"
          @onUpdate="handleUpdateTime"
        />
        <fx-row>
          <fx-col :span="12">
            <!-- 线索状态 -->
            <LeadStatusStatistic
              :marketing-event-id="marketingEventId"
              :update-tick="updateTick"
              @onUpdate="handleUpdateTime"
            />
          </fx-col>
          <fx-col :span="12">
            <!-- 获客转化 -->
            <LeadTransferStatistic
              :marketing-event-id="marketingEventId"
              :update-tick="updateTick"
              @onUpdate="handleUpdateTime"
            />
          </fx-col>
        </fx-row>
        <!-- 影响商机订单 -->
        <MarketingAttributeData
          :marketing-event-id="marketingEventId"
          :update-tick="updateTick"
          @onUpdate="handleUpdateTime"
        />
      </div>
      <div :class="$style.right">
        <!-- 微信数据 -->
        <wechartAnalysis
          :marketing-event-id="marketingEventId"
          :update-tick="updateTick"
        />
        <!-- 新老客户分布 -->
        <customerDistribute
          :marketing-event-id="marketingEventId"
          :update-tick="updateTick"
        />
        <!-- 员工推广排名 -->
        <employeeRank
          :marketing-event-id="marketingEventId"
          :update-tick="updateTick"
        />
        <!-- 内容效果排名 -->
        <contentRank
          :scene-type="sceneType"
          :marketing-event-id="marketingEventId"
          :update-tick="updateTick"
        />
      </div>
    </div>
  </div>
</template>

<script>
import moment from 'base-moment'
import employeeRank from './module/employee-rank.vue'
import contentRank from './module/content-rank.vue'
import channelRank from './module/channel-rank.vue'
import wechartAnalysis from './module/wechart-analysis.vue'
import customerDistribute from './module/customer-distribute.vue'
import SpreadChannelStatistic from './module/spread-channel-statistic.vue'
import LeadStatusStatistic from './module/lead-status-statistic.vue'
import LeadTransferStatistic from './module/lead-transfer-statistic.vue'
import MarketingAttributeData from './module/marketing-attribute-data.vue'
import AttendingSituation from '@/pages/meeting-marketing/MeetingDetail/components/AttendingSituation.vue'

export default {
  components: {
    employeeRank,
    channelRank,
    contentRank,
    wechartAnalysis,
    customerDistribute,
    SpreadChannelStatistic,
    LeadStatusStatistic,
    LeadTransferStatistic,
    MarketingAttributeData,
    AttendingSituation
  },
  props: {
    marketingEventId: {
      type: String,
      default: '',
    },
    sceneType: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      updateTime: 0,
      updateTick: 0,
    }
  },
  computed: {
    isWechatOpen() {
      const {
        isWechatOpen,
      } = this.MARKETING_GLOBAL.OPENINFO
      return isWechatOpen
    },
    updateTimeStr() {
      const dateStr = moment(this.updateTime).format('YYYY-MM-DD HH:mm')
      return `${$t('marketing.pages.content_marketing.zjgxsj_94136f')} ${dateStr}`
    },
  },
  mounted() {
    console.log('analysis mounted')
  },
  methods: {
    handleUpdateData() {
      this.updateTick += 1
    },

    handleUpdateTime(e) {
      if (e.updateTime > this.updateTime) {
        this.updateTime = e.updateTime
      }
    },
  },
}
</script>

<style lang="less" module>
.content-marketing-analysis__wrapper {
  background: #f2f2f5;
  padding-bottom: 10px;
  .wrap {
    display: flex;
  }
  .left {
    margin: 0 10px;
    width: 70%;
  }
  .right {
    width: 30%;
    margin-right: 10px;
  }

  .update {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    color: #91959E;
    font-size: 12px;
    height: 18px;
    padding-bottom: 10px;
    padding-right: 10px;

    .loading {
      display: flex;
      align-items: center;
      margin-right: 10px;
      .icon {
        margin-right: 10px;
      }
    }
  }
}
</style>
