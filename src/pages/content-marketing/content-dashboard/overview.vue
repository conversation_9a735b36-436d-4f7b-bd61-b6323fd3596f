<!-- 组件说明 -->
<template>
  <div :class="$style.activityOverview">
    <div :class="$style.row1">
      <dataInfo
        :class="$style.dataInfo"
        :activity-marketing-detail="activityMarketingDetail"
      />
      <clueRate
        :class="$style.clueRate"
        :activity-marketing-detail="activityMarketingDetail"
      />
    </div>
    <div :class="$style.row2">
      <SpreadContentList
        :id="id"
        :apply-object="{
          enable: false
        }"
        :life-status="lifeStatus"
        :material-label="$t('marketing.commons.hd_36c6f5')"
        :showTemplate="false"
      />
      <MarketingActivityQrposter
        scene="content"
        :life-status="lifeStatus"
        style="margin-left:10px"
        size="small"
        :marketing-event-id="id"
        :reset-page-size="6"
        :title="$t('marketing.commons.qbhb_621d02')"
        :show-mobile-display-tag="true"
        :material-label="$t('marketing.commons.hd_36c6f5')"
      />
      <!-- <MarketingActivityQrposter
        scene="content"
        size="small"
        :class="$style.poster"
        :marketingEventId="activityMarketingDetail.marketingEvent.id"
        :resetPageSize="6"
      ></MarketingActivityQrposter> -->
    </div>
  </div>
</template>

<script>
import MarketingActivityQrposter from '@/components/MarketingActivityQrposter/index.vue'
import dataInfo from './module/data-info.vue'
import clueRate from './module/clue-rate.vue'
import SpreadContentList from './module/spread-content-list.vue'

export default {
  components: {
    dataInfo,
    clueRate,
    MarketingActivityQrposter,
    SpreadContentList,
  },
  data() {
    return {}
  },
  computed: {
    activityMarketingDetail() {
      return this.$store.state.ActivityMarketing.activityMarketingDetail
    },
    lifeStatus() {
      const _detail = this.$store.state.ActivityMarketing.activityMarketingDetail
      if (_detail && _detail.marketingEvent) {
        return _detail.marketingEvent.lifeStatus || ''
      }
      return ''
    },
  },
  mounted() {},
  created() {
    const { params } = this.$route
    this.id = params.id
  },
  destroyed() {},
  methods: {}, // 生命周期 - 销毁完成
}
</script>

<style lang="less" module>
.activityOverview {
  width: 100%;
  background: #f2f2f5;
  .row1 {
    width: 100%;
    height: 100px;
    display: flex;
    .dataInfo {
      flex: 1;
      height: 100%;
      background: #ffffff;
      margin: 0 0 10px 10px;
    }
    .clueRate {
      margin: 0 10px 10px 10px;
      width: 380px;
      height: 100%;
      background: #ffffff;
    }
  }
  .row2 {
    display: flex;
    padding: 10px;
    .spread {
      width: 77%;
      margin: 0 0 10px 10px;
    }
    .poster {
      width: 33%;
      margin: 0 10px 10px 10px;
    }
  }
}
</style>
