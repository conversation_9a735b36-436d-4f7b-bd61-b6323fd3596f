<!-- 组件说明 -->
<template>
  <div :class="$style.activityMarketingHomeWrapper">
    <div
      v-if="loading"
      :class="$style.activityMarktingMsk"
    />
    <div
      v-else
      :class="$style.activityMarketingHome"
    >
      <content-header
        :title="crumbs"
        :border="true"
        :class="$style.header"
      />
      <dashboard-header
        :head-data="headData"
        @refresh="refresh"
      />
      <el-menu
        :default-active="currentTab"
        class="activity-el-menu-wrapper"
        mode="horizontal"
        @select="handleSelect"
      >
      <!-- 线上营销 -->
        <el-menu-item
          v-if="$route.query.formType == 0"
          class="set-menu-item"
          index="overview"
        >
          {{ $t('marketing.commons.gl_863853') }}
        </el-menu-item>
        <!-- 多活动营销-->
        <el-menu-item
          v-if="$route.query.formType == 6"
          class="set-menu-item"
          index="multiEvent"
        >
          {{ $t('marketing.commons.gl_863853') }}
        </el-menu-item>
        <el-menu-item
          v-if="$route.query.formType == 6"
          class="set-menu-item"
          index="pomotionCont"
        >
          {{ $t('marketing.commons.tgnr_a6ec90') }}
        </el-menu-item>

        <el-menu-item
          class="set-menu-item"
          index="participants"
        >
          {{ $t('marketing.commons.cyry_b13613') }}
        </el-menu-item>
        <el-menu-item
          v-if="vDatas.uinfo.wechatVoucher || vDatas.uinfo.sourceCouponEnabled"
          class="set-menu-item"
          index="coupons"
        >
          {{ $t('marketing.commons.yhq_2f3635') }}
        </el-menu-item>
        <el-menu-item
          class="set-menu-item"
          index="radar"
        >
          {{ $t('marketing.commons.tgld_803716') }}
        </el-menu-item>
        <el-menu-item
          class="set-menu-item"
          v-if="vDatas.pluginInfo.marketingPlan"
          index="kanban"
        >
          {{ $t('marketing.commons.yyjh_942f80') }}<span class="kanban-num">{{ kanbanNum }}</span>
        </el-menu-item>
        <el-menu-item
          class="set-menu-item"
          index="trigger"
        >
          SOP
        </el-menu-item>
        <el-menu-item
          class="set-menu-item"
          index="analysis"
        >
          {{ $t('marketing.commons.sjfx_6450d8') }}
        </el-menu-item>
        <el-menu-item
          v-for="menu in mainMenusData"
          :key="menu.id"
          class="set-menu-item"
          :index="`custom-component-${menu.id}`"
        >
          {{ menu.name }}
        </el-menu-item>
        <el-submenu
          v-if="moreMenusData && moreMenusData.length"
          class="set-submenu"
          index="more"
        >
          <template slot="title">
            {{ $t('marketing.commons.gd_0ec9ea') }}
          </template>
          <el-menu-item
            v-for="menu in moreMenusData"
            :key="menu.id"
            :index="`custom-component-${menu.id}`"
          >
            {{ menu.name }}
          </el-menu-item>
        </el-submenu>
      </el-menu>

      <component
        :is="currentTab"
        v-if="readyRenderComponent"
        ref="marketingComp"
        :object-detail-data="objectDetailData"
        :marketing-event-id="marketingEventId"
        scene-type="marketing_event"
        @refreshSop="refreshSop"
      />
    </div>
  </div>
</template>

<script>
import kisvData from '@/modules/kisv-data.js'
import ContentHeader from '@/components/content-header/index.vue'
import DashboardHeader from './module/header.vue'
import overview from './overview.vue'
import multiEvent from './multiEvent.vue'
import pomotionCont from './pomotionCont.vue'
import participants from './participants.vue'
import trigger from './trigger.vue'
import coupons from './coupons.vue'
import analysis from './analysis.vue'
import http from '@/services/http/index.js'
import radar from '../radar.vue'
import kanban from '@/components/sop-kanban/index.vue'
import crmLayoutMenusMixin from '@/mixins/crm-layout-menus.js'

export default {
  components: {
ContentHeader,
DashboardHeader,
ElMenu: FxUI.Menu,
ElMenuItem: FxUI.MenuItem,
ElSubmenu: FxUI.Submenu,
overview,
multiEvent,
pomotionCont,
participants,
trigger,
coupons,
analysis,
radar,
kanban
},
  mixins: [crmLayoutMenusMixin],
  data() {
    return {
      vDatas: kisvData.datas,
      loading: true,
      // currentTab: this.$route.query.type ? this.$route.query.type : "overview",
      crumbs: [
        {
          text: this.$route.query.formType == 0 ? $t('marketing.commons.xsyx_6c9e91') : $t('marketing.pages.setting.dhdzh_0bf57e'),
          to: { name: 'content-marketing',query: { formType: this.$route.query.formType} },
        },
        {
          text: '--',
          to: false,
        },
      ],
      id: '',
      headData: {},
      eventType: '',
      eventTypeList: [],
      kanbanNum: '',
      activityTypeMapping: [],
    }
  },
  computed: {
    activityMarketingDetail() {
      return this.$store.state.ActivityMarketing.activityMarketingDetail
    },
    currentTab() {
      const { type, formType } = this.$route.query
      if (formType == 6) {
        return type || 'multiEvent'
      }
      return type || 'overview'
    },
    marketingEventId() {
      return this.id
    },
    marketingEventType() {
      return (this.headData && this.headData.eventType) || ''
    },
  },
  mounted() {},
  created() {
    const { params } = this.$route
    this.id = params.id
    this.loading = true
    this.$store
      .dispatch('ActivityMarketing/getMarketingEventsDetail', { id: this.id })
      .then(res => {
        this.loading = false
        this.headData = res.marketingEvent
        this.crumbs[1].text = res.marketingEvent.name
        this.eventType = res.marketingEvent.eventType
      })
    if(params.from === 'marketing-calendar') {
      this.crumbs[0].text = $t('marketing.commons.yxrl_09e6dd')
      this.crumbs[0].to = { name: 'marketing-calendar' }
    }
    this.getSOPTabInfo()
  },
  destroyed() {},
  methods: {
    refreshSop() {
      this.getSOPTabInfo()
      this.$refs.marketingComp.getSOPTabInfo()
    },
    getSOPTabInfo() {
      http.getSOPTabInfo({ objectId: this.id }).then(res => {
        if (res && res.errCode === 0) {
          const isExit = res.data && res.data.exist
          if (isExit && res.data.workCount) {
            this.kanbanNum = `${res.data.finishedWorkCount}/${res.data.workCount}`
          } else {
            this.kanbanNum = ''
          }
          this.boardInfo = res.data
        }
      })
    },
    handleSelect(index) {
      const route = {
        name: 'content-dashboard',
        params: { id: this.id },
        query: {
          type: index,
          formType: this.$route.query.formType,
        },
      }
      if (index === 'radar') {
        route.query = { type: index, source: 'contentTab', formType: this.$route.query.formType }
      }
      this.$router.push(route)
      // if (this.currentTab !== index) {
      //   setTimeout(() => {
      //     this.$refs.comp.show && this.$refs.comp.show();
      //   }, 10);
      // }
      // this.currentTab = index;
    },
    refresh() {
      this.$store
        .dispatch('ActivityMarketing/getMarketingEventsDetail', { id: this.id })
        .then(res => {
          console.log('this.headData : ', this.headData)
          this.headData = res.marketingEvent
          this.crumbs[1].text = res.marketingEvent.name
        })
    },
  }, // 生命周期 - 销毁完成
}
</script>
<style lang="less" scoped>
.activity-el-menu-wrapper {
  margin: 10px 10px 0 10px;
  padding-left: 22px;
  border-top: 1px solid @border-color-base;
  margin-bottom: 10px;
  .set-menu-item {
    margin: 0 40px 0 0;
    padding: 0;
    height: 50px;
    line-height: 52px;
    // &.is-active {
    //   border-bottom-color: #ea7504;
    // }
    .kanban-num {
      font-size: 12px;
      color: #c1c1c1;
      margin-left: 3px;
      vertical-align: top;
    }
  }
  /deep/ .el-submenu {
    .el-submenu__title {
      padding: 0;
      height: 50px;
      line-height: 52px;
    }
  }
  // /deep/ .el-submenu {
  //   &.is-active {
  //     .el-submenu__title {
  //       border-bottom-color: #ea7504;
  //     }
  //   }
  // }
}
</style>
<style lang="less" module>
.activityMarketingHomeWrapper {
  height: 100%;
  min-width: 1024px;
  .activityMarketingHome {
    background: #f2f2f5;
    min-height: 100%;
    display: flex;
    flex-direction: column;
  }
}
</style>
