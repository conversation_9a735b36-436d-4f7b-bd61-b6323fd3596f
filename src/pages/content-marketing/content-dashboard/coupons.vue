<!-- 组件说明 -->
<template>
  <div class="cmCoupons">
    <div class="cmCoupons__header">
      <div class="header__title">
        {{ $t('marketing.pages.content_marketing.yhqpclb_5385e7') }}
      </div>
      <div class="header__right">
        <ElButton
          type="primary"
          size="small"
          @click="couponsTypeSelectorVisible = true"
        >
          <i class="yxt-icon16 icon-add" />{{ $t('marketing.pages.content_marketing.xjyhqpc_f13575') }}
        </ElButton>
      </div>
    </div>
    <CouponsTable
      :marketing-event-id="marketingEventId"
      :is-from-content-center="isFromContentCenter"
    />
    <CouponsTypeSelector
      v-if="couponsTypeSelectorVisible && !isFromContentCenter"
      :visible.sync="couponsTypeSelectorVisible"
      @confirm="handleCouponsTypeSelectorConfirm"
    />
    <FxCouponsSelector
      v-if="couponsTypeSelectorVisible && isFromContentCenter"
      :visible.sync="couponsTypeSelectorVisible"
      @confirm="handleCouponsTypeSelectorConfirm"
    />
  </div>
</template>

<script>
import CouponsTable from '@/pages/Coupons/MarketingCoupons/MarketingCouponsTable/index.vue'
import CouponsTypeSelector from '@/pages/Coupons/MarketingCoupons/Create/CouponsTypeSelector.vue'
import FxCouponsSelector from '@/pages/Coupons/MarketingCoupons/Create/FxCouponsSelector.vue'

export default {
  components: {
    ElButton: FxUI.Button,
    CouponsTable,
    CouponsTypeSelector,
    FxCouponsSelector,
  },
  props: {
    marketingEventId: {
      type: String,
      default: '',
    },
    isFromContentCenter: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      couponsTypeSelectorVisible: false,
    }
  },
  methods: {
    handleCouponsTypeSelectorConfirm({ couponType, couponTemplate, marketingEvent }) {
      console.log('handleCouponsTypeSelectorConfirm', couponType, couponTemplate)
      if (couponType === 'wechat') {
        const { type: ctype, id } = couponTemplate
        this.$router.push({
          name: 'coupons-create',
          query: {
            type: ctype,
            tid: id,
            marketingEventId: this.marketingEventId,
          },
        })
      } else if (couponType === 'fx') {
        const { id } = couponTemplate
        const marketingEventId = this.isFromContentCenter ? marketingEvent.id : this.marketingEventId
        this.$router.push({
          name: 'coupons-fxcreate',
          query: {
            pid: id,
            marketingEventId,
          },
        })
      }
    },
  },
}
</script>

<style lang="less" scoped>
.cmCoupons {
  margin: 0 12px 12px 12px;
  height: 100%;
  min-height: 600px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  .cmCoupons__header {
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    .header__title {
      margin-left: 12px;
      color: #181c25;
      font-size: 14px;
    }
    .header__right {
      margin-right: 12px;
      margin-left: auto;
      .icon-add {
        margin-right: 10px;
      }
    }
  }
}
</style>
