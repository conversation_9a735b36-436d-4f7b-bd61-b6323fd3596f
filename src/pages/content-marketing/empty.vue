<template>
    <div class="content-empty">
      <div class="content-summarize">
        <div
          class="content-summarize-photo"
          :style="{
            'background': `url(${emptyConfig.bannerImg}) center / 1170px 260px no-repeat, linear-gradient(to right, #e7f4ff, #d9eeff)`
          }"
        >
          <div class="guide__banner-h">
            {{ emptyConfig.title }}
          </div>
          <div class="guide__banner-con">
            <span class="guide__banner-text">{{ emptyConfig.desc }}</span>
          </div>
        </div>
      </div>
      <div class="content-tips">{{ emptyConfig.tips }}</div>
      <div class="content-guidance">
        <div class="content-item" v-for="(item, index) in emptyConfig.list" :key="index">
          <div class="guidance-line" v-if="index < (list.length - 1)"></div>
          <div class="guidance-arrow-line" v-else>
            <div class="guidance-arrow"></div>
          </div>
          <img class="guidance-icon" :src="item.image" />
          <div class="guidance-title">{{ item.title }}</div>
          <div class="guidance-desc">{{ item.desc }}</div>
        </div>
      </div>
      <div class="content-foot">
        <fx-button class="tool-button" size="small" type="primary" @click="addContentMarketing">{{ emptyConfig.btnText }}</fx-button>
        <fx-button class="tool-button" size="small" @click="knowMore" v-if="false">{{ $t('marketing.pages.content_marketing.ljgdxq_9f9d04') }}</fx-button>
      </div>
    </div>
</template>

<script>
import introImg from '@/assets/images/content-marketing/intro-image.png'
import introImgEn from '@/assets/images/content-marketing/intro-image-en.png'
import multivenueImg from '@/assets/images/content-marketing/multivenue-image.png'
import multivenueImgEn from '@/assets/images/content-marketing/multivenue-image-en.png'

import { getImageByLang } from '@/utils/i18n.js'

export default {
  data() {
    return {
      list: [
        {
          title: $t('marketing.pages.content_marketing.hdyxcl_f1b2af'),
          image: require("@/assets/images/content-marketing/guidance-1.png"),
          desc: $t('marketing.pages.content_marketing.gjmbyhzdcl_3c7aac')
        }, 
        {
          title: $t('marketing.pages.content_marketing.zzcynr_6d386c'),
          image: require("@/assets/images/content-marketing/guidance-2.png"),
          desc: $t('marketing.commons.hdybpskhal_727ad4')
        }, 
        {
          title: $t('marketing.pages.content_marketing.dqdffnr_7ec27f'),
          image: require("@/assets/images/content-marketing/guidance-3.png"),
          desc: $t('marketing.commons.wxgzhdxxxt_435474')
        }, 
        {
          title: $t('marketing.commons.xgzz_5d9756'),
          image: require("@/assets/images/content-marketing/guidance-4.png"),
          desc: $t('marketing.pages.content_marketing.llhxszh_9a56bc')
        }, 
        {
          title: $t('marketing.pages.content_marketing.nrpghyh_3b47aa'),
          image: require("@/assets/images/content-marketing/guidance-5.png"),
          desc: $t('marketing.commons.dbyhcl_3264ec')
        }
      ],
      bannerImg: getImageByLang([introImg, introImgEn]),
    }
  },
  computed: {
    emptyConfig() {
      const type = Number(this.$route.query.formType)
      const multivenueConfig = {
        bannerImg:  getImageByLang([multivenueImg, multivenueImgEn]),
        title: $t('marketing.pages.setting.dhdzh_0bf57e'),
        desc: $t('marketing.pages.content_marketing.xlxdxhdyzs_21bf13'),
        list: [
          {
            title: $t('marketing.pages.content_marketing.hdch_c8cba8'),
            image: require("@/assets/images/content-marketing/guidance-1.png"),
            desc: $t('marketing.pages.content_marketing.zdxldhchdj_7e9c11')
          }, 
          {
            title: $t('marketing.pages.content_marketing.tynrzz_0a8c28'),
            image: require("@/assets/images/content-marketing/guidance-2.png"),
            desc: $t('marketing.commons.hdybpskhal_727ad4')
          },
          {
            title: $t('marketing.pages.content_marketing.dqdffnr_7ec27f'),
            image: require("@/assets/images/content-marketing/guidance-3.png"),
            desc: $t('marketing.commons.wxgzhdxxxt_435474')
          },
          {
            title: $t('marketing.commons.xgzz_5d9756'),
            image: require("@/assets/images/content-marketing/guidance-4.png"),
            desc: $t('marketing.pages.content_marketing.ssgzghdbmq_837802')
          },
          {
            title: $t('marketing.pages.content_marketing.ztpg_e643dd'),
            image: require("@/assets/images/content-marketing/guidance-5.png"),
            desc: $t('marketing.pages.content_marketing.zzxszhjgpg_6218ff')
          }
        ],
        btnText: $t('marketing.pages.content_marketing.ljxjdhdyx_12194c')
      }
      const normalConfig = {
        bannerImg:  getImageByLang([introImg, introImgEn]),
        title: $t('marketing.pages.content_marketing.xsyx_6c9e91'),
        desc: $t('marketing.pages.content_marketing.jymbyhcjsj_5a1365'),
        tips: $t('marketing.pages.content_marketing.ygjznrzdxy_506815'),
        list: [
          {
            title: $t('marketing.pages.content_marketing.hdyxcl_f1b2af'),
            image: require("@/assets/images/content-marketing/guidance-1.png"),
            desc: $t('marketing.pages.content_marketing.gjmbyhzdcl_3c7aac')
          }, 
          {
            title: $t('marketing.pages.content_marketing.zzcynr_6d386c'),
            image: require("@/assets/images/content-marketing/guidance-2.png"),
            desc: $t('marketing.commons.hdybpskhal_727ad4')
          },
          {
            title: $t('marketing.pages.content_marketing.dqdffnr_7ec27f'),
            image: require("@/assets/images/content-marketing/guidance-3.png"),
            desc: $t('marketing.commons.wxgzhdxxxt_435474')
          },
          {
            title: $t('marketing.commons.xgzz_5d9756'),
            image: require("@/assets/images/content-marketing/guidance-4.png"),
            desc: $t('marketing.pages.content_marketing.llhxszh_9a56bc')
          },
          {
            title: $t('marketing.pages.content_marketing.nrpghyh_3b47aa'),
            image: require("@/assets/images/content-marketing/guidance-5.png"),
            desc: $t('marketing.commons.dbyhcl_3264ec')
          }
        ],
        btnText: $t('marketing.pages.content_marketing.ljxjxsyx_37840d')
      }
      return type === 6 ? multivenueConfig : normalConfig
    }
  },
  methods: {
    addContentMarketing(){
      this.$emit('addContentMarketing')
    },
    activityTitleClick(id) {
      this.$router.push({ name: "content-dashboard", params: { id } });
    },
    knowMore() {}
  }
}
</script>

<style lang="less">
.content-empty {
  height: 100%;
  width: 100%;
  .content-summarize {
    margin: 17px;
    display: flex;
    &-photo {
      position: relative;
      flex: 1 0 auto;
      min-width: 900px;
      width: 100%;
      height: 260px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding-left: 120px;

      .guide__banner-h {
        max-width: 640px;
        font-weight: 700;
        font-size: 20px;
        color: #49638d;
      }

      .guide__banner-con {
        max-width: 640px;
        display: flex;
        flex-direction: column;
        text-align: left;
        margin-top: 16px;

        .guide__banner-text {
          color: #6f889c;
          font-size: 16px;
        }

        .guide__banner-text + .guide__banner-text {
          margin-top: 4px;
        }
      }
    }
  }
  .content-tips {
    color: #2A304D;
    font-size: 16px;
    text-align: center;
    margin: 45px 17px 23px 17px;
  }
  .content-guidance {
    display: flex;
    flex-flow: row nowrap;
    position: relative;
    justify-content: center;
    margin: 15px 34px 65px 34px;
    .content-item {
      display: flex;
      flex-flow: column nowrap;
      align-items: center;
      flex: 1 1 20%;
      padding-left: 5%;
      &:first-of-type {
        padding-left: 0;
      }
      .guidance-line {
        position: absolute;
        height: 1px;
        width: 20%;
        border-bottom: 1px solid #e9edf5;
        top: 20px;
        transform: translateX(50%);
        z-index: 1;
      }
      .guidance-arrow-line {
        position: absolute;
        height: 1px;
        width: 30px;
        border-bottom: 1px solid #e9edf5;
        top: 20px;
        transform: translateX(50%);
        z-index: 1;
        .guidance-arrow {
          position: absolute;
          top: -7px;
          left: 100%;
          width: 14px;
          height: 15px;
          background: url("../../assets/images/content-marketing/arrow.png") center / 14px 15px no-repeat;
        }
      }
      .guidance-icon {
        width: 40px;
        height: 40px;
        z-index: 2;
      }
      .guidance-title {
        color: #181C25;
        margin-top: 14px;
        font-size: 14px;
      }
      .guidance-desc {
        color: #91959E;
        margin-top: 12px;
        font-size: 12px;
      }
    }
  }
  .content-foot {
    text-align: center;
    .tool-button {
      margin: 0 20px;
    }
  }
}
</style>