<template>
  <div class="content-marketing-s">
    <!-- <div class="km-g-loading-mask" v-show="loading">
      <span class="loading"></span>
    </div> -->
    <v-header
      :title="$route.query.formType === 0 ? $t('marketing.commons.xsyx_6c9e91') : $t('marketing.pages.marketing_calendar.dhdzh_0bf57e')"
      :border="true"
    >
      <div class="activeList">
      <ContentGroupHeader
        :object-type="13"
        :data="selectedActivitys"
        type="activity"
        @close="handleClearSelect"
        @update:list="handleUpdateList"
      />
      <div
        v-if="haveData"
        slot="right"
        class="content-tool"
      >
        <ContentTagsQuery
          v-model="materialTagFilter"
          @change="handleTagsChange"
        />
        <AdvancedFilterButton
          style="margin-left: 20px;margin-right: -10px;"
          :object-names="objectNames"
          @confirm="handleFilterConfirm"
        />
        <Select
          v-model="screenStatus"
          class="el-select"
          size="small"
          style="margin-left: 20px;"
          @change="screenStatusChange"
        >
          <Option
            v-for="item in options"
            :key="item.apiName"
            :value="item.apiName"
            :label="item.fieldName"
            style="font-size: 14px;"
          />
        </Select>
        <fx-input
          v-model="searchText"
          size="small"
          class="search-input"
          style="margin-left: 20px;"
          :placeholder="$t('marketing.pages.content_marketing.sshdmc_fad35d')"
          prefix-icon="el-icon-search"
          @change="searchAcitvity"
        />
        <fx-button
          class="add-button"
          type="primary"
          size="small"
          @click="addContentMarketing"
          >
          <i class="yxt-icon16 icon-add"></i>
          <span>{{ $t('marketing.commons.xjhd_adc8df') }}</span>
        </fx-button>
      </div>
    </div>
    </v-header>
    <!-- <div class="createActive">
      <div class="leftContent contentChild">
        <div class="leftCoin">
          <img
            src="@/assets/images/icon/active.png"
            alt=""
          >
        </div>
        <div class="describe">
          <p class="p1">
            {{ $t("marketing.pages.content_marketing.rctghd_2850ce") }}
          </p>
          <p class="p2">
            {{ $t("marketing.pages.content_marketing.qyxbbpsfby_db4429") }}
          </p>
          <p
            class="p3"
            @click="addContentMarketing(0)"
          >
            + {{ $t("marketing.commons.ljcj_9fd000") }}
          </p>
        </div>
      </div>
      <div class="rightContent contentChild">
        <div class="rightCoin">
          <img
            src="@/assets/images/icon/moreMeeting.png"
            alt=""
          >
        </div>
        <div class="describe">
          <p class="p1">
            {{ $t("marketing.pages.content_marketing.xsxxdhchd_aacab5") }}
          </p>
          <p class="p2">
            {{ $t("marketing.pages.content_marketing.cpfbhdxzhq_188f34") }}
          </p>
          <p
            class="p3"
            @click="addContentMarketing(1)"
          >
            + {{ $t("marketing.commons.ljcj_9fd000") }}
          </p>
        </div>
      </div>
    </div> -->
    <div
      v-loading="loading"
      class="content-body"
    >
      <div
        v-if="haveData && pageData.totalCount > 0"
        ref="activityListWrap"
        class="activity-list"
      >
        <div
          v-for="(item, index) in dataList"
          :key="index"
          class="activity-item"
        >
          <div
            class="activity__select"
            @click.stop="handleStopDefault"
          >
            <fx-checkbox
              :value="selectedMap[item.id] > -1"
              @change="e => handleActivityCheck(e, item)"
            />
          </div>
          <div
            class="activity-item-message"
            @click="activityTitleClick(item.id,item.name)"
          >
            <!-- 多活动组合-->
            <div
              v-if="$route.query.formType == 6"
              class="avatarWrap avatarWrapMoreMeeting"
            >
              <img
                src="@/assets/images/icon/moreMeeting.png"
                alt=""
              >
              <p>{{ item.eventTypeLabel }}</p>
            </div>
            <!-- 线上营销 -->
            <div
              v-else
              class="avatarWrap avatarWrapOther"
            >
              <img
                src="@/assets/images/icon/active.png"
                alt=""
              >
              <p>{{ item.eventTypeLabel }}</p>
            </div>

            <div class="activity-item-title">
              <span class="title-span">{{ item.name }}</span>
              <template v-if="false">
                <span
                  v-if="item.bizStatus === 'scheduled'"
                  class="item-status status-scheduled"
                >{{
                  $t("marketing.pages.content_marketing.jhz_4daea5")
                }}</span>
                <span
                  v-else-if="item.bizStatus === 'ongoing'"
                  class="item-status status-ongoing"
                >{{ $t("marketing.commons.jhz_fb852f") }}</span>
                <span
                  v-else-if="item.bizStatus === 'complete'"
                  class="item-status status-complete"
                >{{ $t("marketing.commons.yjs_047fab") }}</span>
                <span
                  v-else-if="item.bizStatus === 'finish'"
                  class="item-status status-finish"
                >{{
                  $t("marketing.pages.content_marketing.yzz_255412")
                }}</span>
              </template>
            </div>
            <div class="activity-item-line">
              <div class="activity-owner">
                {{ $t("marketing.commons.fzr_41b7c4") }}
                {{ getName(item.owners[0]) }}
              </div>
              <div
                v-show="marketingEventAudit"
                class="activity-status"
              >
                <!-- {{item.lifeStatus}} -->
                {{ $t('marketing.commons.shzt_b6d0e9') }}：<span>{{ CampaignReviewStatusLabel[item.lifeStatus] }}</span>
              </div>
              <div class="active-time">
                {{ $t("marketing.commons.hdsj_9e62e9") }}
                {{ getTime(item.beginTime) }}
                {{ $t("marketing.commons.z_981cbe") }}
                {{ getTime(item.endTime) }}
              </div>
            </div>
            <div class="activity-item-line">
              <div
                v-if="item.materialTags && item.materialTags.length > 0"
                class="activity-tags"
                @click.stop
              >
                <img
                  :src="iconTags"
                  class="icon"
                >
                <v-tag
                  style="flex: 1;"
                  name-key="name"
                  empty-text=""
                  placement="bottom-start"
                  :data="item.materialTags || []"
                />
              </div>
            </div>
          </div>

          <div class="activity-item-count">
            <!--            <div class="count-border"></div>-->
            <div
              v-if="$route.query.formType == 6"
              class="count-item sub-count-item"
            >
              <div class="count-num">
                {{ item.relateSubCount || 0 }}
              </div>
              <div class="count-title">
                {{ $t("marketing.commons.glzhd_6ca5c9") }}
              </div>
            </div>
            <template v-else>
              <div class="count-item">
                <div class="count-num">
                  {{ item.contentNum }}
                </div>
                <div class="count-title">
                  {{ $t("marketing.commons.tgnr_a6ec90") }}
                </div>
              </div>
              <div class="count-item">
                <div class="count-num">
                  {{ item.uv }}
                </div>
                <div class="count-title">
                  {{ $t("marketing.commons.fwrs_c3c959") }}
                </div>
              </div>
              <div class="count-item">
                <div class="count-num">
                  {{ item.leadNum }}
                </div>
                <div class="count-title">
                  {{ $t("marketing.commons.xss_0ac14d") }}
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>

      <Empty
        v-if="haveData && pageData.totalCount <= 0"
        class="empty-wrapper"
        :title="$t('marketing.commons.sjwk_a1751c')"
      />
      <div
        v-show="haveData"
        class="activity-list-footer"
      >
        <v-pagen
          :pagedata.sync="pageData"
          @change="handlePageChange"
        />
      </div>
      <content-empty
        v-if="finishAjax && !haveData"
        @addContentMarketing="addContentMarketing(0)"
      />
      <PermissionDialog
        :marketing-event-name="marketingEventName"
        :marketing-event-id="marketingEventId"
        :visible.sync="permissionDialogVisible"
      />
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import Empty from '@/components/common/empty.vue'
import VHeader from '@/components/content-header/index.vue'
import VPagen from '@/components/kitty/pagen.vue'
import http from '@/services/http/index.js'
import { getTime, getName } from './func.js'
import ContentEmpty from './empty.vue'
import { requireAsync } from '@/utils/index.js'
import AdvancedFilterButton from '@/components/advanced-filter-button/index.vue'
import { CampaignReviewStatusLabel } from '@/utils/statusEnum.js'
import PermissionDialog from '@/pages/live-marketing/components/permissionsDialog.vue'
import kisvData from '@/modules/kisv-data.js'
import ContentTagsQuery from '@/components/content-tags-selector/tags-query.vue'
import ContentGroupHeader from '@/components/ContentGroupHeader/index.vue'
import VTag from '@/components/table-ex/tag.vue'

import iconTags from '@/assets/images/icon-tags.svg'

export default {
  components: {
    Empty,
    ContentEmpty,
    VHeader,
    VPagen,
    Select: FxUI.Select.components.ElSelect,
    Option: FxUI.Select.components.ElSelect.components.ElOption,
    AdvancedFilterButton,
    PermissionDialog,
    ContentTagsQuery,
    ContentGroupHeader,
    VTag,
  },
  data() {
    return {
      iconTags,
      finishAjax: false,
      haveData: false,
      loading: false,
      screenStatus: '', // 筛选options
      options: [],
      searchText: '',
      pageData: {
        layout: 'prev, pager, next, total, sizes, jumper',
        pageNum: 1,
        totalCount: 0,
        pageSize: 10,
        pageSizes: [10, 20, 30, 50],
      },
      fitlers: [],
      dataList: [],
      eventTypeList: [],
      objectNames: [{ name: $t('marketing.commons.schd_833ba0'), value: 'MarketingEventObj' }],
      CampaignReviewStatusLabel,
      permissionDialogVisible: false,
      marketingEventId: '',
      marketingEventName: '',
      vDatas: kisvData.datas,
      materialTagFilter: {
        queryType: 2,
        tags: [],
      },
      selectedActivitys: [],
    }
  },
  computed: {
    ...mapState('MarketingEventSet', ['marketingEventAudit']),
    selectedMap() {
      const activitysMap = {}
      this.selectedActivitys.forEach((item, index) => {
        activitysMap[item.id] = index
      })

      return activitysMap
    },
  },
  created() {
    // ..

    this.getMarketingEventCommonSetting()
  },
  mounted() {
    console.log('列表页面mounted')
    this.getDataList()
  },
  methods: {
    handleFilterConfirm(data) {
      console.log(data)
      this.fitlers = data
      this.searchAcitvity()
    },
    getMarketingEventCommonSetting() {
      // 0 线上营销  6 多活动
      const type = Number(this.$route.query.formType)
      http.getMarketingEventCommonSetting({ type }).then(res => {
        if (res && res.errCode == 0) {
          if(res.data.activityTypeMapping && res.data.activityTypeMapping.length > 0) {
            const contentMarketingMapping = res.data.activityTypeMapping.filter(item => {
              return item.activityType === type
            })
            if(contentMarketingMapping && contentMarketingMapping.length > 0) {
              this.options = [
                {
            apiName: '',
            fieldName: $t('marketing.commons.qb_a8b0c2'),
                },
                ...contentMarketingMapping[0].mapping,
              ]
            }
          }
        }
      })
    },
    getTime,
    getName,
    searchAcitvity() {
      this.pageData.pageNum = 1
      this.getDataList()
    },
    screenStatusChange() {
      this.pageData.pageNum = 1
      this.getDataList()
    },
    getCRMDescribeLayout() {
      return new Promise(resolve => {
        FS.util.FHHApi({
          url:
            '/EM1HNCRM/API/v1/object/MarketingEventObj/controller/DescribeLayout',
          data: {
            apiname: 'MarketingEventObj',
            include_detail_describe: true,
            include_layout: true,
            layout_type: 'add',
            recordType_apiName: 'default__c',
          },
          success: res => {
            if (!res.Value || res.Value.errCode) return
            this.loading_marketingEventName = false
            const obj = res.Value.objectDescribe || {}
            resolve(obj)
          },
        })
      })
    },
    async addContentMarketing() {
      const type = Number(this.$route.query.formType)
      const that = this
      const obj = await this.getCRMDescribeLayout()
      const event_form = type === 0 ? 'online_marketing' : 'multivenue_marketing'
      // 新建营销内容
      requireAsync('crm-modules/action/field/field', field => {
        CRM.api.add({
          apiname: 'MarketingEventObj',
          Model: field.Model.extend({
            parse(res) {
              const _options = res.objectDescribe.fields.event_type.options
              const submitOptions = []

              _options.forEach(item => {
                that.options.forEach(i => {
                  if (item.value === i.apiName) {
                    submitOptions.push(item)
                  }
                })
              })
              res.objectDescribe.fields.event_type.options = submitOptions
              res.objectDescribe.fields.event_type.disable_after_filter = false
              // 因为目前市场活动新建event_form字段没支持到禁用能力  所以只展示当前活动类型 其他的都隐藏 
              res.objectDescribe.fields.event_form.options = res.objectDescribe.fields.event_form.options.filter(item =>item.value === event_form)
             console.log('res.objectDescribe.fields',res.objectDescribe.fields)
             return field.Model.prototype.parse.apply(this, arguments)
            },
          }),
          title: $t('marketing.commons.xj_8b0ecd', {
            data: { option0: obj.display_name },
          }),
          show_type: 'full',
          nonEditable: true,
          data: {
            // 默认选择第一个活动类型 因为第一个是全部 所以取第二个
            event_type: that.options.length > 0 ? that.options[1].apiName : '',
            event_form: event_form,
          },
          success: (_type, data) => {
            this.activityTitleClick(data._id)
          },
          renderComplete: (comp, model) => {
            return
            const styleText = `[data-apiname="event_type"]::after {
              content: "";
              position: absolute;
              left: 0;
              top: 0;
              width: 100%;
              height: 100%;
              background: #ccc;
              opacity: 0.5;
            }`
            const $selector = $('.crm-c-dialog [data-apiname="event_type"]')
            const style = document.createElement('style')
            style.type = 'text/css'
            try {
              style.appendChild(document.createTextNode(styleText))
            } catch (ex) {
              style.styleSheet.cssText = styleText
            }
            let hasCurEventType = false // 是否有会议营销的活动类型，如果没有，则放开选择器
            try {
              const curEventType = model.attributes.forms.event_type.fieldAttr.options.filter(
                item => item.value === 'content_marketing',
              )
              if (curEventType.length > 0) {
                hasCurEventType = true
              }
            } catch (e) {
              hasCurEventType = false
            }
            hasCurEventType && $selector.append(style)
          },
        })
      })
    },
    handlePageChange(data) {
      this.pageData.pageNum = data.pageNum
      this.pageData.pageSize = data.pageSize
      this.getDataList()
    },
    getDataList() {
      const materialTagFilter = {
        type: this.materialTagFilter.queryType,
        materialTagIds: this.materialTagFilter.tags.map(el => el.tagId),
      }
      // 多会场活动单独处理
      const eventForm = Number(this.$route.query.formType) === 6 ? 'multivenue_marketing' : 'online_marketing'
      const params = {
        pageNo: this.pageData.pageNum,
        pageSize: this.pageData.pageSize,
        eventType: this.screenStatus,
        filterData: this.fitlers.length > 0 ? this.fitlers[0] : {},
        materialTagFilter,
        eventForm
      }
      if (this.searchText !== '') {
        params.name = this.searchText
      }
      this.loading = true
      http.listContentMarketingEvent(params).then(res => {
        this.finishAjax = true
        if (res && res.errCode === 0) {
          const { data } = res
          this.pageData.totalCount = data.totalCount
          if (!this.haveData && data.totalCount > 0) {
            this.haveData = true
          }
          const _data = data.data || []
          if (_data.length) {
            _data.forEach(element => {
              this.options.forEach(i => {
                if (element.eventType === i.apiName) {
                  element.fieldName = i.fieldName
                }
              })
            })
          }

          this.dataList = _data.map(item => {
            if (!item.owners) {
              return { ...item, owners: [] }
            }
            return item
          })

          if (this.$refs.activityListWrap) {
            this.$refs.activityListWrap.scrollTop = 0
          }

          this.loading = false
        }
      })
    },
    async activityTitleClick(id, name) {
      const permisson = await this.checkMarketingEventPermission(id)
      if (!permisson) {
        this.marketingEventId = id
        this.marketingEventName = name
        this.permissionDialogVisible = true
      } else {
        this.$router.push({ name: 'content-dashboard', params: { id }, query: {formType: this.$route.query.formType} })
      }
    },
    async checkMarketingEventPermission(id) {
      // 开了多组织权限才需要判断
      let flag = true
      if (this.vDatas.uinfo.marketingDataIsolation) {
        const res = await http.getEntityOpenness({
          objectApiName: 'MarketingEventObj',
          objectIds: [id],
        })
        //  1是只读,2是读写
        if (res.errCode === 0 && res.data && res.data.openness) {
          flag = res.data.openness[id] === 2
        }
      }
      return flag
    },
    handleTagsChange() {
      console.log('handleTagsChange')
      this.pageData.pageNum = 1
      this.getDataList()
    },
    handleClearSelect() {
      this.selectedActivitys = []
    },
    handleUpdateList() {
      this.getDataList()
      this.selectedActivitys = []
    },
    handleActivityCheck(checked, item) {
      if (checked) {
        this.selectedActivitys.push(item)
      } else {
        this.selectedActivitys = this.selectedActivitys.filter(el => el.id !== item.id)
      }
    },
    handleStopDefault(e) {
      e.stopPropagation()
    },
  },
}
</script>

<style lang="less">
.content-marketing-s {
  height: 100%;
  display: flex;
  flex-flow: column nowrap;
  position: relative;
  min-width: 700px;
  background: #f2f2f5;
  .createActive {
    width: 100%;
    background: #fff;
    display: flex;
    .contentChild {
      width: 50%;
      box-sizing: border-box;
      padding: 16px;
      display: flex;
      height: 100%;
      border-bottom: 1px solid #dee1e8;
      border-right: 1px solid #dee1e8;
      &:last-child {
        border-right: 0;
      }
      .leftCoin {
        width: 40px;
        height: 40px;
        min-width: 40px;
        border-radius: 50%;
        background: #a3d962;
        display: flex;
        justify-content: center;
        align-items: center;
        img {
          width: 16px;
          height: 16px;
        }
      }
      .rightCoin {
        width: 40px;
        height: 40px;
        min-width: 40px;
        border-radius: 50%;
        background: #368dff;
        display: flex;
        justify-content: center;
        align-items: center;
        img {
          width: 16px;
          height: 16px;
        }
      }
      .describe {
        margin-left: 16px;
        .p1 {
          font-size: 16px;
        }
        .p2 {
          font-size: 14px;
          color: #91959e;
        }
        .p3 {
          font-size: 14px;
          color: var(--color-info06);
          cursor: pointer;
          margin-top: 8px;
        }
      }
    }
    .leftContent {
      i {
        font-size: 20px;
        color: #fff0b7;
      }
    }
  }
  .activeList {
    width: 100%;
    background: #fff;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    .activeListTitle {
      font-size: 16px;
    }
  }
  .content-header {
    flex-shrink: 0;
  }
  .content-tool {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 40px;
    flex: 1;

    .search-input {
      margin: 0px 10px;
    }
  }
  .empty-wrapper {
    height: 100%;
    box-sizing: border-box;
  }
  .content-body {
    height: calc(100% - 120px);
    margin-top: 12px;
    background: #fff;
  }
  .activity-list {
    height: 100%;
    overflow-y: auto;
    border-bottom: 1px solid #e9edf5;
    .activity-item {
      height: 110px;
      display: flex;
      border-bottom: 1px solid #eeeeee;

      &:hover {
        background: #f6f9fc;
      }

      &:last-of-type {
        border-bottom: none;
      }
    }
    .activity__select {
      width: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .activity-item-message {
      cursor: pointer;
      width: 80%;
      min-width: 0;
      margin-right: 24px;
      padding: 20px 0 0 21px;
      .avatarWrap {
        width: 100px;
        height: 70px;
        float: left;
        margin-right: 12px;
        color: #ffffff;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        border-radius: 2px;
        img {
          width: 20px;
          height: 20px;
          margin-bottom: 3px;
        }
        p {
          font-weight: 800;
        }
      }
      .avatarWrapMoreMeeting {
        background: #368dff;
      }
      .avatarWrapLive {
        background: #ffca2b;
      }
      .avatarWrapMeeting {
        background: #40b6ff;
      }
      .avatarWrapOther {
        background: #a3d962;
      }
    }
    .activity-item-line {
      margin-top: 10px;
      color: rgba(84, 88, 97, 100);
      height: 16px;
      font-size: 12px;
      display: flex;
      align-items: center;

      .activity-status {
        margin-left: 40px;
        flex: 1;
      }

      .active-time {
        margin-left: 40px;
      }

      .activity-tags {
        flex: 1;
        display: flex;
        align-items: center;

        .icon {
          width: 16px;
          height: 16px;
          margin-right: 8px;
        }
      }
    }
    .activity-item-title {
      display: flex;
      font-size: 16px;
      height: 17px;
      line-height: 17px;
      min-width: 0;
    }
    .title-span {
      cursor: pointer;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      color: rgba(24, 28, 37, 100);
      font-size: 16px;
    }
    .item-status {
      flex-shrink: 0;
      font-size: 12px;
      margin-left: 9px;
      padding: 0px 4px;
      border-radius: 2px;
    }
    .status-scheduled {
      color: var(--color-primary06,#407FFF);
      border: 1px solid #81b2fe;
    }
    .status-ongoing {
      color: #7fc25d;
      border: 1px solid #7fc25d;
    }
    .status-complete,
    .status-finish {
      color: #91959e;
      border: 1px solid #91959e;
    }
    .activity-item-count {
      display: flex;
      align-items: center;
      flex-shrink: 0;
      padding-left: 50px;
    }
    .count-border {
      margin: 12px 40px 12px 0px;
      width: 2px;
      height: 33px;
      background-color: #e9edf5;
    }
    .count-item {
      margin: 0px 24px;
      text-align: center;
    }
    .sub-count-item {
      margin-left: 100px;
    }
    .count-num {
      font-size: 16px;
      color: #181c25;
    }
    .count-title {
      color: #91959e;
      font-size: 12px;
    }
  }
  .activity-list-footer {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    height: 49px;
    background: #ffffff;
  }
}
</style>
