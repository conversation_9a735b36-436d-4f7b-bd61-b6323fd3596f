<template>
  <div
    v-loading="loading"
    :class="[
      'content-radar',
      source == 'liveTab' && 'radar-extra',
      source == 'contentTab' && 'conent-radar-extra'
    ]"
  >
    <v-header
      v-if="showCrumbs"
      class="radar-header"
      :title="crumbs"
      :border="true"
    />
    <div
      v-if="dataList && dataList.length"
      class="radar-list"
    >
      <div
        v-for="(item) in dataList"
        :key="item.marketingActivityId"
        class="radar-list-item"
        @click="handleDetailShow(item)"
      >
        <div
          class="item-list-photo"
          :style="{ backgroundImage: `url('${item.photo}')` }"
        />
        <div class="item-list-message">
          <div class="item-title">
            <span>
              {{ item.title }}
            </span>
            <span
              slot="icon-element"
              class="status"
              :style="{ color: item.statusColor }"
            ><span
              class="dot"
              :style="{ background: item.statusColor }"
            />{{ item.statusText }}</span>
          </div>
          <div class="item-sub-message">
            <span style="margin-right: 16px;">{{ item.createTime }}</span>
            <span style="margin-right: 16px;">{{ getType(item.spreadType) }}</span>
            <span v-if="marketingActivityAudit">{{ $t('marketing.commons.shzt_b6d0e9') }}：<span
              style="color: #407FFF"
              @click.stop="() => handleOpenMarketingActivityDetail(item)"
            >{{ item.auditStatusStr }}</span></span>
          </div>
        </div>
        <div
          v-if="item.showAuditButton"
          class="item-button"
        >
          <span @click.stop="() => handleSend(item)">{{ $t('marketing.commons.ljfs_1176c5') }}</span>
        </div>
        <div
          v-if="[1, 5, 7, 13].includes(item.spreadType)"
          class="radar-list-counter"
        >
          <!-- 全员, 企业微信 -->
          <div class="counter-item">
            <!-- 访问人次使用pv  不应该使用lookUpUserCount  lookUpUserCount是访问人数 -->
            <div class="counter-num">
              {{ item.pv || 0 }}
            </div>
            <div class="counter-text">
              {{ $t('marketing.commons.fwrc_ed9a0b') }}
            </div>
          </div>
          <div class="counter-item">
            <div class="counter-num">
              {{ item.leadAccumulationCount || 0 }}
            </div>
            <div class="counter-text">
              {{ $t('marketing.commons.hqxs_a65930') }}
            </div>
          </div>
        </div>
        <div
          v-if="[2, 6].includes(item.spreadType)"
          class="radar-list-counter"
        >
          <!-- 公众号、邮件 -->
          <div class="counter-item">
            <div class="counter-num">
              {{ item.needSend || 0 }}
            </div>
            <div class="counter-text">
              {{ $t('marketing.commons.fsrs_8428aa') }}
            </div>
          </div>
          <div class="counter-item">
            <div class="counter-num">
              {{ item.actualSend || 0 }}
            </div>
            <div class="counter-text">
              {{ $t('marketing.commons.fscg_9db9a7') }}
            </div>
          </div>
        </div>
        <div
          v-if="item.spreadType === 3"
          class="radar-list-counter"
        >
          <!-- 短信 -->
          <div class="counter-item">
            <div class="counter-num">
              {{ item.needSend || 0 }}
            </div>
            <div class="counter-text">
              {{ $t('marketing.commons.fsrs_8428aa') }}
            </div>
          </div>
          <div class="counter-item">
            <div class="counter-num">
              {{ item.actualSend || 0 }}
            </div>
            <div class="counter-text">
              {{ $t('marketing.commons.fscg_9db9a7') }}
            </div>
          </div>
        </div>
        <div
          v-if="item.spreadType === 12"
          class="radar-list-counter"
        >
          <!-- whatsapp -->
          <div class="counter-item">
            <div class="counter-num">
              {{ item.sendCount || 0 }}
            </div>
            <div class="counter-text">
              {{ $t('marketing.commons.fsrs_8428aa') }}
            </div>
          </div>
          <div class="counter-item">
            <div class="counter-num">
              {{ item.sendSuccessCount || 0 }}
            </div>
            <div class="counter-text">
              {{ $t('marketing.commons.fscg_9db9a7') }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <Empty
      v-else
      class="empty-wrapper"
      :title="$t('marketing.commons.sjwk_a1751c')"
    />
    <v-pagen
      class="radar-pagen"
      :pagedata.sync="pageData"
      @change="handlePageChange"
    />
    <market-promotion-details
      :params="detailParams"
      :visible="detailVisible"
      @close="detailVisible = false"
    />
    <WhatsappSideslipDetail
      v-if="whatsappSideslipDetail"
      :visible="whatsappSideslipDetail"
      :item="whatsappData"
      @close="handleWhatsappClose"
    />
  </div>
</template>

<script>

import { mapState } from "vuex";
import MarketPromotionDetails from "@/components/market-promotion-details/index";
import WhatsappSideslipDetail from '@/pages/whatsapp/components/sideslip-detail.vue'
import VHeader from "@/components/content-header/index";
import VPagen from "@/components/kitty/pagen";
import { getTime, getType } from "./func.js";
import crowdCover from "@/assets/images/icons/default-cover-crowd.jpg";
import smsCover from "@/assets/images/icons/sms-default-icon.jpg";
import wechatCover from "@/assets/images/icons/wechat-default-icon.jpg";
import Empty from "@/components/common/empty";
import http from "@/services/http/index";
import { requireAsync } from "@/utils";

import {
  AUDIT_STATUS, SPREAD_STATUS, WX_STATUS, SMS_STATUS, QYWX_SPREAD_STATUS, MAIL_STATUS, WHATSAPP_STATUS,
} from '@/utils/constant'

export default {
  components: {
MarketPromotionDetails,
VHeader,
VPagen,
Empty,
WhatsappSideslipDetail,
},
  props: {
    // 传入市场活动ID，则优先取
    marketingEventId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      id: '',
      loading: false,
      sending: false,
      detailParams: {},
      detailVisible: false,
      source: '',
      crumbs: [
        {
          text: $t('marketing.commons.hdyx_f631fa'),
          to: { name: 'content-dashboard' },
          params: { id: this.id },
        },
        {
          text: $t('marketing.commons.tgld_803716'),
          to: false,
        },
      ],
      showCrumbs: true,
      pageData: {
        layout: 'prev, pager, next, total, sizes, jumper',
        pageNum: 1,
        totalCount: 0,
        pageSize: 10,
        pageSizes: [10, 20, 30, 50],
      },
      dataList: [], // dataList会经过handle筛选数据，所以要用接口数据要记得添加
      whatsappSideslipDetail: false,
    }
  },
  computed: {
    ...mapState('MarketingEventSet', ['marketingActivityAudit']),
  },
  mounted() {
    this.id = this.$route.params.id
    const { source, objectName, objectId } = this.$route.query
    this.source = source
    // 来源是直播
    if (source === 'live') {
      this.crumbs = [
        {
          text: $t('marketing.commons.zbyx_a9fa5d'),
          to: { name: 'live-marketing' },
        },
        {
          text: objectName || '--',
          to: { name: 'live-dashboard', params: { id: objectId } },
        },
        {
          text: $t('marketing.commons.tgld_803716'),
          to: false,
        },
      ]
    } else if (source === 'liveTab' || source == 'contentTab' || source == 'conference' || source == 'targetTab') {
      this.showCrumbs = false
    }
    
    // 如果路由地址包含 /meeting-marketing/，则隐藏面包屑
    if (this.$route.path.includes('/meeting-marketing/')) {
      this.showCrumbs = false
    }
    this.getAllRadarList()
  },
  methods: {
    handleWhatsappSideslipDetail(item) {
      this.whatsappSideslipDetail = true
      this.whatsappData = item
    },
    handleWhatsappClose() {
      this.whatsappSideslipDetail = false
    },
    // 打开推广活动详情
    handleOpenMarketingActivityDetail(item) {
      if (item.marketingActivityId) {
        requireAsync('crm-components/showdetail/showdetail', Detail => {
          this.$detail = this.$detail || new Detail({ showMask: true })
          setTimeout(() => {
            this.$detail.setApiName('MarketingActivityObj')
            this.$detail.show(item.marketingActivityId)
          }, 1)
        })
      }
    },
    handleSend(item) {
      if (!item.marketingActivityId || this.sending) return
      this.sending = true
      http.immediatelySend({
        id: item.marketingActivityId,
      }).then(({ errCode, errMsg }) => {
        this.sending = false
        if (errCode === 0) {
          this.getAllRadarList()
        } else {
          FxUI.Message.error(errMsg || $t('marketing.commons.czsbqshzs_fb5ded'))
        }
      })
    },
    handleDetailShow(item) {
      this.$nextTick(() => {
        if (!item.marketingActivityId) {
          return
        }
        this.detailParams = {
          id: item.marketingActivityId,
        }
        if (item.spreadType === 12) {
          this.handleWhatsappSideslipDetail({
            marketingActivityId: item.marketingActivityId,
          })
        } else {
          this.detailVisible = true
        }
      })
    },
    handlePageChange(data) {
      this.pageData.pageNum = data.pageNum
      this.pageData.pageSize = data.pageSize
      this.getAllRadarList()
    },
    getTime,
    getType,
    getAllRadarList() {
      this.loading = true
      http
        .getAllRadarList({
          spreadTypeList: [1, 2, 3, 5, 6, 7, 12, 13],
          pageNum: this.pageData.pageNum || 1,
          pageSize: this.pageData.pageSize,
          marketingEventId: this.marketingEventId || this.id,
        })
        .then(res => {
          this.loading = false
          if (res && res.errCode === 0) {
            const { data } = res
            this.pageData.totalCount = data.totalCount
            this.dataList = (data.result || [])
              .map(data => this.dataHandle(data))
              .filter(data => data.filterError !== true)
            console.log('radar:', this.dataList)
          }
        })
    },
    dataHandle(data) {
      // 将用雷达到的数据转化
      const message = {
        ...data,
        marketingActivityId: data.marketingActivityId,
        title: data.spreadContent,
        createTime: this.getTime(data.createTime),
        spreadType: data.spreadType,
        auditStatusStr: AUDIT_STATUS[data.auditStatus],
        photo: '',
      }
      let typeData = {}
      if (data.spreadType === 1 || data.spreadType === 13) {
        // 全员推广
        typeData = data.marketingActivityNoticeSendStatisticResult
        message.photo = typeData.coverUrl || crowdCover
        message.lookUpUserCount = typeData.lookUpUserCount || 0 // 访问人次
        message.leadAccumulationCount = typeData.leadAccumulationCount || 0 // 线索数
        message.statusText = SPREAD_STATUS[data.spreadStatus] || '--'
        message.showAuditButton = this.marketingActivityAudit && data.auditStatus === 'normal' && data.spreadStatus === 1
        const COLOR = {
          0: '#FF8000',
          1: '#FF8000',
          2: '#FF8000',
          3: '#FF8000',
          4: '#57B855',
          5: '#999',
        }
        message.statusColor = COLOR[data.spreadStatus] || ''
        console.log('message: ', message);
        return message
      } if (
        data.spreadType === 2
        && data.marketingActivityWeChatServiceStatisticResult
      ) {
        // 公众号推广
        typeData = data.marketingActivityWeChatServiceStatisticResult
        message.photo = typeData.materialData
          ? typeData.materialData.photoThumbnailUrl
            || typeData.materialData.coverUrl
            || wechatCover
          : wechatCover
        message.needSend = typeData.needSend || 0 // 发送人数
        message.actualSend = typeData.actualSend || 0 // 成功发送
        message.statusText = WX_STATUS[data.status] || '--'
        message.showAuditButton = this.marketingActivityAudit && data.auditStatus === 'normal' && data.status === 5
        const COLOR = {
          1: '#999',
          2: '#FF8000',
          3: '#57B855',
          4: '#999',
          5: '#FF8000',
          10: '#999',
        }
        message.statusColor = COLOR[data.status] || ''
        return message
      } if (data.spreadType === 3) {
        // 短信推广
        typeData = data.marketingActivitySmsSendStatisticResult
        message.photo = typeData.coverUrl || smsCover
        message.needSend = typeData.needSend || 0 // 发送人数
        message.actualSend = typeData.actualSend || 0 // 成功发送
        message.statusText = SMS_STATUS[data.status] || '--'
        message.showAuditButton = this.marketingActivityAudit && data.auditStatus === 'normal' && data.status === 0
        const COLOR = {
          1: '#999',
          2: '#FF8000',
          3: '#57B855',
          4: '#999',
          5: '#FF8000',
          10: '#999',
        }
        message.statusColor = COLOR[data.status] || ''
        return message
      } if (data.spreadType === 5) {
        // 企业微信推广
        typeData = data.marketingActivityQywxGroupSendMessageResult
        message.photo = typeData.coverUrl || crowdCover
        message.lookUpUserCount = typeData.lookUpUserCount || 0 // 发送人数
        message.leadAccumulationCount = typeData.leadAccumulationCount || 0 // 成功发送
        message.statusText = QYWX_SPREAD_STATUS[data.status] || '--'
        message.showAuditButton = this.marketingActivityAudit && data.auditStatus === 'normal' && data.status === 0
        const COLOR = {
          0: '#999',
          1: '#999',
          2: '#FF8000',
          3: '#57B855',
          10: '#999',
        }
        message.statusColor = COLOR[data.status] || ''
        return message
      } if (data.spreadType === 6) {
        // 邮件推广
        typeData = data.marketingMailGroupSendResult
        message.photo = smsCover
        message.needSend = typeData.sendCount || 0 // 发送人数
        message.actualSend = typeData.successCount || 0 // 成功发送
        message.statusText = MAIL_STATUS[typeData.sendStatus] || '--'
        message.showAuditButton = this.marketingActivityAudit && data.auditStatus === 'normal' && data.status === 0
        const COLOR = {
          0: '#FF8000',
          1: '#FF8000',
          2: '#999',
          3: '#57B855',
          10: '#999',
        }
        message.statusColor = COLOR[typeData.sendStatus] || ''
        return message
      } if (data.spreadType === 7) {
        // 全员推广
        typeData = data.marketingActivityPartnerSendStatisticResult
        message.photo = typeData.coverUrl || crowdCover
        message.lookUpUserCount = typeData.lookUpUserCount || 0 // 访问人次
        message.leadAccumulationCount = typeData.leadAccumulationCount || 0 // 线索数
        message.statusText = SPREAD_STATUS[data.status] || '--'
        message.showAuditButton = this.marketingActivityAudit && data.auditStatus === 'normal' && data.spreadStatus === 1
        const COLOR = {
          0: '#FF8000',
          1: '#FF8000',
          2: '#FF8000',
          3: '#FF8000',
          4: '#57B855',
          5: '#999',
        }
        message.statusColor = COLOR[data.spreadStatus] || ''
        return message
      } if (data.spreadType === 12) {
        // 全员推广
        typeData = data.marketingActivityWhatsAppSpreadResult
        message.photo = typeData.coverUrl || crowdCover
        message.lookUpUserCount = typeData.lookUpUserCount || 0 // 访问人次
        message.leadAccumulationCount = typeData.leadAccumulationCount || 0 // 线索数
        message.statusText = WHATSAPP_STATUS[data.status] || '--'
        const COLOR = {
          3: '#57B855',
          4: '#ff522a',
          5: '#FF8000',
          10: '#999',
        }
        message.statusColor = COLOR[data.status] || ''
        return message
      }
      return { filterError: true } // 没有类型过滤掉
    },
  },
}
</script>

<style lang="less">
.content-radar {
  height: 100%;
  display: flex;
  flex-flow: column nowrap;
  background-color: #fff;
  .radar-header {
    flex-shrink: 0;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
  }
  .radar-list {
    height: 100%;
    display: flex;
    flex-flow: column nowrap;
    overflow-y: auto;
  }
  .radar-list-item {
    height: 82px;
    min-height: 82px;
    border-bottom: 1px solid #e9edf5;
    margin-left: 18px;
    display: flex;
    align-items: center;
    cursor: pointer;
    &:hover{
      background-color: #f5f7fa;
    }
    .item-button{
      padding-right: 60px;
      padding-left: 10px;
        span{
          white-space: nowrap;
          cursor: pointer;
          color: @color-link;
        }
      }
    .item-list-photo {
      cursor: pointer;
      width: 60px;
      height: 43px;
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
      flex-shrink: 0;
      margin-right: 10px;
    }
    .item-list-message {
      width: 100%;
      min-width: 0;
      margin-right: 20px;
      .item-title {
        margin-bottom: 6px;
        color: #181c25;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        > span {
          cursor: pointer;
        }
      }
      .status {
        font-size: 12px;
        color: #879eb8;
        border-radius: 2px;
        display: inline-block;
        text-align: center;
        margin-left: 5px;
        width: 70px;
        flex: 0 0 70px;
        .dot {
          width: 5px;
          height: 5px;
          border-radius: 5px;
          line-height: 21px;
          display: inline-block;
          position: relative;
          bottom: 2px;
          margin-right: 4px;
        }
      }
      .item-sub-message {
        color: #91959e;
        font-size: 12px;
      }
    }
    .radar-list-counter {
      flex-shrink: 0;
      width: 245px;
      display: flex;
      justify-content: space-around;
      align-items: center;
      position: relative;
      &:before {
        position: absolute;
        left: 0;
        height: 36px;
        width: 0px;
        border: 1px solid #e9edf5;
        content: "";
      }
      .counter-item {
        text-align: center;
      }
      .counter-num {
        color: #181c25;
        font-size: 16px;
        margin-bottom: 4px;
      }
      .counter-text {
        color: #91959e;
        font-size: 12px;
      }
    }
  }
  .radar-pagen {
    flex-shrink: 0;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
  }
}
.radar-extra {
  background: #ffffff;
  margin: 0 10px 10px 10px;
  height: calc(100% - 205px);
}
.conent-radar-extra {
  background: #ffffff;
  margin: 0 10px 10px 10px;
  height: 100%;
}
</style>
