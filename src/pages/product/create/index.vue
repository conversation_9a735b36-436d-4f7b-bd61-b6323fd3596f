<template>
  <div class="create-product-page">
    <div class="page-header">
      <div class="page-title">
        {{ title }}
      </div>
      <div class="step-change">
        <fx-button
          type="primary"
          :loading="isSubmiting"
          @click="submit"
        >
          {{ $t('marketing.commons.qr_e83a25') }}
        </fx-button>
        <fx-button @click="cancel">
          {{ $t('marketing.commons.qx_625fb2') }}
        </fx-button>
      </div>
    </div>
    <div class="w-main-content">
      <div class="wmc-preview">
        <div class="preview-title">
          {{ $t('marketing.commons.yl_645dbc') }}
        </div>
        <preview-detail :product-data="productData" />
      </div>
      <div class="wmc-form">
        <div class="form-cols">
          <div
            class="form-col"
            :class="formValidConf.name"
          >
            <span> <em>*</em>{{ $t('marketing.commons.cpmc_ff6855') }} </span>
            <div class="name-input">
              <input
                v-model="productData.name"
                type="text"
                :placeholder="$t('marketing.commons.qsr_02cc4f')"
                @keyup="inputLimit('name')"
              >
              <span>
                <i>{{ productData.name.length }}</i>/
                <i>{{ lengthLimit.name }}</i>
              </span>
            </div>
            <div class="err-msg">
              {{ $t('marketing.pages.product.cpmcbnwk_eecae0') }}
            </div>
          </div>
          <div
            class="form-col"
            :class="formValidConf.summary"
          >
            <span> <em>*</em>{{ $t('marketing.commons.cpjj_a542ce') }} </span>
            <div class="desc-input">
              <textarea
                v-model="productData.summary"
                :placeholder="$t('marketing.commons.qsr_02cc4f')"
                @keyup="inputLimit('summary')"
              />
              <span>
                <i>{{ productData.summary.length }}</i>/
                <i>{{ lengthLimit.summary }}</i>
              </span>
            </div>
            <div class="err-msg">
              {{ $t('marketing.pages.product.cpjjbnwk_6f2631') }}
            </div>
          </div>
          <div class="form-col">
            <span> <em />{{ $t('marketing.pages.product.cpjg_3454ff') }} </span>
            <input
              v-model="productData.price"
              type="text"
              :placeholder="$t('marketing.commons.qsr_02cc4f')"
            >
            <div class="err-msg">
              {{ $t('marketing.commons.jegsywzqlr_0686cd') }}
            </div>
          </div>
          <div class="form-col">
            <span> <em />{{ $t('marketing.commons.yhjg_f1025d') }} </span>
            <input
              v-model="productData.discountPrice"
              type="text"
              :placeholder="$t('marketing.commons.qsr_02cc4f')"
            >
            <div class="err-msg">
              {{ $t('marketing.commons.jegsywzqlr_0686cd') }}
            </div>
          </div>
          <div
            class="form-col"
            :class="formValidConf.coverTapathlist"
          >
            <span> <em>*</em>{{ $t('marketing.commons.fxyfmsz_c868a9') }}</span>
            <div class="upload-wrapper">
              <div class="tips">
                {{ $t('marketing.commons.fmjycc_ae76b8') }}
              </div>
              <PictureCutter
                style="margin-left: 120px;"
                :max-size="1024 * 1024"
                :cut-size="[{
                  title: $t('marketing.pages.meeting_marketing.hbfm_d8183b') + '（9:5）',
                  desc: $t('marketing.components.PictureSelector.xs_6fa81e'),
                  width: 900,
                  height: 500,
                  photoTargetType: 48,
                },{
                  title: $t('marketing.commons.xcxfm_94b4f3') + '（5:4）',
                  desc: $t('marketing.components.PictureSelector.yyzfwxxcxy_d66d73'),
                  width: 420,
                  height: 336,
                  photoTargetType: 46,
                },{
                  title: $t('marketing.commons.pyqfm_ea274c') + '（1:1）',
                  desc: $t('marketing.components.PictureSelector.yyzfwxhpyq_5c11c7'),
                  width: 300,
                  height: 300,
                  photoTargetType: 47,
                }]"
                :default-image-url="coverImage"
                :uploadtip="$t('marketing.commons.jyccxszcgs_906ed5')"
                output-path-type="a"
                show-picture-standard
                @change="handlePictureCutterChange"
              />
              <div class="err-msg">
                {{ $t('marketing.pages.product.qscfmt_982851') }}
              </div>
            </div>
          </div>
          <div
            class="form-col"
            :class="formValidConf.coverTapathlist"
          >
            <span> <em>*</em>{{ $t('marketing.commons.lbt_0c0180') }}</span>
            <div class="upload-wrapper">
              <div class="tips">
                {{ $t('marketing.pages.product.zdsczlbtjy_6e54e1') }}
                <div class="standard">
                  <PictureStandard />
                </div>
              </div>
              <PictureListUpload
                class="upload-comp"
                style="margin-left: 120px;"
                :photolist="coverlist"
                :big-image-list="bigCoverlist"
                :max="coverMax"
                :tapathlist.sync="coverTapathlist"
                :error-code.sync="coverErrorCode"
                :single-change="true"
                :max-select-count="1"
                :is-show="true"
                :cut-size="cutSize"
                @update:imgUrls="updateCover"
              />
              <div class="err-msg">
                {{ $t('marketing.pages.product.qscfmt_982851') }}
              </div>
            </div>
          </div>
          <div
            class="form-col"
            :class="formValidConf.productTapathlist"
          >
            <span> <em>*</em>{{ $t('marketing.commons.cpxq_61644d') }} </span>
            <div class="upload-wrapper video-wrapper">
              <div>
                {{ $t('marketing.commons.sp_7fcf42') }}
              </div>
              <div
                v-if="!video.code"
                class="box box_addPicture"
                @click="showAddVideoDialog = true"
              >
                <i class="icon km-ico-add" />
                <span class="text">{{ $t('marketing.commons.tj_b58c75') }}</span>
                <input type="button">
              </div>
              <div
                v-else
                class="box box_addPicture box_video"
                :style="'background-image: url(' + video.poster + ');'"
              >
                <i
                  class="icon km-ico-close"
                  @click="removeVideo"
                />
                <a
                  :href="video.code"
                  target="_blank"
                >
                  <div class="video-mask" />
                  <img
                    :src="videoBtnImg"
                    class="video-btn-img"
                  >
                </a>
              </div>
              <!-- <div class="err-msg">请上传视频</div> -->
            </div>
            <div class="upload-wrapper picture-upload">
              <div>{{ $t('marketing.pages.product.tpzdsczwlb_8b7cb5', {data: ({'option0': productMax})}) }}</div>
              <PictureListUpload
                class="upload-comp"
                :photolist="productImglist"
                :big-image-list="bigProductImglist"
                :max="productMax"
                :tapathlist.sync="productTapathlist"
                :error-code.sync="productErrorCode"
                :img-urls.sync="imgUrls"
                :draggable="true"
                :is-show="true"
                :max-select-count="maxProSelectCount"
              />
              <div class="err-msg product-detail">
                {{ $t('marketing.commons.qzsscyztp_4600b1') }}
              </div>
            </div>
          </div>
          <div class="form-col custom-poster">
            <span><em />{{ $t('marketing.commons.fxhb_9c7858') }}</span>
            <div
              class="custom-poster"
              @click="handleCustomPoster"
            >
              <div
                v-if="!sharePosterUrl"
                class="custom-poster__empty"
              >
                <i class="icon km-ico-add" />
                <span class="text">{{ $t('marketing.commons.xztp_ba9fc4') }}</span>
              </div>
              <div
                v-else
                class="custom-poster__empty"
              >
                <img
                  :src="sharePosterUrl"
                  class="img"
                >
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="wmc-setup">
        <div class="setup-switch">
          <span>{{ $t('marketing.commons.cpsy_981bb4') }}</span>
          <fx-switch
            v-model="productData.tryOutEnable"
            size="small"
          />
          <!-- @change="changeTryOutEnable" -->
        </div>
        <div class="setup-desc">
          {{ $t('marketing.pages.product.kqhgcpktjs_ce102d') }}
        </div>
        <template v-if="productData.tryOutEnable">
          <!-- <div class="form-name">
            <span>试用表单名称：</span>
            <span class="form-title">{{ tryOutFormName }}</span>
            <a class="abtn" @click="tryOutVisible = true">预览</a>
          </div>-->
          <div class="field-name required">
            {{ $t('marketing.pages.product.xzcpsybd_e2a60c') }}
          </div>
          <form-relative
            :form-title="formTitle"
            :product-form-id="productFormId"
            :from-type="fromType"
            @update:formRelative="handleFormRelative"
          />
          <div class="field-name required">
            {{ $t('marketing.commons.anmc_cf6e87') }}
          </div>
          <fx-input
            v-model="productData.tryOutButtonValue"
            :maxlength="buttonVlen"
            :placeholder="$t('marketing.pages.product.qsrsyanwz_5318bb')"
            size="small"
          />
        </template>
        <!-- CTA -->
        <div class="setup-switch" style="margin-top: 20px;">
          <span>{{ $t('marketing.components.Hexagon.sz_de3a05') }}</span>
          <fx-switch
            :value="ctaConfig.enable"
            size="small"
            @change="handleCTAEnableChange"
          />
          <!-- @change="changeTryOutEnable" -->
        </div>
        <div class="setup-desc">
          {{ $t('marketing.components.Hexagon.kyywysqgzg_fc9e8b') }}<a href="https://help.fxiaoke.com/93d5/0f81/5171" target="_blank" style="margin-left: 10px;">{{
                  $t('marketing.commons.ljgd_06a3c1') }}</a>
        </div>
        <template v-if="ctaConfig.enable">
          <div class="field-name required">
            {{ $t('marketing.pages.product.xz_717a64') }}
          </div>
          <PickerInput :placeholder="$t('marketing.components.Hexagon.qxzzj_18d4f8')"
            :text="ctaConfig && ctaConfig.ctaName || ''" style="width:100%" @click="handlePickerInputClick" />
        </template>
      </div>
    </div>
    <VideoDialog
      v-if="showAddVideoDialog"
      :visible.sync="showAddVideoDialog"
      @confirm="handleUploadConfirm"
    />
    <CustomPosterDialog
      :visible="customPosterDialogVisible"
      :default-img="sharePosterUrl"
      @update:visible="customPosterDialogVisible = false"
      @selected="handleCustomPosterSelected"
    />
    <CtaDialog v-if="ctaDialogVisible" :visible.sync="ctaDialogVisible" sceneType="page"
      @confirm="handleCtaDialogConfirm" />
  </div>
</template>

<script>
import PictureListUpload from '@/components/picture-list-upload/picture-list-upload.js'
import FormRelative from '@/components/form-relative/index.vue'
import previewDetail from '../preview/index.vue'
import Form from '../form/index.js'

import datas from './datas.js'
import videoBtnImg from '@/assets/images/play-btn.png'
import './index.less'

import { confirm } from '@/utils/globals.js'
import http from '@/services/http/index.js'

import appmarketingRPC from '@/utils/appmarketing.rpc.js'
import VideoDialog from '@/pages/video/components/video-dialog.vue'
import util from '@/services/util/index.js'
import PictureStandard from '@/components/PictureStandard/index.vue'
import CustomPosterDialog from '@/components/qrposter-create-dialog/custom-poster-dialog/index.vue'
import PictureCutter from '@/components/picture-cutter/index.vue'
import PickerInput from '@/components/picker-input';
import CtaDialog from '@/pages/cta/components/cta-dialog.vue';

export default {
  components: {
    PictureListUpload,
    FormRelative,
    previewDetail,
    elSwitch: FxUI.Switch,
    elInput: FxUI.Input,
    VideoDialog,
    PictureStandard,
    CustomPosterDialog,
    PictureCutter,
    PickerInput,
    CtaDialog,
  },
  data() {
    return {
      cutSize: {
        width: 900,
        height: 500,
      },
      buttonVlen: 6,
      productData: {
        name: '',
        summary: '',
        price: '',
        discountPrice: '',
        headPicsThumbs: [],
        detailPicsThumbs: [],
        video: '',
        tryOutEnable: true,
        tryOutButtonValue: '',
        originalImageAPath: '',
        cutOffsetList: [],
      },
      coverlist: [],
      bigCoverlist: [],
      coverErrorCode: '',
      coverTapathlist: [],
      productImglist: [],
      bigProductImglist: [],
      productTapathlist: [],
      productErrorCode: '',
      coverMax: 3,
      productMax: 20,
      imgUrls: [],
      lengthLimit: {
        name: 50,
        summary: 300,
      },
      formValidConf: {
        name: '',
        summary: '',
        coverTapathlist: '',
        productTapathlist: '',
      },
      isSubmiting: false,
      showAddVideoDialog: false,
      video: {
        code: '',
        poster: '',
        originLink: '',
      },
      videoType: '',
      videoBtnImg,
      title: $t('marketing.commons.xjcp_b22068'),
      tryOutFormObjectApiName: '',
      tryOutFormName: '',
      tryOutVisible: false,
      trialFormData: null,
      productFormId: '',
      formTitle: '',
      fromType: 0,
      showMCS: false,
      detailPicsList: 0,
      picList: 0,
      sharePosterUrl: '',
      sharePosterAPath: '',
      customPosterDialogVisible: false,
      coverImage: '',
      ctaDialogVisible: false,
      ctaConfig: {
        enable: false,
        ctaId: '',
        ctaName: '',
      },
    }
  },
  computed: {
    maxSelectCount() {
      return 3 - this.picList
    },
    maxProSelectCount() {
      return 20 - this.detailPicsList
    },
  },
  watch: {
    coverTapathlist(newVal) {
      this.picList = newVal.length
    },
    productTapathlist(newVal) {
      this.productData.detailPicsThumbs = newVal
      this.detailPicsList = newVal.length
    },
    imgUrls(newVal) {
      if (newVal.length) {
        this.productData.detailPicsThumbs = newVal
      } else {
        this.productData.detailPicsThumbs = []
      }
    },
    video(newVal) {
      this.productData.video = newVal
    },
  },
  beforeDestroy() {
    if (this.dialog) {
      this.dialog.destroy()
    }
  },
  created() {},
  mounted() {
    if (this.$route.params.pid) {
      this.title = $t('marketing.pages.product.bjcp_0630f4')
    }
    Promise.resolve()
      .then(() => {
        if (this.$route.params.pid) {
          return datas
            .getProductDetail(this, this.$route.params.pid)
            .then(data => {
              if (data) {
                this.productData = data
              }
              // CTA配置
              const ctaConfig = data.ctaRelationInfos && data.ctaRelationInfos[0] || {};
              if(ctaConfig.ctaId){
                this.ctaConfig = {
                  ...ctaConfig,
                  enable: true,
                }
              }
              this.sharePosterUrl = data.sharePosterUrl
              this.sharePosterAPath = data.sharePosterAPath
              this.coverImage = data.sharePicOrdinaryCutUrl || (data.headPicsThumbs && data.headPicsThumbs[0]) || ''
              if (data.bindObjectType === 26) {
                this.productFormId = data.hexagonSiteData.siteId
                this.formTitle = data.hexagonSiteData.siteName
                this.fromType = 26
              } else if (data.bindObjectType === 16) {
                this.productFormId = data.formData.formId
                this.formTitle = data.formData.fromName
                this.fromType = 16
              }
              return data
            })
        }

        return undefined
      })
      .then(() => datas.getTryOutInfo())
      .then(data => {
        if (data) {
          this.tryOutFormObjectApiName = data.apiName
          this.tryOutFormName = data.displayName
          this.productData.tryOutButtonValue = this.limitButtonVlen(
            this.productData.tryOutButtonValue || data.displayName,
          )
          datas.getTryOutFormData(data.apiName).then(formData => {
            if (formData) {
              this.trialFormData = formData
            }
          })
        }
      })
  },
  methods: {
    handleCTAEnableChange(status) {
      console.log('status', status)
      this.ctaConfig.enable = status;
    },
    handlePickerInputClick() {
      this.ctaDialogVisible = true;
    },
    handleCtaDialogConfirm(data) {
      this.ctaConfig.ctaId = data.id;
      this.ctaConfig.ctaName = data.name;
    },
    handlePictureCutterChange(file) {
      const coverImage = file.cutOffsetList[0]
      this.coverImage = coverImage.image
      // this.formData.coverTaPath = file.cropApath;
      // 始终用第一张尺寸的图
      this.productData.originalImageAPath = coverImage.originalPath || coverImage.path || file.photoPath || file.path
      this.productData.cutOffsetList = file.cutOffsetList
    },
    handleDialogClose() {
      this.tryOutVisible = false
    },
    handleDialogApply(tplId) {
      this.tryOutVisible = false
      this.dialog = new Form({
        templateId: tplId || 'new',
        nextPath: { name: 'product-create' },
      })
      this.dialog.show()
    },
    limitButtonVlen(value) {
      return (value || '').slice(0, this.buttonVlen)
    },
    changeTryOutEnable(status) {
      if (status) {
        datas
          .getTryOutInfo()
          .catch(() => {})
          .then(data => {
            // 网络或者接口出错，取不到状态时，也当作"无可用表单"处理
            if (data) {
              this.tryOutFormObjectApiName = data.apiName
              this.tryOutFormName = data.displayName
              this.productData.tryOutButtonValue = this.limitButtonVlen(
                this.productData.tryOutButtonValue || data.displayName,
              )
            } else {
              this.productData.tryOutEnable = false
              confirm(
                $t('marketing.pages.product.nhwcshndcp_e9bf40'),
                $t('marketing.commons.ts_02d981'),
                {
                  confirmButtonText: $t('marketing.pages.product.ljcsh_f715eb'),
                },
              ).then(() => {
                this.tryOutVisible = true
              })
            }
          })
      }
    },
    removeVideo() {
      this.video = datas.getBlankVideo()
    },
    inputLimit(colName) {
      const val = this.productData[colName]
      this.productData[colName] = val.substring(0, this.lengthLimit[colName])
      this.formValidConf[colName] = ''
    },
    updateCover(imgUrls) {
      if (imgUrls.length) {
        this.productData.headPicsThumbs = imgUrls
      } else {
        this.productData.headPicsThumbs = []
      }
    },
    cancel() {
      if (this.$route.query.from === 'dialog') {
        window.close()
      } else {
        this.$router.back()
      }
    },
    checkForm() {
      return datas.checkForm(this)
    },
    submit() {
      if (this.isSubmiting || !this.checkForm()) {
        return
      }
      this.isSubmiting = true
      const options = datas.getSubmitData(this, this.$route.params.pid)
      http
        .createOrUpdateProduct(options)
        .then(res => {
          if (res.errCode === 0) {
            if (this.$route.query.from === 'dialog') {
              // 从素材选择弹窗点击新建过来的
              if (this.$route.query.fromid) {
                appmarketingRPC.set(this.$route.query.fromid, {
                  type: 4,
                  id: res.data.id,
                })
              }
              window.close()
            } else if (this.$route.params.pid) {
              // 编辑态
              this.$router.replace({ name: 'product' })
            } else {
              // 新建态
              const { from } = this.$route.query
              this.$router.replace({
                name: 'materiel-create-success',
                params: {
                  materielType: 'product',
                  materielId: res.data.id,
                },
                ...((from && { query: { from } }) || {}),
              })
            }
          }
        })
        .catch(() => {})
        .then(() => {
          this.isSubmiting = false
        })
    },
    handleFormRelative(formInfo) {
      this.fromType = formInfo.objectType
      this.productFormId = formInfo.id
      this.formTitle = formInfo.title
    },
    handleUploadConfirm(data) {
      this.video = {
        code:
          data.type === 'link'
            ? util.translate2PureVideoLink(data.url)
            : data.url,
        poster: data.cover,
        originLink: data.url,
      }
    },
    handleCustomPoster() {
      this.customPosterDialogVisible = true
    },
    handleCustomPosterSelected(data) {
      const { path, url } = data || {}
      this.sharePosterUrl = url || ''
      this.sharePosterAPath = path || ''
    },
  },
}
</script>
