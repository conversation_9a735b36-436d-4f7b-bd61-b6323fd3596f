// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import Vue from 'vue'
import Router from 'vue-router'
import '@/modules/kisv-data.js' // 触发该模块加载用户信息
import hljs from 'highlight.js/lib/core'
import javascript from 'highlight.js/lib/languages/javascript'
import java from 'highlight.js/lib/languages/java'
import xml from 'highlight.js/lib/languages/xml'
import vuePlugin from '@highlightjs/vue-plugin'
import { permissions } from '@/modules/permissions.js'
import { record } from '@/modules/record.js'
import { vueRouterAddHandler, vueRouterRemoveHandler } from '@/utils/init.js'
import App from './app.vue'
import store from './store/index.js'
import routerConfig from './router/index.js'
import http from '@/services/http/index.js'
import util from '@/services/util/index.js'

import Message from '@/components/message/index.js'
import MessageBox from '@/components/message-box/index.js'

import { getMarktingAIHelper } from './fsinit.js'
import preloadI18nImg from '@/modules/preload-i18n-img'

import 'highlight.js/styles/stackoverflow-light.css'
import 'crm-assets/style/all.css'
import 'crm-assets/style/page.css'

if (WDP_DEV_ENV === 'DEV' || window.location.host === 'crm.ceshi112.com') {
  Vue.config.devtools = true
}

Vue.directive('preload-i18n-img',preloadI18nImg);
Vue.prototype.$markCdnPath = $markCdnPath;

Vue.config.errorHandler = (err, vm, info) => {
  console.log(err)
  console.log(vm)
  console.log(info)
}
window.YXT_ALIAS = {}
window.YXT_ALIAS.http = http
window.YXT_ALIAS.util = util
window.APP_MARKETING_ENV = WDP_DEV_ENV // 注入当前环境  DEV:开发环境 PRO:否则是生产环境
if(!window.MARKETING) {
  window.MARKETING = {};
}
window.MARKETING.store = store;
// 使用Vue-Router
Vue.use(Router)
// todo: 移除全局注入常用组件
Vue.prototype.$ELEMENT = { zIndex: 991 } // 高于企信右下角浮条

hljs.registerLanguage('javascript', javascript)
hljs.registerLanguage('xml', xml)
hljs.registerLanguage('java', java)
Vue.use(vuePlugin)
Vue.prototype.$localMessage = Message
Vue.prototype.$localAlert = MessageBox.alert
Vue.prototype.$localConfirm = MessageBox.confirm
Vue.prototype.$localPrompt = MessageBox.prompt

let app = null

function destroy() {
  // remove router handler
  vueRouterRemoveHandler()
  if (!app || !app.$destroy) return
  app.$destroy()
  app = null
  store.router = null
}

function bootstrap(selector, options = {}) {
  // 解决vue-router3.0以上版本报错问题
  try {
    const routePush = Router.prototype.push
    Router.prototype.push = function push(location) {
      const routePushResult = routePush.call(this, location)
      try {
        return routePushResult.call(this, location).catch(err => err)
      } catch (e) {
        return routePushResult
      }
    }
  } catch (error) { /* empty */ }

  // 初始化之前先销毁，防止重复实例
  destroy()

  // 获取路由配置
  const routes = routerConfig({
    baseHash: options.baseRoutePath,
  })

  console.log('marketing router', routes)
  // prevent global pollution
  const router = new Router(routes)
  router.onError(err => {
    console.error(err)
    // this$1.errorCbs.forEach(function (cb) { cb(err); });
    // 如上是 vue-router 的代码，所以下面加 try
    try {
      // err.errMessage = err.message; // TODO err.message 内含信息并不比 err.request 多
      // err.errStack = err.stack; // TODO stack 信息可能太多
      const errorData = {
        eventType: 'vue-router',
        type: err.type,
        request: err.request,
      }
      FS.util.uploadLog('appMarketingError', errorData)
    } catch (e) { /* empty */ }
  })
  // 路由权限校验
  permissions(router)
  // 全局埋点
  record(router)
  // hijack router handler
  vueRouterAddHandler(router, {
    baseHash: options.baseRoutePath,
  })

  // 页面加载完成后，和AI助手交互
  router.afterEach((to, from) => {
    // 为了取到挂载的vue实例，这里做个延时
    setTimeout(() => {
      let instance = null
      const { matched } = to
      if (matched.length) {
        const { instances } = matched[0]
        instance = instances.default
      }

      const marktingAIHelper = getMarktingAIHelper()
      if (marktingAIHelper) {
        const { href, protocol, host } = window.location
        marktingAIHelper.postMessage({
          type: 'marketing-scene',
          source: 'taro-ai-helper',
          data: {
            path: href.replace(`${protocol}//${host}`, ''),
            instance,
          },
        })
      }
    }, 2000)
  })

  store.router = router
  app = new Vue({
    el: selector,
    router,
    components: { App },
    template: `<app render-type="${options.renderType}"/>`,
    store,
  })

  return app
}

export { bootstrap, destroy }
