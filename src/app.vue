<template>
  <div :class="{ 'yxt-app': 1, 'yxt-app-fc': isFullScreen, 'yxt-app-noHeader': isNoHeader, 'yxt-app-withoutmenu': renderType === 'withoutmenu', 'fx-desktop-common-panel':1 }">
    <meta
      name="referrer"
      content="no-referrer"
    >
    <div
      v-if="!uinfoJudged"
      v-loading="!uinfoJudged"
      class="g-body-loading"
    />
    <div
      v-if="uinfoJudged && !kisvData.invalidType"
      class="g-body-wrapper"
    >
      <div
        v-if="!isFullScreen && renderType !== 'withoutmenu'"
        :class="['g-menu-wrapper', mDatas.folded && 'folded', isMouseOn && 'mouse-on',!(userLanguage === 'zh-CN' || userLanguage === 'zh-TW') && 'g-en-menu-wrapper']"
        @mouseout="isMouseOn = false"
        @mouseover="isMouseOn = true"
      >
        <v-menu
          class="not-fold"
          :tiptxt="mDatas.folded ? $t('marketing.commons.zkcd_513c32') : $t('marketing.app.sqcd_a918f9')"
          @fold-menu="handleFoldMenu"
        />
        <v-menu
          class="fold-menu"
          :fold="true"
          :tiptxt="$t('marketing.commons.zkcd_513c32')"
          @fold-menu="handleFoldMenu"
        />
        <transition
          name="fold-slide"
          @after-enter="afterEnter"
          @after-leave="afterLeave"
        >
          <div
            v-if="showMenuMasker"
            class="menu-masker"
          />
        </transition>
      </div>
      <div class="g-content-wrapper">
        <keep-alive>
          <router-view
            v-if="$route.meta.keepAlive"
            :key="routeKey"
            @fold-menu="showFoldMenu"
          />
        </keep-alive>
        <router-view
          v-if="!$route.meta.keepAlive"
          :key="routeKey"
          @fold-menu="showFoldMenu"
        />
      </div>
    </div>
    <carousel-frame v-if="firstOpen" />
    <app-profile
      v-if="uinfoJudged && kisvData.invalidType"
      :kisvdata="kisvData"
    />
    <!-- 智能运营框架 -->
    <intelli-operation />
    <!-- VPN断网的专属云限制需要使用专属小程序 -->
    <Dialog
      :title="$t('marketing.commons.ts_02d981')"
      :okText="$t('marketing.commons.ljsj_6b4a7f')"
      width="550px"
      class="exclusive__cloud-dailog"
      :visible="updateTipVisible"
      :closeOnClickModal="false"
      :showCancel="false"
      @onSubmit="openWechatAuth"
    >
      <div class="exclusive__cloud-dailog-content">
        <i :class="['mk-icon-warning', 'exclusive__cloud-dailog-icon']"></i>
        <span :class="'exclusive__cloud-dailog-title'"
          >{{ $t('marketing.app.yynswmzgdz_224f54') }}</span
        >
      </div>
    </Dialog>
  </div>
</template>

<script>
import { openWechatAuth } from '@/components/3rd-miniprogram'
import _ from 'lodash'
import kisvData from '@/modules/kisv-data.js'
import wxList from '@/modules/wx-list.js'
import VMenu from '@/modules/menu/index.vue'
import { mDatas, setMenuById } from '@/modules/menu/menu.js'
import CarouselFrame from '@/modules/carousel-frame/index.vue'
import AppProfile from './app-profile.vue'
import Dialog from '@/components/dialog/index.vue'
import IntelliOperation from '@/modules/intelli-operation/index.vue'
import { redirectToFS } from '@/utils/index.js'
import cookie from '@/utils/cookie.js'
import { bus } from '@/utils/globals.js'
import CrmDashboard from '@/modules/crm-dashboard/index.js'

import 'style/all.less'

import http from '@/services/http/index.js'
import util from '@/services/util/index.js'

function triggerResize() {
  try {
    window.dispatchEvent(new Event('resize'))
    return
  } catch (e) { /* empty */ }
  try {
    const resizeEvent = window.document.createEvent('UIEvents')
    resizeEvent.initUIEvent('resize', true, false, window, 0)
    window.dispatchEvent(resizeEvent)
    return
  } catch (e) { /* empty */ }
  $(window).trigger('resize')
}

export default {
  name: 'App',
  components: {
    VMenu,
    CarouselFrame,
    AppProfile,
    IntelliOperation,
    Dialog,
  },
  props: {
    /**
     * 渲染模式，标准：default  CRM菜单：crmmenu
     */
    renderType: {
      type: String,
      default: 'default',
    },
  },
  data() {
    return {
      kisvData: kisvData.datas, // 将数据对象转为VUE响应式数据
      wxData: wxList.datas, // 将数据对象转为VUE响应式数据
      mDatas, // 将数据对象转为VUE响应式数据
      uinfoJudged: false,
      firstOpen: false,
      isFullScreen: false,
      isGetStrategyList: false,
      showMenuMasker: mDatas.folded, // 菜单折叠动画由一个空白元素模拟
      isMouseOn: false, // 菜单折叠后，鼠标进入时要临时展开
      isNoHeader: false,
      miniprogramBindInfo: {},
    }
  },

  computed: {
    isQywxOpen() {
      return this.$store.state.Global.isQywxOpen
    },
    isSmsOpen() {
      return this.$store.state.SmsMarketing.statusSMS === 0
    },
    isWechatOpen() {
      try {
        return wxList.datas.list.length > 0
      } catch (e) {
        return false
      }
    },
    isEmailOpen() {
      return this.$store.state.MailMarketing.completeAllConfig
    },
    isMemberOpen() {
      return this.$store.state.Member.isOpenMember
    },
    isWebsiteAccessOpen() {
      return this.$store.state.WebsiteAccess.isWebsiteAccessOpen
    },
    isCustomMiniappOpen() {
      return this.$store.state.MiniappInfo.isCustomMiniappOpen
    },
    openInfo() {
      return {
        isQywxOpen: this.isQywxOpen, // 企业微信是否开通
        isSmsOpen: this.isSmsOpen, // 短信是否开通
        isWechatOpen: this.isWechatOpen, // 公众号是否开通
        isEmailOpen: this.isEmailOpen, // 邮件是否开通
        isMemberOpen: this.isMemberOpen, // 会员是否开通
        isWebsiteAccessOpen: this.isWebsiteAccessOpen, // 官网接入是否开通
        isCustomMiniappOpen: this.isCustomMiniappOpen, // 托管小程序是否开通
        isEnterpriseLibraryOpen: this.kisvData.uinfo.enterpriseLibraryEnabled, // 企业库是否开通
      }
    },
    MARKETING_GLOBAL() {
      return {
        OPENINFO: this.openInfo,
      }
    },
    routeKey() {
      const { fullPath, meta } = this.$route
      const { key } = meta || {}

      return key || fullPath
    },
    updateTipVisible() {
      //断网私有云提示升级专属小程序
      return this.kisvData.uinfo.isVpnDisconnectCloud && this.miniprogramBindInfo.appType !== undefined && this.miniprogramBindInfo.appType !== 3;
    },
    userLanguage(){
      return FS.userLanguage
    }
  },
  watch: {
    $route(to) {
      this.isFullScreen = to.meta && to.meta.isFullScreen
    },
    kisvData: {
      deep: true,
      handler(val) {
        this.checkEnterpriseLibrary()
      },
    },
    MARKETING_GLOBAL: {
      deep: true,
      handler() {
        console.log(12345, 'MARKETING_GLOBAL', this.MARKETING_GLOBAL)
        this.__proto__.__proto__.MARKETING_GLOBAL = this.MARKETING_GLOBAL // 回溯到全局Vue实例
      },
    },
  },
  mounted() {
    // 隐藏左边的菜单时的处理，如嵌入钉钉客户管理的版本
    if (window.location.href.indexOf('noheader=1') > -1) {
      this.isNoHeader = true
    }
    this.$store.dispatch('Global/getSpreadContentDomain') // 获取用户配置的域名
    const { enterpriseAccount } = FS.contacts.getCurrentEmployee() || {}
    // 仅在线上fs和112可见
    // const isDev = WDP_DEV_ENV === 'DEV' || window.location.host === 'crm.ceshi112.com'
    // if (enterpriseAccount === 'fs' || isDev) {
    //   this.aiHelper = this.appendCompToRoot(AIHelper)
    // }

    // 获取企业品牌常用色
    this.$store.dispatch('Hexagon/fetchBranchColorList')
  },
  created() {
    // this.queryCurrentEmployee();
    const { meta, fullPath } = this.$route
    // 初始化路由前默认全屏
    if (fullPath === '/') {
      this.isFullScreen = true
    } else {
      this.isFullScreen = meta && meta.isFullScreen
    }
    this.checkUstate()
    // this.checkIfOpenCallCenter()
    // this.checkHwcloudPlatform();
    // 华为云查询是否绑定专属小程序
    this.queryBoundMiniappInfo()
    // 初始化数据驾驶舱
    CrmDashboard.init()
    // 获取市场活动配置
    this.$store.dispatch('MarketingEventSet/queryMarketingEventCommonSetting')
    // 通讯录不再使用渠道管理=》企业微信设置下的员工通讯录设置
    // 获取自定义对象列表
    this.$store.dispatch('crmLayoutMenus/findFormDescribeField')
    // this.popOperationFrame(); // 运营框架入口，暂未启用
    bus.$on('update:route-after-login', this.routeTo)
    this.__proto__.__proto__.MARKETING_GLOBAL = this.MARKETING_GLOBAL // 回溯到全局Vue实例，设置全局变量
  },
  beforeDestroy() {
    bus.$off('update:route-after-login')
    try {
      // 清除数据驾驶舱菜单，否则下次进来会重新push
      CrmDashboard.clear()
    } catch (error) { /* empty */ }

    if (this.aiHelper) {
      this.aiHelper.$destroy()
      document.body.removeChild(this.aiHelper.$el)
    }
  },
  methods: {
    handleFoldMenu() {
      this.showFoldMenu(!mDatas.folded)
    },
    showFoldMenu(flag) {
      this.showMenuMasker = flag
      if (!flag) {
        this.afterLeave()
      }
    },
    afterEnter() {
      mDatas.folded = true
      this.isMouseOn = false // 菜单折叠动画完成后，将鼠标进入状态标记为 false，以免折叠后立即触发临时展开
      this.$nextTick(triggerResize)
    },
    afterLeave() {
      mDatas.folded = false
      this.$nextTick(triggerResize)
    },
    setCarouselStorage() {
      util.ls('isOpened', 'true')
    },
    clearCarouselStorage() {
      util.ls('isOpened', 'false')
    },
    // 纷享运营框架配置
    popOperationFrame() {
      if (this.isGetStrategyList || !cookie.get('fs_token')) {
        return
      }
      http.GetStrategyList().then(res => {
        if (res && res.Value) {
          this.isGetStrategyList = true
          const strategy = _.find(res.Value.strategyList, item => item.buttonNodes[0] === 'FSAID_MARKETING_HOME', // 获取营销通策略
          )
          if (strategy && strategy.operationType) {
            this.firstOpen = true
            http.CleanStrategy({ strategyIdList: [strategy.strategyId] })
          }
        }
      })
    },
    setUstate(uinfo = {}) {
      // 管理员身份才调用的其它权限接口
      this.$store.dispatch('Global/getAddressBookSetting') // 获取全局通讯录类型： fs-纷享通讯录 qywx-企业微信通讯录
      this.$store.dispatch('Global/enableWxThirdPlatformBindSetting', uinfo.enableWxThirdPlatformBind) // 绑定专属小程序
      this.$store.dispatch('querySmsMarketingOpenStatus') // 短信主菜单跳转地址，子菜单可见性控制
      this.$store.dispatch('MailMarketing/queryMailMarketingOpenStatus') // 邮件营销子菜单
      this.$store.dispatch('Member/memberMenuSetting', uinfo.memberOpened) // 会员
      this.$store.dispatch('WebsiteAccess/queryWebsiteSetting') // 官网接入开通情况
      this.$store.dispatch('MiniappInfo/getBoundMiniappInfo') // 专属小程序接入情况
      wxList.queryList()
      // VPN跨云需求
      if(!this.kisvData.uinfo.isVpnDisconnectCloud){
        this.checkIsSystemNoticeUser() // 是否产品管理员，是才有权删除公告
      }
    },
    checkUstate() {
      kisvData.onJudge(() => {
        const { uinfo, pluginInfo } = kisvData.datas

        // 设置配置信息
        this.$store.commit('setConfig', uinfo || {})
        // 设置当前用户权限
        this.$store.commit('setUserRole', uinfo.roleIds || [])

        this.setUstate(uinfo)
        // if (uinfo.isAppAdmin) {
        //   this.setUstate(uinfo); // 管理员身份才调用的其它权限接口
        // } else {
        // 非管理员账号不显示这些菜单分类标题
        //   setMenuById("separate-customer", { hide: true });
        //   setMenuById("separate-manages", { hide: true });
        // }
        if (!uinfo.isAppAdmin) {
          setMenuById('separate-customer', { hide: true })
          setMenuById('separate-manages', { hide: true })
        } else {
          setMenuById('setting-setitems', { hide: false })
        }
        if (uinfo.partnerMarketingEnabled) {
          setMenuById('partner-marketing', { hide: false })
          setMenuById('report-partner', { hide: false })
        }
        // 微信商家券
        if (uinfo.wechatVoucher) {
          setMenuById('coupons-template', { hide: false })
        }

        // 纷享优惠券
        if (uinfo.sourceCouponEnabled) {
          setMenuById('fs-coupons', { hide: false })
        }
        if (uinfo.bindTencentAdEnable) {
          setMenuById('ad-tencent', { hide: false })
        }
        if (uinfo.bindBaiduAdEnable) {
          setMenuById('ad-baidu', { hide: false })
        }
        if (uinfo.bindHeadlinesAdEnable) {
          setMenuById('ad-bytedance', { hide: false })
        }
        if (uinfo.bindGoogleAdEnable) {
          setMenuById('ad-google', { hide: false })
        }

        if (uinfo.isEnabledScrmStatistic) {
          setMenuById('report-qywx-moments', { hide: false })
          setMenuById('report-qywx-group-message', { hide: false })
        }

        if (uinfo.marketingSDREnable) {
          setMenuById('marketing-sdr', { hide: false })
          setMenuById('callcenter', { hide: false })
          setMenuById('clue-workbench', { hide: false })
          setMenuById('sdr-kanban', { hide: false })
        }

        if (uinfo.whatsAppPluginEnable) {
          setMenuById('whatsappmenu', { hide: false })
        }

        if (pluginInfo.memberMarketingPluginEnable) {
          setMenuById('member-promotion', { hide: false })
          // 会员相关
          setMenuById('member-center', {
            hide: false,
          })
        }

        if(pluginInfo.meetingMarketing){
          setMenuById('meeting-marketing-init', { hide: false })
        }
        if(pluginInfo.livingMarketing){
          setMenuById('live-marketing', { hide: false })
        }
        // 只通过这个插件来控制开关
        if(pluginInfo.adMarketing){
          setMenuById('ad-marketing', { hide: false })
        }
        if(pluginInfo.websiteMarketing){
          setMenuById('website', { hide: false })
        }
        if(pluginInfo.miniappMarketing){
          setMenuById('vapp-setting', { hide: false })
        }
        if(pluginInfo.employeeMarketing){
          setMenuById('promotion', { hide: false })
        }
        if(pluginInfo.smsMarketing){
          setMenuById('sms-marketing-init', { hide: false })
        }
        if(pluginInfo.mailMarketing){
          setMenuById('mail-welcome', { hide: false })
        }
        if(pluginInfo.qywxMarketing){
          setMenuById('qywx-manage', { hide: false })
        }
        if(pluginInfo.weChatAccount){
          setMenuById('wechat-home', { hide: false })
        }
        if(pluginInfo.marketingPlan){
          setMenuById('kanban', { hide: false })
        }
        // 
        if(pluginInfo.memberMarketingPluginEnable || uinfo.partnerMarketingEnabled || pluginInfo.employeeMarketing){
          setMenuById('employee-promotion',{hide: false})
        }
        // 社会化分析和营销流程
        if(pluginInfo.socialAndAutomationEnable){
          setMenuById('social-distribution-global-index', { hide: false })
          setMenuById('marketing-process', { hide: false })
        }

        this.$store.dispatch('SocialDistribution/setSocialDistributionAuths')
        // 开了数据权限才调用
        if(this.kisvData.uinfo.marketingDataIsolation){
          // 获取员工权限
          this.$store.dispatch('Global/queryCurrentUserAccessible')
        }

        console.log('uinfoJudgeddone,setMenuById',new Date().getTime())
        console.log('uinfoJudgeddone: pluginInfo', pluginInfo);
        this.uinfoJudged = true
      })
    },
    // checkIfOpenCallCenter() {
    //   http.get_tenant_call_center_info().then(results => {
    //     if (!results.Value) return

    //     if (results.Value.isTelemarkWorkstationGrayed === 1) {
    //       setMenuById('tel-marketing', { hide: false })
    //     }
    //   })
    // },
    checkIsSystemNoticeUser() {
      http.isSystemNoticeUser().then(results => {
        if (!results || results.errCode !== 0) return
        this.$store.commit('setLoginInfo', {
          loginInfo: { isSystemNoticeUser: results.data },
        })

        if (results.data) {
          setMenuById('setting', { hide: false })
        }
      })
    },
    routeTo(routerVal) {
      if (routerVal && routerVal.name) {
        this.$router.replace(routerVal)
      }
    },
    setPreloadData() {
      this.$store.dispatch('QywxManage/queryQywxStaff')
      this.$store.dispatch('QywxManage/queryQywxDepartment')
      this.$store.dispatch('DingManage/queryDingStaffList ')
      this.$store.dispatch('DingManage/queryDingDepartmentList')
    },
    // 华为云平台，隐藏某些特定菜单
    // checkHwcloudPlatform() {
    //   if (this.kisvData.isPrivateCloud) {
    //     // setMenuById("video", { hide: true });
    //     setMenuById("folder", { hide: true });
    //     setMenuById("live-marketing", { hide: true });
    //   }
    // },
    // 企业库
    checkEnterpriseLibrary() {
      if (this.kisvData.uinfo.enterpriseLibraryEnabled) {
        setMenuById('enterprise-base', { hide: false })
      }
    },
    // 跳转微信授权页面
    openWechatAuth() {
      const redirectUrl = redirectToFS('#/app/marketing/index/=/miniapp-auth', '_return')
      const platformId = 'YXT'
      openWechatAuth({ redirectUrl, platformId }, this.queryBoundMiniappInfo.bind(this))
    },
    queryBoundMiniappInfo() {
      http
        .getBoundMiniappInfo({
          platformId: 'YXT',
        })
        .then(({ errCode, data }) => {
          if (errCode === 0 && data) {
            this.miniprogramBindInfo = data || {};
            if (data.appType === 3) {
              setMenuById('mini-app-renovation', { hide: false })
              setMenuById('company-website', { hide: false })
            }
          }
        })
    },
    appendCompToRoot(component) {
      const instance = new Vue({
        el: document.createElement('div'),
        render: h => h(component),
      })

      document.body.appendChild(instance.$el)

      return instance
    },
    // queryCurrentEmployee(){
    //   kisvData.getCurrentEmployee().then(({ errCode, data }) => {
    //     if(errCode === 0 && data){
    //       //设置配置信息
    //       this.$$store.commit("setConfig", data || {});
    //       //设置当前用户权限
    //       this.$store.commit("setUserRole", data.roleIds || []);
    //     }
    //   });
    // }
  },
}
</script>

<style lang="less">
:global {
  body {
    .z-index-auto {
      z-index: auto !important;
    }
  }
}

.yxt-app {
  display: flex;
  flex-flow: column nowrap;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  // z-index: 100;
  box-sizing: border-box;
  &.yxt-app-fc {
    padding-top: 0;
    z-index: 1200; // 高于企信右下角浮条
    .full-screen;

    .g-content-wrapper {
      margin-right: 0;
    }
  }
  &.yxt-app-noHeader {
    left: 0 !important;
  }
  &.yxt-app-withoutmenu{
    position: absolute;
    padding-top: 0;
    left: 0!important;
  }
  .g-body-loading {
    width: 100%;
    height: 100%;
  }
  .g-body-wrapper {
    flex: 1 1 auto;
    display: flex;
    flex-flow: row nowrap;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }
  .g-menu-wrapper {
    position: relative;
    flex: 0 0 auto;
    height: 100%;
    width: 200px;
    &.g-en-menu-wrapper{
      width: 250px;
    }
    // 同时存在展开态和折叠态两个菜单
    // 通过空白元素模拟折叠动画，然后切换两个菜单的显示隐藏，实现菜单折叠效果
    .fold-menu {
      display: none;
      width: 66px;
      .menu-title {
        padding: 0;
        font-size: 16px;
        justify-content: space-around;
      }
    }
    &.folded {
      width: 66px;
      .not-fold {
        display: none;
      }
      .fold-menu {
        display: block;
      }
      .menu-masker {
        display: none;
      }
      // 菜单图标 - 展开
      .menu-footer .menu-footer-fold {
        background-image: url('./assets/images/icon/menu-foldx.png');
        &:hover {
          background-image: url('./assets/images/icon/menu-foldx-actived.png');
        }
      }
      &.mouse-on {
        .m-menu-wrapper {
          box-shadow: 2px 0 4px rgba(33, 43, 54, 0.1);
        }
      }
    }
    // 折叠后的临时展开态，通过切换两个菜单显示来实现
    &.folded.mouse-on {
      z-index: 2001; // el-loading-mask 2000
      .fold-menu {
        display: none;
      }
      .not-fold {
        display: block;
      }
    }
  }
  .g-content-wrapper {
    flex: 1 1 auto;
    display: flex;
    flex-flow: column nowrap;
    overflow: auto;
    width: 100%;
    height: 100%;
    background: #ffffff;
  }

  // 这个空元素用于模拟菜单折叠动画
  .menu-masker {
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    width: 180px;
    background: #fff;
  }
  .fold-slide-enter-active {
    transition: width 0.3s;
  }
  .fold-slide-leave-active {
    transition: width 0s;
  }
  .fold-slide-enter,
  .fold-slide-leave-to {
    width: 0;
  }
}

.a-btn-href {
  color: var(--color-primary06,#407FFF);
  cursor: pointer;
}

// .el-message-box {
//   // el-ui 弹框确认按钮区样式
//   .el-message-box__btns {
//     display: flex;
//     flex-direction: row-reverse;
//     margin-bottom: -10px;
//     padding-top: 6px;
//     padding-bottom: 10px;
//     .fx-button {
//       margin-left: 10px;
//       padding: 7px 16px;
//     }
//   }
// }

.nodata-legc {
  // 旧表格无数据样式
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  line-height: 1.6;
  text-align: center;
  .nodata-img {
    width: 80px;
    height: 80px;
    margin: auto;
    background: url('./assets/images/no-data.png') no-repeat;
    background-size: cover;
  }
  .nodata-txt {
    text-align: center;
    color: #9eabbe;
    font-size: 12px;
    margin-top: 20px;
  }
}
//专属云提示
.exclusive__cloud-dailog {
  &-content {
    padding: 14px 10px 24px 10px;
    display: flex;
    align-items: center;
  }
  &-icon {
    font-size: 50px;
    color: #f27474;
  }
  &-title {
    color: @color-title;
    font-size: 14px;
    margin-left: 15px;
  }
  :global {
    .el-dialog__headerbtn {
      display: none;
    }
  }
}
// 解决新版企信桌面端高度问题
.fx-desktop{
  .yxt-app{
    height: auto;
  }
}
</style>
