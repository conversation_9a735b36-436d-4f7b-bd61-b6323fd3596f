// 审核状态
const AUDIT_STATUS = {
  normal: $t('marketing.commons.ytg_ecfa64'),
  in_change: $t('marketing.commons.bgz_3a3ae2'),
  under_review: $t('marketing.commons.shz_b720a6'),
  invalid: $t('marketing.commons.ysc_5cc232'),
  ineffective: $t('marketing.commons.wtg_4fcdbf'),
}

// 全员推广状态
const SPREAD_STATUS = {
  0: $t('marketing.commons.wks_dd4e55'),
  1: $t('marketing.commons.dfs_57a3b6'),
  2: $t('marketing.commons.fsz_702513'),
  3: $t('marketing.commons.tgz_5d2b9f'),
  4: $t('marketing.commons.ych_b3f168'),
  5: $t('marketing.commons.yjs_047fab'),
}

// 企业微信发送状，单个用户的发送状态
const QYWX_STATUS = {
  0: $t('marketing.store.wsd_e7b46a'),
  1: $t('marketing.store.ysd_f87f48'),
  2: $t('marketing.commons.sdsb_b2ccec'),
  3: $t('marketing.commons.sdsb_b2ccec'),
  99: $t('marketing.store.sdsb_b2ccec'),
}

// 企业微信推广状态
const QYWX_SPREAD_STATUS = {
  1: $t('marketing.commons.cg_22b433'),
  2: $t('marketing.commons.jhz_fb852f'),
  3: $t('marketing.commons.ywc_fad522'),
  4: $t('marketing.commons.fssb_9ca6a3'),
  5: $t('marketing.commons.dfs_57a3b6'),
  10: $t('marketing.commons.qxfs_b022e6'),
  11: $t('marketing.commons.chtg_4d5ae4'),
}

// 短信推广状态
const SMS_STATUS = {
  1: $t('marketing.commons.cg_22b433'),
  2: $t('marketing.commons.jhz_fb852f'),
  3: $t('marketing.commons.ywc_fad522'),
  4: $t('marketing.commons.fssb_9ca6a3'),
  5: $t('marketing.commons.dfs_57a3b6'),
  10: $t('marketing.commons.qxfs_b022e6'),
}

// 邮件推广状态
const MAIL_STATUS = {
  0: $t('marketing.commons.ddfs_4e859f'),
  1: $t('marketing.commons.fsz_702513'),
  2: $t('marketing.commons.fssb_9ca6a3'),
  3: $t('marketing.commons.fscg_9db9a7'),
  10: $t('marketing.commons.qxfs_b022e6'),
}

// 公众号推广状态
const WX_STATUS = {
  1: $t('marketing.commons.cg_22b433'),
  2: $t('marketing.commons.jhz_fb852f'),
  3: $t('marketing.commons.ywc_fad522'),
  4: $t('marketing.commons.fssb_9ca6a3'),
  5: $t('marketing.commons.dfs_57a3b6'),
  10: $t('marketing.commons.qxfs_b022e6'),
}

// whatsapp推广状态
const WHATSAPP_STATUS = {
  3: $t('marketing.commons.ywc_fad522'),
  4: $t('marketing.commons.fssb_9ca6a3'),
  5: $t('marketing.commons.dfs_57a3b6'),
  10: $t('marketing.commons.qxfs_b022e6'),
}

// Taro项目图片 CDN 路径
const $cdnPath = 'https://a2.fspage.com/FSR/weex/avatar/marketing_app'

const vibeMarketingWorkSpaceIconMap = {
  content: `${$cdnPath}/images/workspace-icons/6.png`,
  campaign: `${$cdnPath}/images/workspace-icons/11.png`,
  seo: `${$cdnPath}/images/workspace-icons/8.png`,
  ad: `${$cdnPath}/images/workspace-icons/5.png`,
  social: `${$cdnPath}/images/workspace-icons/12.png`,
}

export {
  AUDIT_STATUS,
  SPREAD_STATUS,
  QYWX_STATUS,
  QYWX_SPREAD_STATUS,
  SMS_STATUS,
  MAIL_STATUS,
  WX_STATUS,
  WHATSAPP_STATUS,
  $cdnPath,
  vibeMarketingWorkSpaceIconMap,
}
