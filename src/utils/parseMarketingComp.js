import util from '@/services/util/index.js'

/**
 * 检查组件列表中是否存在活动相关组件
 * @param {Array} components - 组件列表
 * @param {Object} [fieldNameMaps] - 字段名称映射对象，可选参数
 * @returns {boolean} - 是否存在活动组件
 * 
 * @example
 * const components = [
 *   { type: 'container', typeValue: 'activity-comp' },
 *   { fieldName: 'marketingEventTitle' }
 * ];
 * const hasActivity = hasActivityComponent(components);
 * // 返回: true
 */
export function hasActivityComponent(components, fieldNameMaps = {
  marketingEventCover: 'marketingEventCover',
  marketingEventTitle: 'marketingEventTitle',
  marketingEventTime: 'marketingEventTime',
  marketingEventDetail: 'marketingEventDetail',
}) {
  if (!Array.isArray(components)) return false

  return components.some(comp => {
    if (
      (comp.type === 'container' && comp.typeValue === 'activity-comp')
      || Object.keys(fieldNameMaps).includes(comp.fieldName)
    ) {
      return true
    }
    return comp.components ? hasActivityComponent(comp.components, fieldNameMaps) : false
  })
}

// 新增：主入口，兼容数组和对象
export function parseMarketingComp(marketingEventData, siteDataParam = {}) {
  let isArrayInput = false
  let siteData = siteDataParam

  // 如果直接传入的是组件数组，包一层对象
  if (Array.isArray(siteDataParam)) {
    isArrayInput = true
    siteData = { components: siteDataParam }
  }

  // 复用主逻辑
  const result = parseMarketingCompCore(marketingEventData, siteData)

  // 如果输入是数组，返回处理后的components，否则返回对象
  if (isArrayInput) {
    return result && result.components ? result.components : siteDataParam
  }
  return result
}

// 主逻辑迁移到这里
function parseMarketingCompCore(marketingEventData, siteDataParam = {}) {
  // 处理单个对象或数组的情况
  const isArray = Array.isArray(siteDataParam)
  const siteDataList = isArray ? siteDataParam : [siteDataParam]

  // 首先检查是否存在指定类型的组件
  const fieldNameMaps = {
    marketingEventCover: 'marketingEventCover',
    marketingEventTitle: 'marketingEventTitle',
    marketingEventTime: 'marketingEventTime',
    marketingEventDetail: 'marketingEventDetail',
    marketingEventAddress: 'marketingEventAddress',
  }

  // 格式化活动数据
  const activityData = marketingEventData
    ? {
      marketingEventTime: `${util.formatDateTime(
        marketingEventData.startTime,
        'YYYY-MM-DD hh:mm',
      )} - ${util.formatDateTime(marketingEventData.endTime, 'YYYY-MM-DD hh:mm')}`,
      marketingEventDetail: marketingEventData.description || '',
      marketingEventCover: marketingEventData.cover || '',
      marketingEventTitle: marketingEventData.name,
      marketingEventAddress: marketingEventData.location || '',
      isShowMap: marketingEventData.isShowMap || false
    }
    : null

  // 递归更新组件数据
  const processComponents = components => {
    if (!Array.isArray(components)) return components

    return components.map(item => {
      const newItem = { ...item }

      // 更新字段值
      if (newItem.fieldName && activityData) {
        // 有值才更新 没值也得更新
        newItem.value = activityData[fieldNameMaps[newItem.fieldName]] || '--'

        // 特殊处理封面图片
        if (newItem.fieldName === 'marketingEventCover') {
          if (!newItem.images) {
            newItem.images = [{}]
          }
          newItem.images[0].url = activityData[fieldNameMaps[newItem.fieldName]] || ''
        }
        // 处理地图
        if(newItem.fieldName === 'marketingEventAddress'){
          console.log('marketingEventAddress', activityData.marketingEventAddress)
          newItem.isShowMap = activityData.isShowMap
          newItem.value = activityData.marketingEventAddress
        }
      }

      // // 处理报名按钮组件
      // if (newItem.type === 'signupbutton' && newItem.typeValue === 'marketingEventSignupbutton' && activityData && activityData.url) {
      //   // 检查 schedule 是否存在且为对象
      //   if (newItem.schedule && typeof newItem.schedule === 'object') {
      //     // 遍历 schedule 中的每个键
      //     Object.keys(newItem.schedule).forEach(key => {
      //       const scheduleItem = newItem.schedule[key]

      //       // 处理 yes 选项
      //       if (scheduleItem && scheduleItem.yes && typeof scheduleItem.yes === 'object') {
      //         if (scheduleItem.yes.actionType === 'gotoLiveAddress' && scheduleItem.yes.action) {
      //           scheduleItem.yes.action.url = activityData.url
      //         }
      //       }

      //       // 处理 no 选项
      //       if (scheduleItem && scheduleItem.no && typeof scheduleItem.no === 'object') {
      //         if (scheduleItem.no.actionType === 'gotoLiveAddress' && scheduleItem.no.action) {
      //           scheduleItem.no.action.url = activityData.url
      //         }
      //       }
      //     })
      //   }
      // }

      // 递归处理子组件
      if (newItem.components) {
        newItem.components = processComponents(newItem.components)
      }

      return newItem
    })
  }

  // 检查对象是否包含字段映射中的任意一个键的值
  const hasAnyFieldNameValue = (data, fieldMaps) => {
    if (!data || typeof data !== 'object') return false

    return Object.values(fieldMaps).some(fieldName => data[fieldName] !== undefined && data[fieldName] !== null && data[fieldName] !== '')
  }

  // 处理每个 siteData
  const processedSiteDataList = siteDataList.map(siteData => {
    try {
      // 解析 content
      const contentObj = typeof siteData.content === 'string'
        ? JSON.parse(siteData.content)
        : siteData.content || siteData // 兼容直接传入{components:[]}的情况

      const comps = contentObj.components || []

      // 如果不存在指定组件或没有活动数据，直接返回原始数据
      if (!hasActivityComponent(comps, fieldNameMaps) || !Object.keys(activityData).length || !hasAnyFieldNameValue(activityData, fieldNameMaps)) {
        return siteData
      }

      // 处理组件
      contentObj.components = processComponents(comps)

      // 序列化 content
      return {
        ...siteData,
        content: contentObj.content !== undefined ? JSON.stringify(contentObj) : undefined,
        components: contentObj.components,
      }
    } catch (error) {
      return siteData
    }
  })

  // 返回与输入相同类型的数据
  return isArray ? processedSiteDataList : processedSiteDataList[0]
}


