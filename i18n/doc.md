### 国际化相关文档

* [国际化使用规范大纲](https://wiki.firstshare.cn/pages/viewpage.action?pageId=179248848)
* [国际化i18n平台](https://wiki.firstshare.cn/pages/viewpage.action?pageId=73107562)
* [国际化i18n接入方式](https://wiki.firstshare.cn/pages/viewpage.action?pageId=76627012)
* [大前端国际化规范](https://wiki.firstshare.cn/pages/viewpage.action?pageId=179247278)
* [主站前端多语言方案](https://wiki.firstshare.cn/pages/viewpage.action?pageId=73107951)
* [@tools/i18n插件](https://wiki.firstshare.cn/pages/viewpage.action?pageId=76618233)
* [多时区接入](https://wiki.firstshare.cn/pages/viewpage.action?pageId=179244105)
* [多货币接入](https://wiki.firstshare.cn/pages/viewpage.action?pageId=174785949)
* [多语词条平台](http://oss.firstshare.cn/i18n-console)

---

### 目录及文件说明
* differ key 的变更记录
* output 自动扫描生成中文文件
* resource 中文 key 资源文件
* scripts 脚本文件
* utils.js 工具函数集
* xlsx 导出的 excel 文件
* zh-CN.json 所有 key 文件
* custom.json 自定义 key 文件

---

### 适配国际化步骤，注：最好保证git提交干净，用一个单独的 commit 记录国际化的变更，便于在替换后比较文件变更
1. 按文件扫描未替换的中文
2. 更新 key 资源文件，并保存新增和修改的key
3. 根据 key 资源文件生成英文 key
4. 全局替换更新的 key
5. 将更新的 key 同步到多语言平台

---

### 相关命令说明，从上往下依次执行
* `npm run i18n-output` ==> 提取指定目录中的中文到 `output` 目录，可以先查看生成的文件，手动剔除一些不需要替换的词条，然后再执行下一步
* `npm run i18n-resource` ==> 根据指定中文文件（即上一步的产物）存档对应的中文资源文件，也可手动添加
* `npm run i18n-key` ==> 根据中文资源生成 `key` 文件，在 differ 目录整理了 `zh-CN.json` 文件的变更细节，这里注意是和上一次比较，如果要同上一次提交比较，需将 `zh-CN.json` 的更改回退，最好同 git 变更比较
* `npm run i18n-replace` ==> 替换指定目录中的中文，生成的替换后的资源在以 `i18n` 为后缀的文件夹中，可直接覆盖原文件，个别词条如：带变量、带中文标点符号、中英文结合的词条替换会有兼容性问题，需要开发者确认是否替换成功
* `npm run i18n-export` ==> 导出新增或更新的中文词条，原则上和 differ 中的更改保持一致，将最新的变更导出为 `temp.xlsx`, 可批量导入到国际化词条平台
* `npm run i18n-all` ==> 一条龙

---

### 自动替换相关
自动替换使用 `@gamma/scanner`，全局安装后执行 `npx @gamma/scanner` 具体参数如下：  
`'-c, --config-dir <config>', 'key映射文件'`  
`'-s, --src-dir <src>', '输入目录'`  
`'-o, --out-dir <out>', '输出目录'`  
`'-i, --ignore-files <ignore...>', '忽略文件'`   
`'-t, --ignore-tags <ignore...>', '忽略节点标签名称'`  
本地调试 -> `node ./node_modules/@gamma/scanner/bin/scan.js -d -c i18n/zh-CN.json -s src/pages/demo-i18n`
自动替换 -> `npx @gamma/scanner -d -c i18n/zh-CN.json -s src/pages/setting/channel`
---

### key 相关
目前 key 分为 commons、components、pages、others 新增时请将对应的中文写入对应 key 文件

key 的生成规则:  
key 统一自动生成，需执行 `npm run i18n-key` 命令，生成的格式有两种:  
* 常规：`marketing.{当前目录catalog}.{当前文件的根目录[可能没有]}.{key的拼音首字母最多10个}_{当前key的md5戳6位}`
> eg:  
`我的世界 ==> marketing.commons.wdsj_575d06`  
`我的世界 ==> marketing.pages.MyWorld.wdsj_575d06`  
* 无中文: `marketing.{当前目录catalog}.{当前文件的根目录[可能没有]}&{当前key的md5戳6位}`
> eg:  
`my world 😊😊 ==> marketing.commons.&90de79`  
`my world 😊😊 ==> marketing.pages.MyWorld.&90de79`

---

### 替换失败场景及对应搜索正则

* `$t('undefined` ==> `\$t\('undefined`
* `$t('null` ==> `\$t\('null`
* `$t('中文`   ==> `\$t\('[\u40ee-\u9fa5]`
* `$t('{非字母` ==> `\$t\('[\W]`
* `$t('hello')任意字符$t('hello')` ==> `\$t\([^:,]+\$t\(`

---

### 快速替换
* `\$t\('undefined.*\{'0': (.*)\}\)` ==> `$t('null', {data: ({'option0':$1})})`
* `\$t\('(.*)\{0\}(.*)',(.*)\)` ==> `$t('$1{{option0}}$2', {data: ($3)})`
* `\$t\('(.*)',\s*\{data(.*)'0'(.*)\)` ==> `$t('$1', {data$2'option0'$3)`

---

### 内含变量词条替换规则
* {{ $t('null', {data: ({'option0': selectedCount})}) }}
* $t('null', {data: ({'option0': selectedCount})})

---

### 待替换 TODO list 

全局搜索 TODO i18n

---

### 相关 repo 多语适配
- fs-page-designer (采用和 marketing 项目相同的方案实现，英文 key 前缀为 marketing_pd) ✅
- bpm
- marketing-fullcalendar (该项目的 key 在 marketing) ✅
- marketing-ui (采用组件库自身的多语方案实现，引用相关组件需从 marketing-ui 导入，否则不支持) ✅


``` js
// 查询多语言 key 在indexdb 中是否存在，默认为中文，如果需要查询其他语言，需要传入对应的 storeName
function queryIndexedDBByExactKeyAndFieldPrefix(dbName, storeName, targetKey, fieldInValue) {
  const openRequest = indexedDB.open(dbName);

  openRequest.onsuccess = function(event) {
    const db = event.target.result;
    const transaction = db.transaction(storeName, "readonly");
    const store = transaction.objectStore(storeName);

    const getRequest = store.get(targetKey);

    getRequest.onsuccess = function(event) {
      const value = event.target.result;

      if (
        value &&
        typeof value === "object" &&
        value[fieldInValue]
      ) {
        console.log(`✅ 词条 "${fieldInValue}" 对应的值为 "${value[fieldInValue]}" `);
      } else {
        console.log(`❌ 不存在词条 "${fieldInValue}" 的记录内容`);
      }
    };

    getRequest.onerror = function(event) {
      console.error("获取指定 key 失败", event);
    };
  };

  openRequest.onerror = function(event) {
    console.error("数据库打开失败", event);
  };
}


queryIndexedDBByExactKeyAndFieldPrefix('__Fx___88146_1000', '__Fx__', 'i18n-zh-CN', 'marketing.components.ActiveMemberTable.hfsz_572a11')
```