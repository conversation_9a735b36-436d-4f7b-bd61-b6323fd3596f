{"pages.Coupons.CommonCreate.components.EffectiveTime": ["指定时间", "选择时间范围"], "pages.Coupons.CommonCreate.components.HiddenSettings": ["隐藏优惠券的推广链接", "隐藏后优惠券无“推广链接”，但可用于其他营销活动的推广"], "pages.Coupons.CommonCreate.components.OverdueReminder": ["优惠券过期前", "天提醒", "过期提醒需授权公众号，未授权商家无法推送消息", "查看说明", "根据", "《微信公众号平台运营规范》", "，频繁发送营销类模版消息存在被封禁的风险"], "pages.Coupons.CommonCreate.components.PreferentialContent": ["减免", "打", "单品换购价"], "pages.Coupons.CommonCreate.components.ReceiveCountLimit": ["不限次数", "每人限领次数对用户主动领取时生效，部分活动不生效", "最大值为99", "单店内所有用户共享该领取次数限额，单店领取次数达到上限时，其所有用户将无法继续领券"], "pages.Coupons.CommonCreate.components.ReceiverLimit": ["不限制，所有人可领取", "仅会员可领取", "仅伙伴可领取"], "pages.Coupons.CommonCreate.components.Scene": ["商家制券，仅会员可领取，可按照不同的推广渠道创建不同的批次，便于溯源", "上游制券，通过伙伴营销发放，下游可在订货通内核销", "厂家制券，通过伙伴营销下发给经销商，门店可在经销商的订货通内领取并核销"], "pages.Coupons.CommonCreate.components.Tags": ["给领取优惠券的用户添加标签"], "pages.Coupons.CommonCreate.components.UseThreshold": ["无使用门槛", "订单满"], "pages.Coupons.CommonCreate.components.VoucherTime": ["领券当日起", "领券次日起", "若设置固定用券时间，编辑保存后对已领取未使用及后续领取的券均生效。若设置领券当日次日n天可用，编辑保存后仅对后续领取的券生效"], "pages.Coupons.CommonCreate.components.isParticipate": ["经销商可自行决定是否参与活动", "指定范围经销商默认参与活动"], "pages.Coupons.CommonCreate.index": ["优惠券预览"], "pages.Coupons.CouponsTemplate.Create.TypeSelectorDialog": ["选择微信商家券类型"], "pages.Coupons.CouponsTemplate.Create.index": ["优惠券详情获取失败，{{option0}}", "请输入适⽤商品范围", "适⽤商品范围"], "pages.Coupons.CouponsTemplate.Table.config": ["优惠券⽅案名称"], "pages.Coupons.CouponsTemplate.index": ["新建微信商家券⽅案", "创建微信商家券需要先完成微信支付设置", "创建微信商家券需要先完善微信支付设置", "确定删除优惠券⽅案？"], "pages.Coupons.MarketingCoupons.Create.CouponsTypeSelector": ["选择优惠券方案类型", "纷享优惠券是纷享销客为上下游及终端门店提供的电子优惠券解决方案。", "请选择纷享优惠券方案", "微信商家券", "微信支付商家券是微信支付为商户提供的电子优惠券解决方案。", "请选择微信商家券模板", "请选择优惠券方案"], "pages.Coupons.MarketingCoupons.Create.const": ["优惠券基本信息", "用券时间", "生效时间", "经销商是否可选择性参与"], "pages.Coupons.MarketingCoupons.FxCreate.const": ["是否选择性参与", "单店限领次数"], "pages.Coupons.MarketingCoupons.MarketingCouponsTable.config": ["批次号", "方案类型", "下发状态", "剩余", "下发", "未下发", "已下发", "导入中"], "pages.Coupons.MarketingCoupons.MarketingCouponsTable.index": ["优惠券批次列表拉取失败：", "确定下发？", "下发成功！"], "pages.Coupons.components.ActivityCouponsSideslip": ["领取会员列表", "领取下游列表", "经销商参与情况", "门店领取情况", "存入对象"], "pages.Coupons.components.CouponSimpleCard": ["立减"], "pages.Coupons.components.CouponsTemplateSidslip": ["总批次数", "总领取数", "批次列表", "发送总量", "伙伴渠道", "全渠道使用"], "pages.Coupons.components.couponSideslipHeader": ["使用场景：", "下载导入结果", "存在导入失败的客户名单，请下载查看"], "pages.Coupons.components.coupons_preview_dialog": ["优惠券模板预览"], "pages.Coupons.components.coupons_preview": ["立即获取", "优惠内容：", "可用；"], "pages.File.FileList": ["标签添加失败，请重试", "标签添加成功", "文件设置分组成功"], "pages.File.FileListTable.config": ["用户查看/下载资料标签"], "pages.File.FileUpload": ["公司网盘", "单次最多只能上传", "个文件，当前选中", "文件上传中，请稍等...", "文件上传成功", "以下文件上传失败，请重试：", "文件上传失败，请重试", "上传的文件格式不符合，请重新选择", "上传的文件大小不能大于"], "pages.Picture.PictureList": ["图片名不能为空", "图片名最多为30个字", "图片设置分组成功", "请先选择图片", "图片已删除"], "pages.Picture.PictureListGrid": ["移动分组"], "pages.Picture.PictureUpload": ["在线制作", "上传最大图片数不能大于9", "创客贴返回数据异常，请重试", "图片上传中，请稍后...", "图片上传成功", "图片上传失败，请重试", "上传的图片格式不符合，请重新选择", "上传的图片大小不能大于"], "pages.Picture.index": ["图片选择器", "这里是143行"], "pages.TargetPopulation.TargetPopulationAddByFilterDialog": ["按对象条件新增"], "pages.TargetPopulation.TargetPopulationConfig": ["按照打在用户上的标签对平台内所有的营销用户进行筛选，适用于筛选指定画像的用户，或者圈选出指定行为标签的用户，如访问网站，访问某个活动页，参加某场会议的人员", "按业务对象条件筛选", "按业务对象", "按照属性对已存入CRM的线索，客户或联系人进行筛选，适用于对可触达的线索或客户进行跟进及培育的场景", "按企业库筛选", "按企业库", "以企业的视角对目标客户进行筛选，适用于目录营销及ABM营销场景", "按公众号粉丝筛选", "按公众号粉丝", "可对公众号粉丝按标签或属性筛选，适合对公众号粉丝分群做差异化运营的场景", "按企业微信客户筛选", "按企业微信客户", "可以筛选全部的企业微信客户，或者指定员工负责的企业微信客户，或者某些微信群组里面的客户", "基于客户对象的字段条件筛选关联联系人及线索数据，适用于基于客户对象的ABM营销", "基于客户对象的字段条件筛选关联联系人、线索、企业微信客户数据，适用于基于客户对象的ABM营销", "按用户行为筛选", "基于用户全渠道的互动行为条件进行筛选，如筛选访问过具体哪个产品页面、观看具体哪个直播30分钟的用户"], "pages.TargetPopulation.TargetPopulationCreate.TargetPopulationCreateChooseType": ["不需要指定创建人群的规则，可自由将用户加入或移出该群组，适用于对群组成员有精细化控制的场景", "需要指定创建人群的规则后", "将满足规则的用户加入人群。每天定时根据规则进行重算，动态更新人群", "可用配额数", "您的可用配额不足，可删除部分动态人群后再新建，或联系客户经理购买配额。", "不需要指定创建人群的规则，创建完成后点击进入人群详情可以按条件将用户加入或移出该人群，适用于对群组成员有精细化控制的场景，单个人群最大人数限制为10W，按条件计算后超过10W的人群，系统将自动新建多个人群，最多自动新建10个", "将满足规则的用户加入人群，每天凌晨定时根据规则进行重算，动态更新人群，也可以手动实时更新。单个人群最大人数限制为10W，按条件计算后超过10W的人群，考虑计算时间，系统仅取前10W条数据", "已用配额数："], "pages.TargetPopulation.TargetPopulationCreate.TargetPopulationCreateSetDetail": ["重算方式", "请输入人群名称", "请填写群组描述", "管理员开启了多组织数据权限隔离，您仅能圈选以下组织数据：", "用户行为"], "pages.TargetPopulation.TargetPopulationCreate.index": ["确认提交", "选择人群类型", "选择筛选方式", "设置详细信息", "获取动态人群可用配额失败"], "pages.TargetPopulation.TargetPopulationDetail": ["人群计算中，请耐心等候"], "pages.TargetPopulation.TargetPopulationDetailBody": ["人群详情", "推广历史", "操作日志"], "pages.TargetPopulation.TargetPopulationDetailBodyHistory": ["暂无推广历史"], "pages.TargetPopulation.TargetPopulationDetailBodyLog": ["暂无操作日志"], "pages.TargetPopulation.TargetPopulationDetailBodyUser": ["暂无用户，您可以通过以下方式添加", "人群数据更新执行中...", "用户筛选条件", "用户分析", "用户特征分布", "大数据人群:", "用户列表"], "pages.TargetPopulation.TargetPopulationDetailBodyUserAnalysis": ["用户总数", "渠道分布", "业务阶段分布"], "pages.TargetPopulation.TargetPopulationDetailBodyUserFilter": ["查询范围：", "客户数据", "线索数据", "联系人数据", "企微微信客户数据"], "pages.TargetPopulation.TargetPopulationDetailBodyUserTable": ["是否移除", "批量移除成功"], "pages.TargetPopulation.TargetPopulationDetailHeader": ["最近更新时间：", "关联运营计划：", "执行手动更新成功", "是否要", "此人群", "是否要删除此人群？", "目标人群设置分组成功"], "pages.TargetPopulation.TargetPopulationDetailKanban": ["看板创建成功"], "pages.TargetPopulation.TargetPopulationListItem": ["人群更新时间：", "人群用户总数"], "pages.TargetPopulation.index": ["支持基于用户或客户属性进行细分，针对性的制定营销策略进行精准营销", "搜索群组名称", "新建目标人群", "暂无目标人群"], "pages.UserPicture.UserPictureConfig": ["用户标签管理", "构建企业自己的标签及画像体系，全方位了解客户的特征和偏好，赋能业务实现用户精细化运营和精准营销。", "行为积分规则设置", "对用户主动的互动行为（如关注公众号、访问文章等）按指定规则进行积分，最终辅助判断潜在客户的意向度，从而更好的安排线索的跟进策略和优先级。", "线索打分规则设置", "根据企业需求预先设定一套评分规则，根据规则进行打分，打分的目的主要是区分等级，辨别质量好坏。可实现对线索多维度打分、商机赢率自动评估、客户等级的灵活评定，以及其他多种自定义业务的打分评级，自动的优化系统的业务数据，进行精准销售预测和资源调用，最终达到自动营销的目的。"], "pages.Webhook.CallLog.Table.config": ["调用时间"], "pages.Webhook.CallLogDetail.index": ["日志", "调用状态", "日志详情"], "pages.Webhook.CreateWebhook.components.VAuthType": ["请输入密钥", "请输入用户名", "请输入AccessToken获取地址"], "pages.Webhook.CreateWebhook.components.VMessageBody": ["请先选择应用场景"], "pages.Webhook.CreateWebhook.config": ["请输入Webhook名称", "Webhook地址", "请输入消息接收地址", "请输入所有鉴权配置", "消息类型", "消息体", "自动重试"], "pages.Webhook.Table.config": ["Webhook名称", "调用次数"], "pages.Webhook.index": ["新建 Webhook", "确定要"], "pages.ad_marketing.ad_baidu.ad_passback.list": ["重新回传"], "pages.ad_marketing.ad_baidu.ad_passback.passback_rule": ["设置好广告回传规则后，请前往百度设置ocpc策略。", "设置回传规则（转换类型：", "系统初始化已为您预设了一套规则，您可以根据自己的业务需求，对回传规则进行调整，设置完成后实时生效，并且会直接影响回传数据量，请谨慎操作。", "满足下述条件时，触发线索数据回传，上报“", "线索转换", "（线索或线索关联的客户，发生转换时，触发数据回传）", "转客户", "转联系人", "转商机2.0", "转化类型", "一条线索的转化会依次经历电话接通、信息确认、发现意向、高潜成交、成单客户五个阶段。对应的转化类型可在百度ocpc中设置为转化目标", "类型含义", "回访-电话接通", "用户留资号码为真实、可拨通的号码", "回访-信息确认", "经过线索清洗，确认是本人留资、信息正确，后续可进一步跟进", "修改销售线索.线索阶段等于市场认可线索（MQL）", "回访-发现意向", "留资者直接或间接表示有意向或购买需求", "修改销售线索.线索阶段等于销售认可线索（SQL）,线索转客户", "线索转客户", "回访-高潜成交", "意向用户进入合同洽谈或购买决策阶段，有较大概率成单", "修改销售线索.线索阶段等于销售认可线索（SQL）,线索转客户、商机,修改报价单.报价单金额大于0元", "回访-成单客户", "用户提交订单，或者完成合同签订、支付定金、付清尾款", "修改销售订单.销售订单金额大于0元", "商机2.0", "合同", "查看回传效果", "广告回传效果", "若未选择广告计划（市场活动），则统计广告回传前6个月所有广告计划的目标转化成本，以及最近6个月关联了ocpc投放策略的广告计划的目标转化成本。", "广告开始回传", "回传前平均目标转化成本:", "回传后平均目标转化成本："], "pages.ad_marketing.ad_baidu.components.adgroup_table": ["广告推广计划表", "导入线索", "未关联", "广告推广单元列表", "历史数据", "从2024年10月30日起，营销通内百度广告数据将统一按推广单元维度进行统计和展示。如需查看以前按推广计划维度统计的数据，请访问旧版数据。"], "pages.ad_marketing.ad_baidu.components.bind": ["百度广告账户绑定", "绑定成功"], "pages.ad_marketing.ad_baidu.components.table_data": ["计划预算", "推广设备", "广告花费", "关联状态", "推广计划暂停推广", "未审核", "生效", "直播结束后暂停", "转化追踪异常暂停", "基木鱼落地页暂停", "所属项目"], "pages.ad_marketing.ad_baidu.index": ["广告回传明细"], "pages.ad_marketing.ad_baidu.init.guide": ["立即绑定百度账号", "请确认已完成百度广告账户授权？", "连接百度推广，获取百度搜索广告计划、关键词投放效果", "全链路数据接入", "广告计划、关键词投放展现、点击、消费", "官网注册留资线索", "百度基木鱼留资、咨询线索", "百度爱番番咨询线索"], "pages.ad_marketing.ad_baidu.overview.index": ["推广账户"], "pages.ad_marketing.ad_baidu.setting.baidu_ad": ["token设置", "百度广告ocpc投放配置全流程请查看", "第1步：", "填写百度API传回token后，点击生成并复制落地页，前往百度联调窗口粘贴百度API回传token。", "生成落地页", "联调落地页地址：", "（复制该地址百度联调窗口粘贴）", "第2步：", "百度APP扫码打开落地页，选择回传转化类型，点击联调测试，稍等10s左右，前往百度联调窗口点击下一步，联调成功后完成联调测试。", "广告账户名称", "百度API回传token", "联调落地页URL", "启用回传", "请先前往百度联调窗口粘贴百度API回传token", "生成成功！", "停用后将停止广告账户上的所有效果数据回传，是否确认停用", "是否开启该广告账户的效果数据回传", "更新状态成功", "启用无效返款", "停用后将停止该广告账户上线索的外呼无效返款功能，是否确认停用？", "是否开启该广告账户上线索的外呼无效返款功能？"], "pages.ad_marketing.ad_baidu.setting.index": ["广告效果数据回传token配置"], "pages.ad_marketing.ad_bytedance.campaign.index": ["广告计划列表", "投放状态", "操作状态", "投放范围", "广告位大类", "广告投放位置"], "pages.ad_marketing.ad_bytedance.components.adgroup_table": ["广告组列表", "包含已删除", "不包含已删除", "超出广告主日预算", "暂停", "预算", "预算类型", "推广目的", "营销目的", "投放类型", "由于巨量本地推广告平台限制，仅展示数据不为0的广告和项目。将于每日凌晨更新前一天的数据。"], "pages.ad_marketing.ad_bytedance.components.guide": ["立即授权广告账户", "授权巨量引擎账号，获取头条、抖音展示广告组、广告投放效果", "多平台数据接入", "头条、抖音展示广告组、广告计划投放展现、点击、消费", "飞鱼线索：广告表单提交、在线咨询、电话等"], "pages.ad_marketing.ad_tencent.bind_mixin": ["请确认已完成腾讯广告账户授权"], "pages.ad_marketing.ad_tencent.guide": ["未配置", "第一步：绑定广告账户", "前往腾讯广告平台选择要授权绑定的广告账户", "第二步：映射配置", "腾讯广告到CRM市场活动的映射配置", "腾讯线索管理平台线索（关联腾讯广告）到CRM销售线索的映射配置", "第三步：同步线索数据", "同步腾讯广告和线索数据", "完成映射配置和广告账户绑定后，可同步腾讯广告和线索数据。首次同步数据需要等待较长时间，后续每天将会自动更新", "请先完成前面步骤", "是否确定按照当前映射配置同步腾讯历史数据？", "三步连接腾讯广告，获取广告投放效果", "接入腾讯广告顺户，监控厂告投放效果：消费、展现，点击、线索产出，对广告投放进行端到端的投放ROI分析"], "pages.ad_marketing.ad_tencent.index": ["推广计划名称", "一小时只能手动刷新一次"], "pages.ad_marketing.ad_tencent.setting.account": ["广告账户绑定管理", "授权同步数据", "确定启用", "确定停用", "停用成功", "停用失败", "请先完成映射配置", "当前数据正在同步中", "是否确定按照当前映射配置同步腾讯广告历史数据？"], "pages.ad_marketing.ad_tencent.setting.index": ["腾讯广告设置", "映射管理"], "pages.ad_marketing.ad_tencent.setting.mapping": ["腾讯广告推广计划同步到CRM市场活动的映射配置"], "pages.ad_marketing.ad_tencent.table.const": ["出价方式", "广告出价（元）", "广告版位", "广告日预算（元）", "曝光量（次）", "花费（元）", "点击均价（元）"], "pages.ad_marketing.ad_tencent.table.index": ["广告列表", "冻结", "暂停中", "未到投放时间", "投放中", "投放结束", "准备中", "广告被暂停（账户资金被冻结）", "广告被暂停（账户余额不足）", "广告被暂停（账户达日限额）", "广告被暂停（推广计划达日限额）", "广告被暂停（推广计划暂停）", "广告被暂停（广告达日限额）", "部分待投放", "部分投放中", "未知状态", "投放结束（直播结束）", "创意未投放", "创意准备中", "待锁定预算", "解锁预算中", "广告达到日预算上限", "广告被暂停（联合预算达上限）", "广告达到总预算上限"], "pages.ad_marketing.bind_account.index": ["您还需手动初始化同步数据，请点击以下按钮同步百度推广计划"], "pages.ad_marketing.bind_account.steps": ["账号授权", "数据同步"], "pages.ad_marketing.components.account_list": ["账户ID："], "pages.ad_marketing.components.ad_header": ["广告推广计划名称："], "pages.ad_marketing.components.clue_pie": ["线索总数"], "pages.ad_marketing.components.clue_source": ["线索统计"], "pages.ad_marketing.components.landing_list": ["类型：H5页面"], "pages.ad_marketing.components.landing": ["新增落地页"], "pages.ad_marketing.const": ["未同步"], "pages.application.companyfeeds.comment.index": ["动态评论"], "pages.application.companyfeeds.feeds.create_feed_dialog.feed_form": ["请输入推荐语", "拖拽调整顺序，最多添加9张", "推荐语字数不能超过500。"], "pages.application.companyfeeds.feeds.create_feed_dialog.index": ["新建公司动态", "请", "公司动态发布成功。", "公司动态发布失败，请重新发布。"], "pages.application.companyfeeds.feeds.feed_item.index": ["确认删除此动态吗？", "确认删除此评论吗？", "删除评论成功"], "pages.application.companyfeeds.feeds.index": ["企业发布的动态将同步到名片空间的动态", "；可以设置公司动态显示的logo和企业简称", "没有更多了"], "pages.article.const": ["自建"], "pages.article.create.index": ["文章链接地址", "保存文章", "文章已存在，不可重复添加"], "pages.article.create.pa_dialog": ["粘贴企业自有微信公众号推文链接", "可快速获得企业已有公众号推文中的标题、封面及图片内容（不包含投票、视频、小程序卡片等特殊内容）", "如何获取企业已有公众号推文链接？"], "pages.article.edit.index": ["（实际效果建议手机预览）", "原文地址", "请输入公众号", "作者不能为空", "文章内容", "给浏览该内容的用户添加标签", "相关内容", "在文章中关联其他相关内容", "展示形式", "请输入展示名称", "展示名称不能为空", "具体内容", "选择物料", "请选择一个物料", "底部平铺（仅表单）", "右侧悬浮按钮", "底部悬浮按钮", "版本号格式错误：", "编辑文章", "检测到本地存在上次未保存的内容，是否使用？", "此链接中可能有视频内容，由于公众号限制，原链接中视频无法直接使用，您可以上传视频到营销通后重新插入视频。"], "pages.article.edit.preview.article": ["分享"], "pages.article.edit.public_account_article": ["在文章底部添加表单", "请选择一个表单", "表单显示", "请输入底部按钮名称", "底部按钮文案不能为空", "底部平铺", "悬浮按钮", "编辑公众号文章", "新建公众号文章"], "pages.article.table.index": ["更换成功", "更换失败，请重试"], "pages.content_marketing.content_dashboard.coupons": ["优惠券批次列表", "新建优惠券批次"], "pages.content_marketing.content_dashboard.module.channel_rank": ["渠道获取线索排行", "访问用户数"], "pages.content_marketing.content_dashboard.module.content_module": ["还没有营销内容，您可以选择一个模版快速创建", "标准获客文章"], "pages.content_marketing.content_dashboard.module.copy_site_dialog": ["复制到当前活动", "复制为模板", "名称，限100字", "微页面模板", "复制微页面将同时复制此页面的全部设置项，请注意检查更新。", "复制微页面将同时复制此页面的全部设置项，请注意检查更新。复制完成后，可在新建微页面时选择模版快速制作微页面。", "名称不能超过100字"], "pages.content_marketing.content_dashboard.module.customer_distribute": ["新老客户分布", "新客户指报名当前活动且存入CRM成功线索，若在CRM中配置了线索查重规则，客户提交的表单信息满足重复条件时不会新建线索，仅会关联CRM中历史数据，关联的提交信息则作为老客户，未重复的则直接存入线索为新客户。可以在参会人员列表中通过线索存入状态区分新老客户，已关联则为老客户，已存入为新客户。", "老客户", "新客户", "新老客户分布图", "参与客户数", "如果参与活动的用户与当前CRM中的对象数据（销售线索、客户、联系人、会员）没有相同的手机号或工商信息，则被视为新客户；否则被视为老客户。"], "pages.content_marketing.content_dashboard.module.data_info": ["访问所有推广内容的人数（去重）", "推广内容收集的表单提交总数（去重）"], "pages.content_marketing.content_dashboard.module.detail_sideslip": ["市场内容已有推广数据，无法进行移除", "设为通用微页面后，当前内容会同时展示在内容中心-微页面，可被其他活动场景添加选用、编辑和修改，但活动下获得的表单明细不会共享仍只可在对应市场活动下可见", "设为通用微页面", "总提交数"], "pages.content_marketing.content_dashboard.module.employee_rank": ["线索榜", "企微客户榜", "粉丝榜", "粉丝", "获取企微客户数", "获取粉丝数"], "pages.content_marketing.content_dashboard.module.guidance": ["创建推广内容", "选择模板或者自定义创建推广内容", "线上渠道推广", "企业推广：公众号推送、短信推送、全员转发", "二维码：H5二维码、微信小程序码、百度小程序码", "效果跟踪"], "pages.content_marketing.content_dashboard.module.header": ["预期成本：", "实际成本："], "pages.content_marketing.content_dashboard.module.material_preview": ["文章预览", "优惠券推广", "外部内容预览", "内容预览"], "pages.content_marketing.content_dashboard.module.poster_preview": ["海报侧滑页"], "pages.content_marketing.content_dashboard.module.preview_sideslip": ["社交媒体广告投放", "将活动内容的二维码或链接投放在其他社交媒体广告，整合活动效果", "推广链接", "长链接永久有效，短链接一年有效期", "长链接"], "pages.content_marketing.content_dashboard.module.spread_content": ["微页面设置出错，无法录入数据", "物料详情"], "pages.content_marketing.content_dashboard.module.wechart_analysis": ["微信数据统计", "新增公众号粉丝", "新增企微客户"], "pages.content_marketing.content_dashboard.multiEvent": ["新建子活动", "解除绑定", "报名人次", "签到人次", "观看人次", "回放人次", "互动人次", "新建市场活动-子活动", "确定解除绑定？"], "pages.content_marketing.empty": ["以高价值内容主动吸引客户，提升线索转化", "立即新建活动营销", "了解更多详情", "活动营销策略", "根据目标用户制定策略", "制作创意内容", "H5活动页", "白皮书", "多渠道分发内容", "线下投放", "流量和线索转化", "内容评估和优化", "B对比，优化策略", "基于目标用户场景设计的营销内容活动，持续获客", "系列型、大型活动一站式管理", "制定系列、多会场活动计划", "统一内容制作", "实时跟踪各活动报名情况", "‘整体ROI评估", "追踪线索转化结果，评估整体活动价值", "立即新建多活动营销", "立即新建线上营销"], "pages.content_marketing.index": ["日常推广活动", "签约喜报、白皮书发布、优惠促销推送、客户定期关怀等常用的线上活动", "线上线下多会场活动", "产品发布会、大型展会、企业年会等大型会议场景支持主分会场，并同时支持可线上直播", "搜索活动名称", "计划中", "已终止"], "pages.data_cockpit.index": ["数据驾驶舱", "数据驾驶舱ID不能为空"], "pages.dev_demo.index": ["开发的demo页面", "1前往物料创建成功页"], "pages.external_content.components.external_content_create": ["编辑外部内容", "新建外部内容", "输入内容名称", "外部内容链接", "分享图片", "建议尺寸500*500像素，支持png、jpeg、jpg格式，最大不能超过1M", "请填写分享内容", "请输入外部内容链接", "当前链接支持嵌入", "当前链接不支持嵌入", "给访问外部内容的用户添加标签"], "pages.forbidden.index": ["无此功能操作权限"], "pages.form.FormDesigner": ["未命名表单", "请编辑表单内容"], "pages.form.create.const": ["选择表单模板", "设置表单内容", "表单完成提示"], "pages.form.create.finishtip.follow_actions": ["请填写提交成功标题", "请填写提交成功提示语"], "pages.form.create.finishtip.index": ["提交成功标题"], "pages.form.create.index": ["保存失败，请稍后重试！", "您也可以通过表单链接或表单二维码，在外部渠道获取线索"], "pages.form.create.otherinfo.crm_set": ["将报名表单数据存入到线索"], "pages.form.create.smartform.fields.index": ["内容组件", "常规", "扩展", "表单最多可添加200个字段，请删除部分字段后再添加"], "pages.form.create.smartform.fieldset.index": ["添加选项"], "pages.form.create.success_result.index": ["表单创建成功！", "您可以将表单应用在：", "立即前往获客文章，添加表单获客"], "pages.form.create.template.index": ["预览效果", "请输入提交按钮"], "pages.form.create.template.info.index": ["表头内容设置", "表单头部背景", "最佳尺寸：1125px*480px，大小不超过1MB"], "pages.form.form_copy_dialog.index": ["请输入表单名称，限30字", "表单名称不能超过30字"], "pages.form.index": ["表单名称不能重名", "个会议】"], "pages.horn.create.drafts": ["是否存为草稿箱"], "pages.horn.create.index": ["请输入宽度", "图片编辑", "是否需要暂存您的内容？", "离开", "编辑公告", "新建公告", "公告标题", "添加公告标题", "公告内容"], "pages.horn.detail.index": ["纷享营销通团队"], "pages.horn.history.index": ["删除该公告, 是否继续？", "取消该公告置顶, 是否继续？", "取消失败"], "pages.index.datas": ["市场活动数", "企业推广数", "免费试用30天，快来报名吧！", "需求定制", "专业定制企业全营销方案", "免费试听", "免费试听30天，不好不要钱", "全国大会", "专业定制全国大会方案", "关于营销通你都知道多少", "快速实现批量营销获客的正确“开启方式”", "产品试用申请--零成本高效获取潜在客户", "展现（次）", "点击均价（次）", "广告消耗（元）"], "pages.index.growth_class": ["增长学堂", "如何开展一场线上活动，推广获客"], "pages.index.index": ["获客增长，从一个运营计划开始"], "pages.index.marketing_case": ["营销案例", "营销案例学习"], "pages.index.marketing_invoke": ["推广内容投放广告渠道", "将报名二维码、海报、链接投放到社交媒体", "会议二维码", "报名链接", "通过员工邀约客户或进行转发传播", "企业微信推送", "向企业微信客户推送营销内容、邀请参加活动", "向公众号用户群发营销内容、邀请参与活动", "短信推送", "向目标客户人群推送营销内容、邀请参与活动", "邮件推送"], "pages.index.marketing_news": ["还没有动态，立即去发布一条动态吧", "公司动态将会出现在所有员工微信名片的动态中"], "pages.index.marketing_report": ["营销简报"], "pages.index.marketing_tips": ["营销小贴士"], "pages.index.rank_activity": ["活动推广排行榜", "暂无推广排行，快去推广看看哪次推广更有效吧"], "pages.index.rank_employee": ["员工推广排行榜", "暂无员工推广，快去给员工下发一个推广任务吧"], "pages.invitation.create.const": ["邀请函详情"], "pages.invitation.create.detailinfo.index": ["邀请函表单不更修改，与会议详情表单保持一致。"], "pages.invitation.create.index": ["编辑邀请函"], "pages.invitation.create.step_complete": ["邀请函编辑成功", "邀请函创建成功"], "pages.invitation.create.template.info.index": ["会议地址"], "pages.invitation.create.old.baseinfo.index": ["活动人数太多"], "pages.invitation.create.old.const": ["填写活动基本信息"], "pages.invitation.create.old.index": ["编辑活动", "MM月DD日"], "pages.invitation.create.old.otherinfo.index": ["浏览公司产品", "浏览公司官网", "请输入官方网站链接", "请输入官方网站", "请输入正确的官方网站"], "pages.invitation.create.old.smartform.description": ["单行文本一", "单行文本二", "单行文本三", "单行文本四"], "pages.invitation.create.old.smartform.fields.index": ["添加字段"], "pages.invitation.create.old.smartform.fieldset.index": ["高级设置", "开启表单数据自动同步至CRM线索池"], "pages.invitation.create.old.smartform.index": ["请输入表单标题", "请输入表单欢迎语", "请设置CRM线索映射"], "pages.invitation.datas": ["活动规模(人数)", "该活动已存在报名数据，{{option0}}", "即将开始", "推广数", "只能删除已停用的项", "该活动已存在报名数据，继续编辑将会对现有报名数据影响。", "是否继续编辑？", "活动已过期", "您需要先启用该活动，才能进行推广"], "pages.invitation.enrolls.datas": ["序号", "报名时间", "标记为未签到", "标记为已签到"], "pages.invitation.enrolls.index": ["推广人：", "签到状态更换成功"], "pages.invitation.promotion_channel": ["推广通知", "您可以通过推广通知给公司员工下发活动邀请通知", "立即新建推广通知", "服务号群发消息", "您可以通过服务号群发消息给粉丝们发送活动邀请，每次将消耗1次群发消息", "立即新建群发消息（建设中，敬请期待）"], "pages.kanban.components.board_item.index": ["工作项数："], "pages.kanban.components.choose_template_dialog.EnterpriseTemplateCreateDialog": ["模板封面", "请上传模板封面", "模板编辑成功", "模板添加成功"], "pages.kanban.components.create_kanban_dialog.index": ["看板名称", "请选择公开类型", "请选择成员范围"], "pages.kanban.components.detail.associated_object.index": ["选择关联类型", "选择目标对象", "已加载全部数据"], "pages.kanban.components.detail.editor.index": ["点击即可编辑"], "pages.kanban.components.detail.index": ["删除工作项", "请填写卡片名", "填写您的备注信息", "目标完成情况", "任务 （", "确定删除工作项？"], "pages.kanban.components.detail.replyinput.index": ["发送评论", "请输入评论内容"], "pages.kanban.components.detail.status.index": ["选择状态"], "pages.kanban.components.detail.target.index": ["选择目标类型", "请填写目标值"], "pages.kanban.components.detail.task.executor": ["执行人"], "pages.kanban.components.detail.task.index": ["删除任务", "请输入任务内容", "添加任务"], "pages.kanban.components.kanban_collection.add_kanban_list": ["输入列表标题"], "pages.kanban.components.kanban_collection.kanban_list.add_kanban_card": ["输入工作项标题"], "pages.kanban.components.kanban_collection.kanban_list.index": ["删除列表"], "pages.kanban.components.kanban_overview.config": ["人群成员数"], "pages.kanban.components.kanban_overview.index": ["目标", "人群"], "pages.kanban.components.kanban_title.index": ["模板:"], "pages.kanban.components.member_selector.index": ["更新看板成员", "看板成员更新成功", "看板成员更新失败，请检查网络"], "pages.kanban.components.public_selector.index": ["所有人都可以查看和编辑公开的看板，是否继续把这个看板设为公开？", "只有看板成员才能查看和编辑非公开的看板，是否继续把这个看板设为非公开？", "修改公开类型失败，请检查网络"], "pages.kanban.components.switch_kanban_dialog.index": ["切换看板", "创建看板", "卡片数：", "可见范围："], "pages.kanban.const": ["内容营销", "制定目标", "运营推广", "复盘总结"], "pages.kanban.pages.board": ["从模板新建", "我参与的", "公共看板"], "pages.live_marketing.components.content": ["勾选则此内容中包含的表单会视为直播报名表单，报名用户产生用户直播预约等行为"], "pages.live_marketing.components.lead_table_wrap": ["报名线索明细", "取消勾选", "发送短信通知"], "pages.live_marketing.components.live_dialog": ["选择直播", "请选择直播"], "pages.live_marketing.components.sumdata": ["观看次数", "回放人数", "回放次数", "当前直播平台未接入，暂无互动数据", "此数据为在直播开始前20分钟到直播结束时间范围内客户访问直播链接并完成手机号验证的客户数，单个客户具体观看次数暂无统计。", "观看人数及次数在直播结束1小时后同步", "此数据为在直播结束后客户访问直播链接并完成手机号验证的客户数，单个客户具体回放次数暂无统计。", "回放人数及次数次日凌晨5点同步", "平均观看次数", "平均观看时长"], "pages.live_marketing.components.table": ["是否观看"], "pages.live_marketing.create": ["编辑直播", "创建直播", "直播标题：", "直播封面：", "直播讲师：", "选择直播平台：", "保利威直播平台", "关联保利威所有直播活动，可获取用户报名、观看/回放时间及状态、互动数据", "小鹅通直播平台", "关联小鹅通所有直播活动，可获取用户报名、观看/回放时间及状态数据", "微吼直播平台", "支持微吼「视频直播模式」对接，可获取用户报名、观看/回放时间及状态、互动数据", "微信视频号直播", "获取用户报名数据和行为，无法获得直播观看数据", "注意事项", "其他直播平台", "可获取用户报名数据，无法获得直播观看数据", "小鹅通直播活动", "小鹅通直播链接", "保利威直播活动", "保利威直播", "直播时间:", "直播简介：", "给观看用户添加标签", "直播聊天：", "开启后，直播期间、观众可以进行聊天互动", "直播回放：", "开启后，即可在直播结束后自动生成回放视频，可让观众以后也能随时观看直播内容。", "注意：观看回放也会消耗流量，观看次数越多，流量消耗越大。", "观看次数：", "观看人数限制", "直播观看次数最大5000次，超出用户将无法进入观看直播，请合理设置直播观看限制", "创建微信视频号直播注意事项", "请在直播活动开始时间之前，先发起微信视频号直播，避免由于未开播导致用户无法进入观看", "该视频号直播活动开始时间需晚于上个视频号直播结束时间，避免上次活动的用户串到本次直播中", "密码不能为空", "请输入数字值", "密码必须是4位", "请输入直播标题", "请填写直播讲师", "请输入讲师直播密码", "请输入4位数字的讲师直播密码", "请选择结束时间", "请上传直播封面", "请填写直播链接地址", "选择小鹅通直播活动", "回放中", "等待中", "限制观看人数仅支持1～5000范围的数值", "目睹直播平台", "关联目睹所有直播活动，可获取用户报名、观看", "目睹直播活动", "关联目睹所有直播活动，可获取用户报名、观看/回放时间及状态、互动数据", "请选择目睹直播活动", "请选择保利威直播活动", "关联微吼所有直播活动，可获取用户报名、观看/回放时间及状态、互动数据", "微吼直播活动", "请选择微吼直播活动", "直播主页模版"], "pages.live_marketing.dashboard": ["直播概览", "点击按钮，可立即同步以下数据：", "将拉取目睹活动下的虚拟场所，自动创建为子级市场活动，并在下方活动列表展示。", "同步各子级市场活动的观众和直播观看数据，并最终在本活动中汇总。"], "pages.live_marketing.index": ["新建直播"], "pages.mail_marketing.components.mail_preview_dialog": ["发送对象：活动人员邮件地址", "发送对象：上传邮件文件"], "pages.mail_marketing.components.open_setting": ["请将复制以下信息，在DNS服务器配置通过后，再进行检查", "开始检查", "在DNS下配置相应的设置值，只有SPF、DKIM、MX全部通过后才能使用该发信域名。这个过程可能需要几十分钟或者几小时。你可以一键复制配置信息，发给对应配置人员。", "了解如何配置", "主机记录", "记录类型", "需配置的记录值", "一键复制配置", "修改发信域名", "确认保存", "发信域名DNS配置信息如下：", "配置指南：", "gitbook帮助url："], "pages.mail_marketing.components.template_dialog": ["选择邮件模板", "自定义模板", "点击预览", "去选择预设模板", "富文本编辑器模板", "页面编辑器模板"], "pages.mail_marketing.mail_group_send.group_object": ["清空已选", "选择您需要发送邮件的用户清单，点击下载", "上传邮件地址"], "pages.mail_marketing.mail_group_send.index": ["还没有设置发件人与回复人，暂时不能发送邮件，", "前往添加", "预约发送时间：", "发送时间：立即发送", "发送对象：活动报名人员邮件地址", "发送对象：邮件上传地址", "请上传邮件地址", "编辑邮件群发"], "pages.mail_marketing.mail_init.components.card_funnel": ["邮件转化漏斗"], "pages.mail_marketing.mail_init.components.config": ["打开次数", "是否取消订阅", "是否垃圾举报", "成功送达", "无效邮件-SendCloud黑名单中", "无效邮件-取消订阅", "无效邮件-服务器不可达", "无效邮件-地址格式错误", "无效邮件-IP、域名被拒", "无效邮件-地址不存在", "无效邮件-垃圾邮件", "无效邮件-发件人/收件人被拒", "收件人被拒", "无效邮件-其他", "软退信-服务不可达", "软退信-IP、域名被拒", "软退信-邮箱地址不存在", "软退信-发件人/收件人被拒", "软退信-发件人", "软退信-其他"], "pages.mail_marketing.mail_init.components.link_detail": ["点击链接排名", "会议门票URL"], "pages.mail_marketing.mail_init.components.problem_email": ["问题邮件"], "pages.mail_marketing.mail_init.components.spread_detail": ["活动人员邮件地址", "预览内容", "人群中含有", "个问题邮件，为了邮件发送通道的信誉维持良好水平，已在发送时自动过滤。", "查看问题邮箱", "发送取消", "按状态筛选", "按失败原因筛选", "按邮箱搜索"], "pages.mail_marketing.mail_init.home": ["账户信息", "可用余额", "当天可用邮件额度", "信誉度", "搜索邮件标题", "活动报名人员邮件地址", "暂无失败原因"], "pages.mail_marketing.mail_init.welcome": ["开通邮件营销", "创建企业自有发信域名", "（不要使用企业邮箱作为发信域名）", "连接邮件服务配置", "如何配置", "API_USER：", "请输入连接邮件的API_user", "API_KEY：", "请输入连接邮件的API_key", "保存并继续", "开通配置", "确认开通", "请输入API_USER", "请输入API_KEY", "请输入发送域名", "不能含有中文", "不能含有特殊字符", "个性内容精准推送，自动化线索培育"], "pages.mail_marketing.mail_problem.index": ["搜索邮件地址", "无效邮件", "软退信", "请先选择要删除的邮件"], "pages.mail_marketing.mail_template.create.index": ["模板内容：", "编辑邮件模板"], "pages.mail_marketing.mail_template.index": ["搜索邮件模板名称"], "pages.mail_marketing.mail_template.templateList": ["删除该邮件模板，是否继续？"], "pages.mail_marketing.mail_template.components.selectDialog": ["页面编辑器", "拖拽式编辑图片和文本", "富文本编辑器", "自主式编写HTML或文本"], "pages.marketing_calendar.components.marketing_material": ["跳转至：", "添加表单"], "pages.marketing_calendar.components.popover": ["当月还没有创建任何市场活动，马上新建市场活动，开启获客之旅"], "pages.marketing_calendar.dashboard": ["预计ROI：", "实际ROI：", "推广目标人群", "暂无推广", "快快发起一个推广吧", "先选择该市场活动的推广目标人群，方便后续精准推广。", "再添加关于该市场活动的推广内容，用于后续推广该市场活动。", "您可以选一个合适的推广方式推广该活动，轻松开启获客之旅。"], "pages.marketing_calendar.index": ["进入市场活动列表", "日历视图", "计划视图", "年", "结束时间：", "暂无市场活动", "立即新建"], "pages.marketing_calendar.setting": ["您可以设置本企业中，不同市场活动类型在营销日历显示的背景色。", "背景色"], "pages.marketing_process.bpm": ["保存草稿", "营销组件", "流程设计", "属性设置", "高级群发每日可群发的次数最多100次，每个微信用户每月最多只能接收4次推送，超出将无法发送，请合理向微信用户发送消息", "添加流程启动条件", "当前节点未支持", "提交：", "{{option0}}等{{option1}}个标签", "公众号图文素材", "回复“", "更新草稿成功", "保存草稿成功"], "pages.marketing_process.cards": ["推荐营销模板", "流程用户情况", "已完成流程用户数", "新增启动流程用户数", "累计流程用户数", "运行中的流程用户数"], "pages.marketing_process.components.attribute_judgment": ["满足以下条件进入节点："], "pages.marketing_process.components.data": ["通过后台【行为积分】功能自动输出结果", "普通成员", "只读", "读写", "单个图片不得超过20M"], "pages.marketing_process.components.field_update": ["请选择需要更新的字段"], "pages.marketing_process.components.follow": ["您想跟踪用户关注了哪个公众号？", "了解如何绑定微信公众号？"], "pages.marketing_process.components.multi_branch": ["当用户发送文字  完全匹配", "进入节点", "超时后进入节点"], "pages.marketing_process.components.sms": ["仅在特定时间发送", "起始时间"], "pages.marketing_process.components.start_condition": ["附加条件", "当销售线索选择字段更新为对应时，启动本流程", "当用户提交了选择的表单时启动流程", "当用户关注选择公众号时启动流程", "当用户", "选择会议时启动流程", "选择日期字段", "在以下时间启动本流程", "在选择日期当天", "在选择日期前", "对满足人群条件的已有存量用户启动此流程", "默认对满足条件新进入目标人群的用户启动此流程", "销售线索.", "日期当天"], "pages.marketing_process.components.wait": ["如何延迟下一步操作？", "延迟下一步操作", "选择具体某个时间", "等待至具体某个时间"], "pages.marketing_process.components.wechat_tips": ["您还没有绑定的微信公众号，", "立即绑定微信公众号"], "pages.marketing_process.config": ["全部模板", "会议邀请与跟进", "潜在客户培育", "从头开始创建一个新的营销活动", "会议报名跟进", "销售线索更新", "目标人群培育"], "pages.marketing_process.create": ["编辑自动化流程", "设置流程信息", "设计流程", "选择流程模板"], "pages.marketing_process.detail": ["推广详情", "服务号数据统计", "微信服务号：", "分享服务通", "服务号可以瞎发文字了", "内容超出限制"], "pages.marketing_process.details.index": ["流程用户", "流程图", "已", "修改时间", "修改人"], "pages.marketing_process.details.timeline": ["流程详情", "流程开始时间"], "pages.marketing_process.details.timelineitem": ["图片消息", "关注了公众号:", "天启动流程"], "pages.marketing_process.details.ulist": ["营销用户列表", "流程用户总数", "已执行完成", "中止"], "pages.marketing_process.index": ["自动化你的市场营销，识别潜在客户，提升转化效率", "创建自动化流程", "您的SOP流程配额不足，如需购买，请联系纷享客服。"], "pages.marketing_process.info": ["流程名称：", "流程描述：", "请输入描述内容", "请输入流程名称"], "pages.marketing_process.main_list": ["搜索流程名称", "营销流程列表", "草稿流程列表", "确定要删除草稿？"], "pages.marketing_process.template": ["使用模板"], "pages.marketing_process.utils.index": ["【{{option0}}】节点未支持，请先移除后重试", "【{{option0}}】节点至少要有一根连出线", "【{{option0}}】节点至少要有两根连出线", "【{{option0}}】节点至少要有一根连入线", "【{{option0}}】存在未设置节点名称的节点", "【{{option0}}】节点未选择表单", "【{{option0}}】节点未选择时间", "【{{option0}}】节点未选择会议", "【{{option0}}】节点未选择活动报名表单", "【{{option0}}】节点未选择发送公众号", "【{{option0}}】节点未填写发送内容", "【{{option0}}】节点图片大小超过1M，请重新上传", "【{{option0}}】节点未完善发送内容", "【{{option0}}】节点未选择发送模板", "【{{option0}}】节点未选择标签", "【{{option0}}】节点未选择要更新的对象名称", "【{{option0}}】节点未选择要更新的对象字段名称", "【{{option0}}】节点未填写要更新的字段值", "【{{option0}}】节点未填写正确手机号码格式", "【{{option0}}】节点未填写正确邮件格式", "【{{option0}}】节点未配置分支节点", "【{{option0}}】节点未配置匹配关键字", "【{{option0}}】节点未配置默认进入分支节点", "【{{option0}}】节点未设置超时时间", "【{{option0}}】节点未配置超时后进入分支节点", "【{{option0}}】节点未选择公众号", "【{{option0}}】节点未配置完整", "【{{option0}}】节点未配置所有满足条件", "【{{option0}}】节点未配置不满足条件", "【{{option0}}】节点未设置邮件内容", "【启动条件】节点请选择启动条件", "【启动条件】节点未选择公众号", "【启动条件】节点未选择表单", "【启动条件】节点未选择会议", "【启动条件】节点未选择触发条件", "【启动条件】节点未选择要更新的对象名称", "【启动条件】节点未选择要更新的对象字段名称", "【启动条件】节点未填写要更新的字段值", "【启动条件】节点未选择目标人群", "【启动条件】节点未选择日期字段", "微信对象"], "pages.marketing_process.utils.node": ["选择或新建关联对象"], "pages.meeting_marketing.MeetingCreate.MeetingTinymceEditorDialog": ["编辑会议详情"], "pages.meeting_marketing.MeetingCreate.index": ["编辑会议", "地图定位，开启后会议落地页可以支持点击跳转地图", "请输入街道、写字楼、名称自动定位地址", "请输入会议地点", "请选择会议时间", "请选择会议开始时间", "请选择会议结束时间", "海报封面", "填写会议信息", "会议主页设置", "保存并下一步", "完成设置", "跳过，稍后设置", "会议主页预览", "创建会议成功后，系统将根据您设定的会议模板自动生成会议主页。", "保存超时", "填写会议基础信息"], "pages.meeting_marketing.MeetingCreate.MeetingInfo.MeetingInfo": ["会议主页模版", "地理定位已开启", "默认模版"], "pages.meeting_marketing.MeetingData.MeetingDataFans": ["公众号粉丝统计", "新增关注", "活动粉丝"], "pages.meeting_marketing.MeetingDetail.components.AttendingSituation": ["签到率"], "pages.meeting_marketing.MeetingDetail.components.MeetingInfo": ["重新加载", "更多信息"], "pages.meeting_marketing.MeetingDetail.components.ShareSettingDialog": ["海丽别点，点了也没啥可看的。"], "pages.meeting_marketing.MeetingDetail.components.config": ["编辑基本信息", "会议签到设置", "会议邀约设置", "会前", "设置会议报名", "制作推广海报", "会中", "现场签到设置", "会后", "会议资料下载", "制作调查问卷", "会议设置"], "pages.meeting_marketing.MeetingInvitationContent.MeetingInvitationContentContentFormCheckbox": ["勾选则此内容中包含的表单将会视为会议报名表单，同报名设置中的报名表单，受会议报名设置影响，报名用户产生会议报名等行为"], "pages.meeting_marketing.MeetingInvitationContent.MeetingInvitationContentDetail": ["会议主页与报名表单"], "pages.meeting_marketing.MeetingInvite.Whitelist": ["开启状态", "开放范围:", "添加员工", "选择开放范围"], "pages.meeting_marketing.MeetingInvite.index": ["开启后，员工在收到邀约通知后可在邀约名单处获得带有邀约客户名称的邀请函", "允许员工添加客户到邀约名单", "开启后允许员工在纷享销客APP-【营销通】中的会议邀约名单添加拟邀客户", "更新失败，请重试", "可开启面向内部员工开放自主邀约功能，让员工可以自动邀约和添加客户。"], "pages.meeting_marketing.MeetingParticipants.ParticipantsAddDialog": ["参会状态", "参与状态"], "pages.meeting_marketing.MeetingParticipants.ParticipantsCrmResaveDialog": ["确认后，所选参会人员", "（存入失败）将会重新执行存入销售线索，请确认是否继续操作？"], "pages.meeting_marketing.MeetingParticipants.ParticipantsGroupSelector.ParticipantsGroupSelectorSelectorLine": ["添加分组"], "pages.meeting_marketing.MeetingParticipants.ParticipantsGroupSelector.index": ["没有可移除的分组", "请选择要移除的分组："], "pages.meeting_marketing.MeetingParticipants.ParticipantsImportDialog": ["导入参会人员", "请按照标准的Excel导入模板填写需要导入的参会人员数据，导入的参会人员无需报名，可向其发送会议邀请或者直接向其发送会议门票。"], "pages.meeting_marketing.MeetingParticipants.ParticipantsInviteDialog": ["发送邀约", "温馨提示：选择邀约客户，下发任务给邀约员工，员工收到任务向客户发送邀约海报。", "邀约内容", "邀约客户", "邀约负责人", "邀约话术", "邀约时间", "请返回参会人员列表选择邀约客户", "请输入邀约话术", "请添加邀约海报", "请添加指定邀约人", "请选择邀约时间", "请选择邀约开始时间", "请选择邀约结束时间", "诚邀您参加："], "pages.meeting_marketing.MeetingParticipants.ParticipantsInviteDialogCampaign": ["过滤已报名用户"], "pages.meeting_marketing.MeetingParticipants.ParticipantsInviteDialogContent": ["报名海报", "邀约设置------"], "pages.meeting_marketing.MeetingParticipants.ParticipantsInviteDialogPrincipal": ["指定邀约员工"], "pages.meeting_marketing.MeetingParticipants.ParticipantsReviewDialog": ["审核参会人员", "审核通过，参会人员即报名成功"], "pages.meeting_marketing.MeetingParticipants.ParticipantsSigninDialog": ["更改签到状态", "是否更改选择用户签到状态为：已签到"], "pages.meeting_marketing.MeetingParticipants.StatusImportDialog": ["请按照标准的Excel导入模板更新参会人员的签到状态。", "请添加您要导入的签到更新参会人员名单", "参会人员签到状态导入更新模板", "下载签到状态导入更新模版", "导入签到状态失败记录"], "pages.meeting_marketing.MeetingParticipants.config": ["全部审核状态", "全部签到状态", "全部存入线索状态"], "pages.meeting_marketing.MeetingParticipants.index": ["待审核：", "待邀约：", "待处理：", "搜索参会人员名称"], "pages.meeting_marketing.MeetingSignin.components.ScanSignInSetting": ["签到二维码下载打印，张贴在会议现场，参会人员通过微信扫码签到", "下载签到二维码", "指定现场报名内容页面：", "签到成功页配置", "指定现场报名内容页面", "请选择指定现场报名内容", "用户签到未报名时，点击“前往报名”显示的内容页面", "签到页配置"], "pages.meeting_marketing.MeetingSignin.index": ["验票人员", "验票人员更新成功", "提前设置好会议签到方式，方便会议开始后签到信息录入系统。"], "pages.meeting_marketing.MeetingSignin.qrcode_download_dialog": ["签到二维码标签：", "客户签到后自动打上当前设置的标签，可以用于一个会议下多分会场会议签到识别。添加标签后，请重新下载二维码，否则标签不生效"], "pages.meeting_marketing.MeetingSignin.signin_custom.index": ["设置会议签到成功展示信息", "签到时间：2022-01-01 10:00", "查看会议详情", "签到成功提示：", "设置首次及再次签到提示的文案，尤其在付费报名场景下用于提醒会务组此手机号之前已完成签到", "首次签到成功提示", "再次签到成功提示", "自定义展示字段：", "支持在签到成功页显示相关会议安排，比如给当前签到的客户分配的座位号、分组、酒店房间号等。", "首次使用请联系CRM管理员在活动成员对象上新增相关自定义字段，比如酒店房间号字段，会务人员编辑完活动房间号字段后，然后再到此页面配置。", "请选择参会人员自定义字段", "请输入显示相关描述", "请填写相关配置信息", "新增活动成员自定义字段显示", "温馨提醒：", "您已完成签到，无需再次验证", "温馨提示：防止后期遗忘，您可以截图保存相关信息", "引流企微", "签到验证", "签到失败-未报名", "签到失败-审核中", "签到失败-审核未通过", "手机号后6位或邮箱", "手机号后6位", "您的报名信息审核未通过，请添加工作人员沟通联系", "签到失败", "您的报名信息审核中，请添加工作人员沟通联系", "请输入报名时填写的{{option0}}完成签到", "授权手机号", "还没报名，马上报名", "您还未报名", "会议签到时间：", "{{option0}}：", "前往报名", "签到方式：", "手机号签到", "使用小程序快速验证组件", "邮箱签到", "文案描述", "引流二维码：", "请选择企微活码", "选择指定现场报名内容：", "用户签到未报名时，点击“前往报名“显示的内容页面", "签到提示：", "设置签到提示的文案，尤其在付费报名场景下用于提醒会务组此手机号之前已完成签到", "签到失败提示", "签到失败失败提示", "设置会议签到页设置", "提示语", "设置个性化提示语，如：请前往3楼会议大厅", "自2023年8月26日起，腾讯对验证组件进行收费，请确保当前小程序已购买【手机号快速验证组件】资源包", "绑定专属小程序且在小程序管理后台的付费管理中购买【手机号快速验证组件】资源包后使用", "开通专属小程序", "购买验证组件", "必须保留一种签到方式"], "pages.meeting_marketing.MeetingSignin.ticket_before_create_dialog": ["您还没有绑定公众号", "微信会议门票基于微信公众号卡券功能，需要您的公众号已开通卡券功能，并绑定到营销通", "了解如何开通微信公众号卡券功能", "请选择您用来发送微信会议门票的公众号", "绑定其他微信公众号", "检查卡券功能失败，请重新选择", "所选公众号还未授权卡券功能，重新授权以完成会议门票开启", "前往重新授权", "所选公众号还未开启卡券功能，前往微信公众平台开启卡券功能，微信审核通过后即可正常使用微信会议门票", "前往微信公众号平台", "未知错误，请重新选择", "开启微信会议门票"], "pages.meeting_marketing.MeetingSignin.tickets_setting_config": ["手机扫码验票", "参会人员出示短信", "邮件通知中的参会二维码或者通过报手机尾号与给验票人员进行签到入场", "使用方式：打开纷享销客APP-应用-营销通，点击【会议验票】，选择需要验票的活动即可快速查询或者扫描验证", "客户扫码签到", "下载签到二维码，粘贴在会议现场，参会人员通过微信扫描签到二维码完成签到", "微信会议门票", "参会人员在通过出示微信卡包中的会议门票入场", "会务人员在纷享销客APP-营销通-会议营销验证参会人员的微信会议门票完成入场签到", "如何使用微信会议门票？", "前往门票配置页", "会务管理员可在参会人员列表手动更新参会状态", "前往参会人员"], "pages.meeting_marketing.check_in.index": ["签到小程序码", "管理员可以在参会人员列表手动更新参会状态"], "pages.meeting_marketing.directional_invitation.index": ["如何使用定向邀约", "下载导入模板，导入邀约名单", "制作邀约海报", "设置定向邀约的专属海报", "一键通知员工", "选择定向客户，一键通知负责邀约的员工", "邀约情况", "邀约客户总数", "短信邀约", "点击上传邀约名单", "仅支持xls、xlsx格式，下载导入模版", "请按照标准的Excel导入模板填写需要导入的客户信息，一键邀请其参加会议。", "请选择导入文件", "导入邀约人员模板", "条，导入失败", "还未设置邀约海报，设置完成后即可发起定向邀约", "会议信息还未设置完成，设置完成后即可发起定向邀约", "默认向选中的邀约员工发送客户邀约任务，邀约员工可向其所有负责的邀约客户发送定向邀约海报。", "本次已选中", "人，发起邀约员工通知", "负责邀约员工：", "新建定向邀约海报", "暂不新建", "去编辑会议", "暂不编辑", "发起通知", "更新已选择的邀约状态为：", "编辑邀约客户信息", "姓名：", "公司：", "手机：", "邮箱：", "邀约员工：", "姓名不能超过32个字", "公司不能超过128个字", "请输入合法的手机号码", "请输入合法的邮箱", "确认删除该邀约客户吗？", "导入邀约人员失败记录", "无邀约人员"], "pages.meeting_marketing.enroll_setting.compontents.data_setting": ["活动成员映射：", "自动识别会员身份，用户无需再填写表单，自动提交会员信息，但无法收集表单额外信息", "默认报名表单数据存入：{{option0}}", "已设置线索映射", "未设置线索映射"], "pages.meeting_marketing.enroll_setting.compontents.form_map": ["还未完成线索映射", "默认报名表单数据存入", "会员信息存入"], "pages.meeting_marketing.enroll_setting.compontents.manage_setting": ["报名管理设置", "审核人员：", "审核状态页面", "活动详情页审核页面", "审核页面预览", "审核不通过页面预览", "审核中提示文案", "当用户进入报名页面或表单页面时，若报名信息处于审核中或审核未通过状态，将提示相应文案", "请输入审核中提示文案", "审核未通过提示文案", "请输入审核未通过提示文案", "审核中提示文案：", "审核未通过提示文案：", "、{{option0}}", "会员一键报名后，会员信息会存入到「{{option0}}」"], "pages.meeting_marketing.enroll_setting.compontents.other_setting": ["报名线索存入成功后自动创建会员", "限制表单数不超过", "个，提交满额时提示："], "pages.meeting_marketing.enroll_setting.compontents.site_preview_qrcode": ["手机预览"], "pages.meeting_marketing.enroll_setting.index_old": ["选择报名表单", "设置报名表单", "报名按钮设置", "地点：", "请选择报名表单", "请输入报名截止时间", "报名设置保存成功，完成会议详情即可发布会议"], "pages.meeting_marketing.invitation_content.const_invitation": ["第一号邀请函第一号邀请函第一号邀请函第一号邀请函第一号邀请函第一号邀请函第一号邀请函第一号邀请函", "第二号邀请函", "第三号邀请函", "第四号邀请函", "第五号邀请函"], "pages.meeting_marketing.invitation_content.invitation": ["搜索邀请函名称", "新建邀请函之前，请先设置报名表单。"], "pages.meeting_marketing.invitation_content.invite_poster": ["专为会议邀请对象定制个性邀请函海报"], "pages.meeting_marketing.meeting_detail.index": ["（封面建议尺寸：900*500px）", "会议形式", "会议规模", "请输入会议人数", "会议内容", "选择会议模板", "会议标题不能超过50个字", "会议详情保存成功，完成报名设置即可发布会议", "请输入活动地点", "会议人数不能为空", "请输入正整数"], "pages.meeting_marketing.meeting_infomation.index": ["仅需2步准备会议？", "稍后设置", "1.添加会议详情，让参会人了解您的会议", "2.完成报名设置，即可进行会议的发布！", "添加会议详清", "编辑市场活动--会议"], "pages.meeting_marketing.meeting_init.index": ["搜索会议标题", "暂无该状态下的会议", "全部会议"], "pages.meeting_marketing.meeting_init.meeting_list": ["是否删除会议？"], "pages.meeting_marketing.meeting_init.meeting_welcome.meeting_welcome": ["立即新建会议", "计划与审批", "活动落地页", "多渠道推广海报", "电子产品手册等", "多渠道消息群发", "全员裂变推广", "专属渠道推广码", "员工代报名", "在线报名及会议提醒", "报名数据实时跟踪", "报名、审核、参会提醒通知", "线下签到与互动", "客户扫码或参会码验证签到", "扫客户名片一键录入线索", "扫码留资、入群、关注公众号等", "会后跟进及效果追踪", "精彩瞬间及问卷资料推送", "活动线索分配及转化跟进", "活动ROI评估", "线下活动全流程数字化管理，助力营销增长", "支持沙龙、发布会、展会、研讨会、渠道伙伴大会等多类型线下会议场景，", "从会前内容制作、推广、邀约，审核到会中签到、互动，再到会后的总结及", "评估，全流程数字化支撑"], "pages.meeting_marketing.spread_channel.launch_spread": ["会议推广"], "pages.member.components.add_official_site": ["页面地址", "匹配规则", "如存在多个同类型页面需要统一拦截，即可将链接中的变量设置为通配符，", "则会拦截其他所有的客户案例页面", "请填写网站名称", "请填写网站地址"], "pages.member.components.header": ["会员操作拦截代码", "class名称"], "pages.member.components.leads_pool_dialog": ["设置线索存入地方", "请选择线索存入类型"], "pages.member.components.login_preview": ["全屏预览", "手机登录", "微信扫码关注公众号即可快速登录", "手机短信验证码", "登录", "新用户注册", "登录即视为同意", "《服务协议》", "及", "《隐私政策》"], "pages.member.components.official_site": ["配置会员可访问的官网页面，以下列表的官网页面需要会员身份才能访问（注意：需要在官网上安装跟踪代码）", "获取官网会员身份拦截说明"], "pages.member.components.personal": ["设置个人信息表单到会员对象的数据映射", "设置个人信息表单到会员数据的映射", "个人信息表单"], "pages.member.components.register": ["设置会员注册表单到会员对象的数据映射", "给注册会员的用户添加标签", "会员注册二维码", "注册审核", "开启后，会员报名需通过审核才可注册成功", "操作指导"], "pages.member.components.site": ["配置会员可访问的微页面，以下列表的微页面需要会员身份才能访问", "添加微页面"], "pages.member.components.usercenter": ["个人中心二维码", "我的报名展示活动类型", "会员从个人中心进入我的报名页面中，可以看见的活动类型，例如可设置“日常推广活动”类型不展示", "以下选中的活动类型才会展示在我的报名列表中", "至少选择一种活动类型"], "pages.member.components.weblogin": ["登录标题", "标题描述", "登录方式", "使用微信服务号登录", "服务隐私协议", "请输入协议在线链接", "服务协议", "隐私协议", "确认移除？", "移除二维码成功", "移除成功，请重新添加二维码", "设置成功！", "存在表单必填项未填，请重新检查"], "pages.member.content": ["官网会员页面"], "pages.member.detail": ["用户详情", "行为记录", "加载完成"], "pages.member.index": ["您正在使用营销通标准版，升级专业版，进行会员管理", "立即开通会员中心", "会员整体概况", "是否已完成会员启用？", "开通会员初始化会员对象可能需要1-2分钟，如刷新还未显示开通成功状态请过1-2分钟后刷新重试，是否立即刷新会员开通状态？", "本月新增会员数", "构建统一的会员管理及运营体系，精细会员运营，提升会员忠诚度、促进会员转化", "会员数据管理", "统一会员注册登录、注册会员沉淀至CRM统一管理、分析和洞察", "会员标签体系", "基于营销通统一用户营销标签体系，完善会员标签画像", "会员精细运营", "动态会员运营群组，自动识别营销触点，会员精细运营转化", "会员自动旅程", "基于会员的标签和分层，设置不同的会员生命周期旅程运营策略"], "pages.member.page": ["注册页面", "登录页面", "个人信息更新页", "web登录页面"], "pages.member.setting": ["会员等级是一种会员成长体系的对外展示形式。您可以使用此功能，建立一套自己的会员成长体系，根据客户生命周期设置会员等级，让会员通过获取成长值来决定自己的会员等级，获得不同的会员权益，从而帮助您更好的维护客户关系、保证会员的活跃度。", "会员积分", "会员积分是企业回馈给客户消费、活动、互动行为的权益，会员可通过积分兑换、积分抵现以及参与游戏等途径消耗。", "会员成长值", "会员成长值是客户行为的量化指标，用来确定会员等级。会员可通过基础任务、消费任务、活跃任务获得。通常情况下，成长值越高，客户在企业内的等级越高，越活跃；成长值越低，客户在企业内等级越低，越不活跃。", "线索表单自动生成会员", "用户提交表单数据存入线索后，同时为该用户创建会员身份，以便该用户查看个人相关数据。", "报名生成线索", "在用户在会员注册页面完成注册时，会生成线索；在会员一键报名参加市场活动时，生成该市场活动下的线索。"], "pages.member.table": ["输入会员昵称", "添加会员", "新建会员", "升等级"], "pages.mini_app.compontents.AuthPageSetting": ["上传背景图", "恢复默认图", "确定要恢复默认图吗？", "只能上传图片格式", "图片大小不能超过2M"], "pages.mini_app.compontents.brief_intro": ["已认证", "未认证", "累计用户", "授权已有微信小程序，搭建企业微站，快速发布获取企业品牌营销小程序"], "pages.mini_app.compontents.content_rank": ["内容排行mounted"], "pages.mini_app.compontents.data_report": ["微站数据简报"], "pages.mini_app.compontents.employee_rank": ["员工排行mounted"], "pages.mini_app.compontents.open_situation": ["员工名片开通情况"], "pages.mini_app.compontents.pv_trend": ["访问人数趋势"], "pages.mini_app.compontents.rank_sideslip": ["父组件mounted", "这里的pageszie"], "pages.mini_app.compontents.temp": ["模版预览", "是否发布替换整套模版？", "保存后该模版会替换线上微站整个页面和组件，不可撤回", "设备医疗行业", "品牌加盟", "教培行业", "家装行业", "科技板块", "替换成功", "深蓝系", "科技蓝", "清新蓝", "简约紫"], "pages.mini_app.compontents.tempCenter": ["微站模版中心", "可选择模版一键生成您的微站！", "更多模版", "敬请期待"], "pages.mini_app.overview": ["有一个新的小程序版本，", "去更新", "马上授权", "为您推荐的微站模版", "授权后开始搭建——可选择模版一键生成您的微站！"], "pages.mini_app.product": ["产品推广列表", "移动端展示方式", "默认模式", "列表模式", "小图模式"], "pages.mini_app.renovation": ["第一个菜单默认为小程序主页，最多可添加4个菜单", "授权页设置", "用户第一次进入小程序需授权微信的页面", "页面预览", "点击底部菜单可切换预览页面", "底部导航设置", "导航布局：", "图标颜色：", "选中态颜色：", "背景底色：", "首页弹窗", "可用于活动宣传、活动优惠、欢迎语、最新公告等场景", "需要", "建议尺寸：558像素*980像素", "小程序弹窗", "弹出次数", "每次进入小程序后弹出", "首次进入小程序后弹出", "首页弹窗预览", "请在右侧上传图片", "仅弹出一次", "新上传弹窗图片后会重新弹出", "如需手机查看实际效果，请", "扫一扫二维码", "定制导航配置：", "代码："], "pages.partner_marketing.compontents.data_list": ["推广伙伴（个）", "推广人数"], "pages.partner_marketing.compontents.partner_ranking": ["2个对接企业暂未推广：釜山分享科技有限公司、江苏小美科技有限公司"], "pages.partner_marketing.compontents.partner_spread_detail": ["7大核心场景带您解读企业培训行业数字化增长奥秘", "推广标题：直播活动预告「大核心场景带您解读企业培训行业数字化增长奥秘」"], "pages.partner_marketing.compontents.partner_spread_table": ["搜索推广标题", "个伙伴已推广", "个伙伴待推广", "访问人次：", "操作时间：", "推广任务", "推广效果数据", "操作信息", "撤销成功"], "pages.partner_marketing.compontents.spread_content": ["2021-03-08 至 2021-03-009", "7个互联对接企业"], "pages.partner_marketing.staff": ["对接企业员工对外推广话术，在分享到微信好友或朋友圈时可以直接复制粘贴此宣传语进行推广", "推广图片和海报自动携带伙伴公司及员工信息"], "pages.poster_gallery.create.invite_poster": ["编辑海报（可自由拖动【姓名】和【二维码】位置和大小）", "文字排版", "选择姓名颜色"], "pages.poster_gallery.list.index": ["推广活动海报", "图片海报"], "pages.poster_gallery.list.qrposter_list": ["扫码跳转："], "pages.preset_employee_card.cardinfo.index": ["设置员工名片不允许员工编辑信息权限", "其中头像 、姓名、手机号、部门、职位、邮件信息默认从通信录自动获取", "填写以下企业信息"], "pages.preset_employee_card.compontents.anync_employee": ["覆盖更新，更新员工所有名片资料项", "补充更新，只更新到员工未设置的名片资料项", "应用到新开通的员工名片，不覆盖现有员工名片"], "pages.preset_employee_card.const": ["预设名片信息", "预设产品信息"], "pages.preset_employee_card.preview.cardinfo": ["暂无电话", "暂无公司信息", "分享名片", "存入手机", "详细信息", "微信号信息", "暂无邮箱地址", "个人介绍", "个人简介信息", "你的浏览器 不支持 video", "名片信息", "打电话", "企微联系"], "pages.preset_employee_card.productinfo.const": ["展示产品"], "pages.preset_employee_card.productinfo.index": ["最多可预设", "个产品，当前剩余可新增", "暂无预设产品", "当前产品数量已经达到预设上限！"], "pages.preset_employee_card.step_complete": ["接下来您可以将已设置好的名片展示信息批量应用到已开通的员工名片上", "批量应用更新", "是否向未开通员工发送使用邀请？"], "pages.product.create.datas": ["请填写试用表单按钮名称", "请选择产品试用表单"], "pages.product.create.dialog_tplsel": ["创建空白表单", "模板场景表单", "选用此模板", "选择试用表单模板"], "pages.product.create.index": ["产品名称不能为空", "产品简介不能为空", "产品价格", "最多上传3张,封面建议尺寸：900像素＊500像素", "最多上传3张,轮播图建议尺寸：900像素＊500像素", "请上传封面图", "图片（最多上传{{option0}}张。为了保证加载速度，上传图片尽量保持在1M以下，超过1M系统会默认做等比压缩，支持拖动排序）", "开启后，该产品可提交试用表单，用于免费试用、预约体验、私人订制等场景的客户信息收集", "选择产品试用表单", "请输入试用按钮文字", "您还未初始化您的产品试用表单，请先初始化再开启产品试用。", "立即初始化", "编辑产品", "选择CTA"], "pages.product.form.form": ["初始化成功，请前往产品启用产品试用功能", "数据提交", "请提交您的信息，我们会尽快联系您。"], "pages.product.form.netdisk.index": ["搜索文件", "从-[网盘]-[公司文件]选择", "从-[网盘]-[公司文件]-[客脉文件夹]选择"], "pages.product.list.datas": ["确认停用该产品？", "确认启用？", "产品移动失败，请重试", "产品置顶失败，请重试"], "pages.product.list.form": ["开启产品试用后，用户访问产品详情页可以点击试用按钮提交试用申请，申请信息会自动存入线索池，是获取潜在客户和商机的有力工具。", "立即开启", "了解最适合您的产品试用方案", "查看案例"], "pages.product.list.trial_datas": ["关联产品"], "pages.product.list.trial_list": ["产品列表", "关联产品：", "编辑试用表单", "预览试用表单", "导出表单数据", "全部产品"], "pages.product.trial_tpl_datas": ["免费试用", "适用于大多数行业收集客户信息的场景", "在线预约", "适用于汽车试驾、软装、法律咨询等行业收集预约报名信息的场景", "预约时间", "需求订制", "适用于旅游、家装、保险等行业客户需求收集的场景", "需求说明", "课程试听", "适用于教育培训行业收集意向学员信息等场景", "请提交您的信息，我们会尽快与您联系"], "pages.promotion.cards": ["员工使用", "开通率"], "pages.promotion.index": ["搜索任务标题", "你可以向未开通员工发送客脉小程序使用邀请，并且批量预设员工名片资料", "批量预设", "直接邀请", "修改预设"], "pages.promotion.send_invation": ["是否邀请开通？"], "pages.promotion_activity.index": ["企业推广", "推广方式："], "pages.promotion_activity.sms.components.index": ["预览效果：", "按邀约人员选择", "选择您需要发送短信的用户手机号清单，单个文件手机号不可超过10000个，点击下载", "发送模板：", "使用已有模板", "创建新模板", "由于创建新模板需要审核，请于审核通过后，在短信列表点击发送短信", "提示：开通短信服务可自定义企业短信签名", "Excel模板示例：尊敬的", "请选择邀约人员", "请选择活动人员", "请不要输入超过450个字", "请在群发对象excel文件中将自定义内容补充完整。", "忽略手机号清单中的错误手机号"], "pages.promotion_group.detail.statistics": ["热门内容", "群成员互动排行", "次互动", "浏览次数"], "pages.promotion_group.index": ["您正在使用营销通标准版，升级专业版，即可开始微信群管理", "搜索微信群名称", "排序："], "pages.qywx_manage.customer_circle.components.pickVideo": ["清除"], "pages.qywx_manage.customer_circle.components.sendContent": ["若选择“发送员工”，则选择的“发送员工”均会收到发送朋友圈任务，但实际只有选择的“发送对象”可见。如“发送对象”只有客户A，员工小明加了客户A，员工小李未加客户A，小明和小李均会收到朋友圈任务，但最终两人发送朋友圈后，只有客户A看到小明发送的朋友圈内容，而小李发送的内容无客户可见。", "发送客户", "发表内容：", "客户每天只能看到同一个员工发表的3条朋友圈，这三条朋友圈可以是在此处企业发表的，也可以是员工在企微端自己发表的", "图片大小超过10M，请重新上传", "总像素超过限制，请重新上传", "预计本次推送涉及员工数", "涉及企微客户数", "最多支持9个图片类型，或者1个视频，或者1个链接，类型只能三选一。其中图片可以点击标题栏拖动调整顺序"], "pages.qywx_manage.customer_circle.components.spread_table": ["由于微信规则限制，一个员工每天只有3条朋友圈能被客户看到。以下预计发送范围含超过3次的数据，可能会有误差，仅供参考。", "由于微信规则限制，多个员工添加同一个客户时，只会给最近与客户有聊天的员工发送群发任务。以下预计发送数按最近添加客户的员工计算，可能会有误差。"], "pages.qywx_manage.customer_circle.components.vmsg_conts": ["企业微信限制图片长宽乘积不超过1440*1080像素，每张图片不超过10M，最多支持9张", "视频大小超过10M，请重新上传"], "pages.qywx_manage.customer_circle.create.index": ["新建朋友圈发布"], "pages.qywx_manage.customer_circle.customers_table_sideslip.table_tools": ["本月已推送次数"], "pages.qywx_manage.customer_circle.info.index": ["管理员可创建朋友圈发表任务，成员确认内容后即可发布到朋友圈中，客户每天只能看到同一个员工发表的3条朋友圈，这三条朋友圈可以是在此处企业发表的，也可以是员工在企微端自己发表的，发布前请确保相关员工有企微朋友圈应用权限，否则无法接收朋友圈发送通知，相关流程操作如下", "管理员创建完朋友圈后，员工将在企业微信上收到【客户朋友圈】新消息通知", "员工点击【客户朋友圈】，点击待发送的朋友圈，可一键将朋友圈内容发表", "管理员查看员工发送情况，可以再次推送营销通消息通知员工进行发表"], "pages.qywx_manage.customer_circle.list.index": ["朋友圈发送列表", "发表朋友圈"], "pages.qywx_manage.customer_circle.list.table_tools": ["朋友圈内容", "访问总次数/总人数"], "pages.qywx_manage.customer_circle.sendDetail.index": ["朋友圈发送详情", "发表员工", "已发表/未发表员工", "已触达/待触达客户", "访问人数/人次", "转发人数/人次", "已发表员工", "未发表员工", "已触达客户", "待触达客户", "发表明细"], "pages.qywx_manage.customer_circle.sendDetail.publish_table": ["确认发送任务提醒？", "发送提醒成功"], "pages.qywx_manage.customer_circle.sendDetail.publish_tools": ["关联客户数", "未发表", "已发表"], "pages.qywx_manage.customer_circle.total.index": ["数据概览（本月）", "发表朋友圈任务数", "未满4次限制微信用户数", "员工累计发表朋友圈总次数", "累计触达客户数"], "pages.qywx_manage.customer_groups.detail.group_member.index": ["企业员工无法打标签"], "pages.qywx_manage.customer_groups.detail.group_member.table_tools": ["`群成员${_pushItem.groupMemberUserId.substring(_pushItem.groupMemberUserId.length - 6, _pushItem.groupMemberUserId.length)}`", "群成员类型", "入群时间", "入群方式", "该群成员未被添加为客户，暂时取不到昵称", "企业员工", "由成员直接邀请入群", "由成员通过邀请链接入群", "通过扫描群二维码入群"], "pages.qywx_manage.customer_groups.info.index": ["全部群聊"], "pages.qywx_manage.customer_groups.list.index": ["搜索群名称"], "pages.qywx_manage.customers.customer.panel_tips": ["通过直播活动PPT、线下门店等投放企业微信联系我二维码，向企业私域池导流。", "客户培育转化", "通过渠道/分类/标签等对企业微信客户分群，可通过定向群发、客户朋友圈向目标客户群投递广告。"], "pages.qywx_manage.customers.customer.panel": ["客户简报"], "pages.qywx_manage.customers.customer.table_tools": ["首次添加人", "首次添加时间", "未知来源", "搜索手机号", "名片分享", "手机通讯录", "微信联系人", "安装第三方应用时自动添加的客服人员", "搜索邮箱", "视频号添加", "通过日程参与人添加", "通过会议参与人添加", "添加微信好友对应的企业微信", "通过智慧硬件专属客服添加", "通过上门服务客服添加", "内部成员共享", "管理员/负责人分配"], "pages.qywx_manage.customers.customer.table": ["搜索昵称", "同步企业微信更新"], "pages.qywx_manage.dashboard.index": ["运营看板"], "pages.qywx_manage.group_qrcode.components.download_dialog": ["群活码下载", "群活码可以下载后在H5微页面、易拉宝、海报等推广内容制作时使用，引流客户到企微客户群", "群活码二维码"], "pages.qywx_manage.group_qrcode.components.qrcode_detail": ["是否群满后自动建群：", "自动建群名称及序号：", "当前状态：", "进群备注：", "群聊数据", "设置为失效"], "pages.qywx_manage.group_qrcode.components.table": ["当前状态", "群聊名称", "群来源", "群主", "群成员数", "初始绑定", "自动新建"], "pages.qywx_manage.group_qrcode.create.form": ["可加入的群聊", "最多可以添加5个群聊，客户扫码加群时单个群聊数量限制为200人，常用场景是在企微先建一个初始群，在此处选中，后续群群满后自动创建", "入群设置", "选择群聊群人数达到上限后，将以原群主身份自动创建新群", "创建新群名称", "群名前缀", "起始序号", "自动创建的群名为前缀加序号，如【VIP群10】，后续群名称序号递增", "备注信息可以在企微客户群成员【入群备注】字段中查看，支持查询筛选，做渠道统计", "起始序号必须为正整数！", "选择自动建群时，起始序号不能为空", "选择自动建群时，群名称不能为空", "请输入群活码名称", "请选择群聊"], "pages.qywx_manage.group_qrcode.create.group_selector": ["请勿重复选择群聊", "一个群活码最多只可选择5个群聊"], "pages.qywx_manage.group_qrcode.index": ["新建群活码", "获取分组失败"], "pages.qywx_manage.groups_messaging.create.components.choose_group_qrcode": ["选择群活码"], "pages.qywx_manage.groups_messaging.create.components.filters": ["支持多选，为或关系，包含任意一个标签则会被选中为发送对象"], "pages.qywx_manage.groups_messaging.create.components.sendContent": ["入群引导语：", "您可以填写邀请客户入群的引导语", "指定群聊：", "自定义入群邀请图片", "待加入客户群：", "上传群活码：", "管理员可以", "在企业微信后台或企微客户端创建群聊后再下载二维码，可以直接上传或设计排版后再上传", "待发送的群活码：", "过滤群内客户：", "开启后已在群聊中的客户将不会收到提醒", "下发群发任务到相关员工，员工点击群发后，下发群二维码给客户扫码进群，每日仅能群发一次", "请选择待加入客户群", "请上传群活码", "请选择待发送的群活码"], "pages.qywx_manage.groups_messaging.create.index": ["新建企业微信群发", "请选择海报", "新建企业客户群群发"], "pages.qywx_manage.groups_messaging.create.label_group_chat": ["新建标签群任务"], "pages.qywx_manage.groups_messaging.index": ["客户群发", "通过标签筛选及目标人群圈选对企业微信客户做定向内容推送，相关人员收到客户群发通知后，点击通知卡片确认发送内容到指定客户", "新建客户群发", "客户群群发", "给予群主对企业微信客户群做定向内容推送，相关群主收到客户群通知后，点击通知卡片确认发送内容到指定客户群", "新建客户群群发", "标签建群", "基于标签筛选客户后，给相关负责员工推送邀请任务，员工收到任务后，一键发送入群二维码给客户，客户扫码入群", "新建标签建群任务"], "pages.qywx_manage.groups_messaging.list.index": ["搜索群发内容"], "pages.qywx_manage.groups_messaging.list.table_tools": ["准备发送", "群发类型"], "pages.qywx_manage.infomation.index": ["配置信息", "企业简介", "客户联系Secret", "连接企业微信自建应用配置信息", "应用名称", "如何使用", "客户营销动态", "客户群动态"], "pages.qywx_manage.init.index": ["联系产品顾问", "功能详细介绍", "扫码授权安装代开发应用", "扫码联系顾问下发代开发应用", "配置营销助手侧边栏", "请在企业微信中用管理员身份扫描以上二维码", "安装前请先查看", "企微绑定说明", "已授权安装完成，下一步", "请扫码联系营销顾问进行应用开发配置，顾问回复配置完成后，点击下方确认按钮", "确认应用已完成，下一步", "管理员登陆企业微信管理后台进入", "【客户与上下游", "聊天工具", "聊天工具栏管理", "进入配置】", "点击左上角新增配置应用页面，在自建应用中选择", "【营销助手】", "，进行下一步配置", "页面名称：营销助手", "页面内容：选择自定义后复制下方地址", "已配置完成，进入管理", "扫码后创建对象", "请先扫码完成应用安装授权", "应用未配置完成，请联系顾问检查", "用户数据同步中，请稍等", "链接企业微信与微信", "微信社交营销，精确触达目标客户人群，助力企业获取更多潜在客户与转化", "与CRM互通，全流程服务企业终端客户"], "pages.qywx_manage.init.link_qywx_setting": ["连接企业微信，需要在企业微信获取以下信息并完成配置：", "企业ID：", "营销通关联到企业微信的小程序信息配置："], "pages.qywx_manage.promote_qrcode.components.spread_detail": ["添加好友设置：", "备注："], "pages.qywx_manage.promote_qrcode.create.cpq_form": ["员工活码名称", "使用人员", "选择使用人员", "添加设置", "客户添加时无需经过确认，自动成为好友", "添加好友后打标签", "启用自动回复消息功能，必须要关闭企业微信的自动回复功能。", "请选择类型", "渠道区分长度在 1 到 30 个字符", "备注长度在 0 到 30 个字符", "使用人员不能为空", "创建企微员工活码失败，查看可能", "请输入备注内容", "自动备注名", "开启后，可以在好友名称前自动备注个性信息，方便查看客户来源", "请输入备注内容，由于企业微信限制备注名称仅最多20个字，若客户昵称+备注内容超长时，默认不拼接。", "备注名称示例：云栖大会"], "pages.qywx_manage.promote_qrcode.create.index": ["员工活码示例：", "点击保存"], "pages.qywx_manage.promote_qrcode.index": ["搜索员工活码名称"], "pages.qywx_manage.promote_qrcode.list.table_tools": ["添加好友自动打标签", "添加客户数", "通过该活码添加的所有企微客户数", "新添加客户数", "之前未被其他员工添加，并通过该活码新添加的企微客户数。例如活码添加了客户A，但此前客户A已扫了其他企微码被添加为企微客户，则不统计在新添加客户数中", "添加好友设置", "无需验证", "需要验证"], "pages.qywx_manage.promote_qrcode.qrcode_dialog": ["选择吸粉二维码"], "pages.qywx_manage.welcome_lang.components.sendContent": ["您好", "这是我的名片", "请惠存", "欢迎语名称：", "使用员工：", "每位客户的朋友圈每个月最多展示4条企业发表内容", "编辑传入回显数据", "发送文本"], "pages.qywx_manage.welcome_lang.components.vmsg_conts": ["仅开通员工名片才会触发自动推送名片动作"], "pages.qywx_manage.welcome_lang.info.index": ["客户添加员工企微好友时自动发送欢迎语，支持自动发送员工名片信息，方便客户及时通过手机号等方式联系。由于在企微后台、员工活码、欢迎语都可以配置自动回复，但是企微限制只会推送一次，若同一个员工在多处设置自动回复后，回复优先级：", "企微后台", "；若是在同一个配置入口配置多条则以最后创建的规则做推送。"], "pages.qywx_manage.welcome_lang.list.index": ["好友欢迎语列表", "已取消删除"], "pages.qywx_manage.welcome_lang.list.table_tools": ["欢迎语内容", "使用员工"], "pages.report.index": ["企微消息群发数：在企微营销中创建的群发任务数，包括客户群发、客户群群发、标签建群场景下的所有群发数；", "收到群发员工人数：群发任务下发涉及的员工人数；", "确认群发员工人数：群发任务下发给对应员工后，员工有点击确认发送过的人数；", "送达客户数：员工确认发送后消息送达到的客户数，多次送达按一次计算；", "企微消息群发数", "收到群发员工人数", "确认群发员工人数", "送达客户数", "收到群发数", "已群发数", "群发率", "员工群发排行", "朋友圈任务数：在企微营销中创建的朋友圈任务数；", "收到任务员工人数：朋友圈任务下发涉及的员工人数；", "已发布员工人数：朋友圈任务下发给对应员工后，员工有点击确认发布到朋友圈的人数；", "触达客户数：员工发布到朋友圈后，可以看到此朋友圈的客户数；", "朋友圈任务数", "收到任务员工数", "已发布员工人数", "已发布数", "发布率", "员工发布排行"], "pages.report.analysis.index_backup": ["推广列表"], "pages.report.analysis.index": ["搜索内容标题"], "pages.report.detail.datas": ["转发人数", "排序"], "pages.report.detail.index": ["推广内容：", "获取表单线索明细"], "pages.report.detail.table": ["关联营销活动"], "pages.report.employee.index": ["当前公司还有", "人未开通客脉，", "主属部门", "是否邀请开通"], "pages.report.forward.const": ["（1）转发次数：打开内容后，在分享入口有点击过任意按钮（分享给微信好友、朋友圈海报）主动转发的用户总人数。", "（2）被转发次数：用户转发内容给其他用户后，被其他用户再次转发的总次数。", "（3）被转发人数：用户转发内容给其他用户后，被其他用户再次转发的总人数。", "（4）访问次数：用户主动转发的内容，以及被其他用户再次转发的内容，累计被访问的总次数。", "（5）提交表单数：用户主动转发含有表单的内容给其他用户后，其他用户提交信息的总条数。", "被转发次数", "被转发人数", "按转发次数", "按被转发次数", "按被转发人数", "按访问次数", "按提交表单数", "降序显示", "升序显示"], "pages.report.forward.index": ["用户转发排行", "搜索用户姓名"], "pages.report.partner.const": ["发起伙伴推广数", "参与推广企业数", "互联对接企业名称", "参与推广对接企业员工数"], "pages.report.partner.index": ["伙伴推广排行"], "pages.report.summary.components.member_range_selector": ["全公司"], "pages.report.summary.const": ["员工推广人数：收到任务后，在各个分享入口有点击过任意按钮（分享、转发、下载、复制等）的员工人数；", "员工推广次数：在各个分享入口点击过按钮（分享、转发、下载、复制等）的员工总人次数，一个员工点击多次会累计计算；", "访问人次：员工和客户访问内容的总次数，包括员工在转发内容时的访问次数；", "转发人次：包含员工推广的次数加分享出去后员工和客户点击内容右上角分享按钮的总次数；", "提交表单数：员工转发含有表单的内容给客户后，客户提交信息的总条数。", "收到任务数", "已推广任务数", "推广率", "累计推广次数"], "pages.report.summary.index": ["员工推广排行"], "pages.setting.SetSingleCardInfo.SetSingleCardInfoForm": ["建议尺寸750*750像素，支持png、jpeg、jpg格式，最大不能超过1M"], "pages.setting.channel.components.CardColorPicker.index": ["设置名片背景颜色", "名片预览效果：", "请选择颜色"], "pages.setting.channel.components.contact_me_setting": ["联系我设置", "启用联系我功能", "开启后，名片访客可通过点击【联系我】按钮在微信端与员工进行互动，默认员工通过纷享销客企信中的【客脉工作台】处理客户留言与客户互动。", "已连接企业微信，默认已为所有员工切换企业微信联系我模式", "客户点击【联系我】向员工发送添加企业微信好友请求，", "添加好友后访客通过微信聊天与员工进行沟通", "使用企业微信和纷享销客的员工优先选择", "在纷享销客企信处理微信名片留言", "在企业微信与客户沟通", "关闭后名片访客无法和员工进行沟通，确定要关闭吗？", "客户点击【联系我】时展示当前员工企微二维码，客户长按识别后可以加员工好友直接微信沟通，已经是好友的可以直接发起沟通", "通过添加企微员工好友沟通"], "pages.setting.channel.display_setting": ["暂不支持预览", "员工名片导航菜单设置", "编辑名片", "名片背景颜色设置", "名片上显示公司logo", "上传公司logo", "建议尺寸200*50像素", "恢复默认", "确认选择该名片模板？", "确认在名片上显示公司logo吗？", "确认关闭名片上公司logo选项？", "名片背景设置", "建议尺寸686*400像素", "名片文字图标颜色", "名片默认头像", "上传公司LOGO", "当前模板为默认名片，若不同部门需展示不同名片样式，可单独新建模板", "去创建名片模板", "名片适用部门", "出错了", "分享名片样式"], "pages.setting.channel.index": ["名片列表", "当前推广员工通讯录已切换成企微通讯录，请前往", "当前推广员工通讯录已切换成纷享通讯录，请前往", "员工列表", "名片展示设置"], "pages.setting.channel.qywx_employee": ["相关说明：", "2、由于企微开始对接口收费，【营销助手】应用首次安装后前3个月可以免费使用，到期后需要购买员工接口账号配并激活后才可以正常使用，未激活的员工账号无法接收任务通知及同步当前员工客户、客户群数据等。", "3、由于从企微接口无法直接获取员工手机号，无法自动匹配企微员工与CRM员工身份，需要员工主动授权手机号后自动匹配或者手动匹配，身份绑定后企微客户及客户群数据遵循CRM标准数据权限，支持报表查询。", "前往手动匹配", "4、可以统一设置员工名片对外展示资料（公司名称、公司介绍视频、图片等），在员工开通名片时自动应用。点击更新名片可选择直接覆盖更新或补充更新到已开通员工的名片资料中。", "按员工姓名搜索", "邀请开通名片", "更新名片", "同步员工数据", "清空条件", "设置筛选", "员工激活状态", "CRM账号绑定状态", "授权过期时间", "已激活", "未激活", "已绑定", "名片加企微好友数", "激活时间", "过期时间", "身份绑定状态", "身份绑定时间", "同步选中员工的企微客户"], "pages.setting.channel.open_statistic": ["员工名片对外展示信息", "统一设置员工名片对外展示资料（公司名称、公司介绍视频、图片等），在员工开通名片时自动应用。修改后可选择直接覆盖更新或补充更新到已开通员工的名片资料中。", "已开通：", "未开通：", "开通状态", "您还未设置员工名片对外展示信息，请先完成员工名片设置", "您还未设置员工名片对外展示信息，是否完成设置后再邀请员工开通小程序名片", "继续邀请", "导出中，请稍后", "数字人"], "pages.setting.company_activity.index": ["活动中心预览"], "pages.setting.company_content.index": ["企业内容预览"], "pages.setting.company_website.index": ["官网预览", "以下为示例", "您还没有官网", "马上创建", "设置官网微页面设置", "公司官网页 mounted", "您还没有配置官网页面，无法开启，是否创建官网"], "pages.setting.mankeep.index": ["人脉设置", "客户设置"], "pages.setting.miniapp_setting.MenuEditing": ["菜单名称", "菜单图标", "选择图标", "请选择跳转的微页面", "页面路径", "保 存", "请输入菜单名称", "请选择菜单图标", "请设置跳转类型", "请设置跳转的微页面", "请设置跳转的小程序AppId", "旧版", "产品推广（旧版）", "暂时不支持web预览", "请输入网页链接", "仅支持", "协议头的网页链接", "不允许设置第一个选项为跳转网页链接或者跳转小程序"], "pages.setting.miniapp_setting.base_setting": ["授权成功，已升级为企业专属小程序，企业微信链接已失效，需要更新小程序链接信息才可正常使用企业微信营销，", "马上更新", "（第一个默认为首页，最多4个导航栏目）", "还未购买此服务，如需详细了解或购买，可联系专属产品顾问"], "pages.setting.miniapp_setting.icons": ["通知消息", "我", "公司/工作", "资讯/培训"], "pages.setting.miniapp_setting.index": ["基本设置", "支付设置"], "pages.setting.miniapp_setting.set_mp_config": ["企业微信小程序配置", "请输入AgentId"], "pages.setting.setitems.SetitemsChannelManage.const": ["基于微信服务号，沉淀微信粉丝，进行培育转化", "绑定", "企业品牌小程序", "智能名片&小程序官网，让每一个员工都是企业品牌传播者", "直接触达微信客户/客户群，精细化运营私域流量", "连接", "接入SendCloud邮件服务商账号，进行精准邮件营销", "接入", "开通纷享短信服务，群发营销短信与通知", "跟踪官网访客行为，统计官网线索，识别官网线索来源", "渠道值"], "pages.setting.setitems.SetitemsChannelManage": ["营销触点接入", "添加推广内容推广渠道，在内容推广时获取内容对应渠道二维码与链接，进行线索来源渠道追踪", "内容推广渠道", "添加推广内容推广渠道，在内容推广时获取内容对应渠道二维码与链接，进行线索渠道追踪", "添加推广渠道", "渠道名不能为空", "确认删除渠道【"], "pages.setting.setitems.ad_set_const": ["维度", "含义", "广告系列活动、活动名称", "用于区分广告，主要使用A/B-test和按内容定位广告，标记广告细微差别", "营销媒介，CPC、EDM、Banner", "搜索引擎渠道来源，如百度", "广告关键词，主要使用SEM"], "pages.setting.setitems.ad_set": ["百度广告账户绑定管理", "绑定百度广告账户", "巨量引擎广告主账号授权管理", "绑定头条广告账户", "推广链接UTM参数市场活动设置", "营销通会默认推广链接中所有带有的以下参数进行解析，并将信息一并存入销售线索", "自动根据utm_campaign创建【广告营销】类型的市场活动", "确定解绑？", "解绑失败", "确定启用？", "头条"], "pages.setting.setitems.adviser_setting": ["通过调用公众号能力，提供员工直接联系微信客户的沟通能力，每个员工将产生独立营销顾问码，微信潜客扫码后即可提供一对一专属沟通服务。", "专属顾问昵称", "专属顾问头像", "上传头像", "图片格式支持：png、jpg、jpeg，大小不超过1M", "设置自动回复", "（关注过该公众号的粉丝扫码后才能收到回复）", "营销顾问设置", "请设置可见范围", "请设置专属顾问头像", "请设置自动回复", "图片格式只能是png、jpg、jpeg！", "图片大小不能超过1MB！", "专属顾问"], "pages.setting.setitems.base_set": ["支持png、jpg、ipeg，建议尺寸300*300像素，最大不超过2M", "启用二维码logo", "产品顾问", "纷享销客通讯录", "企业微信应用可见范围", "钉钉应用可见范围", "纷享销客APP应用通知", "企微小程序应用通知", "企微H5（营销助手）应用通知", "H5内容访问授权设置", "内容（微页面、文章、产品、会议落地页等）以H5形式推送、转发分享到微信时，可以基于微信公众号主动或自动授权获取当前微信用户身份，用于统一用户行为记录及展示。其中采用静默授权时，仅能获取用户openid，但是相较于不开启授权，首次访问页面加载速度会慢约1秒，主动授权需要客户点击授权头像昵称后才能访问对应内容，但是可以获取用户头像、昵称等更多信息，也可以用于统一识别小程序用户与企微客户身份。", "查看授权说明", "公众号授权方式：", "无公众号授权", "公众号静默授权", "公众号主动授权", "授权公众号：", "授权说明", "不开启授权或开启静默授权不阻断客户访问，开启主动授权后，非粉丝用户需要主动授权头像昵称才可以进入访问具体内容，见下图示例，若用户已经是粉丝则与开启静默授权体验一致。", "请选择授权公众号", "支持png、jpg、jpeg，建议尺寸300*300像素，最大不超过2M，用于二维码分享。", "200*50像素", "支持png、jpg、jpeg，建议尺寸200*50像素，最大不超过2M，在微信名片、直播、会议、文章、产品、微页面的朋友圈海报可展示。", "企微专属域名", "将营销通内容链接升级成企业专属域名，帮助企业更好的建立品牌形象、提升用户信任度、提高线索转化"], "pages.setting.setitems.clue_set": ["分销线索审核", "开启审核，分销人员推荐的线索均需由分销管理员进行审核，审核通过才可存入销售线索。", "（开启后，可对表单存入线索的数据进行全局性的通用设置，无需在各个表单中进行映射）", "开启后，可对表单存入线索的数据进行全局性的通用设置，无需在各个表单中进行映射）", "映射内容", "此字段默认获取用户当前浏览器、设备类型等信息，映射后可用于线索来源分析等，获取信息示例如下：Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "市场活动负责人", "指定处理人员"], "pages.setting.setitems.components.crm_mapping_dialog": ["设置53快服线索到CRM销售线索的数据映射"], "pages.setting.setitems.components.pay_set_dialog": ["微信支付配置", "温馨提醒：如果自有小程序没有开通微信支付请勿配置此页内容", "支付商户号", "商户密钥", "商户v3秘钥", "证书序列号", "API安全，“申请API证书”栏目中，点击管理证书，获取长度为40位的证书序列号。", "API证书", "API安全，查看或设置。上传 apiclient_cert.p12 文件。", "解密文件", "API安全，查看或设置。上传 apiclient_cert.pem 文件。", "秘钥文件", "API安全，查看或设置。上传 apiclient_key.pem 文件。", "请输入支付商户号", "请输入关联的AppID", "请输入商户秘钥", "请上传API证书", "请上传解密文件", "请上传秘钥文件"], "pages.setting.setitems.components.seting_map_diaog": ["添加活动类型到活动营销场景应用范围", "该活动类型已被其他营销场景使用，请先移除后再设置"], "pages.setting.setitems.components.staff_authority_dialog": ["请权限范围"], "pages.setting.setitems.components.staff_authority": ["纷享APP端：不设置情况下默认上级看下级，下级看自己的推广统计结果，设置后，在推广简报中仅能查看权限范围内数据", "新增指定员工数据权限", "指定数据权限范围", "数据权限范围？", "多组织数据隔离", "营销通数据权限说明"], "pages.setting.setitems.components.user_role_dialog": ["使用角色分配", "请分配角色", "权限详情说明", "权限范围", "仅能圈选以下部门或组织的数据做营销运营及员工营销时仅能选择以下部门员工下发推广任务", "CRM部门不能为空", "对接企微后员工营销，企微群发、朋友圈群发等场景时仅能选择以下企微组织架构中的部门人员", "企微部门不能为空", "渠道账号分配", "指定部门", "（百度）", "（头条）", "（腾讯）"], "pages.setting.setitems.const.pre_crm_kf": ["访客名称", "搜索词", "着陆页", "咨询页", "对话渠道", "工号", "访客ID"], "pages.setting.setitems.const.pre_crm_xiaoe": ["请输入手机号码", "信息采集手机号", "请输入信息采集手机号", "真实姓名", "国家", "微信邮箱", "行业"], "pages.setting.setitems.customize_object": ["营销用户范围设置", "营销通默认以CRM中客户、联系人、销售线索、企业微信客户、微信用户、会员六个对象作为营销触达用户，除此之外还可以新增其他CRM自定义对象进行短信或邮件营销触达", "请选择需要新增的自定义对象（选中对象中需包含手机号字段，用于身份统一识别及营销触达）", "配置字段映射（手机为必须配置项）", "未选择自定义对象", "删除此配置后，新建目标人群无法选择自定义对象，历史创建的目标人群不受影响", "营销用户设置", "自定义营销用户对象", "自定义营销对象：营销通默认以CRM中客户、联系人、销售线索、企业微信客户、微信用户、会员六个对象作为营销触达用户，除此之外还可以新增其他CRM自定义对象进行短信或邮件营销触达", "营销用户关联【客户对象】", "营销用户作为一个自然人身份，通过手机号、unionid等", "身份合并规则", "自动打通各营销对象（线索、联系人、微信用户、企业微信客户等）的身份，统一用户行为及标签。在B2B企业运营场景中，【客户】对象存储企业信息，不适合关联营销用户身份。但是在B2C企业运营场景中，【客户】对象存储的是自然人信息，可以与营销用户身份合并关联，开启开关后，营销用户身份关联时会自动关联【客户】对象。", "营销用户身份关联时，历史标签同步到关联营销用户对象", "开启此开关后，", "多渠道用户身份合并", "时，会将历史所有关联身份数据的标签全部统一。如用户A，在系统中有粉丝身份W1，此身份上有T1、T2两个标签，也有线索身份L1，有标签T2、T3，当A在公众号提交表单后，自动合并营销用户身份，此时会将粉丝和线索标签全部统一取并集，此时两条数据都会打上标签T1、T2、T3"], "pages.setting.setitems.domain_set": ["配置企业域名，获取带有企业域名的内容推广链接，进行广告投放或推广，帮助企业更好的建立品牌形象、提升用户信任度、提高线索转化", "示例：www.fxiaoke.com，无需http：//等域名前缀", "CNAME配置", "前往域名DNS服务商处进行CNAME解析", "设置HTTPS", "证书（crt文件）", "私钥（key文件）", "请输入企业域名", "请输入正确的企业域名，完整示例：www.fxiaoke.com"], "pages.setting.setitems.entinfosetting": ["是否启用二维码logo"], "pages.setting.setitems.form_set": ["新建线索表单"], "pages.setting.setitems.founder": ["各场景中产生线索的创建人默认设置", "通过营销通获取的线索的创建人默认为推广人（比如客户通过A销售分享朋友圈，提交了报名信息产生的线索，此线索推广人就是A销售），若无推广人，则创建人默认是市场活动（活动营销、直播营销、会议营销）的负责人。除此外还有部分场景既无推广人也无市场活动负责人，可以指定具体创建人，若不指定，则线索中创建人字段默认存入的是“系统”。", "更改", "官网表单获取的线索", "广告同步的线索", "客服系统对接同步的线索", "其他所有无创建人的线索"], "pages.setting.setitems.index": ["权限管理", "安全管理", "市场活动设置"], "pages.setting.setitems.mail_set": ["发信域名", "发信域名配置", "邮件设置"], "pages.setting.setitems.mail_user": ["设为默认", "是否同时创建为回复人", "添加发件人", "发件人名称", "发件人地址", "确认删除该", "添加回复人", "回复人名称", "回复人地址"], "pages.setting.setitems.marketing_plugin.index": ["升级开通", "前往上传", "配置收款账号", "对接配置", "53快服对接映射", "在配置53快服推送信息前，请务必先完成线索映射配置", "查看映射", "1）登录管理后台进入客服系统>功能扩展>应用对接开发>推送配置>开发者账号，打开开发者账号开关，点击保存更改；", "2）切换tab栏到推送配置，开启推送设置开关；", "3）配置URL（服务器地址）：", "4）Token（令牌）：", "5）选中聊天记录不推送，启用访客信息推送，保存刚更改", "保利威直播配置", "在配置前，请先确保您的企业已经开通了保利威直播服务，需要登录保利威后台获取相关配置信息", "注册保利威直播", "登录保利威直播后台，访问【云直播】-【开发者设置】-【开发者信息】获取相关设置信息", "userId（账号ID）", "AppId（应用ID）", "AppSercet（应用密钥）", "请输入AppSecret", "小鹅通直播配置", "第一步：", "注册小鹅通直播", "创建并授权应用，创建方式请参考", "开发指南-创建并授权应用", "第二步：", "创建应用后，登陆小鹅通", "获取应用信息（路径：【小鹅通店铺管理台】-【营销中心】-【API自主开发】-【云服务控制台】），并配置如下白名单：", "一键复制", "第三步：", "，进入对应应用管理页面，申请开通以下权限集（路径：【应用控制台】-【权限管理】-【权限集管理】）", "第四步：", "相关设置信息，并填写于下方：", "app_id（授权店铺ID）：", "请输入app_id", "client_id（应用标识）：", "client_secret（应用秘钥）：", "请输入client_secret", "您当前使用的是营销通标准版，请升级到专业版开启该插件功能", "请联系您的专属客服升级吧！客服热线：", "53快服线索接入映射配置", "小鹅通线索接入映射配置", "微信视频号配置", "1.使用前需先升级为", "企业专属小程序", "2.小程序和视频号需为同一主体。若小程序与视频号的主体不同，需同时满足以下3个条件：", "小程序绑定了微信开放平台账号", "小程序与微信开放平台账号的关系为同主体或关联主体", "微信开放平台账号的主体与关联主体列表中包含视频号的主体，可参考", "关联主体申请流程", "视频号ID", "如何获取视频号ID？", "视频号名称", "视频号头像", "如何获取视频号ID", "获取视频号ID的需要登陆视频号助手，在首页可以查看自己的视频号ID", "图片分组展示设置", "以下选中的图片分组将于移动端和侧边栏展示", "图片库分组", "移动端展示分组", "外部系统对接配置", "整体流程：活动中配置外部推广二维码海报并下发推广任务", "员工推广相关内容", "客户访问外部内容", "系统回传外部推广数据", "查看推广转化详情", "第一步：二维码生成接口配置", "二维码生成地址：", "第二步：数据回传映射", "非必须项，有较强开发能力的客户可以将外部系统的后续事件回传到CRM自定义对象，并关联到活动详情页中展示，记录外部访问、转化、注册、下单情况", "查看数据回传接口指引", "请输入id", "请输入name", "请上传头像", "您正在使用营销通标准版，升级专业版，使用伙伴营销。客服电话 400-1122-778", "请输入二维码生成地址", "请输入正确的地址", "插件详情", "您还未配置收款账号，是否前往配置？", "您还完成对接设置，是否前往配置？", "请先配置保利威直播", "是否开启腾讯广告功能插件，并前往完成广告设置？", "当前有启用中的广告账户，请停用所有广告账户后再关闭腾讯广告功能。", "请先配置小鹅通直播", "请先配置微信视频号", "使用前需先升级为专属小程序", "官网客服插件埋码中追加u_cust_id及u_cust_name参数，用于打通官网访客和53快服产生的线索身份，埋码前请先埋营销通", "官网SDK", "查看53快服", "埋码说明", "登录管理后台进入客服系统>功能扩展>应用对接开发>推送配置>开发者账号，打开开发者账号开关，点击保存更改；", "切换tab栏到推送配置，开启推送设置开关；", "配置URL（服务器地址）：", "Token（令牌）：", "选中聊天记录整体推送，启用访客信息推送，保存更改，并更新最新图片，现有图片标记错误", "选中聊天记录整体推送，启用访客信息推送，保存更改", "在53快服新增名片时查看是否有线索推送", "跳转查看", "标签操作", "订单管理", "查询订单信息", "直播管理", "查询直播信息", "学习数据", "查询学习记录", "用户管理", "查询用户标签", "查询用户信息", "用户操作", "360", "linkedin", "facebook", "微吼线索接入映射配置", "请先配置微吼直播", "1）", "2）", "3）", "4）", "5）", "6）", "7）", "请联系客户经理开通linkedin连接器。", "请先开启上方营销场景中的【直播营销】插件开关", "请先开启上方营销场景中的【广告营销】插件开关", "【请联系客户经理下单试用】", "您当前使用的是营销通标准版，请升级到旗舰版开启该插件功能", "通讯录设置"], "pages.setting.setitems.marketing_plugin.plugin_map": ["广告对接", "可接入百度广告账户，监控广告投放效果：曝光、点击、消费、线索产出，对广告投放进行端到端的投放ROI分析。", "是否确定开启百度广告功能插件？", "是否确定关闭百度广告功能插件？", "可接入腾讯广告账户，监控广告投放效果：曝光、点击、消费、线索产出，对广告投放进行端到端的投放ROI分析。", "是否确定开启腾讯广告功能插件？", "是否确定关闭腾讯广告功能插件？", "可接入头条广告账户，监控广告投放效果：曝光、点击、消费、线索产出，对广告投放进行端到端的投放ROI分析。", "广告效果数据回传", "通过上报客户深度转化标记到广告投放平台，大数据会找到更多精准用户，提升线索有效率和降低成本。", "线索收集", "启用后将开通连接器，完成配置后，可通过与知乎营销画报对接收集线索。", "启用后将开通连接器，完成配置后，可通过与搜狗线索通对接收集线索。", "快手", "启用后将开通连接器，完成配置后，可通过与快手线索CRM对接收集线索。", "启用后将开通连接器，完成配置后，可通过与UC神马建站工具对接收集线索。", "直接前往CRM，通过线索模板直接上传导入360线索。", "直播对接", "保利威直播对接", "完成配置后，在创建直播时支持对接保利威直播，系统自动打通直播报名流程，同步客户观看、回放、互动数据。", "小鹅通直播对接", "完成配置后，在创建直播时支持对接小鹅通直播，系统自动打通直播报名流程，同步客户观看数据。", "微吼直播对接", "完成配置后，在创建直播时支持对接微吼直播，系统自动打通直播报名流程，同步客户观看、回放、互动数据。", "微信视频号对接", "启用并完成配置后，在创建直播时支持使用视频号直播，同时在支持在微页面中使用视频号组件和后动作", "营销通路", "渠道营销解决方案，企业（上游）可将营销内容下发给到合作伙伴（下游）进行推广，并查看伙伴推广完成情况。", "线索型社会化分销解决方案。社会分销员（非员工）自己或招募其他分销员推荐线索，可以获得线索成单奖励。", "营销应用", "电子优惠券解决方案，可实现优惠券生成、下发、领取、核销的闭环和管理操作，还可增强营销归因能力。", "1. 启用优惠券功能后，预设微信商户券，优惠券，用户券等对象。", "2. 一旦开启，将无法关闭。", "企业钱包", "提供安全便捷企业级支付服务，支持快捷支付、收款、查询对账等功能，结合微页面可以用于付费报名、商品购买等场景。", "启用后将于移动端和企微侧边栏展示图片库，设置完要展示的图片分组后，员工可自由分享展示分组内的图片给用户。", "外部推广码对接", "对接外部系统的内容进行推广，比如动态生成外部商品二维码，推广后跟踪后期访问、注册、下单等全链路数据，精准评估活动价值", "客服一体化", "53KF云客服线索对接", "可同步53KF客服的官网咨询、留言线索进入CRM，自动打通官网访客身份，关联来源百度广告投放计划、关键词。", "企微话术库", "启用后将开通话术库，可配置不同场景下规范化的聊天话术来进行快捷回复，提升用户体验与沟通运营效率。", "营销服一体化", "订货推广", "打通渠道订货与营销一体化链路，借助营销通的活动营销、微页面、发放优惠券等能力，推广渠道商品或活动、拓展经销通路，赋能经销商或门店，助铺货，助动销，提升渠道销量", "可在营销助手查询知识库内容，分享知识档案给客户。", "微信商家券是微信支付为商户提供的电子优惠券解决方案。", "企业营销用户组件", "在CRM【企业库】【客户】对象详情中，新增【营销用户】栏，以OneID身份展示该企业的关联人员信息，可以快捷查看相关人员行为轨迹，洞察相关联系人关键行为", "在CRM【企业库】对象详情中，新增【营销用户】栏，以OneID身份展示该企业的关联人员信息，可以快捷查看相关人员行为轨迹，洞察相关联系人关键行为", "是否确认开启企业营销用户组件", "是否确认关闭企业营销用户组件", "目睹直播对接", "完成配置后，在创建直播时支持对接目睹直播，系统自动打通直播报名流程，同步客户观看、回放、互动数据。", "启用后将开通连接器，完成配置后，可通过与linkedin广告表单对接收集线索。", "启用后将开通连接器，完成配置后，可通过与facebook广告表单对接收集线索。", "google广告", "可接入google广告数据，监控广告投放效果：曝光、点击、消费、线索产出，对广告投放进行端到端的投放ROI分析。", "接入纷享【在线客服】能力，SDR一站式处理客户在线咨询及需要回呼的线索，清洗识别后完成线索分配并跟踪线索转化情况", "linkedin广告", "可接入linkedin广告数据，监控广告投放效果：曝光、点击、消费、线索产出，对广告投放进行端到端的投放ROI分析。", "AI插件", "通过ShareGPT自动生成高质量营销文案和图片，富文本AI邮件助您轻松沟通，SEO优化建议提升网站排名，提升营销效率。", "营销数据隔离", "支持多分子公司、多独立产品线营销业务数据（营销用户、内容、渠道账号、报表）等隔离，集团或总部统一运营管理，一套营销通支持整个集团业务", "是否打开营销数据隔离", "关闭后营销业务数据将不再相互隔离，请谨慎操作", "企业线下会议数字化管理，从会议策划、内容制作、邀约客户、推广会议、在线报名、报名审核、现场签到全流程数字化管理 ，比如参加展会、自办大型线下发布会、生态伙伴大会等", "对接保利威、微吼、目睹、小鹅通、视频号主流直播平台，支撑在线沙龙、大型发布会、线上培训课等场景的前中后全流程闭环管理，追踪活动转化效果", "国内外主流广告平台对接，同步广告平台计划、单元、关键词，展点消及线索数据，端到端评估广告ROI，助力投放优化", "官网营销", "接入企业官网，搭建官网内容中心，跟踪官网线索来源，追踪官网潜在客户关键行为，掌握潜在客户的兴趣与意向", "托管企业自有微信小程序，个性化拖拉拽搭建小程序内容，承载活动中心、资料中心、个人中心等，连接品牌与终端用户，进行获客转化", "市场人员设置统一推广物料及话术，向公司员工发送推广任务进行活动宣传推广，员工可在微信群和微信朋友圈中进行推广传播，追踪员工推广效果", "构建基础会员身份体系，如会员查看专属内容，会员一键报名活动、下载内容等，同时无CRM账号员工、伙伴、客户等，注册成为推广会员后，品牌方可以下发推广通知到相关会员，并追踪推广结果", "群发通道", "市场营销管理人员可根据企业的业务场景灵活定制营销内容，向目标客户群批量发送短信，可以追踪短信中链接访问提交事件", "市场营销管理人员可根据企业的业务场景灵活定制邮件营销内容，向目标客户群批量发送邮件 ，可以追踪客户打开、点击链接、提交、取消阅、投诉等事件", "连接企业微信，提供员工活码、群活码、欢迎语能力，同步员工添加的微信好友及客户群，支持按标签、条件等对客户/客户群进行精准化的内容推送。", "市场人员可通过营销通公众号营销接入微信服务号，进行推广获客，拉新促活，精准推送营销内容，跟踪粉丝行为，提升粉丝转化率", "直播平台", "广告平台", "Facebook线索连接器", "知乎线索连接器", "搜狗线索连接器", "快手线索连接器", "神马线索连接器", "营销服一体化插件", "市场营销团队的协同办公工具，根据场景设置运营看板，制定活动计划、分配项目工作任务，实时把握活动推进情况", "小红书线索连接器", "启用后将开通连接器，完成配置后，可通过与小红书聚光投流对接收集线索。", "小冰数字人", "微软小冰专属数字人打造，开通后员工可在微信名片中训练自己的交互数字人名片", "巨量引擎", "可接入巨量引擎广告账户，监控广告投放效果：曝光、点击、消费、线索产出，对广告投放进行端到端的投放ROI分析。", "抖音来客线索连接器", "启用后将开通连接器，完成配置后，可通过与抖音来客对接收集线索", "会员计划", "企业为了鼓励和奖励顾客的持续购买行为而设计的一种营销策略。这些计划通常通过提供积分、折扣、优惠券、会员专享服务或其他形式的奖励来增加顾客的忠诚度，从而促使他们重复购买或使用企业的产品和服务。", "SDR智能工作台", "企业为了鼓励和奖励顾客的持续购买行为而设计的一种营销策略。这些计划通常通过提供积分、折扣、优惠券、会员专享服务或其他形式的奖励来增加顾客的忠诚度，从而促使他们重复购买或使用企业的产品和服务。", "海外营销", "Facebook广告", "可接入Facebook广告数据，监控广告投放效果：曝光、点击、消费、线索产出，对广告投放进行端到端的投放ROI分析。", "Tiktok广告", "可接入Tiktok广告数据，监控广告投放效果：曝光、点击、消费、线索产出，对广告投放进行端到端的投放ROI分析。", "Mailchimp连接器", "同步纷享销客CRM联系人数据到Mailchimp进行邮件群发，并同步Mailchimp的发送结果及客户行为到营销通中展示"], "pages.setting.setitems.marketingevent_set": ["营销场景与市场活动类型映射", "市场活动的活动类型：", "营销场景报名表单设置", "新建会议或直播活动时，可选择设置的模板生成报名表单", "添加报名表单模板", "默认", "设为默认模板", "设置表单线索默认映射", "市场活动业务组件布局", "支持按活动类型自定义配置活动详情页的其他关联信息展示栏，比如参会嘉宾，活动物料等，配置后对【活动营销】【会议营销】【直播营销】【广告营销】详情页面生效", "添加活动场景布局配置", "表单存入线索根据手机号合并设置", "活动营销、会议营销、直播营销中提交的表单信息，在CRM端未开启手机号查重规则时，在同一个活动中若存在同一个手机号多次提交情况下，默认仅存入一条销售线索，开启此配置后，将不会拦截重复手机号，全部存入销售线索。", "营销场景默认SOP设置", "自动按此处设置生成会议、直播场景的默认SOP", "添加场景SOP", "SOP名称：", "配置活动详情功能菜单", "拖动配置菜单是否在活动详情页展示以及调整菜单顺序", "主导航", "市场活动类型", "导航菜单", "确认将该表单模板设置为默认模板？", "（副本）", "新建失败", "确认删除该表单模版？", "确认删除SOP模版？", "新增失败", "编辑失败", "若有营销管控需求，如一次发送10W条短信，需要直属领导审核后才能发送，可以联系您的系统管理员给【市场活动】【营销活动】对象配置审批流程（营销活动对象管理短信、邮件、公众号、企微群发推送数据），只有审批通过才能执行营销推广操作，注意，以下开关仅在配置了审批流程后才生效。", "审批流程配置指引", "市场活动审核", "若对【市场活动】配置了审批流程，开启后，仅审核通过的市场活动才能进行推广，审核中或审核未通过的市场活动详情页中的推广按钮将置灰", "营销推广审核", "市场活动端到端分析", "视频号直播", "其他直播", "营销场景活动主页模版设置", "新建会议或直播活动时，可选择设置的模板生成活动主页", "添加预设模版", "是否需要新建一个预设的活动主页模版？", "活动形式与市场活动类型映射"], "pages.marketing_calendar.components.CreateActivity": ["多活动组合", "适合营销、自定义、优惠活动等各类活动", "线下活动的邀约、签到、互动等全流程", "公开课、直播、在线售货会等形式", "关联活动及其分会场，和时间同时排列上", "签约喜报、白皮书、优惠促销、客户定期关怀等常用的线上活动", "大型活动支持主分会场，也可同时支持线上直播"], "pages.setting.setitems.meeting_set_const": ["报名成功通知", "未开启审核，用户提交报名将发送此短信通知", "报名提交审核通知", "开启报名审核、用户提交报名将发送此短信通知", "审核通过提醒", "开启报名审核、用户报名审核通过将发送此短信通知", "审核未通过提醒", "开启报名审核、用户报名审核未通过将发送此短信通知", "欢迎参加 {Event_Title}，您已成功报名本活动，会议时间：{Event_Date}，会议地点：{Event_Location}，期待您的莅临。", "感谢您报名参加 {Event_Title} ，我们已收到您的报名信息，审核结果将以短信的形式进行通知，请耐心等候审核结果。", "恭喜您在 {Event_Title} 提交的报名资料已成功通过审核！会议时间：{Event_Date}，会议地点：{Event_Location}，非常期待您的参与。", "很抱歉，您提交的 {Event_Title} 报名未通过审核，感谢您报名参加本次会议。"], "pages.setting.setitems.meeting_set": ["短信发送渠道", "选择短信发送渠道：", "短信配额剩余", "去充值", "纷享销客短信通道与签名", "企业专属短信通道与签名"], "pages.setting.setitems.pay_set": ["请按以下步骤配置在线支付功能", "查看支付配置教程", "将自有小程序授权给纷享销客，并绑定营销通应用", "去授权", "自有小程序需要开通微信支付。（登录", "微信小程序后台管理", "认证", "完成后，获取到关联微信支付商户的信息：商户名称、商户号、密钥等，填入配置参数中。（登录", "微信支付商户平台", "产品中心", "开发配置）", "小程序名称", "「"], "pages.setting.setitems.permission_set": ["营销通市场活动、销售线索数据权限遵循平台业务数据权限，如需设置请前往系统后台进行设置", "添加使用成员", "角色", "角色名称", "角色描述", "角色权限", "确定删除该成员角色权限, 是否继续？", "删除失败，请重试", "CRM员工状态"], "pages.setting.setitems.pubplat_set": ["启用公众号头像昵称授权组件", "启用后，所有推广到公众号的H5内容，客户访问时首先会弹出用户授权框，授权完后可获取粉丝头像、昵称、姓名、地区等基础信息，若客户取消授权，也会进入内容，但无法获取相关信息", "1.推送到公众号或好友的H5内容", "2.点击后首次进入需要用户授权", "3.授权或取消授权都可以进入H5详情", "4.更新授权过的分析头像昵称"], "pages.setting.setitems.qywx_set": ["企微连接二维码", "手动同步客户", "同步通讯录", "第1步", "请在企业微信中用管理员身份扫描以下二维码", "第2步", "微信扫描以下二维码，添加产品顾问，安装代开发应用", "企业微信设置", "营销通关联到企业微信的小程序信息配置", "开始执行同步企业微信客户信息更新，确认后将会自动开始同步，一段时间后您可以在微信客户处查看客户更新", "同步执行中，请稍后前往微信客户处查看", "开始执行同步企业微信通讯录信息更新，确认后将会自动开始同步，预计需要几分钟时间", "同步执行中，请稍后查看", "营销助手侧边栏发送物料类型", "消息设置", "员工通讯录", "企微消息群发、欢迎语、朋友圈中员工选择器切换为以下通讯录，使用纷享通讯录可以按多级架构查看员工推广情况", "企微应用可见范围员工通讯录", "员工营销及互动行为通知方式"], "pages.setting.setitems.secure_set": ["手机号脱敏", "开启后，将对营销通内所有的表单收集的用户手机号、营销用户的手机号显示进行脱敏处理，用户无法查看手机号信息。", "脱敏前：", "脱敏后：", "开启后，营销通中所有手机号展示和导出时将会进行脱敏处理"], "pages.site.components.GroupManage": ["管理分组", "管理分类", "新建分类"], "pages.site.components.List": ["分组：", "使用对外标题", "外部名称："], "pages.site.components.SiteTypeSelector": ["选择微页面类型", "通用微页面", "不限定场景，公开使用", "通用微页面可用于多个市场活动", "活动微页面", "限定场景，只可在一个活动下使用", "快捷生成微页面", "支持快捷上传", "附件及设置其他信息", "从PDF快捷生成微页面"], "pages.site.components.TemplateList": ["空白页面"], "pages.site.config": ["关键数据", "最后更新时间"], "pages.site.design_old": ["请注意，使用该表单必须升级使用自有小程序，", "去升级", "如果升级使用自有小程序，请完成", "站点名修改失败，请重试"], "pages.site.hexagon_ex_article_component.index": ["请选择文章"], "pages.site.index": ["新建模版", "将同时复制此页面的全部设置项，请注意检查更新", "设置分组失败", "名称不能同名", "请先选中左侧某个分组后再新建，其中系统预设分组【全部】【未分组】【我创建的】不支持选择", "支持将表单数据CRM对象中，必填字段需要有映射字段，如果字段为空时会导致存入失败", "表单数据存入", "设置对外标题", "请输入外部标题"], "pages.site.page_designer.ActionSlots": ["函数入参：", "使用规则：", "1. 创建自定义函数时，参数类型必须为Map且名称必须是Data。", "2. 函数入参有表单数据、页面URL上所携带参数和上一次自定义函数执行结果，自定义函数内可通过【Data.表单字段名】取表单提交数据，【Data.functionAdditionalMsg】集合内字段为页面URL参数和上一次自定义函数执行结果。 ", "3. 函数返回值为Map类型并自动存入微页面全局缓存，可嵌入页面显示。如返回值Map里含有title字段，则使用方式如下：在文本输入区直接输入", "4. 返回值Map里jumpPage为预留字段，作为表单提交后跳转页面名称。", "用户可通过小程序私信功能与指定员工互动", "私信功能仅支持在专属小程序使用，", "前往升级", "参数值", "添加参数"], "pages.site.page_designer.Designer": ["请前往【系统设置", "营销插件】开通【企业钱包】插件后使用", "保存中...", "已保存", "选中内容默认按创建时间倒序排序，需要对内容排序，请前往内容中心统一进行置顶排序", "排序规则", "推广中心"], "pages.site.page_designer.mixins": ["立即登录", "活动已结束"], "pages.site.site_ad": ["如何玩转微页面？", "推荐模板"], "pages.site.site_info_dialog.index": ["请输入微页面名称，限100字", "请输入微页面名称"], "pages.site.site_setting_dialog.campaign_member_dialog": ["表单数据存入活动成员", "仅支持同步提交信息到活动成员自定义字段上，比如房间号、座位号等，若有需求请联系CRM管理员新增相关字段。必填字段需要有映射字段，如果字段为空时会导致保存失败。"], "pages.site.site_setting_dialog.content": ["客户提交的表单信息首先会存入纷享销客CRM线索中，同时也会同步到钉钉【客户管理>客户>个人客户】中，可前往钉钉客户管理查看获取的客户信息", "线索存入成功后自动创建会员", "设置活动成员映射", "给访问微页面的用户添加标签", "自动识别会员身份", "数据存入场景", "收集线索信息：数据存入CRM的销售线索中，后期进行分配处理", "收集其他信息：数据存入CRM任意业务对象，比如收集合作伙伴信息、售后申请信息、调查问卷等", "其他信息存入设置", "请选择需要存入的对象", "设置数据存入映射", "请选择存入对象", "自动识别并填充会员基础信息（姓名、手机、邮箱、公司、职务）到表单，用户仅填写额外信息即可", "自动识别会员身份，跳过表单填写，无法收集额外信息", "不自动识别身份，需要再次填写提交表单", "收起更多场景", "展开更多场景", "线索存入成功后自动创建会员，可用于后期身份认证，如无需提交表单一键报名、资料下载等", "存入设置", "设为报名内容", "会员信息回填映射"], "pages.site.site_setting_dialog.index": ["微页面设置", "请检查线索设置"], "pages.site.site_setting_tips_dialog.index": ["微页面中含有表单组件，还需要完成表单设置才可以正常使用，同时还可以设置微页面标签。", "是否需要设置微页面标签，当用户访问此页面时，自动打上配置的标签。", "暂不设置"], "pages.site.site_template_dialog.content": ["什么也没搜到…"], "pages.site.site_template_dialog.index": ["搜索模版名称"], "pages.sms_marketing.components.sms_calc_expend_dialog.index": ["发送用户数", "预计消耗短信量", "当前短信余量", "无法发送", "上传Exce中含有", "个不规范手机号码，请检查后再提交", "下载不规范手机号到txt文档", "计算中，请稍后...", "短信余量不足，请先购买短信包", "不合格手机号.txt", "上传Exce中含有{{option0}}个不规范手机号码，请检查后再提交"], "pages.sms_marketing.sms_group_send.before_create_dialog.index": ["当前短信可用配额为0，无法进行短信推广，是否前往购买？", "暂无可用签名，请先设置短信签名再进行群发。", "暂无可用签名，请先设置短信签名再进行模板创建。"], "pages.sms_marketing.sms_group_send.const": ["发送完成", "废弃", "部分成功", "模板已通过", "模板审核中", "模板无效", "模板隐藏", "模板禁用", "模板已过期", "模板已删除", "模板审核失败", "再次编辑"], "pages.sms_marketing.sms_group_send.detail.index": ["审核通过待发送", "送达详情"], "pages.sms_marketing.sms_group_send.index": ["搜索关键字", "剩余短信配额", "充值", "短信试用", "运行配额"], "pages.sms_marketing.sms_group_send.send_confirm_dialog.index": ["操作失败，请重试"], "pages.sms_marketing.sms_group_send.submit_send.index": ["提交发送", "一周前"], "pages.sms_marketing.sms_init.sms_form.const": ["企业法人身份证复印件 加盖公章  （非必要资料，运营商后期会抽查，若被抽查到请及时提供）"], "pages.sms_marketing.sms_init.sms_form.contact": ["请填写贵司用于开通短信通道与短信签名纸质材料寄件信息", "为确保您的短信通道与短信签名能够正常使用，请确保所寄纸质材料为上传的电子材料一致为原件", "联系手机", "请填写手机号码，以接收审核短信通知", "运单号", "请填写快递公司与快递单号，请勿到付", "收件信息", "陈先生", "联系方式：", "深圳市南山区铜鼓路大冲国际中心22层", "请输入联系人", "请输入电话号码", "请输入运单号"], "pages.sms_marketing.sms_init.sms_form.files": ["上传材料", "请上传材料"], "pages.sms_marketing.sms_init.sms_form.index": ["开通前须知：", "开通完签名后，所有发送的短信的签名会切换为您申请的短信签名，包括【营销通】中表单的验证码，并且费用从您的", "短信充值费用中扣除", "，首次开通后，请进行短信充值，防止使用了验证码的场景下，余额不足导致验证码发送失败。", "申请开通步骤：", "1、下载短信签名受理模板，并按模板填写相关信息", "受理材料模板", "下载模版", "2、信息填写完成后打印并盖章，然后扫码加短信服务商（梦网科技）客服好友，提供照片或电子扫描件给客服在线审核，若盖章流程相对复杂，可以提前联系客服进行电子档审核，初审通过后再盖章", "扫码联系", "3、客服初审通过后，将盖章后的扫描件文件发送到", "邮箱，并将相关纸质原文件寄送给梦网科技，具体寄送地址联系客服同学提供", "4、在下方填写相关授权信息，包括填写短信签名及描述、上传审核通过的扫描件、填写相关寄送信息后提交审核，", "未提交相关信息会导致最终审核无法通过", "5、服务商（梦网科技）收到纸质文件后，3-5个工作日内完成最终审核，审核通过后，客服同学会告知已审核通过，首次使用请在系统内完成短信配额充值，充值发票可以联系您的纷享客户经理索取", "提交授权信息：", "提交失败，请检查表单信息。", "2、信息填写完成后打印并盖章，然后扫码加短信服务商（梦网-锦囊互动）客服好友，提供照片或电子扫描件给客服在线审核，若盖章流程相对复杂，可以提前联系客服进行电子档审核，初审通过后再盖章"], "pages.sms_marketing.sms_init.sms_form.signature": ["请描述您的使用业务场景", "长度为2-8个字，内容不包括【】，范例：纷享销客"], "pages.sms_marketing.sms_init.sms_result.const": ["特殊签名第三方证明材料（软件著作权证书或商标证书）"], "pages.sms_marketing.sms_init.sms_result.information": ["申请信息", "开通短信通道与短信签名受理材料电子扫描清单：", "展开全部", "开通短信通道与短信签名纸质签名的邮寄信息：", "联系电话：", "运单号：", "若后续需要修改短信签名，需要重新准备审核资料，资料准备完成后再次联系第三方短信运营商客服同学（梦网科技短信审核人员）进行线上初步审核，客服审核完成后请发送所有资料扫描件到{{option0}}邮箱，并进行线下邮寄，具体邮寄地址由客服提供，运营商在收到邮寄文件后3-5工作日内完成审核，审核通过后自动变更签名。", "扫码联系客服"], "pages.sms_marketing.sms_init.sms_result.status": ["还未申请签名"], "pages.sms_marketing.sms_init.sms_welcome.index": ["试用", "使用场景1:优惠促销", "使用场景2:生日关怀", "使用场景3:节日祝福", "试用短信营销", "现在试用短信营销，将赠送20条短信配额，赶紧来试用吧。", "立即试用", "使用场景1：优惠促销", "使用场景2：生日关怀", "使用场景3：节日祝福", "已获得20条短信配额", "精准短信营销", "适用于优惠促销、生日关怀、节日祝福等多种场景，对市场营销活动的目标客户精准推送短信营销内容，帮助企业获客、增加销售机会"], "pages.sms_marketing.sms_init.sms_welcome.index2": ["接入指引及报价", "使用场景1:营销短信", "向目标客户群发送营销内容短信，例如：节日祝福、关怀问候、生日祝福等。", "使用场景2:业务通知", "客户执行某个业务操作自动触发业务短信通知，例如工单受理通知、服务通知、业务提醒等。", "支持三网合一的专属短信通道，稳定快速", "提供营销短信、业务通知和短信服务，支持对特定目标群体用户进行发送，让短信触达更加精准。"], "pages.sms_marketing.sms_setting.index": ["短信签名信息", "模板管理", "短信明细"], "pages.sms_marketing.sms_setting.sms_detail.index": ["时间范围:", "搜索模板名称、手机号", "发送方", "短信发送记录"], "pages.sms_marketing.sms_setting.sms_quota.index": ["短信套餐名称", "短信总量（条）", "付款金额（元）", "购买时间", "短信消耗", "已消耗短信总数", "{{option0}}消耗短信数", "短信配额统计", "购买短信总数", "已消耗短信数", "配额购买记录", "购买配额", "剩余短信"], "pages.sms_marketing.sms_setting.sms_notice_dialog.index": ["剩余配额通知", "开启/关闭通知", "当短信剩余配额数低于以下设定值时，将于上午10点给选中员工发送通知提醒。", "短信配额数", "请填写要发送通知提醒的剩余配额数", "通知员工", "添加通知员工", "请填写剩余配额数", "剩余配额数必须为正整数！", "剩余配额数需小于10000000(一千万)"], "pages.sms_marketing.sms_setting.sms_quota_buy.index": ["购买短信配额", "选择配额套餐：", "选择套餐数量：", "描述文字", "总计：", "总费用：", "我已阅读并同意", "《纷享销客短信服务协议》", "立即支付", "请选择套餐数量", "套餐数量请不要超过99", "请阅读并同意《纷享销客短信服务协议》"], "pages.sms_marketing.sms_setting.sms_template_manage.index2": ["模板使用方", "短信计费：超过70字短信按67字", "条计费，总字数不能超过350字。", "编辑参数", "请输入参数名称", "请输入合法参数", "请输入短信内容", "短信字数不能超过350字", "使用方", "编辑短信模板"], "pages.sms_marketing.sms_template.sms_signature.edit": ["营业执照", "支持jpg、jpeg、png，大小不得超过512KB", "请上传营业执照", "提交中"], "pages.sms_marketing.sms_template.sms_signature.index": ["自定义签名字数限制为2-8个字，可包含中文、数字、英文；", "短信签名建议为公司名/品牌名/网站名/店铺名，其他签名可能会导致签名审核不通过；", "签名示例：【纷享科技】周三会员，立享半价购买，更有新用户注册好礼相送，速来抢购。", "修改签名", "，下月才能修改"], "pages.sms_marketing.sms_template.template_content.const": ["短信模板内容", "业务通知短信", "消息中带有变量，请注意控制实际长度"], "pages.sms_marketing.sms_template.template_content.create_template_dialog.index": ["营销类短信需要运营商审核通过后才能发送。适用于海量推广场景，如群发活动报名链接、群发优惠活动信息等；", "通知类短信无需审核即可发送。适用于业务通知场景，如会议提醒通知，报名成功通知、审核通过通知等。请勿使用通知短信发送营销内容，会被运营商拦截导致发送失败；", "选择直播或会议短信，可插入直播或会议的活动成员参数。", "模版名称", "发送模板内容", "请选择模板场景", "通知类短信", "营销类短信", "模板新增成功", "为了避免使用通知模板发送营销类短信，带来的封号风险，此入口仅能创建营销模板", "需要运营商审核通过后才能发送，预计2小时内审核通过，适用于海量推广场景，如群发活动报名链接、群发优惠活动信息等，客户回复R可以拒收", "无需审核即可使用，适用于业务通知场景，如会议提醒通知、报名成功通知、审核通过通知等。请勿使用通知模板发送营销内容，会被运营商拦截导致发送失败且有封号风险", "适用于海量推广场景，如群发报名链接、群发优惠活动信息等，客户回复R可以拒收", "适用于业务通知场景，如活动报名成功通知、审核通过通知、参会提醒通知等", "海外短信", "支持从中国内地向中国香港、中国澳门和中国台湾及其他国家和地区手机号码发送短信。", "短信通道", "了解此通道模板审核及发送规则"], "pages.sms_marketing.sms_template.template_content.create_template_dialog.template_content_box": ["字数统计包含签名", "普通模板示例：周三会员日，APP下单立享半价购买，更有新用户注册好礼相送，速来抢购。Excel模板示例：尊敬的", "插入任意链接，非营销通内容链接无法追踪链接访问打开情况", "快捷插入营销通创建的内容链接，以普通H5短链形式发送，可以追踪客户访问提交情况", "快捷插入营销通创建的内容链接，以小程序短链形式发送，可以从短信打开微信小程序，可以追踪客户访问提交情况", "在活动场景中，动态插入当前活动基础信息及参会人员信息", "支持插入动态变量，在具体发送页面可以配置变量值，变量名称支持修改，示例：尊敬的{name}您好，您的报名已审核通过，参会码：{code}，活动地址：{param1}", "查看内容审核标准", "URL变量", "通用变量", "短信模板支持插入URL变量和通用变量。通用变量可插入例如活动成员昵称、活动地址、活动时间等变量；URL变量可插入例如报名链接、活动链接、内容链接等变量。由于短信模板中的URL变量只能被拆分成固定前缀和动态参数两部分，请确定模板中已经添加了https://fs80.cn/的固定前缀，动态参数在发送时自动拼接到固定前缀后形成完整内容链接，使用动态链接可以追踪用户访问、提交行为。"], "pages.sms_marketing.sms_template.template_content.index": ["根据模板名称搜索"], "pages.sms_marketing.sms_template.template_content.table.index": ["其他营销短信", "其他通知短信", "会议邀约与推广", "直播推广", "EXCEL名单发送", "确认删除此模板吗？"], "pages.social_distribution.distribution_data.list.file": ["分销人员在客脉小程序中可以获取使用", "[网盘] — [公司文件] — [营销通文件夹] - [{{option0}}] 下的所有文件"], "pages.social_distribution.distribution_data.list.index": ["关联活动前，请先选择一个分销计划。"], "pages.social_distribution.distribution_distributors.const": ["提交线索数", "招募人", "所属企业分销员", "拒绝原因", "注册时间"], "pages.social_distribution.distribution_distributors.distributor_confirm.index": ["分销人员审核", "审核通过，分销人员将会收到审核通知，并可在客脉小程序访问分销中心。", "拒绝原因请不要超过", "分销人员审核完成"], "pages.social_distribution.distribution_distributors.index": ["更换企业分销员", "选择分销管理员", "请勾选需要更换企业分销员的分销人员", "获取分销管理员列表失败，请重试。", "更换企业分销员成功。"], "pages.social_distribution.distribution_lead.const": ["线索详情", "招募人员", "线索成单激励", "招募激励", "线索提交奖励", "无效描述"], "pages.social_distribution.distribution_lead.lead_confirm.index": ["线索确认", "确认线索有效，将会自动存入CRM线索池进行分配", "确认线索无效，请填写无效描述", "无效描述请不要超过", "线索确认完成"], "pages.social_distribution.distribution_lead.pay_reward.index": ["本次发放分销员奖励金额", "本次发放招募人奖励金额"], "pages.social_distribution.distribution_lead.set_reward.index": ["分销员奖励金额", "招募人奖励金额", "招募人奖励金额不能为空！"], "pages.social_distribution.distribution_lead.table.index": ["待确认", "赢单", "输单"], "pages.social_distribution.distribution_level.index": ["提交线索激励", "分销人员提交有效线索即可获得此奖励", "为分销员设置不同档位的线索成单奖励，促进分销员推荐线索，分销员获得分销线索成单后的奖励根据规则自动计算。", "为分销员设置不同档位的招募激励，促进分销员招募更多分销员推荐线索。分销员招募刺激后可根据规则自动计算刺激分销员推荐线索成单后的可获得的奖励。", "获取 订单分销员分润总金额", "获取 订单招募人分销总金额"], "pages.social_distribution.distribution_level.set_reward_dialog.index": ["设置激励金额"], "pages.social_distribution.distribution_level.set_rule_dialog.const": ["累计订单总金额", "累计招募分销员人数", "招募线索成单个数", "订单分销员分润总金额", "订单招募人分销总金额"], "pages.social_distribution.distribution_level.set_rule_dialog.index": ["升级到下一级，需要满足：", "且（and）", "分销线索成单后，分销员可获得的线索成单激励计算条件：", "输入金额", "请填写权益说明", "请输入数值"], "pages.social_distribution.distribution_manage.index": ["分润规则"], "pages.social_distribution.distribution_operators.const": ["关联社会分销员数", "社团大声道撒打开撒掘地三尺打开手机的撒娇地面", "社团社团大声道撒打开撒掘地三尺打开手机的撒娇地面", "社机的撒娇地面", "开手机的撒娇地面"], "pages.social_distribution.distribution_operators.index": ["添加企业分销员", "选择同事", "请选择分销计划后再添加分销管理员.", "移除员工后", "分销管理员将会取消所在微信群的运营管理权限，并无法再访问营销通，请确认是否移除？"], "pages.social_distribution.distribution_plan.distribution_plan_list.datas": ["分销计划标题", "存入线索池", "分销管理员数", "已招募社会分销人员数", "分销线索数", "分销奖励总额"], "pages.social_distribution.distribution_plan.distribution_plan_list.index": ["您确定要停用"], "pages.social_distribution.distribution_plan.distribution_plan_list.level_create": ["是否关闭弹窗，请确认设置已保存？"], "pages.social_distribution.distribution_planindex.plan_box_list.index": ["我的分销计划"], "pages.social_distribution.distribution_setting.distribution_level.index": ["当前选中的分销计划是："], "pages.social_distribution.distribution_setting.distribution_level.level_create": ["累计线索赢单金额不能小于0", "线索成单奖励不能小于0", "招募分销人员线索成单奖励不能小于0"], "pages.social_distribution.distribution_setting.distribution_level.level_table": ["累计线索成单"], "pages.social_distribution.global_index.const": ["分销管理"], "pages.social_distribution.global_index.switch_distribution_plan_dialog.index": ["切换分销计划"], "pages.social_distribution.group_detail.index": ["群资料"], "pages.social_distribution.group_detail.members": ["更换管理员", "选择群成员"], "pages.social_distribution.group_detail.metarials": ["群资料列表", "推荐产品", "推荐文章", "推荐活动"], "pages.social_distribution.index.associate_group_dialog.index": ["关联微信群为分销群"], "pages.social_distribution.index.index": ["已招募社会分销人员数：", "马上去招募", "修改分销计划", "分销人员线索排行", "分销人员奖励排行", "分销微信群", "分销微信群是运营分销人员的微信群，可选择将绑定了企业的微信群关联为分销微信群，分销微信群相比普通微信群会增加分销运营内容（分销招募海报与推广素材）。", "企业分销计划招募海报，点击“设置”可更新海报", "已招募社会分销人员", "分销管理员专属邀请码", "保存邀请码", "分销招募二维码", "下载分销招募二维码，用户通过微信扫描招募二维码后，查看分销计划、加入并成为分销人员。", "专属二维码", "有效线索数", "关联微信群", "专属邀请码.", "IE浏览器下请将鼠标移动到二维码图片，单击右键选择图片另存为即可"], "pages.social_distribution.initial.index": ["您正在使用营销通标准版，升级专业版，即可开始社会化分销", "简单4步，开启社会化分销", "创建分销计划"], "pages.social_distribution.plan_setting.index": ["请设置CRM映射", "分销计划设置成功"], "pages.social_distribution.plan_setting.info.index": ["设置分销计划标题", "上传分销介绍图片，最多9张", "图片不能为空", "设置按钮名称", "按钮名称不能为空", "您可以选择将分销线索存入到", "选择线索池", "社会化线索"], "pages.social_distribution.versiontip": ["分版提示"], "pages.tags.dialogs_group": ["修改标签分组", "确定要删除分组吗？", "删除后，该分组的标签也会被删除，分组标签下的所有用户保留标签属性，请谨慎操作。"], "pages.tags.dialogs_tag": ["修改标签", "确定要删除标签吗？", "删除标签后，该标签下的所有用户将保留该标签属性，请谨慎操作。"], "pages.tags.list_group": ["请至少保留一个标签分组"], "pages.tags_manage.model_create": ["标签模型", "模型名称", "模型类型", "请选择模型类型", "所有角色使用", "部分角色使用", "适用对象", "请选择适用对象"], "pages.tags_manage.model_list": ["暂无模型", "标签设置", "允许员工在筛选查找标签时，新增自定义企业标签"], "pages.tags_manage.model_template_selector": ["选择标签模型", "RFM模型", "RFM模型2"], "pages.tags_manage.tags_create": ["个人标签"], "pages.tags_manage.tags_list_level_mixin": ["移除后，该标签的子标签也会从模型移除，已添加到用户上的标签保持不变，请谨慎操作。", "移除后，已添加到用户上的标签保持不变，请谨慎操作。"], "pages.tags_manage.tags_list_level1": ["互斥", "设为互斥", "取消互斥"], "pages.tags_manage.tags_list": ["暂无标签", "，请先选择模型"], "pages.tags_manage.tags_tools": ["添加子标签"], "pages.tel_marketing.components.data_list.index": ["请输入名字", "参数：", "调用呼叫中心成功，请在响铃后通过坐席【", "】接听电话", "开始刷新列表"], "pages.tel_marketing.components.service_record_obj.index": ["新建通话记录", "不能直接新增的通话备注，请在电话打通后操作。", "通话记录", "无记录"], "pages.tel_marketing.index": ["电销中心", "今日统计：", "外呼", "接通", "有效通话数", "有效通话时长"], "pages.trigger.components.PrivateTriggerList": ["选择通用SOP", "创建专属SOP", "专属SOP", "起止时间：", "停止", "运行", "添加失败", "是否确认移除", "查看执行函数"], "pages.trigger.components.TriggerDatePicker": ["指定时间运行"], "pages.trigger.components.TriggerDialog": ["执行用户总数："], "pages.trigger.create.Setting": ["设置触发条件", "设置执行动作", "触发动作", "由于该节点下方已有配置数据，因此无法再添加分支判断条件", "当前场景不支持使用分支"], "pages.trigger.create.components.Condition": ["选择触发规则", "选择触发时间", "选择触发条件", "发生即触发（同一用户重复行为只触发一次）", "回看", "观看", "时长到达", "重新添加", "选择公众号菜单", "选择群发邮件", "请选择邮件", "选择官网页面", "选择官网表单", "触发人员为满足以下条件的用户", "用户标签满足", "选择触发人员", "设置时间规则", "触发时间范围", "触发人群", "期间，", "重复执行", "观看时长到达", "营销通封装低代码调用函数（Fx.marketing.sop），提供给CRM其他业务模块调用，比如在CRM审批流程、工作流、业务流中可以调用营销SOP函数，来触发营销SOP执行后动作。如客户对象上的流失风险字段值由低变为高时，可以触发运营任务给相关员工，通知员工发送相关物料及话术给客户挽回客户。", "保存此SOP后在列表页获取当前SOP的触发函数，复制函数代码给CRM管理员在需要触发的场景中进行配置，具体参考", "CRM流程与营销SOP对接指南", "外部触发", "活动开始后当天", "活动开始后", "SOP邮件", "群发邮件", "展开更多"], "pages.trigger.create.components.Lists": ["选择触发", "动作", "你还没开通短信", "你还没开通邮件", "这是一个分支节点，修改动作类型会删除后续所有的节点数据", "确定修改动作类型吗？"], "pages.trigger.create.components.Notice": ["应用通知（APP/企微/钉钉）", "未开通短信", "短信通知", "通知预览", "通知内容", "选择提醒人员", "客户互动通知", "自定义通知", "指定手机号", "指定人员", "关联数据的负责人", "所有添加客户企微好友的员工", "关联数据的其他人员", "提醒人员过滤", "发送提醒消息时，不会发给以下部门的员工，不设置默认不做过滤", "按字母查看", "按组织架构查看", "选择指定员工", "防打扰设置", "仅有姓名或呢称的用户产生行为才触发互动通知", "此SOP下多次访问同一内容仅触发一次通知", "活动或内容.推广人", "市场活动.负责人", "市场活动.相关团队成员"], "pages.trigger.create.components.QySop": ["选择SOP类型", "推送任务名称", "任务执行范围", "仅指定的部门或员工会收到任务通知", "任务分配员工优先级", "当公司多个部门的多个员工加同一个客户为好友时，基于以下优先级下发任务给相关员工", "第1优先级：员工部门", "第2优先级：添加客户好友时间", "最近添加客户好友的员工", "最早添加客户好友的员工", "任务截止时间", "任务发布后", "天后，当日", "任务截止"], "pages.trigger.create.components.Task": ["选择看板", "选择列表", "工作内容"], "pages.trigger.create.components.Webhook": ["选择Webhook"], "pages.trigger.create.components.WeekAndMonthPicker": ["选择星期", "周"], "pages.trigger.create.config": ["企微群发，企微SOP运营任务等", "会议报名、报名审核、会议开始、现场签到等", "报名预约、直播开始、观看直播、参与互动等", "访问推广内容、提交表单等", "关注公众号、打开菜单、扫描二维码等", "收到邮件、打开邮件、点击邮件内链接等", "访问官网页面、提交官网表单等", "定时型-单次", "定时型-重复", "触发型-完成A", "触发型-完成A 未完成B", "用户报名会议", "用户提交报名审核", "用户报名审核通过", "用户报名审核未通过", "用户完成签到", "用户报名预约直播", "用户观看直播", "参与直播互动", "用户访问内容", "用户提交表单", "打开公众号菜单", "用户打开邮件时触发", "用户点击邮件内链接时触发", "用户取消邮件订阅时触发", "用户投诉举报邮件时触发", "用户访问官网页面", "用户提交官网表单", "用户查看或下载文件", "用户添加企微员工好友", "所有参会人员", "所有待审核参会人员", "所有审核通过参会人员", "所有审核未通过参会人员", "所有已签到的参会人员", "所有未签到的参会人员", "所有直播报名人员", "所有观看直播的参与人员", "所有参与互动的参与人员", "所有回放直播的参与人员", "所有报名人员", "对满足条件的公众号粉丝发送模板通知或图文消息，其中图文消息仅能在客户有主动发消息，支付成功48小时内，或点击菜单，关注公众号，扫二维码码1分钟内推送", "发送企微客户/客户群消息", "对满足条件的企微客户或客户群发送消息，负责的员工收到【群发助手】通知后，点击确认后发送企微消息", "每位客户或客户群每天仅能接收1条群发消息", "发送企微SOP任务", "下发企微消息推送任务", "对满足条件的企微客户/客户群群发送消息，负责的员工或群主收到【群发助手】通知后，点击确认后一键群发消息到所有相关客户/客户群,单个客户/客户群每天仅能接收1条群发消息", "下发企微运营任务", "对满足条件的企微客户/客户群群进行消息触达，负责的员工或群主收到【营销助手】通知后，在客户/客户群聊天窗口的企微侧边栏【营销助手】中逐一手动点击推送消息，无次数限制", "对满足条件的企微客户或客户群发送消息，负责的员工收到【营销助手】客户运营通知后，在企微侧边栏【营销助手】中快捷推送消息，无次数限制", "对满足条件的拥有手机号的营销用户发送短信通知", "对满足条件的拥有手机号的营销用户发送邮件通知", "对满足条件的营销用户打上指定的标签", "发送员工通知", "对满足条件的营销用户的相关责任人发送客户动态通知，如客户签到后告知相关销售同事去接待", "创建工作任务", "满足以上触发条件时，在运营看板中的给某个员工设置待办任务，将会展示在移动端应用的的待办列表中", "满足以上触发条件时，调用外部系统接口，实现跨系统数据交互", "行为名称", "外部触发型", "基于用户提交表单值", "例子:对产品1感兴趣的发送内容1，对产品2感兴趣的发送内容2", "根据上一个最近节点的用户表单提交值进行判断，依次从左到右执行分支：", "如果上一个最近节点提交的表单内容数据满足分支条件，则执行对应分支。", "如果上一个最近节点没有提交满足分支条件的表单内容数据，在经过其他分支等待时间后，将执行其他分支。", "例如，表单内容未通过SOP配置发送，或不作为触发条件，则经过等待时间后SOP将执行其他分支。", "基于营销用户标签", "例子:标签是“VIP”发送内容1，其他发送内容2", "根据用户在当前分支时的用户标签进行判断，依次从左到右执行分支：", "如果上一个最近节点的用户标签满足分支条件，则执行对应分支。", "如果上一个最近节点没有满足分支条件的用户标签，在等待其他分支时间后，执行其他分支。", "基于动作结果", "例子:可根据邮件发送状态，执行不同分支", "根据上一个最近节点的群发行为结果进行判断，依次从左到右执行分支：", "如果上一个最近节点的群发行为结果满足分支条件，则执行对应分支。", "如果上一个最近节点未设置任何群发动作，或未返回群发动作结果，在等待其他分支时间后，执行其他分支。", "基于用户行为", "例子:根据是否产生营销动态，走不同的分支", "根据上一个最近节点的用户行为进行判断，依次从左到右执行分支：", "如果上一个最近节点的用户行为满足分支条件，则执行对应分支。", "如果上一个最近节点未产生满足分支条件的用户行为，在等待其他分支时间后，执行其他分支。", "基于营销用户对象属性值", "例子:职位是CIO的发送内容1，职位是CMO的发送内容2", "根据用户在当前分支时的营销用户对象属性进行判断，依次从左到右执行分支：", "如果用户在当前分支时的营销用户对象属性满足分支条件，则执行对应分支。", "如果用户在当前分支时的营销用户对象属性未满足任何分支条件，在等待其他分支时间后，执行其他分支。", "由于发送成功失败结果需要等待外部平台回调，因此目前必须勾选其他分支的等待时间。", "由于用户行为（如打开邮件、访问页面等）不会立即发生，因此目前必须勾选其他分支的等待时间。即系统将在一定时间内观察是否产生该行为，若未产生则执行其他分支。", "用户打开邮件", "用户点击邮件内链接", "用户取消邮件订阅", "用户投诉举报邮件"], "pages.trigger.create.index": ["点击添加名称", "点击添加描述", "触发条件", "触发规则：", "触发人员：", "未命名", "目标人群SOP", "会议SOP", "直播SOP", "活动SOP", "公众号SOP"], "pages.trigger.create.item.ActionList": ["后执行", "发送文本：", "短信模版：", "邮件内容：", "工作内容：", "通知内容：", "请填写SOP名称", "请选择触发规则", "请设置时间规则", "请设置触发时间范围", "请选择素材内容", "请添加触发动作", "定时型任务不支持发送员工通知及Webhook后动作，请更新后再保存", "触发人群是企微客户群场景下，仅支持发送企微客户/客户群消息、发送企微SOP任务两个动作，请更新后再保存", "请选择执行动作类型", "请选择消息模版", "请完善消息模版内容", "请填写消息内容", "请上传消息图片", "请选择消息素材", "请选择短信模版", "请设置邮件内容", "请完善工作任务内容项", "请选择运营计划", "请选择运营计划列表", "请输入工作内容", "请选择工作任务负责人", "请选择工作任务时间", "请选择Webhook", "触发动作【发送员工通知】 不适用于触发规则【时间触发型】，请修改后再次保存", "请选择提醒人员", "请选择指定人员", "最多支持20个指定手机号配置", "最多支持同时通知100个指定员工", "请输入发送文本或内容", "请设置任务截止时间", "请输入推送任务名称", "请选择任务执行范围"], "pages.trigger.dashboard": ["直播简报"], "pages.trigger.detail.EmployeeExecutionDetail": ["员工执行详情", "客户群总数"], "pages.trigger.detail.FlowSlide": ["触发执行"], "pages.trigger.detail.MarketingEventList": ["SOP状态："], "pages.trigger.detail.SopTimeline": ["任务下发到{{option0}}名员工，内容将推送到{{option1}}名客户，截止目前还有{{option2}}名员工负责的{{option3}}名客户未完成消息推送，", "任务下发到{{option0}}名员工，内容将推送到{{option1}}个客户群，截止目前还有{{option2}}名员工负责的{{option3}}个客户群未完成消息推送，", "企微群发", "推送内容：", "任务下发到", "个员工，内容将推送到", "名客户，截止目前还有", "名员工负责的", "个客户未完成消息推送，"], "pages.trigger.detail.UserExecutionDetail": ["执行详情"], "pages.trigger.detail.UserTable": ["成功执行", "个动作", "发送公众号消息执行用户", "发送邮件执行用户", "发送短信执行用户", "发送员工通知给员工执行用户", "时触发的营销用户", "个节点"], "pages.trigger.detail.index": ["SOP流程", "SOP执行情况", "员工执行情况", "触发用户列表", "定时重复任务：在", "时间段内", "每天执行", "定时单次任务：在", "触发任务：用户触发指定行为后执行", "移除失败", "外部触发列表", "SOP概览", "企微消息", "企微任务", "SOP用户路径统计", "版本", "每次修改SOP时都会保存为一个新版本，并分别统计每个版本SOP的用户执行数据", "清除筛选条件", "定时重复任务：", "定时单次任务："], "pages.trigger.detail.table": ["执行情况"], "pages.trigger.index": ["SOP列表", "搜索SOP名称", "新建SOP", "编辑SOP", "新建SOP模板", "编辑SOP模板", "已启用SOP总数", "执行量排行榜"], "pages.user.index": ["营销用户汇总"], "pages.user.table": ["与我相关的营销用户", "暂无与我相关的营销用户，可联系管理员开放线索、客户、联系人以及微信用户对象的数据权限", "没有更多与我相关的营销用户了", "不使用单独的按标签筛选"], "pages.user.tags": ["用户标签"], "pages.video.components.upload": ["建议不超过2G，最大支持6G；支持视频格式：", "视频格式", "视频名称", "请输入视频名称", "网络异常，上传失败，请稍后重试"], "pages.video.components.video_dialog": ["选择视频内容", "仅支持腾讯视频地址，示例：", "请输入正确的腾讯视频播放地址", "视频封面", "以下是内容中心已经成功转码的可用视频内容", "暂无素材", "请选择视频", "请输入视频链接"], "pages.website_access.access_script": ["凤凰", "网易", "腾讯"], "pages.website_access.website_init.index": ["您正在使用营销通标准版，升级专业版，即可开始官网接入"], "pages.website_access.website_init.website_welcome": ["将以下代码复制并粘贴到您网站HTML源代码标签的结束符之前", "安装成功后，配置需要跟踪的页面，即可跟踪访客行为", "立即接入官网", "官网名称：", "官网URL：", "请输入官网URL", "请输入官网名称", "接入指南", "官网访客雷达", "追踪访客行为，掌握潜在客户的兴趣点、关注点"], "pages.website_access.website_manage.components.card_ad_word": ["线索来源广告词分布"], "pages.website_access.website_manage.components.card_clue_sources": ["线索来源"], "pages.website_access.website_manage.components.card_promotion_plan": ["表单线索来源SEM推广计划排行"], "pages.website_access.website_manage.components.event_data": ["追踪网站页面点击行为", "暂无点击事件", "快添加您想要追踪网站页面点击事件吧", "马上添加点击事件", "编辑点击事件", "事件自定义属性", "添加属性", "添加事件属性", "编辑事件属性", "事件添加说明", "你可以通过以下方式跟踪事件行为", "复制添加说明", "属性ID", "属性显示名称", "事件ID", "事件显示名称", "添加方式：", "使用方式：", "1.添加跟踪代码", "2.上报点击事件", "属性时上报点击行为", "必填，事件ID", "选填，属性ID", "选填，市场活动ID", "选填，自定义事件描述", "上报事件属性方法", "上报方式：", "上报属性:需要带上该属性所属的事件ID", "上报示例按钮", "当前事件信息：", "属性ID不能重复", "是否确认删除该属性？", "是否确认删除该事件？", "获取添加说明"], "pages.website_access.website_manage.components.overview_header": ["接入时间：", "跟踪代码", "官网跟踪代码", "将以下代码复制并粘贴到您网站HTML源代码的, body 标签的结束符之前。安装成功后，配置需要跟踪的页面，即可跟踪访客行为。", "停用将不会再获取官网跟踪页面的访问数据，再次启用后停用期间数据将会缺失，请确认是否继续停用？", "仅2024-01-05之后的数据支持按天查询", "官网流量统计和流量来源渠道概览仅支持查询2024-01-05之后的数据；"], "pages.website_access.website_manage.components.pie_detail": ["线索来源统计", "线索来源类型统计", "线索来源趋势"], "pages.website_access.website_manage.components.website_data": ["官网线索来源", "监测分析"], "pages.website_access.website_manage.index": ["集客二维码", "网站设置", "新增官网", "SEO分析"], "pages.website_access.website_manage.qrcode": ["公众号渠道二维码", "公众号绑定", "企微吸粉二维码", "添加企微吸粉二维码", "企业微信绑定", "在需要嵌入集客二维码的位置插入id", "参考如下代码", "在需要嵌入集客二维码的位置插入id=qrcode的div，在页面body标签结束之前加入生成二维码的js代码。"], "pages.website_access.website_manage.website_clue": ["官网获取线索明细", "53快服线索明细", "在线客服线索明细"], "pages.website_access.website_manage.website_manage": ["获取在线客户咨询", "留言线索", "53快服", "配置", "官网获取线索", "添加线索表单", "免费注册试用、报名预约、留言咨询...", "在官网上放置线索获取表单，整合官网获取线索，掌握访客动态", "马上添加线索获取表单", "官网跟踪页面", "暂无官网跟踪页面", "快添加您想要跟踪关注的官网页面吧", "马上添加跟踪页面", "页面名称：", "页面URL：", "同类页面统一跟踪通配符", "如存在多个同类型页面需要统一跟踪，即可将链接中的变量设置为通配符，", "则会跟踪其他所有的客户案例页面", "你可以通过以下方式将官网线索表单嵌入官网页面中", "如果需要使用自己开发的表单样式，可以选择SDK对接", "您可以复制以下信息给官网开发同学用于调用营销通微页面SDK进行数据提交", "当前表单ID（formId）：", "调用密钥（sercret）：", "SDK对接", "注：如果嵌入的表单不是在页面加载时渲染（如：点击按钮弹出线索表单），请选择动态生成的方式", "详细步骤可", "查看帮助文档", "iframe嵌入", "动态生成", "请输入页面URL", "请输入页面名称", "访问人总数（UV）", "访问总次数（PV）", "该微页面缺少表单，无法设置", "iframe容器的ID", "编辑跟踪页面", "跟踪页面", "当前官网下可保存的受访页面数量上限为3000个，如您有更多数量的页面分析需求，请与客户经理联系。", "在线客服", "选择不同的嵌入场景，最终将影响表单提交时转化页的记录，", "嵌入场景", "在转化页嵌入表单", "点击按钮嵌入表单", "在新开页面嵌入表单", "嵌入样式", "营销通表单样式", "自定义表单样式", "当前场景Id（sceneType）：", "确认删除该官网CTA？", "关联CTA组件", "添加CTA组件"], "pages.wechat.autoResponse": ["关注后回复"], "pages.wechat.components.asyncDialog": ["公众号图文素材同步", "同步全部图文素材", "同步所选日期之后的图文素材"], "pages.wechat.components.graphic_item": ["该场景暂不支持多图文消息"], "pages.wechat.components.keyword_res_dialog": ["规则名称：", "关键词：", "回复内容：", "给回复关键词的微信用户添加标签", "有效时间：", "通过编辑内容或者关键词规则，快速自动回复设置。", "添加关键词回复", "全匹配", "半匹配", "请输入规则名称", "请选择截止时间", "编辑关键词回复", "（包含关键词）", "（被关键词包含）"], "pages.wechat.components.qrcode_dialog": ["选择渠道二维码"], "pages.wechat.components.tplmsg_setting_dialog": ["编辑模版消息"], "pages.wechat.components.tplmsg_setting": ["会议门票", "直播报名页", "电子门票"], "pages.wechat.components.wechat_notice": ["基于目标人群与标签，对公众号粉丝精准推送图文内容，每个用户每月可接收四次消息", "模板消息群发", "结合活动对目标用户推送活动相关提醒，如活动报名成功通过、会议提醒等"], "pages.wechat.contract_datas": ["关注来源", "关注时间", "关注状态", "取消关注", "已关注", "未关注", "添加标签成功", "删除标签成功", "添加标签失败", "删除标签失败"], "pages.wechat.follow_res": ["设置自动回复内容，当用户关注公众号后，则自动回复该内容"], "pages.wechat.graphic": ["搜索草稿箱名称"], "pages.wechat.history": ["全部消息类型", "跳转小程序：", "会议电子门票", "【模板消息】", "【高级群发】"], "pages.wechat.home": ["我绑定的公众号", "简单4步，玩转公众号营销", "选择您想要绑定到营销通的微信公众号", "当前暂无公众号可选，是否前往绑定？", "通过微联服务号，您可以更好的将业务系统与微信公众号结合。", "无法访问，如有需要可联系该公众号管理员进行设置。", "粉丝分类管理，精准推送运营，发掘客户提升转化"], "pages.wechat.keyword_res": ["搜索规则名称", "新建关键词回复", "【公众号图文素材】", "回复内容", "回复行为标签", "确认删除该条关键词回复吗？"], "pages.wechat.massmsg": ["新建高级群发", "发送公众号消息基于微信高级群发，每个微信用户每月最多只能接收4次消息推送，请结合业务需要和用户体验合理发送消息。", "注意：群发最少需要两个粉丝"], "pages.wechat.qr_create": ["编辑渠道二维码", "二维码名称：", "内容推广渠道：", "关注后回复：", "给扫码关注的微信用户添加标签", "关联市场活动：", "请选择内容推广渠道"], "pages.wechat.qrcodes": ["创建渠道二维码，为不同渠道来的关注的粉丝回复专属内容或自动添加标签", "批量下载", "查看回复", "二维码下载", "下载渠道二维码，张贴于宣传物料，", "微信用户扫码后将回复专属内容", "场景1:活动投放", "将二维码作为活动的入口，运用在不同的渠道宣传，统计每个渠道带来的粉丝数量", "场景2:信息查询", "二维码宣传物料，用户扫码关注后。公众号自动推送产品介绍或者购买链接等", "场景3:实体门店", "不同客户扫码关注后，公众号自动推送门店介绍和优惠信息，同事标记粉丝来源", "场景4:收据收集", "用户扫码关注后，向用户发送带链接的调查问卷或者试用表单，收集客户数据。", "扫码回复内容", "扫码添加标签", "请先选择需要下载的二维码", "删除后，此二维码将会失效，是否确认删除？"], "pages.wechat.setting": ["粉丝数", "是否已创建完成"], "pages.wechat.statistics": ["用户变化趋势", "用户关注来源分析", "新增微信客户数", "外部联系人数", "工单提交总数", "结束流程总数", "微信咨询人数", "微信消息总数", "客服回复总数", "关注渠道", "新增粉丝", "新增占比", "流失粉丝", "流失占比"], "pages.wechat.switch_wechat_dialog.index": ["切换公众号"], "pages.wechat.tplmsg": ["新建模板消息群发", "模板设置：", "说明：群发通知使用了模板消息，不占用每月4次群发次数，请控制发送的频次，以免造成对微信客户的打扰而被投诉，影响服务号正常使用", "查看模板消息运营规范", "复制参数", "查看APL代码调用参数", "微信公众号ID", "企业EI", "会员ID", "跳转页面地址", "模版ID"], "pages.member.components.login": ["登录设置", "开启一键注册登录"], "pages.qywx_manage.customer_circle.components.spreadCustomerDetail": ["预计由{{option0}}个员工 发送给{{option1}}个客户"], "pages.qywx_manage.customer_circle.sendDetail.publish_customer_tools": ["触达状态", "触达时间", "未触达"], "pages.setting.setitems.content_set.const": ["聊天工具栏", "企微侧边栏", "所有角色", "社会化分销员"], "pages.setting.setitems.content_set.index": ["移动端和侧边栏推广入口设置", "可以设置各功能入口的显隐和名称。", "内容入口展示示例", "推广物料类型", "展示端", "入口权限", "展示/隐藏", "移动端：", "PC端：", "需要先开启图片库插件！", "需要先开启社会化分销插件！", "构建内容标签体系，快捷筛选或按标签展示营销物料，统一管理微页面、文章、产品、文件、视频、海报、图片、活动、直播、会议标签，后期可以基于用户和内容标签匹配规则，进行个性化内容推荐", "【", "】？", "微页面、文章、产品、文件、视频、会议落地页以H5形式推送，转发至微信时，可以设置用户授权微信ID、头像、昵称后才能访问。", "小程序内容访问授权设置", "微页面、文章、产品、文件、视频、会议落地页以小程序形式推送，转发至微信时，可以设置用户授权微信", "、头像、昵称、手机号后才能访问。", "授权方式：", "无需授权", "仅授权手机", "仅授权头像昵称", "授权头像、昵称、手机", "线索存入：", "微页面、文章、产品、文件、视频、会议落地页以小程序形式推送，转发至微信时，可以设置用户授权微信ID、头像、昵称、手机号后才能访问。", "微信运营规范要求，不允许进入页面就获取用户信息，关闭后将隐藏此模块，后续可以到CTA组件模块下按单个页面配置授权功能。"], "pages.setting.channel.components.CardTemplate.index": ["名片样式设置", "经典", "黑金", "商务"], "pages.setting.product.index": ["产品推广预览"], "pages.site.page_designer.SetProduct": ["产品分组展示设置", "文章分组展示设置", "列表展示分组", "请选择列表展示分组"], "pages.website_access.website_manage.components.importWebsiteTemplateDialog": ["批量导入", "上传您需要追踪的官网页面清单", "点击下载", "官网追踪页面导入模板"], "pages.index.activity_overview": ["近期参与人数最多", "人报名", "您还没有营销活动，立即创建营销活动，即可获取线索"], "pages.index.ads_report": ["广告推广"], "pages.index.banner": ["在科技与人性完美融合的时代", "制定全方位战略，运用MarTech，设计客户旅程，开启数字化消费新商机"], "pages.index.content_overview": ["内容概览", "近期访问次数最多", "您还没有营销内容，立即创建营销内容，即可推广内容获取线索"], "pages.index.miniprogram_overview": ["昨日数据简报", "您还未授权小程序，暂时无法获取数据", "立即授权已有微信小程序，搭建企业微站，快速发布获取企业品牌营销小程序", "立即授权小程序"], "pages.index.target_population": ["您还没有创建目标人群，立即创建目标人群，精准培育转化", "最近触达人群"], "pages.index.marketing_radar_card": ["近期报名人数最多", "该活动暂未推广"], "pages.setting.setitems.clue_set.clue_set": ["查看日志"], "pages.setting.setitems.components.knowledge_database_dialog": ["知识库场景设置", "查询场景列表失败", "查询场景失败"], "pages.ad_marketing.ad_baidu.components.advertise_call_dialog": ["回访中间号", "获取中间号失败，点击重新获取", "号码已过期，点击重新获取号码", "内有效", "请及时回访，需注意以下事项：", "线索产生12小时内，必须完成首页中间号回访；", "线索产生24小时内，至少对网民进行3次中间号回访（包含首次回回访）；", "线索产生24至48小时内，至少对网民进行1次中间号回访。"], "pages.ad_marketing.ad_bytedance.components.const": ["所属广告组"], "pages.ad_marketing.ad_bytedance.overview.index": ["广告组名称"], "pages.setting.channel.card_setting": ["当有多条业务或者子公司场景下，支持给不同部分的员工设置不同的名片模版，同一个员工在多个模版的适用范围内时，优先以最小的部门规则生效，如给全公司配置了统一模版A，又给研发中心配置了一套模版B", "研发中心人员均以模版B生效。若同时给同级部门配置了多套模版，则以最近创建的模版生效。", "创建名片模板", "适用范围：", "名片模板名称", "请输入名片模板名称", "确认删除该名片模板吗？删除该模板后员工将使用默认模板", "当有多条业务或者子公司场景下，支持给不同部分的员工设置不同的名片模版，同一个员工在多个模版的适用范围内时，优先以最小的部门规则生效，如给全公司配置了统一模版A，又给研发中心配置了一套模版B,研发中心人员均以模版B生效。若同时给同级部门配置了多套模版，则以最近设置的模版生效。"], "pages.wechat.contracts": ["若批量选中的数据量较大，标签配置有一定时间延迟，请稍等几分钟后再刷新查看"], "pages.Coupons.MarketingCoupons.Create.FxCouponsSelector": ["请开启纷享优惠券插件"], "pages.trigger.components.OutTriggerDialog": ["获取函数示例", "您可以将以下函数内容复制给CRM管理员，并告知具体需要的触发条件，如客户某个状态变更时，需要触发此营销SOP，管理员配置完成后可以进行触发测试，触发后在SOP详情页查看触发情况。", "函数示例代码如下：", "复制函数代码"], "pages.trigger.detail.triggerTable": ["触发名称", "传递对象数据", "关联营销用户数", "触发状态"], "pages.live_marketing.components.permissionsDialog": ["您暂无此活动的编辑权限，无法进入详情页，若需要编辑请联系活动负责人分配编辑权限，", "若需要查看此活动中员工的推广情况，可直接前往数据分析模块中查看"], "pages.site.site_setting_dialog.crm_set": ["您可以选择将数据存入到CRM线索池统一分配或直接自动分配给销售跟进", "提示：由于无推广人的线索会自动分配给当前活动的负责人，且非销售部门同事推广获得的线索也会自动下发给推广人，但是市场人员及非销售员工都不需要处理线索，可以联系CRM管理员配置工作流，自动将无推广人的线索及非销售部门推广人获得的线索自动存入到统一线索池，进行后续分配处理", "统一进入线索池进行管理，后续可以按线索池规则自动或者手动分配给销售侧跟进", "对于有推广人的线索自动分配给对应推广人，没有推广人的默认分配给当前活动的负责人", "线索映射"], "pages.mini_app.index": ["物料库"], "pages.mini_app.tableTools": ["注册会员信息"], "pages.live_marketing.components.dashboard_mudu_eventlist": ["在营销通完成直播活动创建后，系统将自动将目睹活动下的虚拟场所创建为子级市场活动，并在下方的活动列表中展示。", "即使在目睹活动中删除了虚拟场所，子级市场活动也会保留，不会被删除。", "在直播开始后，系统会自动同步目睹各场所的观众和直播数据，并在本活动中进行汇总。", "数据在直播活动创建后的一个月内每小时更新一次，之后将不再自动更新。如果需要继续同步数据，请点击设置项中的【同步直播数据】按钮。"], "pages.live_marketing.components.mudu_selector_dialog": ["了解如何对接目睹直播"], "pages.live_marketing.components.mudu_sumdata": ["人均观看时长", "人均点播时长", "驻留人数", "平均驻留时长", "驻留人数/次数"], "pages.setting.setitems.marketing_plugin.mudu_setting_dialog": ["目睹直播配置", "注册目睹直播", "并开通专业版或旗舰版直播服务。完成开通后，在目睹平台【开发者设置】-【应⽤管理】目录下创建开发者应用，其中能力类型选择API", "等待审核通过后，下载应用凭证，获取accessKeyId和secretAccessKey，并填写于下方", "请输入accessKeyId", "请输入secretAccessKey"], "pages.target_marketing.components.limit": ["仅参与一次", "参与多次", "天内，最多参与", "次此运营计划"], "pages.target_marketing.components.overview": ["人群总数"], "pages.target_marketing.components.targetMarketingItem": ["运营时间：", "已停止创建运营活动"], "pages.target_marketing.components.triggerExample": ["孵化型SOP示例", "场景：针对近3个月在官网提交过申请实用信息，且回呼后短期暂无需求的客户，进行运营转化，基于以上条件创建动态人群【官网二次孵化客户】进行持续运营。", "触发条件：", "执行动作：", "立即创建SOP", "SOP1：第一轮触达", "定时启动后当天12:00", "立即执行【发送激活短信】", "SOP2：第一轮反馈", "用户访问短信内容", "立即执行【发送员工通知】", "立即执行【打上已孵化标签】", "SOP3：第二轮触达", "定时启动第4天后12:00，不包含【已孵化】标签的用户", "立即执行【发送邮件1】", "延时1天执行【发送邮件2】", "延时2天执行【发送邮件3】", "SOP2：第二轮反馈", "用户查看邮件", "SOP3：结束运营", "定时启动第8天后12:00，不包含【已孵化】标签的用户", "立即执行【打上沉睡标签】", "SOP4：第二轮反馈", "SOP5：结束运营"], "pages.target_marketing.components.triggerList": ["提示：以下配置的SOP仅提供给周期性创建的子活动复制使用，此主活动上SOP均不会执行，已停止的SOP，不会自动复制到子活动。主活动SOP变更后，仅对新创建的子活动生效，历史已创建子活动不变更"], "pages.target_marketing.components.WeekAndMonthAndDayOptionPicker": ["选择月度", "每季度", "第一个月", "第二个月", "第三个月"], "pages.target_marketing.pages.create": ["主数据信息", "计划名称", "执行周期", "运营时间范围", "整个主活动持续运营时间范围，仅在执行周期范围内会自动创建新的运营子活动", "单次运营周期", "自动创建的子活动的运营截止日期，从子活动创建后的n天后，子活动将自动结束，配置的运营SOP将自动停止", "请输入单次运营周期（只支持数字）", "运营人群", "参与限制", "当一个用户满足参与多次子活动时，为了防止对客户造成骚扰，允许配置参与限制，如对有沉睡标签的客户群，每周推送重复邮件，每周都会对此客户发送重复邮件，可以配置仅参与一次或间隔多少天后再参与。", "目标人群单次运营", "目标人群周期性运营", "请填写计划名称", "请选择执行周期", "请选择运营时间范围", "请填写单次运营周期", "请选择运营人群", "请填写参与限制", "运营活动名称", "计划执行时间", "请选择计划执行时间"], "pages.target_marketing.pages.list": ["固定目标人群单次运营", "基于选中的特定人群，经过多轮自动化运营，", "适用于固定批次人群运营场景，仅生成一个运营活动", "动态目标人群周期性运营", "基于选中的人群，在固定周期重复自动进行多轮运营，适用于有动态属性变更的人群，比如每周流失人群换回计划，每周会针对有流失风险的客户生成一个专属的运营活动", "按运营状态筛选", "搜索运营活动", "基于选中的人群，自动化进行多轮过滤触达，适用于固定批次人群运营场景，仅生成一个运营活动", "基于选中的人群，在固定周期重复自动进行多轮运营，适用于有动态属性变更的人群，比如每周流失人群挽回计划，每周会针对有流失风险的客户生成一个专属的运营活动"], "pages.target_marketing.pages.welcome": ["自动化运营目标人群，提升运营效率，挖掘存量客户潜在价值", "5步教你玩转目标人群运营", "立即创建目标人群运营计划", "圈选目标人群", "按条件或标签筛选出需要运营的人群，支持动态人群持续运营。", "创建运营计划", "基于选中的人群设定一次性运营或周期性运营计划。", "创建需要推广的营销物料，包括邮件、短信模板、收集信息的微页面表单等。", "配置运营SOP", "基于时间或客户行为触发内容推送，配合标签过滤设置多个SOP，自动化执行运营计划。", "查看运营数据", "基于整个运营计划查看触达、访问、提交及转化数据，评估运营价值。", "创建单次运营", "创建周期性运营", "创建单次运营活动", "创建周期性运营活动"], "pages.File.FileListTableNew": ["设置用户标签"], "pages.content_tags_manage.model_create": ["请输入标签分组", "{{option0}}标签组", "请输入分组描述"], "pages.content_tags_manage.model_list": ["新建标签组", "暂无标签组"], "pages.content_tags_manage.tags_list_level_mixin": ["移除后，该标签的子标签也会从模型移除，已添加到内容上的标签保持不变，请谨慎操作。", "移除后，已添加到内容上的标签保持不变，请谨慎操作。"], "pages.content_tags_manage.tags_list": ["请先选择标签组"], "pages.setting.setitems.components.vhall_setting_dialog": ["微吼直播配置", "注册微吼直播", "。登陆后，进入", "开发设置", "，创建应用。若无法创建即无权限，需联系微吼客户经理开通", "启用应用后，获取APPKey和SecretKey，并填写于下方"], "pages.website_access.website_manage.components.overview_data": ["数据简报"], "pages.website_access.website_manage.components.overview_lead": ["线索趋势", "线索数量", "转化率"], "pages.website_access.website_manage.components.overview_statistic": ["官网流量统计", "柱状图", "移动端流量", "PC端流量", "付费搜索", "直接", "外链", "资讯", "自然搜索", "社交媒体", "流量来源渠道统计", "直接访问：直接访问网址的流量，比如直接在浏览器网址输入栏输入 fxiaoke.com进行访问；", "自然搜索：在搜索引擎搜索关键词，访问网站非广告的链接，比如搜索 纷享销客，访问纷享销客官网自然搜索结果；", "付费搜索：在搜索引擎搜索关键词，访问网站SEM广告的链接，比如搜索 纷享销客，访问纷享销客官网广告；", "社交媒体：从社交媒体访问的流量，如从微博、知乎来的访问流量；", "外链：通过在第三方网站的超链接访问，比如在abc.com访问fxiaoke.com的超链接；", "其他：所有其他渠道的访问流量。"], "pages.website_access.website_manage.components.overview_trend": ["官网流量趋势", "IP数", "跳出率"], "pages.website_access.website_seo.ai_keyword": ["AI推荐关键词", "相关长尾词"], "pages.website_access.website_seo.baidu_keyword": ["百度关键词-PC趋势", "百度关键词-移动趋势", "百度关键词排名-PC端", "个关键词", "百度关键词排名-移动端", "{{option0}}个关键词", "前10名", "前20名", "前30名", "前40名", "前50名"], "pages.website_access.website_seo.base_info": ["SEO评分", "根据官网的TDK描述和页面结构进行综合评分", "SEO表现", "根据官网在搜索引擎的表现，如关键词数量、收录数量、排名等因素进行综合评分", "网站流量", "总PV", "总UV", "设备流量", "PC端占比：", "移动端占比：", "SEO流量", "自然搜索流量占比：", "备案信息", "备案号：", "性质：", "审核时间：", "最近90天", "综合得分"], "pages.website_access.website_seo.framework": ["H1标题超过1个", "使用AI优化", "AI思考中", "图片属性", "图片Alt属性", "缺失"], "pages.website_access.website_seo.index": ["TDK分析", "SEO分析中，请勿离开该页面，请稍等..."], "pages.website_access.website_seo.keyword_sideslip": ["新排名", "旧排名", "移动端", "PC端"], "pages.website_access.website_seo.other_chart": ["百度收录", "反链接数量"], "pages.website_access.website_seo.tdk": ["（一般不超过{{option0}}字符）", "{{option0}}个字符", "网站描述-Description", "原描述：", "优化描述：", "网站关键词-Keywords", "原关键词：", "优化关键词："], "pages.whatsapp.components.const": ["已读", "模板状态", "语种", "消息状态", "已读时间", "群发任务名称"], "pages.whatsapp.components.group_object": ["选择您需要发送whatsapp消息的账户列表，点击下载", "whatsapp上传文件模板", "上传whatsapp账号", "手动输入手机号"], "pages.whatsapp.components.preview_dialog": ["预览模版"], "pages.whatsapp.components.sideslip_detail": ["【模板消息通知】", "按手机号搜索", "消息已成功推送到WhatsApp服务端，但客户未打开WhatsApp应用，此时消息状态为已发送，客户打开应用后，会自动更新状态为已送达"], "pages.whatsapp.components.table": ["审批状态", "确认取消群发任务【{{option0}}】", "确认删除群发任务【{{option0}}】"], "pages.whatsapp.components.tmp_dialog": ["重置", "前往牛信云新增模板"], "pages.whatsapp.create": ["{{option0}}WhatsApp群发任务", "1、群发模板消息务必遵循 WhatsApp Business相关政策，防止未按规范发送导致封号。", "WhatsApp Business 政策", "2、首次进行模板消息推送之前，请确保账号有3-5天的活跃时间，有客户主动进行会话，直接申请模板就开始群发容易导致新号被封。", "3、申请模板时，请严格参考官方模板示例 ，充值及新增模板请前往牛信云管理后台处理。", "官方模板示例", "牛信云模板管理", "4、发送客户手机号需要有国际区号，否则会发送失败。", "请输入任务名称", "群发对象", "设置变量", "请填写变量", "发送测试", "请输入测试手机号（加国际区号），多个手机号之间用英文逗号分开，最多支持5个号码", "请输入需要发送的手机号（加国际区号），多个手机号之间用英文逗号分开，最多支持1000个号码", "请输入发送账号", "请先选发送账号", "请上传WhatsApp账号", "请选择发送账号", "请选择发送模板", "请填写测试账号"], "pages.whatsapp.index": ["群发任务列表", "按任务名称搜索", "新建群发任务"], "pages.Coupons.CommonCreate.components.UseRange.index": ["根据您导入客户上的识别字段值，我们将精准匹配自动查找您的客户对象，并将其添加到领取范围中。请注意，通过导入方式添加领取范围需要一定的时间来处理数据。处理完成后，您才可以进行下发优惠券操作，您可在优惠券批次详情页查看导入情况", "根据您导入客户上的识别字段值，我们将精准匹配自动查找您的客户对象，并将其添加到发放范围中。请注意，通过导入方式添加发放范围需要一定的时间来处理数据。处理完成后，您才可以进行下发优惠券操作，您可在优惠券批次详情页查看导入情况", "仅支持xls", "仅支持xls/xlsx文件，且不能超过5M。", "客户名单导入模板.xlsx"], "pages.TargetPopulation.TargetPopulationExportDialog": ["导出加密后的手机号，用于在广告平台投放", "导出数据可能需要较长时间，将转入后台进行导出任务，导出完成后请前往企信的文件助手中下载广告人群包。", "加密方式：", "手机号（MD5）", "手机号（SHA256）", "添加手机号前缀：", "不同的广告平台会要求上传自定义人群包是否需要添加前缀，比如86等，根据广告平台要求添加即可。", "请输入前缀", "导出成功"], "pages.content_marketing.content_dashboard.module.spread_channel_statistic": ["我们将记录不同渠道产生的用户营销动态行为，包括但不限于内容的访问和转发、关注公众号、邮件/短信的打开和点击，以及添加企业微信好友等行为。为了更好地分析和了解用户的动态行为，您可以在推广内容时设置相应渠道，以便更精准地跟踪和优化营销效果。"], "pages.sms_marketing.sms_setting.sms_template_manage.add_template_dialog": ["显示文案", "取当前短信平台默认签名", "短信需要运营商审核后才能发送", "查看审核规则", "验证码短信用于登录、注册等安全校验场景"], "pages.sms_marketing.sms_setting.sms_template_manage.create_h5obj_url": ["订货通", "对象详情页", "请选择链接域名", "自定义渠道域名{{option0}}", "CRM登陆域名{{option0}}", "链接域名"], "pages.sms_marketing.sms_setting.sms_template_manage.create_minip_url": ["请选择小程序"], "pages.sms_marketing.sms_setting.sms_template_manage.smstpl_list": ["隐藏", "更新再次审核中", "国际/港澳台消息", "取消审核"], "pages.promotion_activity.sms.components.sms_template_dialog": ["通知短信", "为了避免使用通知模板发送营销类短信带来的封号风险，此入口仅能选择审核通过后的营销模板进行推送。", "查看发送入口区别"], "pages.marketing_process.template.tpl_1": ["发送短信引导关注公众号", "销售线索.状态", "属于", "未分配，待处理"], "pages.marketing_process.template.tpl_2": ["更新打分规则", "发送会议提醒短信", "给报名用户打标签", "活动兴趣者", "会议报名用户跟进", "销售线索.销售线索详情=更新打分规则"], "pages.marketing_process.template.tpl_3": ["更新打分规则字段属性", "发送会场提醒", "给签到用户打标签", "活动参与者", "销售线索.销售线索详情=更新打分规则字段属性"], "pages.marketing_process.template.tpl_5": ["在用户关注公众号后的欢迎消息中可与自身业务特性采用产品免费试用、专业资料获取、线下活动等内容及表单进行结合引导，收集微信用户的联系方式并进一步识别微信用户以进行筛选。", "消息内容示例：", "欢迎关注纷享销客", "我是连接型CRM开创者，大家都叫我移动销售管理专家。", "这里有优质的CRM解决方案、有趣的销售管理常识及前沿的SaaS行业干货。", "消息内容设计小贴士：", "欢迎关注纷享销客我连接型CRM开创者，大家都叫我移动销售管理专家。这里有优质的CRM解决方案、有趣的销售管理常识及前沿的SaaS行业干货。", "提交了产品试用表单", "提交：如何设计与实施组织发展与变革项目 ——基于侯氏组织领导力模型", "核心用户", "活跃用户", "销售线索.销售线索详情=核心用户", "免费试用纷享销客CRM30天！"], "pages.trigger.create.components.branch.BaseObjectValueDialog": ["请选择对象", "请设置查询条件", "字段条件共", "等字段条件共"], "pages.trigger.create.components.branch.BaseSubmitFormDialog": ["未填信息不计入条件", "包含", "请设置条件", "很抱歉，当前选择的内容中没有包含表单。请您重新选择内容或在内容中添加一个表单。"], "pages.trigger.create.components.branch.BaseUserBehaviorDialog.Material": ["选择内容分组", "当前物料无表单"], "pages.trigger.create.components.branch.BaseUserBehaviorDialog.config": ["企微好友", "添加好友", "删除好友", "企微群聊", "加入群聊", "退出群聊", "预约直播", "参与互动", "访问视频号主页", "访问视频号视频", "访问微页面", "访问文章", "访问产品", "访问文件", "查看外部内容", "转发外部内容", "领取优惠券", "核销优惠券", "参加优惠券活动", "访问会议主页", "转发会议主页", "报名会议活动", "扫描渠道二维码", "点击公众号菜单", "点击公众号链接", "访问", "回复邮件", "垃圾邮件举报", "点击邮件链接"], "pages.trigger.create.components.branch.BaseUserBehaviorDialog.index": ["行为动作", "公众号菜单", "官网页面", "官网事件", "全部邮件", "邮件标题包含以下关键词", "请选择行为动作", "请输入邮件标题关键字", "请选择官网事件", "输入关键字后请回车"], "pages.trigger.create.components.branch.index": ["配置分支节点", "新增分支节点", "动作类型", "启用其他分支", "分支等待时间", "邮件发送状态", "该分支后续所有节点都将被删除。", "确定删除该分支吗", "邮件发送成功", "邮件发送失败", "请注意，关闭其他分支将会删除该分支下所有的节点数据，是否确认关闭？", "确定关闭其他分支吗？"], "pages.trigger.create.item.branchNode": ["分支", "请配置分支节点", "其他分支节点", "这是一个分支节点，删除该节点会删除后续分支所有的节点数据", "确定删除吗？"], "pages.trigger.create.validate": ["请设置分支等待时间", "请为分支设置动作", "请选择动作类型", "请补齐短信模板动态参数", "请填写完整发送的H5物料信息", "请填写完整发送的小程序物料信息"], "pages.trigger.detail.UserPathTree": ["点击后在下方查看明细"], "pages.setting.channel.components.CardColorPicker.shareCardPreview": ["您好，这是我的名片，请惠存", "设置名片主题色", "点击查看名片", "卡片样式", "信封样式", "默认样式", "李雷", "大客户经理", "销售部", "北京市海淀区"], "pages.setting.channel.components.CardContent.index": ["名片内容设置", "由公司统一规定展示的信息：{{option0}}", "员工名片页下方展示内容：{{option0}}", "员工名片信息", "为了保持公司的统一和准确性，可设置员工名片不允许员工编辑的信息权限，员工名片页下方的展示内容由公司决定，以下勾选的选项不允许员工自行修改。其中头像、姓名、手机号、部门、职位、邮件信息默认从通讯录自动获取。", "营销推广内容"], "pages.setting.channel.components.CardContent.promotion": ["指定视频和图片", "指定微页面内容"], "pages.setting.channel.components.CardTemplate.components.template5": ["沉浸式", "请前往【数字展厅>小程序设置】中完成小程序升级后使用"], "pages.setting.SetSingleCardInfo.SetSingleCardInfoConfig": ["营销内容"], "pages.trigger.create.components.QyWechat": ["任务执行员工范围", "最近与客户有聊天互动的员工", "指定部门员工（企微客户的添加员工不在此范围，不会触发群发任务）", "内已接收推送群聊"], "pages.ad_marketing.ad_bytedance.setting.index": ["头条广告设置", "巨量引擎广告设置"], "pages.live_marketing.empty": ["全流程提升获客效率", "助力营销增长", "直播前", "多渠道邀约推厂，报名留资", "观众互动交流，点赞分事", "直播后", "数据追踪分析，客户转化"], "pages.ad_marketing.dashboard.const": ["广告获客成本多少？", "广告消费和获客趋势如何？", "获客TOP关键词是什么？消费多少？", "广告整体数据概览", "广告投放给不同地区分别带来多少线索？", "广告投放情况如何？", "广告转化情况如何？", "产生了多少MQL、SQL、商机和订单？", "线索转化周期多少？", "广告账户获客占比情况如何？", "微信粉丝", "本年度", "上年度", "同比", "赢单商机", "线索转MQL", "MQL转SQL", "SQL转赢单商机", "线索转赢单商机"], "pages.ad_marketing.dashboard.component.ad_conversion_cycle": ["对比时间平均为"], "pages.ad_marketing.dashboard.component.ad_customer_funnel": ["点击率", "转线索率", "转MQL率", "转SQL率", "赢单率"], "pages.ad_marketing.dashboard.component.ad_input_output": ["平均MQL单价", "平均SQL单价"], "pages.ad_marketing.dashboard.component.ad_key_data": ["累计广告线索", "*含历史线索转化"], "pages.ad_marketing.dashboard.component.ad_leads_geography": ["北京", "天津", "重庆", "市", "省"], "pages.ad_marketing.dashboard.component.ad_order_stage": ["转MQL", "转SQL", "转赢单商机", "以下数量均按转化时间统计，线索创建时间不限", "以前"], "pages.marketing_collaboration.components.menu_dialog": ["展示数据", "菜单设置", "设置列表展示分组", "请选择知识库场景", "请选择可用Agent", "可用Agent"], "pages.ad_marketing.dashboard.component.ad_setting_cell": ["展示数据（最多显示3个）", "展示维度", "范围", "无需配置", "数据Top范围", "展示数据（最多显示6个）"], "pages.ad_marketing.dashboard.component.ad_setting": ["请选择涨幅对比", "请选择数据维度", "不可选重复内容", "请选择广告账户", "数据时间范围", "涨幅对比", "转化漏斗数据定义", "转MQL包含【线索阶段】为", "的数据", "转SQL包含【线索阶段】为", "自定义配置需要展示的数据内容", "下拉选择图表可调整位置"], "pages.ad_marketing.dashboard.component.header": ["数据范围：", "涨幅对比："], "pages.trigger.create.components.branch.BaseTagDialog": ["不包含以下任意标签"], "pages.marketing_collaboration.components.const": ["纷享AI", "个人资料库", "遵循市场活动对象权限", "满足标签", "对象条件", "适用分组对应的权限设置", "单个分组", "多个分组", "会议验票", "领券中心", "优惠券活动", "员工（有纷享员工账号、企业微信员工账号或无以上账号通过会员注册的员工）", "伙伴", "伙伴（有纷享互联账号或通过会员注册的渠道、代理商等）", "会员（通过会员注册的KOL客户、推广大使等）", "访问小程序首页进入", "访问首页直接进入（适用于进入小程序主要获取推广物料进行推广的人群，如员工）", "微站会员中心进入", "微站会员中心进入（访问小程序首页先进入微站，在会员中心点击【推广工作台】进入，适用于兼职的分销伙伴人群）", "【互联企业】属于【{{option0}}】", "【互联企业组】属于【{{option0}}】"], "pages.marketing_collaboration.components.menu_detail": ["确认删除【{{option0}}】菜单？", "< 返回", "当前模板为系统预置，若不同部门、角色需展示不同应用数据及菜单，可单独新建模板", "去创建 >", "应用模板预览", "应用菜单名称：", "首页标题：", "此应用可以给员工、伙伴、推广客户使用，无纷享相关账号的用户可以使用会员类型账号，", "纷享通讯录：", "企业微信通讯录：", "未绑定企业微信", "未开通会员营销插件", "会员条件：", "菜单及权限设置", "点击可编辑菜单，拖拽可调整顺序", "添加菜单", "菜单图标样式", "系统信息", "请选择互联企业", "小程序快捷入口：", "使用范围：", "开通企业微信后使用", "开通会员营销后使用", "纷享下游互联企业：", "开通伙伴营销后使用"], "pages.marketing_collaboration.components.menu_list": ["应用模版", "请输入应用模板名称", "【纷享通讯录】", "【企微通讯录】", "【会员】", "若企业有多业务线、分子公司以及渠道伙伴的独立运营业务时，支持给员工、伙伴、客户设置不同的应用菜单及展示数据，同一个推广用户在多个模版的适用范围内时，优先以最小的部门规则生效，如给全公司配置了统一模版A，又给研发中心配置了一套模版B,研发中心人员均以模版B生效。若给同级部门或同条件下的伙伴、客户配置了多套模版，则以最近创建的模版生效。", "创建应用模板", "应用模板名称", "预览应用模板", "使用人群", "【伙伴】", "预置"], "pages.marketing_collaboration.components.menu_range_sideslip": ["使用成员列表"], "pages.marketing_collaboration.components.list": ["搜索姓名、手机号、邮箱", "CRM员工", "钉钉员工", "伙伴会员", "员工会员", "相关说明", "、营销助手是在", "、微信、钉钉端提供的一个营销协同应用", "，应用成员可以接收推广任务或快捷获取营销物料，一键转发到企微、微信、朋友圈等，追踪用户查看、下载、留咨等行为，洞察用户需求，为一线业务人员赋能。", "、使用成员列表展示所有使用此应用的员工、伙伴、会员信息，这些用户均可以使用营销助手的功能。", "查看权限说明", "、当用户绑定完多渠道身份后，可以在多渠道访问应用、接收全员推广通知、用户互动通知等，企业微信渠道使用需要员工额外开通【互通接口】。", "了解如何绑定多渠道身份", "同步成员数据", "企微微信员工通讯录", "纷享下游互联企业对接人", "满足会员条件（推广身份等于伙伴）", "满足会员条件（推广身份等于员工）"], "pages.marketing_collaboration.components.multi_group_dialog": ["分组展示设置"], "pages.promotion_activity.sms.components.sms_params": ["请插入", "活动基础信息", "参会人员信息", "活动数据"], "pages.marketing_collaboration.components.table_tools": ["CRM账号", "企微账号", "伙伴账号", "钉钉账号", "关联会员"], "pages.marketing_collaboration.marketing_assistant": ["通知配置", "应用模板"], "pages.marketing_collaboration.components.setting": ["请选择微信公众号", "修改绑定公众号后，原有的成员与粉丝绑定关系将全部清空，需要重新关联粉丝身份及重新设置下方的互动通知及任务通知模板后才能正常通知，请谨慎操作。确认后保存并进行以下操作", "若使用成员需要在服务号中收到互动及任务通知时，需要绑定成员与服务号粉丝身份，请设置下方用于通知和身份绑定的公众号，设置后请谨慎修改，所有成员身份需要重新绑定", "已绑定：{{option0}}", "全员营销通知", "公众号通知", "配置公众号模板消息并开启后，绑定过公众号粉丝身份的员工、伙伴、会员将会通过公众号收到推广任务通知一键推广", "公众号通知模版配置：", "互动行为通知", "用户在各个渠道产生互动行为时，相关人员在已绑定身份各个渠道会收到客户互动卡片通知，洞察客户需求。", "查看触发行为", "接收通知的相关人员", "了解相关人员", "关联数据的负责人：{{option0}}", "指定员工：{{option0}}", "关联数据的其他人员：{{option0}}", "接收通知的人员范围", "通知仅发送给此范围的相关人员", "指定范围：{{option0}}", "配置公众号模板消息并开启通知后，可以通过公众号模板消息通知相关人员"], "pages.target_marketing.components.detail_header": ["启用后，若仍在运营时间范围内，则会在下一个计划执行时间生成并执行新的单次运营活动。", "您是否确认要启用周期性创建运营活动？", "一旦停止，系统将不再自动生成或执行新的单次运营活动。", "您是否确认要停止周期性创建运营活动？", "停止成功", "停止活动", "启用活动"], "pages.ad_marketing.ad_baidu.setting.plugin": ["百度广告设置"], "pages.setting.setitems.content_set.miniProgramAuthSettingDialog": ["请输入姓名", "请输入头像", "授权手机信息存入设置"], "pages.mini_app.userList": ["按昵称搜索"], "pages.sms_marketing.components.smsToggleDialog": ["切换短信平台"], "pages.sms_marketing.sms_setting.sms_template_manage.sms_params_dialog": ["对象变量", "自定义变量", "请输入变量，例如：param1", "快捷生成", "请选择变量类型", "请选择变量", "第三方短信提供商只支持英文、数字、下划线和连字符", "链接变量", "根据阿里云短信模板创建规范，在设置变量时，必须明确指定变量属性，若变量属性选择错误会导致短信模版创建失败。"], "pages.setting.setitems.marketing_plugin.xiaobing_setting_dialog": ["小冰数字人对接设置", "营销用对接【小冰】提供的数字人服务，请输入绑定信息", "SDK密钥"], "pages.ad_marketing.ad_bytedance.setting.bytedanceAdFields": ["线索ID", "广告主ID", "广告主名", "广告ID", "内容ID", "线索工具ID", "线索修改时间", "用户性别", "区县", "商家备注", "商家表单自定义的字段信息", "流量类型", "互动场景", "留资页面", "跟进账户类型", "跟进账户ID", "跟进账户名称", "订单ID", "线索阶段", "线索阶段名称", "所属人姓名", "线索通话状态", "分配状态", "当前线索对应广告的请求id", "意向门店名称", "意向门店ID", "接待抖音号", "接待抖音昵称", "内容创作者抖音号", "内容创作者昵称", "内容创作者角色", "跟进门店ID", "标题ID", "视频ID", "图文ID", "来源职人号抖音号", "来源职人号昵称", "是否私信线索", "流量入口", "用户所在省份", "用户填写省份", "用户所在城市", "用户填写城市", "用户填写区县"], "pages.ad_marketing.ad_bytedance.setting.mapping": ["每5分钟自动拉取巨量本地推线索到CRM销售线索对象中，自动关联巨量引擎广告组、广告计划。", "每2分钟自动拉取巨量本地推线索到CRM销售线索对象中，自动关联巨量引擎广告组、广告计划。"], "pages.ad_marketing.ad_bytedance.setting.account": ["账户平台："], "pages.ad_marketing.ad_bytedance.setting.forceSyncDialog": ["线索时间范围", "请注意：同步历史数据前请先配置好线索映射，确认后将开始同步巨量本地推中获取的广告线索数据。"], "pages.setting.setitems.components.bytedance_clue_set": ["巨量竞价广告线索映射配置"], "pages.site.components.UploadPdfToSite": ["转换中，请稍后...", "PDF一键转微页面", "从文件库中选择", "请选择pdf文件", "请先填写推广内容名称"], "pages.setting.setitems.marketing_plugin.verify_sms_template_dialog": ["中国大陆手机号使用短信平台", "国际手机号使用短信平台", "目前仅支持阿里云短信平台，接入后即可进行选择。", "短信设置", "在使用营销通微页面中的表单时，如果您需要在收集客户手机号码后进行验证码校验，需要配置验证码发送的短信平台。针对中国大陆和国际手机号，我们提供了不同的短信发送平台供您选择。新对接的短信平台，系统创建的短信模板需审核通过后才能使用。", "短信模板正在审核中，审核通过后即可使用该平台发送验证码"], "pages.cta.components.actions": ["提交信息查看全文", "扫码关注公众号", "添加微信好友", "扫码添加客服"], "pages.cta.detail_table_tools": ["内容名称", "触发微信授权数", "新增粉丝数", "新增企业微信好友数"], "pages.cta.table_tools": ["留咨组件及规则", "有", "复制SDK"], "pages.cta.create": ["请设置触发时机", "添加引导组件", "编辑CTA组件", "新建CTA组件", "暂无预览数据", "触发时机(可同时开启多个)", "用户点击内容中的按钮，链接，图片等元素后触发，如点击【下载】按钮，需要登录后才能下载。触发组件可以选择内容中的已有组件，也可以通过SDK方式使用外部系统的组件触发", "用户点击内容中的按钮、链接和图片等元素后触发，如点击【下载】按钮，需要登录后才能下载。触发组件可以选择内容中的已有组件，也可以通过", "方式使用外部系统的组件触发", "用户浏览内容到一定进度后触发，如文章阅读到20%需要提交表单，若设置0%时，进入页面就触发", "浏览内容", "观看百分比", "用户刚刚进入页面或在页面浏览一定时间后触发，如刚刚进入资料详情页或视频观看20秒后，需要登录，若设置0秒时，进入页面就触发", "引导组件(支持最多配置3个组件，从上往下执行)", "移动端会员登录页面", "WEB端登录页面", "企业微信渠道二维码", "暂未配置统一按钮", "已配置统一按钮"], "pages.cta.util": ["点击页面组件", "计划离开页面时触发", "添加企微好友", "此操作将永久删除此CAT组件, 是否继续?"], "pages.cta.list": ["CTA分组", "CTA组件", "设置用户信息验证、引导及信息收集组件，可以挂载在营销通生成内容上，也通过SDK方式对接到第三方系统的内容中，用户在进入页面、或者点击某按钮后，弹出引导内容，也可以配置需要进行留咨或授权身份后才能进行下一步动作，比如点击下载按钮后，需要客户提交表单后才能下载。同时支持组合型验证，如需要先关注公众号，并且再提交表单后才能下载文件。一个CTA组件，可以被多个内容应用。", "设置用户信息验证、引导及信息收集组件，可以挂载在营销通生成内容上，也通过", "方式对接到第三方系统的内容中，用户在进入页面、或者点击某按钮后，弹出引导内容，也可以配置需要进行留咨或授权身份后才能进行下一步动作，比如点击下载按钮后，需要客户提交表单后才能下载。同时支持组合型验证，如需要先关注公众号，并且再提交表单后才能下载文件。一个", "组件，可以被多个内容应用。"], "pages.cta.components.guide": ["Cal-to-Action (CTA)“呼吁行动”，旨在引导用户执行特定的操作，例如了解更多、联系客服、申请试用、下载文件等。CTA通过吸引用户的注意力和提供明确的指导，帮助企业实现其营销目标，如获取更多潜客线索、增加销售额提高转化率等。", "整体方案", "营销通中可以设置用户信息验证、引导及信息收集组件，可以挂载在营销通生成内容上，也通过SDK方式对接到第二方系统的内容中，用户在进入页面、或者点击某按钮后，弹出引导内容，也可以配置需要进行留咨或授权身份后才能进行下一步动作，比如点击下载按钮后，需要客户提交表单后才能下载。同时支持组合型验证，如需要先关注公众号，并且再提交表单后才能下载文件。一个CTA组件，可以被多个内容应用。"], "pages.cta.detail": ["触发方式及引导组件", "获取SDK", "触发方式：", "引导组件：", "关联内容数", "微信授权头像昵称数", "微信授权手机号次数", "会员登录次数", "会员注册人数", "公众号扫码次数", "新增公众号粉丝数", "新增企微好友数", "新增表单提交数"], "pages.cta.components.cta_dialog": ["此场景下为页面级触发，仅能选择触发方式包含【内容浏览一定进度】【经过一段时间触发】【计划离开页面触发】的组件", "此场景下为自有按钮触发，仅能选择触发方式为【点击页面组件时触发】且有独立按钮的组件", "此场景下为按钮触发，仅能选择触发方式为【点击页面组件时触发】的组件", "有独立按钮", "无独立按钮", "选择CTA组件"], "pages.cta.components.actions_dialog": ["请选择微页面表单", "请选择会员登录页面类型", "请选择企业微信渠道二维码", "请选择是否允许跳过", "该引导组件已被添加", "您还未开通会员模块，请先前往会员中心开通", "您还未绑定公众号，请先前往绑定", "您还未绑定企业微信，请先前往绑定", "通过提交表单获取用户信息，仅能选择有表单组件的微页面。微页面设置中的所有设置项均生效，如打标签、自动跳过，仅能提交一次限制等。微页面表单提交后无后动作时，自动关闭引导组件。若有配置后动作，将会执行后动作。", "通过登录会员身份获取用户信息，若当前内容中有会员身份，自动跳过此组件。默认使用会员中心>会员页面>登录页面作为会员登录页。", "通过小程序或公众号授权获取用户的微信头像昵称，若是小程序内容，默认使用营销通中的小程序授权，若是H5内容，则需要选择授权公众号。在非微信环境中访问时，若设置了不允许跳过，会引导至微信小程序打开，允许跳过时默认自动跳过。若未设置公众号，在微信H5环境中无法收集头像昵称。", "通过小程序授权获取用户的微信手机号，在非微信环境中访问时，若设置了不允许跳过，会引导至微信小程序打开，允许跳过时默认自动跳过。", "引导客户关注公众号，未关注的客户扫码后点击关注后自动跳过，已关注的粉丝扫码后自动跳过，若缓存中还有粉丝身份，直接自动跳过。", "引导客户添加员工的企业微信账号，未加员工好友的客户扫码后添加好友自动跳过，由于已添加企微好友的客户扫码时企微无事件推送，无法判断是否加好友成功，更适合于新客获取的场景，默认允许跳过且不能切换。", "选择组件", "顶部引导文案", "选择微页面表单", "选择公众号渠道二维码", "选择企业微信渠道二维码", "授权手机号存入线索设置", "暂未设置", "编辑引导组件"], "pages.cta.components.sdk_dialog": ["CTA SDK代码", "如果CTA设置中配置了悬浮按钮样式，页面新增悬浮按钮，直接使用悬浮按钮触发CTA"], "pages.qywx_manage.groups_messaging.create.components.sendDataCalculate": ["计算中，若数据量较大，可能需要一定时间，计算完成后需要您手动点击刷新", "当前数据量较大，还在计算中，请稍后再刷新", "计算发送人数", "刷新计算结果"], "pages.ad_marketing.ad_bytedance.detail.overview": ["花费"], "pages.promotion_activity.common.ai_slogan": ["请先选择素材", "生成推广话术失败", "请求超时", "帮写", "换一句", "AI帮写", "请先选择内容", "正在生成"], "pages.TargetPopulation.TargetPopulationCreate.TargetPopulationCreateSetDetailFilter": ["用户行为记录"], "pages.content_marketing.content_dashboard.module.spread_content_list": ["编辑页面", "该推广内容为报名内容，用户提交表单将被视为参与活动报名，同时受报名截止时间控制", "当前推广内容会展示在营销助手推广工作台，员工可推广活动或邀约用户参与活动。"], "pages.meeting_marketing.MeetingDetail.mixins.guide": ["点击【编辑页面】完善制作报名内容。", "完成内容制作后，还需完成【设置】才能去推广。", "您还可以快速制作该内容的关联海报，推广海报获客哦！", "您可以点击【添加内容】制作其他推广内容去推广获客哦！", "您可以制作多个类型的推广内容用于会前推广获客、会后资料下发等。", "点击这里制作海报去推广获客哦！", "点击这里去推广内容哦！", "点击这里添加参会人员哦！", "选择要邀约的活动成员，让员工去邀约吧!", "会前准备", "完善信息", "去完善", "市场活动信息", "推广内容制作与配置", "其他内容", "SOP设置", "报名数据收集", "推广报名内容", "去推广", "手动添加参会人员", "员工邀约", "去开启", "通知参会人员", "手动选择通知", "去通知", "SOP系统自动通知", "会议进行中", "通知所有参会人员", "管理员手动更新参会人员签到状态", "去补签", "下发活动内容", "发送会议相关资料给参会人员", "去发送", "查看会议效果数据", "去查看", "查看推广内容数据", "会议结束后", "选择您要通知的参会人员，点击「邀约通知」按钮。", "选择您要补签的参会人员，点击「手动补签」按钮。", "这是会议的数据概览", "如果您提前设置好了会议前自动通知的SOP，SOP会根据您设置的通知时间自动发送通知给参会人员。若您还没有设置会议SOP，您可以立即去设置SOP。", "立即设置SOP", "已设好了或稍后再设置", "推广会议相关资料", "会议基础设置", "选择或添加您要推广的资料内容点击推广按钮去推广", "更新基本信息", "查看运营任务看板", "内容制作", "更新会议主页（含报名表单）", "添加产品资料、问卷等推广内容", "制作多渠道推广海报", "互动设置", "配置报名成功、参会提醒等自动化通知", "验票人员设置及签到码获取", "员工邀约及代报名设置", "邀约推广", "群发、裂变等多渠道活动推广", "指定客户清单并下发邀约任务", "报名跟进", "手动补发通知", "活动签到", "查看未签到明细", "后台手动补签", "消息推送", "配置实时自动通知", "手动选择参会人员通知", "报名信息获取", "导出参会人员数据", "参会人员跟进", "查看线索处理情况", "会后内容推广", "问卷、资料、活动精彩集锦推送", "查看群发及关键任务执行情况", "查看整体活动数据分析"], "pages.meeting_marketing.MeetingSetting.index": ["会议基本信息"], "pages.meeting_marketing.enroll_setting.compontents.tag_setting": ["有认知", "A-Awareness:有认知", "给访问会议报名主页者添加标签", "给提交表单者添加标签"], "pages.meeting_marketing.MeetingCreate.MeetingInfo.MeetingInfoForm": ["开启后当前活动会展示在营销助手推广工作台，业务人员可推广活动或邀约用户参与活动。", "开启后当前活动会展示在对外推广内容的活动中心列表中，例如用户可在小程序微站活动中心列表中自主报名活动。"], "pages.promotion_activity.staff.index": ["推广场景"], "pages.promotion_activity.staff.sceneSelect": ["向已注册的推广会员下发推广任务，通过会员进行活动或内容推广传播获客", "预计发送人数：", "给内部员工下发推广任务，通过员工的社交链路进行内容推广传播", "给互联合作伙伴（代理商）下发推广任务，通过伙伴的社交链路进行内容推广传播", "给无CRM、互联账号的员工、伙伴、KOL客户下发推广任务，通过全员的社交链路进行内容推广传播"], "pages.setting.channel.components.CardTemplate.components.template6": ["传统"], "pages.cta.components.cta_button": ["CTA统一按钮"], "pages.vibe_marketing.pages.workspace.index": ["社媒", "是否删除当前记录", "删除记录"], "pages.vibe_marketing.pages.apply_tmp.index": ["创意"], "pages.vibe_marketing.pages.agent.conponents.BaseAgent.components.Content": ["海报生成", "微页面制作", "SOP生成", "似乎没有找到对应的，试试其他创意吧"], "pages.vibe_marketing.pages.agent.conponents.BaseAgent.components.NewHeader": ["工作空间资产", "制作", "工作空间", "返回工作空间", "退出全屏", "全屏"], "pages.vibe_marketing.components.workspace_dialog": ["编辑工作空间", "替换", "建议尺寸"], "pages.promotion_activity.mail_promotion_notice.index": ["前往邮箱发送", "退出", "邮件推广任务", "管理员下发统一邮件模板，您可以使用此模板快捷进行邮件发送", "任务说明", "任务下发人", "邮件模版内容", "主题", "正文", "已发送成功", "继续发送", "管理员下发统一邮件模板，您可以使用此模板快捷发送邮件给您的客户"], "pages.promotion_activity.staff.materials": ["内容推广", "推广示例", "编辑模版"], "pages.setting.setitems.marketing_ai": ["AI插件配置", "AI插件配置进展", "完成步骤后需手动打钩更新进度"], "pages.setting.setitems.marketing_sdr": ["小于", "大于", "评分模型结果", "最多跟进次数", "跟进时间间隔", "SDR工作台配置", "定义SDR评分模型，以评估线索质量。如BANT模型，各个维度的权重及评分标准", "定义SDR Agent的回复语言、语气风格、跟进规则等", "填写建议SDR与用户主动沟通的话题，沟通时自动推荐合适话题", "维护与用户沟通的对话案例，帮助AI更好地与用户互动", "前往在线客服，完成客服入口和会话规则配置等", "请选择产品画布", "请输入沟通主题", "请输入最多生成案例数量", "请选择回复语言", "请选择回复语气", "请选择咨询机器人目标", "请选择留资字段", "请选择转化模型", "请输入转化指标", "请输入线索跟进规则", "请选择知识空间", "中文", "英文", "SDR智能工作台配置", "话术生成中", "生成沟通话术", "查看话术", "生成沟通话术案例", "此操作会基于填写的信息生成沟通话术案例，由于生成话术案例需要一定时间，请几分钟前往查看及修改。", "产品画布", "沟通主题", "最多生成案例数量", "SDR业务规则配置", "回复语言", "回复语气", "咨询机器人目标", "留资字段", "转化模型", "线索跟进规则", "选择用户画像分析字段", "知识空间", "设置SDR工作台到线索的映射", "定义 SDR Agent的回复语言、语气风格、跟进规则等", "第 1 步：理想客户画像", "第 2 步：营销产品画布", "第 3 步：SDR评分模型", "第 4 步：SDR业务规则", "第 5 步：SDR话题库", "第 6 步：SDR沟通话术案例", "第 7 步：在线客服配置"], "pages.setting.setitems.components.step_setting": ["配置进度", "标记为完成"], "pages.meeting_marketing.MeetingDetail.index": ["会议创建成功！", "更多会议设置项，您可根据【流程引导】进行设置哦！", "您的会议主页还没有设置，可能会导致您无法收集报名信息进入CRM线索池。", "您刚创建的【会议主页】在这里哦！", "这里是会议的【流程引导】，您可根据里面的步骤做会前设置和准备哦！", "会议配置及流程导览"], "pages.meeting_marketing.baseinfo_setting.components.marketingCrmFiled": ["CRM字段", "必填字段：创建或编辑【市场活动】时的必填字段。", "系统信息字段：位于【市场活动】新建", "编辑页面“系统信息”分组下的字段。", "暂无必填或系统信息分组下的字段"], "pages.promotion_activity.sms.components.utils": ["个人姓名", "数量/金额", "日期/时间", "链接参数", "企业/组织名称", "车牌号", "快递单号", "取件码", "其他号码", "电话号码", "邮箱地址", "其他（如名称、账号、地址等）", "仅数字", "数字+字母组合或仅字母"]}