{"name": "marketing", "version": "1.0.0", "description": "fs app marketing", "author": "Louis", "private": true, "scripts": {"build": "webpack --config webpack/webpack.pro.conf.js", "build-md5": "cross-env ENV=md5 webpack --config webpack/webpack.pro.conf.js", "build-release": "cross-env NODE_OPTIONS='--max_old_space_size=4096' ENV=md5 webpack --config webpack/webpack.pro.conf.js", "dev": "webpack --config webpack/webpack.dev.conf.js", "dev-hot": "webpack-dev-server --config webpack/webpack.dev.conf.js", "commit": "npx git-cz", "commit:noverify": "npx git-cz --no-verify", "parse-build": "babel src/utils/parser/lib --out-dir src/utils/parser/build", "build-iconfont": "node build/build-iconfont", "tiny-image": "tiny-image -c build/tinyimage/conf.js", "lint": "eslint -c .eslintrc.js src/**/*.{js,vue}", "lint-fix": "eslint-autofix", "yalc-add": "yalc link fs-page-designer", "yalc-remove": "yalc remove fs-page-designer && rm node_modules/fs-page-designer", "i18n": "i18n-helper -c .i18n-helper.js", "i18n-check": "node build/i18n-check/check-chinese.js"}, "dependencies": {"@babel/plugin-proposal-optional-chaining": "^7.12.7", "@babel/plugin-syntax-import-meta": "^7.10.4", "@highlightjs/vue-plugin": "^1.0.2", "@marketing/vue-draggable-resizable": "^2.3.2", "@popperjs/core": "^2.11.8", "@tinymce/tinymce-vue": "^3.2.3", "@ungap/structured-clone": "^1.2.0", "@yireen/squoosh-browser": "^1.0.7", "ai-helper-web": "2.0.33", "marketing-sharegpt-web": "0.0.10", "axios": "^0.21.1", "clipboard": "^2.0.4", "color": "^4.2.3", "core-js": "^2.6.11", "emoji-mart-vue": "^2.6.6", "eslint-plugin-import": "^2.27.5", "eventemitter3": "^5.0.1", "fs-extra": "^9.0.1", "html2canvas": "^1.0.0-alpha.12", "lodash": "^4.17.21", "markdown-it": "^10.0.0", "parse5": "^6.0.1", "qrcodejs2": "^0.0.2", "smartcrop": "^2.0.3", "tiny-image": "^1.1.4", "tinymce": "^5.5.0", "vue": "2.6.12", "vue-content-placeholders": "^0.2.1", "vue-router": "3.0.1", "vue-seamless-scroll": "^1.1.23", "vuex": "3.0.1", "xss": "^1.0.3"}, "devDependencies": {"3rd-miniprogram": "1.5.18", "@babel/cli": "7.8.4", "@babel/core": "7.9.6", "@babel/plugin-proposal-class-properties": "^7.8.3", "@babel/plugin-syntax-dynamic-import": "^7.2.0", "@babel/polyfill": "7.8.7", "@babel/preset-env": "7.9.6", "@chuangkit/chuangkit-design": "^1.0.9", "@open-wc/webpack-import-meta-loader": "^0.4.7", "@squoosh/browser": "^0.0.3", "@tools/babel-plugin-transform-i18n": "^1.1.3", "@tools/eslint-plugin-i18n-check": "^1.0.9", "@tools/i18n": "1.4.6", "@tools/i18n-helper": "0.2.16", "@tools/i18n2": "1.4.26", "babel-loader": "8.0.4", "babel-plugin-component": "^1.1.1", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-runtime": "^6.23.0", "babel-plugin-transform-vue-jsx": "^3.7.0", "bpm": "1.0.25", "chalk": "^2.4.1", "child_process": "^1.0.2", "clean-webpack-plugin": "^1.0.0", "cli-table": "^0.3.11", "copy-webpack-plugin": "^6.0.3", "cross-env": "^3.1.4", "crypto-js": "^4.0.0", "css-loader": "^1.0.0", "cz-emoji": "^1.1.2", "dompurify": "^3.1.7", "driver.js": "^0.9.7", "echarts": "^4.2.0-rc.2", "element-ui": "^2.15.2", "es6-promise": "^4.2.8", "eslint": "^8.57.1", "eslint-config-firstshare-vue": "^1.1.5", "extract-text-webpack-plugin": "^4.0.0-beta.0", "file-loader": "^3.0.1", "friendly-errors-webpack-plugin": "^1.1.3", "fs-page-designer": "10.6.0-1", "glob": "^7.1.3", "happypack": "^5.0.0-beta.4", "hard-source-webpack-plugin": "^0.13.1", "html-loader": "^0.5.5", "husky": "^4.3.8", "inject-loader": "^2.0.1", "json2xls": "^0.1.2", "latest": "^0.2.0", "less": "3.9.0", "less-loader": "^4.1.0", "less-plugin-autoprefix": "^2.0.0", "lint-staged": "^10.5.4", "loader-utils": "^1.2.3", "marketing-fullcalendar": "3.1.2", "marketing-ui": "^2.16.2", "node-sass": "^4.14.1", "plop": "^2.7.4", "progress": "^2.0.3", "progress-bar-webpack-plugin": "^1.11.0", "pyfl": "^1.1.4", "qrcode-generator": "^1.4.4", "sass-loader": "^10.0.1", "schema-utils": "^2.1.0", "speed-measure-webpack-plugin": "^1.5.0", "style-loader": "^0.23.0", "style-resources-loader": "^1.2.1", "terser-webpack-plugin": "^1.4.1", "tinify": "^1.7.1", "uglifyjs-webpack-plugin": "^2.1.2", "url-loader": "^1.1.1", "vditor": "^3.5.3", "vue-loader": "15.4.1", "vue-style-loader": "4.1.2", "vue-template-compiler": "2.6.12", "vue2-editor": "2.6.6", "vuedraggable": "^2.24.0", "wasm-module-webpack-plugin": "^2.0.1", "webpack": "4.43.0", "webpack-bundle-analyzer": "2.13.1", "webpack-cli": "3.3.12", "webpack-dev-server": "3.11.0", "webpack-merge": "2.6.1", "webpack-runtime-public-path-plugin": "^1.1.2"}, "engines": {"node": ">=10.0.0 <15.0.0", "npm": ">=6.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "husky": {}, "lint-staged": {"*.{js,jsx,ts,tsx,vue,html}": ["eslint --fix"]}}