module.exports = {
  // 模块名称，该字段会自动做为key前缀
  moduleName: 'marketing',
  // 项目主目录
  mainDir: 'src',
  // 词条相关文件存放目录
  targetDir: 'i18n',
  // 指定需要处理文件的后缀名，目前支持 .html, .js, .vue文件
  exts: ['vue', 'js'],
  // 词条tag
  tags: ['web', 'marketing'],
  pres: {
    // 指定需要单独已文件形式分类的目录名，仅支持 src 顶层子目录
    dirPre: ['components', 'pages'],
    // 公共目录
    commonPre: 'commons',
    // 剩余目录
    restPre: 'others',
  },
  // 需要处理多语的入口目录
  entryPath: [
    'src/pages/promotion-activity/sms',
  ],
  // 指定忽略的文件（相对entryPath的路径）或文件夹（相对entryPath的路径）或以某字符结尾的文件（例如 *-tpl.js）
  excludedDirs: [],
  // 词条中的变量模板
  varTemplate: (i) => `{{option${i}}}`,
  // 词条中的变量字面量
  varLiteral: (i) => `option${i}`,
  // 词条中的变量替换方法
  varReplacer: (key, varLiterals) => {
    if (varLiterals && varLiterals.length) {
      const dataStr = varLiterals
        .map((d, i) => {
          return `option${i}: ${d}`;
        })
        .join(', ');
      return `$t('${key}', { data: ({ ${dataStr} }) })`;
    }
    return `$t('${key}')`;
  }
}
