const https = require("https");
const fs = require("fs");
const path = require("path");
const os = require('os');

/**
 * 生成iconfont图标文件
 * ******************** bug警告 **************
 * copyUrl 需要先更新到最新
 * ******************** bug警告 **************
 */

const sourceList = [
  {
    copyUrl: 'https://at.alicdn.com/t/c/font_2046471_9yamc6e5rdb.css',
    targetPath: path.resolve("./src/assets/style/iconfont.less"),
    needReplace: true,
  },
  {
    copyUrl: 'https://at.alicdn.com/t/c/font_2176374_od12mn0wph.css',
    targetPath: path.resolve("./src/assets/style/hexagon-iconfont.less"),
  }
]

let start = 0;
const length = sourceList.length;
const copySource = () => {
  if (start < length) {
    const { copyUrl, targetPath, needReplace } = sourceList[start];
    https.get(copyUrl, res => {
      let rawData = "";
      res.on("data", (chunk) => {
        rawData += chunk;
      });

      res.on("end", () => {
        let data = rawData.toString()
        if (needReplace) {
          data = data.replace(".marketing-iconfont", ".iconfont");
        }

        fs.writeFileSync(targetPath, '/**' + os.EOL)
        fs.appendFileSync(targetPath, ' * ******************** bug警告 **************' + os.EOL)
        fs.appendFileSync(targetPath, ' * 禁止直接编辑此文件，请执行：npm run build-iconfont' + os.EOL)
        fs.appendFileSync(targetPath, ' * ******************** bug警告 **************' + os.EOL)
        fs.appendFileSync(targetPath, ' */' + os.EOL)

        fs.appendFileSync(targetPath, data);

        fs.appendFileSync(targetPath, os.EOL)
        fs.appendFileSync(targetPath, '/**' + os.EOL)
        fs.appendFileSync(targetPath, ' * ******************** bug警告 **************' + os.EOL)
        fs.appendFileSync(targetPath, ' * 禁止直接编辑此文件，请执行：npm run build-iconfont' + os.EOL)
        fs.appendFileSync(targetPath, ' * ******************** bug警告 **************' + os.EOL)
        fs.appendFileSync(targetPath, ' */' + os.EOL)
        console.log('success: ' + targetPath);

        start += 1;
        copySource();
      });
    })
    .on("error", (e) => {
      console.error(e);
    });
  }
}

copySource()
