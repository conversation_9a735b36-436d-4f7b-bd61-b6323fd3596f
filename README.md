# 营销通

[![Commitizen friendly](https://img.shields.io/badge/commitizen-friendly-brightgreen.svg)](http://commitizen.github.io/cz-cli/)

## Build Setup

```bash
# 本地 alpha 开发
npm run dev-hot   # 热加载模式
npm run dev       # 文件监听模式

# ceshi113 发布 (不允许使用md5)
命令：npm run build -- --host=ceshi113 --env=html?        # 需要指定相关发布环境，默认为 html 环境
地址：http://jenkins.firstshare.cn/view/113WebDeploy/job/113_deploy_web_all/

# ceshi112 发布
命令：npm run build-md5 -- --host=ceshi112 --env=html?    # 需要指定相关发布环境，默认为 html 环境
地址：http://jenkins.firstshare.cn/view/112WebDeploy/job/deploy_app_all/

# 全网 发布
命令：npm run build-md5 -- --host=fxiaoke --env=html?    # 需要指定相关发布环境，默认为 html 环境
地址：http://oss.foneshare.cn/jenkins/view/web/job/deploy_web_index2_gulp/

# 访问地址 (域名自行切换)
https://www.fxiaoke.com/XV/Home/Index#/app/marketing/index

```

## app 项目 与 fs 主站 共用模块

- app 输出模块以文件夹命名，以 index.js 为入口。
- app 引用 fs 模块，需要在 webpack/config.js 中进行模块注册，可使用 utils.requireAsync 进行异步加载。

## 全局变量 与 局部变量

- 引入主站全局变量 \$, FS, CRM, seajs。
- 所有挂载 window、Vue.prototype 的全局变量移动到 utils/globals 内部，使用时需要局部引用。

## 路由注册与跳转

- - 所有路由注册必须使用 小写 + 中划线， 不允许使用大写。
- - 所欲路由注册不允许使用子路由、可选参数等配置。
- modules 下面的 routes 模块为 app 与 fs 共用模块。
- routes 路由模块注册完后，在 src/router 中根据路由命名注册相应的路由组件。
- 所有路由跳转必须使用路由名称进行跳转，包括带参跳转。

## 引用路径

- @: 代表 src 目录。
- components：代表 src/components 组件目录。
- modules: 代表 modules app 项目与 fs 主站共用模块。

## 代码规范

- 严格按照 eslint 配置，使用 airbnb-base 规范

## 遗留问题

## 附录：开发环境搭建

### 开发机环境配置

```cmd
; windows 系统下，以管理员身份运行 命令提示符 执行以下命令
; 设置本机 host 域名解析
echo 127.0.0.1  dev.ceshi113.com >> C:\Windows\System32\drivers\etc\hosts
echo 127.0.0.1  dev.ceshi112.com >> C:\Windows\System32\drivers\etc\hosts
; 从官网下载安装 git 工具（模拟 linux 命令行）：https://git-scm.com/download/win

```

```bash
# 设置本机 host 域名解析
echo -e '127.0.0.1  dev.ceshi113.com' | sudo tee -a /etc/hosts
echo -e '127.0.0.1  dev.ceshi112.com' | sudo tee -a /etc/hosts

# 安装 nodejs(推荐v8.x版本)和npm，设置 npm 镜像
echo "registry=http://registry-npm.firstshare.cn" > ~/.npmrc

# 安装全局 npm 模块
npm i -g gulp

# 配置开发机web服务器
<NAME_EMAIL>:fe-connect/alpha.git
cd alpha
git checkout test-wma
npm install
npm run init -- /work/fs
rm -r ./cache
npm run start -- ceshi113 -u
```

### 下载运行营销通项目

```bash
mkdir -p /work/fs/app && cd /work/fs/app
<NAME_EMAIL>:fe-app/marketing.git
cd marketing && npm i
npm run dev-hot
```

### 安装开发机 https 证书

以 windows 为例，双击打开文件：alpha/ssl/car.crt
安装证书、本地计算机、选择证书存储位置、受信任的根证书颁发机构、完成

### 启动浏览器访问开发地址

https://dev.ceshi113.com/XV/Home/Index#/app/marketing/index

## 可选安装内容

### 使用安装脚本下载主站其它项目

```bash
<NAME_EMAIL>:h5/fs-easy-deploy.git
cd fs-easy-deploy && npm i
rm -rf /work/fs
# 没错就是要准备一个空目录
./fs.js --type deploy --path /work/fs
rm -rf /work/fs/qx
# 本地开发可以暂时忽略企信，换来一点好处
```

### 生成主站基础运行环境

```bash
cd /work/fs/base && npm i && gulp
cd /work/fs/crm && npm i && gulp
cd /work/fs/bi && npm i && gulp
cd /work/fs/fs && npm i && gulp
cd /work/fs/app/common && npm i && gulp
```
